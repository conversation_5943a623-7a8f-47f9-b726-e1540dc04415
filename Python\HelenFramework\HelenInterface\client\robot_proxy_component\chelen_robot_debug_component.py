# -*- coding: utf-8 -*-
# time: 2022/07/28
# Author: CCG

import six2
import MType, MObject
import random
from common.RpcMethodArgs import Int, Dict, List, Bool, Str, Tuple
from common.rpcdecorator import rpc_method, CLIENT_STUB
from gclient.framework.util import <PERSON><PERSON><PERSON><PERSON>
from HelenUtils import Formula
# 这玩意真几把难搞
from gclient.util import debug_draw_util as DebugUtil


class EQSItemObj(object):
    def __init__(self, text, pos, color, up_offset=35):
        self.entity = MHelper.CreateDummy(pos)
        self.entity.Billboard = MObject.CreateObject("BoardComponent")
        self.billboard = self.entity.Billboard
        self.billboard.AddTextLayer(text, MType.Vector2(0, up_offset), MType.Vector4(1, 1, 1, 1),
                                    MType.Vector4(0, 0, 0, 1), 46, False)

    def __del__(self):
        self.billboard = None
        self.entity.Billboard = None
        self.entity = None
        self.sphere = None


class CHelenRobotDebugComponent(object):
    def __init_component__(self, bdict):
        self.need_debug_path = False
        self.debug_path_color = (random.uniform(.3, 1), random.uniform(.3, 1), random.uniform(.3, 1))
        self.debug_path_lines = []

    def __post_component__(self, _):
        self.debug_objs = []
        self.debug_next_point = None
        self.debug_move_yaw_line = None
        self.debug_eqs_items = []
        self.is_helen_ai = True

        # self.async_frame_tick = None
        # self.async_frame_tick and self.async_frame_tick.cancel()
        # self.async_frame_tick = self.frame_tick()
        # self.async_frame_tick.run()

        self.draw_vision_timer = None
        self.debug_vision_info_dict = {}
        self.debug_vision_use_triangles = False
        self.ai_squad_task_pos = {}
        self._squad_task_pos_debug_on = False
        self.debug_squad_task_pos_timer = None

        self.debug_color = (random.random(), random.random(), random.random())

        self.debug_path_point = {}
        self.debug_path_line = {}
        self.move_yaw = 0

        self.debug_vehicle_sweep = []
        self.debug_timer = None
        if getattr(self, 'auto_f12', False):
            from HelenUtils.debug_imgui.ImHierarchy import ImHierarchy
            self.add_timer(0.1, lambda: ImHierarchy.instance().set_obj(None, self))
        # [DEBUG]
        # self.debug_timer = self.add_repeat_timer(0, self.frame_tick)
        # [DEBUG]

    # [DEBUG]
    def _after_model_finish_debug(self):
        pass

    def __on_enter_component__(self):
        if getattr(self, "auto_f12", False):
            import six2.moves.builtins as __builtin__
            __builtin__.e = self

        return

    def __before_leave_space_component__(self):
        self.draw_vision_timer and self.cancel_timer(self.draw_vision_timer)
        self.draw_vision_timer = None
        self.debug_squad_task_pos_timer and self.cancel_timer(self.debug_squad_task_pos_timer)
        self.debug_squad_task_pos_timer = None
        self.debug_timer and self.cancel_timer(self.debug_timer)

    def DisplayRobotInfo(self):
        # self.top_logo.SetVisibleDistance(6400)
        self.top_logo.SetExtraInfo(self.extra_info)
        self.top_logo.UpdateName()

    @property
    def extra_info(self):
        states = list(filter(lambda x: self.real_states[x]['enabled'], list(self.real_states.keys())))
        res_states = '\n|'
        for s in states:
            # color = TopTextDebug3DUi().state_color_map.get(s, '#00ff12')
            # color = '#00ff12'
            # res_states += (color + s + '#E|')
            res_states += (s + '|')

        return res_states

    def add_debug_timer(self):
        if self.debug_timer:
            return
        self.debug_timer = self.add_repeat_timer(0, self.player_frame_tick)

    def remove_debug_timer(self):
        self.debug_timer and self.cancel_timer(self.debug_timer)
        self.debug_timer = None

    @rpc_method(CLIENT_STUB, List(), Dict())
    def draw_eqs_items(self, items, result_point):
        # print "draw_eqs_items", items, result_point
        if not items:
            return
        redraw_items = False if hasattr(self, 'eqs_items') and self.eqs_items == items else True

        # if not hasattr(__builtin__, 'e') or __builtin__.e != self:
        #    self.server.draw_eqs_items_on(False)
        #    return

        def draw():
            max_score = float(result_point['filter']) * result_point['score'] if result_point else 1.0
            if redraw_items:
                self.debug_eqs_items = []
                min_score = min([float(item['filter']) * item['score'] for item in items])
                score_range = max_score - min_score
                for item in items:
                    if not item['filter']:
                        color = [0.0, 0.0, 1.0]
                    else:
                        r = (item['score'] - min_score) / score_range if score_range > 0 else 0.0
                        g = 1.0 - r
                        b = 0.0
                        color = [r, g, b]

                    sphere = DebugUtil.draw_sphere(position=item['position'], radius=0.2, color=color)
                    self.debug_eqs_items.append(sphere)
                    text = str(round(item['score'], 3)) if item['filter'] else str(item['score'])
                    text_obj = EQSItemObj(text, item['position'], color)
                    self.debug_eqs_items.append(text_obj)

                if result_point:
                    capsule = DebugUtil.draw_cylinder(
                        None,
                        radius=0.5,
                        height=2.0,
                        position=Formula.add(result_point['position'], (0, 0.5, 0)),
                        color=[1.0, 0.0, 0.0]
                    )
                    self.debug_eqs_items.append(capsule)

        self.add_timer(0.0, draw)

    @rpc_method(CLIENT_STUB)
    def forbidden_draw_eqs_items(self):
        self.debug_eqs_items = []
        self.debug_peek_pos = []

    @rpc_method(CLIENT_STUB, List(), Int(), List())
    def draw_peek_pos(self, pos, side, tactical_list):
        self.debug_peek_pos = []
        for tac_data in tactical_list:
            tac_pos = tac_data[0:3]
            tac_dir = tac_data[3:6]
            sphere = DebugUtil.draw_sphere(position=tac_pos, radius=0.2, color=[1, 0, 0])
            self.debug_peek_pos.append(sphere)
            line_start = Formula.add(tac_pos, (0, 0.5, 0))
            line_end = Formula.add(line_start, tac_dir)
            dir_line = DebugUtil.draw_polyline(color=[1, 0, 0], points=[line_start, line_end])
            self.debug_peek_pos.append(dir_line)

        # sphere = DebugUtil.draw_sphere(position=pos, radius=0.2, color=[1, 0, 0])
        # self.debug_peek_pos.append(sphere)
        if pos:
            # 右綠 左藍
            color = [0.0, 1.0, 0.0] if side == 1 else [0.0, 0.0, 1.0]
            capsule = DebugUtil.draw_cylinder(
                None,
                radius=0.5,
                height=2.0,
                position=Formula.add(pos, (0, 0.5, 0)),
                color=color
            )
            self.debug_peek_pos.append(capsule)

    def player_frame_tick(self, _=None):
        for k, e in self.space.entities.items():
            if e.IsHelenAI:
                e.frame_tick()

    def frame_tick(self, _=None):
        player = genv.player
        space = genv.space
        if not hasattr(player, 'DEBUG_SHOW_ROBOT_POS'):
            player.DEBUG_SHOW_ROBOT_POS = False
        self.debug_objs = []
        if space and player:
            if getattr(player, "DEBUG_SHOW_ROBOT_POS", False):
                color = (0, 255, 255)
                self.debug_objs.append(DebugUtil.draw_sphere(position=self.pos, radius=0.5, color=color))
                if self.ai_info and self.ai_info.get('nav_pos'):
                    v = self.ai_info.get('nav_pos')
                    if str.startswith(v, '(') and str.endswith(v, ')'):
                        nav_pos = [float(s.strip(' ').strip(',')) for s in str.split(v[1:-1])]
                        if nav_pos and len(nav_pos) == 3:
                            self.debug_objs.append(
                                DebugUtil.draw_box(position=nav_pos, color=(0, 0, 255), scale=(0.25, 0.25, 0.25)))
            if getattr(player, "DEBUG_SHOW_ROBOT_YAW", False):
                color = (0, 255, 255)
                yaw_vector = Formula.yaw_to_vector(self.upper_yaw)
                start_pos = MType.Vector3(*self.position)
                start_pos.y += 1
                yaw_vec3 = MType.Vector3(*yaw_vector)
                yaw_vec3.length = 2
                end_pos = start_pos + yaw_vec3

                self.debug_objs.append(DebugUtil.draw_arrow_line(start_pos, end_pos, color=color))
            if getattr(player, 'DEBUG_SHOW_ROBOT_MOVE_YAW', False):
                color = (255, 0, 0)
                move_yaw = self.move_yaw
                yaw_vector = Formula.yaw_to_vector(move_yaw)
                start_pos = MType.Vector3(*self.position)
                start_pos.y += 1
                yaw_vec3 = MType.Vector3(*yaw_vector)
                yaw_vec3.length = 2
                end_pos = start_pos + yaw_vec3

                arrow_line = DebugUtil.draw_arrow_line(start_pos, end_pos, color=color)
                self.debug_move_yaw_line = arrow_line

    @rpc_method(CLIENT_STUB, Str(), Dict())
    def debug_action_chain(self, entity_id, current_dict):
        from HelenUtils.debug_imgui.ImHelenAI import HelenAIDebugWindow
        HelenAIDebugWindow.instance().action_chain_infos.append(current_dict)

    def debug_action_chain_client(self, entity_id, current_dict):
        from HelenUtils.debug_imgui.ImHelenAI import ClientHelenAIDebugWindow
        ClientHelenAIDebugWindow.instance().action_chain_infos.append(current_dict)

    @rpc_method(CLIENT_STUB, Int(), Dict())
    def debug_ai_planner_info_client(self, planner_ts, score_info):
        from HelenUtils.debug_imgui.ImHelenAI import HelenAIDebugWindow
        HelenAIDebugWindow.instance().planer_info = score_info

    def debug_ai_client_move(self, prefer_type, pos):
        self.des_pos = pos
        self.path_call_id = genv.player.RequestServerPath(
            self.position, self.des_pos, self.OnRequestServerPath)

    # Helen AI 视野相关
    @rpc_method(CLIENT_STUB, Dict())
    def on_debug_helen_ai_vision(self, ai_vision_info):
        self.draw_vision_timer and self.cancel_timer(self.draw_vision_timer)
        self.debug_vision_info.clear()
        self.helen_ai_vision_debug_objs = []
        self.draw_vision_timer = self.add_repeat_timer(0.1, lambda x=ai_vision_info: self._draw_helen_vision(x)) if ai_vision_info else None

    def _draw_helen_vision(self, ai_vision_info):
        self.helen_ai_vision_debug_objs = []
        alpha = 0.2 if self.debug_vision_use_triangles else 1.0
        draw_func = DebugUtil.draw_fan_triangles if self.debug_vision_use_triangles else DebugUtil.draw_fan
        info_getter = ai_vision_info.get
        blurry_start_range = info_getter('blurry_start_range', 40.0)
        common_range = info_getter('common_range', 50.0)
        half_common_angle = info_getter('common_angle', 1.31)  # 默认150
        focus_range = info_getter('focus_range', 100.0)
        half_focus_angle = info_getter('focus_angle', 0.4)

        ai_yaw = self.yaw
        ai_pos = list(self.position)
        ai_pos[1] += 1.5

        direction_pos = Formula.add3d(ai_pos, (Formula.sin(ai_yaw) * common_range, 0.0, Formula.cos(ai_yaw) * common_range))
        d_ob = draw_func(ai_pos, direction_pos, half_common_angle, 3, color=(0.5, 0, 1.0), alpha=alpha)
        self.helen_ai_vision_debug_objs.append(d_ob)
        if self.debug_vision_use_triangles:
            ai_pos[1] += 0.1
        direction_pos = Formula.add3d(ai_pos, (Formula.sin(ai_yaw) * blurry_start_range, 0.0, Formula.cos(ai_yaw) * blurry_start_range))
        d_ob = draw_func(ai_pos, direction_pos, half_common_angle, 3, alpha=alpha)
        self.helen_ai_vision_debug_objs.append(d_ob)
        if self.debug_vision_use_triangles:
            ai_pos[1] += 0.1
        direction_pos = Formula.add3d(ai_pos, (Formula.sin(ai_yaw) * focus_range, 0.0, Formula.cos(ai_yaw) * focus_range))
        d_ob = draw_func(ai_pos, direction_pos, half_focus_angle, 2, color=(1.0, 0, 1.0), alpha=alpha)
        self.helen_ai_vision_debug_objs.append(d_ob)
        self.helen_ai_vision_debug_objs.append(DebugUtil.draw_sphere(position=self.position, radius=info_getter('protected_distance', 5.0), color=(1.0, 0.5, 0.0)))

        if self.debug_vision_info:
            entity_getter = self.space.entities.get
            for e_id, info in self.debug_vision_info.items():
                e = entity_getter(e_id, None)
                if not e:
                    continue
                in_vision, raw_in_vision_time, is_target, thre_val = info
                if is_target > 0:
                    color = (0.0, 1.0, 0.0) if in_vision else (0.0, 0.4, 0.0)
                else:
                    color = (0.0, 0.0, 1.0) if in_vision else (0.0, 0.0, 0.2)
                self.helen_ai_vision_debug_objs.append(DebugUtil.draw_cylinder(position=e.position, radius=0.5, height=4.0, color=color))
                self.helen_ai_vision_debug_objs.append(EQSItemObj(str(round(thre_val, 3)), e.position, color, up_offset=280))

    # Helen Squad 相关
    @property
    def squad_task_pos_debug_on(self):
        return self._squad_task_pos_debug_on

    @squad_task_pos_debug_on.setter
    def squad_task_pos_debug_on(self, flag):
        self._squad_task_pos_debug_on = flag
        self.squad_task_pos_debug_objs = []
        self.debug_squad_task_pos_timer and self.cancel_timer(self.debug_squad_task_pos_timer)
        self.debug_squad_task_pos_timer = self.add_repeat_timer(0.2, self.draw_squad_task_pos) if flag else None

    def draw_squad_task_pos(self):
        self.squad_task_pos_debug_objs = []
        task_pos_dict = self.ai_squad_task_pos
        if not task_pos_dict:
            return
        entity_getter = self.space.entities.get
        for e_id, e_pos in task_pos_dict.items():
            e_pos = (e_pos[0], e_pos[1] + 20.0, e_pos[-1])
            self.squad_task_pos_debug_objs.append(DebugUtil.draw_cylinder(position=e_pos, radius=0.6, height=40.0, color=(1.0, 0, 1.0)))
            entity = entity_getter(e_id)
            if entity:
                self.squad_task_pos_debug_objs.append(DebugUtil.draw_arrow_line(entity.position, e_pos, color=(0.4, 0, 1.0)))

    def debug_move_path(self, path_points):
        from HelenAI import ai_consts
        self.debug_path_point = {}
        self.debug_path_line = {}

        if len(path_points) <= 1:
            return
        debug_func_map = {
            ai_consts.ENAV_PATH_TYPE_START: (DebugUtil.draw_sphere, {
                'radius': 0.1,
                'color': (0.81640625, 0.26953125, 0.30078125),
            }),
            ai_consts.ENAV_PATH_TYPE_START_LOCAL: (DebugUtil.draw_sphere, {
                'radius': 0.1,
                'color': (0.81640625, 0.26953125, 0.30078125),
            }),
            ai_consts.ENAV_PATH_TYPE_STRAIGHT: (DebugUtil.draw_sphere, {
                'radius': 0.1,
                'color': (0.98828125, 0.93359375, 0.296875),
            }),
            ai_consts.ENAV_PATH_TYPE_RUN_END: (DebugUtil.draw_sphere, {
                'radius': 0.1,
                'color': (0.31640625, 0.68359375, 0.2109375),
            }),
            ai_consts.ENAV_PATH_TYPE_RUN_START_F: (DebugUtil.draw_sphere, {
                'radius': 0.1,
                'color': (0.98828125, 0.703125, 0.015625),
            }),
            ai_consts.ENAV_PATH_TYPE_RUN_START_B: (DebugUtil.draw_sphere, {
                'radius': 0.1,
                'color': (0.98828125, 0.703125, 0.015625),
            }),
            ai_consts.ENAV_PATH_TYPE_RUN_START_L: (DebugUtil.draw_sphere, {
                'radius': 0.1,
                'color': (0.98828125, 0.703125, 0.015625),
            }),
            ai_consts.ENAV_PATH_TYPE_RUN_START_R: (DebugUtil.draw_sphere, {
                'radius': 0.1,
                'color': (0.98828125, 0.703125, 0.015625),
            }),

            ai_consts.ENAV_PATH_TYPE_ENTER_BLINDAGE: (DebugUtil.draw_box, {
                'scale': (0.1, 0.1, 0.1),
                'color': (0.81640625, 0.26953125, 0.30078125),
            }),
            ai_consts.ENAV_PATH_TYPE_LEAVE_BLINDAGE: (DebugUtil.draw_box, {
                'scale': (0.1, 0.1, 0.1),
                'color': (0.3125, 0.4453125, 0.9375),
            }),

            ai_consts.ENAV_PATH_TYPE_LTWIST: (DebugUtil.draw_cylinder, {
                'radius': 0.1,
                'height': 0.2,
                'color': (0.734375, 0.328125, 0.88671875),
            }),
            ai_consts.ENAV_PATH_TYPE_RTWIST: (DebugUtil.draw_cylinder, {
                'radius': 0.1,
                'height': 0.2,
                'color': (0.734375, 0.328125, 0.88671875),
            }),
            ai_consts.ENAV_PATH_TYPE_LTWIST_STAND_NORMAL: (DebugUtil.draw_cylinder, {
                'radius': 0.1,
                'height': 0.2,
                'color': (0.734375, 0.328125, 0.88671875),
            }),
            ai_consts.ENAV_PATH_TYPE_RTWIST_STAND_NORMAL: (DebugUtil.draw_cylinder, {
                'radius': 0.1,
                'height': 0.2,
                'color': (0.734375, 0.328125, 0.88671875),
            }),
            ai_consts.ENAV_PATH_TYPE_LTWIST_STAND_BATTLE: (DebugUtil.draw_cylinder, {
                'radius': 0.1,
                'height': 0.2,
                'color': (0.734375, 0.328125, 0.88671875),
            }),
            ai_consts.ENAV_PATH_TYPE_RTWIST_STAND_BATTLE: (DebugUtil.draw_cylinder, {
                'radius': 0.1,
                'height': 0.2,
                'color': (0.734375, 0.328125, 0.88671875),
            }),
            ai_consts.ENAV_PATH_TYPE_JUMP_UP: (DebugUtil.draw_cylinder, {
                'radius': 0.1,
                'height': 0.3,
                'color': (0xff, 0x0f, 0x00),
            }),
            ai_consts.ENAV_PATH_TYPE_JUMP_IN_AIR: (DebugUtil.draw_cylinder, {
                'radius': 0.2,
                'height': 0.2,
                'color': (0x00, 0x00, 0xff),
            }),
            ai_consts.ENAV_PATH_TYPE_JUMP_DOWN: (DebugUtil.draw_cylinder, {
                'radius': 0.1,
                'height': 0.3,
                'color': (0xff, 0xff, 0x00),
            }),

            'default': (DebugUtil.draw_sphere, {
                'radius': 0.1,
                'color': (0.203125, 0.203125, 0.203125),
            }),
        }

        idx = 0
        while idx < len(path_points):
            p = path_points[idx][0:3]

            if p[-1] == 5:
                debug_func = debug_func_map[ai_consts.ENAV_PATH_TYPE_JUMP_IN_AIR][0]
                extra_args = debug_func_map[ai_consts.ENAV_PATH_TYPE_JUMP_IN_AIR][1]
            else:
                debug_func = debug_func_map[ai_consts.ENAV_PATH_TYPE_STRAIGHT][0]
                extra_args = debug_func_map[ai_consts.ENAV_PATH_TYPE_STRAIGHT][1]
            self.debug_path_point[idx] = debug_func(position=p[0:3], **extra_args)
            if idx < len(path_points) - 1:
                # 画线
                p2 = path_points[idx + 1][0:3]
                p3 = (p2[0], p2[1] + 1, p2[2])
                p4 = (p[0], p[1] + 1, p[2])
                self.debug_path_line[idx] = DebugUtil.draw_polyline(points=(p, p2, p3, p4), color=(255, 255, 0))
            idx += 1

        # for p in path_points:
        #     point = p[0:3]
        #     if p[-1] == 5:
        #         debug_func = debug_func_map[ai_consts.ENAV_PATH_TYPE_JUMP_IN_AIR][0]
        #         extra_args = debug_func_map[ai_consts.ENAV_PATH_TYPE_JUMP_IN_AIR][1]
        #     else:
        #         debug_func = debug_func_map[ai_consts.ENAV_PATH_TYPE_STRAIGHT][0]
        #         extra_args = debug_func_map[ai_consts.ENAV_PATH_TYPE_STRAIGHT][1]
        #     self.debug_obj.append(debug_func(position=point, **extra_args))

    def debug_next_move_pos(self, idx):
        next_idx = idx + 1
        last_idx = idx - 1
        if idx in self.debug_path_point:
            sphere = self.debug_path_point[idx]
            sphere.color = MType.Vector3(*(255, 0, 0))
        if idx in self.debug_path_line:
            line = self.debug_path_line[idx]
            line.color = MType.Vector3(*(0, 255, 0))
        if next_idx in self.debug_path_point:
            sphere = self.debug_path_point[next_idx]
            sphere.color = MType.Vector3(*(0, 255, 0))
        if last_idx in self.debug_path_line:
            line = self.debug_path_line[last_idx]
            line.color = MType.Vector3(*(255, 0, 0))

    def debug_move_yaw(self, yaw_diff):
        self.move_yaw = self.yaw + yaw_diff

    @rpc_method(CLIENT_STUB, List())
    def on_debug_nearby_tactical_points(self, debug_infos):
        from HelenUtils.debug import DebugUtil
        self.env_tactical_obj = []
        if not debug_infos:
            return
        for tac in debug_infos:
            tac_type, tac_pos = tac
            color = [0.0, 1.0, 0.0]
            height = 2.0
            if tac_type & (1 << 0):
                # low
                height = 0.5
            elif tac_type & (1 << 1):
                # mid
                height = 1.0
            elif tac_type & (1 << 2):
                # high
                height = 2.0
            if tac_type & (1 << 3):
                # can peek
                color = [0.0, 0.0, 1.0]
            capsule = DebugUtil.draw_cylinder(
                None,
                radius=0.5,
                height=height,
                position=Formula.add(tac_pos, (0, 0.5, 0)),
                color=color
            )
            self.env_tactical_obj.append(capsule)

    @rpc_method(CLIENT_STUB, List())
    def on_debug_hide_in_tacticals(self, debug_infos):
        from HelenUtils.debug import DebugUtil
        self.env_tactical_obj_1 = []
        color_map = {
            0: (0x00, 0xff, 0x00),
            1: (0xff, 0x00, 0x00),
            2: (0x00, 0x00, 0xff)
        }
        if debug_infos:
            for i in list(range(0, len(debug_infos))):
                if not debug_infos[i]:
                    continue
                itype, item = debug_infos[i]
                if not itype or not item:
                    continue
                color = color_map.get(i, (0xff, 0xff, 0xff))
                if itype == 1<<14:
                    tacs, verts = item
                    for tac in tacs:
                        pos, normal, tac_type = tac
                        self.env_tactical_obj_1.append(DebugUtil.draw_sphere(position=pos, radius=0.2, color=color))

                if itype == 1<<13:
                    pos = item[:3]
                    radius = item[3]
                    self.env_tactical_obj_1.append(DebugUtil.draw_sphere(position=pos, radius=radius, color=color))

    @rpc_method(CLIENT_STUB, Dict())
    def debug_robot_behavior(self, debug_info):
        from HelenUtils.debug_imgui.ImHelenRobot import HelenRobotStatisticsWindow
        HelenRobotStatisticsWindow().add_behavior_data(debug_info)

    @rpc_method(CLIENT_STUB, List())
    def draw_ai_snare_infos(self, debug_info):
        if not debug_info:
            return
        self.debug_snares = []
        color = [(1, 0, 0),
                 (0, 1, 0),
                 (0, 0, 1),
                 (1, 1, 0),
                 (1, 0, 1)]
        index = 0
        for snare_dict in debug_info:
            for k, v in snare_dict.items():
                self.debug_snares.append(DebugUtil.draw_sphere(position=v, radius=10, color=color[index]))
            index += 1

    @rpc_method(CLIENT_STUB, Tuple(), Tuple(), Tuple(), Tuple(), Tuple(), Tuple(), Int())
    def draw_vehicle_sweep_result(self, box, box_rotation, start_pos, end_pos, hit_pos, final_pos, is_hit):
        color = [(1, 0, 0),
                 (0, 1, 0),
                 (0, 0, 1),
                 (1, 1, 1),
                 (1, 0, 1)]

        # 终点
        self.debug_vehicle_sweep.append(
            DebugUtil.draw_cylinder(position=final_pos, radius=0.5, height=300.0, color=color[3]))

        # 车体
        # if box:
        #     self.debug_vehicle_sweep.append(
        #         DebugUtil.draw_box(position=start_pos, rotation=box_rotation, scale=box, color=color[2]))

        if is_hit:
            self.debug_vehicle_sweep.append(DebugUtil.draw_cylinder(position=hit_pos, radius=1, height=50.0, color=color[0]))
            self.debug_vehicle_sweep.append(DebugUtil.draw_arrow_line(start_pos, end_pos, color=color[0]))
        else:
            self.debug_vehicle_sweep.append(DebugUtil.draw_arrow_line(start_pos, end_pos, color=color[1]))

    @rpc_method(CLIENT_STUB, List())
    def draw_vehicle_path_points(self, points):
        for i in range(len(points)):
            p = points[i]
            self.debug_vehicle_sweep.append(
                DebugUtil.draw_cylinder(position=p, radius=1, height=100.0, color=(0, 1, 0)))

    @rpc_method(CLIENT_STUB)
    def draw_vehicle_sweep_result_clear(self):
        self.debug_vehicle_sweep = []

    def show_info(self):
        print(self)
        # print 'is_actor', self.actor == genv.player.id
        # print 'tach reason', self.model._attach_reason
        print('main_client_id', self.main_client_id)
        print('actorcomponent', self.model.model.Skeleton.ApplyMotion)
        print('filtercomponent', hasattr(self.model, 'filter') and self.model.filter.ApplyMotion)
        print('charctrlcomponent', self.model.charctrl.PassiveMode is False)
        print('posesender', self.model.model.PoseSender and self.model.model.PoseSender.EnableSend)

    def get_client_ai_blackboard_info(self):
        if not self.ai_agent_cpp:
            return
        # ai_black_board = self.ai_blackboard
        bb_info = self.ai_blackboard.get_all_blackboard_kvs()
        return bb_info

    # region client ai debug
    def debug_ai_move(self, prefer_type, target_pos):
        self.ai_move(prefer_type, target_pos)
    # endregion
    # [DEBUG]

