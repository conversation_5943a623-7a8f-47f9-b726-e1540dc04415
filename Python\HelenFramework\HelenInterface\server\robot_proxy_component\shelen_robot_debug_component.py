# -*- coding: utf-8 -*-

import random

from common.rpcdecorator import rpc_method, CLIENT_ANY
from common.RpcMethodArgs import Bool, List, Str, Int, Float, EntityID, Dict
from common.classutils import Property, CustomMapType, CustomFloatListType, CustomListType
from common.EntityManager import EntityManager
from HelenAI import ai_consts
from HelenAI.ai_system.BehaviourTreeNodes import BTBaseNode
from HelenUtils import TimeUtil
from gshare import formula, consts, weapon_util
from gserver import sconst
from gserver.data import equip_data, gun_attachments_data


MAX_LOG_NUM = 30


class CustomFloatListMapType(CustomMapType):
    IS_SPEC_TYPE = True
    VALUE_TYPE = CustomFloatListType


class DebugPathInfo(CustomListType):
    VALUE_TYPE = CustomListType


class SHelenRobotDebugComponent(object):
    """
    保存和输出robot的状态、行为树、黑板值、视野、威胁（原仇恨值）、目标的问题
    """
    Property("ai_config_name", '', Property.ALL_CLIENTS)
    Property("cur_goal_id", '', Property.ALL_CLIENTS)
    Property("cur_action_name", '', Property.ALL_CLIENTS)
    Property("cur_bt_name", '', Property.ALL_CLIENTS)
    Property("ai_world_state", 0, Property.ALL_CLIENTS)

    Property("ai_info", CustomMapType, Property.ALL_CLIENTS)
    Property('auto_f12', False, Property.ALL_CLIENTS)  # 上线记得删
    Property('debug_vision_info', CustomFloatListMapType, Property.ALL_CLIENTS)
    Property('debug_threaten_info', CustomFloatListMapType, Property.ALL_CLIENTS)
    Property('debug_sound_heat_info', CustomFloatListMapType, Property.ALL_CLIENTS)
    Property('squad_inner_index', 0, Property.ALL_CLIENTS)
    Property("debug_path", DebugPathInfo, Property.ALL_CLIENTS)
    Property("tester", '', Property.ALL_CLIENTS)

    def __init_component__(self, bdict):
        self.last_test_weapon_id = 0
        self.weapon_test_timer = None
        self.shoot_test_timer = None
        self.tester = bdict.get('tester', '')
        self.path_record_timer = None
        self.debug_repeat_shoot_timer = None
        self.debug_shoot_timer = None

        self.debug_behavior_timer = None
        self.upper_behavior_info_list = []  # value: dict -> {behavior_name: str, behavior_param: dict}  # 开火换弹等
        self.move_behavior_info_list = []  # value: dict -> {behavior_name: str, behavior_param: dict}  # run， walk等
        self.additive_behavior_info_list = []  # value: dict -> {behavior_name: str, behavior_param: dict}  # 滑铲跳跃蹲起等等
        self.debug_behavior_running_info = {}

    def __post_component__(self, _):
        # 一秒收集10次
        self.debug_timer = None
        self.jump_timer = None
        self.move_start_pos = ()
        self.state_info_history = []
        self.is_debug_ai_vision = False
        self.node_test = None
        if sconst.IS_LOCAL_TEST:
            self.init_ai_debug()
            if self.auto_f12:
                self.change_debug_mode("", True)

    def __on_death_component__(self, attacker):
        self.debug_timer and self.cancel_timer(self.debug_timer)
        self.jump_timer and self.cancel_timer(self.jump_timer)
        self.path_record_timer and self.cancel_timer(self.path_record_timer)

    def __before_leave_space_component__(self):
        self.debug_timer and self.cancel_timer(self.debug_timer)
        self.jump_timer and self.cancel_timer(self.jump_timer)
        self.path_record_timer and self.cancel_timer(self.path_record_timer)

    @property
    def debug_output(self):
        return getattr(genv, 'e', None) is self

    def debug_log(self, formats, *args):
        if self.debug_output:
            self.logger.info(formats, *args)

    def init_ai_debug(self):
        self.debug_timer and self.cancel_timer(self.debug_timer)
        self.debug_timer = self.add_repeat_timer(0.1, self.debug_battleroyale_ai_info)
        if not self.node_test:
            self.node_test = BTBaseNode('node_test')
        self.path_record_timer = self.add_repeat_timer(5, self.debug_record_path)

    def debug_record_path(self):
        len_path = len(self.debug_path)
        pos = self.position
        if len_path > 1:
            if len_path > 200:
                self.debug_path.pop(0)
            last_pos = self.debug_path[-1]
            if formula.Distance3DSquare(last_pos, pos) < 1:
                return
        self.debug_path.append(CustomListType(pos))

    def debug_battleroyale_ai_info(self):
        # 内服才收集这个log，其他只保留关键信息
        info = {}
        helen_ai = self.helen_ai
        if not helen_ai:
            return
        is_in_aoi = self.ai_agent_cpp.is_in_avatar_aoi
        if not is_in_aoi or (not self.actor and not self.auto_f12):
            return
        if not helen_ai.ai_debug_on:
            helen_ai.start_ai_debug()

        ab = self.ai_blackboard
        ab_get_bool = ab.get_bool_value
        get_int_value = ab.get_int_value
        if sconst.IS_LOCAL_TEST and self.auto_f12:
            info.update(ab.get_all_blackboard_kvs())
            if self.is_debug_ai_vision:
                self.debug_vision_info = self.debug_get_ai_vision_targets_info()
            self.debug_threaten_info = self.debug_get_ai_threaten_info()
            # self.debug_sound_heat_info = self.debug_get_ai_sound_heat_info()
            info['yaw'] = str(self.yaw)
            # self.debug_helen_hide_in_tacticals()
            # self.debug_helen_nearby_tactical_points()
        elif sconst.IS_LOCAL_TEST:
            self.debug_threaten_info = self.debug_get_ai_threaten_info()
            self.debug_sound_heat_info = self.debug_get_ai_sound_heat_info()
            # 记录一些关键信息
            info['target'] = helen_ai.target_id
            info['line_of_shoot'] = ab_get_bool(ai_consts.AI_Target_line_of_shoot_BB_Key, False)
            info['change_target_index'] = get_int_value(ai_consts.AI_Can_Change_Target_BB_key, 0)
            info['target_in_sight'] = ab_get_bool(ai_consts.AI_Target_in_vision_BB_Key, False)
            info['target_lose_vision_time'] = ab.get_float_value(ai_consts.AI_Target_lose_vision_time_BB_Key, 0.0)
            info['move_end_pos'] = str(ab.get_tuple_value(ai_consts.AI_Move_End_Pos, (0, 0, 0)))
            info['last_known_pos'] = str(ab.get_tuple_value(ai_consts.AI_Target_last_know_pos_BB_Key, (0, 0, 0)))
            # 加几个index的显示
            info['tTask_search_index'] = str(get_int_value(ai_consts.AI_SQUAD_TASK_SEARCH_INDEX_BBK))
            info['tTask_defense_index'] = str(get_int_value(ai_consts.AI_SQUAD_TASK_DEFENSE_INDEX_BBK))
            info['tTask_relocate_index'] = str(get_int_value(ai_consts.AI_SQUAD_TASK_RELOCATE_INDEX_BBK))
            info['tTask_fire_index'] = str(get_int_value(ai_consts.AI_SQUAD_TASK_FIRE_INDEX_BBK))
            info['tTask_loot_index'] = str(get_int_value(ai_consts.AI_SQUAD_TASK_LOOT_INDEX_BBK))

        info['world_state_local'] = helen_ai.world_state_local
        info['ai_config_name'] = helen_ai.ai_config_name
        info['ai_goal_id'] = helen_ai.ai_goal_id
        info['current_ai_action_id'] = helen_ai.current_ai_action_id
        info['current_ai_threaten_config'] = helen_ai.threaten_config_id
        bt_runner = helen_ai.ai_bt_runner
        if hasattr(bt_runner, 'get_ai_bt_trace_info'):
            info['bt_trace_info'] = bt_runner.get_ai_bt_trace_info() if bt_runner else ''
            sub_bt_info = ''
            for sub_bt_runner in helen_ai.sub_ai_map.values():
                if sub_bt_runner:
                    sub_bt_info += sub_bt_runner.get_ai_bt_trace_info() + '\n'
            info['sub_bt_trace_info'] = sub_bt_info
        info['bt_trace_record'] = bt_runner.ai_trace_last_record if bt_runner else ''
        info['navigate_radius'] = str(getattr(self, 'navigate_radius', 0))
        info['move_start_pos'] = str(self.move_start_pos)
        # info['nav_pos'] = str(self.get_nav_pos())
        info['nav_yaw'] = str(getattr(self.navigator, 'navigate_yaw', 0))
        self.ai_info = info

    @rpc_method(CLIENT_ANY, Bool())
    def change_debug_mode(self, avatar_id, mode):
        avatar = EntityManager.getentity(avatar_id)
        print("change_debug_mode", avatar, avatar_id)
        self.auto_f12 = mode
        if self.auto_f12:
            if hasattr(genv, 'robot') and genv.robot and genv.robot is not self and not genv.robot.is_destroyed():
                genv.robot.auto_f12 = False
            genv.robot = self
            genv.e = self
            import six2.moves.builtins as __builtin__
            __builtin__.e = __builtin__.robot = self
            print(22222222, 'server get pointing entity  e:{}'.format(self.id))
            # avatar.SendLogToClient('server get pointing entity  e:{}'.format(self.id))

    @rpc_method(CLIENT_ANY, Int())
    def debug_add_gun(self, _, equip_item_id):
        if equip_item_id:
            self.space.game_logic.AddEquip(self,
                    equip_item_id,
                    replace_flag=1,
                    replace_and_not_drop=True,
                    setup_equip_skin=True)

            self.AddExtraAmmo()
            return

        self.AddDefaultEquipAndItems()
        self.AddExtraAmmo()
        self.update_resource_level()

    @rpc_method(CLIENT_ANY)
    def debug_arm_weapon(self, _):
        self.ai_arm_weapon()

    @rpc_method(CLIENT_ANY)
    def debug_pick_up(self, _):
        self.ai_pick_up(BTBaseNode("pick_up_test"))

    def debug_stop_ai_shoot(self):
        self.callall("HelenRobotShootEnd")
        self.debug_shoot_timer and self.cancel_timer(self.debug_shoot_timer)
        self.debug_repeat_shoot_timer and self.cancel_timer(self.debug_repeat_shoot_timer)
        self.debug_shoot_timer = None
        self.debug_repeat_shoot_timer = None

    def debug_ai_shoot(self, shoot_time):
        # 这里是强行让AI开枪，不校验有无target及是否有子弹
        if not sconst.IS_LOCAL_TEST:
            return
        self.debug_shoot_timer and self.cancel_timer(self.debug_shoot_timer)
        self.debug_repeat_shoot_timer and self.cancel_timer(self.debug_repeat_shoot_timer)
        self.debug_shoot_timer = self.add_timer(shoot_time, self.debug_stop_ai_shoot)
        weapon = self.GetCurWeapon()
        if not weapon:
            return False
        count, interval = self.helen_get_weapon_shoot_info(weapon)
        spell_id = 1
        self.callall('HelenRobotShootStart', spell_id, '')
        self.debug_repeat_shoot_timer = self.add_repeat_timer(interval, self._debug_ai_shoot)

    def _debug_ai_shoot(self):
        weapon = self.GetCurWeapon()
        if not weapon:
            return
        if weapon.ammo <= 0:
            return self.ai_reload_ammo()
        helen_ai = self.helen_ai
        if not self.actor:
            force_miss = False
            shoot_start_pos = helen_ai.get_weapon_pos()
            shoot_dir = formula.YawToVector(self.yaw)
            self.SimulateGunSpellStrike(shoot_start_pos, formula.Tuple(shoot_dir), force_miss)

        if weapon.ammo <= 0:
            # 换弹
            return self.ai_reload_ammo()

    @rpc_method(CLIENT_ANY, Str(), Int())
    def debug_ai_shoot_target(self, avatar_id, target_id, count):
        target = self.space.entities.get(target_id)
        # 强制拉一下yaw
        self.debug_lock_target(avatar_id)
        if target.hp < target.maxhp * .5:
            target.ChangeHp(target.maxhp)
        weapon = self.GetCurWeapon()
        if not weapon:
            return False
        if weapon.weapon_case.attr_stock_capacity - weapon.ammo > 0:
            weapon.DeltaAmmo(weapon.weapon_case.attr_stock_capacity - weapon.ammo)

        ret = self.ai_shoot_target(target, count, is_debug=True)
        self.cur_shoot_state = None
        return ret

    @rpc_method(CLIENT_ANY, Str(), Str())
    def debug_ai_run_eqs(self, _, target_id, eqs_key):
        rt = self.ai_run_eqs(eqs_key)
        if not self.ai_blackboard.has_valid_value(ai_consts.AI_Move_End_Pos):
            pos = None
        else:
            pos = self.ai_blackboard.get_tuple_value(ai_consts.AI_Move_End_Pos)
        self.draw_eqs_items()
        target = self.space.combat_avatars.get(target_id, None)
        target = getattr(target, 'master', target)
        target.SendLogToClient(str({
            'eqs_result': rt,
            'move_end_pos': pos,
            'move_end_node': self.ai_blackboard.get_value(ai_consts.AI_Move_End_Node),
        }))

    @rpc_method(CLIENT_ANY)
    def debug_reload_ammo(self, _):
        self.ai_reload_ammo()

    @rpc_method(CLIENT_ANY, Str())
    def debug_start_patrol(self, _, bind_avatar_id):
        random_x = random.randint(1, 10)
        random_z = random.randint(1, 10)
        pos = (self.position[0] + random_x, self.position[1], self.position[2] + random_z)
        self.ai_start_patrol(pos)

    @rpc_method(CLIENT_ANY)
    def debug_put_weapon(self, _):
        self.ai_put_weapon()

    @rpc_method(CLIENT_ANY, Str())
    def debug_fist_attack(self, _, target_id):
        target = self.space.entities.get(target_id)
        self.ai_fist_attack(self.node_test, target)

    @rpc_method(CLIENT_ANY, Str(), List())
    def debug_ai_move(self, _, prefer_type, target_pos):
        self.ai_move(prefer_type, target_pos)

    @rpc_method(CLIENT_ANY, Str(), List())
    def debug_ai_move_client(self, _, prefer_type, target_pos):
        self.ai_move_client(prefer_type, target_pos)

    @rpc_method(CLIENT_ANY, Bool())
    def debug_ai_change_targeting_mode(self, _, is_targeting):
        self.ai_change_targeting_mode(is_targeting)

    @rpc_method(CLIENT_ANY)
    def debug_stand(self, _):
        self.ai_enter_stand()

    @rpc_method(CLIENT_ANY, Int())
    def debug_crouch(self, _):
        self.ai_enter_crouch()

    @rpc_method(CLIENT_ANY)
    def debug_lie(self, _):
        self.ai_enter_lie()

    @rpc_method(CLIENT_ANY)
    def debug_jump(self, _):
        self.ai_jump()

    @rpc_method(CLIENT_ANY)
    def debug_ai_show(self, _):
        self.ai_show(self.node_test)

    @rpc_method(CLIENT_ANY)
    def debug_enter_crouch(self, _):
        self.ai_enter_crouch()

    @rpc_method(CLIENT_ANY)
    def debug_enter_stand(self, _):
        self.ai_enter_stand()

    # 状态测试
    @rpc_method(CLIENT_ANY, Str())
    def debug_can_enter_state(self, avatar, state_id):
        ret = self.can_enter_state(state_id)
        avatar = self.space.avatars.get(avatar)
        if avatar:
            avatar.SendLogToClient(str(ret))

    @rpc_method(CLIENT_ANY, Str())
    def debug_enter_state(self, avatar, state_id):
        ret = self.add_motion_state(state_id, state_info={'where': 'ai_lock_target'})
        ret and self.add_state_timer(state_id, None)

    @rpc_method(CLIENT_ANY, Str())
    def debug_cancel_state(self, _, state_id):
        self.remove_state(state_id)

    @rpc_method(CLIENT_ANY, Str())
    def debug_is_in_state(self, avatar, state_id):
        ret = self.is_in_state(state_id)
        avatar = self.space.entities.get(avatar)
        if avatar:
            avatar.SendLogToClient(str(ret))

    @rpc_method(CLIENT_ANY)
    def debug_disable_state(self, avatar):
        avatar = self.space.entities.get(avatar)
        if avatar:
            avatar.SendLogToClient(str(self.enable_states))

    @rpc_method(CLIENT_ANY)
    def debug_stop_move(self, _):
        self.cancel_nav('debug_stop_move')

    @rpc_method(CLIENT_ANY, EntityID())
    def debug_lock_target(self, avatar_id, target_id=None):
        avatar = EntityManager.getentity(avatar_id)
        print("debug_lock_target", avatar, avatar.combat_avatar)
        if not avatar:
            return
        avatar_c = avatar.combat_avatar
        if not avatar_c:
            return
        target_id = avatar_c.id
        self.helen_ai.set_target_id(target_id)
        self.ai_lock_target(self.node_test)

    @rpc_method(CLIENT_ANY)
    def debug_unlock(self, _):
        self.ai_unlock()

    @rpc_method(CLIENT_ANY)
    def debug_recovery(self, _):
        self.ai_recovery()

    @rpc_method(CLIENT_ANY)
    def debug_break_recovery(self, _):
        self.ai_break_recovery()

    @rpc_method(CLIENT_ANY, Int())
    def debug_ai_get_game_tag_pos(self, _, game_tag_type):
        self.guard_zone = list(range(1, 11))
        self.ai_get_game_tag_pos(game_tag_type, ai_consts.AI_Move_End_Pos)

    @rpc_method(CLIENT_ANY, Int(), Float())
    def debug_ai_shift(self, _, shift_type, shift_time):
        if shift_type == ai_consts.ShiftType_Random:
            shift_type = random.randint(1, 2)
        self.ai_shift(self.node_test, shift_type, shift_time)

    @rpc_method(CLIENT_ANY)
    def debug_ai_get_vehicle_pos(self, _):
        self.ai_get_nearest_vehicle_pos(100)

    @rpc_method(CLIENT_ANY)
    def debug_ai_get_on_vehicle(self, _):
        self.ai_get_on_vehicle(0)

    @rpc_method(CLIENT_ANY)
    def debug_ai_get_off_vehicle(self, _):
        self.ai_get_off_vehicle()

    @rpc_method(CLIENT_ANY)
    def debug_ai_drive_to(self, avatar_id):
        avatar = EntityManager.getentity(avatar_id)
        combat_avatar = avatar.combat_avatar

        if combat_avatar and getattr(combat_avatar, 'map_tag_pos', None):
            self.ai_drive_to(combat_avatar.map_tag_pos)
        else:
            print("add a map tag first!!!!!!!!!!!!!!!")

    @rpc_method(CLIENT_ANY)
    def debug_ai_switch_debug_info(self, _):
        self.ai_switch_drive_debug_info()

    @rpc_method(CLIENT_ANY)
    def debug_ai_get_patrol_point(self, _):
        if self.ai_get_patrol_point():
            self.ai_move('slow_move')
            # self.cancel_nav('debug_ai_get_patrol_point')
            target_pos = self.ai_blackboard.get_tuple_value(ai_consts.AI_Patrol_Pos_BB_Key)
            print("self.patrol_data", self.patrol_data)
            print("patrol_point", target_pos)
            if target_pos:
                self.ai_blackboard.set_tuple_value(ai_consts.AI_Move_End_Pos, target_pos)
                rt = self.ai_move('slow_move')
                print("ai_move", rt)

    @rpc_method(CLIENT_ANY)
    def debug_ai_get_peek_pos(self, _):
        helen_nav_tactical = self.space.game_logic.helen_nav_tactical
        if not helen_nav_tactical:
            return False
        if self.ai_get_peek_pos(20.0):
            print('AI_Peek_Pos_BB_Key', self.ai_blackboard.get_tuple_value(ai_consts.AI_Peek_Pos_BB_Key))
            print('AI_Peek_Side_BB_Key', self.ai_blackboard.get_int_value(ai_consts.AI_Peek_Side_BB_Key))
            # 把所有的点画出来
            x, _, z = self.position
            tactical_list = helen_nav_tactical.Query(x, z, 5.0)
            self.callall("draw_peek_pos", self.ai_blackboard.get_tuple_value(ai_consts.AI_Peek_Pos_BB_Key),
                         self.ai_blackboard.get_int_value(ai_consts.AI_Peek_Side_BB_Key), tactical_list)
        else:
            x, _, z = self.position
            tactical_list = helen_nav_tactical.Query(x, z, 5.0)
            self.callall("draw_peek_pos", (), 0, tactical_list)

    @rpc_method(CLIENT_ANY, Int())
    def debug_ai_arm_bomb(self, avatar_id, bomb_id=0):
        self.debug_lock_target(avatar_id)
        if not bomb_id:
            bomb_id = 64
        self.ai_arm_bomb(self.node_test, bomb_id)
        self.node_test.custom_timer_finish_ts = -1.0

    @rpc_method(CLIENT_ANY)
    def debug_ai_pre_use_bomb(self, avatar_id):
        self.debug_lock_target(avatar_id)
        self.ai_pre_use_bomb(self.node_test)
        self.remove_state(ai_consts.EState_PRE_THROW_GRENADE)
        self.node_test.custom_timer_finish_ts = -1.0

    @rpc_method(CLIENT_ANY)
    def debug_ai_use_bomb(self, avatar_id):
        self.debug_lock_target(avatar_id)
        self.ai_use_bomb(self.node_test)
        self.remove_state(ai_consts.EState_THROW_GRENADE)
        self.node_test.custom_timer_finish_ts = -1.0

    @rpc_method(CLIENT_ANY, Int())
    def debug_add_health(self, _, health_num=1000):
        self.max_health = health_num
        self.health = health_num

    @rpc_method(CLIENT_ANY)
    def debug_use_medicine(self, _):
        self.ai_use_medicine(self.node_test)

    @rpc_method(CLIENT_ANY, EntityID())
    def debug_wave_hand(self, _, target_id):
        self.debug_lock_target(target_id)
        self.ai_wave_hand(self.node_test)

    def forbidden_draw_eqs_items(self):
        self.callall('forbidden_draw_eqs_items')

    @rpc_method(CLIENT_ANY)
    def debug_attack_main_gate(self, _):
        self.ai_attack_main_gate()

    @rpc_method(CLIENT_ANY, Int())
    def debug_ai_pve_skill(self, avatar_id, index):
        self.debug_lock_target(avatar_id)
        self.ai_pve_skill(None, index)

    @rpc_method(CLIENT_ANY, Float())
    def debug_ai_chase_target(self, avatar_id, dis=3.0):
        self.debug_lock_target(avatar_id)
        self.ai_chase_target(prefer_type=ai_consts.MONSTER_NAV_PATH_COMMON_PREFER_SLOW_MOVE, dis=dis)

    @rpc_method(CLIENT_ANY, Float())
    def debug_ai_chase_target_melee(self, avatar_id, dis=3.0):
        self.debug_lock_target(avatar_id)
        self.ai_chase_target_melee(prefer_type=ai_consts.MONSTER_NAV_PATH_COMMON_PREFER_SLOW_MOVE, dis=dis)

    @rpc_method(CLIENT_ANY, Int(), Dict())
    def debug_ai_add_buff(self, avatar_id, buff_id, extra):
        if extra == {}:
            extra = None
        self.buff_mgr.AddBuff(buff_id, extra=extra)

    @rpc_method(CLIENT_ANY)
    def debug_ai_get_wall_point(self, _):
        if self.ai_get_wall_point():
            pt_idx = self.ai_blackboard.get_int_value(ai_consts.AI_GIANT_MAIN_GATE_IDX_BB_Key)
            wall_pos = self.ai_blackboard.get_tuple_value(ai_consts.AI_GIANT_MAIN_GATE_POS_BB_Key)
            wall_lock_pos = self.ai_blackboard.get_tuple_value(ai_consts.AI_GIANT_MAIN_GATE_LOCK_POS_BB_Key)
            self.ai_blackboard.set_tuple_value(ai_consts.AI_Move_End_Pos, wall_pos)
            self.ai_move('slow_move')
            print("ai_get_wall_point", pt_idx, wall_pos, wall_lock_pos)

    @rpc_method(CLIENT_ANY)
    def debug_ai_jump(self, _):
        self.ai_jump()

    @rpc_method(CLIENT_ANY, EntityID())
    def debug_ai_rescue(self, _, rescue_id=None):
        rescue_target = None
        entity_type = "HelenRobot" + self.game_logic.COMBAT_AVATAR_TYPE
        robots = self.entities_in_range_with_name(entity_type, 6.0)
        for robot in robots:
            if robot.faction == self.faction and robot.is_dying:
                rescue_target = robot
                break
        # rescue_target = self.space.entities.get(rescue_id)
        self.ai_rescue(rescue_target)

    @rpc_method(CLIENT_ANY, Str(), Str())
    def debug_single_action(self, _, action_name, xml_name):
        helen_ai = self.helen_ai
        try:
            helen_ai.ai_bt_runner.ai_name = action_name
            helen_ai.stop_ai_system()
            helen_ai.ai_bt_runner.ReloadAIByName(action_name, xml_name)
        except:
            import traceback
            return {"result": False, "info": "热更失败：%s" % traceback.format_exc()}
        else:
            return {"result": True, "info": "热更成功！"}

    def on_change_state(self, extra):
        if len(self.state_info_history) >= MAX_LOG_NUM:
            self.state_info_history.pop(0)
        extra['time_stamp'] = TimeUtil.Now()
        extra['position'] = self.position
        extra['states'] = str(self.real_states)
        self.state_info_history.append(extra)

    @rpc_method(CLIENT_ANY)
    def get_state_info_history(self, _):
        self.callall(
            'remote_write_monster_log',
            self.history_to_str(self.state_info_history),
            'state_info_history' + '  ' + str(TimeUtil.Now())
        )

    @rpc_method(CLIENT_ANY)
    def debug_ai_slide(self, _=None):
        self.ai_slide(self.node_test)

    @rpc_method(CLIENT_ANY)
    def debug_ai_rescue_self(self, _=None):
        self.ai_rescue_self()

    def history_to_str(self, infos):
        log = ''
        if isinstance(infos, dict):  # 对字典结构的拆分处理
            for key, val in infos.items():
                log = log + str(key) + ':\n'
                if isinstance(val, list):
                    for info in val:
                        log = log + str(info) + '\n'
                    log += '\n'
                else:
                    log = log + str(val) + '\n'
        elif isinstance(infos, list):
            for info in infos:
                log = log + str(info) + '\n'
        return log

    def debug_get_ai_vision_info(self):
        res = {}
        helen_ai = self.helen_ai
        if helen_ai:
            res.update({
                'common_range': helen_ai.vision_common_range,
                'common_angle': helen_ai.vision_half_angle,
                'focus_range': helen_ai.vision_focus_range,
                'focus_angle': helen_ai.vision_half_focus_angle,
                'blurry_start_range': helen_ai.vision_blurry_start_dis,
                'protected_distance': helen_ai.ai_proto.get('sensing_protected_distance', 5.0),
            })
        return res

    if sconst.IS_LOCAL_TEST:
        def debug_get_ai_vision_targets_info(self):
            res = {}
            helen_ai = self.helen_ai
            if helen_ai:
                threaten_list = helen_ai.threaten_list
                for e_id, vision_info in helen_ai.vision_info_dict.items():
                    if vision_info.in_vision_time > 0.0:
                        res[e_id] = [vision_info.in_vision, vision_info.in_vision_time, 1.0 if e_id == helen_ai.target_id else 0.0, threaten_list.get(e_id, -1.0)]
            return res

        def debug_get_ai_threaten_info(self):
            res = {}
            helen_ai = self.helen_ai
            if helen_ai:
                threaten_list = helen_ai.threaten_list
                for t_id, t_val in threaten_list.items():
                    vision_info = helen_ai.vision_info_dict.get(t_id, None)
                    if not vision_info:
                        continue
                    res[t_id] = [t_val, float(vision_info.in_vision), vision_info.distance, float(getattr(vision_info, 'in_sensing', 1.0)),
                                 helen_ai.query_recent_damage_in_time_gap(t_id, -1.0)]
                if res:
                    res['const'] = [helen_ai.THRE_VALID_THRESHOLD, helen_ai.THRE_DANGER_THRESHOLD]
            return res

        def debug_get_ai_sound_heat_info(self):
            res = {}
            helen_ai = self.helen_ai
            if helen_ai:
                heat_lru = helen_ai.sound_heat_lru
                for src_id, sound_heat in heat_lru.items():
                    code = 0
                    for key in sound_heat.last_info_dict.keys():
                        code += ai_consts.DEBUG_SOUND_HEAT_FACT_DICT[key]
                    res[src_id] = [float(sound_heat.cur_heat_val), float(sound_heat.is_in_ai_threaten), sound_heat.cur_heat_dis, float(code)]
                if res:
                    res['const'] = [helen_ai.SOUND_HEAT_OVERLOAD_THRESHOLD]
                    # res['const'].extend(helen_ai.visible_id_set)
            return res

    def debug_client_move(self, value):
        self.use_client_move = value
        if value:
            # 停止服务端移动
            self.cancel_nav('on_change_actor')
            # 停止服务端攀爬
            self.ai_climb_end()
            # 停止服务端lock
            self.ai_set_navigate_lock(False)
            # 停止服务端跳
        else:
            # 停止客户端移动
            self.cancel_nav_client('on_change_actor')

    def debug_helen_nearby_tactical_points(self):
        pos_bbk = ai_consts.AI_NEARBY_TACTICAL_POINT_POS
        type_bbk = ai_consts.AI_NEARBY_TACTICAL_POINT_TYPE
        pos = self.ai_blackboard.get_tuple_value(pos_bbk, None)
        pos_type = self.ai_blackboard.get_int_value(type_bbk, None)
        self.callall('on_debug_nearby_tactical_points', [(pos_type, pos)] if pos else ())

    def debug_helen_hide_in_tacticals(self):
        helen_royale_map = self.space.game_logic.helen_royale_map
        if not helen_royale_map:
            return

        self_bbk = ai_consts.AI_HIDE_IN_TACTICAL_ITEM_ID
        tar_bbk = ai_consts.AI_TARGET_HIDE_IN_TACTICAL_ITEM_ID
        self_id = self.ai_blackboard.get_int_value(self_bbk)
        tar_id = self.ai_blackboard.get_int_value(tar_bbk)
        debug_infos = [(), (), ()]
        if self_id:
            debug_infos[0] = helen_royale_map.indoor_map.debug_mapitem_to_py(self_id, False)
        if tar_id:
            debug_infos[1] = helen_royale_map.indoor_map.debug_mapitem_to_py(tar_id, False)
        self.callall('on_debug_hide_in_tacticals', debug_infos)

    def debug_robot_behavior(self, infos, box_item=None):
        # 只在trunk上开
        if not sconst.IS_LOCAL_TEST:
            return
        infos['robot_id'] = self.id
        infos['target'] = self.target.id if self.target else ''
        # self.callall("debug_robot_behavior", infos)
        if box_item:
            if box_item.proto_id == 9999:
                infos['behavior'] = 'pickup_deadbox'
            elif box_item.proto_id == 9998:
                infos['behavior'] = 'pickup_airdrop'
            else:
                return
        for avatar in self.space.avatars.values():
            if getattr(avatar, 'robot_static_debug_on', False):
                avatar.CallClient('debug_robot_behavior', infos)

    def debug_robot_pick_info(self, item_id):
        if not sconst.IS_LOCAL_TEST:
            return
        # if self.robot_state != RobotState.HelenActivate:
        #    return
        debug_pick_resource = self.space.game_logic.debug_pick_resource
        if item_id not in debug_pick_resource:
            debug_pick_resource[item_id] = 0
        debug_pick_resource[item_id] += 1

    def MyTest(self):
        from helen_data.server import tactical_area
        start_time = TimeUtil.Now()
        for i in list(range(10000)):
            city_id = self.cur_squad_select_city_info.city_id
            spaceno = self.space.spaceno
            all_data = tactical_area.data.get(spaceno)
            if not all_data:
                return ()

            snare_map = tactical_area.data[spaceno]['supply_box']
            if city_id not in snare_map:
                return ()
            pos_list = snare_map[city_id] if snare_map else ()
            box_dict = []
            for box_pos in pos_list:
                box_dict.extend(self.space.entities_in_range_with_name('SupplyBoxWithAni', box_pos[0], box_pos[-1], 1.0))
            box_dict = list(set(box_dict))

        print(888888888, box_dict)
        print(9999999999999, TimeUtil.Now() - start_time)

    def get_ai_snare_info(self):
        city_area_info = self.cur_squad_select_city_info
        snare_dict_list = city_area_info.get_snares_dict_list(self.faction)
        self.combat_near_snare_dict_list = snare_dict_list
        self.callall('draw_ai_snare_infos', snare_dict_list)

    def debug_burst_shoot(self, weapon):
        from gserver.data import ai_attack_data  # noqa
        equip_proto = weapon.equip_proto
        default_shoot_mode = equip_proto.get('default_shoot_mode', 2)  # 默认连发
        ai_attack_cnt = ai_attack_data.data.get('ai_atk_count', [8, 25])
        shoot_speed = equip_proto.get('shoot_speed', 0.1)  # 默认射速0.1
        if default_shoot_mode == 1:  # 单点
            count = 1
        else:  # 连发
            if self.actor:
                count = random.randint(1, 3)
            else:
                count = random.randint(ai_attack_cnt[0], ai_attack_cnt[-1])
        count = min(count, weapon.ammo)  # 不能超过剩余的子弹数量
        return count, shoot_speed

    def debug_vehicle_sweep_result(self, box, box_rotation, start_pos, end_pos, hit_pos, final_pos, is_hit):
        self.callall('draw_vehicle_sweep_result', box, box_rotation, start_pos, end_pos, hit_pos if hit_pos else (), final_pos, is_hit)

    def debug_vehicle_path_points(self, points):
        self.callall('draw_vehicle_path_points', points)

    def debug_vehicle_sweep_result_clear(self):
        self.callall('draw_vehicle_sweep_result_clear')

    def debug_ai_use_skill(self):
        self.ai_use_skill(self.node_test)

    def DoTest(self):
        self.weapon_test_timer = self.add_repeat_timer(5.0, self.DoWeaponShift)

    def DoWeaponShootTestImpl(self):
        if not self.tester:
            return
        self.debug_ai_shoot_target(self.tester, self.tester, 1)

    def DoWeaponShootTest(self):
        self.shoot_test_timer and self.cancel_timer(self.shoot_test_timer)
        self.shoot_test_timer = self.add_repeat_timer(1.0, self.DoWeaponShootTestImpl)
        self.DoWeaponShootTestImpl()

    def DoWeaponShift(self):
        cur_weapon = self.GetCurWeapon()
        cur_weapon_id = None
        cur_weapon_guid = None
        if cur_weapon:
            cur_weapon_guid = cur_weapon.guid
            cur_weapon_id = cur_weapon.item_id
        for i, info in equip_data.data.items():
            if 'mod_id' not in info:
                continue
            if info['equip_type'] != 1:
                continue
            if i <= self.last_test_weapon_id:
                continue
            if i == cur_weapon_id:
                continue
            if cur_weapon_guid:
                self.RemoveItem(cur_weapon_guid)
            self.last_test_weapon_id = i
            self.AddItemByItemID(info['mod_id'], 1)
            break
        else:
            self.last_test_weapon_id = 0
        if self.tester and self.tester in self.space.combat_avatars:
            self.space.combat_avatars[self.tester].CallClient('DoShoot', self.id)

    @rpc_method(CLIENT_ANY, Str(), Int())
    def DebugAddWeaponPart(self, _, equip_id, part_id):
        """ 给武器添加配件， 这里是靶场改枪
        """
        # TODO: 校验武器库中是否含有该配件
        if part_id not in gun_attachments_data.data:
            return
        if equip_id not in self.backpack:
            return

        item = self.backpack[equip_id]
        if not item.is_gun:
            return

        slot = gun_attachments_data.data[part_id]["attachment_type"]
        if slot not in item.gun_proto.get("modify_type_list", ()):
            return
        if part_id not in weapon_util.GetWeaponPartIdListByType(item.gun_id, slot):
            return
        if slot == consts.WeaponPartType_Muzzle and weapon_util.IsWeaponBarrelOwnMuzzle(item.part_slots):
            return
        if slot == consts.WeaponPartType_Underbarrel and weapon_util.IsWeaponBarrelOwnUnderbarrel(item.part_slots):
            return
        if slot == consts.WeaponPartType_Reargrip and weapon_util.IsWeaponStockOwnReargrip(item.part_slots):
            return
        item.AddPart(slot, part_id)

        item.ammo = min(item.ammo, item.weapon_case.attr_stock_capacity)
        if item.is_dual_weapon:
            item.left_hand_ammo = min(item.left_hand_ammo, item.weapon_case.attr_stock_capacity)
        else:
            item.left_hand_ammo = 0
        # self.CallClient("OnReloadWeapon", item.guid, item.ammo, item.left_hand_ammo)

    @rpc_method(CLIENT_ANY, List(), List(), List())
    def debug_robot_behavior(self, avatar_id, upper_behavior_info_list, move_behavior_info_list, additive_behavior_info_list):
        print(555555555555555555, upper_behavior_info_list, move_behavior_info_list, additive_behavior_info_list)
        self.debug_behavior_timer and self.cancel_timer(self.debug_behavior_timer)  # 取消上一次得timer
        self.debug_behavior_running_info.clear()  # 清空运行信息
        self.upper_behavior_info_list = upper_behavior_info_list
        self.move_behavior_info_list = move_behavior_info_list
        self.additive_behavior_info_list = additive_behavior_info_list
        self.debug_behavior_timer = self.add_repeat_timer(0.1, self.tick_robot_behavior_running)

    def tick_robot_behavior_running(self):
        if self.upper_behavior_info_list:
            # 当前执行行为
            cur_upper_behavior_index = self.debug_behavior_running_info.get('cur_upper_behavior_index', 0)
            cur_upper_behavior_info = self.upper_behavior_info_list[cur_upper_behavior_index % len(self.upper_behavior_info_list)]
            cur_upper_behavior_name = cur_upper_behavior_info['behavior_name']
            cur_upper_behavior_param = cur_upper_behavior_info['behavior_param']
            func = getattr(self, 'execute_' + cur_upper_behavior_name, None)
            if func:
                result = func(cur_upper_behavior_info)
            else:
                result = False
                func = getattr(self, cur_upper_behavior_name, None)
                if func:
                    result = func(**cur_upper_behavior_param)
            if result != ai_consts.RUNNING:
                cur_upper_behavior_index += 1
                self.debug_behavior_running_info['cur_upper_behavior_index'] = cur_upper_behavior_index

        if self.move_behavior_info_list:
            # 当前执行行为
            cur_move_behavior_index = self.debug_behavior_running_info.get('cur_move_behavior_index', 0)
            cur_move_behavior_info = self.move_behavior_info_list[cur_move_behavior_index % len(self.move_behavior_info_list)]
            cur_move_behavior_name = cur_move_behavior_info['behavior_name']
            cur_move_behavior_param = cur_move_behavior_info['behavior_param']
            func = getattr(self, 'execute_' + cur_move_behavior_name, None)
            if func:
                result = func(cur_move_behavior_info)
            else:
                result = False
                func = getattr(self, cur_move_behavior_name, None)
                if func:
                    result = func(**cur_move_behavior_param)
            if result != ai_consts.RUNNING:
                cur_move_behavior_index += 1
                self.debug_behavior_running_info['cur_move_behavior_index'] = cur_move_behavior_index

        if self.additive_behavior_info_list:
            # 当前执行行为
            cur_additive_behavior_index = self.debug_behavior_running_info.get('cur_additive_behavior_index', 0)
            cur_additive_behavior_info = self.additive_behavior_info_list[cur_additive_behavior_index % len(self.additive_behavior_info_list)]
            cur_additive_behavior_name = cur_additive_behavior_info['behavior_name']
            cur_additive_behavior_param = cur_additive_behavior_info['behavior_param']
            func = getattr(self, 'execute_' + cur_additive_behavior_name, None)
            if func:
                result = func(cur_additive_behavior_info)
            else:
                result = False
                func = getattr(self, cur_additive_behavior_name, None)
                if func:
                    result = func(**cur_additive_behavior_param)
            if result != ai_consts.RUNNING:
                cur_additive_behavior_index += 1
                self.debug_behavior_running_info['cur_additive_behavior_index'] = cur_additive_behavior_index

    def execute_ai_lock_target(self, behavior_info):
        behavior_param = behavior_info['behavior_param']
        is_lock_target = behavior_param.get('is_lock_target', False)
        if is_lock_target:
            target_id = behavior_param.get('target_id', '')
            self.helen_ai.set_target_id(target_id)
            return self.ai_lock_target(self.node_test)
        else:
            lock_pos = behavior_param.get('lock_pos', (0, 0, 0))
            if not lock_pos:
                return False
            self.ai_blackboard.set_tuple_value(ai_consts.AI_Patrol_Pos_BB_Key, lock_pos)
            return self.ai_lock_target(self.node_test, bbk=ai_consts.AI_Patrol_Pos_BB_Key)

    def execute_ai_slide(self, behavior_info):
        return self.ai_slide(self.node_test)

    def execute_ai_move(self, behavior_info):
        behavior_param = behavior_info['behavior_param']
        prefer_type = behavior_param.get('prefer_type', ai_consts.MONSTER_NAV_PATH_COMMON_PREFER_QUICK_MOVE)
        move_end_pos = behavior_param.get('position', (0, 0, 0))
        valid_pos = self.space.find_nearest_valid_pos_flag(move_end_pos[0], move_end_pos[1], move_end_pos[2], 0,
                                                           self.navigator_flag | 0x40)
        self.ai_blackboard.set_tuple_value(ai_consts.AI_Move_End_Pos, valid_pos)
        return self.ai_move(prefer_type)

