# -*- coding:utf-8 -*-

import MUI
import MImGui
import pyimgui
import MEngine
import builtins
from gclient import cconst
from HelenUtils.debug_imgui.BaseImGui import ImWindow
from gclient.util.debugger import onConsoleInput


class Im3CTestRobot(ImWindow):
    """
    用于定制3C测试机器人的行为
    """
    UPPER_BEHAVIOR = [
        "ai_lock_target:锁定",  # 锁定
        "ai_shoot_target:射击",  # 射击
        "ai_reload_ammo:换弹",  # 换弹
        "ai_put_weapon:收枪",  # 收枪
        "ai_arm_weapon:切枪",  # 切枪
    ]
    MOVE_BEHAVIOR = [
        "ai_move:移动",  # 移动
    ]
    ADDITIVE_BEHAVIOR = [
        'ai_slide:滑铲',
        'ai_jump:跳跃',
        "ai_crouch:下蹲",
        "ai_stand:站起",
    ]

    MOVE_TYPE = [
        'quick_move:小跑',
        'slow_move:正常走',
    ]
    def __init__(self):
        super(Im3CTestRobot, self).__init__("3C测试机器人")
        self.debug_obj = None
        # 行为集列表
        self.upper_behavior_info_list = [] # value: dict -> {behavior_name: str, behavior_param: dict}  # 开火换弹等
        self.move_behavior_info_list = [] # value: dict -> {behavior_name: str, behavior_param: dict}  # run， walk等
        self.additive_behavior_info_list = [] # value: dict -> {behavior_name: str, behavior_param: dict}  # 滑铲跳跃蹲起等等

    def render(self):
        # 创建机器人得节点
        if pyimgui.button("创建机器人"):
            onConsoleInput("#helen_robot 3C_Test")
        # 获取当前选中得机器人
        if hasattr(builtins, 'e') and builtins.e is not self.debug_obj:
            self.debug_obj = builtins.e

        if not self.debug_obj:
            return
        pyimgui.same_line(spacing=5)
        if pyimgui.button("执行"):
            self.run_behavior()
        # 渲染上半身得行为集
        self.render_upper_behavior()
        # 渲染移动行为集
        self.render_move_behavior()
        # 渲染附加行为集
        self.render_additive_behavior()

    def close(self):
        super(Im3CTestRobot, self).close()

    # region行为集渲染
    def render_upper_behavior(self):
        expanded, visible = pyimgui.collapsing_header("上半身行为集")
        if not expanded:
            return
        if pyimgui.button("添加上半身行为集"):
            behavior_info = {
                "behavior_index": 0,
                "behavior_name": "ai_lock_target",
                "behavior_param": {}
            }
            self.upper_behavior_info_list.append(behavior_info)
        pyimgui.same_line(spacing=5)
        if pyimgui.button("清空上半身行为集"):
            self.upper_behavior_info_list.clear()
        to_delete = set()
        for index, behavior_info in enumerate(self.upper_behavior_info_list):
            # render_common
            behavior_index = behavior_info['behavior_index']
            change, _behavior_index = pyimgui.combo("上半身行为集%s" % index, behavior_index, self.UPPER_BEHAVIOR, len(self.UPPER_BEHAVIOR))
            if change:
                behavior_info['behavior_index'] = _behavior_index
                behavior_info['behavior_name'] = self.UPPER_BEHAVIOR[_behavior_index].split(":")[0]
            pyimgui.same_line(spacing=5)
            if pyimgui.button("移除上半身行为%s" % index):
                to_delete.add(index)
            pyimgui.separator()
            pyimgui.text("参数:")
            behavior_name = behavior_info['behavior_name']
            func = getattr(self, 'render_%s' % behavior_name, None)
            func and func(index, behavior_info)

        for index in to_delete:
            self.upper_behavior_info_list.pop(index)

    def render_move_behavior(self):
        expanded, visible = pyimgui.collapsing_header("移动行为集")
        if not expanded:
            return
        if pyimgui.button("添加移动行为集"):
            behavior_info = {
                "behavior_index": 0,
                "behavior_name": "ai_move",
                "behavior_param": {}
            }
            self.move_behavior_info_list.append(behavior_info)
        pyimgui.same_line(spacing=5)
        if pyimgui.button("清空移动行为集"):
            self.move_behavior_info_list.clear()
        to_delete = set()
        for index, behavior_info in enumerate(self.move_behavior_info_list):
            # render_common
            behavior_index = behavior_info['behavior_index']
            change, _behavior_index = pyimgui.combo("移动行为集%s" % index, behavior_index, self.MOVE_BEHAVIOR,
                                                    len(self.MOVE_BEHAVIOR))
            if change:
                behavior_info['behavior_index'] = _behavior_index
                behavior_info['behavior_name'] = self.MOVE_BEHAVIOR[_behavior_index].split(":")[0]
            pyimgui.same_line(spacing=5)
            if pyimgui.button("移除移动行为%s" % index):
                to_delete.add(index)
            pyimgui.separator()
            pyimgui.text("参数:")
            behavior_name = behavior_info['behavior_name']
            func = getattr(self, 'render_%s' % behavior_name, None)
            func and func(index, behavior_info)

        for index in to_delete:
            self.move_behavior_info_list.pop(index)

    def render_additive_behavior(self):
        expanded, visible = pyimgui.collapsing_header("叠加行为集")
        if not expanded:
            return
        if pyimgui.button("添加叠加行为集"):
            behavior_info = {
                "behavior_index": 0,
                "behavior_name": "ai_slide",
                "behavior_param": {}
            }
            self.additive_behavior_info_list.append(behavior_info)
        pyimgui.same_line(spacing=5)
        if pyimgui.button("清空叠加行为集"):
            self.additive_behavior_info_list.clear()
        to_delete = set()
        for index, behavior_info in enumerate(self.additive_behavior_info_list):
            # render_common
            behavior_index = behavior_info['behavior_index']
            change, _behavior_index = pyimgui.combo("叠加行为集%s" % index, behavior_index, self.ADDITIVE_BEHAVIOR,
                                                    len(self.ADDITIVE_BEHAVIOR))
            if change:
                behavior_info['behavior_index'] = _behavior_index
                behavior_info['behavior_name'] = self.ADDITIVE_BEHAVIOR[_behavior_index].split(":")[0]
            pyimgui.same_line(spacing=5)
            if pyimgui.button("移除叠加行为%s" % index):
                to_delete.add(index)
            pyimgui.text("参数:")
            behavior_name = behavior_info['behavior_name']
            func = getattr(self, 'render_%s' % behavior_name, None)
            func and func(index, behavior_info)
            pyimgui.separator()
            pyimgui.separator()

        for index in to_delete:
            self.additive_behavior_info_list.pop(index)

    def render_ai_lock_target(self, index, behavior_info):
        # 锁定
        pyimgui.same_line(spacing=5)
        behavior_param = behavior_info['behavior_param']
        behavior_param['target_id'] = genv.player.id
        is_lock_target = behavior_param.get('is_lock_target', False)
        change, _is_lock_target = pyimgui.checkbox("是否锁定玩家%s" % index, is_lock_target)
        if change:
            behavior_param['is_lock_target'] = _is_lock_target
        if _is_lock_target:
            # 锁定玩家就不要填位置了
            return
        pyimgui.same_line(spacing=5)
        lock_pos = behavior_param.get('lock_pos', (0, 0, 0))
        change, _lock_pos = pyimgui.input_float3("锁定位置%s" % index, *lock_pos)
        if change:
            behavior_param['lock_pos'] = _lock_pos

    def render_ai_move(self, index, behavior_info):
        # 移动
        pyimgui.same_line(spacing=5)
        behavior_param = behavior_info['behavior_param']
        prefer_type_index = behavior_param.get('prefer_type_index', 0)
        change, _prefer_type_index = pyimgui.combo("移动类型%s" % index, prefer_type_index, self.MOVE_TYPE, len(self.MOVE_TYPE))
        if change:
            behavior_param['prefer_type_index'] = _prefer_type_index
            behavior_param['prefer_type'] = self.MOVE_TYPE[_prefer_type_index].split(":")[0]

        move_end_pos = behavior_param.get('position', (0, 0, 0))
        change, _move_end_pos = pyimgui.input_float3("移动位置%s" % index, *move_end_pos)
        if change:
            behavior_param['position'] = _move_end_pos
        pyimgui.same_line(spacing=5)
        if pyimgui.button("准星位置%s" % index):
            pos = self.get_screen_pos()
            if pos:
                behavior_param['position'] = pos

    # endregion
    # region 行为集执行，把所有行为集打包到服务端去让服务端AI执行
    def run_behavior(self):
        if not self.debug_obj:
            return
        self.debug_obj.server.debug_robot_behavior(self.upper_behavior_info_list, self.move_behavior_info_list, self.additive_behavior_info_list)
    # endregion

    def get_screen_pos(self):
        middle_pos = [MUI.GetScreenWidth() / 2.0, MUI.GetScreenHeight() / 2.0]
        camera = MEngine.GetGameplay().Player.Camera
        to_dir = camera.GetRayDirectionFromScreenPoint(int(middle_pos[0]),
                                                       int(middle_pos[1]))
        camera = MEngine.GetGameplay().Player.Camera
        camera_start = camera.GetOrigin()
        p_results, r = genv.player.space.RaycastWithPenetrate(camera_start,
                                                              1000,
                                                              cconst.PHYSICS_SHOOT_TEST,
                                                              with_trigger=False,
                                                              to_dir=to_dir)
        if r and r.IsHit and r.Body:
            pos = (r.Pos.x, r.Pos.y, r.Pos.z)
            return pos
        return None