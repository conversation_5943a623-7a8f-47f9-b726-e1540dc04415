# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 58-结算界面模型镜头配置表.xlsx, sheetname:结算场景模型位置
from taggeddict import taggeddict as TD

data = {
    2: TD({
        1: TD({
            'id': '2.1',
            'level_name': 'L_PostMatchTest01',
            'space_id': 2,
            'num': 1,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
        }), 
        2: TD({
            'id': '2.2',
            'space_id': 2,
            'num': 2,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (0.4, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
        }), 
        3: TD({
            'id': '2.3',
            'space_id': 2,
            'num': 3,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-0.9, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.9, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
        }), 
        4: TD({
            'id': '2.4',
            'space_id': 2,
            'num': 4,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-1.2, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.4, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
            'pos4': (1.2, 1252, 0, ),
            'rot4': 0.0,
            'ui_pos4': (0, 285, ),
        }), 
        5: TD({
            'id': '2.5',
            'space_id': 2,
            'num': 5,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
            'pos5': (1.49, 998.57, -1.48, ),
            'rot5': 1.15883444444444,
            'ui_pos5': (0, 285, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    10: TD({
        1: TD({
            'id': '10.1',
            'space_id': 10,
            'num': 1,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
        }), 
        2: TD({
            'id': '10.2',
            'space_id': 10,
            'num': 2,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
        }), 
        3: TD({
            'id': '10.3',
            'space_id': 10,
            'num': 3,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
        }), 
        4: TD({
            'id': '10.4',
            'space_id': 10,
            'num': 4,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
        }), 
        5: TD({
            'id': '10.5',
            'space_id': 10,
            'num': 5,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
            'pos5': (1.49, 998.57, -1.48, ),
            'rot5': 1.15883444444444,
            'ui_pos5': (0, 285, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    16: TD({
        1: TD({
            'id': '16.1',
            'space_id': 16,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '16.2',
            'space_id': 16,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '16.3',
            'space_id': 16,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '16.4',
            'space_id': 16,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '16.5',
            'space_id': 16,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    17: TD({
        1: TD({
            'id': '17.1',
            'space_id': 17,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '17.2',
            'space_id': 17,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '17.3',
            'space_id': 17,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '17.4',
            'space_id': 17,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '17.5',
            'space_id': 17,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    18: TD({
        1: TD({
            'id': '18.1',
            'space_id': 18,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '18.2',
            'space_id': 18,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '18.3',
            'space_id': 18,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '18.4',
            'space_id': 18,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '18.5',
            'space_id': 18,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    19: TD({
        1: TD({
            'id': '19.1',
            'space_id': 19,
            'num': 1,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
        }), 
        2: TD({
            'id': '19.2',
            'space_id': 19,
            'num': 2,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
        }), 
        3: TD({
            'id': '19.3',
            'space_id': 19,
            'num': 3,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
        }), 
        4: TD({
            'id': '19.4',
            'space_id': 19,
            'num': 4,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
        }), 
        5: TD({
            'id': '19.5',
            'space_id': 19,
            'num': 5,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
            'pos5': (1.49, 998.57, -1.48, ),
            'rot5': 1.15883444444444,
            'ui_pos5': (0, 285, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    21: TD({
        1: TD({
            'id': '21.1',
            'space_id': 21,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '21.2',
            'space_id': 21,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '21.3',
            'space_id': 21,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '21.4',
            'space_id': 21,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '21.5',
            'space_id': 21,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    22: TD({
        1: TD({
            'id': '22.1',
            'space_id': 22,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '22.2',
            'space_id': 22,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '22.3',
            'space_id': 22,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '22.4',
            'space_id': 22,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '22.5',
            'space_id': 22,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    23: TD({
        1: TD({
            'id': '23.1',
            'space_id': 23,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '23.2',
            'space_id': 23,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '23.3',
            'space_id': 23,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '23.4',
            'space_id': 23,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '23.5',
            'space_id': 23,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    25: TD({
        1: TD({
            'id': '25.1',
            'space_id': 25,
            'num': 1,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
        }), 
        2: TD({
            'id': '25.2',
            'space_id': 25,
            'num': 2,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
        }), 
        3: TD({
            'id': '25.3',
            'space_id': 25,
            'num': 3,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
        }), 
        4: TD({
            'id': '25.4',
            'space_id': 25,
            'num': 4,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
        }), 
        5: TD({
            'id': '25.5',
            'space_id': 25,
            'num': 5,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 1.63506777777778,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
            'pos5': (1.49, 998.57, -1.48, ),
            'rot5': 1.15883444444444,
            'ui_pos5': (0, 285, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    32: TD({
        1: TD({
            'id': '32.1',
            'space_id': 32,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '32.2',
            'space_id': 32,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '32.3',
            'space_id': 32,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '32.4',
            'space_id': 32,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '32.5',
            'space_id': 32,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '32.6',
            'space_id': 32,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    33: TD({
        1: TD({
            'id': '33.1',
            'space_id': 33,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '33.2',
            'space_id': 33,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '33.3',
            'space_id': 33,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '33.4',
            'space_id': 33,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '33.5',
            'space_id': 33,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    37: TD({
        1: TD({
            'id': '37.1',
            'space_id': 37,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '37.2',
            'space_id': 37,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '37.3',
            'space_id': 37,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '37.4',
            'space_id': 37,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '37.5',
            'space_id': 37,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    41: TD({
        1: TD({
            'id': '41.1',
            'space_id': 41,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '41.2',
            'space_id': 41,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '41.3',
            'space_id': 41,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '41.4',
            'space_id': 41,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '41.5',
            'space_id': 41,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '41.6',
            'space_id': 41,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    46: TD({
        1: TD({
            'id': '46.1',
            'space_id': 46,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '46.2',
            'space_id': 46,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '46.3',
            'space_id': 46,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '46.4',
            'space_id': 46,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '46.5',
            'space_id': 46,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '46.6',
            'space_id': 46,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    51: TD({
        1: TD({
            'id': '51.1',
            'level_name': 'L_PostMatchTest01',
            'space_id': 51,
            'num': 1,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
        }), 
        2: TD({
            'id': '51.2',
            'space_id': 51,
            'num': 2,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (0.4, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
        }), 
        3: TD({
            'id': '51.3',
            'space_id': 51,
            'num': 3,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-0.9, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.9, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
        }), 
        4: TD({
            'id': '51.4',
            'space_id': 51,
            'num': 4,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-1.2, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.4, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
            'pos4': (1.2, 1252, 0, ),
            'rot4': 0.0,
            'ui_pos4': (0, 285, ),
        }), 
        5: TD({
            'id': '51.5',
            'space_id': 51,
            'num': 5,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
            'pos5': (1.49, 998.57, -1.48, ),
            'rot5': 1.15883444444444,
            'ui_pos5': (0, 285, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    53: TD({
        1: TD({
            'id': '53.1',
            'space_id': 53,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '53.2',
            'space_id': 53,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '53.3',
            'space_id': 53,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '53.4',
            'space_id': 53,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '53.5',
            'space_id': 53,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '53.6',
            'space_id': 53,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    60: TD({
        1: TD({
            'id': '60.1',
            'level_name': 'L_PostMatchTest01',
            'space_id': 60,
            'num': 1,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
        }), 
        2: TD({
            'id': '60.2',
            'space_id': 60,
            'num': 2,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (0.4, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
        }), 
        3: TD({
            'id': '60.3',
            'space_id': 60,
            'num': 3,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-0.9, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.9, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
        }), 
        4: TD({
            'id': '60.4',
            'space_id': 60,
            'num': 4,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-1.2, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.4, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
            'pos4': (1.2, 1252, 0, ),
            'rot4': 0.0,
            'ui_pos4': (0, 285, ),
        }), 
        5: TD({
            'id': '60.5',
            'space_id': 60,
            'num': 5,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
            'pos5': (1.49, 998.57, -1.48, ),
            'rot5': 1.15883444444444,
            'ui_pos5': (0, 285, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    61: TD({
        1: TD({
            'id': '61.1',
            'level_name': 'L_PostMatchTest01',
            'space_id': 61,
            'num': 1,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
        }), 
        2: TD({
            'id': '61.2',
            'space_id': 61,
            'num': 2,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (0.4, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
        }), 
        3: TD({
            'id': '61.3',
            'space_id': 61,
            'num': 3,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-0.9, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.9, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
        }), 
        4: TD({
            'id': '61.4',
            'space_id': 61,
            'num': 4,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-1.2, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.4, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
            'pos4': (1.2, 1252, 0, ),
            'rot4': 0.0,
            'ui_pos4': (0, 285, ),
        }), 
        5: TD({
            'id': '61.5',
            'space_id': 61,
            'num': 5,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
            'pos5': (1.49, 998.57, -1.48, ),
            'rot5': 1.15883444444444,
            'ui_pos5': (0, 285, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    65: TD({
        1: TD({
            'id': '65.1',
            'level_name': 'L_PostMatchTest01',
            'space_id': 65,
            'num': 1,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
        }), 
        2: TD({
            'id': '65.2',
            'space_id': 65,
            'num': 2,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (0.4, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
        }), 
        3: TD({
            'id': '65.3',
            'space_id': 65,
            'num': 3,
            'pos1': (0, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-0.9, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.9, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
        }), 
        4: TD({
            'id': '65.4',
            'space_id': 65,
            'num': 4,
            'pos1': (-0.4, 1252, 0, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (-1.2, 1252, 0, ),
            'rot2': 0.0,
            'ui_pos2': (0, 400, ),
            'pos3': (0.4, 1252, 0, ),
            'rot3': 0.0,
            'ui_pos3': (0, 345, ),
            'pos4': (1.2, 1252, 0, ),
            'rot4': 0.0,
            'ui_pos4': (0, 285, ),
        }), 
        5: TD({
            'id': '65.5',
            'space_id': 65,
            'num': 5,
            'pos1': (2.51, 998.57, 0.138, ),
            'rot1': 0.0,
            'ui_pos1': (0, 650, ),
            'pos2': (2.25, 998.56, -0.72, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (0, 400, ),
            'pos3': (2.201, 998.57, 1.31, ),
            'rot3': 2.07815666666667,
            'ui_pos3': (0, 345, ),
            'pos4': (2.09, 998.58, -1.56, ),
            'rot4': 1.15883444444444,
            'ui_pos4': (0, 285, ),
            'pos5': (1.49, 998.57, -1.48, ),
            'rot5': 1.15883444444444,
            'ui_pos5': (0, 285, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    77: TD({
        1: TD({
            'id': '77.1',
            'space_id': 77,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '77.2',
            'space_id': 77,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '77.3',
            'space_id': 77,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '77.4',
            'space_id': 77,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '77.5',
            'space_id': 77,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '77.6',
            'space_id': 77,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    78: TD({
        1: TD({
            'id': '78.1',
            'space_id': 78,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '78.2',
            'space_id': 78,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '78.3',
            'space_id': 78,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '78.4',
            'space_id': 78,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '78.5',
            'space_id': 78,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '78.6',
            'space_id': 78,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    79: TD({
        1: TD({
            'id': '79.1',
            'space_id': 79,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '79.2',
            'space_id': 79,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '79.3',
            'space_id': 79,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '79.4',
            'space_id': 79,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '79.5',
            'space_id': 79,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '79.6',
            'space_id': 79,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    80: TD({
        1: TD({
            'id': '80.1',
            'space_id': 80,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '80.2',
            'space_id': 80,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '80.3',
            'space_id': 80,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '80.4',
            'space_id': 80,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '80.5',
            'space_id': 80,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '80.6',
            'space_id': 80,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    81: TD({
        1: TD({
            'id': '81.1',
            'space_id': 81,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '81.2',
            'space_id': 81,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '81.3',
            'space_id': 81,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '81.4',
            'space_id': 81,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '81.5',
            'space_id': 81,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '81.6',
            'space_id': 81,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
    82: TD({
        1: TD({
            'id': '82.1',
            'space_id': 82,
            'num': 1,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        2: TD({
            'id': '82.2',
            'space_id': 82,
            'num': 2,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        3: TD({
            'id': '82.3',
            'space_id': 82,
            'num': 3,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        4: TD({
            'id': '82.4',
            'space_id': 82,
            'num': 4,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        5: TD({
            'id': '82.5',
            'space_id': 82,
            'num': 5,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
        6: TD({
            'id': '82.6',
            'space_id': 82,
            'num': 6,
            'pos1': (2.51, 998.576294, 0.0, ),
            'rot1': 1.52028333333333,
            'ui_pos1': (0, 650, ),
            'pos2': (2.35, 998.559, -0.74, ),
            'rot2': 1.37811111111111,
            'ui_pos2': (-10, 100, ),
            'pos3': (2.35, 998.567, 0.757, ),
            'rot3': 1.82617166666667,
            'ui_pos3': (20, 100, ),
            'pos4': (2.42, 998.58, -1.49, ),
            'rot4': 1.03026888888889,
            'ui_pos4': (0, 650, ),
            'pos5': (2.2, 998.567, 1.55, ),
            'rot5': 2.07815666666667,
            'ui_pos5': (0, 100, ),
            'pos6': (2.13, 998.54, -2.24, ),
            'rot6': 0.762426666666667,
            'ui_pos6': (-20, 100, ),
        }), 
    }), 
}
