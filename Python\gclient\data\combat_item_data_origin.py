# -*- coding: utf-8 -*-
# flake8: noqa
# generated by: excel_to_data.py
# generated from , post_process.py, GenerateUseItemMode
from taggeddict import taggeddict as TD
data = {
    1: TD({
        'id': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100001, 
        'icon': 735, 
        'icon_outline': 325, 
        'item_description': '原装M4A1步枪', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 1, 
    }), 
    10: TD({
        'id': 10, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 8, 
        'ground_max_stack_limit': 2, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 107, 
        'item_description': '使用后会扫描周围，暴露敌人所在位置', 
        'item_ground_model_id': 508, 
        'item_sub_type': 12, 
        'item_type': 101, 
        'name': '侦查手雷', 
        'quality': 1, 
        'short_tips': '投掷物', 
    }), 
    100: TD({
        'id': 100, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 57, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10164, 
        'item_description': '增加毒气陷阱爆炸范围，并产生干扰效果，只能存储1个', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '毒气陷阱-剧毒', 
    }), 
    10001: TD({
        'id': 10001, 
        'bag_max_stack_limit': 1, 
        'equip_id': 15, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 55, 
        'icon': 736, 
        'icon_outline': 177, 
        'icon_outline_2': 799, 
        'item_description': '半自动手枪，可靠的副武器', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Glock', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    10002: TD({
        'id': 10002, 
        'bag_max_stack_limit': 1, 
        'equip_id': 15, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 4002, 
        'icon': 651, 
        'icon_outline': 177, 
        'icon_outline_2': 799, 
        'item_description': '半自动手枪，可靠的副武器', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Glock', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    10003: TD({
        'id': 10003, 
        'bag_max_stack_limit': 1, 
        'equip_id': 15, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 4003, 
        'icon': 652, 
        'icon_outline': 177, 
        'icon_outline_2': 799, 
        'item_description': '半自动手枪，可靠的副武器', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Glock', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    10004: TD({
        'id': 10004, 
        'bag_max_stack_limit': 1, 
        'equip_id': 15, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 4004, 
        'icon': 653, 
        'icon_outline': 177, 
        'icon_outline_2': 799, 
        'item_description': '半自动手枪，可靠的副武器', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Glock', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    10005: TD({
        'id': 10005, 
        'bag_max_stack_limit': 1, 
        'equip_id': 74, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100015, 
        'icon': 742, 
        'icon_outline': 326, 
        'item_description': '发射.50子弹的重型手枪，子弹威力大', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Desert Eagle', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    10006: TD({
        'id': 10006, 
        'bag_max_stack_limit': 1, 
        'equip_id': 74, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 15003, 
        'icon': 687, 
        'icon_outline': 326, 
        'item_description': '发射.50子弹的重型手枪，子弹威力大', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Desert Eagle', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    10007: TD({
        'id': 10007, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 74, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 15004, 
        'icon': 688, 
        'icon_outline': 326, 
        'item_description': '发射.50子弹的重型手枪，子弹威力大', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Desert Eagle', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    10008: TD({
        'id': 10008, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 74, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 15005, 
        'icon': 689, 
        'icon_outline': 326, 
        'item_description': '发射.50子弹的重型手枪，子弹威力大', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Desert Eagle', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    1001: TD({
        'id': 1001, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 300, 
        'has_cancel': True, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1096, 
        'ig_voice_CD_id': (1148, ), 
        'ig_voice_id': (1147, ), 
        'item_description': '音乐伙伴波波飞出，在目标位置生成混音台，音浪范围内的友方获得治疗。', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '元气音域', 
        'quality': 4, 
        'skill_full_description': '音乐伙伴波波飞出，在目标位置生成混音台，音浪范围内的友方获得治疗。', 
        'take_directly': True, 
    }), 
    100101: TD({
        'id': 100101, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1001, 
        'icon': 735, 
        'icon_outline': 325, 
        'item_description': '原装M4A1步枪', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 1, 
    }), 
    100102: TD({
        'id': 100102, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1002, 
        'icon': 645, 
        'icon_outline': 325, 
        'item_description': '装备全息瞄具的M4A1步枪', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 2, 
    }), 
    100103: TD({
        'id': 100103, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1003, 
        'icon': 646, 
        'icon_outline': 325, 
        'item_description': '装备全息镜，强化中距离作战能力', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1 - 中距离', 
        'quality': 3, 
    }), 
    100104: TD({
        'id': 100104, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1004, 
        'icon': 647, 
        'icon_outline': 325, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1 - 近距离强化', 
        'quality': 4, 
    }), 
    1002: TD({
        'id': 1002, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            8: 'hold', 
        }), 
        'equip_id': 301, 
        'has_cancel': True, 
        'hold': TD({
            10: 'recycle', 
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1097, 
        'ig_voice_CD_id': (1151, ), 
        'ig_voice_id': (1150, ), 
        'item_description': '创造一个具有加速效果的圆环，穿过友方获得持续一段时间的移速加成。', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '律动加速环', 
        'quality': 4, 
        'skill_full_description': '创造一个具有加速效果的圆环，友方穿过时可获得持续一段时间的移速加成。', 
        'take_directly': True, 
    }), 
    100201: TD({
        'id': 100201, 
        'bag_max_stack_limit': 1, 
        'equip_id': 2, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100002, 
        'icon': 1031, 
        'item_description': '原装MP5', 
        'item_ground_model_id': 618, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'MP5', 
        'quality': 1, 
    }), 
    100202: TD({
        'id': 100202, 
        'bag_max_stack_limit': 1, 
        'equip_id': 2, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 200201, 
        'icon': 1031, 
        'item_description': '装备全息镜，强化中距离作战能力', 
        'item_ground_model_id': 618, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'MP5 - 全息', 
        'quality': 2, 
    }), 
    100203: TD({
        'id': 100203, 
        'bag_max_stack_limit': 1, 
        'equip_id': 2, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 200202, 
        'icon': 1031, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 618, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'MP5 - 轻量级', 
        'quality': 3, 
    }), 
    100204: TD({
        'id': 100204, 
        'bag_max_stack_limit': 1, 
        'equip_id': 2, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 200203, 
        'icon': 1031, 
        'item_description': '加装镭射和50发弹鼓，最大化近距离作战能力', 
        'item_ground_model_id': 618, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'MP5 - 腰射满改', 
        'quality': 4, 
    }), 
    1003: TD({
        'id': 1003, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 303, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 1093, 
        'ig_voice_id': (1129, ), 
        'item_description': '朝输入方向短暂冲刺，冲刺期间无视重力', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '机动突跃', 
        'quality': 4, 
        'skill_full_description': '进行一段高速短冲刺。面对墙壁跳跃时，再次使用跳跃键可触发二段跳。', 
        'take_directly': True, 
    }), 
    1004: TD({
        'id': 1004, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            8: 'hold', 
        }), 
        'equip_id': 304, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
            9: 'using', 
        }), 
        'icon': 1094, 
        'ig_voice_id': (1129, ), 
        'ignore_quick_mode': True, 
        'item_description': '投掷电弧苦无，可命中敌方造成减速和伤害。X秒后苦无爆炸，造成范围伤害和减速。', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '落雷刃', 
        'quality': 4, 
        'skill_full_description': '投掷电弧苦无，可命中敌方造成减速和伤害。3秒后苦无爆炸，造成范围伤害和减速。', 
        'take_directly': True, 
    }), 
    1005: TD({
        'id': 1005, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 305, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 1102, 
        'ig_voice_CD_id': (1193, ), 
        'ig_voice_id': (1192, ), 
        'item_description': '使用后变身成数码鲨鱼，以鱼形态移动', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'mark_voice_id': (1194, ), 
        'name': '鲨鱼溜溜', 
        'quality': 4, 
        'skill_full_description': '鲨琪把自己变成一只数码鲨鱼，提升移动能力。', 
        'take_directly': True, 
    }), 
    1006: TD({
        'id': 1006, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 306, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1103, 
        'ig_voice_CD_id': (1197, ), 
        'ig_voice_id': (1195, ), 
        'item_description': '以自身为中心开启数码矩阵，矩阵范围内敌人位置暴露，穿越边界会减速', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'mark_voice_id': (1198, ), 
        'name': '数字水族馆', 
        'quality': 4, 
        'skill_full_description': '创造一个数码领域，对进入领域的敌人揭示其位置并造成短暂减速，领域内鲨琪移速提升。', 
        'take_directly': True, 
    }), 
    100601: TD({
        'id': 100601, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100006, 
        'icon': 343, 
        'item_description': '原装Origin12', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Origin12', 
        'quality': 1, 
    }), 
    100602: TD({
        'id': 100602, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6002, 
        'icon': 343, 
        'item_description': '加装扩容弹匣，提升火力持续性', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Origin12 - 扩容', 
        'quality': 2, 
    }), 
    100603: TD({
        'id': 100603, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6014, 
        'icon': 343, 
        'item_description': '加装延长枪管，提升近距离作战可靠性', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Origin12 - 中近距离作战', 
        'quality': 3, 
    }), 
    100604: TD({
        'id': 100604, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6003, 
        'icon': 343, 
        'item_description': '满改Origin12，最大化近距离作战能力', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Origin12 - 腰射满改', 
        'quality': 4, 
    }), 
    1007: TD({
        'id': 1007, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 307, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1099, 
        'ig_voice_id': (78, ), 
        'item_description': '强化受无人机影响的队友，提升多种能力', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '为我心跳', 
        'quality': 4, 
        'skill_full_description': '厄洛斯跟随玩家时，按下技能可强化目标玩家，使其获得临时生命值、移速提升、射速提升。', 
        'take_directly': True, 
    }), 
    100801: TD({
        'id': 100801, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100008, 
        'icon': 344, 
        'item_description': '原装Vector', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Vector', 
        'quality': 1, 
    }), 
    100802: TD({
        'id': 100802, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8002, 
        'icon': 344, 
        'item_description': '安装了全息瞄具', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Vector - 全息', 
        'quality': 2, 
    }), 
    100803: TD({
        'id': 100803, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8003, 
        'icon': 344, 
        'item_description': '弹容量提升', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Vector - 轻量级', 
        'quality': 3, 
    }), 
    100804: TD({
        'id': 100804, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8004, 
        'icon': 344, 
        'item_description': '最大化近距离作战能力', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Vector - 腰射满改', 
        'quality': 4, 
    }), 
    101: TD({
        'id': 101, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 58, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10164, 
        'item_description': '毒气陷阱隐身，但爆炸伤害降低,可存储2个', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '毒气陷阱-防御', 
    }), 
    1010: TD({
        'id': 1010, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 421, 
        'has_cancel': True, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 1104, 
        'ig_voice_id': (57, ), 
        'item_description': '释放一个幻象并隐身，再次释放技能可传送到幻象当前所处位置。', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '命运替演', 
        'short_tips': '释放一个幻象并隐身，再次释放技能可传送到幻象当前所处位置。', 
        'skill_full_description': '多里安向面朝方向制造一个分身幻象，再次使用技能可传送至分身位置。', 
        'take_directly': True, 
    }), 
    101001: TD({
        'id': 101001, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10001, 
        'icon': 861, 
        'icon_outline': 174, 
        'item_description': '原装AK12步枪', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK12', 
        'quality': 1, 
    }), 
    101002: TD({
        'id': 101002, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10002, 
        'icon': 861, 
        'icon_outline': 174, 
        'item_description': '强化了近距离作战能力的AK12步枪', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK12', 
        'quality': 2, 
    }), 
    101003: TD({
        'id': 101003, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10003, 
        'icon': 861, 
        'icon_outline': 174, 
        'item_description': '装备了扩容弹匣的AK12步枪', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK12-中距离', 
        'quality': 3, 
    }), 
    101004: TD({
        'id': 101004, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10004, 
        'icon': 861, 
        'icon_outline': 174, 
        'item_description': '强化了远距离作战能力的AK12步枪', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK12-远距离强化', 
        'quality': 4, 
    }), 
    1011: TD({
        'id': 1011, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 422, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10270, 
        'ig_voice_id': (57, ), 
        'item_description': '释放一个自身的幻象，且在此期间内自己进入隐身状态。开火等进攻行为会解除隐身。', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '幻象传送占位', 
        'short_tips': '释放一个自身的幻象，且在此期间内自己进入隐身状态。开火等进攻行为会解除隐身。幻象持续10秒，CD60秒。', 
        'skill_full_description': '投掷脱身道具，触爆产生视障烟幕，范围内友方获得隐身效果。', 
        'take_directly': True, 
    }), 
    1012: TD({
        'id': 1012, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            8: 'hold', 
        }), 
        'equip_id': 426, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1105, 
        'icon_outline': 11462, 
        'item_sub_type': 18, 
        'item_type': 101, 
        'mark_voice_id': (61, ), 
        'name': '谎言之幕', 
        'quality': 4, 
        'skill_full_description': '投掷脱身道具，触爆产生视障烟幕，范围内友方获得隐身效果。', 
        'take_directly': True, 
    }), 
    1013: TD({
        'id': 1013, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 308, 
        'has_cancel': True, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1100, 
        'icon_outline': 11462, 
        'item_sub_type': 11, 
        'item_type': 101, 
        'name': '弹跳恶魔', 
        'skill_full_description': '投掷恶魔粘弹，第一次碰撞后持续移动、反弹，对敌方造成伤害和击退效果。', 
        'take_directly': True, 
    }), 
    1014: TD({
        'id': 1014, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 309, 
        'has_cancel': True, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1101, 
        'icon_outline': 11462, 
        'item_sub_type': 18, 
        'item_type': 101, 
        'multi_using_code': 'ManualTriggerBomb', 
        'name': '震荡粘雷', 
        'quality': 4, 
        'skill_full_description': '投掷粘雷，再次按下技能引爆。对敌人造成伤害和击退，对自身只有击退效果。', 
        'take_directly': True, 
    }), 
    1015: TD({
        'id': 1015, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 310, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1106, 
        'ig_voice_id': (1127, ), 
        'item_description': '蓄力后向前突刺，快速移动并对途径敌人造成伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '踏风斩', 
        'quality': 4, 
        'skill_full_description': '按下Q消耗能量进入蓄力，松开时释放技能向前冲刺，并对沿途的敌人造成伤害。蓄力越久冲刺距离越远。白止的技能都需要消耗能量来使用，能量会随时间自然恢复，也可以通过对敌人造成伤害恢复。', 
        'take_directly': True, 
    }), 
    101501: TD({
        'id': 101501, 
        'bag_max_stack_limit': 1, 
        'equip_id': 74, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100015, 
        'icon': 742, 
        'icon_outline': 326, 
        'item_description': '发射.50子弹的重型手枪，子弹威力大', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Desert Eagle', 
        'quality': 1, 
    }), 
    1016: TD({
        'id': 1016, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            8: 'hold', 
            'cache': (8, ), 
        }), 
        'equip_id': 311, 
        'has_cancel': True, 
        'hold': TD({
            8: 'using', 
        }), 
        'icon': 1107, 
        'item_description': '格挡来自前方的子弹', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '未济势', 
        'quality': 4, 
        'skill_full_description': '白止横刀进入格挡状态，持续期间，可消耗能量格挡来自正面的攻击。', 
        'take_directly': True, 
        'using': TD({
            10: 'recycle', 
            3: 'recycle', 
            'cache': (3, ), 
        }), 
    }), 
    1017: TD({
        'id': 1017, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 312, 
        'has_cancel': True, 
        'icon': 1109, 
        'item_description': '强化自身能力', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '完美萃取', 
        'quality': 4, 
        'skill_full_description': '伊迪丝摄入特制试剂强化自身，持续期间，移速提升。', 
        'take_directly': True, 
    }), 
    101801: TD({
        'id': 101801, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100018, 
        'icon': 704, 
        'icon_outline': 324, 
        'item_description': '原装M700狙击枪', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700', 
        'quality': 1, 
    }), 
    101802: TD({
        'id': 101802, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18001, 
        'icon': 704, 
        'icon_outline': 324, 
        'item_description': '适合中距离作战的M700狙击枪', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700 - 轻量化', 
        'quality': 2, 
    }), 
    101803: TD({
        'id': 101803, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18002, 
        'icon': 705, 
        'icon_outline': 324, 
        'item_description': '最大化远距离作战能力的M700狙击枪', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700 - 远距离', 
        'quality': 3, 
    }), 
    101804: TD({
        'id': 101804, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18003, 
        'icon': 705, 
        'icon_outline': 324, 
        'item_description': '安装了消音器的M700狙击枪', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700', 
        'quality': 4, 
    }), 
    102: TD({
        'id': 102, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 59, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10160, 
        'item_description': '设置一个范围治疗无人机，可持续治疗所受伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '治疗无人机', 
    }), 
    102001: TD({
        'id': 102001, 
        'bag_max_stack_limit': 1, 
        'equip_id': 79, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 20001, 
        'icon': 709, 
        'icon_outline': 321, 
        'item_description': '装备扩容弹匣的M870霰弹枪', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M870 - 扩容弹匣', 
        'quality': 2, 
    }), 
    102002: TD({
        'id': 102002, 
        'bag_max_stack_limit': 1, 
        'equip_id': 79, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 20002, 
        'icon': 710, 
        'icon_outline': 321, 
        'item_description': '强化开镜命中率的M870霰弹枪', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M870 - 开镜强化', 
        'quality': 3, 
    }), 
    102301: TD({
        'id': 102301, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23005, 
        'icon': 749, 
        'icon_outline': 611, 
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR', 
        'quality': 1, 
    }), 
    102302: TD({
        'id': 102302, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23006, 
        'icon': 715, 
        'icon_outline': 611, 
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR - 2倍镜', 
        'quality': 2, 
    }), 
    102303: TD({
        'id': 102303, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23007, 
        'icon': 716, 
        'icon_outline': 611, 
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR - 近战强化', 
        'quality': 3, 
    }), 
    102401: TD({
        'id': 102401, 
        'equip_id': 90, 
        'gun_blueprint_id': 24010, 
        'icon': 770, 
        'icon_outline': 763, 
        'item_description': '原装P90冲锋枪', 
        'item_ground_model_id': 591, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'P90', 
        'quality': 1, 
    }), 
    102402: TD({
        'id': 102402, 
        'equip_id': 90, 
        'gun_blueprint_id': 24011, 
        'icon': 770, 
        'icon_outline': 763, 
        'item_description': 'P90', 
        'item_ground_model_id': 591, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (49, ), 
        'name': 'P90 - 反射瞄具', 
        'quality': 2, 
    }), 
    102403: TD({
        'id': 102403, 
        'equip_id': 90, 
        'gun_blueprint_id': 24012, 
        'icon': 770, 
        'icon_outline': 763, 
        'item_description': 'P90', 
        'item_ground_model_id': 591, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (50, ), 
        'name': 'P90 - 短枪管', 
        'quality': 3, 
    }), 
    102501: TD({
        'id': 102501, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100025, 
        'icon': 813, 
        'item_description': '原装VSS', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'VSS', 
        'quality': 1, 
    }), 
    102502: TD({
        'id': 102502, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25009, 
        'icon': 813, 
        'item_description': '加装2.0x Hamr瞄准镜，提升中距离作战能力', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'VSS - 中程强化', 
        'quality': 2, 
    }), 
    102503: TD({
        'id': 102503, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25010, 
        'icon': 813, 
        'item_description': '20发扩容弹匣，提升火力持续性', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'VSS - 扩容', 
        'quality': 3, 
    }), 
    102504: TD({
        'id': 102504, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25011, 
        'icon': 813, 
        'item_description': '加装狙击枪托和特种战术枪管，最大限度提高远距离作战能力', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'VSS - 远距离满改', 
        'quality': 4, 
    }), 
    102901: TD({
        'id': 102901, 
        'bag_max_stack_limit': 1, 
        'equip_id': 98, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100029, 
        'icon': 216, 
        'icon_outline': 217, 
        'item_description': '原装AUG', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AUG', 
        'quality': 1, 
    }), 
    102902: TD({
        'id': 102902, 
        'bag_max_stack_limit': 1, 
        'equip_id': 98, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 202901, 
        'icon': 216, 
        'icon_outline': 217, 
        'item_description': '安装了扩容弹匣的AUG', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AUG - 扩容弹匣', 
        'quality': 2, 
    }), 
    102903: TD({
        'id': 102903, 
        'bag_max_stack_limit': 1, 
        'equip_id': 98, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 202902, 
        'icon': 216, 
        'icon_outline': 217, 
        'item_description': '强化了近距离作战能力的AUG', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AUG', 
        'quality': 3, 
    }), 
    102904: TD({
        'id': 102904, 
        'bag_max_stack_limit': 1, 
        'equip_id': 98, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 202903, 
        'icon': 216, 
        'icon_outline': 217, 
        'item_description': '均衡的AUG', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AUG', 
        'quality': 4, 
    }), 
    103: TD({
        'id': 103, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 60, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10160, 
        'item_description': '设置一个无人机跟随队友，可持续治疗并提供加速效果', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '治疗无人机-速度', 
    }), 
    103901: TD({
        'id': 103901, 
        'bag_max_stack_limit': 1, 
        'equip_id': 121, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100039, 
        'item_description': '原装QBZ95步枪', 
        'item_ground_model_id': 3991, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'QBZ95', 
        'quality': 1, 
    }), 
    103902: TD({
        'id': 103902, 
        'bag_max_stack_limit': 1, 
        'equip_id': 121, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 203901, 
        'item_description': '优化了后坐力控制的QBZ95步枪', 
        'item_ground_model_id': 3991, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'QBZ95', 
        'quality': 3, 
    }), 
    104: TD({
        'id': 104, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 61, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10160, 
        'item_description': '设置一个无人机跟随队友，可持续治疗并提供使目标枪械伤害增加', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '治疗无人机-火力', 
    }), 
    104001: TD({
        'id': 104001, 
        'bag_max_stack_limit': 1, 
        'equip_id': 110, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 40010, 
        'icon': 110097, 
        'icon_outline': 110099, 
        'item_description': 'Uzi', 
        'item_ground_model_id': 3868, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Uzi', 
        'quality': 1, 
    }), 
    104002: TD({
        'id': 104002, 
        'bag_max_stack_limit': 1, 
        'equip_id': 110, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 40011, 
        'icon': 110097, 
        'icon_outline': 110099, 
        'item_description': 'Uzi', 
        'item_ground_model_id': 3868, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Uzi-远程', 
        'quality': 2, 
    }), 
    104003: TD({
        'id': 104003, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 110, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 40012, 
        'icon': 110097, 
        'icon_outline': 110099, 
        'item_description': 'Uzi', 
        'item_ground_model_id': 3868, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Uzi-特种作战', 
        'quality': 3, 
    }), 
    104004: TD({
        'id': 104004, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 110, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 40013, 
        'icon': 110097, 
        'icon_outline': 110099, 
        'item_description': 'Uzi', 
        'item_ground_model_id': 3868, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Uzi-战术', 
        'quality': 3, 
    }), 
    104101: TD({
        'id': 104101, 
        'bag_max_stack_limit': 1, 
        'equip_id': 402, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100042, 
        'icon': 736, 
        'icon_outline': 1088, 
        'icon_outline_2': 799, 
        'item_description': '原装STI2011手枪', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'STI2011', 
        'quality': 1, 
    }), 
    109: TD({
        'id': 109, 
        'br_task_id': 6, 
        'ground_max_stack_limit': 1, 
        'icon': 114, 
        'item_description': '护送卡车到目标地点', 
        'item_ground_model_id': 513, 
        'item_type': 107, 
        'name': '任务代号-护送', 
        'quality': 3, 
    }), 
    110: TD({
        'id': 110, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 65, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10160, 
        'item_description': '使用深海的力量，召唤一个烟雾立场', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '深海领域', 
    }), 
    11001: TD({
        'id': 11001, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6001, 
        'icon': 737, 
        'icon_outline': 214, 
        'item_description': '高射速连发霰弹枪，近距离杀伤力极强', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Origin12', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    11002: TD({
        'id': 11002, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6005, 
        'icon': 657, 
        'icon_outline': 214, 
        'item_description': '高射速连发霰弹枪，近距离杀伤力极强', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Origin12', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    11003: TD({
        'id': 11003, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6006, 
        'icon': 658, 
        'icon_outline': 214, 
        'item_description': '高射速连发霰弹枪，近距离杀伤力极强', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Origin12', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    11004: TD({
        'id': 11004, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6007, 
        'icon': 659, 
        'icon_outline': 214, 
        'item_description': '高射速连发霰弹枪，近距离杀伤力极强', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Origin12', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    11005: TD({
        'id': 11005, 
        'equip_id': 79, 
        'gun_blueprint_id': 100020, 
        'icon': 745, 
        'icon_outline': 321, 
        'item_description': '半自动智能霰弹枪，经过合适的改装也可胜任中近距离的作战情景', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M870', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    11006: TD({
        'id': 11006, 
        'bag_max_stack_limit': 1, 
        'equip_id': 79, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 20004, 
        'icon': 711, 
        'icon_outline': 321, 
        'item_description': '半自动智能霰弹枪，经过合适的改装也可胜任中近距离的作战情景', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M870', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    11007: TD({
        'id': 11007, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 79, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 20005, 
        'icon': 712, 
        'icon_outline': 321, 
        'item_description': '半自动智能霰弹枪，经过合适的改装也可胜任中近距离的作战情景', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M870', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    11008: TD({
        'id': 11008, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 79, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 20006, 
        'icon': 713, 
        'icon_outline': 321, 
        'item_description': '半自动智能霰弹枪，经过合适的改装也可胜任中近距离的作战情景', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M870', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    111: TD({
        'id': 111, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 66, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10160, 
        'item_description': '降低立场内的温度，立场内的玩家受到减速效果', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '深海领域-极寒', 
    }), 
    112: TD({
        'id': 112, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 67, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10160, 
        'item_description': '立场具有腐蚀性，立场内的玩家受到持续伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '深海领域-腐蚀', 
    }), 
    113: TD({
        'id': 113, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 68, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10160, 
        'item_description': '丢出一支苦无，并在短暂的延迟后飞到苦无所在位置', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '赛博忍术-瞬身', 
    }), 
    114: TD({
        'id': 114, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 69, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10160, 
        'item_description': '设置一个图腾，一定时间内再次使用可回到图腾位置', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '赛博忍术-回溯1阶段', 
    }), 
    115: TD({
        'id': 115, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 70, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10160, 
        'item_description': '设置一个图腾，一定时间内再次使用可回到图腾位置', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '赛博忍术-回溯2阶段', 
    }), 
    116: TD({
        'id': 116, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'ground_max_stack_limit': 1, 
        'icon': 10160, 
        'item_description': '使用后能 从三个天赋中选一个', 
        'item_ground_model_id': 513, 
        'item_type': 111, 
        'name': '天赋核心三选一', 
        'operate_mode': 1, 
        'use_code': 'TACTICAL_CORE', 
    }), 
    117: TD({
        'id': 117, 
        'equip_id': 71, 
        'gun_blueprint_id': 100013, 
        'icon': 740, 
        'icon_outline': 266, 
        'item_description': 'Kala狙击枪', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Kala', 
        'quality': 1, 
    }), 
    118: TD({
        'id': 118, 
        'auto_mark': 1, 
        'equip_id': 71, 
        'gun_blueprint_id': 100013, 
        'icon': 740, 
        'icon_outline': 266, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Kala-自定义', 
        'quality': 6, 
    }), 
    119: TD({
        'id': 119, 
        'equip_id': 72, 
        'gun_blueprint_id': 100014, 
        'icon': 741, 
        'icon_outline': 217, 
        'item_description': '原装KAG-6步枪', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6', 
        'quality': 1, 
    }), 
    120: TD({
        'id': 120, 
        'auto_mark': 1, 
        'equip_id': 72, 
        'gun_blueprint_id': 100014, 
        'icon': 741, 
        'icon_outline': 217, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6-自定义', 
        'quality': 6, 
    }), 
    12001: TD({
        'id': 12001, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8001, 
        'icon': 738, 
        'icon_outline': 220, 
        'item_description': '射速极快的冲锋枪，在枪手的精准操控下非常致命', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Vector', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    12002: TD({
        'id': 12002, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8007, 
        'icon': 664, 
        'icon_outline': 220, 
        'item_description': '射速极快的冲锋枪，在枪手的精准操控下非常致命', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Vector', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    12003: TD({
        'id': 12003, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8008, 
        'icon': 665, 
        'icon_outline': 220, 
        'item_description': '射速极快的冲锋枪，在枪手的精准操控下非常致命', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Vector', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    12004: TD({
        'id': 12004, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8009, 
        'icon': 666, 
        'icon_outline': 220, 
        'item_description': '射速极快的冲锋枪，在枪手的精准操控下非常致命', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Vector', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    12005: TD({
        'id': 12005, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16001, 
        'icon': 743, 
        'icon_outline': 322, 
        'item_description': '射速较慢的冲锋枪，但子弹威力衰减弱，适用于多种交战场合', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'URB', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    12006: TD({
        'id': 12006, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16007, 
        'icon': 694, 
        'icon_outline': 322, 
        'item_description': '射速较慢的冲锋枪，但子弹威力衰减弱，适用于多种交战场合', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'URB', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    12007: TD({
        'id': 12007, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16008, 
        'icon': 695, 
        'icon_outline': 322, 
        'item_description': '射速较慢的冲锋枪，但子弹威力衰减弱，适用于多种交战场合', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'URB', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    12008: TD({
        'id': 12008, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16009, 
        'icon': 696, 
        'icon_outline': 322, 
        'item_description': '射速较慢的冲锋枪，但子弹威力衰减弱，适用于多种交战场合', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'URB', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    12009: TD({
        'id': 12009, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17001, 
        'icon': 748, 
        'icon_outline': 323, 
        'item_description': '射速中等的冲锋枪，性能可靠，适用于中近距离作战', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'INP-9', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    12010: TD({
        'id': 12010, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17007, 
        'icon': 701, 
        'icon_outline': 323, 
        'item_description': '射速中等的冲锋枪，性能可靠，适用于中近距离作战', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'INP-9', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    12011: TD({
        'id': 12011, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17008, 
        'icon': 702, 
        'icon_outline': 323, 
        'item_description': '射速中等的冲锋枪，性能可靠，适用于中近距离作战', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'INP-9', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    12012: TD({
        'id': 12012, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17009, 
        'icon': 703, 
        'icon_outline': 323, 
        'item_description': '射速中等的冲锋枪，性能可靠，适用于中近距离作战', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'INP-9', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    13: TD({
        'id': 13, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100018, 
        'icon': 744, 
        'icon_outline': 324, 
        'item_description': '加装全息瞄准镜的M700', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700', 
        'quality': 1, 
    }), 
    13001: TD({
        'id': 13001, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1001, 
        'icon': 735, 
        'icon_outline': 325, 
        'item_description': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M4A1', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    13002: TD({
        'id': 13002, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1008, 
        'icon': 648, 
        'icon_outline': 325, 
        'item_description': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M4A1', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    13003: TD({
        'id': 13003, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1009, 
        'icon': 649, 
        'icon_outline': 325, 
        'item_description': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M4A1', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    13004: TD({
        'id': 13004, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1010, 
        'icon': 650, 
        'icon_outline': 325, 
        'item_description': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M4A1', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    13005: TD({
        'id': 13005, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10001, 
        'icon': 739, 
        'icon_outline': 174, 
        'item_description': '耐用性极强的全自动步枪，射速慢但子弹威力大，适合中远距离', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'AK47', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    13006: TD({
        'id': 13006, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10008, 
        'icon': 671, 
        'icon_outline': 174, 
        'item_description': '耐用性极强的全自动步枪，射速慢但子弹威力大，适合中远距离', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'AK47', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    13007: TD({
        'id': 13007, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10009, 
        'icon': 672, 
        'icon_outline': 174, 
        'item_description': '耐用性极强的全自动步枪，射速慢但子弹威力大，适合中远距离', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'AK47', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    13008: TD({
        'id': 13008, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10010, 
        'icon': 673, 
        'icon_outline': 174, 
        'item_description': '耐用性极强的全自动步枪，射速慢但子弹威力大，适合中远距离', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'AK47', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    13009: TD({
        'id': 13009, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14001, 
        'icon': 741, 
        'icon_outline': 217, 
        'item_description': '全自动步枪，射速快、易控制，非常适合远距离击杀敌人', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'KAG-6', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    13010: TD({
        'id': 13010, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14008, 
        'icon': 683, 
        'icon_outline': 217, 
        'item_description': '全自动步枪，射速快、易控制，非常适合远距离击杀敌人', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'KAG-6', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    13011: TD({
        'id': 13011, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14009, 
        'icon': 684, 
        'icon_outline': 217, 
        'item_description': '全自动步枪，射速快、易控制，非常适合远距离击杀敌人', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'KAG-6', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    13012: TD({
        'id': 13012, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14010, 
        'icon': 685, 
        'icon_outline': 217, 
        'item_description': '全自动步枪，射速快、易控制，非常适合远距离击杀敌人', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'KAG-6', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    13013: TD({
        'id': 13013, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'gun_blueprint_id': 23001, 
        'icon': 749, 
        'icon_outline': 611, 
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'SCAR', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    13014: TD({
        'id': 13014, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'gun_blueprint_id': 23002, 
        'icon': 715, 
        'icon_outline': 611, 
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'SCAR', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    13015: TD({
        'id': 13015, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'gun_blueprint_id': 23003, 
        'icon': 716, 
        'icon_outline': 611, 
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'SCAR', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    13016: TD({
        'id': 13016, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'gun_blueprint_id': 23004, 
        'icon': 717, 
        'icon_outline': 611, 
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'SCAR', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    1380: TD({
        'id': 1380, 
        'bag_max_stack_limit': 1, 
        'icon': 10329, 
        'icon_outline': 1, 
        'item_description': '冲锋枪和霰弹枪30M内交战时的伤害提升5%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2002, 
        'name': '正面冲突', 
        'quality': 3, 
        'short_tips': '提高近战伤害', 
    }), 
    1381: TD({
        'id': 1381, 
        'bag_max_stack_limit': 1, 
        'icon': 10374, 
        'icon_outline': 1, 
        'item_description': '投掷物的爆炸、燃烧伤害增加30%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2003, 
        'name': '手雷改良', 
        'quality': 2, 
        'short_tips': '增加手雷爆炸伤害', 
    }), 
    1382: TD({
        'id': 1382, 
        'bag_max_stack_limit': 1, 
        'icon': 10348, 
        'icon_outline': 1, 
        'item_description': '提高步枪和冲锋枪命中四肢时的伤害', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2004, 
        'name': '关节破坏', 
        'quality': 2, 
        'short_tips': '提升四肢伤害', 
    }), 
    1383: TD({
        'id': 1383, 
        'bag_max_stack_limit': 1, 
        'icon': 10347, 
        'icon_outline': 1, 
        'item_description': '狙击枪致命伤害范围增加', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2005, 
        'name': '贯穿', 
        'quality': 2, 
        'short_tips': '扩大狙击枪暴击范围', 
    }), 
    1384: TD({
        'id': 1384, 
        'bag_max_stack_limit': 1, 
        'icon': 10323, 
        'icon_outline': 1, 
        'item_description': '敌人发现你时会有明显的屏幕提示', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2006, 
        'name': '危机意识', 
        'quality': 3, 
        'short_tips': '预警敌人', 
    }), 
    1385: TD({
        'id': 1385, 
        'bag_max_stack_limit': 1, 
        'icon': 10358, 
        'icon_outline': 1, 
        'item_description': '可以查看敌人脚印', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2007, 
        'name': '脚步查看', 
        'quality': 3, 
        'short_tips': '查看敌人脚印', 
    }), 
    1386: TD({
        'id': 1386, 
        'bag_max_stack_limit': 1, 
        'icon': 10370, 
        'icon_outline': 1, 
        'item_description': '减少闪光、减速、揭露等负面状态50%持续时间', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2008, 
        'name': '全面防护', 
        'quality': 3, 
        'short_tips': '提升异常抗性', 
    }), 
    1387: TD({
        'id': 1387, 
        'bag_max_stack_limit': 1, 
        'icon': 10344, 
        'icon_outline': 1, 
        'item_description': '全队完成任务的奖励提高', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2009, 
        'name': '额外奖赏', 
        'quality': 3, 
        'short_tips': '任务奖励提升', 
    }), 
    1389: TD({
        'id': 1389, 
        'bag_max_stack_limit': 1, 
        'icon': 10342, 
        'icon_outline': 1, 
        'item_description': '技能冷却时间减少15%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2011, 
        'name': '充能', 
        'quality': 3, 
        'short_tips': '技能冷却减少', 
    }), 
    1390: TD({
        'id': 1390, 
        'bag_max_stack_limit': 1, 
        'icon': 10368, 
        'icon_outline': 1, 
        'item_description': '减少冲刺开火时间，翻越速度加快', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2012, 
        'name': '轻装上阵', 
        'quality': 3, 
        'short_tips': '提高机动性', 
    }), 
    1391: TD({
        'id': 1391, 
        'bag_max_stack_limit': 1, 
        'icon': 10343, 
        'icon_outline': 1, 
        'item_description': '初始开火后坐力降低', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2148, 
        'name': '钻石手', 
        'quality': 2, 
        'short_tips': '降低枪械后坐力', 
    }), 
    1393: TD({
        'id': 1393, 
        'bag_max_stack_limit': 1, 
        'icon': 10353, 
        'icon_outline': 1, 
        'item_description': '攻击时有概率恢复护盾', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2150, 
        'name': '能量反馈', 
        'quality': 4, 
        'short_tips': '攻击恢复护盾', 
    }), 
    1394: TD({
        'id': 1394, 
        'bag_max_stack_limit': 1, 
        'icon': 10571, 
        'icon_outline': 1, 
        'item_description': '感知一定范围内血量低于50%的敌人', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2151, 
        'name': '嗜血', 
        'quality': 3, 
        'short_tips': '感知残血敌人', 
    }), 
    1395: TD({
        'id': 1395, 
        'bag_max_stack_limit': 1, 
        'icon': 10572, 
        'icon_outline': 1, 
        'item_description': '短暂显示对你发起攻击的玩家名牌', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2152, 
        'name': '弹道分析', 
        'quality': 3, 
        'short_tips': '标记攻击你的玩家', 
    }), 
    1396: TD({
        'id': 1396, 
        'bag_max_stack_limit': 1, 
        'icon': 10573, 
        'icon_outline': 1, 
        'item_description': '助攻或击杀敌人后，可额外获得一份战利品', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2153, 
        'name': '拾荒者', 
        'quality': 2, 
        'short_tips': '额外战利品', 
    }), 
    1397: TD({
        'id': 1397, 
        'bag_max_stack_limit': 1, 
        'icon': 10372, 
        'icon_outline': 1, 
        'item_description': '使用枪械攻击命中敌人时，延缓其护盾恢复速度', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2013, 
        'name': '护盾阻断', 
        'quality': 3, 
        'short_tips': '破坏护盾恢复', 
    }), 
    1398: TD({
        'id': 1398, 
        'bag_max_stack_limit': 1, 
        'icon': 10715, 
        'icon_outline': 1, 
        'item_description': '可携带更多手雷', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2154, 
        'name': '手雷狂人', 
        'quality': 2, 
        'short_tips': '额外投掷物', 
    }), 
    14: TD({
        'id': 14, 
        'bag_max_stack_limit': 30, 
        'ground_max_stack_limit': 30, 
        'icon': 396, 
        'item_description': '狙击步枪用弹药', 
        'item_ground_model_id': 542, 
        'item_type': 102, 
        'mark_voice_id': (10081, ), 
        'name': '狙击枪子弹', 
        'quality': 1, 
        'short_tips': '狙击枪弹药', 
    }), 
    14001: TD({
        'id': 14001, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100018, 
        'icon': 744, 
        'icon_outline': 324, 
        'item_description': '轻型狙击步枪，机动性强，命中头部一发致命', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M700', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    14002: TD({
        'id': 14002, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18005, 
        'icon': 706, 
        'icon_outline': 324, 
        'item_description': '轻型狙击步枪，机动性强，命中头部一发致命', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M700', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    14003: TD({
        'id': 14003, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18006, 
        'icon': 707, 
        'icon_outline': 324, 
        'item_description': '轻型狙击步枪，机动性强，命中头部一发致命', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M700', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    14004: TD({
        'id': 14004, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18007, 
        'icon': 708, 
        'icon_outline': 324, 
        'item_description': '轻型狙击步枪，机动性强，命中头部一发致命', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'M700', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    14005: TD({
        'id': 14005, 
        'equip_id': 71, 
        'gun_blueprint_id': 100013, 
        'icon': 740, 
        'icon_outline': 266, 
        'item_description': '重型狙击步枪，机动性差，但子弹威力大，命中头部一发致命', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Kala', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    14006: TD({
        'id': 14006, 
        'bag_max_stack_limit': 1, 
        'equip_id': 71, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 13004, 
        'icon': 676, 
        'icon_outline': 266, 
        'item_description': '重型狙击步枪，机动性差，但子弹威力大，命中头部一发致命', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Kala', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    14007: TD({
        'id': 14007, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 71, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 13005, 
        'icon': 677, 
        'icon_outline': 266, 
        'item_description': '重型狙击步枪，机动性差，但子弹威力大，命中头部一发致命', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Kala', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    14008: TD({
        'id': 14008, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 71, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 13006, 
        'icon': 678, 
        'icon_outline': 266, 
        'item_description': '重型狙击步枪，机动性差，但子弹威力大，命中头部一发致命', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': 'Kala', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    14009: TD({
        'id': 14009, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 83, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100022, 
        'icon': 747, 
        'icon_outline': 524, 
        'item_description': '复合弓', 
        'item_ground_model_id': 582, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '复合弓', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    1401: TD({
        'id': 1401, 
        'bag_max_stack_limit': 1, 
        'icon': 10364, 
        'icon_outline': 1, 
        'item_description': '换弹速度增加25%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2101, 
        'name': '快手', 
        'quality': 2, 
        'short_tips': '增加换弹速度', 
    }), 
    14010: TD({
        'id': 14010, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 83, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100022, 
        'icon': 747, 
        'icon_outline': 524, 
        'item_description': '复合弓', 
        'item_ground_model_id': 582, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '复合弓', 
        'quality': 2, 
        'skip': 'skip', 
    }), 
    14011: TD({
        'id': 14011, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 83, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100022, 
        'icon': 747, 
        'icon_outline': 524, 
        'item_description': '复合弓', 
        'item_ground_model_id': 582, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '复合弓', 
        'quality': 3, 
        'skip': 'skip', 
    }), 
    14012: TD({
        'id': 14012, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 83, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100022, 
        'icon': 747, 
        'icon_outline': 524, 
        'item_description': '复合弓', 
        'item_ground_model_id': 582, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '复合弓', 
        'quality': 4, 
        'skip': 'skip', 
    }), 
    14013: TD({
        'id': 14013, 
        'bag_max_stack_limit': 1, 
        'equip_id': 402, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100042, 
        'icon': 736, 
        'icon_outline': 1088, 
        'icon_outline_2': 799, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'STI2011', 
        'quality': 6, 
        'skip': 'skip', 
    }), 
    14014: TD({
        'id': 14014, 
        'bag_max_stack_limit': 1, 
        'equip_id': 402, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100042, 
        'icon': 736, 
        'icon_outline': 1088, 
        'icon_outline_2': 799, 
        'item_description': '原装STI2011手枪', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'STI2011', 
        'quality': 1, 
        'skip': 'skip', 
    }), 
    1402: TD({
        'id': 1402, 
        'bag_max_stack_limit': 1, 
        'icon': 10373, 
        'icon_outline': 1, 
        'item_description': '使用冲锋枪增加腰射准确度和移动速度', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2102, 
        'name': '突击兵', 
        'quality': 2, 
        'short_tips': '提高冲锋枪能力', 
    }), 
    1403: TD({
        'id': 1403, 
        'bag_max_stack_limit': 1, 
        'icon': 10340, 
        'icon_outline': 1, 
        'item_description': '降低10%步枪、冲锋枪的后坐力', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2103, 
        'name': '沉稳', 
        'quality': 2, 
        'short_tips': '降低后坐力', 
    }), 
    1404: TD({
        'id': 1404, 
        'bag_max_stack_limit': 1, 
        'icon': 10362, 
        'icon_outline': 1, 
        'item_description': '增加20%开镜速度', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2104, 
        'name': '肌肉记忆', 
        'quality': 2, 
        'short_tips': '加快开镜速度', 
    }), 
    1405: TD({
        'id': 1405, 
        'bag_max_stack_limit': 1, 
        'icon': 10333, 
        'icon_outline': 1, 
        'item_description': '受到爆炸伤害减少30%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2105, 
        'name': '爆炸防护', 
        'quality': 2, 
        'short_tips': '增加爆炸抵抗', 
    }), 
    1406: TD({
        'id': 1406, 
        'bag_max_stack_limit': 1, 
        'icon': 10359, 
        'icon_outline': 1, 
        'item_description': '腰射精度提升', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2106, 
        'name': '精准射击', 
        'quality': 2, 
        'short_tips': '提升腰射准度', 
    }), 
    1407: TD({
        'id': 1407, 
        'bag_max_stack_limit': 1, 
        'icon': 10352, 
        'icon_outline': 1, 
        'item_description': '滑铲增加1发弹药', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2107, 
        'name': '花式换弹', 
        'quality': 3, 
        'short_tips': '滑铲增加弹药', 
    }), 
    1408: TD({
        'id': 1408, 
        'bag_max_stack_limit': 1, 
        'icon': 10338, 
        'icon_outline': 1, 
        'item_description': '击倒敌人时，立刻开始护盾充能', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2108, 
        'name': '惩戒', 
        'quality': 3, 
        'short_tips': '击杀加快恢复', 
    }), 
    1409: TD({
        'id': 1409, 
        'bag_max_stack_limit': 1, 
        'icon': 10339, 
        'icon_outline': 1, 
        'item_description': '击碎敌人护甲时，提高移动速度', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2109, 
        'name': '乘胜追击', 
        'quality': 4, 
        'short_tips': '破甲加快移速', 
    }), 
    1410: TD({
        'id': 1410, 
        'bag_max_stack_limit': 1, 
        'icon': 10332, 
        'icon_outline': 1, 
        'item_description': '自身护盾破裂时，减免20%伤害', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2110, 
        'name': '保命要紧', 
        'quality': 3, 
        'short_tips': '护盾耗尽时减伤', 
    }), 
    1411: TD({
        'id': 1411, 
        'bag_max_stack_limit': 1, 
        'icon': 10357, 
        'icon_outline': 1, 
        'item_description': '提升腰射开火时的移动速度', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2111, 
        'name': '火力至上', 
        'quality': 4, 
        'short_tips': '增加开火移速', 
    }), 
    1412: TD({
        'id': 1412, 
        'bag_max_stack_limit': 1, 
        'icon': 10336, 
        'icon_outline': 1, 
        'item_description': '步枪有效射程增加20%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2112, 
        'name': '步枪专家', 
        'quality': 2, 
        'short_tips': '提高步枪射程', 
    }), 
    1413: TD({
        'id': 1413, 
        'bag_max_stack_limit': 1, 
        'icon': 10341, 
        'icon_outline': 1, 
        'item_description': '持续开火时的后座稳定性提升', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2113, 
        'name': '持久战斗', 
        'quality': 2, 
        'short_tips': '提升后坐力控制', 
    }), 
    1414: TD({
        'id': 1414, 
        'bag_max_stack_limit': 1, 
        'icon': 10355, 
        'icon_outline': 1, 
        'item_description': '步枪对护盾的伤害增加10%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2114, 
        'name': '护盾克星', 
        'quality': 2, 
        'short_tips': '步枪更快破盾', 
    }), 
    1415: TD({
        'id': 1415, 
        'bag_max_stack_limit': 1, 
        'icon': 10330, 
        'icon_outline': 1, 
        'item_description': '爆头伤害增加15%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2115, 
        'name': '致命一击', 
        'quality': 3, 
        'short_tips': '增加爆头伤害', 
    }), 
    1416: TD({
        'id': 1416, 
        'bag_max_stack_limit': 1, 
        'icon': 10331, 
        'icon_outline': 1, 
        'item_description': '狙击枪伤害不随距离衰减', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2116, 
        'name': '百步穿杨', 
        'quality': 4, 
        'short_tips': '狙击枪伤害不衰减', 
    }), 
    142: TD({
        'id': 142, 
        'bag_max_stack_limit': 1, 
        'equip_id': 74, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100015, 
        'icon': 742, 
        'icon_outline': 326, 
        'item_description': '原装沙鹰', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'DesertEagle', 
        'quality': 1, 
    }), 
    1420: TD({
        'id': 1420, 
        'bag_max_stack_limit': 1, 
        'icon': 10369, 
        'icon_outline': 1, 
        'item_description': '倒地后获得50%生命值的护盾', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2120, 
        'name': '求生意志', 
        'quality': 2, 
        'short_tips': '倒地获得护盾', 
    }), 
    1421: TD({
        'id': 1421, 
        'bag_max_stack_limit': 1, 
        'icon': 10346, 
        'icon_outline': 1, 
        'item_description': '提升10点生命值', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2121, 
        'name': '强壮', 
        'quality': 2, 
        'short_tips': '增加生命值', 
    }), 
    1422: TD({
        'id': 1422, 
        'bag_max_stack_limit': 1, 
        'icon': 10363, 
        'icon_outline': 1, 
        'item_description': '护盾的回复间隔变短20%，回复速度变快33%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2122, 
        'name': '极限充能', 
        'quality': 2, 
        'short_tips': '护盾充能加快', 
    }), 
    1423: TD({
        'id': 1423, 
        'bag_max_stack_limit': 1, 
        'icon': 10368, 
        'icon_outline': 1, 
        'item_description': '受攻击时，短暂的增加移动速度', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2123, 
        'name': '跑酷达人', 
        'quality': 2, 
        'short_tips': '受伤增加移速', 
    }), 
    1424: TD({
        'id': 1424, 
        'bag_max_stack_limit': 1, 
        'icon': 10366, 
        'icon_outline': 1, 
        'item_description': '开镜时获得一个护盾', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2124, 
        'name': '迷你护盾I', 
        'quality': 2, 
        'short_tips': '开镜获得护盾', 
    }), 
    1425: TD({
        'id': 1425, 
        'bag_max_stack_limit': 1, 
        'icon': 10349, 
        'icon_outline': 1, 
        'item_description': '屏蔽信号，不会被雷达和UAV侦测', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2125, 
        'name': '鬼魅', 
        'quality': 3, 
        'short_tips': '屏蔽敌人侦察', 
    }), 
    1426: TD({
        'id': 1426, 
        'bag_max_stack_limit': 1, 
        'icon': 10356, 
        'icon_outline': 1, 
        'item_description': '生命值不满时会缓慢恢复', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2126, 
        'name': '恢复', 
        'quality': 4, 
        'short_tips': '缓慢恢复生命', 
    }), 
    1427: TD({
        'id': 1427, 
        'bag_max_stack_limit': 1, 
        'icon': 10328, 
        'icon_outline': 1, 
        'item_description': '破盾时，脚下会产生一个烟雾弹', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2127, 
        'name': '烟雾护体', 
        'quality': 3, 
        'short_tips': '受伤触发烟雾弹', 
    }), 
    1428: TD({
        'id': 1428, 
        'bag_max_stack_limit': 1, 
        'icon': 10322, 
        'icon_outline': 1, 
        'item_description': '声音提示范围增加 30%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2128, 
        'name': '声音侦查', 
        'quality': 2, 
        'short_tips': '声音提示范围加大', 
    }), 
    1429: TD({
        'id': 1429, 
        'bag_max_stack_limit': 1, 
        'icon': 643, 
        'icon_outline': 1, 
        'item_description': '攻击敌人时，会标记敌人一段时间（队友可见）', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2129, 
        'name': '染色标记', 
        'quality': 2, 
        'short_tips': '攻击标记敌人', 
    }), 
    143: TD({
        'id': 143, 
        'bag_max_stack_limit': 1, 
        'equip_id': 74, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100015, 
        'icon': 742, 
        'icon_outline': 326, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'DesertEagle-自定义', 
        'quality': 6, 
    }), 
    1430: TD({
        'id': 1430, 
        'bag_max_stack_limit': 1, 
        'icon': 10365, 
        'icon_outline': 1, 
        'item_description': '击杀敌人时，在小地图标记敌人队友的位置', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2130, 
        'name': '追踪', 
        'quality': 2, 
        'short_tips': '击杀标记敌人位置', 
    }), 
    1432: TD({
        'id': 1432, 
        'bag_max_stack_limit': 1, 
        'icon': 10337, 
        'icon_outline': 1, 
        'item_description': '击杀/助攻获得20%技能冷却（包括被动技能）', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2132, 
        'name': '超频', 
        'quality': 3, 
        'short_tips': '减少技能冷却', 
    }), 
    1433: TD({
        'id': 1433, 
        'bag_max_stack_limit': 1, 
        'icon': 10345, 
        'icon_outline': 1, 
        'item_description': '击杀你的玩家的位置将会暴露20s', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2133, 
        'name': '一换一', 
        'quality': 3, 
        'short_tips': '暴露敌人位置', 
    }), 
    1434: TD({
        'id': 1434, 
        'bag_max_stack_limit': 1, 
        'icon': 10371, 
        'icon_outline': 1, 
        'item_description': '击杀越多，护甲上限越高，最多25点', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2134, 
        'name': '吸收', 
        'quality': 4, 
        'short_tips': '杀敌增加护盾', 
    }), 
    1435: TD({
        'id': 1435, 
        'bag_max_stack_limit': 1, 
        'icon': 10335, 
        'icon_outline': 1, 
        'item_description': '护盾承受伤害增加50%，输出增加20%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2135, 
        'name': '玻璃人', 
        'quality': 4, 
        'short_tips': '伤害增加，护盾减弱', 
    }), 
    1436: TD({
        'id': 1436, 
        'bag_max_stack_limit': 1, 
        'icon': 10350, 
        'icon_outline': 1, 
        'item_description': '野外商店购买道具打85折', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2136, 
        'name': '后门协议', 
        'quality': 2, 
        'short_tips': '道具打折', 
    }), 
    1437: TD({
        'id': 1437, 
        'bag_max_stack_limit': 1, 
        'icon': 10367, 
        'icon_outline': 1, 
        'item_description': '救援队友的速度增加50%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2137, 
        'name': '救援加速', 
        'quality': 3, 
        'short_tips': '救援速度加快', 
    }), 
    1438: TD({
        'id': 1438, 
        'bag_max_stack_limit': 1, 
        'icon': 10325, 
        'icon_outline': 1, 
        'item_description': '治疗类道具或技能的效果增加20%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2138, 
        'name': '细胞再生', 
        'quality': 2, 
        'short_tips': '治疗效果加强', 
    }), 
    1439: TD({
        'id': 1439, 
        'bag_max_stack_limit': 1, 
        'icon': 10354, 
        'icon_outline': 1, 
        'item_description': '50%护盾变成生命值', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2139, 
        'name': '护盾转化', 
        'quality': 2, 
        'short_tips': '50%护盾变成生命值', 
    }), 
    144: TD({
        'id': 144, 
        'bag_max_stack_limit': 1, 
        'equip_id': 73, 
        'gun_blueprint_id': 100019, 
        'icon': 193, 
        'item_ground_model_id': 516, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '枪匕首', 
        'quality': 3, 
    }), 
    1442: TD({
        'id': 1442, 
        'bag_max_stack_limit': 1, 
        'icon': 10375, 
        'icon_outline': 1, 
        'item_description': '切枪速度变快，使用道具速度加快', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2142, 
        'name': '熟能生巧', 
        'quality': 3, 
        'short_tips': '加快动作速度', 
    }), 
    1443: TD({
        'id': 1443, 
        'bag_max_stack_limit': 1, 
        'icon': 10324, 
        'icon_outline': 1, 
        'item_description': '滑铲速度变快，滑铲时腰射准确度提升', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2143, 
        'name': '下方突击', 
        'quality': 2, 
        'short_tips': '提高滑铲速度', 
    }), 
    1445: TD({
        'id': 1445, 
        'bag_max_stack_limit': 1, 
        'icon': 10351, 
        'icon_outline': 1, 
        'item_description': '掉落伤害减少50%，不触发落地缓冲动作', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2145, 
        'name': '下落缓冲', 
        'quality': 2, 
        'short_tips': '减少掉落伤害', 
    }), 
    1446: TD({
        'id': 1446, 
        'bag_max_stack_limit': 1, 
        'icon': 10360, 
        'icon_outline': 1, 
        'item_description': '奔跑和冲刺时获得15%伤害减免', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2146, 
        'name': '紧急回避', 
        'quality': 3, 
        'short_tips': '冲刺减少伤害', 
    }), 
    145: TD({
        'id': 145, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 10683, 
        'item_description': '拾取后获得进化能量', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 112, 
        'mark_voice_id': (44, ), 
        'name': '敌人的名牌', 
        'quality': 3, 
        'short_tips': '拾取后获得进化能量', 
    }), 
    146: TD({
        'id': 146, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 391, 
        'item_description': '技能获得大幅强化', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 111, 
        'name': '技能模组', 
        'quality': 6, 
        'short_tips': '大幅强化技能效果', 
    }), 
    147: TD({
        'id': 147, 
        'equip_id': 75, 
        'gun_blueprint_id': 100016, 
        'icon': 743, 
        'icon_outline': 322, 
        'item_description': '原装URB冲锋枪', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M82', 
        'quality': 1, 
    }), 
    148: TD({
        'id': 148, 
        'auto_mark': 1, 
        'equip_id': 75, 
        'gun_blueprint_id': 100016, 
        'icon': 743, 
        'icon_outline': 322, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M82-自定义', 
        'quality': 6, 
    }), 
    149: TD({
        'id': 149, 
        'equip_id': 76, 
        'gun_blueprint_id': 100017, 
        'icon': 748, 
        'icon_outline': 323, 
        'item_description': '原装INP9冲锋枪', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP9', 
        'quality': 1, 
    }), 
    15: TD({
        'id': 15, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 10, 
        'ground_max_stack_limit': 1, 
        'icon': 145, 
        'item_description': '呼叫侦察机，在地图上标记敌对目标', 
        'item_sub_type': 14, 
        'item_type': 101, 
        'name': 'UAV', 
        'operate_mode': 1, 
        'quality': 4, 
    }), 
    150: TD({
        'id': 150, 
        'auto_mark': 1, 
        'equip_id': 76, 
        'gun_blueprint_id': 100017, 
        'icon': 748, 
        'icon_outline': 323, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP9-自定义', 
        'quality': 6, 
    }), 
    1500: TD({
        'id': 1500, 
        'bag_max_stack_limit': 1, 
        'icon': 10406, 
        'icon_outline': 1, 
        'item_description': '护盾持续时间增加', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3000, 
        'name': '能量充沛', 
        'quality': 4, 
        'short_tips': '增加护盾时间', 
    }), 
    1501: TD({
        'id': 1501, 
        'bag_max_stack_limit': 1, 
        'icon': 10391, 
        'icon_outline': 1, 
        'item_description': '护盾阻挡范围变大', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3001, 
        'name': '护盾强化', 
        'quality': 4, 
        'short_tips': '提升护盾大小', 
    }), 
    1502: TD({
        'id': 1502, 
        'bag_max_stack_limit': 1, 
        'icon': 10280, 
        'icon_outline': 1, 
        'item_description': '护盾变为单向穿透，阻挡外侧子弹', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3002, 
        'name': '矩阵运算', 
        'quality': 4, 
        'short_tips': '护盾可单向穿透', 
    }), 
    1503: TD({
        'id': 1503, 
        'bag_max_stack_limit': 1, 
        'icon': 10395, 
        'icon_outline': 1, 
        'item_description': '增加导弹打击次数（提升光线持续时间和半径）', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3003, 
        'name': '多重打击', 
        'quality': 4, 
        'short_tips': '增加打击次数', 
    }), 
    1504: TD({
        'id': 1504, 
        'bag_max_stack_limit': 1, 
        'icon': 10398, 
        'icon_outline': 1, 
        'item_description': '增加导弹打击伤害（光线伤害）', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3004, 
        'name': '爆炸强化', 
        'quality': 4, 
        'short_tips': '提升爆炸伤害', 
    }), 
    1505: TD({
        'id': 1505, 
        'bag_max_stack_limit': 1, 
        'icon': 10275, 
        'icon_outline': 1, 
        'item_description': '导弹打击升级为聚能风暴，可以穿透建筑攻击敌人', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3005, 
        'name': '聚能风暴', 
        'quality': 4, 
        'short_tips': '升级为聚能风暴', 
    }), 
    151: TD({
        'id': 151, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100018, 
        'icon': 351, 
        'icon_outline': 324, 
        'item_description': '原装M700狙击枪', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700', 
        'quality': 3, 
    }), 
    1510: TD({
        'id': 1510, 
        'bag_max_stack_limit': 1, 
        'icon': 10402, 
        'icon_outline': 1, 
        'item_description': '扫描时间增加', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3010, 
        'name': '扫描充能', 
        'quality': 4, 
        'short_tips': '延长扫描时间', 
    }), 
    1511: TD({
        'id': 1511, 
        'bag_max_stack_limit': 1, 
        'icon': 10392, 
        'icon_outline': 1, 
        'item_description': '扫描范围增加', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3011, 
        'name': '范围强化', 
        'quality': 4, 
        'short_tips': '扩大扫描范围', 
    }), 
    1512: TD({
        'id': 1512, 
        'bag_max_stack_limit': 1, 
        'icon': 10273, 
        'icon_outline': 1, 
        'item_description': '扫描升级，实时显示所有玩家位置', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3012, 
        'name': '进阶扫描', 
        'quality': 4, 
        'short_tips': '实时扫描全地图玩家', 
    }), 
    1513: TD({
        'id': 1513, 
        'bag_max_stack_limit': 1, 
        'icon': 10396, 
        'icon_outline': 1, 
        'item_description': '炮塔攻击力上升', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3013, 
        'name': '弹药升级', 
        'quality': 4, 
        'short_tips': '提升炮塔攻击力', 
    }), 
    1514: TD({
        'id': 1514, 
        'bag_max_stack_limit': 1, 
        'icon': 10397, 
        'icon_outline': 1, 
        'item_description': '炮塔攻击有概率造成减速', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3014, 
        'name': '穿透子弹', 
        'quality': 4, 
        'short_tips': '炮塔攻击减速', 
    }), 
    1515: TD({
        'id': 1515, 
        'bag_max_stack_limit': 1, 
        'icon': 327, 
        'icon_outline': 1, 
        'item_description': '炮台升级为机枪炮台，拥有更快的攻击频率', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3015, 
        'name': '重火力炮塔', 
        'quality': 4, 
        'short_tips': '炮塔全方位强化', 
    }), 
    1516: TD({
        'id': 1516, 
        'bag_max_stack_limit': 1, 
        'icon': 10401, 
        'icon_outline': 1, 
        'item_description': '提升技能激活时的移动速度', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3016, 
        'name': '速度强化', 
        'quality': 4, 
        'short_tips': '提升技能移动速度', 
    }), 
    1517: TD({
        'id': 1517, 
        'bag_max_stack_limit': 1, 
        'icon': 10404, 
        'icon_outline': 1, 
        'item_description': '技能激活时减免伤害', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3017, 
        'name': '临界反应', 
        'quality': 4, 
        'short_tips': '技能激活时减免伤害', 
    }), 
    1518: TD({
        'id': 1518, 
        'bag_max_stack_limit': 1, 
        'icon': 10270, 
        'icon_outline': 1, 
        'item_description': '刺杀形态会激活隐形迷彩', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3018, 
        'name': '幽灵形态', 
        'quality': 4, 
        'short_tips': '极大的增强隐蔽能力', 
    }), 
    1519: TD({
        'id': 1519, 
        'bag_max_stack_limit': 1, 
        'icon': 10400, 
        'icon_outline': 1, 
        'item_description': '提升治疗效率', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3019, 
        'name': '治疗强化', 
        'quality': 4, 
        'short_tips': '提高治疗效果', 
    }), 
    152: TD({
        'id': 152, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100018, 
        'icon': 744, 
        'icon_outline': 324, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700-自定义', 
        'quality': 6, 
    }), 
    1520: TD({
        'id': 1520, 
        'bag_max_stack_limit': 1, 
        'icon': 10403, 
        'icon_outline': 1, 
        'item_description': '无人机间隔性释放恢复20点护盾的脉冲', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3020, 
        'name': '能量脉冲', 
        'quality': 4, 
        'short_tips': '激活能量脉冲', 
    }), 
    1521: TD({
        'id': 1521, 
        'bag_max_stack_limit': 1, 
        'icon': 10278, 
        'icon_outline': 1, 
        'item_description': '无人机可以跟随玩家移动，持续治疗目标玩家', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3021, 
        'name': '进阶无人机', 
        'quality': 4, 
        'short_tips': '无人机可跟随玩家', 
    }), 
    1522: TD({
        'id': 1522, 
        'bag_max_stack_limit': 1, 
        'icon': 10405, 
        'icon_outline': 1, 
        'item_description': '增加旋涡负面状态的持续时间', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3022, 
        'name': '干扰', 
        'quality': 4, 
        'short_tips': '增加致盲时间', 
    }), 
    1523: TD({
        'id': 1523, 
        'bag_max_stack_limit': 1, 
        'icon': 10399, 
        'icon_outline': 1, 
        'item_description': '增加旋涡的判定范围', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3023, 
        'name': '波动', 
        'quality': 4, 
        'short_tips': '提升判定范围', 
    }), 
    1524: TD({
        'id': 1524, 
        'bag_max_stack_limit': 1, 
        'icon': 10267, 
        'icon_outline': 1, 
        'item_description': '旋涡升级为深渊，在当前位置生成一个暗影区域，所有进入区域的角色视野被阻挡', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3024, 
        'name': '深渊', 
        'quality': 4, 
        'short_tips': '技能升级为深渊', 
    }), 
    1525: TD({
        'id': 1525, 
        'bag_max_stack_limit': 1, 
        'icon': 10393, 
        'icon_outline': 1, 
        'item_description': '增加毒气每秒伤害', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3025, 
        'name': '毒性升级', 
        'quality': 4, 
        'short_tips': '增加毒气伤害', 
    }), 
    1526: TD({
        'id': 1526, 
        'bag_max_stack_limit': 1, 
        'icon': 10394, 
        'icon_outline': 1, 
        'item_description': '增加毒气扩散范围', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3026, 
        'name': '毒性扩散', 
        'quality': 4, 
        'short_tips': '扩大毒气范围', 
    }), 
    1527: TD({
        'id': 1527, 
        'bag_max_stack_limit': 1, 
        'icon': 10271, 
        'icon_outline': 1, 
        'item_description': '毒气手雷升级为毒气领域，大范围释放毒气', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3027, 
        'name': '剧毒领域', 
        'quality': 4, 
        'short_tips': '技能升级为毒气领域', 
    }), 
    1528: TD({
        'id': 1528, 
        'icon': 394, 
        'item_description': '一弹匣子弹', 
        'item_ground_model_id': 541, 
        'item_type': 115, 
        'name': '子弹包', 
        'short_tips': '一弹匣子弹', 
    }), 
    1529: TD({
        'id': 1529, 
        'bag_max_stack_limit': 1, 
        'icon': 10482, 
        'item_description': '提升1级进化核心等级', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'name': '进化核心等级+1', 
        'quality': 4, 
        'short_tips': '进化能量', 
    }), 
    153: TD({
        'id': 153, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 78, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100019, 
        'icon': 178, 
        'icon_outline': 327, 
        'item_description': '原装M9A3手枪', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M9A3', 
        'quality': 3, 
    }), 
    1530: TD({
        'id': 1530, 
        'bag_max_stack_limit': 1, 
        'icon': 10337, 
        'item_description': '击杀或助攻会永久减少技能冷却时间，每次5%，上限50%', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2200, 
        'name': '猎魂', 
        'quality': 3, 
        'short_tips': '减少技能冷却', 
    }), 
    1531: TD({
        'id': 1531, 
        'bag_max_stack_limit': 1, 
        'icon': 10337, 
        'item_description': '每4次击杀可以获得一次技能充能', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 2201, 
        'name': '猎魂', 
        'quality': 3, 
        'short_tips': '减少技能冷却', 
    }), 
    1532: TD({
        'id': 1532, 
        'bag_max_stack_limit': 1, 
        'icon': 640, 
        'item_description': '增加钻墙手雷的爆炸伤害', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3028, 
        'name': '爆炸艺术', 
        'quality': 3, 
        'short_tips': '增加爆炸伤害', 
    }), 
    1533: TD({
        'id': 1533, 
        'bag_max_stack_limit': 1, 
        'icon': 641, 
        'item_description': '可额外存储一发钻墙手雷', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 106, 
        'learn_talent_id': 3029, 
        'name': '安可', 
        'quality': 3, 
        'short_tips': '存储上限加1', 
    }), 
    154: TD({
        'id': 154, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 78, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100019, 
        'icon': 178, 
        'icon_outline': 327, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M9A3-自定义', 
        'quality': 6, 
    }), 
    155: TD({
        'id': 155, 
        'equip_id': 79, 
        'gun_blueprint_id': 100020, 
        'icon': 745, 
        'icon_outline': 321, 
        'item_description': '原装M870单发霰弹枪', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M870', 
        'quality': 1, 
    }), 
    156: TD({
        'id': 156, 
        'auto_mark': 1, 
        'equip_id': 79, 
        'gun_blueprint_id': 100020, 
        'icon': 745, 
        'icon_outline': 321, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M870-自定义', 
        'quality': 6, 
    }), 
    157: TD({
        'id': 157, 
        'item_description': '测试用载具', 
        'item_sub_type': 1, 
        'item_type': 201, 
        'name': '测试载具-roar', 
        'quality': 1, 
    }), 
    158: TD({
        'id': 158, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 398, 
        'item_description': '能把背包子弹补满', 
        'item_ground_model_id': 513, 
        'item_sub_type': 1, 
        'item_type': 113, 
        'name': '子弹补满', 
        'quality': 4, 
    }), 
    159: TD({
        'id': 159, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            7: 'hold', 
        }), 
        'equip_id': 80, 
        'ground_max_stack_limit': 1, 
        'hold': TD({
            7: 'recycle', 
        }), 
        'item_description': '帮助专家适应各种作战环境', 
        'item_sub_type': 19, 
        'item_type': 101, 
        'name': 'xdroid', 
        'quality': 3, 
    }), 
    16: TD({
        'id': 16, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 11, 
        'ground_max_stack_limit': 1, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 399, 
        'item_description': '倒地后可复活自己', 
        'item_ground_model_id': 507, 
        'item_sub_type': 16, 
        'item_type': 101, 
        'mark_voice_id': (10075, ), 
        'name': '自救针', 
        'quality': 4, 
        'short_tips': '倒地自救装置', 
        'take_directly': True, 
    }), 
    160: TD({
        'id': 160, 
        'ground_max_stack_limit': 1, 
        'icon': 403, 
        'item_description': '高价值空投', 
        'item_ground_model_id': 552, 
        'item_type': 103, 
        'name': '高价值空投', 
    }), 
    161: TD({
        'id': 161, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1001, 
        'icon': 735, 
        'icon_outline': 325, 
        'item_description': '原装M4A1步枪', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 1, 
    }), 
    162: TD({
        'id': 162, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1002, 
        'icon': 645, 
        'icon_outline': 325, 
        'item_description': '装备全息瞄具的M4A1步枪', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 2, 
    }), 
    163: TD({
        'id': 163, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1003, 
        'icon': 646, 
        'icon_outline': 325, 
        'item_description': '装备全息镜，强化中距离作战能力', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1 - 中距离', 
        'quality': 3, 
    }), 
    164: TD({
        'id': 164, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1004, 
        'icon': 647, 
        'icon_outline': 325, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1 - 腰射强化', 
        'quality': 4, 
    }), 
    165: TD({
        'id': 165, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6001, 
        'icon': 737, 
        'icon_outline': 214, 
        'item_description': '原装Origin12连发霰弹枪', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Origin12', 
        'quality': 1, 
    }), 
    166: TD({
        'id': 166, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6002, 
        'icon': 655, 
        'icon_outline': 214, 
        'item_description': '装备扩容弹匣的Origin12霰弹枪', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Origin12 - 扩容弹匣', 
        'quality': 2, 
    }), 
    167: TD({
        'id': 167, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6003, 
        'icon': 656, 
        'icon_outline': 214, 
        'item_description': '强化近距离作战能力的Origin12霰弹枪', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Origin12 - CQC', 
        'quality': 4, 
    }), 
    168: TD({
        'id': 168, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8001, 
        'icon': 738, 
        'icon_outline': 220, 
        'item_description': '原装Vector冲锋枪', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector', 
        'quality': 1, 
    }), 
    169: TD({
        'id': 169, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8002, 
        'icon': 661, 
        'icon_outline': 220, 
        'item_description': '装备了全息镜的Vector冲锋枪', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector', 
        'quality': 2, 
    }), 
    17: TD({
        'id': 17, 
        'add_br_money': 500, 
        'icon': 118, 
        'item_description': '少量金币', 
        'item_ground_model_id': 505, 
        'item_type': 105, 
        'mark_voice_id': (10077, ), 
        'name': '金币（小）', 
        'quality': 1, 
        'short_tips': '少量金币', 
    }), 
    170: TD({
        'id': 170, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8003, 
        'icon': 662, 
        'icon_outline': 220, 
        'item_description': '装备了扩容弹匣的Vector冲锋枪', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector - 扩容弹匣', 
        'quality': 3, 
    }), 
    171: TD({
        'id': 171, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8004, 
        'icon': 663, 
        'icon_outline': 220, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector - CQC腰射', 
        'quality': 4, 
    }), 
    172: TD({
        'id': 172, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10001, 
        'icon': 739, 
        'icon_outline': 174, 
        'item_description': '原装AK47步枪', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47', 
        'quality': 1, 
    }), 
    173: TD({
        'id': 173, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10002, 
        'icon': 668, 
        'icon_outline': 174, 
        'item_description': '强化近距离作战能力的AK47步枪', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47 - 近程强化', 
        'quality': 2, 
    }), 
    174: TD({
        'id': 174, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10003, 
        'icon': 669, 
        'icon_outline': 174, 
        'item_description': '装备了扩容弹匣的AK47步枪', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47 - 扩容弹匣', 
        'quality': 3, 
    }), 
    175: TD({
        'id': 175, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10004, 
        'icon': 670, 
        'icon_outline': 174, 
        'item_description': '装备2倍镜，强化中远距离作战能力', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47 - 远程强化', 
        'quality': 4, 
    }), 
    176: TD({
        'id': 176, 
        'bag_max_stack_limit': 1, 
        'equip_id': 71, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 13001, 
        'icon': 674, 
        'icon_outline': 266, 
        'item_description': '装备6倍镜，强化中距离作战能力', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Kala - 中程强化', 
        'quality': 2, 
    }), 
    177: TD({
        'id': 177, 
        'bag_max_stack_limit': 1, 
        'equip_id': 71, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 13002, 
        'icon': 675, 
        'icon_outline': 266, 
        'item_description': '装备狙击镜，强化远距离作战能力', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Kala - 狙击手', 
        'quality': 3, 
    }), 
    178: TD({
        'id': 178, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14001, 
        'icon': 741, 
        'icon_outline': 217, 
        'item_description': '原装KAG-6步枪', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6', 
        'quality': 1, 
    }), 
    179: TD({
        'id': 179, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14002, 
        'icon': 680, 
        'icon_outline': 217, 
        'item_description': '装备的KAG-6步枪', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6', 
        'quality': 2, 
    }), 
    18: TD({
        'id': 18, 
        'add_br_money': 1000, 
        'icon': 119, 
        'item_description': '一袋金币', 
        'item_ground_model_id': 511, 
        'item_type': 105, 
        'mark_voice_id': (10077, ), 
        'name': '金币（中）', 
        'quality': 1, 
        'short_tips': '一袋金币', 
    }), 
    180: TD({
        'id': 180, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14003, 
        'icon': 681, 
        'icon_outline': 217, 
        'item_description': '装备全息镜和扩容弹匣，强化中距离作战能力', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6 - 扩容弹匣', 
        'quality': 3, 
    }), 
    181: TD({
        'id': 181, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14004, 
        'icon': 682, 
        'icon_outline': 217, 
        'item_description': '装备2倍镜，强化中远距离作战能力', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6 - 远程强化', 
        'quality': 4, 
    }), 
    182: TD({
        'id': 182, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 74, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 15001, 
        'icon': 686, 
        'icon_outline': 326, 
        'item_description': '装备3倍镜，有成为狙击枪的潜力', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Desert Eagle - 狙击手', 
        'quality': 4, 
    }), 
    183: TD({
        'id': 183, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16001, 
        'icon': 743, 
        'icon_outline': 322, 
        'item_description': '原装URB冲锋枪', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB', 
        'quality': 1, 
    }), 
    184: TD({
        'id': 184, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16002, 
        'icon': 691, 
        'icon_outline': 322, 
        'item_description': '装备全息镜的URB冲锋枪', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB', 
        'quality': 2, 
    }), 
    185: TD({
        'id': 185, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16003, 
        'icon': 692, 
        'icon_outline': 322, 
        'item_description': '装备全息镜和扩容弹匣，强化中距离作战能力', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB - 扩容弹匣', 
        'quality': 3, 
    }), 
    186: TD({
        'id': 186, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16003, 
        'icon': 693, 
        'icon_outline': 322, 
        'item_description': '装备长枪管，适合作为狙击副手武器', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB - 狙击副手', 
        'quality': 4, 
    }), 
    187: TD({
        'id': 187, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17001, 
        'icon': 748, 
        'icon_outline': 323, 
        'item_description': '原装INP-9冲锋枪', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9', 
        'quality': 1, 
    }), 
    188: TD({
        'id': 188, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17002, 
        'icon': 698, 
        'icon_outline': 323, 
        'item_description': '装备全息镜的INP-9冲锋枪', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9', 
        'quality': 2, 
    }), 
    189: TD({
        'id': 189, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17003, 
        'icon': 699, 
        'icon_outline': 323, 
        'item_description': '装备全息镜和扩容弹匣，强化近距离作战能力', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9 - 近程强化', 
        'quality': 4, 
    }), 
    19: TD({
        'id': 19, 
        'add_br_money': 2000, 
        'icon': 120, 
        'item_description': '大堆金币', 
        'item_ground_model_id': 512, 
        'item_type': 105, 
        'mark_voice_id': (10077, ), 
        'name': '金币（大）', 
        'quality': 3, 
        'short_tips': '大堆金币', 
    }), 
    190: TD({
        'id': 190, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17003, 
        'icon': 700, 
        'icon_outline': 323, 
        'item_description': '装备中距离枪管，适合作为狙击副手武器', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9 - 狙击副手', 
        'quality': 4, 
    }), 
    191: TD({
        'id': 191, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18001, 
        'icon': 704, 
        'icon_outline': 324, 
        'item_description': '装备全息瞄具，强化近距离作战能力', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700 - CQC', 
        'quality': 2, 
    }), 
    192: TD({
        'id': 192, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18002, 
        'icon': 705, 
        'icon_outline': 324, 
        'item_description': '装备狙击镜，强化远距离作战能力', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700 - 狙击手', 
        'quality': 3, 
    }), 
    193: TD({
        'id': 193, 
        'bag_max_stack_limit': 1, 
        'equip_id': 79, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 20001, 
        'icon': 709, 
        'icon_outline': 321, 
        'item_description': '装备扩容弹匣的M870霰弹枪', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M870 - 扩容弹匣', 
        'quality': 2, 
    }), 
    194: TD({
        'id': 194, 
        'bag_max_stack_limit': 1, 
        'equip_id': 79, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 20002, 
        'icon': 710, 
        'icon_outline': 321, 
        'item_description': '强化开镜命中率的M870霰弹枪', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M870 - 开镜强化', 
        'quality': 3, 
    }), 
    195: TD({
        'id': 195, 
        'bag_max_stack_limit': 1, 
        'equip_id': 82, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100021, 
        'icon': 746, 
        'icon_outline': 430, 
        'item_description': '原装RPG-7发射器', 
        'item_ground_model_id': 581, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'RPG-7', 
        'quality': 1, 
    }), 
    196: TD({
        'id': 196, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 82, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100021, 
        'icon': 746, 
        'icon_outline': 430, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 581, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'RPG-7-自定义', 
        'quality': 6, 
    }), 
    197: TD({
        'id': 197, 
        'bag_max_stack_limit': 10, 
        'ground_max_stack_limit': 10, 
        'icon': 425, 
        'item_description': '发射器专用弹药', 
        'item_ground_model_id': 586, 
        'item_type': 102, 
        'mark_voice_id': (10082, ), 
        'name': '发射器子弹', 
        'quality': 1, 
        'short_tips': '发射器弹药', 
    }), 
    198: TD({
        'id': 198, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 83, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100022, 
        'icon': 747, 
        'icon_outline': 524, 
        'item_description': '复合弓', 
        'item_ground_model_id': 582, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '复合弓', 
        'quality': 1, 
    }), 
    199: TD({
        'id': 199, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 83, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100022, 
        'icon': 747, 
        'icon_outline': 524, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 582, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '复合弓-自定义', 
        'quality': 6, 
    }), 
    20: TD({
        'id': 20, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 12, 
        'ground_max_stack_limit': 2, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 393, 
        'icon_outline': 11460, 
        'item_description': '爆炸后引燃周围，造成持续的烧伤', 
        'item_ground_model_id': 537, 
        'item_sub_type': 11, 
        'item_type': 101, 
        'mark_voice_id': (10071, ), 
        'name': '燃烧瓶', 
        'quality': 1, 
        'short_tips': '燃烧瓶', 
    }), 
    200: TD({
        'id': 200, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 200, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10279, 
        'ig_voice_CD_id': (1144, ), 
        'ig_voice_id': (1143, ), 
        'item_description': '设置一个可阻挡子弹的屏障', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '防护屏障', 
        'skill_full_description': '放置一个可阻挡子弹的屏障(无法阻挡投掷物等道具类装置)，持续时间#eed42b 30s #E；当护盾生成器被破坏时，屏障也会消失', 
        'take_directly': True, 
    }), 
    201: TD({
        'id': 201, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 201, 
        'has_cancel': True, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 1091, 
        'ig_voice_CD_id': (1144, ), 
        'ig_voice_id': (1143, ), 
        'item_description': '设置一个单向屏障，可阻挡外侧的子弹。', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '第四面墙', 
        'skill_full_description': '设置一个单向屏障，可阻挡外侧的子弹，但无法阻挡投掷物等道具。当护盾生成器被破坏时，屏障也会消失。', 
        'take_directly': True, 
    }), 
    202: TD({
        'id': 202, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 202, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10276, 
        'ig_voice_id': (54, ), 
        'item_description': '呼叫远程导弹打击目标地点', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '导弹轰炸', 
        'skill_full_description': '呼叫远程导弹打击目标地点，一共轰炸#eed42b 7 #E次，前#eed42b 3 #E发导弹会精准的攻击目标点，随后轰炸范围会逐步扩大到#eed42b 25m #E；每发导弹中心区域的伤害为#eed42b 150 #E点', 
        'take_directly': True, 
    }), 
    203: TD({
        'id': 203, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 203, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10275, 
        'ig_voice_id': (73, ), 
        'item_description': '呼叫卫星发射聚能射线，持续打击目标地点', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '光线打击', 
    }), 
    204: TD({
        'id': 204, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 204, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
            8: 'using', 
        }), 
        'icon': 10272, 
        'ig_voice_id': (53, ), 
        'item_description': '投掷一个毒气手雷，手雷撞击后引爆，持续不断的释放有毒气体;NOVA还可以主动吸收毒气来强化自己的战斗力', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '毒气手雷', 
        'skill_full_description': '投掷触爆式香水瓶，碎裂产生致幻毒气，遮蔽范围内玩家视野并造成伤害。', 
        'take_directly': True, 
    }), 
    205: TD({
        'id': 205, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 205, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10271, 
        'ig_voice_id': (74, ), 
        'item_description': '设置一个毒气炸弹，释放大范围有毒气体，造成视线模糊和咳嗽', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '剧毒领域', 
    }), 
    206: TD({
        'id': 206, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 206, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10274, 
        'ig_voice_id': (51, ), 
        'item_description': '设置一个扫描装置，将附近敌人的位置标记在小地图上', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '卫星扫描', 
        'skill_full_description': '设置一个扫描装置，每隔#eed42b 6s #E扫描范围#eed42b 150m #E内的敌人，将并将其位置标记在小地图上，扫描持续#eed42b 20s #E', 
        'take_directly': True, 
    }), 
    207: TD({
        'id': 207, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 207, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10273, 
        'ig_voice_id': (75, ), 
        'item_description': '升级扫描装置，将全图敌人的位置实时显示在小地图上', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '进阶卫星扫描', 
    }), 
    208: TD({
        'id': 208, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 208, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 328, 
        'ig_voice_id': (52, ), 
        'item_description': '部署一个自动攻击的炮台', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '防御炮塔', 
        'skill_full_description': '部署一个自动攻击的炮台，炮台单次攻击伤害#eed42b 15 #E,攻击半径#eed42b 20m #E,攻击间隔#eed42b 1s #E,并拥有#eed42b 100 #E点生命值', 
        'take_directly': True, 
    }), 
    209: TD({
        'id': 209, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 209, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 327, 
        'ig_voice_id': (52, ), 
        'item_description': '炮台升级，性能更加强大', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '重火力炮塔', 
    }), 
    21: TD({
        'id': 21, 
        'icon': 388, 
        'item_type': 104, 
        'name': '战斗装备', 
    }), 
    210: TD({
        'id': 210, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 210, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10269, 
        'ig_voice_id': (57, ), 
        'ignore_quick_mode': True, 
        'item_description': '进入潜行状态，移动时不发出声音', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '刺杀形态', 
        'skill_full_description': '进入潜行状态，移动时不发出声音；进入状态后持续消耗能量，动作幅度越大，能量消耗越多；开火，跳跃等动作会额外消耗能量；技能再次开启间隔为 #eed42b 10s #E', 
        'take_directly': True, 
    }), 
    211: TD({
        'id': 211, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 211, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10270, 
        'ig_voice_id': (57, ), 
        'ignore_quick_mode': True, 
        'item_description': '激活隐形迷彩，隐身状态下动作幅度越小，可见度越低', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '幽灵形态', 
    }), 
    212: TD({
        'id': 212, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 212, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10277, 
        'ig_voice_id': (55, ), 
        'item_description': '放置一个医疗信标，治疗周围的队友', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '治疗信标', 
        'skill_full_description': '放置一个医疗信标，治疗半径 #eed42b 5m #E内的队友，每秒恢复#eed42b 10 #E点生命值，持续 #eed42b 20s#E', 
        'take_directly': True, 
    }), 
    213: TD({
        'id': 213, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 213, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10278, 
        'ig_voice_id': (78, ), 
        'item_description': '设置一个跟随队友的无人机，无人机可持续治疗队友', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '治疗无人机', 
        'take_directly': True, 
    }), 
    214: TD({
        'id': 214, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 214, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10268, 
        'ig_voice_id': (58, ), 
        'item_description': '召唤群鸦干扰敌人视野，其中一只乌鸦会追踪附近的敌人', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '旋涡', 
        'skill_full_description': '召唤群鸦干扰敌人视野，最远可命中#eed42b 33m #E内的目标；其中一只乌鸦会自动追踪半径#eed42b 8m #E内的敌人，被命中的敌人将进入#eed42b 2s #E的视野受阻状态', 
        'take_directly': True, 
    }), 
    215: TD({
        'id': 215, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 215, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10267, 
        'ig_voice_id': (79, ), 
        'item_description': '以自己为圆心创造一个领域，削弱敌人的视野', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '深渊', 
    }), 
    216: TD({
        'id': 216, 
        'bag_max_stack_limit': 1, 
        'equip_id': 15, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 55, 
        'icon': 736, 
        'icon_outline': 177, 
        'icon_outline_2': 799, 
        'item_description': '装备扩容弹匣的Glock手枪', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Glock', 
        'quality': 1, 
    }), 
    217: TD({
        'id': 217, 
        'bag_max_stack_limit': 1, 
        'equip_id': 74, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 56, 
        'icon': 348, 
        'icon_outline': 326, 
        'item_description': '装备扩容弹匣的沙鹰', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'DesertEagle', 
        'quality': 3, 
    }), 
    218: TD({
        'id': 218, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 81, 
        'ground_max_stack_limit': 2, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 539, 
        'icon_outline': 11459, 
        'item_description': '爆炸后产生范围伤害', 
        'item_ground_model_id': 539, 
        'item_sub_type': 11, 
        'item_type': 101, 
        'mark_voice_id': (1073, ), 
        'name': '粘雷', 
        'quality': 1, 
        'short_tips': '投掷物', 
    }), 
    219: TD({
        'id': 219, 
        'bag_max_stack_limit': 1, 
        'equip_id': 84, 
        'icon': 486, 
        'icon_outline': 504, 
        'item_description': '斧子', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '斧子', 
        'quality': 4, 
    }), 
    22: TD({
        'id': 22, 
        'icon': 181, 
        'item_type': 104, 
        'mark_voice_id': (69, ), 
        'name': '团队空投', 
    }), 
    220: TD({
        'id': 220, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 85, 
        'ground_max_stack_limit': 2, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 428, 
        'item_description': '使用吸入器，持续恢复生命', 
        'item_ground_model_id': 583, 
        'item_sub_type': 17, 
        'item_type': 101, 
        'mark_voice_id': (1074, ), 
        'name': '吸入器', 
        'operate_mode': 1, 
        'short_tips': '医疗道具', 
    }), 
    221: TD({
        'id': 221, 
        'bag_max_stack_limit': 1, 
        'equip_id': 86, 
        'icon': 549, 
        'icon_outline': 557, 
        'item_description': '武士刀', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '武士刀', 
        'quality': 4, 
    }), 
    222: TD({
        'id': 222, 
        'bag_max_stack_limit': 1, 
        'equip_id': 87, 
        'icon': 486, 
        'icon_outline': 504, 
        'item_description': '棒球棍', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '棒球棍', 
        'quality': 4, 
        'skip': 'trunk_only', 
    }), 
    223: TD({
        'id': 223, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'gun_blueprint_id': 100023, 
        'icon': 749, 
        'icon_outline': 611, 
        'item_description': 'Scar', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR', 
        'quality': 1, 
    }), 
    224: TD({
        'id': 224, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'gun_blueprint_id': 100023, 
        'icon': 749, 
        'icon_outline': 611, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR-自定义', 
        'quality': 6, 
    }), 
    225: TD({
        'id': 225, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 225, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 755, 
        'ig_voice_id': (1054, 1055, ), 
        'item_description': '召唤出一道无法通过的冰墙，来阻挡敌人的进攻', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '寒冰屏障', 
        'quality': 4, 
        'skill_full_description': '召唤出一道无法通过的冰墙，来阻挡敌人的进攻，冰墙生命值 #eed42b 1000 #E，持续时间#eed42b 25 #E秒', 
        'take_directly': True, 
    }), 
    226: TD({
        'id': 226, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1001, 
        'icon': 341, 
        'icon_outline': 325, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1-自定义', 
        'quality': 6, 
    }), 
    227: TD({
        'id': 227, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1002, 
        'icon': 341, 
        'icon_outline': 325, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1-自定义', 
        'quality': 6, 
    }), 
    228: TD({
        'id': 228, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1003, 
        'icon': 341, 
        'icon_outline': 325, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1-自定义', 
        'quality': 6, 
    }), 
    229: TD({
        'id': 229, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1004, 
        'icon': 341, 
        'icon_outline': 325, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1-自定义', 
        'quality': 6, 
    }), 
    23: TD({
        'id': 23, 
        'ground_max_stack_limit': 1, 
        'item_ground_model_id': 577, 
        'item_type': 103, 
        'name': '死亡掉落盒子', 
    }), 
    230: TD({
        'id': 230, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8001, 
        'icon': 344, 
        'icon_outline': 220, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector-自定义', 
        'quality': 6, 
    }), 
    231: TD({
        'id': 231, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8002, 
        'icon': 344, 
        'icon_outline': 220, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector-自定义', 
        'quality': 6, 
    }), 
    232: TD({
        'id': 232, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8003, 
        'icon': 344, 
        'icon_outline': 220, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector-自定义', 
        'quality': 6, 
    }), 
    233: TD({
        'id': 233, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8004, 
        'icon': 344, 
        'icon_outline': 220, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector-自定义', 
        'quality': 6, 
    }), 
    234: TD({
        'id': 234, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10001, 
        'icon': 345, 
        'icon_outline': 174, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47-自定义', 
        'quality': 6, 
    }), 
    235: TD({
        'id': 235, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10002, 
        'icon': 345, 
        'icon_outline': 174, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47-自定义', 
        'quality': 6, 
    }), 
    236: TD({
        'id': 236, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10003, 
        'icon': 345, 
        'icon_outline': 174, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47-自定义', 
        'quality': 6, 
    }), 
    237: TD({
        'id': 237, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10004, 
        'icon': 345, 
        'icon_outline': 174, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47-自定义', 
        'quality': 6, 
    }), 
    238: TD({
        'id': 238, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14001, 
        'icon': 347, 
        'icon_outline': 217, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6-自定义', 
        'quality': 6, 
    }), 
    239: TD({
        'id': 239, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14002, 
        'icon': 347, 
        'icon_outline': 217, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6-自定义', 
        'quality': 6, 
    }), 
    24: TD({
        'id': 24, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 13, 
        'ground_max_stack_limit': 1, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 106, 
        'item_description': '主动防御装置，自动摧毁一定范围内的投掷物', 
        'item_ground_model_id': 31, 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '主动防御装置', 
        'operate_mode': 1, 
        'quality': 3, 
    }), 
    240: TD({
        'id': 240, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14003, 
        'icon': 347, 
        'icon_outline': 217, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6-自定义', 
        'quality': 6, 
    }), 
    241: TD({
        'id': 241, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14004, 
        'icon': 347, 
        'icon_outline': 217, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6-自定义', 
        'quality': 6, 
    }), 
    242: TD({
        'id': 242, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16001, 
        'icon': 349, 
        'icon_outline': 322, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB-自定义', 
        'quality': 6, 
    }), 
    243: TD({
        'id': 243, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16002, 
        'icon': 349, 
        'icon_outline': 322, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB-自定义', 
        'quality': 6, 
    }), 
    244: TD({
        'id': 244, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16003, 
        'icon': 349, 
        'icon_outline': 322, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB-自定义', 
        'quality': 6, 
    }), 
    245: TD({
        'id': 245, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16003, 
        'icon': 349, 
        'icon_outline': 322, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB-自定义', 
        'quality': 6, 
    }), 
    246: TD({
        'id': 246, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17001, 
        'icon': 350, 
        'icon_outline': 323, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9-自定义', 
        'quality': 6, 
    }), 
    247: TD({
        'id': 247, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17002, 
        'icon': 350, 
        'icon_outline': 323, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9-自定义', 
        'quality': 6, 
    }), 
    248: TD({
        'id': 248, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17003, 
        'icon': 350, 
        'icon_outline': 323, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9-自定义', 
        'quality': 6, 
    }), 
    249: TD({
        'id': 249, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17003, 
        'icon': 350, 
        'icon_outline': 323, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9-自定义', 
        'quality': 6, 
    }), 
    250: TD({
        'id': 250, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 226, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 788, 
        'ig_voice_id': (1125, ), 
        'item_description': '召唤来自地狱的火焰，形成一堵阻挡敌人视野的火墙，经过火墙的敌人会受到短暂的致盲效果', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '地狱之火', 
        'skill_full_description': '召唤来自地狱的火焰，形成一堵阻挡敌人视野的火墙，火墙长#eed42b 30 #Em,高#eed42b 4 #Em，创建方向可以自由切换，持续#eed42b 20 #E秒，经过火墙的敌人会受到#eed42b 1.5s#E 的致盲效果', 
        'take_directly': True, 
    }), 
    251: TD({
        'id': 251, 
        'bag_max_stack_limit': 1, 
        'equip_id': 89, 
        'icon': 627, 
        'icon_outline': 630, 
        'item_description': '棒球棍', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '棒球棍', 
        'quality': 4, 
    }), 
    252: TD({
        'id': 252, 
        'equip_id': 90, 
        'gun_blueprint_id': 100024, 
        'icon': 770, 
        'icon_outline': 763, 
        'item_description': '原装P90冲锋枪', 
        'item_ground_model_id': 591, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'P90', 
        'quality': 1, 
    }), 
    253: TD({
        'id': 253, 
        'auto_mark': 1, 
        'equip_id': 90, 
        'gun_blueprint_id': 100024, 
        'icon': 770, 
        'icon_outline': 763, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 591, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'P90-自定义', 
        'quality': 6, 
    }), 
    254: TD({
        'id': 254, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 227, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
            8: 'using', 
        }), 
        'icon': 642, 
        'ig_voice_id': (1052, 1053, ), 
        'item_description': 'blast可以用改装手臂发射炸弹，并且可以切换2种炸弹的形态；一种可以穿透掩体对敌人造成伤害，另一种可以引发持续爆炸', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '钻墙炸弹', 
        'skill_full_description': 'blast可以用改装手臂发射炸弹，并且可以切换2种炸弹的形态；一种可以穿透掩体对敌人造成伤害，可钻透#eed42b 5.5M #E厚度的掩体；钻墙成功时会在另一侧发射一枚涂鸦炸弹，对半径#eed42b 8m #E的敌人造成最高#eed42b 125 #E点爆炸伤害；另一种可以引发持续 #eed42b 6s #E的爆炸，爆炸半径为#eed42b 4m#E，每秒伤害为#eed42b 30#E点', 
        'take_directly': True, 
    }), 
    255: TD({
        'id': 255, 
        'bag_max_stack_limit': 1, 
        'equip_id': 228, 
        'icon': 10268, 
        'ig_voice_id': (1052, 1053, ), 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '钻墙炸弹-穿透', 
    }), 
    256: TD({
        'id': 256, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23005, 
        'icon': 749, 
        'icon_outline': 611, 
        'item_description': '原装SCAR步枪', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR', 
        'quality': 1, 
    }), 
    257: TD({
        'id': 257, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23006, 
        'icon': 719, 
        'icon_outline': 611, 
        'item_description': '装备全息瞄具的SCAR步枪', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR', 
        'quality': 2, 
    }), 
    258: TD({
        'id': 258, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23007, 
        'icon': 720, 
        'icon_outline': 611, 
        'item_description': '装备扩容弹匣的SCAR步枪', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR-扩容弹匣', 
        'quality': 3, 
    }), 
    259: TD({
        'id': 259, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23008, 
        'icon': 721, 
        'icon_outline': 611, 
        'item_description': '装备延长枪管，强化中距离作战能力的SCAR步枪', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR-中程强化', 
        'quality': 4, 
    }), 
    260: TD({
        'id': 260, 
        'bag_max_stack_limit': 1, 
        'equip_id': 92, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100026, 
        'icon': 351, 
        'icon_outline': 324, 
        'item_description': '原装VSS半自动狙击枪', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '测试用枪', 
        'quality': 1, 
        'skip': 'trunk_only', 
    }), 
    261: TD({
        'id': 261, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 92, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100026, 
        'icon': 351, 
        'icon_outline': 324, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '测试用枪-自定义', 
        'quality': 6, 
        'skip': 'trunk_only', 
    }), 
    262: TD({
        'id': 262, 
        'bag_max_stack_limit': 1, 
        'equip_id': 95, 
        'icon': 20700000200, 
        'icon_outline': 11734, 
        'item_description': '蝴蝶刀', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '蝴蝶刀', 
        'quality': 4, 
    }), 
    263: TD({
        'id': 263, 
        'bag_max_stack_limit': 1, 
        'equip_id': 96, 
        'icon': 1044, 
        'icon_outline': 1045, 
        'item_description': '双手斧', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '双手斧', 
        'quality': 4, 
    }), 
    264: TD({
        'id': 264, 
        'bag_max_stack_limit': 1, 
        'equip_id': 97, 
        'icon': 893, 
        'icon_outline': 899, 
        'item_description': '双刀', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '双刀', 
        'quality': 4, 
    }), 
    265: TD({
        'id': 265, 
        'bag_max_stack_limit': 1, 
        'equip_id': 99, 
        'icon': 1044, 
        'icon_outline': 1070, 
        'item_description': '爪刀', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '爪刀', 
        'quality': 4, 
    }), 
    266: TD({
        'id': 266, 
        'bag_max_stack_limit': 1, 
        'equip_id': 104, 
        'icon': 20700000200, 
        'icon_outline': 11734, 
        'item_description': '蝴蝶刀', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '蝴蝶刀v6', 
        'quality': 4, 
    }), 
    269: TD({
        'id': 269, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 244, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
            8: 'using', 
        }), 
        'icon': 642, 
        'ig_voice_id': (1052, 1053, ), 
        'item_description': '释放一枚可自动追踪敌人的小型导弹，导弹爆炸后会标记敌人', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '追踪导弹', 
        'quality': 4, 
        'skill_full_description': 'blast发射一枚可自动追踪敌人的小型导弹，导弹在飞行#eed42b 0.2s #E后会追踪附近的敌人，发现目标后会飞向目标的脚下；导弹在碰撞#eed42b 1s #E后爆炸，导弹爆炸会造成 #eed42b 60 #E点伤害并标记敌人#eed42b 4s#E', 
        'take_directly': True, 
    }), 
    27: TD({
        'id': 27, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8, 
        'icon': 351, 
        'icon_outline': 324, 
        'item_description': '加装2倍镜的M700', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700', 
        'quality': 2, 
    }), 
    270: TD({
        'id': 270, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            8: 'hold', 
        }), 
        'equip_id': 245, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 642, 
        'ig_voice_id': (1052, 1053, ), 
        'item_description': '占位blast击退炸弹描述 物品表270', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '击退炸弹', 
        'skill_full_description': '占位blast击退炸弹描述 物品表270', 
        'take_directly': True, 
    }), 
    271: TD({
        'id': 271, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            8: 'hold', 
        }), 
        'equip_id': 246, 
        'hold': TD({
            9: 'using', 
        }), 
        'icon': 642, 
        'ig_voice_id': (1052, 1053, ), 
        'item_description': '快速丢出一枚延迟爆炸的手雷，手雷爆炸时的火花会减速附近的敌人', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '烟火秀', 
        'quality': 4, 
        'skill_full_description': '快速丢出一枚延迟爆炸的手雷，手雷爆炸时的火花会减速附近的敌人', 
        'take_directly': True, 
    }), 
    28: TD({
        'id': 28, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 9, 
        'icon': 351, 
        'icon_outline': 324, 
        'item_description': '加装全狙击镜的M700', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700', 
        'quality': 4, 
    }), 
    29: TD({
        'id': 29, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 114, 
        'item_description': '随机获得一个普通天赋', 
        'item_ground_model_id': 513, 
        'item_sub_type': 1, 
        'item_type': 110, 
        'name': '随机天赋', 
        'quality': 4, 
        'random_talent_type': (10, 11, 12, 13, ), 
    }), 
    292: TD({
        'id': 292, 
        'add_br_money': 60000, 
        'high_value': 1, 
        'icon': 13402, 
        'item_description': '60000', 
        'item_ground_model_id': 649, 
        'item_sub_type': 21, 
        'item_type': 105, 
        'mark_voice_id': (49, ), 
        'name': '古典花瓶', 
        'quality': 4, 
    }), 
    293: TD({
        'id': 293, 
        'add_br_money': 80000, 
        'high_value': 1, 
        'icon': 13403, 
        'item_description': '80000', 
        'item_ground_model_id': 648, 
        'item_sub_type': 21, 
        'item_type': 105, 
        'mark_voice_id': (49, ), 
        'name': '纯金奖牌', 
        'quality': 4, 
    }), 
    294: TD({
        'id': 294, 
        'add_br_money': 100000, 
        'high_value': 1, 
        'icon': 13400, 
        'item_description': '100000', 
        'item_ground_model_id': 651, 
        'item_sub_type': 21, 
        'item_type': 105, 
        'mark_voice_id': (49, ), 
        'name': '纯金圣杯', 
        'quality': 4, 
    }), 
    295: TD({
        'id': 295, 
        'add_br_money': 150000, 
        'high_value': 1, 
        'icon': 13401, 
        'item_description': '150000', 
        'item_ground_model_id': 647, 
        'item_sub_type': 21, 
        'item_type': 105, 
        'mark_voice_id': (49, ), 
        'name': '纯金王冠', 
        'quality': 4, 
    }), 
    296: TD({
        'id': 296, 
        'add_br_money': 2000, 
        'icon': 116, 
        'item_description': '一堆金条', 
        'item_ground_model_id': 511, 
        'item_type': 105, 
        'mark_voice_id': (49, ), 
        'name': '金币', 
        'quality': 2, 
        'short_tips': '少量金条', 
    }), 
    297: TD({
        'id': 297, 
        'add_br_money': 5000, 
        'icon': 13406, 
        'item_description': '一堆金条', 
        'item_ground_model_id': 630, 
        'item_type': 105, 
        'mark_voice_id': (49, ), 
        'name': '金条', 
        'quality': 2, 
        'short_tips': '少量金条', 
    }), 
    298: TD({
        'id': 298, 
        'add_br_money': 10000, 
        'icon': 13405, 
        'item_description': '一堆金条', 
        'item_ground_model_id': 631, 
        'item_type': 105, 
        'mark_voice_id': (49, ), 
        'name': '金条堆（小）', 
        'quality': 4, 
        'short_tips': '少量金条', 
    }), 
    299: TD({
        'id': 299, 
        'add_br_money': 30000, 
        'icon': 13404, 
        'item_description': '一袋金条', 
        'item_ground_model_id': 632, 
        'item_type': 105, 
        'mark_voice_id': (49, ), 
        'name': '金条堆（大）', 
        'quality': 4, 
        'short_tips': '一袋金条', 
    }), 
    3: TD({
        'id': 3, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 3, 
        'ground_max_stack_limit': 2, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 389, 
        'icon_outline': 11462, 
        'ig_voice_id': (1082, ), 
        'item_description': '爆炸后产生范围伤害', 
        'item_ground_model_id': 536, 
        'item_sub_type': 11, 
        'item_type': 101, 
        'mark_voice_id': (10071, ), 
        'name': '破片手雷', 
        'quality': 1, 
        'short_tips': '投掷物', 
    }), 
    300: TD({
        'id': 300, 
        'add_br_money': 50000, 
        'high_value': 1, 
        'icon': 13407, 
        'item_description': '大堆金条', 
        'item_ground_model_id': 633, 
        'item_type': 105, 
        'mark_voice_id': (49, ), 
        'name': '大袋金条', 
        'quality': 4, 
        'short_tips': '大堆金条', 
    }), 
    301: TD({
        'id': 301, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 10682, 
        'item_description': '拾取后减少队友30%复活时间', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 112, 
        'mark_voice_id': (44, ), 
        'name': '队友的名牌', 
        'quality': 3, 
        'short_tips': '拾取减少复活时间', 
    }), 
    302: TD({
        'id': 302, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 10132, 
        'item_description': '由战利品天赋击杀获得', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 114, 
        'name': '战利品', 
        'quality': 3, 
        'trophies_group': 1053, 
    }), 
    303: TD({
        'id': 303, 
        'add_core_points': 100, 
        'icon': 598, 
        'item_description': '获得100点进化能量', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 116, 
        'mark_voice_id': (10076, ), 
        'name': '进化能量（小）', 
        'quality': 2, 
        'short_tips': '获得100点进化能量', 
    }), 
    304: TD({
        'id': 304, 
        'add_core_points': 200, 
        'icon': 599, 
        'item_description': '获得200点进化能量', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 116, 
        'mark_voice_id': (10076, ), 
        'name': '进化能量（中）', 
        'quality': 3, 
        'short_tips': '获得200点进化能量', 
    }), 
    305: TD({
        'id': 305, 
        'add_core_points': 400, 
        'auto_mark': 1, 
        'icon': 600, 
        'item_description': '获得400点进化能量', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 116, 
        'mark_voice_id': (10076, ), 
        'name': '进化能量（大）', 
        'quality': 4, 
        'short_tips': '获得400点进化能量', 
    }), 
    306: TD({
        'id': 306, 
        'add_core_points': 600, 
        'auto_mark': 1, 
        'icon': 601, 
        'item_description': '获得600点进化能量', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 116, 
        'mark_voice_id': (10076, ), 
        'name': '进化能量（特）', 
        'quality': 4, 
        'short_tips': '获得600点进化能量', 
    }), 
    307: TD({
        'id': 307, 
        'bag_max_stack_limit': 1, 
        'can_drop': False, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 229, 
        'hold': TD({
            1: 'recycle', 
            3: 'recycle', 
            7: 'recycle', 
        }), 
        'icon': 11181, 
        'item_description': '点击后可以扫描裂隙位置', 
        'item_sub_type': 20, 
        'item_type': 101, 
        'name': '扫描器', 
        'quality': 1, 
        'short_tips': '扫描裂隙位置', 
    }), 
    308: TD({
        'id': 308, 
        'can_drop': False, 
        'ground_max_stack_limit': 6, 
        'icon': 11230, 
        'item_description': '可用于局外进行奖励兑换', 
        'item_ground_model_id': 595, 
        'item_sub_type': 1, 
        'item_type': 117, 
        'name': '血晶石', 
        'quality': 4, 
        'short_tips': '兑换活动奖励', 
    }), 
    309: TD({
        'id': 309, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 230, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 788, 
        'ig_voice_id': (1125, ), 
        'item_description': '召唤来自地狱的火焰，形成一堵阻挡敌人视野的火墙，经过火墙的敌人会受到短暂的致盲效果', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '地狱之火', 
        'skill_full_description': '召唤来自地狱的火焰，形成一堵阻挡敌人视野的火墙，火墙长#eed42b 30 #Em,高#eed42b 4 #Em，创建方向可以自由切换，持续#eed42b 20 #E秒，经过火墙的敌人会受到#eed42b 1.5s#E 的致盲效果', 
        'take_directly': True, 
    }), 
    310: TD({
        'id': 310, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 231, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 827, 
        'ig_voice_id': (1127, ), 
        'item_description': '蓄力后向前斩出，获得一段位移', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '疾风斩', 
        'skill_full_description': '蓄力后向前斩出，蓄力时间越长，斩击速度越快；三段蓄力所需时间分别为 #eed42b 1.2/1.8/2.4 s#E ，蓄力后斩击速度分别为 #eed42b 8/16/30 m/s #E', 
        'take_directly': True, 
    }), 
    311: TD({
        'id': 311, 
        'br_task_id': 7, 
        'ground_max_stack_limit': 1, 
        'icon': 11302, 
        'item_description': '拾取后接取神秘商店任务', 
        'item_ground_model_id': 561, 
        'item_type': 107, 
        'mark_voice_id': (70, ), 
        'name': '任务代号-神秘商店', 
        'quality': 3, 
    }), 
    312: TD({
        'id': 312, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100025, 
        'icon': 813, 
        'icon_outline': 815, 
        'item_description': '原装VSS半自动狙击枪', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS', 
        'quality': 1, 
    }), 
    313: TD({
        'id': 313, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100025, 
        'icon': 813, 
        'icon_outline': 815, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS-自定义', 
        'quality': 6, 
    }), 
    314: TD({
        'id': 314, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            8: 'hold', 
            'cache': (8, ), 
        }), 
        'equip_id': 232, 
        'has_cancel': True, 
        'hold': TD({
            8: 'using', 
        }), 
        'icon': 788, 
        'item_description': '格挡来自前方的子弹', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '格挡', 
        'take_directly': True, 
        'using': TD({
            10: 'recycle', 
            3: 'recycle', 
            'cache': (3, ), 
        }), 
    }), 
    315: TD({
        'id': 315, 
        'bag_max_stack_limit': 1, 
        'equip_id': 233, 
        'icon': 549, 
        'icon_outline': 557, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '武士刀-大厅挂接', 
        'quality': 4, 
    }), 
    316: TD({
        'id': 316, 
        'bag_max_stack_limit': 1, 
        'icon': 549, 
        'icon_outline': 557, 
        'item_description': 'ZERO-武士刀', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '武士刀鞘-大厅挂接', 
        'quality': 4, 
    }), 
    317: TD({
        'id': 317, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 234, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 853, 
        'ig_voice_id': (1129, ), 
        'item_description': '蓄力后向前发射电磁脉冲，对护甲和设备造成伤害，同时使敌人进入干扰状态，看不清队友名牌并且无法使用技能', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '电磁爆破', 
        'quality': 4, 
        'skill_full_description': '蓄力后向前发射电磁脉冲（脉冲宽度为 #eed42b 20m #E）,蓄力最远可对 #eed42b 40m #E内敌人的护甲和设备造成 #eed42b 40 #E伤害，同时使敌人进入干扰状态，看不清队友名牌并且无法使用技能，持续 #eed42b 6s#E', 
        'take_directly': True, 
    }), 
    318: TD({
        'id': 318, 
        'bag_max_stack_limit': 1, 
        'icon': 11406, 
        'item_description': '获得护甲升级，125点护甲', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 118, 
        'name': '护甲升级Lv.4', 
        'quality': 4, 
    }), 
    319: TD({
        'id': 319, 
        'bag_max_stack_limit': 1, 
        'icon': 11407, 
        'item_description': '获得技能升级', 
        'item_ground_model_id': 579, 
        'item_sub_type': 2, 
        'item_type': 119, 
        'name': '技能升级', 
        'quality': 4, 
    }), 
    320: TD({
        'id': 320, 
        'bag_max_stack_limit': 1, 
        'icon': 11404, 
        'item_description': '获得护甲升级，75点护甲', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 118, 
        'name': '护甲Lv.2', 
        'quality': 2, 
    }), 
    321: TD({
        'id': 321, 
        'bag_max_stack_limit': 1, 
        'icon': 11405, 
        'item_description': '获得护甲升级，100点护甲', 
        'item_ground_model_id': 579, 
        'item_sub_type': 1, 
        'item_type': 118, 
        'name': '护甲Lv.3', 
        'quality': 3, 
    }), 
    322: TD({
        'id': 322, 
        'bag_max_stack_limit': 1, 
        'equip_id': 93, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100027, 
        'icon': 110000, 
        'icon_outline': 110002, 
        'item_description': '原装AR97', 
        'item_ground_model_id': 608, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AR97', 
        'quality': 1, 
    }), 
    323: TD({
        'id': 323, 
        'item_sub_type': 1, 
        'item_type': 201, 
        'name': '占位-直升机', 
        'quality': 1, 
        'skip': 'trunk_only', 
    }), 
    324: TD({
        'id': 324, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 93, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100027, 
        'icon': 110000, 
        'icon_outline': 110002, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 608, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AR97-自定义', 
        'quality': 6, 
    }), 
    325: TD({
        'id': 325, 
        'bag_max_stack_limit': 1, 
        'equip_id': 94, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100028, 
        'icon': 1011, 
        'icon_outline': 1010, 
        'item_description': '原装Minigun', 
        'item_ground_model_id': 616, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Minigun', 
        'quality': 1, 
    }), 
    326: TD({
        'id': 326, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 94, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100028, 
        'icon': 1011, 
        'icon_outline': 1010, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 616, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Minigun-自定义', 
        'quality': 6, 
    }), 
    327: TD({
        'id': 327, 
        'bag_max_stack_limit': 1, 
        'equip_id': 235, 
        'icon': 1092, 
        'ig_voice_CD_id': (1141, ), 
        'ig_voice_id': (1140, ), 
        'item_description': '激活高能状态，持续期间滑铲速度提升。', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '嗨翻全场', 
        'quality': 4, 
        'skill_full_description': '激活高能状态，持续期间滑铲速度提升。', 
    }), 
    328: TD({
        'id': 328, 
        'bag_max_stack_limit': 1, 
        'equip_id': 2, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100002, 
        'icon': 1031, 
        'icon_outline': 12074, 
        'item_description': '原装MP5', 
        'item_ground_model_id': 618, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'MP5', 
        'quality': 1, 
    }), 
    329: TD({
        'id': 329, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 2, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100002, 
        'icon': 1031, 
        'icon_outline': 12074, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 618, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'MP5', 
        'quality': 6, 
    }), 
    33: TD({
        'id': 33, 
        'br_task_id': 3, 
        'ground_max_stack_limit': 1, 
        'icon': 400, 
        'item_description': '拾取后接取猎杀任务', 
        'item_ground_model_id': 561, 
        'item_type': 107, 
        'mark_voice_id': (70, ), 
        'name': '任务代号-猎杀', 
        'quality': 3, 
    }), 
    330: TD({
        'id': 330, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1015, 
        'icon': 341, 
        'icon_outline': 325, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1-自定义', 
        'quality': 6, 
    }), 
    331: TD({
        'id': 331, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1016, 
        'icon': 341, 
        'icon_outline': 325, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1-自定义', 
        'quality': 6, 
    }), 
    332: TD({
        'id': 332, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8016, 
        'icon': 344, 
        'icon_outline': 220, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector-自定义', 
        'quality': 6, 
    }), 
    333: TD({
        'id': 333, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8017, 
        'icon': 344, 
        'icon_outline': 220, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector-自定义', 
        'quality': 6, 
    }), 
    334: TD({
        'id': 334, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10016, 
        'icon': 345, 
        'icon_outline': 174, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47-自定义', 
        'quality': 6, 
    }), 
    335: TD({
        'id': 335, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10017, 
        'icon': 345, 
        'icon_outline': 174, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47-自定义', 
        'quality': 6, 
    }), 
    336: TD({
        'id': 336, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14016, 
        'icon': 347, 
        'icon_outline': 217, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6-自定义', 
        'quality': 6, 
    }), 
    337: TD({
        'id': 337, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14017, 
        'icon': 347, 
        'icon_outline': 217, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6-自定义', 
        'quality': 6, 
    }), 
    338: TD({
        'id': 338, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16019, 
        'icon': 349, 
        'icon_outline': 322, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB-自定义', 
        'quality': 6, 
    }), 
    339: TD({
        'id': 339, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16020, 
        'icon': 349, 
        'icon_outline': 322, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB-自定义', 
        'quality': 6, 
    }), 
    34: TD({
        'id': 34, 
        'br_task_id': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 401, 
        'item_description': '拾取后接取占点任务', 
        'item_ground_model_id': 561, 
        'item_type': 107, 
        'mark_voice_id': (70, ), 
        'name': '任务代号-占点', 
        'quality': 3, 
    }), 
    340: TD({
        'id': 340, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17017, 
        'icon': 350, 
        'icon_outline': 323, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9-自定义', 
        'quality': 6, 
    }), 
    341: TD({
        'id': 341, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17018, 
        'icon': 350, 
        'icon_outline': 323, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9-自定义', 
        'quality': 6, 
    }), 
    342: TD({
        'id': 342, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23017, 
        'icon': 749, 
        'icon_outline': 611, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR-自定义', 
        'quality': 6, 
    }), 
    343: TD({
        'id': 343, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23018, 
        'icon': 749, 
        'icon_outline': 611, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SCAR-自定义', 
        'quality': 6, 
    }), 
    344: TD({
        'id': 344, 
        'bag_max_stack_limit': 1, 
        'equip_id': 90, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 24014, 
        'icon': 770, 
        'icon_outline': 763, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 591, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'P90-自定义', 
        'quality': 6, 
    }), 
    345: TD({
        'id': 345, 
        'bag_max_stack_limit': 1, 
        'equip_id': 90, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 24015, 
        'icon': 770, 
        'icon_outline': 763, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 591, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'P90-自定义', 
        'quality': 6, 
    }), 
    346: TD({
        'id': 346, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25006, 
        'icon': 813, 
        'icon_outline': 815, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS-自定义', 
        'quality': 6, 
    }), 
    347: TD({
        'id': 347, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25007, 
        'icon': 813, 
        'icon_outline': 815, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS-自定义', 
        'quality': 6, 
    }), 
    348: TD({
        'id': 348, 
        'can_drop': False, 
        'ground_max_stack_limit': 5, 
        'icon': 11860, 
        'item_description': '可用于局外进行奖励兑换', 
        'item_ground_model_id': 614, 
        'item_sub_type': 1, 
        'item_type': 120, 
        'name': '线索', 
        'quality': 4, 
        'short_tips': '兑换活动奖励', 
    }), 
    349: TD({
        'id': 349, 
        'icon': 189, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '转移无人机', 
        'quality': 4, 
    }), 
    35: TD({
        'id': 35, 
        'br_task_id': 2, 
        'ground_max_stack_limit': 1, 
        'icon': 402, 
        'item_description': '拾取后接取搜寻任务', 
        'item_ground_model_id': 561, 
        'item_type': 107, 
        'mark_voice_id': (70, ), 
        'name': '任务代号-搜寻', 
        'quality': 3, 
    }), 
    350: TD({
        'id': 350, 
        'icon': 189, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '干扰UAV', 
        'quality': 4, 
    }), 
    351: TD({
        'id': 351, 
        'icon': 189, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '快捷商店', 
        'quality': 4, 
    }), 
    352: TD({
        'id': 352, 
        'bag_max_stack_limit': 1, 
        'equip_id': 236, 
        'icon': 1008, 
        'item_description': '短时间内大幅度强化甲虫无人机的治疗效果', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '应急治疗', 
        'quality': 4, 
        'skill_full_description': '短时间内增加  #eed42b 100% #E甲虫无人机的治疗效果', 
    }), 
    353: TD({
        'id': 353, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 237, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1098, 
        'ig_voice_id': (78, ), 
        'item_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '爱愈降临', 
        'quality': 4, 
        'skill_full_description': '对友方或自身释放厄洛斯，厄洛斯会跟随并持续治疗目标玩家。', 
        'take_directly': True, 
    }), 
    354: TD({
        'id': 354, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 238, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1017, 
        'ig_voice_id': (78, ), 
        'item_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '甲虫无人机', 
        'quality': 4, 
        'skill_full_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友，每1.5秒恢复 #eed42b 8 #E生命值（护盾效率减半）；无人机可对 #eed42b 40m#E内的队友释放（不会受到阻挡），无人机可以被子弹或技能破坏', 
    }), 
    355: TD({
        'id': 355, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1017, 
        'icon': 856, 
        'icon_outline': 325, 
        'item_description': '装备扩容弹匣的M4A1步枪', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 3, 
    }), 
    356: TD({
        'id': 356, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 1018, 
        'icon': 857, 
        'icon_outline': 325, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 4, 
    }), 
    357: TD({
        'id': 357, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6014, 
        'icon': 858, 
        'icon_outline': 214, 
        'item_description': '装备延长枪管的Origin12霰弹枪', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Origin12', 
        'quality': 3, 
    }), 
    358: TD({
        'id': 358, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 34, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 6015, 
        'icon': 859, 
        'icon_outline': 214, 
        'item_description': '装备弹鼓的Origin12霰弹枪', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Origin12', 
        'quality': 4, 
    }), 
    359: TD({
        'id': 359, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8018, 
        'icon': 860, 
        'icon_outline': 220, 
        'item_description': '综合性能优秀的Vector冲锋枪', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector', 
        'quality': 3, 
    }), 
    36: TD({
        'id': 36, 
        'add_br_money': 4000, 
        'icon': 116, 
        'item_description': '大堆金币', 
        'item_ground_model_id': 512, 
        'item_type': 105, 
        'mark_voice_id': (10077, ), 
        'name': '金条', 
        'quality': 4, 
        'short_tips': '大堆金币', 
    }), 
    360: TD({
        'id': 360, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 38, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 8019, 
        'icon': 861, 
        'icon_outline': 220, 
        'item_description': '装备弹鼓的Vector冲锋枪', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector', 
        'quality': 4, 
    }), 
    361: TD({
        'id': 361, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10018, 
        'icon': 862, 
        'icon_outline': 174, 
        'item_description': '装备4倍镜，强化中距离作战能力', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47', 
        'quality': 3, 
    }), 
    362: TD({
        'id': 362, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 10019, 
        'icon': 863, 
        'icon_outline': 174, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47', 
        'quality': 4, 
    }), 
    363: TD({
        'id': 363, 
        'bag_max_stack_limit': 1, 
        'equip_id': 71, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 13013, 
        'icon': 864, 
        'icon_outline': 266, 
        'item_description': '装备延长重型枪管，强化远距离作战能力', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Kala', 
        'quality': 3, 
    }), 
    364: TD({
        'id': 364, 
        'bag_max_stack_limit': 1, 
        'equip_id': 71, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 13014, 
        'icon': 865, 
        'icon_outline': 266, 
        'item_description': '装备2倍镜，强化近距离作战能力', 
        'item_ground_model_id': 570, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Kala', 
        'quality': 4, 
    }), 
    365: TD({
        'id': 365, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14018, 
        'icon': 866, 
        'icon_outline': 217, 
        'item_description': '装备扩容弹匣的KAG-6步枪', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6', 
        'quality': 3, 
    }), 
    366: TD({
        'id': 366, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 72, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 14019, 
        'icon': 867, 
        'icon_outline': 217, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6', 
        'quality': 4, 
    }), 
    367: TD({
        'id': 367, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16021, 
        'icon': 868, 
        'icon_outline': 322, 
        'item_description': '装备2倍镜，强化中距离作战能力', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB', 
        'quality': 3, 
    }), 
    368: TD({
        'id': 368, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 75, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 16022, 
        'icon': 869, 
        'icon_outline': 322, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 573, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'URB', 
        'quality': 4, 
    }), 
    369: TD({
        'id': 369, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17019, 
        'icon': 870, 
        'icon_outline': 323, 
        'item_description': '综合性能优秀的INP-9冲锋枪', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9', 
        'quality': 3, 
    }), 
    37: TD({
        'id': 37, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 14, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 110, 
        'item_description': '使用后可扫描玩家前方50M内的敌人', 
        'item_ground_model_id': 27, 
        'item_sub_type': 13, 
        'item_type': 101, 
        'name': '心跳扫描器', 
        'quality': 3, 
        'using': TD({
            1: 'recycle', 
            3: 'recycle', 
        }), 
    }), 
    370: TD({
        'id': 370, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 76, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 17020, 
        'icon': 871, 
        'icon_outline': 323, 
        'item_description': '装备2倍镜和枪口制退器，强化中距离作战能力', 
        'item_ground_model_id': 574, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'INP-9', 
        'quality': 4, 
    }), 
    371: TD({
        'id': 371, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18014, 
        'icon': 872, 
        'icon_outline': 324, 
        'item_description': '装备扩容弹匣的M700狙击步枪', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700', 
        'quality': 3, 
    }), 
    372: TD({
        'id': 372, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 18015, 
        'icon': 873, 
        'icon_outline': 324, 
        'item_description': '装备2倍镜，强化近距离作战能力', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700', 
        'quality': 4, 
    }), 
    373: TD({
        'id': 373, 
        'bag_max_stack_limit': 1, 
        'equip_id': 79, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 20012, 
        'icon': 874, 
        'icon_outline': 321, 
        'item_description': '综合性能优秀的M870霰弹枪', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M870', 
        'quality': 3, 
    }), 
    374: TD({
        'id': 374, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 79, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 20013, 
        'icon': 875, 
        'icon_outline': 321, 
        'item_description': '装备长枪管，强化中距离作战能力', 
        'item_ground_model_id': 576, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M870', 
        'quality': 4, 
    }), 
    375: TD({
        'id': 375, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23019, 
        'icon': 876, 
        'icon_outline': 611, 
        'item_description': '综合性能优秀的SCAR步枪', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Scar', 
        'quality': 3, 
    }), 
    376: TD({
        'id': 376, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 88, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 23020, 
        'icon': 877, 
        'icon_outline': 611, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 588, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Scar', 
        'quality': 4, 
    }), 
    377: TD({
        'id': 377, 
        'bag_max_stack_limit': 1, 
        'equip_id': 90, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 24016, 
        'icon': 878, 
        'icon_outline': 763, 
        'item_description': '综合性能优秀的P90冲锋枪', 
        'item_ground_model_id': 591, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'P90', 
        'quality': 3, 
    }), 
    378: TD({
        'id': 378, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 90, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 24017, 
        'icon': 879, 
        'icon_outline': 763, 
        'item_description': '装备2倍镜，强化近距离作战能力', 
        'item_ground_model_id': 591, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'P90', 
        'quality': 4, 
    }), 
    379: TD({
        'id': 379, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25008, 
        'icon': 880, 
        'icon_outline': 815, 
        'item_description': '原装VSS半自动狙击枪', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS', 
        'quality': 1, 
    }), 
    38: TD({
        'id': 38, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 1, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100001, 
        'icon': 735, 
        'icon_outline': 325, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 6, 
    }), 
    380: TD({
        'id': 380, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25009, 
        'icon': 881, 
        'icon_outline': 815, 
        'item_description': '装备2倍镜的VSS半自动狙击枪', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS', 
        'quality': 2, 
    }), 
    381: TD({
        'id': 381, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25010, 
        'icon': 882, 
        'icon_outline': 815, 
        'item_description': '装备扩容弹匣的VSS半自动狙击枪', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS', 
        'quality': 3, 
    }), 
    382: TD({
        'id': 382, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25011, 
        'icon': 883, 
        'icon_outline': 815, 
        'item_description': '装备狙击枪托和4倍镜，强化远距离作战能力', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS', 
        'quality': 4, 
    }), 
    383: TD({
        'id': 383, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25012, 
        'icon': 884, 
        'icon_outline': 815, 
        'item_description': '装备20发尖头弹匣，强化伤害', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS', 
        'quality': 3, 
    }), 
    384: TD({
        'id': 384, 
        'bag_max_stack_limit': 1, 
        'equip_id': 91, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 25013, 
        'icon': 885, 
        'icon_outline': 815, 
        'item_description': '综合性能优秀的VSS半自动狙击枪', 
        'item_ground_model_id': 602, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'VSS', 
        'quality': 4, 
    }), 
    385: TD({
        'id': 385, 
        'bag_max_stack_limit': 1, 
        'equip_id': 93, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 27006, 
        'icon': 886, 
        'icon_outline': 110002, 
        'item_description': '原装AR97步枪', 
        'item_ground_model_id': 608, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AR97', 
        'quality': 1, 
    }), 
    386: TD({
        'id': 386, 
        'bag_max_stack_limit': 1, 
        'equip_id': 93, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 27007, 
        'icon': 887, 
        'icon_outline': 110002, 
        'item_description': '装备了枪口制退器的AR97步枪', 
        'item_ground_model_id': 608, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AR97', 
        'quality': 2, 
    }), 
    387: TD({
        'id': 387, 
        'bag_max_stack_limit': 1, 
        'equip_id': 93, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 27008, 
        'icon': 888, 
        'icon_outline': 110002, 
        'item_description': '装备扩容弹匣的原装AR97步枪', 
        'item_ground_model_id': 608, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AR97', 
        'quality': 3, 
    }), 
    388: TD({
        'id': 388, 
        'bag_max_stack_limit': 1, 
        'equip_id': 93, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 27009, 
        'icon': 889, 
        'icon_outline': 110002, 
        'item_description': '腰射精准，强化近距离作战能力', 
        'item_ground_model_id': 608, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AR97', 
        'quality': 4, 
    }), 
    389: TD({
        'id': 389, 
        'bag_max_stack_limit': 1, 
        'equip_id': 93, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 27010, 
        'icon': 890, 
        'icon_outline': 110002, 
        'item_description': '综合性能优秀的AR97步枪', 
        'item_ground_model_id': 608, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AR97', 
        'quality': 3, 
    }), 
    390: TD({
        'id': 390, 
        'bag_max_stack_limit': 1, 
        'equip_id': 93, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 27011, 
        'icon': 891, 
        'icon_outline': 110002, 
        'item_description': '装备长枪管和4倍镜，强化远距离作战能力', 
        'item_ground_model_id': 608, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AR97', 
        'quality': 4, 
    }), 
    391: TD({
        'id': 391, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 239, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'item_description': '可用于破解任务打开保险箱使用', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '破解任务-U盘', 
        'quality': 4, 
        'skip': 'trunk_only', 
        'take_directly': True, 
    }), 
    392: TD({
        'id': 392, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 240, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
            8: 'using', 
        }), 
        'icon': 642, 
        'ig_voice_id': (1052, 1053, ), 
        'item_description': 'blast可以用改装手臂发射热能炸弹，可以穿透障碍伤害敌人', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '钻墙炸弹-改', 
        'skill_full_description': 'blast可以用改装手臂发射热能炸弹，可以穿透障碍伤害敌人', 
        'take_directly': True, 
    }), 
    393: TD({
        'id': 393, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            8: 'hold', 
        }), 
        'equip_id': 241, 
        'ground_max_stack_limit': 2, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 389, 
        'icon_outline': 11462, 
        'ig_voice_id': (1082, ), 
        'item_description': '爆炸后产生范围伤害', 
        'item_sub_type': 11, 
        'item_type': 101, 
        'mark_voice_id': (61, ), 
        'name': '分裂手雷', 
        'quality': 1, 
        'short_tips': '投掷物', 
    }), 
    394: TD({
        'id': 394, 
        'bag_max_stack_limit': 1, 
        'equip_id': 98, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100029, 
        'icon': 110021, 
        'icon_outline': 110023, 
        'item_description': '原装AUG', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AUG', 
        'quality': 1, 
    }), 
    395: TD({
        'id': 395, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 98, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100029, 
        'icon': 110021, 
        'icon_outline': 110023, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 646, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AUG-自定义', 
        'quality': 6, 
    }), 
    396: TD({
        'id': 396, 
        'bag_max_stack_limit': 1, 
        'equip_id': 101, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100030, 
        'icon': 110021, 
        'icon_outline': 110023, 
        'item_description': '原装Magnum', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Magnum', 
        'quality': 1, 
        'skip': 'trunk_only', 
    }), 
    397: TD({
        'id': 397, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 101, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100030, 
        'icon': 110021, 
        'icon_outline': 110023, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 572, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Magnum-自定义', 
        'quality': 6, 
        'skip': 'trunk_only', 
    }), 
    398: TD({
        'id': 398, 
        'bag_max_stack_limit': 1, 
        'equip_id': 102, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100031, 
        'icon': 110029, 
        'icon_outline': 110031, 
        'item_description': '原装MCX', 
        'item_ground_model_id': 656, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'MCX', 
        'quality': 1, 
    }), 
    399: TD({
        'id': 399, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 102, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100031, 
        'icon': 110029, 
        'icon_outline': 110031, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 656, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'MCX-自定义', 
        'quality': 6, 
    }), 
    4: TD({
        'id': 4, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 4, 
        'ground_max_stack_limit': 2, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 108, 
        'icon_outline': 11463, 
        'ig_voice_id': (1083, ), 
        'item_description': '爆炸后产生烟雾，遮挡视野', 
        'item_ground_model_id': 538, 
        'item_sub_type': 12, 
        'item_type': 101, 
        'mark_voice_id': (10071, ), 
        'name': '烟雾弹', 
        'quality': 1, 
        'short_tips': '投掷物', 
    }), 
    40: TD({
        'id': 40, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 77, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100018, 
        'icon': 351, 
        'icon_outline': 324, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M700', 
        'quality': 3, 
    }), 
    400: TD({
        'id': 400, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 242, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1086, 
        'ig_voice_id': (1129, ), 
        'item_description': '命中后可短暂的显示附近敌人的位置', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '侦察箭', 
        'quality': 4, 
        'skill_full_description': '命中后可短暂的显示附近  #eed42b 10m #E敌人的位置,持续  #eed42b 15s #E', 
        'take_directly': True, 
    }), 
    400004: TD({
        'id': 400004, 
        'bag_max_stack_limit': 10, 
        'can_drop': False, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 300, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10482, 
        'item_description': '准备阶段使用，使用后随机获得一个天赋', 
        'item_ground_model_id': 524, 
        'item_sub_type': 17, 
        'item_type': 101, 
        'name': 'x化合物', 
        'operate_mode': 1, 
        'quality': 3, 
        'short_tips': '使用后随机获得天赋', 
        'skip': 'skip', 
    }), 
    401: TD({
        'id': 401, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 243, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1083, 
        'ig_voice_id': (1129, ), 
        'item_description': '发射可穿墙的脉冲爆炸，并减速命中的敌人', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '脉冲箭', 
        'quality': 4, 
        'skill_full_description': '发射一道宽  #eed42b 8m#E ,长  #eed42b 40m#E 的可穿墙的脉冲爆炸，被命中的敌人减速并且无法进入冲刺，持续 #eed42b 5s #E', 
        'take_directly': True, 
    }), 
    402: TD({
        'id': 402, 
        'bag_max_stack_limit': 1, 
        'equip_id': 301, 
        'icon': 549, 
        'icon_outline': 557, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'name': '弓-大厅挂接', 
        'quality': 4, 
    }), 
    403: TD({
        'id': 403, 
        'bag_max_stack_limit': 1, 
        'equip_id': 103, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100032, 
        'icon': 110029, 
        'icon_outline': 110031, 
        'item_description': '原装FAL', 
        'item_ground_model_id': 656, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'FAL', 
        'quality': 1, 
        'skip': 'trunk_only', 
    }), 
    404: TD({
        'id': 404, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 103, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100032, 
        'icon': 110029, 
        'icon_outline': 110031, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 656, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'FAL-自定义', 
        'quality': 6, 
        'skip': 'trunk_only', 
    }), 
    405: TD({
        'id': 405, 
        'bag_max_stack_limit': 1, 
        'equip_id': 103, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100033, 
        'icon': 351, 
        'icon_outline': 324, 
        'item_description': '原装Kar98k栓动步枪', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Kar98k', 
        'quality': 3, 
        'skip': 'trunk_only', 
    }), 
    406: TD({
        'id': 406, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 103, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100033, 
        'icon': 744, 
        'icon_outline': 324, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 575, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Kar98k-自定义', 
        'quality': 6, 
        'skip': 'trunk_only', 
    }), 
    407: TD({
        'id': 407, 
        'bag_max_stack_limit': 1, 
        'equip_id': 100, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100035, 
        'icon': 735, 
        'icon_outline': 325, 
        'item_description': '原装M4A1步枪', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 1, 
    }), 
    408: TD({
        'id': 408, 
        'bag_max_stack_limit': 1, 
        'equip_id': 100, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100035, 
        'icon': 735, 
        'icon_outline': 325, 
        'item_description': '原装M4A1步枪', 
        'item_ground_model_id': 565, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'M4A1', 
        'quality': 1, 
    }), 
    409: TD({
        'id': 409, 
        'bag_max_stack_limit': 1, 
        'equip_id': 108, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100034, 
        'icon': 110072, 
        'icon_outline': 110074, 
        'item_description': '原装PKM', 
        'item_ground_model_id': 3863, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'PKM', 
        'quality': 1, 
    }), 
    41: TD({
        'id': 41, 
        'bag_max_stack_limit': 1, 
        'equip_id': 15, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100004, 
        'icon': 736, 
        'icon_outline': 1088, 
        'icon_outline_2': 799, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Glock', 
        'quality': 6, 
    }), 
    410: TD({
        'id': 410, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 108, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100034, 
        'icon': 110072, 
        'icon_outline': 110074, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 3863, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'PKM-自定义', 
        'quality': 6, 
    }), 
    42: TD({
        'id': 42, 
        'bag_max_stack_limit': 1, 
        'equip_id': 15, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100004, 
        'icon': 736, 
        'icon_outline': 1088, 
        'icon_outline_2': 799, 
        'item_description': '原装Glock手枪', 
        'item_ground_model_id': 566, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Glock', 
        'quality': 1, 
    }), 
    429: TD({
        'id': 429, 
        'bag_max_stack_limit': 1, 
        'equip_id': 110, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100040, 
        'icon': 110097, 
        'icon_outline': 110099, 
        'item_description': '原装Uzi', 
        'item_ground_model_id': 3868, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Uzi', 
        'quality': 1, 
    }), 
    43: TD({
        'id': 43, 
        'bag_max_stack_limit': 1, 
        'equip_id': 16, 
        'icon': 149, 
        'icon_outline': 149, 
        'item_description': '拳头', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '拳头', 
        'quality': 1, 
    }), 
    430: TD({
        'id': 430, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 110, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100040, 
        'icon': 110097, 
        'icon_outline': 110099, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 3868, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Uzi-自定义', 
        'quality': 6, 
    }), 
    437: TD({
        'id': 437, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 437, 
        'ground_max_stack_limit': 1, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 328, 
        'ig_voice_id': (52, ), 
        'item_description': '占位遥控战车：部署一个可遥控的小车攻击敌人。持续30秒，CD：90秒', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '占位-HANK遥控战车', 
        'short_tips': '占位遥控战车：部署一个可遥控的小车攻击敌人。持续30秒，CD：90秒', 
        'skill_full_description': '部署一台机械狗，再次使用技能可指令其移动。机械狗会自动锁定攻击范围内可见敌人。', 
        'skip': 'trunk_only', 
        'take_directly': True, 
    }), 
    44: TD({
        'id': 44, 
        'bag_max_stack_limit': 5, 
        'ground_max_stack_limit': 1, 
        'icon': 114, 
        'item_description': '增加一次复活机会', 
        'item_sub_type': 1, 
        'item_type': 108, 
        'name': '重生勋章', 
        'quality': 4, 
        'rebirth_count': 1, 
    }), 
    46: TD({
        'id': 46, 
        'bag_max_stack_limit': 8, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 20, 
        'ground_max_stack_limit': 8, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 179, 
        'ig_voice_id': (1167, ), 
        'item_description': '使用绷带，小幅度回复生命值', 
        'item_ground_model_id': 523, 
        'item_sub_type': 17, 
        'item_type': 101, 
        'mark_voice_id': (10069, ), 
        'name': '绷带', 
        'operate_mode': 1, 
        'short_tips': '医疗道具', 
        'take_directly': True, 
    }), 
    467: TD({
        'id': 467, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 467, 
        'ground_max_stack_limit': 1, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1089, 
        'ig_voice_id': (52, ), 
        'item_description': '部署一个可指挥移动的全自动战车攻击敌人', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '游猎哨卫', 
        'short_tips': '部署一个可指挥移动的全自动战车攻击敌人', 
        'skill_full_description': '部署一台机械狗，再次使用技能可指令其移动。机械狗会自动锁定攻击范围内可见敌人。', 
        'take_directly': True, 
    }), 
    469: TD({
        'id': 469, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 469, 
        'has_cancel': True, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1090, 
        'item_description': 'HANK的巡逻无人机', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '巡蜂索敌', 
        'skill_full_description': '投掷一个巡飞弹，在指定范围内盘旋并索敌，锁定目标后俯冲引爆，对范围内敌人造成伤害和短暂减速，并暴露其当前位置。', 
        'take_directly': True, 
    }), 
    47: TD({
        'id': 47, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 21, 
        'ground_max_stack_limit': 2, 
        'ground_pitch': 90.0, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 180, 
        'ig_voice_id': (1167, ), 
        'item_description': '使用医疗包，大幅回复生命值', 
        'item_ground_model_id': 524, 
        'item_sub_type': 17, 
        'item_type': 101, 
        'mark_voice_id': (10069, ), 
        'name': '医疗包', 
        'operate_mode': 1, 
        'short_tips': '医疗道具', 
        'take_directly': True, 
    }), 
    473: TD({
        'id': 473, 
        'bag_max_stack_limit': 1, 
        'equip_id': 114, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100037, 
        'icon': 110111, 
        'icon_outline': 110113, 
        'item_description': '原装FN2000', 
        'item_ground_model_id': 3951, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'FN2000', 
        'quality': 1, 
    }), 
    474: TD({
        'id': 474, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 114, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100037, 
        'icon': 110111, 
        'icon_outline': 110113, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 3951, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'FN2000-自定义', 
        'quality': 6, 
    }), 
    475: TD({
        'id': 475, 
        'bag_max_stack_limit': 1, 
        'equip_id': 115, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100038, 
        'icon': 100038, 
        'icon_outline': 110126, 
        'item_description': '原装Galil', 
        'item_ground_model_id': 3974, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Galil', 
        'quality': 1, 
    }), 
    476: TD({
        'id': 476, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 115, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100038, 
        'icon': 100038, 
        'icon_outline': 110126, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 3974, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Galil-自定义', 
        'quality': 6, 
    }), 
    48: TD({
        'id': 48, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 10132, 
        'item_description': '解析后可提升战斗力', 
        'item_ground_model_id': 513, 
        'item_type': 109, 
        'name': '战斗模组', 
        'quality': 3, 
        'random_talent_type': (10, ), 
    }), 
    49: TD({
        'id': 49, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 10133, 
        'item_description': '可提升机动性和适应力', 
        'item_ground_model_id': 513, 
        'item_type': 109, 
        'name': '适应模组', 
        'quality': 3, 
        'random_talent_type': (11, ), 
    }), 
    5: TD({
        'id': 5, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 5, 
        'ground_max_stack_limit': 1, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 109, 
        'item_description': '使用后产生一个小型掩体', 
        'item_ground_model_id': 510, 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '掩体生成器', 
        'quality': 3, 
    }), 
    50: TD({
        'id': 50, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 10134, 
        'item_description': '可提升生存发育能力', 
        'item_ground_model_id': 513, 
        'item_type': 109, 
        'name': '生存模组', 
        'quality': 3, 
        'random_talent_type': (12, ), 
    }), 
    51: TD({
        'id': 51, 
        'bag_max_stack_limit': 1, 
        'ground_max_stack_limit': 1, 
        'icon': 10135, 
        'item_description': '可提升侦查与反侦察能力', 
        'item_ground_model_id': 513, 
        'item_type': 109, 
        'name': '侦查模组', 
        'quality': 3, 
        'random_talent_type': (13, ), 
    }), 
    513: TD({
        'id': 513, 
        'bag_max_stack_limit': 1, 
        'equip_id': 121, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100039, 
        'icon': 110166, 
        'icon_outline': 110168, 
        'item_description': '原装QBZ95', 
        'item_ground_model_id': 3974, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'QBZ95', 
        'quality': 1, 
        'skip': 'trunk_only', 
    }), 
    514: TD({
        'id': 514, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 121, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100039, 
        'icon': 110166, 
        'icon_outline': 110168, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 3974, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'QBZ95-自定义', 
        'quality': 6, 
        'skip': 'trunk_only', 
    }), 
    515: TD({
        'id': 515, 
        'bag_max_stack_limit': 1, 
        'equip_id': 122, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100043, 
        'icon': 110111, 
        'icon_outline': 110113, 
        'item_description': '原装FN2000', 
        'item_ground_model_id': 3951, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'FN2000-New', 
        'quality': 1, 
    }), 
    516: TD({
        'id': 516, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 122, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100043, 
        'icon': 110111, 
        'icon_outline': 110113, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 3951, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'FN2000-New-自定义', 
        'quality': 6, 
    }), 
    517: TD({
        'id': 517, 
        'bag_max_stack_limit': 1, 
        'equip_id': 123, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100044, 
        'icon': 110111, 
        'icon_outline': 110113, 
        'item_description': '原装SKS', 
        'item_ground_model_id': 3951, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SKS', 
        'quality': 1, 
    }), 
    518: TD({
        'id': 518, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 123, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100044, 
        'icon': 110111, 
        'icon_outline': 110113, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 3951, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'SKS-自定义', 
        'quality': 6, 
    }), 
    52: TD({
        'id': 52, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 22, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 184, 
        'item_description': '在目标点创造一个空间裂缝，造成大量爆炸伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '空间裂隙', 
    }), 
    53: TD({
        'id': 53, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 23, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 183, 
        'item_description': '扫描周围200M范围内的敌人，并标记在小地图上', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '卫星扫描', 
    }), 
    54: TD({
        'id': 54, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 24, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10163, 
        'item_description': '在正前方展开一个阻挡伤害的粒子护盾', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '粒子护盾', 
    }), 
    55: TD({
        'id': 55, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 25, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 187, 
        'item_description': '创造一个与自己一模一样的全息诱饵来干扰敌人', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '全息诱饵', 
    }), 
    56: TD({
        'id': 56, 
        'equip_id': 26, 
        'icon': 186, 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '能量涌动', 
    }), 
    57: TD({
        'id': 57, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 27, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10162, 
        'item_description': '爆炸产生电磁脉冲，对护盾和电子设备造成高额伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': 'EMP手雷', 
    }), 
    58: TD({
        'id': 58, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 28, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10161, 
        'item_description': '锁定目标位置后，可呼叫远程导弹对目标点进行打击', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '导弹打击', 
    }), 
    59: TD({
        'id': 59, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 29, 
        'icon': 10160, 
        'item_description': '大幅提高移动速度，并降低冲刺时受到的伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '暴走', 
    }), 
    60: TD({
        'id': 60, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 30, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10164, 
        'item_description': '为队友设置一个治疗无人机，可持续治疗所受伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '治疗无人机', 
    }), 
    61: TD({
        'id': 61, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 31, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 189, 
        'item_description': '放置一个弹药箱，可为队友的当前武器填装高爆子弹', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '弹药箱', 
        'operate_mode': 1, 
    }), 
    63: TD({
        'id': 63, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 32, 
        'has_cancel': True, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 190, 
        'item_description': '可以阻挡子弹的屏障', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '移动屏障', 
        'using': TD({
            5: 'recycle', 
        }), 
    }), 
    64: TD({
        'id': 64, 
        'icon': 181, 
        'item_description': '开启后装备幽灵战甲', 
        'item_sub_type': 1, 
        'item_type': 104, 
        'name': '空投-幽灵战甲', 
    }), 
    65: TD({
        'id': 65, 
        'bag_max_stack_limit': 1, 
        'equip_id': 33, 
        'icon': 485, 
        'icon_outline': 503, 
        'item_description': '匕首', 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': '匕首', 
        'quality': 4, 
    }), 
    66: TD({
        'id': 66, 
        'equip_id': 34, 
        'gun_blueprint_id': 100006, 
        'icon': 737, 
        'icon_outline': 214, 
        'item_description': '原装Origin12连发霰弹枪', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Origin-12', 
        'quality': 1, 
    }), 
    67: TD({
        'id': 67, 
        'equip_id': 34, 
        'gun_blueprint_id': 100006, 
        'icon': 737, 
        'icon_outline': 214, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 567, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Origin-12-自定义', 
        'quality': 6, 
    }), 
    68: TD({
        'id': 68, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 35, 
        'icon': 212, 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '幽灵战甲-隐身', 
    }), 
    7: TD({
        'id': 7, 
        'bag_max_stack_limit': 100, 
        'ground_max_stack_limit': 100, 
        'icon': 394, 
        'item_description': '步枪用弹药', 
        'item_ground_model_id': 541, 
        'item_type': 102, 
        'mark_voice_id': (10079, ), 
        'name': '步枪子弹', 
        'quality': 1, 
        'short_tips': '步枪弹药', 
    }), 
    70: TD({
        'id': 70, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 37, 
        'icon': 211, 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '幽灵一闪', 
        'take_directly': True, 
    }), 
    701: TD({
        'id': 701, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 401, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 1017, 
        'item_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友', 
        'item_sub_type': 22, 
        'item_type': 101, 
        'name': '通用可投掷物测试', 
        'quality': 4, 
        'skill_full_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友，每1.5秒恢复 #eed42b 8 #E生命值（护盾效率减半）；无人机可对 #eed42b 40m#E内的队友释放（不会受到阻挡），无人机可以被子弹或技能破坏', 
    }), 
    71: TD({
        'id': 71, 
        'bag_max_stack_limit': 30, 
        'ground_max_stack_limit': 30, 
        'icon': 397, 
        'item_description': '霰弹枪弹药', 
        'item_ground_model_id': 544, 
        'item_type': 102, 
        'mark_voice_id': (10080, ), 
        'name': '霰弹枪子弹', 
        'short_tips': '霰弹枪弹药', 
    }), 
    72: TD({
        'id': 72, 
        'equip_id': 38, 
        'gun_blueprint_id': 100008, 
        'icon': 738, 
        'icon_outline': 220, 
        'item_description': '原装Vector冲锋枪', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector', 
        'quality': 1, 
    }), 
    73: TD({
        'id': 73, 
        'auto_mark': 1, 
        'equip_id': 38, 
        'gun_blueprint_id': 100008, 
        'icon': 738, 
        'icon_outline': 220, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector-自定义', 
        'quality': 6, 
    }), 
    76: TD({
        'id': 76, 
        'item_description': '测试用载具', 
        'item_sub_type': 1, 
        'item_type': 201, 
        'name': '测试载具', 
        'quality': 1, 
    }), 
    77: TD({
        'id': 77, 
        'auto_mark': 1, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100010, 
        'icon': 739, 
        'icon_outline': 174, 
        'item_description': '自定义枪械', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47', 
        'quality': 6, 
    }), 
    78: TD({
        'id': 78, 
        'bag_max_stack_limit': 1, 
        'equip_id': 40, 
        'ground_max_stack_limit': 1, 
        'gun_blueprint_id': 100010, 
        'icon': 739, 
        'icon_outline': 174, 
        'item_description': '原装AK47步枪', 
        'item_ground_model_id': 569, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'AK47', 
        'quality': 1, 
    }), 
    79: TD({
        'id': 79, 
        'item_collection': TD({
            14: 60, 
            7: 200, 
            71: 40, 
            8: 200, 
        }), 
        'item_description': '子弹补给，包含各类型子弹', 
        'item_sub_type': 2, 
        'item_type': 104, 
        'name': '子弹补给', 
    }), 
    8: TD({
        'id': 8, 
        'bag_max_stack_limit': 100, 
        'ground_max_stack_limit': 100, 
        'icon': 395, 
        'item_description': '冲锋枪用弹药', 
        'item_ground_model_id': 543, 
        'item_type': 102, 
        'mark_voice_id': (10078, ), 
        'name': '冲锋枪子弹', 
        'quality': 1, 
        'short_tips': '冲锋枪弹药', 
    }), 
    80: TD({
        'id': 80, 
        'item_collection': TD({
            46: 20, 
            47: 4, 
        }), 
        'item_description': '医疗道具补给，包含各类型医疗道具', 
        'item_sub_type': 2, 
        'item_type': 104, 
        'name': '医疗补给', 
    }), 
    81: TD({
        'id': 81, 
        'equip_id': 38, 
        'gun_blueprint_id': 17, 
        'icon': 738, 
        'icon_outline': 220, 
        'item_description': '加装全息瞄准镜的Vector', 
        'item_ground_model_id': 568, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'Vector', 
        'quality': 1, 
    }), 
    82: TD({
        'id': 82, 
        'equip_id': 72, 
        'gun_blueprint_id': 18, 
        'icon': 741, 
        'icon_outline': 217, 
        'item_description': '加装3倍镜的AUG', 
        'item_ground_model_id': 571, 
        'item_sub_type': 1, 
        'item_type': 101, 
        'mark_voice_id': (48, ), 
        'name': 'KAG-6', 
        'quality': 1, 
    }), 
    83: TD({
        'id': 83, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 62, 
        'ground_max_stack_limit': 1, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 108, 
        'item_description': '爆炸后产生烟雾，遮挡视野', 
        'item_ground_model_id': 508, 
        'item_sub_type': 12, 
        'item_type': 101, 
        'name': '技能-瞬爆烟雾弹', 
        'quality': 1, 
    }), 
    84: TD({
        'id': 84, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 41, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10163, 
        'item_description': '放置一个矩形粒子护盾，阻挡前方伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '粒子护盾', 
    }), 
    85: TD({
        'id': 85, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 42, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10163, 
        'item_description': '放置一个半球形粒子护盾，阻挡伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '粒子护盾-球型', 
    }), 
    86: TD({
        'id': 86, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 43, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10163, 
        'item_description': '放置一个单向粒子护盾，子弹传过后会增加伤害', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '粒子护盾-充能', 
    }), 
    87: TD({
        'id': 87, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 44, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 183, 
        'item_description': '放置一个卫星扫描装置，进行定点扫描', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '卫星扫描', 
    }), 
    88: TD({
        'id': 88, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 45, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 183, 
        'item_description': '卫星扫描效果跟随玩家移动，但扫描范围变小，持续时间变短', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '卫星扫描-追踪', 
    }), 
    89: TD({
        'id': 89, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 46, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 183, 
        'item_description': '卫星扫描范围变大，但扫描间隔和冷却时间变长', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '卫星扫描-全局', 
    }), 
    9: TD({
        'id': 9, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 7, 
        'ground_max_stack_limit': 2, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 113, 
        'icon_outline': 11461, 
        'ig_voice_id': (1084, ), 
        'item_description': '爆炸后产生闪光，致盲敌人', 
        'item_ground_model_id': 508, 
        'item_sub_type': 12, 
        'item_type': 101, 
        'mark_voice_id': (10071, ), 
        'name': '闪光弹', 
        'quality': 1, 
        'short_tips': '投掷物', 
    }), 
    90: TD({
        'id': 90, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 47, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10160, 
        'item_description': '投掷一个昆虫探测器，对目标点周围进行小范围扫描，被扫描的敌人将进入暴露状态', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '昆虫监视器', 
    }), 
    91: TD({
        'id': 91, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 48, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10160, 
        'item_description': '装置变为隐身陷阱，敌人靠近时触发爆炸，造成伤害并持续暴露敌人位置', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '昆虫监视器-陷阱', 
    }), 
    92: TD({
        'id': 92, 
        'bag_max_stack_limit': 1, 
        'empty': TD({
            1: 'hold', 
            'cache': (1, ), 
        }), 
        'equip_id': 49, 
        'hold': TD({
            1: 'using', 
        }), 
        'icon': 10160, 
        'item_description': '以自身为范围进行一次快速扫描，被扫描的敌人将进入暴露状态', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '昆虫监视器-突破', 
    }), 
    93: TD({
        'id': 93, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 50, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10162, 
        'item_description': '丢出一个EMP手雷，爆炸产生电磁脉冲，对护盾和装置类道具产生大量伤害，效果可穿墙', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': 'EMP手雷', 
    }), 
    94: TD({
        'id': 94, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 51, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10162, 
        'item_description': '增加EMP手雷爆炸范围，但伤害变低', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': 'EMP手雷-大号', 
    }), 
    95: TD({
        'id': 95, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 52, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10162, 
        'item_description': '爆炸还会产生减速效果，但伤害变低', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': 'EMP手雷-震荡', 
    }), 
    96: TD({
        'id': 96, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 53, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10161, 
        'item_description': '呼叫远程导弹，随机多次打击目标范围', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '导弹打击', 
    }), 
    97: TD({
        'id': 97, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 54, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10161, 
        'item_description': '导弹爆炸伤害降低，爆炸后产生范围燃烧效果', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '导弹打击-燃烧', 
    }), 
    98: TD({
        'id': 98, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 55, 
        'has_cancel': True, 
        'hold': TD({
            3: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10161, 
        'item_description': '导弹爆炸伤害降低，爆炸后产生大量烟雾', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '导弹打击-烟雾', 
    }), 
    99: TD({
        'id': 99, 
        'bag_max_stack_limit': 2, 
        'empty': TD({
            1: 'hold', 
        }), 
        'equip_id': 56, 
        'hold': TD({
            1: 'recycle', 
            4: 'using', 
            5: 'recycle', 
        }), 
        'icon': 10164, 
        'item_description': '布置一个毒气陷阱，敌人靠近时爆炸，可存储2个', 
        'item_sub_type': 18, 
        'item_type': 101, 
        'name': '毒气陷阱', 
    }), 
}
