# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 2-装备表.xlsx, sheetname:装备类型
from taggeddict import taggeddict as TD

data = {
    1: TD({
        'id': 1,
        'name': 'M4A1',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 1,
        'icon_id': 341,
        'small_icon_id': 436,
        'big_icon_id': 512,
        'shadow_icon_id': 10849,
        'mod_id': 38,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/m4.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'm4a1',
        'gun_media_bnk': 'weap_m4a1',
        'single_shot_sound_id': 166,
        'kill_tip_icon': 358,
        'gunsmith_item_desc': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 1,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    2: TD({
        'id': 2,
        'name': 'MP5',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 2,
        'icon_id': 1031,
        'small_icon_id': 1030,
        'big_icon_id': 1032,
        'shadow_icon_id': 12074,
        'mod_id': 329,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/m4.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'mp5',
        'gun_media_bnk': 'weap_mp5',
        'single_shot_sound_id': 785,
        'kill_tip_icon': 12075,
        'gunsmith_item_desc': '具备卓越准确性和可靠性的冲锋枪，在近距离作战中有着优秀的表现',
        'move_action_type': 2,
        'tps_move_action_type': 2,
        'anim_data_id': 2,
        'anim_speed_data_id': 2,
        'little_helper': (21, ),
    }), 
    3: TD({
        'id': 3,
        'name': '破片手雷',
        'equip_type': 11,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 536,
        'icon_id': 389,
        'mod_id': 3,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 2,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 11425,
        'gunsmith_item_desc': '爆炸后产生范围伤害',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
        'warehouse_show_rotation': (0, 180, 0, ),
    }), 
    4: TD({
        'id': 4,
        'name': '烟雾弹',
        'equip_type': 12,
        'equip_case': 'LeftHand',
        'throwable': True,
        'model_id': 538,
        'icon_id': 108,
        'mod_id': 4,
        'default_attach': ('HP_Hand', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/tactical_grenade.graph',
        'tps_graph': 'TPS/Spell/smoke_grenade_toss.graph',
        'spell_id': 3,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 11426,
        'gunsmith_item_desc': '爆炸后产生烟雾，遮挡视野',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
        'warehouse_show_rotation': (0, -160, 0, ),
    }), 
    5: TD({
        'id': 5,
        'name': '掩体生成器',
        'equip_type': 18,
        'equip_case': 'None',
        'take_check': 'TakeSkillCheck',
        'use_check': 'UsingPlaceEntityCheck',
        'icon_id': 57,
        'mod_id': 5,
        'spell_id': 4,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    7: TD({
        'id': 7,
        'name': '闪光弹',
        'equip_type': 12,
        'equip_case': 'LeftHand',
        'throwable': True,
        'model_id': 538,
        'icon_id': 113,
        'mod_id': 9,
        'default_attach': ('HP_Hand', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/tactical_grenade.graph',
        'tps_graph': 'TPS/Spell/smoke_grenade_toss.graph',
        'spell_id': 7,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 11424,
        'gunsmith_item_desc': '爆炸后产生闪光，让敌人短时间看不清东西',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
        'warehouse_show_rotation': (0, -154, 0, ),
    }), 
    8: TD({
        'id': 8,
        'name': '快照弹',
        'equip_type': 12,
        'equip_case': 'LeftHand',
        'throwable': True,
        'model_id': 538,
        'icon_id': 107,
        'mod_id': 10,
        'default_attach': ('HP_Hand', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/tactical_grenade.graph',
        'spell_id': 8,
        'gun_media_bnk': 'tactical_weapon',
        'gunsmith_item_desc': '使用后会扫描周围，暴露敌人所在位置',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    10: TD({
        'id': 10,
        'name': 'UAV',
        'equip_type': 14,
        'mod_id': 15,
        'spell_id': 19,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    11: TD({
        'id': 11,
        'name': '自救针',
        'equip_type': 16,
        'equip_case': 'Item',
        'take_check': 'TakeEmergencyNeedleCheck',
        'model_id': 546,
        'mod_id': 16,
        'default_attach': ('HP_Hand', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/injection.graph',
        'spell_id': 12,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    12: TD({
        'id': 12,
        'name': '燃烧弹',
        'equip_type': 11,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 537,
        'icon_id': 393,
        'mod_id': 20,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 10,
        'kill_tip_icon': 11423,
        'gunsmith_item_desc': '爆炸后生成一片燃烧区域，经过会受到持续伤害',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    13: TD({
        'id': 13,
        'name': '主动防御装置',
        'equip_type': 18,
        'equip_case': 'None',
        'take_check': 'TakeSkillCheck',
        'use_check': 'UsingPlaceEntityCheck',
        'model_id': 31,
        'icon_id': 57,
        'mod_id': 24,
        'spell_id': 13,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    14: TD({
        'id': 14,
        'name': '心跳扫描器',
        'equip_type': 13,
        'equip_case': 'LeftHand',
        'take_check': 'TakeSkillCheck',
        'model_id': 35,
        'mod_id': 37,
        'default_attach': ('HP_Hand', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/heartbeat_detector.graph',
        'spell_id': 17,
    }), 
    15: TD({
        'id': 15,
        'name': 'Glock',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 4,
        'icon_id': 342,
        'small_icon_id': 434,
        'big_icon_id': 510,
        'shadow_icon_id': 10853,
        'mod_id': 41,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_gun', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/Glock_fps_mp.graph',
        'tps_graph': 'TPS/Spell/glock.graph',
        'spell_id': 1,
        'vice_spell_id': 55,
        'need_backup_charger': True,
        'gun_sound_type': 'glock',
        'gun_media_bnk': 'weap_glock',
        'single_shot_sound_id': 165,
        'kill_tip_icon': 357,
        'kill_tip_icon_2': 798,
        'gunsmith_item_desc': '半自动手枪，可靠的副武器',
        'move_action_type': 4,
        'tps_move_action_type': 4,
        'anim_data_id': 4,
        'anim_speed_data_id': 5,
        'little_helper': (21, ),
    }), 
    16: TD({
        'id': 16,
        'name': '拳头',
        'equip_type': 2,
        'equip_case': 'Melee',
        'can_in_backpack': True,
        'icon_id': 149,
        'mod_id': 43,
        'hand_graph': 'FPS/Hand/fists_fps_mp.graph',
        'tps_graph': 'TPS/Spell/fists_attack.graph',
        'spell_id': 20,
        'gun_sound_type': 'melee',
        'kill_tip_icon': 149,
        'equip_movement_speed': 1.15,
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 5,
        'anim_speed_data_id': 1,
        'little_helper': (5, ),
    }), 
    18: TD({
        'id': 18,
        'name': '集束手雷',
        'equip_type': 11,
        'unable_gunsmith': True,
        'model_id': 11,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 21,
        'gun_media_bnk': 'tactical_weapon',
        'move_action_type': 0,
        'tps_move_action_type': 0,
    }), 
    19: TD({
        'id': 19,
        'name': '集束手雷-分裂',
        'equip_type': 11,
        'unable_gunsmith': True,
        'model_id': 11,
        'spell_id': 22,
        'gun_media_bnk': 'tactical_weapon',
        'move_action_type': 0,
        'tps_move_action_type': 0,
    }), 
    20: TD({
        'id': 20,
        'name': '绷带',
        'equip_type': 17,
        'equip_case': 'Item',
        'state': 60,
        'take_check': 'TakeBandageCheck',
        'unable_gunsmith': True,
        'model_id': 40,
        'icon_id': 179,
        'default_attach': ('bandages01', 'WR', ),
        'hand_tach_point': ('HP_root', 'bandages01', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/bandage.graph',
        'tps_graph': 'TPS/Locomotion/tps_medical_item.graph',
        'spell_id': 23,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    21: TD({
        'id': 21,
        'name': '医疗包',
        'equip_type': 17,
        'equip_case': 'Item',
        'state': 60,
        'take_check': 'TakeBandageCheck',
        'unable_gunsmith': True,
        'model_id': 546,
        'icon_id': 392,
        'default_attach': ('HP_Hand', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/medical_box.graph',
        'tps_graph': 'TPS/Locomotion/tps_medical_item.graph',
        'spell_id': 24,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    22: TD({
        'id': 22,
        'name': '空间裂缝（技能）',
        'equip_type': 13,
        'equip_case': 'Item',
        'icon_id': 112,
        'hand_graph': 'FPS/Hand/skill2.graph',
        'spell_id': 25,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    23: TD({
        'id': 23,
        'name': '卫星扫描',
        'equip_type': 13,
        'equip_case': 'Item',
        'using_ban_move': True,
        'model_id': 42,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill4.graph',
        'spell_id': 27,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    24: TD({
        'id': 24,
        'name': '粒子护盾',
        'equip_type': 13,
        'equip_case': 'Item',
        'throwable': True,
        'model_id': 47,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill3.graph',
        'tps_graph': 'TPS/Spell/skill_test1.graph',
        'spell_id': 28,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    25: TD({
        'id': 25,
        'name': '全息诱饵',
        'equip_type': 13,
        'equip_case': 'Item',
        'icon_id': 112,
        'hand_graph': 'FPS/Hand/skill3.graph',
        'tps_graph': 'TPS/Spell/skill_test1.graph',
        'spell_id': 29,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    26: TD({
        'id': 26,
        'name': '能量涌动',
        'equip_type': 13,
        'equip_case': 'LeftHand',
        'icon_id': 112,
        'spell_id': 30,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    27: TD({
        'id': 27,
        'name': 'EMP手雷',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 517,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 32,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    28: TD({
        'id': 28,
        'name': '导弹打击',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 522,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill7_missile.graph',
        'spell_id': 33,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    29: TD({
        'id': 29,
        'name': '暴走',
        'equip_type': 13,
        'equip_case': 'Item',
        'icon_id': 112,
        'hand_graph': 'FPS/Hand/skill8_baozou.graph',
        'spell_id': 34,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    30: TD({
        'id': 30,
        'name': '治疗无人机',
        'equip_type': 13,
        'equip_case': 'Item',
        'use_check': 'UsingCureUavCheck',
        'model_id': 530,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill6_drone.graph',
        'spell_id': 35,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    31: TD({
        'id': 31,
        'name': '弹药箱',
        'equip_type': 18,
        'equip_case': 'Item',
        'using_ban_move': True,
        'model_id': 514,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'WL', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill9_supplybox.graph',
        'tps_graph': 'TPS/Spell/skill_test1.graph',
        'spell_id': 36,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    32: TD({
        'id': 32,
        'name': '移动屏障',
        'equip_type': 13,
        'equip_case': 'None',
        'icon_id': 112,
        'spell_id': 37,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    33: TD({
        'id': 33,
        'name': '匕首',
        'equip_type': 2,
        'equip_case': 'Melee',
        'model_id': 1469,
        'icon_id': 485,
        'small_icon_id': 1035,
        'big_icon_id': 507,
        'mod_id': 65,
        'default_attach': ('HP_Root', 'WR', ),
        'hand_tach_point': ('HP_Root', 'HP_Root', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/ghostknife_fps_mp.graph',
        'tps_graph': 'TPS/Spell/ghostknife_attack.graph',
        'spell_id': 20,
        'gun_sound_type': 'melee',
        'gun_media_bnk': 'dagger_media',
        'kill_tip_icon': 491,
        'equip_movement_speed': 1.15,
        'move_action_type': 5,
        'tps_move_action_type': 5,
        'anim_data_id': 25,
        'anim_speed_data_id': 1,
        'melee_weapon_id': 1,
        'screenshot_rotate': (78, 179.5, -24.3, ),
        'little_helper': (5, ),
        'warehouse_show_rotation': (-20.9, 18.4, 5.3, ),
        'warehouse_show_scale': 1.0,
        'gacha_show_scale': 1.0,
    }), 
    34: TD({
        'id': 34,
        'name': 'Origin-12',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 6,
        'icon_id': 343,
        'small_icon_id': 440,
        'big_icon_id': 517,
        'shadow_icon_id': 10846,
        'mod_id': 67,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/origin12.graph',
        'spell_id': 39,
        'need_backup_charger': True,
        'gun_sound_type': 'origin12',
        'gun_media_bnk': 'origin12_media',
        'single_shot_sound_id': 206,
        'kill_tip_icon': 214,
        'gunsmith_item_desc': '高射速连发霰弹枪，近距离杀伤力极强',
        'move_action_type': 6,
        'tps_move_action_type': 6,
        'anim_data_id': 7,
        'anim_speed_data_id': 10,
        'little_helper': (21, ),
    }), 
    35: TD({
        'id': 35,
        'name': '幽灵战甲',
        'equip_type': 13,
        'icon_id': 112,
        'spell_id': 38,
    }), 
    37: TD({
        'id': 37,
        'name': '幽灵一闪',
        'equip_type': 13,
        'equip_case': 'Item',
        'icon_id': 112,
        'hand_graph': 'FPS/Hand/skill5_yishan.graph',
        'tps_graph': 'TPS/Spell/tps_skill_yishan.graph',
        'spell_id': 40,
    }), 
    38: TD({
        'id': 38,
        'name': 'Vector',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 8,
        'icon_id': 344,
        'small_icon_id': 443,
        'big_icon_id': 521,
        'shadow_icon_id': 10856,
        'mod_id': 73,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/vector.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'vector',
        'gun_media_bnk': 'vector_media',
        'single_shot_sound_id': 171,
        'kill_tip_icon': 366,
        'gunsmith_item_desc': '射速极快的冲锋枪，在枪手的精准操控下非常致命',
        'move_action_type': 8,
        'tps_move_action_type': 8,
        'anim_data_id': 9,
        'anim_speed_data_id': 2,
        'lobby_gun_offset': (0, 0.018, 0, ),
        'little_helper': (21, ),
    }), 
    40: TD({
        'id': 40,
        'name': 'AK47',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 10,
        'icon_id': 345,
        'small_icon_id': 431,
        'big_icon_id': 505,
        'shadow_icon_id': 10855,
        'mod_id': 77,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/m4.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'ak47',
        'gun_media_bnk': 'weap_ak47',
        'single_shot_sound_id': 162,
        'kill_tip_icon': 354,
        'gunsmith_item_desc': '耐用性极强的全自动步枪，射速慢但子弹威力大，适合中远距离',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 6,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    41: TD({
        'id': 41,
        'name': '粒子护盾',
        'equip_type': 13,
        'equip_case': 'Item',
        'throwable': True,
        'model_id': 47,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill3.graph',
        'tps_graph': 'TPS/Spell/skill_test1.graph',
        'spell_id': 100,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    42: TD({
        'id': 42,
        'name': '粒子护盾-球型',
        'equip_type': 13,
        'equip_case': 'Item',
        'throwable': True,
        'model_id': 47,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill3.graph',
        'tps_graph': 'TPS/Spell/skill_test1.graph',
        'spell_id': 101,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    43: TD({
        'id': 43,
        'name': '粒子护盾-充能',
        'equip_type': 13,
        'equip_case': 'Item',
        'throwable': True,
        'model_id': 47,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill3.graph',
        'tps_graph': 'TPS/Spell/skill_test1.graph',
        'spell_id': 102,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    44: TD({
        'id': 44,
        'name': '卫星扫描',
        'equip_type': 13,
        'equip_case': 'Item',
        'using_ban_move': True,
        'model_id': 42,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill4.graph',
        'tps_graph': 'TPS/Spell/skill_satellite_scan.graph',
        'spell_id': 103,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    45: TD({
        'id': 45,
        'name': '卫星扫描-追踪',
        'equip_type': 13,
        'equip_case': 'Item',
        'using_ban_move': True,
        'model_id': 42,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill4.graph',
        'spell_id': 104,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    46: TD({
        'id': 46,
        'name': '卫星扫描-全局',
        'equip_type': 13,
        'equip_case': 'Item',
        'using_ban_move': True,
        'model_id': 42,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill4.graph',
        'tps_graph': 'TPS/Spell/skill_satellite_scan.graph',
        'spell_id': 105,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    47: TD({
        'id': 47,
        'name': '昆虫监视器',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 11,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 106,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    48: TD({
        'id': 48,
        'name': '昆虫监视器-陷阱',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 11,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 107,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    49: TD({
        'id': 49,
        'name': '昆虫监视器-突破',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 11,
        'hand_graph': 'FPS/Hand/skill8_baozou.graph',
        'spell_id': 108,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    50: TD({
        'id': 50,
        'name': 'EMP手雷',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 517,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 109,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    51: TD({
        'id': 51,
        'name': 'EMP手雷-大号',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 517,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 110,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    52: TD({
        'id': 52,
        'name': 'EMP手雷-震荡',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 517,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 111,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    53: TD({
        'id': 53,
        'name': '导弹打击',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 522,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill7_missile.graph',
        'tps_graph': 'TPS/Spell/skill_missile.graph',
        'spell_id': 112,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    54: TD({
        'id': 54,
        'name': '导弹打击-燃烧',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 522,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill7_missile.graph',
        'tps_graph': 'TPS/Spell/skill_missile.graph',
        'spell_id': 113,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    55: TD({
        'id': 55,
        'name': '导弹打击-烟雾',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 522,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill7_missile.graph',
        'tps_graph': 'TPS/Spell/skill_missile.graph',
        'spell_id': 114,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    56: TD({
        'id': 56,
        'name': '毒气陷阱',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 517,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 115,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    57: TD({
        'id': 57,
        'name': '毒气陷阱-剧毒',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 517,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 116,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    58: TD({
        'id': 58,
        'name': '毒气陷阱-防御',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 517,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 117,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    59: TD({
        'id': 59,
        'name': '治疗无人机',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 530,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill7_drone_stay.graph',
        'tps_graph': 'TPS/Spell/skill_use_medical_drone.graph',
        'spell_id': 118,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    60: TD({
        'id': 60,
        'name': '治疗无人机-速度',
        'equip_type': 13,
        'equip_case': 'Item',
        'use_check': 'UsingCureUavCheck',
        'model_id': 530,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill6_drone.graph',
        'tps_graph': 'TPS/Spell/skill_medical_drone.graph',
        'spell_id': 119,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    61: TD({
        'id': 61,
        'name': '治疗无人机-火力',
        'equip_type': 13,
        'equip_case': 'Item',
        'use_check': 'UsingCureUavCheck',
        'model_id': 530,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill6_drone.graph',
        'tps_graph': 'TPS/Spell/skill_medical_drone.graph',
        'spell_id': 120,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    62: TD({
        'id': 62,
        'name': '瞬爆烟雾弹',
        'equip_type': 12,
        'equip_case': 'LeftHand',
        'unable_gunsmith': True,
        'throwable': True,
        'model_id': 12,
        'icon_id': 108,
        'mod_id': 83,
        'default_attach': ('HP_Hand', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/tactical_grenade.graph',
        'spell_id': 42,
        'gun_media_bnk': 'tactical_weapon',
        'gunsmith_item_desc': '爆炸后产生烟雾，遮挡视野',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    65: TD({
        'id': 65,
        'name': '深海领域',
        'equip_type': 13,
        'equip_case': 'Item',
        'hand_graph': 'FPS/Hand/skill8_baozou.graph',
        'tps_graph': 'TPS/Spell/skill_smoke_zone.graph',
        'spell_id': 121,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    66: TD({
        'id': 66,
        'name': '深海领域-极寒',
        'equip_type': 13,
        'equip_case': 'Item',
        'hand_graph': 'FPS/Hand/skill8_baozou.graph',
        'tps_graph': 'TPS/Spell/skill_smoke_zone.graph',
        'spell_id': 122,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    67: TD({
        'id': 67,
        'name': '深海领域-腐蚀',
        'equip_type': 13,
        'equip_case': 'Item',
        'hand_graph': 'FPS/Hand/skill8_baozou.graph',
        'tps_graph': 'TPS/Spell/skill_smoke_zone.graph',
        'spell_id': 123,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    68: TD({
        'id': 68,
        'name': '赛博忍术-瞬身',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'use_check': 'UsingNinjiaTransferCheck',
        'throwable': True,
        'model_id': 517,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 124,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    69: TD({
        'id': 69,
        'name': '赛博忍术-回溯1阶段',
        'equip_type': 13,
        'equip_case': 'Item',
        'hand_graph': 'FPS/Hand/skill3.graph',
        'tps_graph': 'TPS/Spell/skill_test1.graph',
        'spell_id': 125,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    70: TD({
        'id': 70,
        'name': '赛博忍术-回溯2阶段',
        'equip_type': 13,
        'equip_case': 'Item',
        'hand_graph': 'FPS/Hand/skill8_baozou.graph',
        'tps_graph': 'TPS/Spell/skill_smoke_zone.graph',
        'spell_id': 126,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    71: TD({
        'id': 71,
        'name': 'AWM',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 13,
        'icon_id': 346,
        'small_icon_id': 435,
        'big_icon_id': 520,
        'shadow_icon_id': 10842,
        'mod_id': 118,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/SR001_fps_mp.graph',
        'tps_graph': 'TPS/Spell/sr001.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'kala',
        'gun_media_bnk': 'kala_media',
        'single_shot_sound_id': 193,
        'have_bolt': 1,
        'kill_tip_icon': 365,
        'gunsmith_item_desc': '重型狙击步枪，机动性差，但子弹威力大，命中头部一发致命',
        'move_action_type': 3,
        'tps_move_action_type': 3,
        'anim_data_id': 14,
        'anim_speed_data_id': 3,
        'little_helper': (20, ),
    }), 
    72: TD({
        'id': 72,
        'name': 'KAG-6',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 14,
        'icon_id': 347,
        'small_icon_id': 432,
        'big_icon_id': 506,
        'shadow_icon_id': 10854,
        'mod_id': 120,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/AR01_fps_mp.graph',
        'tps_graph': 'TPS/Spell/ar01.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'kag6',
        'gun_media_bnk': 'kag6_media',
        'single_shot_sound_id': 164,
        'kill_tip_icon': 355,
        'gunsmith_item_desc': '全自动步枪，射速快、易控制，非常适合远距离击杀敌人',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 16,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    73: TD({
        'id': 73,
        'name': '枪匕首',
        'equip_type': 3,
        'equip_case': 'GunMelee',
        'gun_id': 19,
        'model_id': 1417,
        'icon_id': 193,
        'mod_id': 154,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/assassinate_fps_mp.graph',
        'tps_graph': 'TPS/Spell/glock.graph',
        'vice_tps_graph': 'TPS/Spell/assasine_attack.graph',
        'spell_id': 1,
        'vice_spell_id': 45,
        'need_backup_charger': True,
        'gun_sound_type': 'm9a3',
        'gun_media_bnk': 'm9a3_media',
        'kill_tip_icon': 360,
        'move_action_type': 4,
        'tps_move_action_type': 4,
        'anim_data_id': 19,
        'anim_speed_data_id': 2,
        'little_helper': (21, 5, ),
    }), 
    74: TD({
        'id': 74,
        'name': 'Deagle',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 15,
        'icon_id': 348,
        'small_icon_id': 433,
        'big_icon_id': 508,
        'shadow_icon_id': 10852,
        'mod_id': 143,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/DE_fps_mp.graph',
        'tps_graph': 'TPS/Spell/de.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'eagle',
        'gun_media_bnk': 'eagle_media',
        'single_shot_sound_id': 161,
        'kill_tip_icon': 356,
        'gunsmith_item_desc': '发射.50子弹的重型手枪，子弹威力大',
        'move_action_type': 4,
        'tps_move_action_type': 4,
        'anim_data_id': 21,
        'anim_speed_data_id': 12,
        'little_helper': (21, ),
    }), 
    75: TD({
        'id': 75,
        'name': 'M82',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 16,
        'icon_id': 349,
        'small_icon_id': 442,
        'big_icon_id': 519,
        'shadow_icon_id': 10843,
        'mod_id': 148,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M82_fps_mp.graph',
        'tps_graph': 'TPS/Spell/sr001.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'kala',
        'gun_media_bnk': 'kala_media',
        'single_shot_sound_id': 170,
        'kill_tip_icon': 364,
        'gunsmith_item_desc': '射速较慢的冲锋枪，但子弹威力衰减弱，适用于多种交战场合',
        'move_action_type': 2,
        'tps_move_action_type': 3,
        'anim_data_id': 17,
        'anim_speed_data_id': 3,
        'little_helper': (21, ),
    }), 
    76: TD({
        'id': 76,
        'name': 'PP19',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 17,
        'icon_id': 350,
        'small_icon_id': 438,
        'big_icon_id': 515,
        'shadow_icon_id': 10847,
        'mod_id': 150,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/m4.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'inp9',
        'gun_media_bnk': 'inp9_media',
        'single_shot_sound_id': 167,
        'kill_tip_icon': 362,
        'gunsmith_item_desc': '射速中等的冲锋枪，性能可靠，适用于中近距离作战',
        'move_action_type': 2,
        'tps_move_action_type': 2,
        'anim_data_id': 18,
        'anim_speed_data_id': 2,
        'little_helper': (21, ),
    }), 
    77: TD({
        'id': 77,
        'name': 'M700',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 18,
        'icon_id': 351,
        'small_icon_id': 437,
        'big_icon_id': 514,
        'shadow_icon_id': 10850,
        'mod_id': 152,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M700_fps_mp.graph',
        'tps_graph': 'TPS/Spell/kar98.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'm700',
        'gun_media_bnk': 'weap_m700',
        'single_shot_sound_id': 205,
        'have_bolt': 1,
        'kill_tip_icon': 359,
        'gunsmith_item_desc': '轻型狙击步枪，机动性强，命中头部一发致命',
        'move_action_type': 3,
        'tps_move_action_type': 3,
        'anim_data_id': 22,
        'anim_speed_data_id': 3,
        'little_helper': (20, ),
    }), 
    78: TD({
        'id': 78,
        'name': 'M9A3',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'gun_id': 19,
        'icon_id': 178,
        'mod_id': 154,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/assassinate_fps_mp.graph',
        'tps_graph': 'TPS/Spell/glock.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'm9a3',
        'gun_media_bnk': 'm9a3_media',
        'kill_tip_icon': 360,
        'move_action_type': 4,
        'tps_move_action_type': 4,
        'anim_data_id': 19,
        'anim_speed_data_id': 1,
    }), 
    79: TD({
        'id': 79,
        'name': 'M870',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 20,
        'icon_id': 353,
        'small_icon_id': 439,
        'big_icon_id': 516,
        'shadow_icon_id': 10848,
        'mod_id': 156,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/MP155_fps_mp.graph',
        'tps_graph': 'TPS/Spell/mp155.graph',
        'spell_id': 39,
        'need_backup_charger': True,
        'gun_sound_type': 'mp155',
        'gun_media_bnk': 'weap_m870',
        'single_shot_sound_id': 163,
        'have_bolt': 1,
        'kill_tip_icon': 361,
        'gunsmith_item_desc': '半自动智能霰弹枪，经过合适的改装也可胜任中近距离的作战情景',
        'move_action_type': 3,
        'tps_move_action_type': 3,
        'anim_data_id': 20,
        'anim_speed_data_id': 10,
        'little_helper': (21, ),
    }), 
    80: TD({
        'id': 80,
        'name': 'xdroid',
        'equip_type': 19,
        'equip_case': 'Item',
        'state': 40,
        'take_check': 'TakeXdroidCheck',
        'model_id': 547,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/xdroid.graph',
        'tps_graph': 'TPS/Locomotion/tps_xdorid.graph',
        'spell_id': 46,
        'anim_data_id': 5,
    }), 
    81: TD({
        'id': 81,
        'name': '粘雷',
        'equip_type': 11,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 539,
        'icon_id': 539,
        'mod_id': 218,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 47,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 11422,
        'gunsmith_item_desc': '爆炸后产生范围伤害',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
        'warehouse_show_rotation': (0, -176, 0, ),
    }), 
    82: TD({
        'id': 82,
        'name': 'RPG-7',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 21,
        'model_id': 1441,
        'icon_id': 484,
        'small_icon_id': 441,
        'big_icon_id': 518,
        'shadow_icon_id': 10845,
        'mod_id': 196,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/RPG7_fps_mp.graph',
        'tps_graph': 'TPS/Spell/rpg7.graph',
        'spell_id': 48,
        'gun_sound_type': 'rpg',
        'gun_media_bnk': 'rpg_media',
        'single_shot_sound_id': 163,
        'kill_tip_icon': 494,
        'gunsmith_item_desc': '单兵肩托式反火箭发射器，主要用于防空和反坦克作战',
        'move_action_type': 3,
        'tps_move_action_type': 3,
        'anim_data_id': 23,
        'anim_speed_data_id': 4,
        'little_helper': (21, ),
    }), 
    83: TD({
        'id': 83,
        'name': '复合弓',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 22,
        'model_id': 1468,
        'icon_id': 487,
        'small_icon_id': 489,
        'big_icon_id': 511,
        'shadow_icon_id': 10851,
        'mod_id': 199,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 1, ),
        'hand_graph': 'FPS/Hand/CombatBow_fps_mp.graph',
        'tps_graph': 'TPS/Spell/combatbow.graph',
        'spell_id': 50,
        'gun_sound_type': 'composite_bow',
        'gun_media_bnk': 'composite_bow_media',
        'single_shot_sound_id': 163,
        'kill_tip_icon': 492,
        'gunsmith_item_desc': '现代复合弓，在帮助使用者省力的同时具有很高的精准度',
        'move_action_type': 3,
        'tps_move_action_type': 3,
        'anim_data_id': 24,
        'anim_speed_data_id': 4,
        'screenshot_rotate': (43, 73.5, 79.6, ),
        'little_helper': (21, ),
        'warehouse_show_rotation': (50.95, 92.85, 65, ),
        'gunsmith_rotation': (35.3, 24.3, 75.6, ),
    }), 
    84: TD({
        'id': 84,
        'name': '单手斧',
        'equip_type': 2,
        'equip_case': 'Melee',
        'model_id': 1470,
        'icon_id': 486,
        'small_icon_id': 1036,
        'big_icon_id': 509,
        'mod_id': 219,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/axe_fps_mp.graph',
        'tps_graph': 'TPS/Spell/axe_attack.graph',
        'spell_id': 20,
        'gun_media_bnk': 'handaxe_media',
        'kill_tip_icon': 493,
        'equip_movement_speed': 1.15,
        'move_action_type': 5,
        'tps_move_action_type': 5,
        'anim_data_id': 26,
        'anim_speed_data_id': 1,
        'melee_weapon_id': 2,
        'screenshot_rotate': (90, 0, 0, ),
        'little_helper': (5, ),
        'warehouse_show_rotation': (33.8, 55.98, 9.635, ),
        'gacha_show_scale': 1.0,
    }), 
    85: TD({
        'id': 85,
        'name': '吸入器',
        'equip_type': 17,
        'equip_case': 'Item',
        'state': 46,
        'unable_gunsmith': True,
        'model_id': 91,
        'icon_id': 57,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/inhaler.graph',
        'tps_graph': 'TPS/Locomotion/tps_medical_item.graph',
        'spell_id': 51,
        'gun_media_bnk': 'handaxe_media',
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    86: TD({
        'id': 86,
        'name': '武士刀',
        'equip_type': 2,
        'equip_case': 'Melee',
        'model_id': 1516,
        'icon_id': 549,
        'small_icon_id': 1033,
        'big_icon_id': 549,
        'mod_id': 221,
        'scabbard_id': 1523,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/katana_fps_mp.graph',
        'tps_graph': 'TPS/Spell/katana_attack.graph',
        'spell_id': 20,
        'gun_media_bnk': 'handaxe_media',
        'kill_tip_icon': 561,
        'equip_movement_speed': 1.15,
        'move_action_type': 6,
        'tps_move_action_type': 6,
        'anim_data_id': 27,
        'anim_speed_data_id': 1,
        'melee_weapon_id': 3,
        'screenshot_rotate': (90, 0, 0, ),
        'little_helper': (5, ),
        'warehouse_show_rotation': (64.65, -154.95, 151.2, ),
    }), 
    87: TD({
        'id': 87,
        'skip': 'trunk_only',
        'name': '棒球棍',
        'equip_type': 2,
        'equip_case': 'Melee',
        'model_id': 222,
        'icon_id': 486,
        'small_icon_id': 489,
        'mod_id': 222,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/katana_fps_mp.graph',
        'tps_graph': 'TPS/Spell/katana_attack.graph',
        'spell_id': 20,
        'gun_media_bnk': 'handaxe_media',
        'kill_tip_icon': 493,
        'equip_movement_speed': 1.15,
        'move_action_type': 5,
        'tps_move_action_type': 5,
        'anim_data_id': 27,
        'anim_speed_data_id': 1,
        'melee_weapon_id': 3,
        'screenshot_rotate': (90, 0, 0, ),
        'little_helper': (10, ),
    }), 
    88: TD({
        'id': 88,
        'name': 'SCAR',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 23,
        'icon_id': 603,
        'small_icon_id': 604,
        'big_icon_id': 612,
        'shadow_icon_id': 10844,
        'mod_id': 224,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/scar.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'scar',
        'gun_media_bnk': 'scar_media',
        'single_shot_sound_id': 166,
        'kill_tip_icon': 610,
        'gunsmith_item_desc': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 28,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    89: TD({
        'id': 89,
        'name': '棒球棍',
        'equip_type': 2,
        'equip_case': 'Melee',
        'model_id': 1653,
        'icon_id': 627,
        'small_icon_id': 1034,
        'big_icon_id': 629,
        'mod_id': 251,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_root', ),
        'default_attach_tps': (1, 0, ),
        'melee_default_ik': (True, ),
        'hand_graph': 'FPS/Hand/BAT_fps_mp.graph',
        'tps_graph': 'TPS/Spell/bat_attack.graph',
        'spell_id': 20,
        'gun_sound_type': 'baseball_bat',
        'gun_media_bnk': 'baseball_bat_media',
        'kill_tip_icon': 631,
        'equip_movement_speed': 1.15,
        'move_action_type': 6,
        'tps_move_action_type': 6,
        'anim_data_id': 29,
        'anim_speed_data_id': 1,
        'is_tps_bonefilter_arm': True,
        'melee_weapon_id': 4,
        'screenshot_rotate': (90, 0, 0, ),
        'little_helper': (5, ),
        'warehouse_show_rotation': (-55, 172.2, 38, ),
    }), 
    90: TD({
        'id': 90,
        'name': 'P90',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 24,
        'icon_id': 759,
        'small_icon_id': 760,
        'big_icon_id': 764,
        'mod_id': 253,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/p90.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'p90',
        'gun_media_bnk': 'p90_media',
        'single_shot_sound_id': 519,
        'kill_tip_icon': 762,
        'gunsmith_item_desc': '造型独特的个人防卫武器，可使用50发弹匣的紧凑设计使其在近距离作战中表现出色',
        'move_action_type': 2,
        'tps_move_action_type': 2,
        'anim_data_id': 30,
        'anim_speed_data_id': 2,
        'little_helper': (21, ),
    }), 
    91: TD({
        'id': 91,
        'name': 'VSS',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 25,
        'icon_id': 813,
        'small_icon_id': 814,
        'big_icon_id': 826,
        'shadow_icon_id': 10850,
        'mod_id': 313,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/vss.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'vss',
        'gun_media_bnk': 'vss_media',
        'single_shot_sound_id': 620,
        'kill_tip_icon': 816,
        'gunsmith_item_desc': '发射9x39亚音速弹的微声狙击步枪，通过不同的改装切换单发和全自动模式，以适应多种作战环境',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 32,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    92: TD({
        'id': 92,
        'skip': 'trunk_only',
        'name': '测试用枪',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 26,
        'icon_id': 341,
        'small_icon_id': 436,
        'big_icon_id': 512,
        'shadow_icon_id': 10849,
        'mod_id': 261,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/test_gun_fps_mp.graph',
        'tps_graph': 'TPS/Spell/m4.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'm4a1',
        'gun_media_bnk': 'weap_m4a1',
        'single_shot_sound_id': 166,
        'kill_tip_icon': 358,
        'gunsmith_item_desc': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 33,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    93: TD({
        'id': 93,
        'name': 'AR97',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 27,
        'icon_id': 110000,
        'small_icon_id': 110001,
        'big_icon_id': 110004,
        'shadow_icon_id': 110005,
        'mod_id': 324,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/AR97_fps_mp.graph',
        'tps_graph': 'TPS/Spell/AR97.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'ar97',
        'gun_media_bnk': 'ar97_media',
        'single_shot_sound_id': 620,
        'kill_tip_icon': 110003,
        'gunsmith_item_desc': '三连发点射步枪，三连发精度极高，适合在中远距离融化敌人',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 34,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    94: TD({
        'id': 94,
        'name': 'Minigun',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 28,
        'icon_id': 1011,
        'small_icon_id': 1012,
        'big_icon_id': 1009,
        'shadow_icon_id': 10850,
        'mod_id': 326,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/Minigun_fps_mp.graph',
        'tps_graph': 'TPS/Spell/Minigun.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'minigun',
        'gun_media_bnk': 'minigun_media',
        'single_shot_sound_id': 725,
        'kill_tip_icon': 1016,
        'gunsmith_item_desc': '拥有极大容量的弹匣与一台高出力驱动电机的机枪，能够驱动六根大口径枪管加速旋转，提供可靠的火力支援与恐怖的压制力',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 35,
        'anim_speed_data_id': 8,
        'little_helper': (21, ),
    }), 
    95: TD({
        'id': 95,
        'name': '蝴蝶刀',
        'equip_type': 2,
        'equip_case': 'Melee',
        'model_id': 2435,
        'icon_id': 20700000200,
        'small_icon_id': 20700000201,
        'big_icon_id': 507,
        'mod_id': 262,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/ButterflyKnife_fps_mp.graph',
        'tps_graph': 'TPS/Spell/ghostknife_attack.graph',
        'spell_id': 20,
        'gun_sound_type': 'butterflyknife',
        'gun_media_bnk': 'butterflyknife_media',
        'kill_tip_icon': 11735,
        'equip_movement_speed': 1.15,
        'move_action_type': 5,
        'tps_move_action_type': 5,
        'anim_data_id': 36,
        'anim_speed_data_id': 1,
        'melee_weapon_id': 1,
        'screenshot_rotate': (90, 0, 0, ),
        'little_helper': (5, ),
        'warehouse_show_rotation': (45, -155, -23, ),
        'warehouse_show_scale': 1.0,
        'gacha_show_scale': 1.5,
    }), 
    96: TD({
        'id': 96,
        'name': '双手斧',
        'equip_type': 2,
        'equip_case': 'Melee',
        'model_id': 2654,
        'icon_id': 1044,
        'small_icon_id': 1037,
        'big_icon_id': 1042,
        'mod_id': 263,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'melee_default_ik': (True, ),
        'hand_graph': 'FPS/Hand/TwoHandAxe_fps_mp.graph',
        'tps_graph': 'TPS/Spell/TwoHandAxe_attack.graph',
        'spell_id': 20,
        'gun_media_bnk': 'bsfb_axe_media',
        'kill_tip_icon': 1038,
        'equip_movement_speed': 1.15,
        'move_action_type': 6,
        'tps_move_action_type': 6,
        'anim_data_id': 38,
        'anim_speed_data_id': 9,
        'is_tps_bonefilter_arm': True,
        'melee_weapon_id': 5,
        'screenshot_rotate': (90, 0, 0, ),
        'little_helper': (5, ),
        'warehouse_show_rotation': (-55, 172.2, 38, ),
    }), 
    97: TD({
        'id': 97,
        'name': '双刀',
        'equip_type': 2,
        'equip_case': 'DualMelee',
        'model_id': 2902,
        'icon_id': 893,
        'small_icon_id': 892,
        'big_icon_id': 894,
        'mod_id': 264,
        'sub_model_id': 2902,
        'sub_model_attach': ('HP_root', 'HP_Scabbard', ),
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/DoubleBlade_fps_mp.graph',
        'tps_graph': 'TPS/Spell/DoubleBlade_attack.graph',
        'spell_id': 20,
        'gun_media_bnk': 'doubleblade_media',
        'kill_tip_icon': 898,
        'equip_movement_speed': 1.15,
        'move_action_type': 6,
        'tps_move_action_type': 6,
        'anim_data_id': 39,
        'anim_speed_data_id': 9,
        'is_tps_bonefilter_arm': True,
        'melee_weapon_id': 1,
        'screenshot_rotate': (90, 0, 0, ),
        'little_helper': (5, ),
        'warehouse_show_rotation': (-38, -150, 0, ),
    }), 
    98: TD({
        'id': 98,
        'name': 'AUG',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 29,
        'icon_id': 110021,
        'small_icon_id': 110022,
        'big_icon_id': 110025,
        'shadow_icon_id': 110026,
        'mod_id': 395,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/aug.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'aug',
        'gun_media_bnk': 'aug_media',
        'single_shot_sound_id': 1355,
        'kill_tip_icon': 110024,
        'gunsmith_item_desc': '最早大规模列装的无托步枪。AUG优秀的人机功效和指向性，使得使用者抬枪即可快速瞄准。',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 40,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    99: TD({
        'id': 99,
        'name': '爪刀',
        'equip_type': 2,
        'equip_case': 'Melee',
        'model_id': 3190,
        'icon_id': 1044,
        'small_icon_id': 1037,
        'big_icon_id': 1042,
        'mod_id': 265,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/clawknife_fps_mp.graph',
        'tps_graph': 'TPS/Spell/clawknife_attack.graph',
        'spell_id': 20,
        'gun_media_bnk': 'dagger_media',
        'kill_tip_icon': 1071,
        'equip_movement_speed': 1.15,
        'move_action_type': 5,
        'tps_move_action_type': 5,
        'anim_data_id': 42,
        'anim_speed_data_id': 1,
        'melee_weapon_id': 1,
        'screenshot_rotate': (90, 0, 0, ),
        'little_helper': (5, ),
        'warehouse_show_rotation': (-20.9, 18.4, 5.3, ),
        'warehouse_show_scale': 1.0,
    }), 
    100: TD({
        'id': 100,
        'name': 'M4A1-2',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 35,
        'icon_id': 341,
        'small_icon_id': 436,
        'big_icon_id': 512,
        'shadow_icon_id': 10849,
        'mod_id': 408,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/m4.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'm4a1',
        'gun_media_bnk': 'weap_m4a1',
        'single_shot_sound_id': 166,
        'kill_tip_icon': 358,
        'gunsmith_item_desc': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 1,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    101: TD({
        'id': 101,
        'name': 'Magnum',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 30,
        'icon_id': 110144,
        'small_icon_id': 110145,
        'big_icon_id': 110148,
        'shadow_icon_id': 110146,
        'mod_id': 397,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/Magnum_fps_mp.graph',
        'tps_graph': 'TPS/Spell/Magnum.graph',
        'spell_id': 1,
        'vice_spell_id': 39,
        'gun_sound_type': 'magnum',
        'gun_media_bnk': 'magnum_media',
        'single_shot_sound_id': 1288,
        'kill_tip_icon': 110147,
        'gunsmith_item_desc': '发射.44大威力左轮专用弹的手枪，威力巨大',
        'move_action_type': 4,
        'tps_move_action_type': 4,
        'anim_data_id': 41,
        'anim_speed_data_id': 11,
        'little_helper': (21, ),
    }), 
    102: TD({
        'id': 102,
        'name': 'MCX',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 31,
        'icon_id': 110029,
        'small_icon_id': 110030,
        'big_icon_id': 110033,
        'shadow_icon_id': 110034,
        'mod_id': 399,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/MCX_fps_mp.graph',
        'tps_graph': 'TPS/Spell/MCX.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'mcx',
        'gun_media_bnk': 'mcx_media',
        'single_shot_sound_id': 951,
        'kill_tip_icon': 110032,
        'gunsmith_item_desc': '高度模块化的卡宾枪，射速快，爆头伤害较高',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 43,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    103: TD({
        'id': 103,
        'name': 'FAL',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 32,
        'icon_id': 110042,
        'small_icon_id': 110043,
        'big_icon_id': 110046,
        'shadow_icon_id': 110047,
        'mod_id': 404,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/FAL_fps_mp.graph',
        'tps_graph': 'TPS/Spell/FAL.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'fal',
        'gun_media_bnk': 'fal_media',
        'single_shot_sound_id': 1010,
        'kill_tip_icon': 110045,
        'gunsmith_item_desc': '威力强大的战斗步枪，自由世界的右手',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 44,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    104: TD({
        'id': 104,
        'name': '蝴蝶刀v6',
        'equip_type': 2,
        'equip_case': 'Melee',
        'model_id': 3646,
        'icon_id': 20700000200,
        'small_icon_id': 20700000201,
        'big_icon_id': 507,
        'mod_id': 266,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/ButterflyKnifev6_fps_mp.graph',
        'tps_graph': 'TPS/Spell/ghostknife_attack.graph',
        'spell_id': 20,
        'gun_sound_type': 'butterflyknife',
        'gun_media_bnk': 'butterflyknife_media',
        'kill_tip_icon': 11735,
        'equip_movement_speed': 1.15,
        'move_action_type': 5,
        'tps_move_action_type': 5,
        'anim_data_id': 46,
        'anim_speed_data_id': 1,
        'melee_weapon_id': 1,
        'screenshot_rotate': (90, 0, 0, ),
        'little_helper': (5, ),
        'warehouse_show_rotation': (45, -155, -23, ),
        'warehouse_show_scale': 1.0,
        'gacha_show_scale': 1.5,
    }), 
    105: TD({
        'id': 105,
        'skip': 'trunk_only',
        'name': '快捷xdroid',
        'equip_type': 19,
        'equip_case': 'LeftHand',
        'model_id': 547,
        'default_attach': ('HP_Hand', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/xdroid.graph',
        'tps_graph': 'TPS/Locomotion/tps_xdorid.graph',
        'spell_id': 46,
        'anim_data_id': 5,
    }), 
    108: TD({
        'id': 108,
        'name': 'PKM',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 34,
        'icon_id': 110072,
        'small_icon_id': 110073,
        'big_icon_id': 110076,
        'shadow_icon_id': 110077,
        'mod_id': 410,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/PKM_fps_mp.graph',
        'tps_graph': 'TPS/Spell/PKM3p.graph',
        'spell_id': 1,
        'gun_sound_type': 'pkm',
        'gun_media_bnk': 'pkm_media',
        'single_shot_sound_id': 1095,
        'kill_tip_icon': 110075,
        'gunsmith_item_desc': '一款性能优秀的火力支援武器，具有较高射速和火力压制力',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 48,
        'anim_speed_data_id': 8,
        'little_helper': (21, ),
    }), 
    110: TD({
        'id': 110,
        'name': 'Uzi',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 40,
        'icon_id': 110097,
        'small_icon_id': 110098,
        'big_icon_id': 110101,
        'shadow_icon_id': 110102,
        'mod_id': 430,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_gun', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/uzi_new.graph',
        'spell_id': 1,
        'vice_spell_id': 55,
        'need_backup_charger': True,
        'gun_sound_type': 'uzi',
        'gun_media_bnk': 'uzi_media',
        'single_shot_sound_id': 1197,
        'kill_tip_icon': 110100,
        'gunsmith_item_desc': '射速较慢但很稳定的冲锋枪，枪身紧凑并且容易控制',
        'move_action_type': 2,
        'tps_move_action_type': 2,
        'anim_data_id': 51,
        'anim_speed_data_id': 2,
        'little_helper': (21, ),
    }), 
    114: TD({
        'id': 114,
        'name': 'FN2000',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 37,
        'icon_id': 110111,
        'small_icon_id': 110112,
        'big_icon_id': 110115,
        'shadow_icon_id': 110116,
        'mod_id': 474,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/FN2000.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'fn2000',
        'gun_media_bnk': 'fn2000_media',
        'single_shot_sound_id': 1217,
        'kill_tip_icon': 110114,
        'gunsmith_item_desc': '大量采用复合材料的无托结构突击步枪，拥有优秀的人机工效和极高的射速',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 53,
        'anim_speed_data_id': 1,
        'lobby_gun_offset': (0, 0.018, 0, ),
        'little_helper': (21, ),
    }), 
    115: TD({
        'id': 115,
        'name': 'Galil',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 38,
        'icon_id': 110124,
        'small_icon_id': 110125,
        'big_icon_id': 110128,
        'shadow_icon_id': 110129,
        'mod_id': 476,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/Galil_fps_mp.graph',
        'tps_graph': 'TPS/Spell/Galil.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'galil',
        'gun_media_bnk': 'galil_media',
        'single_shot_sound_id': 1243,
        'kill_tip_icon': 110127,
        'gunsmith_item_desc': '采用大量聚合物材料，保持高威力的同时，大容量弹匣也提升了其火力持续性。',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 54,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    121: TD({
        'id': 121,
        'name': 'QBZ95',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 39,
        'icon_id': 110166,
        'small_icon_id': 110167,
        'big_icon_id': 110170,
        'shadow_icon_id': 110168,
        'mod_id': 514,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/m4.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'qbz95',
        'gun_media_bnk': 'weap_qbz',
        'single_shot_sound_id': 1339,
        'kill_tip_icon': 110169,
        'gunsmith_item_desc': '占位',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 58,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    122: TD({
        'id': 122,
        'name': 'FN2000-New',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'can_in_backpack': True,
        'gun_id': 42,
        'icon_id': 110111,
        'small_icon_id': 110112,
        'big_icon_id': 110115,
        'shadow_icon_id': 110116,
        'mod_id': 516,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/FN2000.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'fn2000',
        'gun_media_bnk': 'fn2000_media',
        'single_shot_sound_id': 1217,
        'kill_tip_icon': 110114,
        'gunsmith_item_desc': '大量采用复合材料的无托结构突击步枪，拥有优秀的人机工效和极高的射速',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 53,
        'anim_speed_data_id': 1,
        'lobby_gun_offset': (0, 0.018, 0, ),
        'little_helper': (21, ),
    }), 
    123: TD({
        'id': 123,
        'name': 'SKS',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 43,
        'icon_id': 813,
        'small_icon_id': 814,
        'big_icon_id': 826,
        'shadow_icon_id': 10850,
        'mod_id': 517,
        'default_attach': ('HP_gun_1', 'tag_weapon_left', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/M4_fps_mp.graph',
        'tps_graph': 'TPS/Spell/m4.graph',
        'spell_id': 1,
        'need_backup_charger': True,
        'gun_sound_type': 'vss',
        'gun_media_bnk': 'vss_media',
        'single_shot_sound_id': 620,
        'kill_tip_icon': 816,
        'gunsmith_item_desc': 'SKS',
        'move_action_type': 1,
        'tps_move_action_type': 1,
        'anim_data_id': 60,
        'anim_speed_data_id': 1,
        'little_helper': (21, ),
    }), 
    200: TD({
        'id': 200,
        'name': 'NEW SHEILD',
        'equip_type': 13,
        'equip_case': 'Item',
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'WL', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/ethan_skill_fps.graph',
        'tps_graph': 'TPS/Spell/skill_test1.graph',
        'spell_id': 200,
    }), 
    201: TD({
        'id': 201,
        'name': 'BS SHIELD',
        'equip_type': 13,
        'equip_case': 'Item',
        'throwable': True,
        'model_id': 554,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'WL', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/fps_skill_Ethan_shield.graph',
        'tps_graph': 'TPS/Spell/skill_test1.graph',
        'spell_id': 201,
    }), 
    202: TD({
        'id': 202,
        'name': 'bombardment',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 522,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill7_missile.graph',
        'tps_graph': 'TPS/Spell/skill_missile.graph',
        'spell_id': 202,
        'kill_tip_icon': 407,
        'anim_data_id': 999,
    }), 
    203: TD({
        'id': 203,
        'name': 'orbital',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 522,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill7_missile.graph',
        'tps_graph': 'TPS/Spell/skill_missile.graph',
        'spell_id': 203,
        'kill_tip_icon': 409,
        'anim_data_id': 999,
    }), 
    204: TD({
        'id': 204,
        'name': 'GAS GRENADE',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 556,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill_gasgrenade.graph',
        'tps_graph': 'TPS/Spell/gasgrenade_toss.graph',
        'spell_id': 204,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 408,
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 999,
        'anim_speed_data_id': 15,
    }), 
    205: TD({
        'id': 205,
        'name': 'DEATH CLOUD',
        'equip_type': 13,
        'equip_case': 'Item',
        'using_ban_move': True,
        'model_id': 557,
        'icon_id': 112,
        'default_attach': ('root', 'WL', ),
        'hand_tach_point': ('root', 'WL', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill_gasbomb.graph',
        'tps_graph': 'TPS/Spell/skill_gasbomb.graph',
        'spell_id': 205,
        'kill_tip_icon': 408,
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 999,
        'anim_speed_data_id': 15,
    }), 
    206: TD({
        'id': 206,
        'name': 'SURVEY SATELLITE',
        'equip_type': 13,
        'equip_case': 'Item',
        'using_ban_move': True,
        'using_ban_prone': True,
        'using_ban_in_air': True,
        'model_id': 42,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill_scan.graph',
        'tps_graph': 'TPS/Spell/skill_satellite_scan.graph',
        'spell_id': 206,
    }), 
    207: TD({
        'id': 207,
        'name': 'ADVANCED SATELLITE',
        'equip_type': 13,
        'equip_case': 'Item',
        'using_ban_move': True,
        'using_ban_prone': True,
        'using_ban_in_air': True,
        'model_id': 558,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill_scan.graph',
        'tps_graph': 'TPS/Spell/skill_satellite_scan.graph',
        'spell_id': 207,
    }), 
    208: TD({
        'id': 208,
        'name': 'TURRET',
        'equip_type': 13,
        'equip_case': 'Item',
        'take_check': 'TakeSkillCheck',
        'use_check': 'UsingPlaceEntityCheck',
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill_turret.graph',
        'tps_graph': 'TPS/Spell/tps_skill_turret.graph',
        'spell_id': 208,
        'kill_tip_icon': 410,
        'anim_data_id': 999,
    }), 
    209: TD({
        'id': 209,
        'name': 'HEAVT TURRET',
        'equip_type': 13,
        'equip_case': 'Item',
        'take_check': 'TakeSkillCheck',
        'use_check': 'UsingPlaceEntityCheck',
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill_turret.graph',
        'tps_graph': 'TPS/Spell/tps_skill_turret.graph',
        'spell_id': 209,
        'kill_tip_icon': 410,
        'anim_data_id': 999,
    }), 
    210: TD({
        'id': 210,
        'name': 'SILENCE',
        'equip_type': 13,
        'take_check': 'TakeSkillCheck',
        'spell_id': 210,
    }), 
    211: TD({
        'id': 211,
        'name': 'GHOST',
        'equip_type': 13,
        'take_check': 'TakeSkillCheck',
        'spell_id': 211,
    }), 
    212: TD({
        'id': 212,
        'name': 'HEALING BUOY',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 530,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill7_drone_stay.graph',
        'tps_graph': 'TPS/Spell/skill_use_medical_drone.graph',
        'spell_id': 212,
    }), 
    213: TD({
        'id': 213,
        'name': 'HEALING DRONE',
        'equip_type': 13,
        'equip_case': 'Item',
        'use_check': 'UsingCureUavCheck',
        'model_id': 530,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill6_drone.graph',
        'tps_graph': 'TPS/Spell/skill_medical_drone.graph',
        'spell_id': 213,
    }), 
    214: TD({
        'id': 214,
        'name': 'VORTEX',
        'equip_type': 13,
        'equip_case': 'Item',
        'hand_graph': 'FPS/Hand/skill_vortex.graph',
        'tps_graph': 'TPS/Spell/skill_vortex.graph',
        'spell_id': 214,
        'anim_data_id': 999,
    }), 
    215: TD({
        'id': 215,
        'name': 'DARK ZONE',
        'equip_type': 13,
        'equip_case': 'Item',
        'hand_graph': 'FPS/Hand/skill8_baozou.graph',
        'tps_graph': 'TPS/Spell/skill_smoke_zone.graph',
        'spell_id': 215,
        'anim_data_id': 999,
    }), 
    225: TD({
        'id': 225,
        'name': 'ICE WALL',
        'equip_type': 13,
        'equip_case': 'Item',
        'use_check': 'UsingPlaceEntityCheck',
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'hand_graph': 'FPS/Hand/skill_ICEWALL.graph',
        'tps_graph': 'TPS/Spell/icewall_toss.graph',
        'spell_id': 225,
        'anim_data_id': 999,
    }), 
    226: TD({
        'id': 226,
        'name': 'FIRE WALL',
        'equip_type': 13,
        'equip_case': 'Item',
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'hand_graph': 'FPS/Hand/skill_FIREWALL.graph',
        'tps_graph': 'TPS/Spell/tps_skill_firewall.graph',
        'spell_id': 226,
        'kill_tip_icon_2': 800,
        'anim_data_id': 5,
    }), 
    227: TD({
        'id': 227,
        'name': 'GRAFFTI GRENADE',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 235,
        'icon_id': 642,
        'default_attach': ('HP_Shell', 'HP_Shell', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill_DRILL.graph',
        'tps_graph': 'TPS/Spell/skill_drill.graph',
        'spell_id': 227,
        'kill_tip_icon': 411,
        'anim_data_id': 999,
    }), 
    228: TD({
        'id': 228,
        'name': '涂鸦炸弹-穿透后',
        'equip_type': 13,
        'equip_case': 'Item',
        'throwable': True,
        'model_id': 233,
        'icon_id': 112,
        'spell_id': 228,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 411,
        'anim_data_id': 999,
    }), 
    229: TD({
        'id': 229,
        'name': '扫描器',
        'equip_type': 20,
        'equip_case': 'Item',
        'state': 53,
        'take_check': 'TakeCrackScannerCheck',
        'model_id': 547,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/scanner.graph',
        'tps_graph': 'TPS/Locomotion/tps_scanner.graph',
        'spell_id': 54,
        'anim_data_id': 31,
        'anim_speed_data_id': 7,
    }), 
    230: TD({
        'id': 230,
        'name': 'FIRE WALL加强',
        'equip_type': 13,
        'equip_case': 'Item',
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'hand_graph': 'FPS/Hand/skill_FIREWALL.graph',
        'tps_graph': 'TPS/Spell/tps_skill_firewall.graph',
        'spell_id': 231,
        'kill_tip_icon_2': 800,
    }), 
    231: TD({
        'id': 231,
        'name': '疾风斩',
        'equip_type': 13,
        'equip_case': 'SamuraiSword',
        'model_id': 1516,
        'icon_id': 549,
        'small_icon_id': 550,
        'big_icon_id': 549,
        'mod_id': 310,
        'scabbard_id': 1523,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/katana_fps_mp.graph',
        'tps_graph': 'TPS/Spell/tps_skill_samurai.graph',
        'spell_id': 232,
        'gun_media_bnk': 'handaxe_media',
        'kill_tip_icon': 561,
        'anim_data_id': 27,
    }), 
    232: TD({
        'id': 232,
        'name': '格挡',
        'equip_type': 13,
        'equip_case': 'SamuraiSword',
        'model_id': 1516,
        'icon_id': 549,
        'small_icon_id': 550,
        'big_icon_id': 549,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/katana_fps_mp.graph',
        'tps_graph': 'TPS/Spell/tps_skill_samurai.graph',
        'spell_id': 233,
        'anim_data_id': 27,
    }), 
    233: TD({
        'id': 233,
        'name': '武士-武士刀挂接',
        'equip_type': 2,
        'equip_case': 'Item',
        'model_id': 308,
        'scabbard_id': 309,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_root', 'HP_katana_root', ),
        'default_attach_tps': (1, 0, ),
    }), 
    234: TD({
        'id': 234,
        'name': '电磁振荡波',
        'equip_type': 13,
        'equip_case': 'Item',
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'hand_graph': 'FPS/Hand/skill_shockwave.graph',
        'tps_graph': 'TPS/Spell/tps_skill_shockwave.graph',
        'spell_id': 234,
        'anim_data_id': 999,
    }), 
    235: TD({
        'id': 235,
        'name': 'ETHAN充能',
        'equip_type': 13,
        'equip_case': 'LeftHand',
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'hand_graph': 'FPS/Hand/fps_skill_lefthandgeneral.graph',
        'tps_graph': 'TPS/Spell/skill_smoke_zone.graph',
        'spell_id': 235,
        'anim_data_id': 999,
    }), 
    236: TD({
        'id': 236,
        'name': '无人机治疗加速-二技能',
        'equip_type': 13,
        'equip_case': 'Item',
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'spell_id': 236,
        'anim_data_id': 999,
    }), 
    237: TD({
        'id': 237,
        'name': '蜂型治疗无人机',
        'equip_type': 13,
        'equip_case': 'Item',
        'use_check': 'UsingCureUavCheck',
        'model_id': 113,
        'icon_id': 112,
        'default_attach': ('HP_root', 'Bip001', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill_healing_drone.graph',
        'tps_graph': 'TPS/Spell/skill_healing_drone.graph',
        'fire_ready_event': True,
        'spell_id': 237,
        'anim_data_id': 37,
        'use_dynamic_visibility_box': True,
    }), 
    238: TD({
        'id': 238,
        'name': '蜂型治疗无人机',
        'equip_type': 13,
        'equip_case': 'Item',
        'use_check': 'UsingCureUavCheck',
        'model_id': 113,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill_healing_drone.graph',
        'tps_graph': 'TPS/Spell/skill_healing_drone.graph',
        'spell_id': 238,
        'anim_data_id': 37,
    }), 
    239: TD({
        'id': 239,
        'skip': 'trunk_only',
        'name': '破解任务-U盘',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 612,
        'default_attach': ('HP_Hand', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'hand_graph': 'FPS/Hand/UseDisk_fps_mp.graph',
        'tps_graph': 'TPS/Spell/tps_use_disk.graph',
    }), 
    240: TD({
        'id': 240,
        'name': '钻墙炸弹-改',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 235,
        'icon_id': 642,
        'default_attach': ('HP_Shell', 'HP_Shell', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill_DRILL.graph',
        'tps_graph': 'TPS/Spell/skill_drill.graph',
        'spell_id': 239,
        'kill_tip_icon': 411,
        'anim_data_id': 999,
    }), 
    241: TD({
        'id': 241,
        'name': '分裂手雷',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'take_check': 'TakeClusterBombeCheck',
        'throwable': True,
        'model_id': 536,
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 240,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 11425,
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    242: TD({
        'id': 242,
        'name': '侦查箭',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 257,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 1, ),
        'hand_graph': 'FPS/Hand/skill_ScoutArrow.graph',
        'tps_graph': 'TPS/Spell/skill_ScoutArrow_TPS.graph',
        'spell_id': 242,
        'move_action_type': 3,
        'tps_move_action_type': 3,
        'anim_data_id': 24,
    }), 
    243: TD({
        'id': 243,
        'name': '脉冲箭',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 257,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_Hand_Left_IK', 'HP_gun', ),
        'default_attach_tps': (0, 1, ),
        'hand_graph': 'FPS/Hand/skill_PulseArrow.graph',
        'tps_graph': 'TPS/Spell/skill_PulseArrow_TPS.graph',
        'spell_id': 243,
        'move_action_type': 3,
        'tps_move_action_type': 3,
        'anim_data_id': 24,
    }), 
    244: TD({
        'id': 244,
        'name': '跟踪炸弹',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 271,
        'icon_id': 642,
        'default_attach': ('HP_Shell', 'HP_Shell', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill_DRILL.graph',
        'tps_graph': 'TPS/Spell/skill_drill.graph',
        'spell_id': 244,
        'kill_tip_icon': 411,
        'anim_data_id': 999,
    }), 
    245: TD({
        'id': 245,
        'name': '击退炸弹',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 272,
        'icon_id': 642,
        'default_attach': ('HP_Shell', 'HP_Shell', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill_REPEL.graph',
        'tps_graph': 'TPS/Spell/skill_repel.graph',
        'spell_id': 245,
        'kill_tip_icon': 411,
        'anim_data_id': 999,
    }), 
    246: TD({
        'id': 246,
        'name': '彩蛋炸弹',
        'equip_type': 13,
        'equip_case': 'LeftHand',
        'throwable': True,
        'model_id': 661,
        'icon_id': 642,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_WL', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/fps_skill_lefthandgeneral.graph',
        'tps_graph': 'TPS/Spell/smoke_grenade_toss.graph',
        'spell_id': 246,
        'kill_tip_icon': 11425,
        'anim_data_id': 999,
    }), 
    300: TD({
        'id': 300,
        'skip': 'skip',
        'name': 'DJ-CAT',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'use_check': 'UsingPlaceEntityCheck',
        'throwable': True,
        'model_id': 261,
        'icon_id': 642,
        'default_attach': ('HP_root', 'Bip001', ),
        'hand_tach_point': ('HP_root', 'Bip001', ),
        'default_attach_tps': (1, 1, ),
        'default_attach_tps_new': ('HP_root', 'biped', ),
        'hand_graph': 'FPS/Hand/fps_skill_NYANG_DJCAT.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 300,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    301: TD({
        'id': 301,
        'name': 'BOOST-RING',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 634,
        'icon_id': 642,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'WR', ),
        'default_attach_tps': (1, 1, ),
        'default_attach_tps_new': ('HP_root', 'HP_WR', ),
        'hand_graph': 'FPS/Hand/fps_skill_NYANG_RING.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 301,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    303: TD({
        'id': 303,
        'name': 'BODY_DASH',
        'equip_type': 13,
        'equip_case': 'Item',
        'icon_id': 642,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/fps_skill_katya_dash.graph',
        'tps_graph': 'TPS/Spell/tps_skill_katya_dash.graph',
        'spell_id': 303,
        'anim_data_id': 999,
        'anim_speed_data_id': 1,
    }), 
    304: TD({
        'id': 304,
        'name': 'electric dart',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 269,
        'icon_id': 642,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/kuwutest.graph',
        'tps_graph': 'TPS/Spell/kuwutest_tps.graph',
        'spell_id': 304,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 11422,
        'gunsmith_item_desc': '爆炸后产生范围伤害',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    305: TD({
        'id': 305,
        'name': '数码化',
        'equip_type': 13,
        'equip_case': 'Item',
        'icon_id': 642,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/fps_skill_Marok_skill02.graph',
        'tps_graph': 'TPS/Spell/tps_skill_Marok_skill02.graph',
        'spell_id': 305,
    }), 
    306: TD({
        'id': 306,
        'name': '赛博领域',
        'equip_type': 13,
        'equip_case': 'Item',
        'icon_id': 642,
        'default_attach': ('HP_root', 'Bip001', ),
        'hand_tach_point': ('HP_root', 'Bip001', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/fps_skill_Marok_skill01.graph',
        'tps_graph': 'TPS/Spell/tps_skill_Marok_skill01.graph',
        'spell_id': 306,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    307: TD({
        'id': 307,
        'name': '强化激素',
        'equip_type': 13,
        'equip_case': 'Item',
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/fps_skill_Serin_boost.graph',
        'tps_graph': 'TPS/Spell/skill_smoke_zone.graph',
        'spell_id': 307,
        'anim_data_id': 37,
    }), 
    308: TD({
        'id': 308,
        'name': '破坏球',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 672,
        'icon_id': 389,
        'mod_id': 1013,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/fps_skill_ran_grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 308,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 11425,
        'gunsmith_item_desc': '爆炸后产生范围伤害',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
        'warehouse_show_rotation': (0, 180, 0, ),
    }), 
    309: TD({
        'id': 309,
        'name': '震荡手雷',
        'equip_type': 13,
        'equip_case': 'LeftHandStunGrenade',
        'throwable': True,
        'model_id': 673,
        'icon_id': 389,
        'mod_id': 1014,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/fps_skill_lefthandgeneral.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 309,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon': 11425,
        'gunsmith_item_desc': '爆炸后产生范围伤害',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
        'warehouse_show_rotation': (0, 180, 0, ),
    }), 
    310: TD({
        'id': 310,
        'name': '武士刀 迭代',
        'equip_type': 13,
        'equip_case': 'SamuraiSword',
        'model_id': 308,
        'icon_id': 549,
        'small_icon_id': 550,
        'big_icon_id': 549,
        'mod_id': 310,
        'scabbard_id': 1523,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/katana_fps_mp.graph',
        'tps_graph': 'TPS/Spell/tps_skill_samurai.graph',
        'spell_id': 310,
        'gun_media_bnk': 'handaxe_media',
        'kill_tip_icon': 561,
        'anim_data_id': 36,
    }), 
    311: TD({
        'id': 311,
        'name': '格挡 迭代',
        'equip_type': 13,
        'equip_case': 'SamuraiSword',
        'model_id': 308,
        'icon_id': 549,
        'small_icon_id': 550,
        'big_icon_id': 549,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/katana_fps_mp.graph',
        'tps_graph': 'TPS/Spell/tps_skill_samurai.graph',
        'spell_id': 311,
        'anim_data_id': 36,
    }), 
    312: TD({
        'id': 312,
        'name': '伊迪丝 - 自我强化',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 310,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WR', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/fps_skill_EdithBoost.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 312,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
    401: TD({
        'id': 401,
        'name': '通用可投掷物',
        'equip_type': 22,
        'equip_case': 'Carriable',
        'icon_id': 112,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/throw_carriable_fps.graph',
        'tps_graph': 'TPS/Spell/fists_attack.graph',
        'spell_id': 500,
    }), 
    402: TD({
        'id': 402,
        'name': 'STI2011',
        'equip_type': 1,
        'equip_case': 'Weapon',
        'unable_gunsmith': True,
        'can_in_backpack': True,
        'gun_id': 41,
        'icon_id': 342,
        'small_icon_id': 434,
        'big_icon_id': 510,
        'shadow_icon_id': 10853,
        'mod_id': 14013,
        'default_attach': ('HP_gun', 'WR', ),
        'hand_tach_point': ('HP_gun', 'HP_gun', ),
        'default_attach_tps': (0, 0, ),
        'hand_graph': 'FPS/Hand/STI2011_fps_mp.graph',
        'tps_graph': 'TPS/Spell/glock.graph',
        'spell_id': 1,
        'vice_spell_id': 55,
        'need_backup_charger': True,
        'gun_sound_type': 'glock',
        'gun_media_bnk': 'glock_media',
        'single_shot_sound_id': 165,
        'kill_tip_icon': 357,
        'kill_tip_icon_2': 798,
        'gunsmith_item_desc': '半自动手枪，可靠的副武器',
        'move_action_type': 4,
        'tps_move_action_type': 4,
        'anim_data_id': 59,
        'anim_speed_data_id': 5,
        'little_helper': (21, ),
    }), 
    421: TD({
        'id': 421,
        'name': '占位-幻象，传送JOKER',
        'equip_type': 13,
        'equip_case': 'LeftHand',
        'take_check': 'TakeSkillCheck',
        'icon_id': 14915,
        'default_attach': ('HP_root_SZ01', 'WL', ),
        'hand_tach_point': ('HP_root_SZ01', 'HP_root_SZ01', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/fps_skill_lefthandgeneral.graph',
        'tps_graph': 'TPS/Spell/tps_skill_mirage.graph',
        'spell_id': 340,
        'gun_media_bnk': 'esm_skills_spike_ghost_release_3d',
    }), 
    422: TD({
        'id': 422,
        'name': '幻象',
        'equip_type': 13,
        'equip_case': 'Item',
        'take_check': 'TakeSkillCheck',
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'WL', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill_mirage.graph',
        'tps_graph': 'TPS/Spell/tps_skill_mirage.graph',
        'spell_id': 341,
        'gun_media_bnk': 'esm_skills_spike_ghost_release_3d',
    }), 
    426: TD({
        'id': 426,
        'name': '占位-诱饵雷',
        'equip_type': 13,
        'equip_case': 'RightHandThrowable',
        'throwable': True,
        'model_id': 536,
        'icon_id': 389,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/grenade.graph',
        'tps_graph': 'TPS/Spell/grenade_toss.graph',
        'spell_id': 342,
        'gun_media_bnk': 'tactical_weapon',
        'kill_tip_icon_2': 11425,
        'gunsmith_item_desc': '爆炸后产生范围伤害',
        'move_action_type': 0,
        'tps_move_action_type': 0,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
        'warehouse_show_rotation': (0, 180, 0, ),
    }), 
    437: TD({
        'id': 437,
        'skip': 'trunk_only',
        'name': '占位-HANK遥控战车',
        'equip_type': 13,
        'equip_case': 'Item',
        'take_check': 'TakeSkillCheck',
        'use_check': 'UsingPlaceEntityCheck',
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill_turret.graph',
        'tps_graph': 'TPS/Spell/tps_skill_turret.graph',
        'spell_id': 320,
        'kill_tip_icon_2': 410,
        'anim_data_id': 999,
    }), 
    467: TD({
        'id': 467,
        'name': '占位-HANK指挥战车',
        'equip_type': 13,
        'equip_case': 'Item',
        'take_check': 'TakeSkillCheck',
        'use_check': 'UsingPlaceEntityCheck',
        'model_id': 677,
        'icon_id': 14905,
        'default_attach': ('HP_Hand', 'WR', ),
        'hand_tach_point': ('HP_Hand', 'HP_Hand', ),
        'default_attach_tps': (1, 0, ),
        'hand_graph': 'FPS/Hand/skill_turret.graph',
        'tps_graph': 'TPS/Spell/tps_skill_turret.graph',
        'spell_id': 326,
        'kill_tip_icon_2': 410,
        'anim_data_id': 999,
    }), 
    469: TD({
        'id': 469,
        'name': 'HANK的巡逻无人机',
        'equip_type': 13,
        'equip_case': 'Item',
        'model_id': 671,
        'icon_id': 112,
        'default_attach': ('HP_root', 'WL', ),
        'hand_tach_point': ('HP_root', 'HP_root', ),
        'default_attach_tps': (1, 1, ),
        'hand_graph': 'FPS/Hand/skill_cruise_drone.graph',
        'tps_graph': 'TPS/Spell/skill_medical_drone.graph',
        'spell_id': 330,
        'anim_data_id': 15,
        'anim_speed_data_id': 1,
    }), 
}
