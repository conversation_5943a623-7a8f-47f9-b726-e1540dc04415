# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 80-热点战玩法表.xlsx, sheetname:热点_热点配置
from taggeddict import taggeddict as TD

data = {
    2: TD({
        1: TD({
            'id': '2.1',
            'space_id': 2,
            'local_id': 1,
            'point_pos': (209.2, 6.0, 229.6, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (8.0, 5.0, 11.0, 0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 25, 26, 27, ),
            'spawn_pos_1': (163.0, 265.0, ),
            'spawn_rad_1': 30.0,
            'main_spawn_2': (6, 7, 8, 9, 10, 19, 20, ),
            'spawn_pos_2': (189, 184, ),
            'spawn_rad_2': 30.0,
            'backup_spawn_1': (6, 7, 8, 9, 10, 19, 20, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 25, 26, 27, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        2: TD({
            'id': '2.2',
            'space_id': 2,
            'local_id': 2,
            'point_pos': (143.3, 6.0, 176.7, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (9.0, 5.0, 11.0, 0, ),
            'main_spawn_1': (6, 7, 8, 9, 10, 12, 29, 34, 35, ),
            'spawn_pos_1': (189, 184, ),
            'spawn_rad_1': 30.0,
            'main_spawn_2': (11, 13, 14, 32, ),
            'spawn_pos_2': (163.0, 265.0, ),
            'spawn_rad_2': 30.0,
            'backup_spawn_1': (11, 13, 14, 32, ),
            'backup_spawn_2': (6, 7, 8, 9, 10, 12, 29, 34, 35, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        3: TD({
            'id': '2.3',
            'space_id': 2,
            'local_id': 3,
            'point_pos': (196.6, 5.5, 259.4, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (10.6, 4.1, 12.5, 0, ),
            'main_spawn_1': (13, 24, 32, ),
            'spawn_pos_1': (163.0, 265.0, ),
            'spawn_rad_1': 30.0,
            'main_spawn_2': (21, 22, 30, 31, ),
            'spawn_pos_2': (189, 184, ),
            'spawn_rad_2': 30.0,
            'backup_spawn_1': (21, 22, 30, 31, ),
            'backup_spawn_2': (13, 24, 32, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        4: TD({
            'id': '2.4',
            'space_id': 2,
            'local_id': 4,
            'point_pos': (147.7, 8.5, 252.9, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (19.0, 4.1, 12.0, 0, ),
            'main_spawn_1': (13, 1, ),
            'spawn_pos_1': (189, 184, ),
            'spawn_rad_1': 30.0,
            'main_spawn_2': (11, 24, 23, 25, ),
            'spawn_pos_2': (163.0, 265.0, ),
            'spawn_rad_2': 30.0,
            'backup_spawn_1': (11, 24, 23, 25, ),
            'backup_spawn_2': (13, 1, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        5: TD({
            'id': '2.5',
            'space_id': 2,
            'local_id': 5,
            'point_pos': (210.5, 8.5, 179.6, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (5.5, 5.0, 11.0, 0, ),
            'main_spawn_1': (6, 7, 12, 34, ),
            'spawn_pos_1': (163.0, 265.0, ),
            'spawn_rad_1': 30.0,
            'main_spawn_2': (1, 30, 21, 22, ),
            'spawn_pos_2': (189, 184, ),
            'spawn_rad_2': 30.0,
            'backup_spawn_1': (1, 30, 21, 22, ),
            'backup_spawn_2': (6, 7, 12, 34, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
    }), 
    17: TD({
        1: TD({
            'id': '17.1',
            'space_id': 17,
            'local_id': 1,
            'point_pos': (0, 2.4, -3.1, ),
            'point_type': 1,
            'point_shape_param': (10, 5, 14.7, 0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 44, ),
            'spawn_pos_1': (40, 0, ),
            'spawn_rad_1': 20.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, 35, 36, 37, 38, 39, 40, 41, 42, 43, 58, 59, ),
            'spawn_pos_2': (-40, 0, ),
            'spawn_rad_2': 20.0,
            'backup_spawn_1': (7, 8, 9, 10, 11, 12, 35, 36, 37, 38, 39, 40, 41, 42, 43, 58, 59, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 44, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        2: TD({
            'id': '17.2',
            'space_id': 17,
            'local_id': 2,
            'point_pos': (0, 3.4, 10.15, ),
            'point_type': 1,
            'point_shape_param': (19.4, 5, 10.28, 0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 44, ),
            'spawn_pos_1': (40, 0, ),
            'spawn_rad_1': 20.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, 35, 36, 37, 38, 39, 40, 41, 42, 43, 58, 59, ),
            'spawn_pos_2': (-40, 0, ),
            'spawn_rad_2': 20.0,
            'backup_spawn_1': (7, 8, 9, 10, 11, 12, 35, 36, 37, 38, 39, 40, 41, 42, 43, 58, 59, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 44, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        3: TD({
            'id': '17.3',
            'space_id': 17,
            'local_id': 3,
            'point_pos': (0, 2.4, -3.1, ),
            'point_type': 1,
            'point_shape_param': (10, 5, 14.7, 0, ),
            'main_spawn_1': (7, 8, 9, 10, 11, 12, 35, 36, 37, 38, 39, 40, 41, 42, 43, 58, 59, ),
            'spawn_pos_1': (-40, 0, ),
            'spawn_rad_1': 20.0,
            'main_spawn_2': (1, 2, 3, 4, 5, 6, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 44, ),
            'spawn_pos_2': (40, 0, ),
            'spawn_rad_2': 20.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 44, ),
            'backup_spawn_2': (7, 8, 9, 10, 11, 12, 35, 36, 37, 38, 39, 40, 41, 42, 43, 58, 59, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        4: TD({
            'id': '17.4',
            'space_id': 17,
            'local_id': 4,
            'point_pos': (0, 3.4, 10.15, ),
            'point_type': 1,
            'point_shape_param': (19.4, 5, 10.28, 0, ),
            'main_spawn_1': (7, 8, 9, 10, 11, 12, 35, 36, 37, 38, 39, 40, 41, 42, 43, 58, 59, ),
            'spawn_pos_1': (-40, 0, ),
            'spawn_rad_1': 20.0,
            'main_spawn_2': (1, 2, 3, 4, 5, 6, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 44, ),
            'spawn_pos_2': (40, 0, ),
            'spawn_rad_2': 20.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 13, 14, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 44, ),
            'backup_spawn_2': (7, 8, 9, 10, 11, 12, 35, 36, 37, 38, 39, 40, 41, 42, 43, 58, 59, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
    }), 
    20: TD({
        1: TD({
            'id': '20.1',
            'space_id': 20,
            'local_id': 1,
            'point_pos': (-17.83, 2.98, 3.64, ),
            'point_type': 1,
            'point_shape_param': (16.6, 5, 5, 0, ),
            'main_spawn_1': (16, 17, 18, 4, 21, 19, ),
            'main_spawn_2': (15, 14, 23, 12, 34, 11, 20, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        2: TD({
            'id': '20.2',
            'space_id': 20,
            'local_id': 2,
            'point_pos': (9.47, 2.98, 22.6, ),
            'point_type': 1,
            'point_shape_param': (15.1, 5, 5.5, 0, ),
            'main_spawn_1': (1, 17, 18, 21, 24, 25, 26, ),
            'main_spawn_2': (23, 27, 28, 29, 14, 22, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        3: TD({
            'id': '20.3',
            'space_id': 20,
            'local_id': 3,
            'point_pos': (-31.8, 7.4, -11, ),
            'point_type': 1,
            'point_shape_param': (7.57, 5, 7.47, 0, ),
            'main_spawn_1': (16, 21, 37, 33, 5, ),
            'main_spawn_2': (32, 34, 35, 13, 30, 31, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        4: TD({
            'id': '20.4',
            'space_id': 20,
            'local_id': 4,
            'point_pos': (12.71, 2.47, -13.75, ),
            'point_type': 1,
            'point_shape_param': (21.2, 4, 10.7, 0, ),
            'main_spawn_1': (42, 6, 41, 37, 20, ),
            'main_spawn_2': (29, 28, 19, 38, 39, 24, 25, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        5: TD({
            'id': '20.5',
            'space_id': 20,
            'local_id': 5,
            'point_pos': (-30.2, 2.47, 11.6, ),
            'point_type': 1,
            'point_shape_param': (6.6, 4, 9.42, 0, ),
            'main_spawn_1': (34, 45, 43, 33, 32, 20, 12, 35, ),
            'main_spawn_2': (46, 29, 27, 31, 15, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
    }), 
    21: TD({
        1: TD({
            'id': '21.1',
            'space_id': 21,
            'local_id': 1,
            'point_pos': (-0.6, 2.1, 1.1, ),
            'point_rot': 0.8,
            'point_type': 1,
            'point_shape_param': (6.5, 4, 6.5, 0, ),
            'main_spawn_1': (8, 11, 12, 13, 14, ),
            'spawn_pos_1': (0, -40, ),
            'spawn_rad_1': 25.0,
            'main_spawn_2': (21, 17, 18, 22, 19, 20, 3, ),
            'spawn_pos_2': (0, 40, ),
            'spawn_rad_2': 25.0,
            'backup_spawn_1': (21, 22, 24, ),
            'backup_spawn_2': (16, 23, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        2: TD({
            'id': '21.2',
            'space_id': 21,
            'local_id': 2,
            'point_pos': (-13.1, 2.1, -20.8, ),
            'point_type': 1,
            'point_shape_param': (10.4, 4, 8.5, 0, ),
            'main_spawn_1': (11, 14, 16, ),
            'spawn_pos_1': (18, -20, ),
            'spawn_rad_1': 14.0,
            'main_spawn_2': (22, 25, 26, 29, 19, ),
            'spawn_pos_2': (-15, 21, ),
            'spawn_rad_2': 13.0,
            'backup_spawn_1': (26, 29, 30, ),
            'backup_spawn_2': (27, 28, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        3: TD({
            'id': '21.3',
            'space_id': 21,
            'local_id': 3,
            'point_pos': (-13.9, 2.1, -1.8, ),
            'point_type': 1,
            'point_shape_param': (8.5, 4, 16.5, 0, ),
            'main_spawn_1': (8, 11, 12, 13, 14, ),
            'spawn_pos_1': (0, -40, ),
            'spawn_rad_1': 25.0,
            'main_spawn_2': (21, 17, 18, 22, 19, 20, 3, ),
            'spawn_pos_2': (0, 40, ),
            'spawn_rad_2': 25.0,
            'backup_spawn_1': (21, 22, 24, ),
            'backup_spawn_2': (16, 23, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        4: TD({
            'id': '21.4',
            'space_id': 21,
            'local_id': 4,
            'point_pos': (15.7, 1.6, 6.2, ),
            'point_type': 1,
            'point_shape_param': (6.2, 3, 12, 0, ),
            'main_spawn_1': (8, 11, 12, 13, 14, ),
            'spawn_pos_1': (0, -40, ),
            'spawn_rad_1': 25.0,
            'main_spawn_2': (21, 17, 18, 22, 19, 20, 3, ),
            'spawn_pos_2': (0, 40, ),
            'spawn_rad_2': 25.0,
            'backup_spawn_1': (21, 22, 24, ),
            'backup_spawn_2': (16, 23, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        5: TD({
            'id': '21.5',
            'space_id': 21,
            'local_id': 5,
            'point_pos': (9.6, 2.1, 21.5, ),
            'point_type': 1,
            'point_shape_param': (18, 4, 6.3, 0, ),
            'main_spawn_1': (16, 31, 32, 27, 14, ),
            'spawn_pos_1': (18, -20, ),
            'spawn_rad_1': 14.0,
            'main_spawn_2': (19, 20, 22, ),
            'spawn_pos_2': (-15, 21, ),
            'spawn_rad_2': 13.0,
            'backup_spawn_1': (22, 33, 34, ),
            'backup_spawn_2': (27, 28, 31, 32, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
    }), 
    32: TD({
        1: TD({
            'id': '32.1',
            'space_id': 32,
            'local_id': 1,
            'point_pos': (6.4, 1.8, -5.88, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (8, 4, 8, 0.35, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'reborn_enemy_threaten_rad': 13.0,
            'reborn_teammate_safe_rad': 1.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 15.0,
        }), 
        2: TD({
            'id': '32.2',
            'space_id': 32,
            'local_id': 2,
            'point_pos': (28.85, 1.8, -29.1, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (8, 4, 8, 0.35, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'reborn_enemy_threaten_rad': 13.0,
            'reborn_teammate_safe_rad': 1.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 15.0,
        }), 
        3: TD({
            'id': '32.3',
            'space_id': 32,
            'local_id': 3,
            'point_pos': (6.52, 1.8, -30.29, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (8, 4, 8, 0.35, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'reborn_enemy_threaten_rad': 13.0,
            'reborn_teammate_safe_rad': 1.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 15.0,
        }), 
        4: TD({
            'id': '32.4',
            'space_id': 32,
            'local_id': 4,
            'point_pos': (29.04, 1.8, -7.08, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (8, 4, 8, 0.35, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'reborn_enemy_threaten_rad': 13.0,
            'reborn_teammate_safe_rad': 1.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 15.0,
        }), 
        5: TD({
            'id': '32.5',
            'space_id': 32,
            'local_id': 5,
            'point_pos': (17.59, 0.18, -18.31, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (5, 2, 5, 0.8, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, ),
            'reborn_enemy_threaten_rad': 13.0,
            'reborn_teammate_safe_rad': 1.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 15.0,
        }), 
    }), 
    33: TD({
        1: TD({
            'id': '33.1',
            'space_id': 33,
            'local_id': 1,
            'point_pos': (15.62, -0.35, -22.62, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (6, 7, 8, 9, 10, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (11, 12, 13, 14, 15, ),
            'backup_spawn_2': (16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        2: TD({
            'id': '33.2',
            'space_id': 33,
            'local_id': 2,
            'point_pos': (-17.34, -0.35, 27.05, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (6, 7, 8, 9, 10, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (11, 12, 13, 14, 15, ),
            'backup_spawn_2': (16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        3: TD({
            'id': '33.3',
            'space_id': 33,
            'local_id': 3,
            'point_pos': (-4.19, -0.35, -22.56, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (6, 7, 8, 9, 10, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (11, 12, 13, 14, 15, ),
            'backup_spawn_2': (16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        4: TD({
            'id': '33.4',
            'space_id': 33,
            'local_id': 4,
            'point_pos': (1.02, -0.35, 26.83, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (6, 7, 8, 9, 10, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (11, 12, 13, 14, 15, ),
            'backup_spawn_2': (16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        5: TD({
            'id': '33.5',
            'space_id': 33,
            'local_id': 5,
            'point_pos': (-1.49, -0.35, 2.32, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (6, 7, 8, 9, 10, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (11, 12, 13, 14, 15, ),
            'backup_spawn_2': (16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        6: TD({
            'id': '33.6',
            'space_id': 33,
            'local_id': 6,
            'point_pos': (-6.9, 1.54, -18.9, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (10.0, 4, 14.0, 1.3, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 6.0,
            'reborn_safe_hi': 6.0,
            'reborn_vis_ang': 90.0,
            'reborn_vis_rad': 20.0,
        }), 
    }), 
    37: TD({
        1: TD({
            'id': '37.1',
            'space_id': 37,
            'local_id': 1,
            'point_pos': (-53.7, 2.6, -36.59, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (6, 7, 8, 9, 10, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (1, 2, 3, 4, 5, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (11, 12, 13, 14, 15, ),
            'backup_spawn_2': (16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        2: TD({
            'id': '37.2',
            'space_id': 37,
            'local_id': 2,
            'point_pos': (-60.25, 2.6, -59.9, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (11, 12, 13, 14, 15, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (1, 2, 3, 4, 5, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (6, 7, 8, 9, 10, ),
            'backup_spawn_2': (16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        3: TD({
            'id': '37.3',
            'space_id': 37,
            'local_id': 3,
            'point_pos': (-62.98, 6.6, -13.02, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (6, 7, 8, 9, 10, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (11, 12, 13, 14, 15, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (16, 17, 18, 19, 20, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        4: TD({
            'id': '37.4',
            'space_id': 37,
            'local_id': 4,
            'point_pos': (-9.7, 2.6, -31.65, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (11, 12, 13, 14, 15, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (6, 7, 8, 9, 10, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, ),
            'backup_spawn_2': (16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
        5: TD({
            'id': '37.5',
            'space_id': 37,
            'local_id': 5,
            'point_pos': (-23.83, 6.6, -19.02, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (3.5, 2, 2, 0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (16, 17, 18, 19, 20, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (11, 12, 13, 14, 15, ),
            'backup_spawn_2': (6, 7, 8, 9, 10, ),
            'reborn_enemy_threaten_rad': 20.0,
            'reborn_teammate_safe_rad': 20.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 60.0,
            'reborn_vis_rad': 60.0,
        }), 
    }), 
    41: TD({
        1: TD({
            'id': '41.1',
            'space_id': 41,
            'local_id': 1,
            'point_pos': (-79.18, 1.3, -56.8, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.0, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, ),
            'backup_spawn_2': (52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        2: TD({
            'id': '41.2',
            'space_id': 41,
            'local_id': 2,
            'point_pos': (-38.65, 5.5, -13.6, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.5, 4.7, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (79, 80, 81, 82, 83, 84, 85, 86, 87, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (79, 80, 81, 82, 83, 84, 85, 86, 87, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, ),
            'backup_spawn_2': (79, 80, 81, 82, 83, 84, 85, 86, 87, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        3: TD({
            'id': '41.3',
            'space_id': 41,
            'local_id': 3,
            'point_pos': (-95.3, 0.3, -29.86, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (9.1, 3, 13.3, 1.8, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 68, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 68, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, ),
            'backup_spawn_2': (25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 68, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        4: TD({
            'id': '41.4',
            'space_id': 41,
            'local_id': 4,
            'point_pos': (-42.25, 1.54, -63.87, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (12.2, 2.0, 6.5, 0.2, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (88, 89, 90, 91, 92, 93, 94, 95, 96, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (97, 98, 99, 100, 101, 102, 103, 104, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, ),
            'backup_spawn_2': (88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        5: TD({
            'id': '41.5',
            'space_id': 41,
            'local_id': 5,
            'point_pos': (-74.56, 5.5, -21.54, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.5, 2.5, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (111, 112, 113, 114, 115, 116, 117, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (105, 106, 107, 108, 109, 110, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (111, 112, 113, 114, 115, 116, 117, 105, 106, 107, 108, 109, 110, ),
            'backup_spawn_2': (111, 112, 113, 114, 115, 116, 117, 105, 106, 107, 108, 109, 110, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
    }), 
    46: TD({
        1: TD({
            'id': '46.1',
            'space_id': 46,
            'local_id': 1,
            'point_pos': (6.52, 1.8, -30.29, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (4, 4, 4, 0.35, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 30.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
        2: TD({
            'id': '46.2',
            'space_id': 46,
            'local_id': 2,
            'point_pos': (29.04, 1.8, -7.08, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (4, 4, 4, 0.35, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 30.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
        3: TD({
            'id': '46.3',
            'space_id': 46,
            'local_id': 3,
            'point_pos': (6.4, 1.8, -4.88, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (4, 4, 4, 0.35, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 30.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
        4: TD({
            'id': '46.4',
            'space_id': 46,
            'local_id': 4,
            'point_pos': (28.85, 1.8, -29.1, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (4, 4, 4, 0.35, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 30.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
        5: TD({
            'id': '46.5',
            'space_id': 46,
            'local_id': 5,
            'point_pos': (17.59, 1.8, -18.31, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (4, 4, 4, 0.35, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ),
            'reborn_enemy_threaten_rad': 30.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
    }), 
    53: TD({
        1: TD({
            'id': '53.1',
            'space_id': 53,
            'local_id': 1,
            'point_pos': (9.646, 6.7, -40.757, ),
            'point_rot': -0.182,
            'point_type': 1,
            'point_shape_param': (7.0, 5.0, 12.5, 0.1, ),
            'param_edge_gap': (1, 1, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (17.09, -6.51, ),
            'spawn_rad_1': 20.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (27.56, -70, ),
            'spawn_rad_2': 20.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 20, 21, 22, 23, 24, 25, 26, ),
            'backup_spawn_2': (7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, ),
            'reborn_enemy_threaten_rad': 45.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 50.0,
        }), 
        2: TD({
            'id': '53.2',
            'space_id': 53,
            'local_id': 2,
            'point_pos': (7.714, 6.133, -16.139, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (11.3, 5.0, 7.8, 0.05, ),
            'param_edge_gap': (1.0, 1.6, 1.0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (17.09, -6.51, ),
            'spawn_rad_1': 20.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (27.56, -70, ),
            'spawn_rad_2': 20.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 20, 21, 22, 23, 24, 25, 26, ),
            'backup_spawn_2': (7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, ),
            'reborn_enemy_threaten_rad': 45.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 50.0,
        }), 
        3: TD({
            'id': '53.3',
            'space_id': 53,
            'local_id': 3,
            'point_pos': (29.182, 4.677, -49.758, ),
            'point_rot': -0.341,
            'point_type': 1,
            'point_shape_param': (8.2, 2.0, 10.8, 0.01, ),
            'param_edge_gap': (1, 1, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (17.09, -6.51, ),
            'spawn_rad_1': 20.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (27.56, -70, ),
            'spawn_rad_2': 20.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 20, 21, 22, 23, 24, 25, 26, ),
            'backup_spawn_2': (7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, ),
            'reborn_enemy_threaten_rad': 45.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 50.0,
        }), 
        4: TD({
            'id': '53.4',
            'space_id': 53,
            'local_id': 4,
            'point_pos': (18.664, 5.588, -66.872, ),
            'point_rot': -0.33,
            'point_type': 1,
            'point_shape_param': (7.5, 4.0, 13.0, 0.1, ),
            'param_edge_gap': (1.0, 1.5, 1.0, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (17.09, -6.51, ),
            'spawn_rad_1': 20.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (27.56, -70, ),
            'spawn_rad_2': 20.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 20, 21, 22, 23, 24, 25, 26, ),
            'backup_spawn_2': (7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, ),
            'reborn_enemy_threaten_rad': 45.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 50.0,
        }), 
        5: TD({
            'id': '53.5',
            'space_id': 53,
            'local_id': 5,
            'point_pos': (25.661, 5.609, -38.059, ),
            'point_rot': -0.17,
            'point_type': 1,
            'point_shape_param': (7.5, 4.0, 12.0, 0.1, ),
            'param_edge_gap': (1, 1, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (17.09, -6.51, ),
            'spawn_rad_1': 20.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (27.56, -70, ),
            'spawn_rad_2': 20.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 20, 21, 22, 23, 24, 25, 26, ),
            'backup_spawn_2': (7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, ),
            'reborn_enemy_threaten_rad': 45.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 50.0,
        }), 
    }), 
    54: TD({
        1: TD({
            'id': '54.1',
            'space_id': 54,
            'local_id': 1,
            'point_pos': (31.69, 2.0, -29.76, ),
            'point_rot': 0.0,
            'point_type': 2,
            'point_shape_param': (4.5, 1.0, 2.0, 0.3, ),
            'param_edge_gap': (2, 4, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 25.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
        2: TD({
            'id': '54.2',
            'space_id': 54,
            'local_id': 2,
            'point_pos': (51.66, 1.2, -56.27, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.0, 1.0, 6.0, 0.3, ),
            'param_edge_gap': (2, 4, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 25.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
        3: TD({
            'id': '54.3',
            'space_id': 54,
            'local_id': 3,
            'point_pos': (7.33, 2.0, -14.21, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.0, 2.0, 6.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 25.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
        4: TD({
            'id': '54.4',
            'space_id': 54,
            'local_id': 4,
            'point_pos': (62.72, 2.0, -17.7, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.0, 1.0, 4.0, 0.3, ),
            'param_edge_gap': (2, 4, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 25.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
        5: TD({
            'id': '54.5',
            'space_id': 54,
            'local_id': 5,
            'point_pos': (38.16, 2.0, -16.92, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (10.0, 2.0, 10.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 25.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
        6: TD({
            'id': '54.6',
            'space_id': 54,
            'local_id': 6,
            'point_pos': (24.07, 2.0, -43.57, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.0, 2.0, 6.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 25.0,
            'reborn_teammate_safe_rad': 8.0,
            'reborn_safe_hi': 5.0,
            'reborn_vis_ang': 160.0,
            'reborn_vis_rad': 30.0,
        }), 
    }), 
    77: TD({
        1: TD({
            'id': '77.1',
            'space_id': 77,
            'local_id': 1,
            'point_pos': (-79.18, 1.3, -56.8, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.0, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'backup_spawn_2': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        2: TD({
            'id': '77.2',
            'space_id': 77,
            'local_id': 2,
            'point_pos': (-38.65, 5.5, -13.6, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.5, 4.7, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'backup_spawn_2': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        3: TD({
            'id': '77.3',
            'space_id': 77,
            'local_id': 3,
            'point_pos': (-95.3, 0.3, -29.86, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (9.1, 3, 13.3, 1.8, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'backup_spawn_2': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        4: TD({
            'id': '77.4',
            'space_id': 77,
            'local_id': 4,
            'point_pos': (-42.25, 1.54, -63.87, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (12.2, 2.0, 6.5, 0.2, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'backup_spawn_2': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        5: TD({
            'id': '77.5',
            'space_id': 77,
            'local_id': 5,
            'point_pos': (-74.56, 5.5, -21.54, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.5, 2.5, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'backup_spawn_2': (13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
    }), 
    78: TD({
        1: TD({
            'id': '78.1',
            'space_id': 78,
            'local_id': 1,
            'point_pos': (-79.18, 1.3, -56.8, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.0, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        2: TD({
            'id': '78.2',
            'space_id': 78,
            'local_id': 2,
            'point_pos': (-38.65, 5.5, -13.6, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.5, 4.7, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        3: TD({
            'id': '78.3',
            'space_id': 78,
            'local_id': 3,
            'point_pos': (-95.3, 0.3, -29.86, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (9.1, 3, 13.3, 1.8, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        4: TD({
            'id': '78.4',
            'space_id': 78,
            'local_id': 4,
            'point_pos': (-42.25, 1.54, -63.87, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (12.2, 2.0, 6.5, 0.2, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        5: TD({
            'id': '78.5',
            'space_id': 78,
            'local_id': 5,
            'point_pos': (-74.56, 5.5, -21.54, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.5, 2.5, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
    }), 
    79: TD({
        1: TD({
            'id': '79.1',
            'space_id': 79,
            'local_id': 1,
            'point_pos': (-79.18, 1.3, -56.8, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.0, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        2: TD({
            'id': '79.2',
            'space_id': 79,
            'local_id': 2,
            'point_pos': (-38.65, 5.5, -13.6, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.5, 4.7, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        3: TD({
            'id': '79.3',
            'space_id': 79,
            'local_id': 3,
            'point_pos': (-95.3, 0.3, -29.86, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (9.1, 3, 13.3, 1.8, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        4: TD({
            'id': '79.4',
            'space_id': 79,
            'local_id': 4,
            'point_pos': (-42.25, 1.54, -63.87, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (12.2, 2.0, 6.5, 0.2, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        5: TD({
            'id': '79.5',
            'space_id': 79,
            'local_id': 5,
            'point_pos': (-74.56, 5.5, -21.54, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.5, 2.5, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
    }), 
    80: TD({
        1: TD({
            'id': '80.1',
            'space_id': 80,
            'local_id': 1,
            'point_pos': (-79.18, 1.3, -56.8, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.0, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        2: TD({
            'id': '80.2',
            'space_id': 80,
            'local_id': 2,
            'point_pos': (-38.65, 5.5, -13.6, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.5, 4.7, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        3: TD({
            'id': '80.3',
            'space_id': 80,
            'local_id': 3,
            'point_pos': (-95.3, 0.3, -29.86, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (9.1, 3, 13.3, 1.8, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        4: TD({
            'id': '80.4',
            'space_id': 80,
            'local_id': 4,
            'point_pos': (-42.25, 1.54, -63.87, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (12.2, 2.0, 6.5, 0.2, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        5: TD({
            'id': '80.5',
            'space_id': 80,
            'local_id': 5,
            'point_pos': (-74.56, 5.5, -21.54, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.5, 2.5, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
    }), 
    81: TD({
        1: TD({
            'id': '81.1',
            'space_id': 81,
            'local_id': 1,
            'point_pos': (-79.18, 1.3, -56.8, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.0, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        2: TD({
            'id': '81.2',
            'space_id': 81,
            'local_id': 2,
            'point_pos': (-38.65, 5.5, -13.6, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.5, 4.7, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        3: TD({
            'id': '81.3',
            'space_id': 81,
            'local_id': 3,
            'point_pos': (-95.3, 0.3, -29.86, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (9.1, 3, 13.3, 1.8, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        4: TD({
            'id': '81.4',
            'space_id': 81,
            'local_id': 4,
            'point_pos': (-42.25, 1.54, -63.87, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (12.2, 2.0, 6.5, 0.2, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        5: TD({
            'id': '81.5',
            'space_id': 81,
            'local_id': 5,
            'point_pos': (-74.56, 5.5, -21.54, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.5, 2.5, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
    }), 
    82: TD({
        1: TD({
            'id': '82.1',
            'space_id': 82,
            'local_id': 1,
            'point_pos': (-79.18, 1.3, -56.8, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.0, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        2: TD({
            'id': '82.2',
            'space_id': 82,
            'local_id': 2,
            'point_pos': (-38.65, 5.5, -13.6, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (13.0, 2.5, 4.7, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        3: TD({
            'id': '82.3',
            'space_id': 82,
            'local_id': 3,
            'point_pos': (-95.3, 0.3, -29.86, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (9.1, 3, 13.3, 1.8, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        4: TD({
            'id': '82.4',
            'space_id': 82,
            'local_id': 4,
            'point_pos': (-42.25, 1.54, -63.87, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (12.2, 2.0, 6.5, 0.2, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
        5: TD({
            'id': '82.5',
            'space_id': 82,
            'local_id': 5,
            'point_pos': (-74.56, 5.5, -21.54, ),
            'point_rot': 0.0,
            'point_type': 1,
            'point_shape_param': (6.5, 2.5, 12.0, 0.3, ),
            'param_edge_gap': (1, 2, 1, ),
            'main_spawn_1': (1, 2, 3, 4, 5, 6, ),
            'spawn_pos_1': (5.8, -29.9, ),
            'spawn_rad_1': 12.0,
            'main_spawn_2': (7, 8, 9, 10, 11, 12, ),
            'spawn_pos_2': (26.8, -4.47, ),
            'spawn_rad_2': 12.0,
            'backup_spawn_1': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'backup_spawn_2': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, ),
            'reborn_enemy_threaten_rad': 15.0,
            'reborn_teammate_safe_rad': 10.0,
            'reborn_safe_hi': 10.0,
            'reborn_vis_ang': 30.0,
            'reborn_vis_rad': 25.0,
        }), 
    }), 
}
