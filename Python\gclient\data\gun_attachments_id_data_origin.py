# -*- coding: utf-8 -*-
# flake8: noqa
# generated by: excel_to_data.py
# generated from 8-枪械配件表.xlsx, sheetname:配件属性表, post_process.py, SplitGunAttachmentsData
from taggeddict import taggeddict as TD
data = {
    1: TD({
        1: TD({
            1: 10101, 
        }), 
        2: TD({
            1: 10201, 
            2: 10202, 
            3: 10203, 
            4: 10204, 
            6: 10206, 
        }), 
        3: TD({
            1: 10301, 
            10: 10310, 
            11: 10311, 
            12: 10312, 
            13: 10313, 
            2: 10302, 
            3: 10303, 
            4: 10304, 
            5: 10305, 
            6: 10306, 
            7: 10307, 
            8: 10308, 
            9: 10309, 
        }), 
        4: TD({
            1: 10401, 
            2: 10402, 
            3: 10403, 
            4: 10404, 
            5: 10405, 
            6: 10406, 
            7: 10407, 
            8: 10408, 
            9: 10409, 
        }), 
        5: TD({
            1: 10501, 
            2: 10502, 
            3: 10503, 
            4: 10504, 
            5: 10505, 
        }), 
        6: TD({
            1: 10601, 
            2: 10602, 
            3: 10603, 
            4: 10604, 
            5: 10605, 
        }), 
        7: TD({
            1: 10701, 
            2: 10702, 
            3: 10703, 
            4: 10704, 
            5: 10705, 
            6: 10706, 
            7: 10707, 
        }), 
        8: TD({
            1: 10801, 
            10: 10810, 
            11: 10811, 
            12: 10812, 
            2: 10802, 
            3: 10803, 
            4: 10804, 
            5: 10805, 
            6: 10806, 
            7: 10807, 
            8: 10808, 
            9: 10809, 
        }), 
        9: TD({
            1: 10901, 
            2: 10902, 
            3: 10903, 
            4: 10904, 
        }), 
    }), 
    10: TD({
        1: TD({
            1: 100101, 
        }), 
        2: TD({
            1: 100201, 
            2: 100202, 
            3: 100203, 
            4: 100204, 
        }), 
        3: TD({
            1: 100301, 
            10: 100310, 
            11: 100311, 
            12: 100312, 
            13: 100313, 
            14: 100314, 
            15: 1003010001, 
            16: 1003110001, 
            2: 100302, 
            3: 100303, 
            4: 100304, 
            5: 100305, 
            6: 100306, 
            7: 100307, 
            8: 100308, 
            9: 100309, 
        }), 
        4: TD({
            1: 100401, 
            2: 100402, 
            3: 100403, 
            4: 100404, 
            5: 100405, 
        }), 
        5: TD({
            1: 100501, 
            2: 100503, 
            3: 100502, 
        }), 
        6: TD({
            1: 100601, 
            2: 100602, 
            3: 100603, 
            4: 100604, 
            5: 100605, 
        }), 
        7: TD({
            1: 100701, 
            2: 100702, 
            3: 100703, 
            4: 100704, 
            5: 100705, 
            6: 100706, 
            7: 100707, 
        }), 
        8: TD({
            1: 100801, 
            2: 100802, 
            3: 100803, 
            4: 100804, 
            5: 100805, 
            6: 100806, 
            7: 100807, 
            8: 100808, 
        }), 
        9: TD({
            1: 100901, 
            2: 100902, 
            3: 100903, 
            4: 100904, 
        }), 
    }), 
    13: TD({
        1: TD({
            1: 130101, 
        }), 
        2: TD({
            1: 130201, 
            2: 130202, 
            3: 130203, 
            4: 130204, 
        }), 
        3: TD({
            1: 130301, 
            10: 130310, 
            11: 130311, 
            12: 130312, 
            13: 130313, 
            2: 130302, 
            3: 130303, 
            4: 130304, 
            5: 130305, 
            6: 130306, 
            7: 130307, 
            8: 130308, 
            9: 130309, 
        }), 
        4: TD({
            1: 130401, 
            2: 130402, 
            3: 130403, 
        }), 
        5: TD({
            1: 130501, 
            2: 130502, 
            3: 130503, 
            4: 130504, 
        }), 
        6: TD({
            1: 130601, 
        }), 
        7: TD({
            1: 130701, 
        }), 
        8: TD({
            1: 130801, 
            2: 130802, 
            3: 130803, 
            4: 130804, 
            5: 130805, 
            6: 130806, 
        }), 
    }), 
    14: TD({
        1: TD({
            1: 140101, 
        }), 
        2: TD({
            1: 140201, 
            2: 140202, 
            3: 140203, 
            4: 140204, 
        }), 
        3: TD({
            1: 140301, 
            10: 140310, 
            11: 140311, 
            12: 140312, 
            13: 140313, 
            14: 1403110001, 
            2: 140302, 
            4: 140304, 
            5: 140305, 
            6: 140306, 
            7: 140307, 
            8: 140308, 
            9: 140309, 
        }), 
        4: TD({
            1: 140401, 
            2: 140402, 
            3: 140403, 
            4: 140404, 
        }), 
        5: TD({
            1: 140501, 
            2: 140502, 
            3: 140503, 
            4: 140504, 
            5: 140505, 
        }), 
        6: TD({
            1: 140601, 
        }), 
        7: TD({
            1: 140701, 
            2: 140702, 
            3: 140703, 
            4: 140704, 
            5: 140705, 
            6: 140706, 
            7: 140707, 
        }), 
        8: TD({
            1: 140801, 
            2: 140802, 
            3: 140803, 
            4: 140804, 
            5: 140805, 
            6: 140806, 
            7: 140807, 
            8: 140808, 
        }), 
    }), 
    15: TD({
        1: TD({
            1: 150101, 
        }), 
        10: TD({
            1: 151001, 
            2: 151002, 
            3: 151003, 
        }), 
        12: TD({
            1: 151201, 
        }), 
        2: TD({
            1: 150201, 
            2: 150202, 
            3: 150203, 
            4: 150204, 
        }), 
        3: TD({
            1: 150301, 
            10: 150310, 
            11: 150311, 
            12: 150312, 
            13: 150313, 
            2: 150302, 
            3: 150303, 
            4: 150304, 
            5: 150305, 
            6: 150306, 
            7: 150307, 
            8: 150308, 
            9: 150309, 
        }), 
        5: TD({
            1: 150501, 
            2: 150502, 
            3: 150503, 
        }), 
        6: TD({
            1: 150601, 
            2: 150602, 
            3: 150603, 
        }), 
        8: TD({
            1: 150801, 
            2: 150802, 
            3: 150803, 
            4: 150804, 
            5: 150805, 
        }), 
        9: TD({
            1: 150901, 
            2: 150902, 
            3: 150903, 
            4: 150904, 
        }), 
    }), 
    16: TD({
        1: TD({
            1: 160101, 
        }), 
        13: TD({
            13: 161301, 
        }), 
        2: TD({
            1: 160201, 
            2: 160202, 
            3: 160203, 
            4: 160204, 
        }), 
        3: TD({
            1: 160301, 
            10: 160310, 
            11: 160311, 
            12: 160312, 
            2: 160302, 
            4: 160304, 
            5: 160305, 
            6: 160306, 
            7: 160307, 
            8: 160308, 
            9: 160309, 
        }), 
        4: TD({
            1: 160401, 
            2: 160402, 
            3: 160403, 
            4: 160404, 
        }), 
        5: TD({
            1: 160501, 
            2: 160502, 
            3: 160503, 
        }), 
        6: TD({
            1: 160601, 
        }), 
        7: TD({
            1: 160701, 
            2: 160702, 
            3: 160703, 
            4: 160704, 
            5: 160705, 
            6: 160706, 
            7: 160707, 
        }), 
        8: TD({
            1: 160801, 
            2: 160802, 
            3: 160803, 
            4: 160804, 
            5: 160805, 
            6: 160806, 
            7: 160807, 
            8: 160808, 
        }), 
    }), 
    17: TD({
        1: TD({
            1: 170101, 
        }), 
        2: TD({
            1: 170201, 
            2: 170202, 
            3: 170203, 
            4: 170204, 
        }), 
        3: TD({
            1: 170301, 
            10: 170310, 
            11: 170311, 
            12: 170312, 
            2: 170302, 
            4: 170304, 
            5: 170305, 
            6: 170306, 
            7: 170307, 
            8: 170308, 
            9: 170309, 
        }), 
        4: TD({
            1: 170401, 
            2: 170402, 
            3: 170403, 
            4: 170404, 
        }), 
        5: TD({
            1: 170501, 
            2: 170502, 
            3: 170503, 
        }), 
        6: TD({
            1: 170601, 
        }), 
        7: TD({
            1: 170701, 
            2: 170702, 
            3: 170703, 
            4: 170704, 
            5: 170705, 
            6: 170706, 
            7: 170707, 
        }), 
        8: TD({
            1: 170801, 
            2: 170802, 
            3: 170803, 
            4: 170804, 
            5: 170805, 
            6: 170806, 
            7: 170807, 
            8: 170808, 
        }), 
    }), 
    18: TD({
        1: TD({
            1: 180101, 
        }), 
        13: TD({
            1: 181301, 
            2: 181302, 
            3: 181303, 
        }), 
        2: TD({
            1: 180201, 
            2: 180202, 
        }), 
        3: TD({
            1: 180301, 
            10: 180310, 
            11: 180311, 
            12: 180312, 
            13: 180313, 
            2: 180302, 
            3: 180303, 
            4: 180304, 
            5: 180305, 
            6: 180306, 
            7: 180307, 
            8: 180308, 
            9: 180309, 
        }), 
        4: TD({
            1: 180401, 
            3: 180403, 
        }), 
        5: TD({
            1: 180501, 
            2: 180502, 
        }), 
        7: TD({
            1: 180701, 
            2: 180702, 
        }), 
        8: TD({
            1: 180801, 
            2: 180802, 
            3: 180803, 
            4: 180804, 
            5: 180805, 
            6: 180806, 
            7: 180807, 
            8: 180808, 
        }), 
        9: TD({
            1: 180901, 
            2: 180902, 
            3: 180903, 
            4: 180904, 
        }), 
    }), 
    19: TD({
        1: TD({
            1: 190101, 
        }), 
        10: TD({
            1: 191001, 
        }), 
        2: TD({
            1: 190201, 
        }), 
        3: TD({
            1: 190301, 
            2: 190302, 
            3: 190303, 
            4: 190304, 
        }), 
        5: TD({
            1: 190501, 
        }), 
        8: TD({
            1: 190801, 
        }), 
    }), 
    2: TD({
        1: TD({
            1: 20101, 
        }), 
        2: TD({
            1: 20201, 
            2: 20202, 
            3: 20203, 
            4: 20204, 
            8: 20208, 
        }), 
        3: TD({
            1: 20301, 
            10: 20310, 
            11: 20311, 
            12: 20312, 
            2: 20302, 
            3: 20303, 
            4: 20304, 
            5: 20305, 
            6: 20306, 
            7: 20307, 
            8: 20308, 
            9: 20309, 
        }), 
        4: TD({
            1: 20401, 
            2: 20402, 
            3: 20403, 
            5: 20405, 
        }), 
        5: TD({
            1: 20501, 
            2: 20502, 
            3: 20503, 
        }), 
        6: TD({
            1: 20601, 
            2: 20602, 
        }), 
        7: TD({
            1: 20701, 
            2: 20702, 
            3: 20703, 
            4: 20704, 
            5: 20705, 
            6: 20706, 
            7: 20707, 
        }), 
        8: TD({
            1: 20801, 
            10: 20810, 
            11: 20811, 
            2: 20802, 
            3: 20803, 
            4: 20804, 
            5: 20805, 
            6: 20806, 
            7: 20807, 
            8: 20808, 
            9: 20809, 
        }), 
        9: TD({
            1: 20901, 
            2: 20902, 
            3: 20903, 
            4: 20904, 
        }), 
    }), 
    20: TD({
        1: TD({
            1: 200101, 
        }), 
        13: TD({
            1: 201301, 
            2: 201302, 
            3: 201303, 
        }), 
        2: TD({
            1: 200201, 
            2: 200202, 
            3: 200203, 
        }), 
        3: TD({
            1: 200301, 
            10: 200310, 
            11: 200311, 
            12: 200312, 
            13: 200313, 
            2: 200302, 
            3: 200303, 
            4: 200304, 
            5: 200305, 
            6: 200306, 
            7: 200307, 
            8: 200308, 
            9: 200309, 
        }), 
        4: TD({
            1: 200401, 
            2: 200402, 
            3: 200403, 
        }), 
        5: TD({
            1: 200501, 
            2: 200502, 
            3: 200503, 
            4: 200504, 
        }), 
        7: TD({
            1: 200701, 
            2: 200702, 
            3: 200703, 
        }), 
        8: TD({
            1: 200801, 
            2: 200802, 
            3: 200803, 
            4: 200804, 
            5: 200805, 
            6: 200806, 
        }), 
        9: TD({
            1: 200901, 
            2: 200902, 
            3: 200903, 
            4: 200904, 
        }), 
    }), 
    21: TD({
        1: TD({
            1: 210101, 
        }), 
        2: TD({
            1: 210201, 
        }), 
        3: TD({
            1: 210301, 
        }), 
        5: TD({
            1: 210501, 
        }), 
        7: TD({
            1: 210701, 
        }), 
    }), 
    22: TD({
        1: TD({
            1: 220101, 
        }), 
        2: TD({
            1: 220201, 
        }), 
        3: TD({
            1: 220301, 
            2: 220302, 
            3: 220303, 
            4: 220304, 
        }), 
        5: TD({
            1: 220501, 
            2: 220502, 
        }), 
    }), 
    23: TD({
        1: TD({
            1: 230101, 
        }), 
        2: TD({
            1: 230201, 
            2: 230202, 
            3: 230203, 
            4: 230204, 
        }), 
        3: TD({
            1: 230301, 
            10: 230310, 
            11: 230311, 
            12: 230312, 
            13: 230313, 
            14: 2303100001, 
            2: 230302, 
            3: 230303, 
            4: 230304, 
            5: 230305, 
            6: 230306, 
            7: 230307, 
            8: 230308, 
            9: 230309, 
        }), 
        4: TD({
            1: 230401, 
            2: 230402, 
            3: 230403, 
            4: 230404, 
        }), 
        5: TD({
            1: 230501, 
            2: 230502, 
            3: 230503, 
            4: 230504, 
        }), 
        6: TD({
            1: 230601, 
            2: 230602, 
            3: 230603, 
        }), 
        7: TD({
            1: 230701, 
            2: 230702, 
            3: 230703, 
            4: 230704, 
            5: 230705, 
            6: 230706, 
            7: 230707, 
        }), 
        8: TD({
            1: 230801, 
            2: 230802, 
            3: 230803, 
            4: 230804, 
            5: 230805, 
            6: 230806, 
            7: 230807, 
            8: 230808, 
        }), 
        9: TD({
            1: 230901, 
            2: 230902, 
            3: 230903, 
            4: 230904, 
        }), 
    }), 
    24: TD({
        1: TD({
            1: 240101, 
        }), 
        2: TD({
            1: 240201, 
            2: 240202, 
            3: 240203, 
            4: 240204, 
        }), 
        3: TD({
            1: 240301, 
            10: 240310, 
            11: 240311, 
            12: 240312, 
            2: 240302, 
            3: 240303, 
            4: 240304, 
            5: 240305, 
            6: 240306, 
            7: 240307, 
            8: 240308, 
            9: 240309, 
        }), 
        4: TD({
            1: 240401, 
            2: 240402, 
            3: 240403, 
            4: 240404, 
        }), 
        5: TD({
            1: 240501, 
        }), 
        6: TD({
            1: 240601, 
            2: 240602, 
            3: 240603, 
            4: 240604, 
        }), 
        7: TD({
            1: 240701, 
        }), 
        8: TD({
            1: 240801, 
            2: 240802, 
            3: 240803, 
            4: 240804, 
            5: 240805, 
            6: 240806, 
            7: 240807, 
            8: 240808, 
        }), 
        9: TD({
            1: 240901, 
            2: 240902, 
            3: 240903, 
            4: 240904, 
        }), 
    }), 
    25: TD({
        1: TD({
            1: 250101, 
        }), 
        2: TD({
            1: 250201, 
            2: 250202, 
            3: 250203, 
            4: 250204, 
        }), 
        3: TD({
            1: 250301, 
            10: 250310, 
            11: 250311, 
            12: 250312, 
            13: 250313, 
            2: 250302, 
            3: 250303, 
            4: 250304, 
            5: 250305, 
            6: 250306, 
            7: 250307, 
            8: 250308, 
            9: 250309, 
        }), 
        4: TD({
            1: 250401, 
            2: 250402, 
            3: 250403, 
        }), 
        5: TD({
            1: 250501, 
            2: 250502, 
            3: 250503, 
            4: 250504, 
            5: 250505, 
            6: 250506, 
        }), 
        6: TD({
            1: 250601, 
        }), 
        7: TD({
            1: 250701, 
            2: 250702, 
            3: 250703, 
            4: 250704, 
            5: 250705, 
            6: 250706, 
            7: 250707, 
        }), 
        8: TD({
            1: 250801, 
        }), 
    }), 
    26: TD({
        1: TD({
            1: 260101, 
        }), 
        2: TD({
            1: 260201, 
        }), 
        3: TD({
            1: 260301, 
        }), 
        5: TD({
            1: 260501, 
        }), 
    }), 
    27: TD({
        1: TD({
            1: 270101, 
        }), 
        2: TD({
            1: 270201, 
            2: 270202, 
            3: 270203, 
            4: 270204, 
        }), 
        3: TD({
            1: 270301, 
            10: 270310, 
            11: 270311, 
            12: 270312, 
            13: 270313, 
            2: 270302, 
            3: 270303, 
            4: 270304, 
            5: 270305, 
            6: 270306, 
            7: 270307, 
            8: 270308, 
            9: 270309, 
        }), 
        4: TD({
            1: 270401, 
            2: 270402, 
            3: 270403, 
            4: 270404, 
        }), 
        5: TD({
            1: 270501, 
            2: 270502, 
            3: 270503, 
            4: 270504, 
            5: 270505, 
        }), 
        6: TD({
            1: 270601, 
        }), 
        7: TD({
            1: 270701, 
            2: 270702, 
            3: 270703, 
            4: 270704, 
            5: 270705, 
            6: 270706, 
            7: 270707, 
        }), 
        8: TD({
            1: 270801, 
            2: 270802, 
            3: 270803, 
            4: 270804, 
            5: 270805, 
            6: 270806, 
            7: 270807, 
            8: 270808, 
        }), 
    }), 
    28: TD({
        1: TD({
            1: 280101, 
        }), 
        2: TD({
            1: 280201, 
            2: 280202, 
            3: 280203, 
            4: 280204, 
        }), 
        3: TD({
            1: 280301, 
            10: 280310, 
            11: 280311, 
            12: 280312, 
            13: 280313, 
            2: 280302, 
            3: 280303, 
            4: 280304, 
            5: 280305, 
            6: 280306, 
            7: 280307, 
            8: 280308, 
            9: 280309, 
        }), 
        4: TD({
            1: 280401, 
            2: 280402, 
            3: 280403, 
        }), 
        5: TD({
            1: 280501, 
            2: 280502, 
            3: 280503, 
        }), 
        6: TD({
            1: 280601, 
        }), 
        7: TD({
            1: 280701, 
            2: 280702, 
            3: 280703, 
        }), 
        8: TD({
            1: 280801, 
            2: 280802, 
            3: 280803, 
            4: 280804, 
            5: 280805, 
        }), 
    }), 
    29: TD({
        1: TD({
            1: 290101, 
        }), 
        2: TD({
            1: 290201, 
            2: 290202, 
            3: 290203, 
            4: 290204, 
            5: 290205, 
        }), 
        3: TD({
            1: 290301, 
            10: 290310, 
            11: 290311, 
            12: 290312, 
            13: 290313, 
            2: 290302, 
            3: 290303, 
            4: 290304, 
            5: 290305, 
            6: 290306, 
            7: 290307, 
            8: 290308, 
            9: 290309, 
        }), 
        4: TD({
            1: 290401, 
            2: 290402, 
            3: 290403, 
            4: 290404, 
        }), 
        5: TD({
            1: 290501, 
            2: 290502, 
            3: 290503, 
        }), 
        7: TD({
            1: 290701, 
            2: 290702, 
            3: 290703, 
            4: 290704, 
            5: 290705, 
            6: 290706, 
            7: 290707, 
        }), 
        8: TD({
            1: 290801, 
            2: 290802, 
            3: 290803, 
            4: 290804, 
            5: 290805, 
            6: 290806, 
            7: 290807, 
            8: 290808, 
        }), 
        9: TD({
            1: 290901, 
            2: 290902, 
            3: 290903, 
            4: 290904, 
        }), 
    }), 
    30: TD({
        1: TD({
            1: 300101, 
        }), 
        10: TD({
            1: 301001, 
            2: 301002, 
            3: 301003, 
        }), 
        12: TD({
            1: 301201, 
            2: 301202, 
            3: 301203, 
        }), 
        2: TD({
            1: 300201, 
            2: 300202, 
            3: 300203, 
            4: 300204, 
            5: 300205, 
        }), 
        3: TD({
            1: 300301, 
            10: 300310, 
            11: 300311, 
            12: 300312, 
            13: 300313, 
            2: 300302, 
            3: 300303, 
            4: 300304, 
            5: 300305, 
            6: 300306, 
            7: 300307, 
            8: 300308, 
            9: 300309, 
        }), 
        5: TD({
            1: 300501, 
            2: 300502, 
            3: 300503, 
            4: 300504, 
        }), 
        6: TD({
            1: 300601, 
            2: 300602, 
            3: 300603, 
            4: 300604, 
        }), 
        8: TD({
            1: 300801, 
            2: 300802, 
            3: 300803, 
            4: 300804, 
            5: 300805, 
            6: 300806, 
            7: 300807, 
        }), 
    }), 
    31: TD({
        1: TD({
            1: 310101, 
        }), 
        2: TD({
            1: 310201, 
            2: 310202, 
            3: 310203, 
            4: 310204, 
        }), 
        3: TD({
            1: 310301, 
            10: 310310, 
            11: 310311, 
            12: 310312, 
            13: 310313, 
            2: 310302, 
            3: 310303, 
            4: 310304, 
            5: 310305, 
            6: 310306, 
            7: 310307, 
            8: 310308, 
            9: 310309, 
        }), 
        4: TD({
            1: 310401, 
            2: 310402, 
            3: 310403, 
            4: 310404, 
            5: 310405, 
        }), 
        5: TD({
            1: 310501, 
            2: 310502, 
            3: 310503, 
            4: 310504, 
            5: 310505, 
            6: 310506, 
        }), 
        6: TD({
            1: 310601, 
        }), 
        7: TD({
            1: 310701, 
            2: 310702, 
            3: 310703, 
            4: 310704, 
            5: 310705, 
            6: 310706, 
            7: 310707, 
        }), 
        8: TD({
            1: 310801, 
            2: 310802, 
            3: 310803, 
            4: 310804, 
            5: 310805, 
            6: 310806, 
            7: 310807, 
            8: 310808, 
        }), 
    }), 
    32: TD({
        1: TD({
            1: 320101, 
        }), 
        2: TD({
            1: 320201, 
            2: 320202, 
            3: 320203, 
            4: 320204, 
        }), 
        3: TD({
            1: 320301, 
            10: 320310, 
            11: 320311, 
            12: 320312, 
            13: 320313, 
            14: 3203110001, 
            2: 320302, 
            3: 320303, 
            4: 320304, 
            5: 320305, 
            6: 320306, 
            7: 320307, 
            8: 320308, 
            9: 320309, 
        }), 
        4: TD({
            1: 320401, 
            2: 320402, 
            3: 320403, 
            4: 320404, 
            5: 320405, 
        }), 
        5: TD({
            1: 320501, 
            2: 320502, 
            3: 320503, 
            4: 320504, 
            5: 320505, 
        }), 
        6: TD({
            1: 320601, 
        }), 
        7: TD({
            1: 320701, 
            2: 320702, 
            3: 320703, 
            4: 320704, 
            5: 320705, 
            6: 320706, 
            7: 320707, 
        }), 
        8: TD({
            1: 320801, 
            10: 320810, 
            11: 320811, 
            2: 320802, 
            3: 320803, 
            4: 320804, 
            5: 320805, 
            6: 320806, 
            7: 320807, 
            8: 320808, 
            9: 320809, 
        }), 
    }), 
    33: TD({
        1: TD({
            1: 330101, 
        }), 
        2: TD({
            1: 330201, 
            2: 330202, 
            3: 330203, 
        }), 
        3: TD({
            1: 330301, 
            10: 330310, 
            11: 330311, 
            12: 330312, 
            13: 330313, 
            14: 330314, 
            15: 3303140001, 
            2: 330302, 
            3: 330303, 
            4: 330304, 
            5: 330305, 
            6: 330306, 
            7: 330307, 
            8: 330308, 
            9: 330309, 
        }), 
        4: TD({
            1: 330401, 
            2: 330402, 
            3: 330403, 
        }), 
        5: TD({
            1: 330501, 
            2: 330502, 
        }), 
        8: TD({
            1: 330801, 
            2: 330802, 
            3: 330803, 
            4: 330804, 
            5: 330805, 
            6: 330806, 
        }), 
    }), 
    34: TD({
        1: TD({
            1: 340101, 
        }), 
        2: TD({
            1: 340201, 
            2: 340202, 
            3: 340203, 
            4: 340204, 
        }), 
        3: TD({
            1: 340301, 
            10: 340310, 
            11: 340311, 
            12: 340312, 
            13: 340313, 
            2: 340302, 
            3: 340303, 
            4: 340304, 
            5: 340305, 
            6: 340306, 
            7: 340307, 
            8: 340308, 
            9: 340309, 
        }), 
        4: TD({
            1: 340401, 
            2: 340402, 
            5: 340405, 
        }), 
        5: TD({
            1: 340501, 
            2: 340502, 
            3: 340503, 
            4: 340504, 
        }), 
        6: TD({
            1: 340601, 
        }), 
        7: TD({
            1: 340701, 
            2: 340702, 
            3: 340703, 
            4: 340704, 
            5: 340705, 
            6: 340706, 
            7: 340707, 
            8: 340708, 
        }), 
        8: TD({
            1: 340801, 
            10: 340810, 
            2: 340802, 
            3: 340803, 
            4: 340804, 
            5: 340805, 
            6: 340806, 
            7: 340807, 
            8: 340808, 
            9: 340809, 
        }), 
    }), 
    35: TD({
        1: TD({
            1: 350101, 
        }), 
        2: TD({
            1: 350201, 
            2: 350202, 
            3: 350203, 
            4: 350204, 
            5: 350205, 
            6: 350206, 
        }), 
        3: TD({
            1: 350301, 
            10: 350310, 
            11: 350311, 
            12: 350312, 
            13: 350313, 
            2: 350302, 
            3: 350303, 
            4: 350304, 
            5: 350305, 
            6: 350306, 
            7: 350307, 
            8: 350308, 
            9: 350309, 
        }), 
        4: TD({
            1: 350401, 
            2: 350402, 
            3: 350403, 
            4: 350404, 
            5: 350405, 
        }), 
        5: TD({
            1: 350501, 
            2: 350502, 
            3: 350503, 
            4: 350504, 
            5: 350505, 
        }), 
        6: TD({
            1: 350601, 
        }), 
        7: TD({
            1: 350701, 
            2: 350702, 
            3: 350703, 
            4: 350704, 
            5: 350705, 
            6: 350706, 
            7: 350707, 
        }), 
        8: TD({
            1: 350801, 
            2: 350802, 
            3: 350803, 
            4: 350804, 
            5: 350805, 
            6: 350806, 
            7: 350807, 
            8: 350808, 
        }), 
    }), 
    36: TD({
        1: TD({
            1: 360101, 
        }), 
        2: TD({
            1: 360201, 
        }), 
        3: TD({
            1: 360301, 
        }), 
        5: TD({
            1: 360401, 
        }), 
        7: TD({
            1: 360501, 
        }), 
    }), 
    37: TD({
        1: TD({
            1: 370101, 
        }), 
        2: TD({
            1: 370201, 
            2: 370202, 
            3: 370203, 
            4: 370204, 
        }), 
        3: TD({
            1: 370301, 
            10: 370310, 
            11: 370311, 
            12: 370312, 
            13: 370313, 
            2: 370302, 
            3: 370303, 
            4: 370304, 
            5: 370305, 
            6: 370306, 
            7: 370307, 
            8: 370308, 
            9: 370309, 
        }), 
        4: TD({
            1: 370401, 
            2: 370402, 
            3: 370403, 
            4: 370404, 
        }), 
        5: TD({
            1: 370501, 
            2: 370502, 
            3: 370503, 
            4: 370504, 
        }), 
        7: TD({
            1: 370701, 
            2: 370702, 
            3: 370703, 
            4: 370704, 
            5: 370705, 
            6: 370706, 
            7: 370707, 
        }), 
        8: TD({
            1: 370801, 
            10: 370810, 
            11: 370811, 
            2: 370802, 
            3: 370803, 
            4: 370804, 
            5: 370805, 
            6: 370806, 
            7: 370807, 
            8: 370808, 
            9: 370809, 
        }), 
    }), 
    38: TD({
        1: TD({
            1: 380101, 
        }), 
        2: TD({
            1: 380201, 
            2: 380202, 
            3: 380203, 
            4: 380204, 
        }), 
        3: TD({
            1: 380301, 
            10: 380310, 
            11: 380311, 
            12: 380312, 
            13: 380313, 
            2: 380302, 
            3: 380303, 
            4: 380304, 
            5: 380305, 
            6: 380306, 
            7: 380307, 
            8: 380308, 
            9: 380309, 
        }), 
        4: TD({
            1: 380401, 
            2: 380402, 
            3: 380403, 
            4: 380404, 
        }), 
        5: TD({
            1: 380501, 
            2: 380502, 
            3: 380503, 
            4: 380504, 
            5: 380505, 
        }), 
        7: TD({
            1: 380701, 
            2: 380702, 
            3: 380703, 
            4: 380704, 
            5: 380705, 
            6: 380706, 
            7: 380707, 
        }), 
        8: TD({
            1: 380801, 
            10: 380810, 
            11: 380811, 
            2: 380802, 
            3: 380803, 
            4: 380804, 
            5: 380805, 
            6: 380806, 
            7: 380807, 
            8: 380808, 
            9: 380809, 
        }), 
    }), 
    39: TD({
        1: TD({
            1: 390101, 
        }), 
        12: TD({
            1: 391201, 
        }), 
        2: TD({
            1: 390201, 
            4: 390204, 
        }), 
        3: TD({
            1: 390301, 
            10: 390310, 
            11: 390311, 
            12: 390312, 
            13: 390313, 
            2: 390302, 
            3: 390303, 
            4: 390304, 
            5: 390305, 
            6: 390306, 
            7: 390307, 
            8: 390308, 
            9: 390309, 
        }), 
        4: TD({
            1: 390401, 
            3: 390403, 
        }), 
        5: TD({
            1: 390501, 
            2: 390502, 
            3: 390503, 
        }), 
        7: TD({
            1: 390701, 
            2: 390702, 
            3: 390703, 
            4: 390704, 
            5: 390705, 
            6: 390706, 
            7: 390707, 
        }), 
        8: TD({
            1: 390801, 
            10: 390810, 
            11: 390811, 
            2: 390802, 
            3: 390803, 
            4: 390804, 
            5: 390805, 
            6: 390806, 
            7: 390807, 
            8: 390808, 
            9: 390809, 
        }), 
        9: TD({
            1: 390901, 
            2: 390902, 
            3: 390903, 
            4: 390904, 
        }), 
    }), 
    4: TD({
        1: TD({
            1: 40101, 
        }), 
        10: TD({
            1: 41001, 
        }), 
        2: TD({
            1: 40201, 
            2: 40202, 
            3: 40203, 
        }), 
        3: TD({
            1: 40301, 
            2: 40302, 
            3: 40303, 
            4: 40304, 
        }), 
        5: TD({
            1: 40501, 
            2: 40502, 
            3: 40503, 
            4: 40504, 
        }), 
        6: TD({
            1: 40601, 
        }), 
        8: TD({
            1: 40801, 
            2: 40802, 
            3: 40803, 
            4: 40804, 
            5: 40805, 
        }), 
        9: TD({
            1: 40901, 
            2: 40902, 
            3: 40903, 
            4: 40904, 
        }), 
    }), 
    40: TD({
        1: TD({
            1: 400101, 
        }), 
        2: TD({
            1: 400201, 
            2: 400202, 
            3: 400203, 
            4: 400204, 
        }), 
        3: TD({
            1: 400301, 
            11: 400311, 
            12: 400312, 
            2: 400302, 
            4: 400304, 
            5: 400305, 
            6: 400306, 
            7: 400307, 
            8: 400308, 
            9: 400309, 
        }), 
        4: TD({
            1: 400401, 
            2: 400402, 
            4: 400404, 
        }), 
        5: TD({
            1: 400501, 
            2: 400502, 
            3: 400503, 
            4: 400504, 
        }), 
        6: TD({
            1: 400601, 
            2: 400602, 
            3: 400603, 
            4: 400604, 
            5: 400605, 
        }), 
        7: TD({
            1: 400701, 
            2: 400702, 
            3: 400703, 
            4: 400704, 
            5: 400705, 
            6: 400706, 
            7: 400707, 
        }), 
        8: TD({
            1: 400801, 
            2: 400802, 
            3: 400803, 
            4: 400804, 
            5: 400805, 
            6: 400806, 
            7: 400807, 
            8: 400808, 
        }), 
    }), 
    41: TD({
        1: TD({
            1: 410101, 
        }), 
        2: TD({
            1: 410201, 
        }), 
        3: TD({
            1: 410301, 
        }), 
        5: TD({
            1: 410501, 
            2: 410502, 
            3: 410503, 
        }), 
        6: TD({
            1: 410601, 
        }), 
        8: TD({
            1: 410801, 
        }), 
        9: TD({
            1: 410901, 
            2: 410902, 
            3: 410903, 
            4: 410904, 
        }), 
    }), 
    42: TD({
        1: TD({
            1: 420101, 
        }), 
        2: TD({
            1: 420201, 
            2: 420202, 
            3: 420203, 
            4: 420204, 
        }), 
        3: TD({
            1: 420301, 
            10: 420310, 
            11: 420311, 
            12: 420312, 
            13: 420313, 
            2: 420302, 
            3: 420303, 
            4: 420304, 
            5: 420305, 
            6: 420306, 
            7: 420307, 
            8: 420308, 
            9: 420309, 
        }), 
        4: TD({
            1: 420401, 
            2: 420402, 
            3: 420403, 
            4: 420404, 
        }), 
        5: TD({
            1: 420501, 
            2: 420502, 
            3: 420503, 
            4: 420504, 
        }), 
        7: TD({
            1: 420701, 
            2: 420702, 
            3: 420703, 
            4: 420704, 
            5: 420705, 
            6: 420706, 
            7: 420707, 
        }), 
        8: TD({
            1: 420801, 
            10: 420810, 
            11: 420811, 
            2: 420802, 
            3: 420803, 
            4: 420804, 
            5: 420805, 
            6: 420806, 
            7: 420807, 
            8: 420808, 
            9: 420809, 
        }), 
    }), 
    43: TD({
        1: TD({
            1: 430101, 
        }), 
        2: TD({
            1: 430201, 
        }), 
        3: TD({
            1: 430301, 
            2: 430302, 
            3: 430303, 
        }), 
        4: TD({
            1: 430401, 
        }), 
        5: TD({
            1: 430501, 
        }), 
        6: TD({
            1: 430601, 
        }), 
        7: TD({
            1: 430701, 
        }), 
        8: TD({
            1: 430801, 
        }), 
    }), 
    6: TD({
        1: TD({
            1: 60101, 
        }), 
        2: TD({
            1: 60201, 
            2: 60202, 
            3: 60203, 
            4: 60204, 
        }), 
        3: TD({
            1: 60301, 
            10: 60310, 
            11: 60311, 
            12: 60312, 
            2: 60302, 
            3: 60303, 
            4: 60304, 
            5: 60305, 
            6: 60306, 
            7: 60307, 
            8: 60308, 
            9: 60309, 
        }), 
        4: TD({
            1: 60401, 
            2: 60402, 
            3: 60403, 
        }), 
        5: TD({
            1: 60501, 
            2: 60502, 
            3: 60503, 
            4: 60504, 
        }), 
        6: TD({
            1: 60601, 
        }), 
        7: TD({
            1: 60701, 
            2: 60702, 
            3: 60703, 
            4: 60704, 
        }), 
        8: TD({
            1: 60801, 
            2: 60802, 
            3: 60803, 
            4: 60804, 
            5: 60805, 
            6: 60806, 
        }), 
        9: TD({
            1: 60901, 
            2: 60902, 
            3: 60903, 
            4: 60904, 
        }), 
    }), 
    8: TD({
        1: TD({
            1: 80101, 
        }), 
        2: TD({
            1: 80201, 
            2: 80202, 
            3: 80203, 
        }), 
        3: TD({
            1: 80301, 
            10: 80310, 
            11: 80311, 
            12: 80312, 
            2: 80302, 
            3: 80303, 
            4: 80304, 
            5: 80305, 
            6: 80306, 
            7: 80307, 
            8: 80308, 
            9: 80309, 
        }), 
        4: TD({
            1: 80401, 
            2: 80402, 
            3: 80403, 
            4: 80404, 
        }), 
        5: TD({
            1: 80501, 
            3: 80504, 
        }), 
        6: TD({
            1: 80601, 
        }), 
        7: TD({
            1: 80701, 
            2: 80702, 
            3: 80703, 
            4: 80704, 
            5: 80705, 
            6: 80706, 
            7: 80707, 
        }), 
        8: TD({
            1: 80801, 
            2: 80802, 
            3: 80803, 
            4: 80804, 
            5: 80805, 
            6: 80806, 
            7: 80807, 
            8: 80808, 
        }), 
        9: TD({
            1: 80901, 
            2: 80902, 
            3: 80903, 
            4: 80904, 
        }), 
    }), 
}
