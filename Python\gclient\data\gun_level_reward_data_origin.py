# -*- coding: utf-8 -*-
# flake8: noqa
# generated by: excel_to_data.py
# generated from , post_process.py, GenerateGunLevelRewardData
from taggeddict import taggeddict as TD
data = {
    1: ((1, TD({'reward_info': 1100001560, 'reward_type': 'skin', }), ), (1, <PERSON>({'reward_info': 10602, 'reward_type': 'part', }), ), (1, <PERSON>({'reward_info': 10603, 'reward_type': 'part', }), ), (1, TD({'reward_info': 10604, 'reward_type': 'part', }), ), (1, TD({'reward_info': 10605, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 10302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 10303, 'reward_type': 'part', }), ), (4, TD({'reward_info': 10304, 'reward_type': 'part', }), ), (5, <PERSON>({'reward_info': 10202, 'reward_type': 'part', }), ), (6, TD({'reward_info': 10305, 'reward_type': 'part', }), ), (7, TD({'reward_info': 10402, 'reward_type': 'part', }), ), (8, TD({'reward_info': 10502, 'reward_type': 'part', }), ), (9, TD({'reward_info': 10702, 'reward_type': 'part', }), ), (10, TD({'reward_info': 10802, 'reward_type': 'part', }), ), (11, TD({'reward_info': 10203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 10306, 'reward_type': 'part', }), ), (13, TD({'reward_info': 10403, 'reward_type': 'part', }), ), (14, TD({'reward_info': 10503, 'reward_type': 'part', }), ), (15, TD({'reward_info': 10703, 'reward_type': 'part', }), ), (16, TD({'reward_info': 10803, 'reward_type': 'part', }), ), (17, TD({'reward_info': 10204, 'reward_type': 'part', }), ), (18, TD({'reward_info': 10307, 'reward_type': 'part', }), ), (19, TD({'reward_info': 10404, 'reward_type': 'part', }), ), (20, TD({'reward_info': 10504, 'reward_type': 'part', }), ), (20, TD({'reward_info': 10902, 'reward_type': 'part', }), ), (20, TD({'reward_info': 10903, 'reward_type': 'part', }), ), (20, TD({'reward_info': 10904, 'reward_type': 'part', }), ), (21, TD({'reward_info': 10704, 'reward_type': 'part', }), ), (22, TD({'reward_info': 10804, 'reward_type': 'part', }), ), (24, TD({'reward_info': 10308, 'reward_type': 'part', }), ), (25, TD({'reward_info': 10405, 'reward_type': 'part', }), ), (25, TD({'reward_info': 10406, 'reward_type': 'part', }), ), (25, TD({'reward_info': 10407, 'reward_type': 'part', }), ), (25, TD({'reward_info': 10408, 'reward_type': 'part', }), ), (25, TD({'reward_info': 10409, 'reward_type': 'part', }), ), (26, TD({'reward_info': 10505, 'reward_type': 'part', }), ), (27, TD({'reward_info': 10705, 'reward_type': 'part', }), ), (28, TD({'reward_info': 10805, 'reward_type': 'part', }), ), (28, TD({'reward_info': 10809, 'reward_type': 'part', }), ), (29, TD({'reward_info': 10206, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1100001590, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 10309, 'reward_type': 'part', }), ), (33, TD({'reward_info': 10706, 'reward_type': 'part', }), ), (34, TD({'reward_info': 10810, 'reward_type': 'part', }), ), (34, TD({'reward_info': 10806, 'reward_type': 'part', }), ), (36, TD({'reward_info': 10310, 'reward_type': 'part', }), ), (39, TD({'reward_info': 10707, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1100001620, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 10811, 'reward_type': 'part', }), ), (40, TD({'reward_info': 10807, 'reward_type': 'part', }), ), (42, TD({'reward_info': 10311, 'reward_type': 'part', }), ), (46, TD({'reward_info': 10812, 'reward_type': 'part', }), ), (46, TD({'reward_info': 10808, 'reward_type': 'part', }), ), (48, TD({'reward_info': 10312, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1100001470, 'reward_type': 'skin', }), ), (54, TD({'reward_info': 10313, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1100001500, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100001530, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100001650, 'reward_type': 'skin', }), ), ), 
    10: ((1, TD({'reward_info': 1100001540, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 100310, 'reward_type': 'part', }), ), (1, TD({'reward_info': 100314, 'reward_type': 'part', }), ), (1, TD({'reward_info': 100307, 'reward_type': 'part', }), ), (1, TD({'reward_info': 100602, 'reward_type': 'part', }), ), (1, TD({'reward_info': 100603, 'reward_type': 'part', }), ), (1, TD({'reward_info': 100604, 'reward_type': 'part', }), ), (1, TD({'reward_info': 100605, 'reward_type': 'part', }), ), (1, TD({'reward_info': 100902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 100903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 100904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 100302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 100503, 'reward_type': 'part', }), ), (4, TD({'reward_info': 100702, 'reward_type': 'part', }), ), (5, TD({'reward_info': 100404, 'reward_type': 'part', }), ), (6, TD({'reward_info': 100405, 'reward_type': 'part', }), ), (6, TD({'reward_info': 100704, 'reward_type': 'part', }), ), (8, TD({'reward_info': 100803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 100203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 100802, 'reward_type': 'part', }), ), (13, TD({'reward_info': 100303, 'reward_type': 'part', }), ), (14, TD({'reward_info': 100804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 100805, 'reward_type': 'part', }), ), (20, TD({'reward_info': 100204, 'reward_type': 'part', }), ), (21, TD({'reward_info': 100304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 100806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 100703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 100305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 100705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 100306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 100311, 'reward_type': 'part', }), ), (28, TD({'reward_info': 1003110001, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1100001570, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 100403, 'reward_type': 'part', }), ), (32, TD({'reward_info': 100707, 'reward_type': 'part', }), ), (34, TD({'reward_info': 100706, 'reward_type': 'part', }), ), (36, TD({'reward_info': 100312, 'reward_type': 'part', }), ), (36, TD({'reward_info': 1003010001, 'reward_type': 'part', }), ), (38, TD({'reward_info': 100807, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1100001600, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 100502, 'reward_type': 'part', }), ), (42, TD({'reward_info': 100308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 100402, 'reward_type': 'part', }), ), (45, TD({'reward_info': 100313, 'reward_type': 'part', }), ), (47, TD({'reward_info': 100808, 'reward_type': 'part', }), ), (49, TD({'reward_info': 100309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1100001450, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 100202, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1100001480, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100001510, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100001630, 'reward_type': 'skin', }), ), ), 
    13: ((1, TD({'reward_info': 1400001180, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 130302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 130802, 'reward_type': 'part', }), ), (5, TD({'reward_info': 130403, 'reward_type': 'part', }), ), (7, TD({'reward_info': 130310, 'reward_type': 'part', }), ), (9, TD({'reward_info': 130303, 'reward_type': 'part', }), ), (10, TD({'reward_info': 130202, 'reward_type': 'part', }), ), (13, TD({'reward_info': 130803, 'reward_type': 'part', }), ), (15, TD({'reward_info': 130304, 'reward_type': 'part', }), ), (16, TD({'reward_info': 130311, 'reward_type': 'part', }), ), (18, TD({'reward_info': 130305, 'reward_type': 'part', }), ), (20, TD({'reward_info': 130502, 'reward_type': 'part', }), ), (22, TD({'reward_info': 130504, 'reward_type': 'part', }), ), (23, TD({'reward_info': 130804, 'reward_type': 'part', }), ), (26, TD({'reward_info': 130402, 'reward_type': 'part', }), ), (28, TD({'reward_info': 130306, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1400001210, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 130203, 'reward_type': 'part', }), ), (32, TD({'reward_info': 130307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 130805, 'reward_type': 'part', }), ), (37, TD({'reward_info': 130312, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1400001240, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 130503, 'reward_type': 'part', }), ), (42, TD({'reward_info': 130308, 'reward_type': 'part', }), ), (44, TD({'reward_info': 130806, 'reward_type': 'part', }), ), (46, TD({'reward_info': 130313, 'reward_type': 'part', }), ), (48, TD({'reward_info': 130309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1400001090, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 130204, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1400001120, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1400001150, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1400001270, 'reward_type': 'skin', }), ), ), 
    14: ((1, TD({'reward_info': 1100001550, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 140302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 140702, 'reward_type': 'part', }), ), (4, TD({'reward_info': 140504, 'reward_type': 'part', }), ), (5, TD({'reward_info': 140403, 'reward_type': 'part', }), ), (6, TD({'reward_info': 140704, 'reward_type': 'part', }), ), (8, TD({'reward_info': 140803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 140204, 'reward_type': 'part', }), ), (12, TD({'reward_info': 140802, 'reward_type': 'part', }), ), (13, TD({'reward_info': 140304, 'reward_type': 'part', }), ), (14, TD({'reward_info': 140804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 140805, 'reward_type': 'part', }), ), (18, TD({'reward_info': 140310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 140202, 'reward_type': 'part', }), ), (21, TD({'reward_info': 140305, 'reward_type': 'part', }), ), (22, TD({'reward_info': 140806, 'reward_type': 'part', }), ), (23, TD({'reward_info': 140505, 'reward_type': 'part', }), ), (24, TD({'reward_info': 140703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 140306, 'reward_type': 'part', }), ), (26, TD({'reward_info': 140705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 140307, 'reward_type': 'part', }), ), (28, TD({'reward_info': 140311, 'reward_type': 'part', }), ), (28, TD({'reward_info': 1403110001, 'reward_type': 'part', }), ), (29, TD({'reward_info': 140404, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1100001580, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 140502, 'reward_type': 'part', }), ), (32, TD({'reward_info': 140706, 'reward_type': 'part', }), ), (34, TD({'reward_info': 140308, 'reward_type': 'part', }), ), (36, TD({'reward_info': 140312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 140807, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1100001610, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 140503, 'reward_type': 'part', }), ), (41, TD({'reward_info': 140309, 'reward_type': 'part', }), ), (42, TD({'reward_info': 140707, 'reward_type': 'part', }), ), (43, TD({'reward_info': 140402, 'reward_type': 'part', }), ), (45, TD({'reward_info': 140313, 'reward_type': 'part', }), ), (47, TD({'reward_info': 140808, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1100001460, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 140203, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1100001490, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100001520, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100001640, 'reward_type': 'skin', }), ), ), 
    15: ((1, TD({'reward_info': 1500000870, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 151002, 'reward_type': 'part', }), ), (1, TD({'reward_info': 151003, 'reward_type': 'part', }), ), (1, TD({'reward_info': 150602, 'reward_type': 'part', }), ), (1, TD({'reward_info': 150603, 'reward_type': 'part', }), ), (1, TD({'reward_info': 150902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 150903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 150904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 150302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 150802, 'reward_type': 'part', }), ), (5, TD({'reward_info': 150502, 'reward_type': 'part', }), ), (7, TD({'reward_info': 150303, 'reward_type': 'part', }), ), (10, TD({'reward_info': 150202, 'reward_type': 'part', }), ), (12, TD({'reward_info': 150304, 'reward_type': 'part', }), ), (14, TD({'reward_info': 150803, 'reward_type': 'part', }), ), (15, TD({'reward_info': 150305, 'reward_type': 'part', }), ), (16, TD({'reward_info': 150310, 'reward_type': 'part', }), ), (18, TD({'reward_info': 150306, 'reward_type': 'part', }), ), (20, TD({'reward_info': 1500000890, 'reward_type': 'skin', }), ), (20, TD({'reward_info': 150203, 'reward_type': 'part', }), ), (22, TD({'reward_info': 150311, 'reward_type': 'part', }), ), (23, TD({'reward_info': 150804, 'reward_type': 'part', }), ), (25, TD({'reward_info': 150312, 'reward_type': 'part', }), ), (26, TD({'reward_info': 150805, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1500000910, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 150503, 'reward_type': 'part', }), ), (32, TD({'reward_info': 150307, 'reward_type': 'part', }), ), (36, TD({'reward_info': 150308, 'reward_type': 'part', }), ), (38, TD({'reward_info': 150313, 'reward_type': 'part', }), ), (39, TD({'reward_info': 150309, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1500000810, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 150204, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1500000830, 'reward_type': 'skin', }), ), (60, TD({'reward_info': 1500000850, 'reward_type': 'skin', }), ), (60, TD({'reward_info': 1500000930, 'reward_type': 'skin', }), ), ), 
    16: ((1, TD({'reward_info': 1200001310, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 160302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 160502, 'reward_type': 'part', }), ), (4, TD({'reward_info': 160702, 'reward_type': 'part', }), ), (5, TD({'reward_info': 160403, 'reward_type': 'part', }), ), (6, TD({'reward_info': 160802, 'reward_type': 'part', }), ), (8, TD({'reward_info': 160803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 160202, 'reward_type': 'part', }), ), (12, TD({'reward_info': 160703, 'reward_type': 'part', }), ), (13, TD({'reward_info': 160304, 'reward_type': 'part', }), ), (14, TD({'reward_info': 160804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 160807, 'reward_type': 'part', }), ), (18, TD({'reward_info': 160310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 160402, 'reward_type': 'part', }), ), (21, TD({'reward_info': 160305, 'reward_type': 'part', }), ), (22, TD({'reward_info': 160806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 160707, 'reward_type': 'part', }), ), (25, TD({'reward_info': 160306, 'reward_type': 'part', }), ), (26, TD({'reward_info': 160704, 'reward_type': 'part', }), ), (27, TD({'reward_info': 160307, 'reward_type': 'part', }), ), (28, TD({'reward_info': 160311, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1200001340, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 160203, 'reward_type': 'part', }), ), (32, TD({'reward_info': 160503, 'reward_type': 'part', }), ), (34, TD({'reward_info': 160308, 'reward_type': 'part', }), ), (36, TD({'reward_info': 160706, 'reward_type': 'part', }), ), (38, TD({'reward_info': 160805, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1200001370, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 160705, 'reward_type': 'part', }), ), (42, TD({'reward_info': 160309, 'reward_type': 'part', }), ), (44, TD({'reward_info': 160808, 'reward_type': 'part', }), ), (46, TD({'reward_info': 160404, 'reward_type': 'part', }), ), (48, TD({'reward_info': 160312, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1200001220, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 160204, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1200001250, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200001280, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200001400, 'reward_type': 'skin', }), ), ), 
    17: ((1, TD({'reward_info': 1200001300, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 170302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 170502, 'reward_type': 'part', }), ), (4, TD({'reward_info': 170702, 'reward_type': 'part', }), ), (5, TD({'reward_info': 170403, 'reward_type': 'part', }), ), (6, TD({'reward_info': 170802, 'reward_type': 'part', }), ), (8, TD({'reward_info': 170803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 170203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 170703, 'reward_type': 'part', }), ), (13, TD({'reward_info': 170304, 'reward_type': 'part', }), ), (14, TD({'reward_info': 170804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 170807, 'reward_type': 'part', }), ), (18, TD({'reward_info': 170310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 170402, 'reward_type': 'part', }), ), (21, TD({'reward_info': 170305, 'reward_type': 'part', }), ), (22, TD({'reward_info': 170806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 170707, 'reward_type': 'part', }), ), (25, TD({'reward_info': 170306, 'reward_type': 'part', }), ), (26, TD({'reward_info': 170704, 'reward_type': 'part', }), ), (27, TD({'reward_info': 170307, 'reward_type': 'part', }), ), (28, TD({'reward_info': 170311, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1200001330, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 170204, 'reward_type': 'part', }), ), (32, TD({'reward_info': 170805, 'reward_type': 'part', }), ), (34, TD({'reward_info': 170308, 'reward_type': 'part', }), ), (36, TD({'reward_info': 170706, 'reward_type': 'part', }), ), (38, TD({'reward_info': 170503, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1200001360, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 170705, 'reward_type': 'part', }), ), (42, TD({'reward_info': 170309, 'reward_type': 'part', }), ), (44, TD({'reward_info': 170312, 'reward_type': 'part', }), ), (46, TD({'reward_info': 170202, 'reward_type': 'part', }), ), (48, TD({'reward_info': 170808, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1200001210, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 170404, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1200001240, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200001270, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200001390, 'reward_type': 'skin', }), ), ), 
    18: ((1, TD({'reward_info': 1400001190, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 181302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 181303, 'reward_type': 'part', }), ), (1, TD({'reward_info': 180902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 180903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 180904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 180302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 180802, 'reward_type': 'part', }), ), (5, TD({'reward_info': 180403, 'reward_type': 'part', }), ), (7, TD({'reward_info': 180310, 'reward_type': 'part', }), ), (10, TD({'reward_info': 180202, 'reward_type': 'part', }), ), (13, TD({'reward_info': 180803, 'reward_type': 'part', }), ), (14, TD({'reward_info': 180303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 180311, 'reward_type': 'part', }), ), (18, TD({'reward_info': 180304, 'reward_type': 'part', }), ), (20, TD({'reward_info': 180502, 'reward_type': 'part', }), ), (23, TD({'reward_info': 180305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 180804, 'reward_type': 'part', }), ), (28, TD({'reward_info': 180306, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1400001220, 'reward_type': 'skin', }), ), (32, TD({'reward_info': 180307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 180805, 'reward_type': 'part', }), ), (34, TD({'reward_info': 180807, 'reward_type': 'part', }), ), (37, TD({'reward_info': 180313, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1400001250, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 180806, 'reward_type': 'part', }), ), (40, TD({'reward_info': 180808, 'reward_type': 'part', }), ), (42, TD({'reward_info': 180308, 'reward_type': 'part', }), ), (45, TD({'reward_info': 180312, 'reward_type': 'part', }), ), (48, TD({'reward_info': 180309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1400001100, 'reward_type': 'skin', }), ), (60, TD({'reward_info': 1400001130, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1400001160, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1400001280, 'reward_type': 'skin', }), ), ), 
    19: ((1, TD({'reward_info': 190302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 190303, 'reward_type': 'part', }), ), (1, TD({'reward_info': 190304, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), ), 
    2: ((1, TD({'reward_info': 1200003590, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 20202, 'reward_type': 'part', }), ), (1, TD({'reward_info': 20203, 'reward_type': 'part', }), ), (1, TD({'reward_info': 20310, 'reward_type': 'part', }), ), (1, TD({'reward_info': 20307, 'reward_type': 'part', }), ), (1, TD({'reward_info': 20902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 20903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 20904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 20302, 'reward_type': 'part', }), ), (4, TD({'reward_info': 20702, 'reward_type': 'part', }), ), (5, TD({'reward_info': 20403, 'reward_type': 'part', }), ), (6, TD({'reward_info': 20802, 'reward_type': 'part', }), ), (8, TD({'reward_info': 20803, 'reward_type': 'part', }), ), (12, TD({'reward_info': 20703, 'reward_type': 'part', }), ), (13, TD({'reward_info': 20303, 'reward_type': 'part', }), ), (14, TD({'reward_info': 20804, 'reward_type': 'part', }), ), (14, TD({'reward_info': 20808, 'reward_type': 'part', }), ), (16, TD({'reward_info': 20811, 'reward_type': 'part', }), ), (16, TD({'reward_info': 20807, 'reward_type': 'part', }), ), (19, TD({'reward_info': 20502, 'reward_type': 'part', }), ), (20, TD({'reward_info': 20402, 'reward_type': 'part', }), ), (21, TD({'reward_info': 20304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 20810, 'reward_type': 'part', }), ), (22, TD({'reward_info': 20806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 20707, 'reward_type': 'part', }), ), (25, TD({'reward_info': 20305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 20704, 'reward_type': 'part', }), ), (27, TD({'reward_info': 20306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 20311, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1200003600, 'reward_type': 'skin', }), ), (32, TD({'reward_info': 20503, 'reward_type': 'part', }), ), (36, TD({'reward_info': 20706, 'reward_type': 'part', }), ), (38, TD({'reward_info': 20805, 'reward_type': 'part', }), ), (38, TD({'reward_info': 20809, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1200003610, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 20705, 'reward_type': 'part', }), ), (42, TD({'reward_info': 20308, 'reward_type': 'part', }), ), (44, TD({'reward_info': 20312, 'reward_type': 'part', }), ), (46, TD({'reward_info': 20405, 'reward_type': 'part', }), ), (49, TD({'reward_info': 20309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1200003620, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 20204, 'reward_type': 'part', }), ), (50, TD({'reward_info': 20208, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1200003630, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200003640, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200003650, 'reward_type': 'skin', }), ), ), 
    20: ((1, TD({'reward_info': 1300000870, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 201302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 201303, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 200302, 'reward_type': 'part', }), ), (5, TD({'reward_info': 200402, 'reward_type': 'part', }), ), (6, TD({'reward_info': 200802, 'reward_type': 'part', }), ), (8, TD({'reward_info': 200303, 'reward_type': 'part', }), ), (10, TD({'reward_info': 200202, 'reward_type': 'part', }), ), (12, TD({'reward_info': 200502, 'reward_type': 'part', }), ), (14, TD({'reward_info': 200304, 'reward_type': 'part', }), ), (16, TD({'reward_info': 200803, 'reward_type': 'part', }), ), (18, TD({'reward_info': 200310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 200804, 'reward_type': 'part', }), ), (20, TD({'reward_info': 200902, 'reward_type': 'part', }), ), (20, TD({'reward_info': 200903, 'reward_type': 'part', }), ), (20, TD({'reward_info': 200904, 'reward_type': 'part', }), ), (22, TD({'reward_info': 200305, 'reward_type': 'part', }), ), (25, TD({'reward_info': 200311, 'reward_type': 'part', }), ), (27, TD({'reward_info': 200805, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1300000890, 'reward_type': 'skin', }), ), (32, TD({'reward_info': 200312, 'reward_type': 'part', }), ), (34, TD({'reward_info': 200306, 'reward_type': 'part', }), ), (35, TD({'reward_info': 200503, 'reward_type': 'part', }), ), (37, TD({'reward_info': 200307, 'reward_type': 'part', }), ), (39, TD({'reward_info': 200313, 'reward_type': 'part', }), ), (39, TD({'reward_info': 200504, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1300000910, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 200806, 'reward_type': 'part', }), ), (42, TD({'reward_info': 200308, 'reward_type': 'part', }), ), (44, TD({'reward_info': 200403, 'reward_type': 'part', }), ), (47, TD({'reward_info': 200309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1300000810, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 200203, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1300000830, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1300000850, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1300000930, 'reward_type': 'skin', }), ), ), 
    21: ((1, TD({'reward_info': 1600000440, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 1600000450, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 1600000460, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (10, TD({'reward_info': 1600000410, 'reward_type': 'skin', }), ), (15, TD({'reward_info': 1600000420, 'reward_type': 'skin', }), ), (20, TD({'reward_info': 1600000430, 'reward_type': 'skin', }), ), (60, TD({'reward_info': 1600000470, 'reward_type': 'skin', }), ), ), 
    22: ((1, TD({'reward_info': 1400001200, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (10, TD({'reward_info': 220302, 'reward_type': 'part', }), ), (20, TD({'reward_info': 220303, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1400001230, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 220304, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1400001260, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 220502, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1400001110, 'reward_type': 'skin', }), ), (60, TD({'reward_info': 1400001140, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1400001170, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1400001290, 'reward_type': 'skin', }), ), ), 
    23: ((1, TD({'reward_info': 1100001980, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 230602, 'reward_type': 'part', }), ), (1, TD({'reward_info': 230603, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 230302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 230502, 'reward_type': 'part', }), ), (4, TD({'reward_info': 230802, 'reward_type': 'part', }), ), (5, TD({'reward_info': 230402, 'reward_type': 'part', }), ), (7, TD({'reward_info': 230803, 'reward_type': 'part', }), ), (9, TD({'reward_info': 230702, 'reward_type': 'part', }), ), (10, TD({'reward_info': 230202, 'reward_type': 'part', }), ), (12, TD({'reward_info': 230704, 'reward_type': 'part', }), ), (13, TD({'reward_info': 230303, 'reward_type': 'part', }), ), (14, TD({'reward_info': 230804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 230805, 'reward_type': 'part', }), ), (18, TD({'reward_info': 230310, 'reward_type': 'part', }), ), (18, TD({'reward_info': 2303100001, 'reward_type': 'part', }), ), (20, TD({'reward_info': 230203, 'reward_type': 'part', }), ), (20, TD({'reward_info': 230902, 'reward_type': 'part', }), ), (20, TD({'reward_info': 230903, 'reward_type': 'part', }), ), (20, TD({'reward_info': 230904, 'reward_type': 'part', }), ), (21, TD({'reward_info': 230304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 230806, 'reward_type': 'part', }), ), (23, TD({'reward_info': 230504, 'reward_type': 'part', }), ), (24, TD({'reward_info': 230703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 230305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 230705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 230306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 230311, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1100001990, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 230404, 'reward_type': 'part', }), ), (32, TD({'reward_info': 230503, 'reward_type': 'part', }), ), (33, TD({'reward_info': 230307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 230706, 'reward_type': 'part', }), ), (36, TD({'reward_info': 230312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 230807, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1100002000, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 230707, 'reward_type': 'part', }), ), (42, TD({'reward_info': 230308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 230403, 'reward_type': 'part', }), ), (45, TD({'reward_info': 230313, 'reward_type': 'part', }), ), (47, TD({'reward_info': 230808, 'reward_type': 'part', }), ), (48, TD({'reward_info': 230309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1100001950, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 230204, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1100001960, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100001970, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100002010, 'reward_type': 'skin', }), ), ), 
    24: ((1, TD({'reward_info': 1200001780, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 240302, 'reward_type': 'part', }), ), (4, TD({'reward_info': 240602, 'reward_type': 'part', }), ), (4, TD({'reward_info': 240802, 'reward_type': 'part', }), ), (5, TD({'reward_info': 240402, 'reward_type': 'part', }), ), (7, TD({'reward_info': 240803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 240202, 'reward_type': 'part', }), ), (10, TD({'reward_info': 240403, 'reward_type': 'part', }), ), (13, TD({'reward_info': 240303, 'reward_type': 'part', }), ), (14, TD({'reward_info': 240804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 240805, 'reward_type': 'part', }), ), (18, TD({'reward_info': 240310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 240902, 'reward_type': 'part', }), ), (20, TD({'reward_info': 240903, 'reward_type': 'part', }), ), (20, TD({'reward_info': 240904, 'reward_type': 'part', }), ), (21, TD({'reward_info': 240304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 240806, 'reward_type': 'part', }), ), (25, TD({'reward_info': 240305, 'reward_type': 'part', }), ), (27, TD({'reward_info': 240306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 240311, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1200001790, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 240204, 'reward_type': 'part', }), ), (33, TD({'reward_info': 240603, 'reward_type': 'part', }), ), (34, TD({'reward_info': 240307, 'reward_type': 'part', }), ), (36, TD({'reward_info': 240312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 240807, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1200001800, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 240404, 'reward_type': 'part', }), ), (42, TD({'reward_info': 240308, 'reward_type': 'part', }), ), (46, TD({'reward_info': 240604, 'reward_type': 'part', }), ), (47, TD({'reward_info': 240808, 'reward_type': 'part', }), ), (48, TD({'reward_info': 240309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1200001810, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 240203, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1200001820, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200001830, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200001840, 'reward_type': 'skin', }), ), ), 
    25: ((1, TD({'reward_info': 1200002720, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 250302, 'reward_type': 'part', }), ), (5, TD({'reward_info': 250502, 'reward_type': 'part', }), ), (9, TD({'reward_info': 250702, 'reward_type': 'part', }), ), (10, TD({'reward_info': 250204, 'reward_type': 'part', }), ), (12, TD({'reward_info': 250704, 'reward_type': 'part', }), ), (13, TD({'reward_info': 250303, 'reward_type': 'part', }), ), (14, TD({'reward_info': 250504, 'reward_type': 'part', }), ), (15, TD({'reward_info': 250402, 'reward_type': 'part', }), ), (18, TD({'reward_info': 250310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 250202, 'reward_type': 'part', }), ), (21, TD({'reward_info': 250304, 'reward_type': 'part', }), ), (24, TD({'reward_info': 250703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 250305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 250705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 250306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 250311, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1200002730, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 250403, 'reward_type': 'part', }), ), (30, TD({'reward_info': 250505, 'reward_type': 'part', }), ), (32, TD({'reward_info': 250506, 'reward_type': 'part', }), ), (33, TD({'reward_info': 250307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 250706, 'reward_type': 'part', }), ), (35, TD({'reward_info': 250503, 'reward_type': 'part', }), ), (36, TD({'reward_info': 250312, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1200002740, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 250707, 'reward_type': 'part', }), ), (42, TD({'reward_info': 250308, 'reward_type': 'part', }), ), (45, TD({'reward_info': 250313, 'reward_type': 'part', }), ), (48, TD({'reward_info': 250309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1200002680, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 250203, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1200002700, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200002710, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200002750, 'reward_type': 'skin', }), ), ), 
    26: ((1, TD({'reward_info': 0, 'reward_type': 'view', }), ), ), 
    27: ((1, TD({'reward_info': 1100003220, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 270302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 270402, 'reward_type': 'part', }), ), (4, TD({'reward_info': 270802, 'reward_type': 'part', }), ), (5, TD({'reward_info': 270502, 'reward_type': 'part', }), ), (7, TD({'reward_info': 270803, 'reward_type': 'part', }), ), (9, TD({'reward_info': 270702, 'reward_type': 'part', }), ), (10, TD({'reward_info': 270202, 'reward_type': 'part', }), ), (12, TD({'reward_info': 270704, 'reward_type': 'part', }), ), (13, TD({'reward_info': 270303, 'reward_type': 'part', }), ), (14, TD({'reward_info': 270804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 270805, 'reward_type': 'part', }), ), (18, TD({'reward_info': 270310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 270203, 'reward_type': 'part', }), ), (21, TD({'reward_info': 270304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 270806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 270703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 270305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 270705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 270306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 270311, 'reward_type': 'part', }), ), (29, TD({'reward_info': 270503, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1100003230, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 270404, 'reward_type': 'part', }), ), (33, TD({'reward_info': 270307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 270706, 'reward_type': 'part', }), ), (36, TD({'reward_info': 270312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 270807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 270504, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1100003240, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 270707, 'reward_type': 'part', }), ), (42, TD({'reward_info': 270308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 270403, 'reward_type': 'part', }), ), (45, TD({'reward_info': 270313, 'reward_type': 'part', }), ), (47, TD({'reward_info': 270808, 'reward_type': 'part', }), ), (48, TD({'reward_info': 270309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1100003260, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 270204, 'reward_type': 'part', }), ), (50, TD({'reward_info': 270505, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1100003200, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100003210, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1100003250, 'reward_type': 'skin', }), ), ), 
    28: ((1, TD({'reward_info': 1700000310, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 280302, 'reward_type': 'part', }), ), (4, TD({'reward_info': 280303, 'reward_type': 'part', }), ), (5, TD({'reward_info': 280402, 'reward_type': 'part', }), ), (10, TD({'reward_info': 280203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 280802, 'reward_type': 'part', }), ), (13, TD({'reward_info': 280304, 'reward_type': 'part', }), ), (18, TD({'reward_info': 280310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 280202, 'reward_type': 'part', }), ), (21, TD({'reward_info': 280305, 'reward_type': 'part', }), ), (22, TD({'reward_info': 280804, 'reward_type': 'part', }), ), (24, TD({'reward_info': 280702, 'reward_type': 'part', }), ), (25, TD({'reward_info': 280306, 'reward_type': 'part', }), ), (27, TD({'reward_info': 280307, 'reward_type': 'part', }), ), (28, TD({'reward_info': 280311, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1700000320, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 280502, 'reward_type': 'part', }), ), (32, TD({'reward_info': 280703, 'reward_type': 'part', }), ), (34, TD({'reward_info': 280308, 'reward_type': 'part', }), ), (36, TD({'reward_info': 280312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 280803, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1700000330, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 280503, 'reward_type': 'part', }), ), (41, TD({'reward_info': 280309, 'reward_type': 'part', }), ), (43, TD({'reward_info': 280403, 'reward_type': 'part', }), ), (45, TD({'reward_info': 280313, 'reward_type': 'part', }), ), (48, TD({'reward_info': 280805, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1700000340, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 280204, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1700000350, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1700000360, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1700000370, 'reward_type': 'skin', }), ), ), 
    29: ((1, TD({'reward_info': 290902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 290903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 290904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 290302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 290402, 'reward_type': 'part', }), ), (4, TD({'reward_info': 290802, 'reward_type': 'part', }), ), (5, TD({'reward_info': 290502, 'reward_type': 'part', }), ), (7, TD({'reward_info': 290803, 'reward_type': 'part', }), ), (9, TD({'reward_info': 290702, 'reward_type': 'part', }), ), (10, TD({'reward_info': 290202, 'reward_type': 'part', }), ), (12, TD({'reward_info': 290704, 'reward_type': 'part', }), ), (13, TD({'reward_info': 290303, 'reward_type': 'part', }), ), (14, TD({'reward_info': 290804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 290805, 'reward_type': 'part', }), ), (18, TD({'reward_info': 290310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 290203, 'reward_type': 'part', }), ), (21, TD({'reward_info': 290304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 290806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 290703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 290305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 290705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 290306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 290311, 'reward_type': 'part', }), ), (29, TD({'reward_info': 290503, 'reward_type': 'part', }), ), (30, TD({'reward_info': 290404, 'reward_type': 'part', }), ), (33, TD({'reward_info': 290307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 290706, 'reward_type': 'part', }), ), (36, TD({'reward_info': 290312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 290807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 290204, 'reward_type': 'part', }), ), (40, TD({'reward_info': 290707, 'reward_type': 'part', }), ), (42, TD({'reward_info': 290308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 290403, 'reward_type': 'part', }), ), (45, TD({'reward_info': 290313, 'reward_type': 'part', }), ), (47, TD({'reward_info': 290808, 'reward_type': 'part', }), ), (48, TD({'reward_info': 290309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 290205, 'reward_type': 'part', }), ), ), 
    30: ((1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 300302, 'reward_type': 'part', }), ), (4, TD({'reward_info': 301002, 'reward_type': 'part', }), ), (4, TD({'reward_info': 301202, 'reward_type': 'part', }), ), (5, TD({'reward_info': 300502, 'reward_type': 'part', }), ), (7, TD({'reward_info': 301003, 'reward_type': 'part', }), ), (7, TD({'reward_info': 301203, 'reward_type': 'part', }), ), (9, TD({'reward_info': 300602, 'reward_type': 'part', }), ), (10, TD({'reward_info': 300202, 'reward_type': 'part', }), ), (13, TD({'reward_info': 300303, 'reward_type': 'part', }), ), (18, TD({'reward_info': 300310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 300203, 'reward_type': 'part', }), ), (21, TD({'reward_info': 300304, 'reward_type': 'part', }), ), (24, TD({'reward_info': 300603, 'reward_type': 'part', }), ), (24, TD({'reward_info': 300604, 'reward_type': 'part', }), ), (25, TD({'reward_info': 300305, 'reward_type': 'part', }), ), (27, TD({'reward_info': 300306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 300311, 'reward_type': 'part', }), ), (29, TD({'reward_info': 300503, 'reward_type': 'part', }), ), (33, TD({'reward_info': 300307, 'reward_type': 'part', }), ), (36, TD({'reward_info': 300312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 300802, 'reward_type': 'part', }), ), (39, TD({'reward_info': 300204, 'reward_type': 'part', }), ), (39, TD({'reward_info': 300205, 'reward_type': 'part', }), ), (42, TD({'reward_info': 300308, 'reward_type': 'part', }), ), (45, TD({'reward_info': 300313, 'reward_type': 'part', }), ), (48, TD({'reward_info': 300309, 'reward_type': 'part', }), ), (49, TD({'reward_info': 300803, 'reward_type': 'part', }), ), (49, TD({'reward_info': 300804, 'reward_type': 'part', }), ), (49, TD({'reward_info': 300805, 'reward_type': 'part', }), ), (49, TD({'reward_info': 300806, 'reward_type': 'part', }), ), (49, TD({'reward_info': 300807, 'reward_type': 'part', }), ), (50, TD({'reward_info': 300504, 'reward_type': 'part', }), ), ), 
    31: ((1, TD({'reward_info': 310302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 310310, 'reward_type': 'part', }), ), (4, TD({'reward_info': 310202, 'reward_type': 'part', }), ), (5, TD({'reward_info': 310502, 'reward_type': 'part', }), ), (5, TD({'reward_info': 310802, 'reward_type': 'part', }), ), (6, TD({'reward_info': 310402, 'reward_type': 'part', }), ), (7, TD({'reward_info': 310702, 'reward_type': 'part', }), ), (8, TD({'reward_info': 310803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 310203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 310404, 'reward_type': 'part', }), ), (14, TD({'reward_info': 310704, 'reward_type': 'part', }), ), (15, TD({'reward_info': 310303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 310804, 'reward_type': 'part', }), ), (18, TD({'reward_info': 310805, 'reward_type': 'part', }), ), (21, TD({'reward_info': 310304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 310806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 310703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 310305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 310705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 310306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 310311, 'reward_type': 'part', }), ), (28, TD({'reward_info': 310403, 'reward_type': 'part', }), ), (29, TD({'reward_info': 310503, 'reward_type': 'part', }), ), (32, TD({'reward_info': 310505, 'reward_type': 'part', }), ), (33, TD({'reward_info': 310307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 310706, 'reward_type': 'part', }), ), (35, TD({'reward_info': 310506, 'reward_type': 'part', }), ), (36, TD({'reward_info': 310312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 310807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 310504, 'reward_type': 'part', }), ), (40, TD({'reward_info': 310405, 'reward_type': 'part', }), ), (42, TD({'reward_info': 310308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 310707, 'reward_type': 'part', }), ), (45, TD({'reward_info': 310313, 'reward_type': 'part', }), ), (47, TD({'reward_info': 310808, 'reward_type': 'part', }), ), (48, TD({'reward_info': 310309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 310204, 'reward_type': 'part', }), ), ), 
    32: ((1, TD({'reward_info': 3203110001, 'reward_type': 'part', }), ), (1, TD({'reward_info': 320302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 320310, 'reward_type': 'part', }), ), (4, TD({'reward_info': 320202, 'reward_type': 'part', }), ), (5, TD({'reward_info': 320502, 'reward_type': 'part', }), ), (5, TD({'reward_info': 320802, 'reward_type': 'part', }), ), (6, TD({'reward_info': 320402, 'reward_type': 'part', }), ), (7, TD({'reward_info': 320702, 'reward_type': 'part', }), ), (8, TD({'reward_info': 320803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 320203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 320404, 'reward_type': 'part', }), ), (14, TD({'reward_info': 320704, 'reward_type': 'part', }), ), (15, TD({'reward_info': 320303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 320804, 'reward_type': 'part', }), ), (18, TD({'reward_info': 320805, 'reward_type': 'part', }), ), (21, TD({'reward_info': 320304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 320806, 'reward_type': 'part', }), ), (23, TD({'reward_info': 320505, 'reward_type': 'part', }), ), (24, TD({'reward_info': 320703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 320305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 320705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 320306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 320311, 'reward_type': 'part', }), ), (28, TD({'reward_info': 320403, 'reward_type': 'part', }), ), (29, TD({'reward_info': 320503, 'reward_type': 'part', }), ), (33, TD({'reward_info': 320307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 320706, 'reward_type': 'part', }), ), (36, TD({'reward_info': 320312, 'reward_type': 'part', }), ), (37, TD({'reward_info': 320809, 'reward_type': 'part', }), ), (38, TD({'reward_info': 320810, 'reward_type': 'part', }), ), (38, TD({'reward_info': 320807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 320504, 'reward_type': 'part', }), ), (40, TD({'reward_info': 320405, 'reward_type': 'part', }), ), (42, TD({'reward_info': 320308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 320707, 'reward_type': 'part', }), ), (45, TD({'reward_info': 320313, 'reward_type': 'part', }), ), (46, TD({'reward_info': 320808, 'reward_type': 'part', }), ), (47, TD({'reward_info': 320811, 'reward_type': 'part', }), ), (48, TD({'reward_info': 320309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 320204, 'reward_type': 'part', }), ), ), 
    33: ((1, TD({'reward_info': 3303140001, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 330302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 330802, 'reward_type': 'part', }), ), (5, TD({'reward_info': 330403, 'reward_type': 'part', }), ), (7, TD({'reward_info': 330310, 'reward_type': 'part', }), ), (10, TD({'reward_info': 330202, 'reward_type': 'part', }), ), (13, TD({'reward_info': 330803, 'reward_type': 'part', }), ), (14, TD({'reward_info': 330303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 330311, 'reward_type': 'part', }), ), (18, TD({'reward_info': 330304, 'reward_type': 'part', }), ), (20, TD({'reward_info': 330502, 'reward_type': 'part', }), ), (23, TD({'reward_info': 330305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 330804, 'reward_type': 'part', }), ), (28, TD({'reward_info': 330306, 'reward_type': 'part', }), ), (30, TD({'reward_info': 330402, 'reward_type': 'part', }), ), (32, TD({'reward_info': 330307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 330805, 'reward_type': 'part', }), ), (37, TD({'reward_info': 330313, 'reward_type': 'part', }), ), (40, TD({'reward_info': 330806, 'reward_type': 'part', }), ), (42, TD({'reward_info': 330308, 'reward_type': 'part', }), ), (45, TD({'reward_info': 330312, 'reward_type': 'part', }), ), (48, TD({'reward_info': 330309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 330203, 'reward_type': 'part', }), ), (50, TD({'reward_info': 330314, 'reward_type': 'part', }), ), ), 
    34: ((1, TD({'reward_info': 340302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 340310, 'reward_type': 'part', }), ), (4, TD({'reward_info': 340202, 'reward_type': 'part', }), ), (5, TD({'reward_info': 340502, 'reward_type': 'part', }), ), (5, TD({'reward_info': 340802, 'reward_type': 'part', }), ), (6, TD({'reward_info': 340402, 'reward_type': 'part', }), ), (7, TD({'reward_info': 340702, 'reward_type': 'part', }), ), (8, TD({'reward_info': 340803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 340203, 'reward_type': 'part', }), ), (14, TD({'reward_info': 340704, 'reward_type': 'part', }), ), (15, TD({'reward_info': 340303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 340804, 'reward_type': 'part', }), ), (18, TD({'reward_info': 340805, 'reward_type': 'part', }), ), (21, TD({'reward_info': 340304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 340806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 340703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 340305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 340705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 340306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 340311, 'reward_type': 'part', }), ), (29, TD({'reward_info': 340503, 'reward_type': 'part', }), ), (33, TD({'reward_info': 340307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 340706, 'reward_type': 'part', }), ), (36, TD({'reward_info': 340312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 340807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 340504, 'reward_type': 'part', }), ), (40, TD({'reward_info': 340405, 'reward_type': 'part', }), ), (42, TD({'reward_info': 340308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 340707, 'reward_type': 'part', }), ), (43, TD({'reward_info': 340708, 'reward_type': 'part', }), ), (45, TD({'reward_info': 340313, 'reward_type': 'part', }), ), (47, TD({'reward_info': 340810, 'reward_type': 'part', }), ), (47, TD({'reward_info': 340808, 'reward_type': 'part', }), ), (47, TD({'reward_info': 340809, 'reward_type': 'part', }), ), (48, TD({'reward_info': 340309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 340204, 'reward_type': 'part', }), ), ), 
    35: ((1, TD({'reward_info': 350302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 350310, 'reward_type': 'part', }), ), (5, TD({'reward_info': 350202, 'reward_type': 'part', }), ), (5, TD({'reward_info': 350402, 'reward_type': 'part', }), ), (5, TD({'reward_info': 350502, 'reward_type': 'part', }), ), (5, TD({'reward_info': 350702, 'reward_type': 'part', }), ), (5, TD({'reward_info': 350802, 'reward_type': 'part', }), ), (8, TD({'reward_info': 350803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 350203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 350404, 'reward_type': 'part', }), ), (14, TD({'reward_info': 350704, 'reward_type': 'part', }), ), (15, TD({'reward_info': 350303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 350804, 'reward_type': 'part', }), ), (18, TD({'reward_info': 350805, 'reward_type': 'part', }), ), (20, TD({'reward_info': 350405, 'reward_type': 'part', }), ), (21, TD({'reward_info': 350304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 350806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 350703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 350305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 350705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 350306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 350311, 'reward_type': 'part', }), ), (29, TD({'reward_info': 350503, 'reward_type': 'part', }), ), (30, TD({'reward_info': 350205, 'reward_type': 'part', }), ), (32, TD({'reward_info': 350505, 'reward_type': 'part', }), ), (33, TD({'reward_info': 350307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 350706, 'reward_type': 'part', }), ), (36, TD({'reward_info': 350312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 350807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 350504, 'reward_type': 'part', }), ), (40, TD({'reward_info': 350403, 'reward_type': 'part', }), ), (42, TD({'reward_info': 350308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 350707, 'reward_type': 'part', }), ), (45, TD({'reward_info': 350313, 'reward_type': 'part', }), ), (47, TD({'reward_info': 350808, 'reward_type': 'part', }), ), (48, TD({'reward_info': 350309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 350204, 'reward_type': 'part', }), ), (50, TD({'reward_info': 350206, 'reward_type': 'part', }), ), ), 
    36: ((1, TD({'reward_info': 0, 'reward_type': 'view', }), ), ), 
    37: ((1, TD({'reward_info': 370302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 370310, 'reward_type': 'part', }), ), (4, TD({'reward_info': 370202, 'reward_type': 'part', }), ), (5, TD({'reward_info': 370502, 'reward_type': 'part', }), ), (5, TD({'reward_info': 370802, 'reward_type': 'part', }), ), (6, TD({'reward_info': 370402, 'reward_type': 'part', }), ), (7, TD({'reward_info': 370702, 'reward_type': 'part', }), ), (8, TD({'reward_info': 370803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 370203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 370403, 'reward_type': 'part', }), ), (14, TD({'reward_info': 370704, 'reward_type': 'part', }), ), (15, TD({'reward_info': 370303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 370804, 'reward_type': 'part', }), ), (18, TD({'reward_info': 370805, 'reward_type': 'part', }), ), (21, TD({'reward_info': 370304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 370806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 370703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 370305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 370705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 370306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 370311, 'reward_type': 'part', }), ), (29, TD({'reward_info': 370503, 'reward_type': 'part', }), ), (33, TD({'reward_info': 370307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 370706, 'reward_type': 'part', }), ), (36, TD({'reward_info': 370312, 'reward_type': 'part', }), ), (37, TD({'reward_info': 370809, 'reward_type': 'part', }), ), (38, TD({'reward_info': 370807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 370810, 'reward_type': 'part', }), ), (40, TD({'reward_info': 370404, 'reward_type': 'part', }), ), (42, TD({'reward_info': 370308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 370707, 'reward_type': 'part', }), ), (45, TD({'reward_info': 370313, 'reward_type': 'part', }), ), (46, TD({'reward_info': 370808, 'reward_type': 'part', }), ), (47, TD({'reward_info': 370811, 'reward_type': 'part', }), ), (48, TD({'reward_info': 370309, 'reward_type': 'part', }), ), (49, TD({'reward_info': 370504, 'reward_type': 'part', }), ), (50, TD({'reward_info': 370204, 'reward_type': 'part', }), ), ), 
    38: ((1, TD({'reward_info': 380302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 380310, 'reward_type': 'part', }), ), (4, TD({'reward_info': 380202, 'reward_type': 'part', }), ), (5, TD({'reward_info': 380802, 'reward_type': 'part', }), ), (6, TD({'reward_info': 380402, 'reward_type': 'part', }), ), (7, TD({'reward_info': 380702, 'reward_type': 'part', }), ), (8, TD({'reward_info': 380803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 380203, 'reward_type': 'part', }), ), (14, TD({'reward_info': 380704, 'reward_type': 'part', }), ), (15, TD({'reward_info': 380303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 380804, 'reward_type': 'part', }), ), (18, TD({'reward_info': 380805, 'reward_type': 'part', }), ), (21, TD({'reward_info': 380304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 380806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 380703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 380305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 380705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 380306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 380311, 'reward_type': 'part', }), ), (28, TD({'reward_info': 380403, 'reward_type': 'part', }), ), (29, TD({'reward_info': 380502, 'reward_type': 'part', }), ), (33, TD({'reward_info': 380307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 380706, 'reward_type': 'part', }), ), (35, TD({'reward_info': 380809, 'reward_type': 'part', }), ), (36, TD({'reward_info': 380312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 380810, 'reward_type': 'part', }), ), (38, TD({'reward_info': 380807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 380503, 'reward_type': 'part', }), ), (40, TD({'reward_info': 380404, 'reward_type': 'part', }), ), (42, TD({'reward_info': 380308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 380707, 'reward_type': 'part', }), ), (45, TD({'reward_info': 380313, 'reward_type': 'part', }), ), (46, TD({'reward_info': 380808, 'reward_type': 'part', }), ), (47, TD({'reward_info': 380811, 'reward_type': 'part', }), ), (48, TD({'reward_info': 380309, 'reward_type': 'part', }), ), (49, TD({'reward_info': 380204, 'reward_type': 'part', }), ), (50, TD({'reward_info': 380504, 'reward_type': 'part', }), ), (50, TD({'reward_info': 380505, 'reward_type': 'part', }), ), ), 
    39: ((1, TD({'reward_info': 390302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 390902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 390903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 390904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 390310, 'reward_type': 'part', }), ), (5, TD({'reward_info': 390802, 'reward_type': 'part', }), ), (7, TD({'reward_info': 390702, 'reward_type': 'part', }), ), (8, TD({'reward_info': 390803, 'reward_type': 'part', }), ), (14, TD({'reward_info': 390704, 'reward_type': 'part', }), ), (15, TD({'reward_info': 390303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 390804, 'reward_type': 'part', }), ), (18, TD({'reward_info': 390805, 'reward_type': 'part', }), ), (21, TD({'reward_info': 390304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 390806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 390703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 390305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 390705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 390306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 390311, 'reward_type': 'part', }), ), (29, TD({'reward_info': 390502, 'reward_type': 'part', }), ), (33, TD({'reward_info': 390307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 390706, 'reward_type': 'part', }), ), (35, TD({'reward_info': 390809, 'reward_type': 'part', }), ), (36, TD({'reward_info': 390312, 'reward_type': 'part', }), ), (38, TD({'reward_info': 390810, 'reward_type': 'part', }), ), (38, TD({'reward_info': 390807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 390503, 'reward_type': 'part', }), ), (40, TD({'reward_info': 390403, 'reward_type': 'part', }), ), (42, TD({'reward_info': 390308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 390707, 'reward_type': 'part', }), ), (45, TD({'reward_info': 390313, 'reward_type': 'part', }), ), (46, TD({'reward_info': 390808, 'reward_type': 'part', }), ), (47, TD({'reward_info': 390811, 'reward_type': 'part', }), ), (48, TD({'reward_info': 390309, 'reward_type': 'part', }), ), (49, TD({'reward_info': 390204, 'reward_type': 'part', }), ), ), 
    4: ((1, TD({'reward_info': 1500000880, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 40902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 40903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 40904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 40802, 'reward_type': 'part', }), ), (5, TD({'reward_info': 40502, 'reward_type': 'part', }), ), (7, TD({'reward_info': 40302, 'reward_type': 'part', }), ), (10, TD({'reward_info': 40202, 'reward_type': 'part', }), ), (12, TD({'reward_info': 40303, 'reward_type': 'part', }), ), (14, TD({'reward_info': 40803, 'reward_type': 'part', }), ), (17, TD({'reward_info': 40304, 'reward_type': 'part', }), ), (20, TD({'reward_info': 1500000900, 'reward_type': 'skin', }), ), (20, TD({'reward_info': 40503, 'reward_type': 'part', }), ), (23, TD({'reward_info': 40804, 'reward_type': 'part', }), ), (26, TD({'reward_info': 40805, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1500000920, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 40203, 'reward_type': 'part', }), ), (35, TD({'reward_info': 40504, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1500000820, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 1500000840, 'reward_type': 'skin', }), ), (60, TD({'reward_info': 1500000860, 'reward_type': 'skin', }), ), (60, TD({'reward_info': 1500000940, 'reward_type': 'skin', }), ), ), 
    40: ((1, TD({'reward_info': 400603, 'reward_type': 'part', }), ), (1, TD({'reward_info': 400604, 'reward_type': 'part', }), ), (1, TD({'reward_info': 400605, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 400302, 'reward_type': 'part', }), ), (3, TD({'reward_info': 400502, 'reward_type': 'part', }), ), (4, TD({'reward_info': 400702, 'reward_type': 'part', }), ), (6, TD({'reward_info': 400802, 'reward_type': 'part', }), ), (8, TD({'reward_info': 400803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 400202, 'reward_type': 'part', }), ), (12, TD({'reward_info': 400703, 'reward_type': 'part', }), ), (13, TD({'reward_info': 400304, 'reward_type': 'part', }), ), (14, TD({'reward_info': 400804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 400807, 'reward_type': 'part', }), ), (20, TD({'reward_info': 400402, 'reward_type': 'part', }), ), (21, TD({'reward_info': 400305, 'reward_type': 'part', }), ), (22, TD({'reward_info': 400806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 400707, 'reward_type': 'part', }), ), (25, TD({'reward_info': 400306, 'reward_type': 'part', }), ), (26, TD({'reward_info': 400704, 'reward_type': 'part', }), ), (27, TD({'reward_info': 400307, 'reward_type': 'part', }), ), (28, TD({'reward_info': 400311, 'reward_type': 'part', }), ), (30, TD({'reward_info': 400203, 'reward_type': 'part', }), ), (32, TD({'reward_info': 400503, 'reward_type': 'part', }), ), (34, TD({'reward_info': 400308, 'reward_type': 'part', }), ), (36, TD({'reward_info': 400706, 'reward_type': 'part', }), ), (38, TD({'reward_info': 400805, 'reward_type': 'part', }), ), (40, TD({'reward_info': 400705, 'reward_type': 'part', }), ), (42, TD({'reward_info': 400309, 'reward_type': 'part', }), ), (44, TD({'reward_info': 400808, 'reward_type': 'part', }), ), (45, TD({'reward_info': 400504, 'reward_type': 'part', }), ), (46, TD({'reward_info': 400404, 'reward_type': 'part', }), ), (48, TD({'reward_info': 400312, 'reward_type': 'part', }), ), (50, TD({'reward_info': 400204, 'reward_type': 'part', }), ), (50, TD({'reward_info': 400602, 'reward_type': 'part', }), ), ), 
    41: ((1, TD({'reward_info': 410502, 'reward_type': 'part', }), ), (1, TD({'reward_info': 410503, 'reward_type': 'part', }), ), (1, TD({'reward_info': 410902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 410903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 410904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), ), 
    42: ((1, TD({'reward_info': 420302, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (3, TD({'reward_info': 420310, 'reward_type': 'part', }), ), (4, TD({'reward_info': 420202, 'reward_type': 'part', }), ), (5, TD({'reward_info': 420502, 'reward_type': 'part', }), ), (5, TD({'reward_info': 420802, 'reward_type': 'part', }), ), (6, TD({'reward_info': 420402, 'reward_type': 'part', }), ), (7, TD({'reward_info': 420702, 'reward_type': 'part', }), ), (8, TD({'reward_info': 420803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 420203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 420403, 'reward_type': 'part', }), ), (14, TD({'reward_info': 420704, 'reward_type': 'part', }), ), (15, TD({'reward_info': 420303, 'reward_type': 'part', }), ), (16, TD({'reward_info': 420804, 'reward_type': 'part', }), ), (18, TD({'reward_info': 420805, 'reward_type': 'part', }), ), (21, TD({'reward_info': 420304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 420806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 420703, 'reward_type': 'part', }), ), (25, TD({'reward_info': 420305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 420705, 'reward_type': 'part', }), ), (27, TD({'reward_info': 420306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 420311, 'reward_type': 'part', }), ), (29, TD({'reward_info': 420503, 'reward_type': 'part', }), ), (33, TD({'reward_info': 420307, 'reward_type': 'part', }), ), (34, TD({'reward_info': 420706, 'reward_type': 'part', }), ), (36, TD({'reward_info': 420312, 'reward_type': 'part', }), ), (37, TD({'reward_info': 420809, 'reward_type': 'part', }), ), (38, TD({'reward_info': 420807, 'reward_type': 'part', }), ), (39, TD({'reward_info': 420810, 'reward_type': 'part', }), ), (40, TD({'reward_info': 420404, 'reward_type': 'part', }), ), (42, TD({'reward_info': 420308, 'reward_type': 'part', }), ), (43, TD({'reward_info': 420707, 'reward_type': 'part', }), ), (45, TD({'reward_info': 420313, 'reward_type': 'part', }), ), (46, TD({'reward_info': 420808, 'reward_type': 'part', }), ), (47, TD({'reward_info': 420811, 'reward_type': 'part', }), ), (48, TD({'reward_info': 420309, 'reward_type': 'part', }), ), (49, TD({'reward_info': 420504, 'reward_type': 'part', }), ), (50, TD({'reward_info': 420204, 'reward_type': 'part', }), ), ), 
    43: ((1, TD({'reward_info': 430303, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (13, TD({'reward_info': 430302, 'reward_type': 'part', }), ), ), 
    6: ((1, TD({'reward_info': 1300000880, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 60702, 'reward_type': 'part', }), ), (1, TD({'reward_info': 60902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 60903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 60904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 60703, 'reward_type': 'part', }), ), (3, TD({'reward_info': 60802, 'reward_type': 'part', }), ), (4, TD({'reward_info': 60303, 'reward_type': 'part', }), ), (5, TD({'reward_info': 60402, 'reward_type': 'part', }), ), (6, TD({'reward_info': 60502, 'reward_type': 'part', }), ), (8, TD({'reward_info': 60310, 'reward_type': 'part', }), ), (10, TD({'reward_info': 60202, 'reward_type': 'part', }), ), (12, TD({'reward_info': 60704, 'reward_type': 'part', }), ), (14, TD({'reward_info': 60306, 'reward_type': 'part', }), ), (15, TD({'reward_info': 60403, 'reward_type': 'part', }), ), (16, TD({'reward_info': 60503, 'reward_type': 'part', }), ), (16, TD({'reward_info': 60803, 'reward_type': 'part', }), ), (17, TD({'reward_info': 60305, 'reward_type': 'part', }), ), (18, TD({'reward_info': 60311, 'reward_type': 'part', }), ), (20, TD({'reward_info': 60203, 'reward_type': 'part', }), ), (20, TD({'reward_info': 60804, 'reward_type': 'part', }), ), (21, TD({'reward_info': 60309, 'reward_type': 'part', }), ), (23, TD({'reward_info': 60308, 'reward_type': 'part', }), ), (24, TD({'reward_info': 60307, 'reward_type': 'part', }), ), (27, TD({'reward_info': 60805, 'reward_type': 'part', }), ), (28, TD({'reward_info': 60312, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1300000900, 'reward_type': 'skin', }), ), (30, TD({'reward_info': 60204, 'reward_type': 'part', }), ), (31, TD({'reward_info': 60304, 'reward_type': 'part', }), ), (34, TD({'reward_info': 60302, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1300000920, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 60806, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1300000820, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 60504, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1300000840, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1300000860, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1300000940, 'reward_type': 'skin', }), ), ), 
    8: ((1, TD({'reward_info': 1200001320, 'reward_type': 'skin', }), ), (1, TD({'reward_info': 80902, 'reward_type': 'part', }), ), (1, TD({'reward_info': 80903, 'reward_type': 'part', }), ), (1, TD({'reward_info': 80904, 'reward_type': 'part', }), ), (1, TD({'reward_info': 0, 'reward_type': 'view', }), ), (2, TD({'reward_info': 80302, 'reward_type': 'part', }), ), (4, TD({'reward_info': 80702, 'reward_type': 'part', }), ), (5, TD({'reward_info': 80404, 'reward_type': 'part', }), ), (6, TD({'reward_info': 80802, 'reward_type': 'part', }), ), (8, TD({'reward_info': 80803, 'reward_type': 'part', }), ), (10, TD({'reward_info': 80203, 'reward_type': 'part', }), ), (12, TD({'reward_info': 80703, 'reward_type': 'part', }), ), (13, TD({'reward_info': 80303, 'reward_type': 'part', }), ), (14, TD({'reward_info': 80804, 'reward_type': 'part', }), ), (16, TD({'reward_info': 80807, 'reward_type': 'part', }), ), (18, TD({'reward_info': 80310, 'reward_type': 'part', }), ), (20, TD({'reward_info': 80403, 'reward_type': 'part', }), ), (21, TD({'reward_info': 80304, 'reward_type': 'part', }), ), (22, TD({'reward_info': 80806, 'reward_type': 'part', }), ), (24, TD({'reward_info': 80707, 'reward_type': 'part', }), ), (25, TD({'reward_info': 80305, 'reward_type': 'part', }), ), (26, TD({'reward_info': 80704, 'reward_type': 'part', }), ), (27, TD({'reward_info': 80306, 'reward_type': 'part', }), ), (28, TD({'reward_info': 80311, 'reward_type': 'part', }), ), (30, TD({'reward_info': 1200001350, 'reward_type': 'skin', }), ), (32, TD({'reward_info': 80504, 'reward_type': 'part', }), ), (34, TD({'reward_info': 80307, 'reward_type': 'part', }), ), (36, TD({'reward_info': 80706, 'reward_type': 'part', }), ), (38, TD({'reward_info': 80805, 'reward_type': 'part', }), ), (40, TD({'reward_info': 1200001380, 'reward_type': 'skin', }), ), (40, TD({'reward_info': 80705, 'reward_type': 'part', }), ), (42, TD({'reward_info': 80308, 'reward_type': 'part', }), ), (44, TD({'reward_info': 80312, 'reward_type': 'part', }), ), (46, TD({'reward_info': 80402, 'reward_type': 'part', }), ), (48, TD({'reward_info': 80808, 'reward_type': 'part', }), ), (49, TD({'reward_info': 80309, 'reward_type': 'part', }), ), (50, TD({'reward_info': 1200001230, 'reward_type': 'skin', }), ), (50, TD({'reward_info': 80202, 'reward_type': 'part', }), ), (60, TD({'reward_info': 1200001260, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200001290, 'reward_type': 'skin', }), ), (70, TD({'reward_info': 1200001410, 'reward_type': 'skin', }), ), ), 
}
