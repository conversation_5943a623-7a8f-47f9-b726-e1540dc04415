# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 42-英雄系统表.xlsx, sheetname:角色演绎表
from taggeddict import taggeddict as TD

data = {
    101: TD({
        0: TD({
            'id': '101.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
        1: TD({
            'id': '101.1',
            'hero_skeleton': 400005,
            'hero_sanim': 'AS_TPS_Ethan_OutSide_01_Select_Play_anim',
            'other_model_anim': TD({
                400006: 'AS_Item_Pistol_OutSide_01_Select_Play', 
                400007: 'AS_Item_Pistol_OutSide_02_Select_Play', 
            }),
            'Cinematic_file': 'Graph/Cinematic/Intro/Ethan/Light_Ethan.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Ethan/Camera_Ethan.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    106: TD({
        0: TD({
            'id': '106.0',
            'hero_skeleton': 400008,
            'hero_sanim': 'AS_TPS_Katya_OutSide_01_Select_Play',
            'other_model_anim': TD({
                400009: 'AS_Item_Default_KuWu_OutSide_02_Select_Play', 
            }),
            'Cinematic_file': 'Graph/Cinematic/Intro/Katya/Light_Katya.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Katya/Camera_Katya.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    107: TD({
        0: TD({
            'id': '107.0',
            'hero_skeleton': 400001,
            'hero_sanim': 'emt_Animation_v011',
            'Cinematic_file': 'Graph/Cinematic/Intro/EMT/Light_EMT.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
        1: TD({
            'id': '107.1',
            'hero_skeleton': 400003,
            'hero_sanim': 'AS_TPS_EMT_OutSide_01_Select_Play',
            'other_model_anim': TD({
                400004: 'AS_Cat_OutSide_Default_01_Select_Play', 
            }),
            'Cinematic_file': 'Graph/Cinematic/Intro/EMT/Light_EMT.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/EMT/Camera_EMT.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    112: TD({
        0: TD({
            'id': '112.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    116: TD({
        0: TD({
            'id': '116.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    117: TD({
        0: TD({
            'id': '117.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    118: TD({
        0: TD({
            'id': '118.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
        1: TD({
            'id': '118.1',
            'hero_skeleton': 400008,
            'hero_sanim': 'AS_TPS_Katya_OutSide_01_Select_Play',
            'other_model_anim': TD({
                400009: 'AS_Item_Default_KuWu_OutSide_02_Select_Play', 
            }),
            'Cinematic_file': 'Graph/Cinematic/Intro/Katya/Light_Katya.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Katya/Camera_Katya.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    119: TD({
        0: TD({
            'id': '119.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
        1: TD({
            'id': '119.1',
            'hero_skeleton': 400010,
            'hero_sanim': 'AS_TPS_Marok_OutSide_Select_Play',
            'other_model_anim': TD({
                400011: 'AS_SKM_Shark_Default_01_OutSide_Select_Play', 
            }),
            'Cinematic_file': 'Graph/Cinematic/Intro/Marok/Light_Marok.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Marok/Camera_Marok.cine',
            'init_with_dissolve': False,
        }), 
    }), 
    120: TD({
        0: TD({
            'id': '120.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    121: TD({
        0: TD({
            'id': '121.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    122: TD({
        0: TD({
            'id': '122.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    123: TD({
        0: TD({
            'id': '123.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    124: TD({
        0: TD({
            'id': '124.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    125: TD({
        0: TD({
            'id': '125.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    126: TD({
        0: TD({
            'id': '126.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    127: TD({
        0: TD({
            'id': '127.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    128: TD({
        0: TD({
            'id': '128.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    129: TD({
        0: TD({
            'id': '129.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
    130: TD({
        0: TD({
            'id': '130.0',
            'Cinematic_file': 'Graph/Cinematic/Intro/Light_Intro_Idle.cine',
            'Camera_Cinematic_file': 'Graph/Cinematic/Intro/Camera_Intro_Idle.cine',
            'init_with_dissolve': True,
        }), 
    }), 
}
