# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 5-场景地图表.xlsx, sheetname:玩法数据表（客户端模式选择用）
from taggeddict import taggeddict as TD

data = {
    28: TD({
        'id': 28,
        'real_match': 28,
        'show_coin': True,
        'space': (62, ),
        'unlock_sys_id': 7,
        'show': True,
        'match_mode': (1, ),
        'prefix_match_name': '练习模式',
        'match_name': '练习模式',
        'desc': '自由磨练竞技技巧',
        'leisure_sort_key': 7,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }), 
    29: TD({
        'id': 29,
        'skip': 'trunk_only',
        'real_match': 29,
        'show_coin': True,
        'space': (2, 36, 62, 63, 64, 67, 68, 69, 42, 47, 55, 73, ),
        'show': True,
        'match_mode': (1, ),
        'prefix_match_name': '开发靶场',
        'match_name': '开发靶场',
        'desc': '自由磨练竞技技巧',
        'leisure_sort_key': 8,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }), 
    31: TD({
        'id': 31,
        'real_match': 31,
        'show_coin': True,
        'space': (40, 46, 56, 32, 41, 53, 54, 51, 60, 61, 65, 69, 70, 76, 77, 78, 79, 80, 81, 82, ),
        'show': True,
        'match_mode': (1, ),
        'prefix_match_name': '地图预览',
        'match_name': '地图预览',
        'leisure_sort_key': 9,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }), 
    32: TD({
        'id': 32,
        'skip': 'trunk_only',
        'real_match': 32,
        'show_coin': True,
        'space': (2, 41, 56, 51, 61, 65, 67, 69, ),
        'show': True,
        'match_mode': (1, 2, 4, ),
        'prefix_match_name': '战术竞技靶场',
        'match_name': '战术竞技靶场',
        'leisure_sort_key': 10,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }), 
    33: TD({
        'id': 33,
        'skip': 'trunk_only',
        'real_match': 33,
        'show_coin': True,
        'space': (2, 41, 56, 51, 61, 65, 67, ),
        'show': True,
        'match_mode': (1, 2, 4, ),
        'prefix_match_name': '私人战术竞技靶场',
        'match_name': '私人战术竞技靶场',
        'leisure_sort_key': 12,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }), 
    1004: TD({
        'id': 1004,
        'real_match': 1004,
        'show_coin': True,
        'space': (61, 65, ),
        'unlock_sys_id': 7,
        'show': True,
        'match_mode': (1, 4, ),
        'prefix_match_name': '战术竞技',
        'match_name': '战术竞技-排位',
        'desc': '在100人的极限战场中，存活到最后，争取荣誉',
        'leisure_sort_key': 1,
        'show_panel_map': True,
        'match_icon_size': 0,
        'match_icon_big': 100001,
        'match_icon_small': 100001,
        'show_rule_info': True,
        'rule_info_ui': 'UIScript/ig_main_guide.csb',
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    1006: TD({
        'id': 1006,
        'real_match': 1006,
        'show_coin': True,
        'space': (61, 65, ),
        'unlock_sys_id': 8,
        'show': True,
        'match_mode': (1, 4, ),
        'prefix_match_name': '战术竞技',
        'match_name': '战术竞技-休闲',
        'desc': '休闲模式，不计入排位分数',
        'leisure_sort_key': 5,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100004,
        'match_icon_small': 100004,
        'show_rule_info': True,
        'rule_info_ui': 'UIScript/ig_main_guide.csb',
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    1100: TD({
        'id': 1100,
        'real_match': 1100,
        'show_coin': True,
        'space': (32, 41, 53, 54, 77, 78, 79, 80, 81, 82, ),
        'unlock_sys_id': 11,
        'show': True,
        'match_mode': (6, ),
        'force_team_match': True,
        'prefix_match_name': '热点战',
        'match_name': '热点战',
        'desc': '6V6占点对抗，率先达到目标分数的队伍胜利',
        'leisure_sort_key': 4,
        'show_panel_map': True,
        'match_icon_size': 0,
        'match_icon_big': 100003,
        'match_icon_small': 100003,
    }), 
    1200: TD({
        'id': 1200,
        'real_match': 2000,
        'show_coin': True,
        'space': (32, 41, 53, 54, ),
        'unlock_sys_id': 10,
        'show': True,
        'match_mode': (4, ),
        'prefix_match_name': '团队竞技',
        'match_name': '小队竞技-休闲',
        'desc': '休闲模式，不计入排位分数',
        'leisure_sort_key': 6,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100005,
        'match_icon_small': 100005,
    }), 
    1202: TD({
        'id': 1202,
        'real_match': 1004,
        'show_coin': True,
        'space': (32, 41, 53, 54, ),
        'unlock_sys_id': 9,
        'show': True,
        'match_mode': (4, ),
        'prefix_match_name': '战术竞技',
        'match_name': '小队竞技-排位',
        'desc': '4V4回合制战术对抗，率先赢下4个回合的队伍胜利',
        'leisure_sort_key': 2,
        'show_panel_map': True,
        'match_icon_size': 0,
        'match_icon_big': 100002,
        'match_icon_small': 100002,
        'show_rule_info': True,
        'rule_info_ui': 'UIScript/ig_main_guide.csb',
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    1600: TD({
        'id': 1600,
        'real_match': 1600,
        'show_coin': True,
        'space': (62, ),
        'show': False,
        'match_mode': (1, ),
        'prefix_match_name': '战术竞技靶场',
        'match_name': '战术竞技靶场',
        'leisure_sort_key': 11,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }), 
    2000: TD({
        'id': 2000,
        'real_match': 2000,
        'show_coin': True,
        'space': (32, 41, 53, 54, ),
        'unlock_sys_id': 11,
        'show': True,
        'match_mode': (6, ),
        'force_team_match': True,
        'prefix_match_name': '团队竞技',
        'match_name': '团队竞技',
        'desc': '6V6对抗，率先达到目标分数的队伍胜利',
        'leisure_sort_key': 3,
        'show_panel_map': True,
        'match_icon_size': 0,
        'match_icon_big': 100003,
        'match_icon_small': 100003,
    }), 
}
