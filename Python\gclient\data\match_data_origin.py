# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 5-场景地图表.xlsx, sheetname:玩法数据表
from taggeddict import taggeddict as TD

data = {
    1: TD({
        'id': 1,
        'skip': 'trunk_only',
        'space': (1, ),
        'game_logic': 'Nowhere',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'mode_type': 2,
        'prefix_match_name': 'Nowhere#debug',
        'desc': 'Nowhere#debug',
        'max_count': 10,
        'max_team_count': 10,
    }), 
    2: TD({
        'id': 2,
        'skip': 'trunk_only',
        'space': (32, ),
        'game_logic': 'Nowhere',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'mode_type': 2,
        'prefix_match_name': 'Nowhere#debug',
        'desc': 'Nowhere#debug',
        'max_count': 10,
        'max_team_count': 10,
    }), 
    15: TD({
        'id': 15,
        'space': (51, ),
        'game_logic': 'Placement',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'max_count': 2,
        'max_team_count': 2,
    }), 
    28: TD({
        'id': 28,
        'space': (62, ),
        'game_logic': 'ShootingRange',
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, ),
        'match_algo': TD({
            'name': 'MatchQueueShootingRange', 
        }),
        'prefix_match_name': '练习模式',
        'match_name': '练习模式',
        'leisure_sort_key': 12,
        'max_count': 1,
        'max_team_count': 1,
        'match_icon_big': 10687,
        'match_icon_small': 10580,
    }), 
    29: TD({
        'id': 29,
        'skip': 'trunk_only',
        'space': (36, 62, 63, 64, 67, 68, 69, 42, 47, 55, 2, 73, ),
        'game_logic': 'ShootingRange',
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 2, ),
        'match_algo': TD({
            'name': 'MatchQueueShootingRange', 
        }),
        'prefix_match_name': '靶场',
        'match_name': '靶场',
        'leisure_sort_key': 6,
        'max_count': 1,
        'max_team_count': 1,
        'match_icon_big': 10687,
        'match_icon_small': 10580,
    }), 
    31: TD({
        'id': 31,
        'space': (40, 46, 56, 32, 41, 53, 54, 51, 60, 61, 65, 2, 69, 70, 76, 77, 78, 79, 80, 81, 82, ),
        'game_logic': 'ShootingRange',
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 2, ),
        'match_algo': TD({
            'name': 'MatchQueueShootingRange', 
        }),
        'prefix_match_name': '地图预览',
        'match_name': '地图预览',
        'leisure_sort_key': 11,
        'max_count': 1,
        'max_team_count': 1,
        'match_icon_big': 10687,
        'match_icon_small': 10580,
    }), 
    32: TD({
        'id': 32,
        'skip': 'trunk_only',
        'space': (2, 41, 56, 51, 61, 65, 67, 69, ),
        'game_logic': 'MobaShootingRange',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'mode_type': 2,
        'prefix_match_name': 'Nowhere#debug',
        'desc': 'Nowhere#debug',
        'max_count': 10,
        'max_team_count': 10,
    }), 
    33: TD({
        'id': 33,
        'skip': 'trunk_only',
        'space': (2, 41, 56, 51, 61, 65, 67, ),
        'game_logic': 'MobaShootingRange',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'mode_type': 2,
        'prefix_match_name': 'Nowhere#debug',
        'desc': 'Nowhere#debug',
        'max_count': 1,
        'max_team_count': 1,
    }), 
    1002: TD({
        'id': 1002,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 1,
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': TD({
            'name': 'MatchQueueMobaNewer', 
        }),
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1004,
        'prefix_match_name': '排位(新手)#debug',
        'desc': '玩起来#debug',
        'show_panel_map': True,
        'max_count': 1,
        'max_team_count': 1,
        'hall_waiting_time': 1,
        'vehicle_medias': ('vehicle_common_media', 'vehicle_pickup_media', 'vehicle_helicopter_media', ),
        'match_icon_big': 10685,
        'match_icon_small': 10579,
        'show_rule_info': True,
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    1003: TD({
        'id': 1003,
        'space': (2, 61, 65, ),
        'game_logic': 'MobaFirstNew',
        'type': 1,
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, ),
        'punish': False,
        'match_algo': TD({
            'name': 'MatchQueueMobaFirstNew', 
        }),
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1006,
        'prefix_match_name': '匹配(首场)#debug',
        'desc': '玩起来#debug',
        'show_panel_map': True,
        'max_count': 1,
        'max_team_count': 1,
        'hall_waiting_time': 1,
        'vehicle_medias': ('vehicle_common_media', 'vehicle_pickup_media', 'vehicle_helicopter_media', ),
        'match_icon_big': 10685,
        'match_icon_small': 10579,
        'show_rule_info': True,
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    1004: TD({
        'id': 1004,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 1,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': TD({
            'name': 'MatchQueueBattleRoyale', 
        }),
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'prefix_match_name': '战术竞技',
        'match_name': '战术竞技-排位',
        'desc': '玩起来#debug',
        'show_panel_map': True,
        'chat_share_icon': 40000,
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
        'vehicle_medias': ('vehicle_common_media', 'vehicle_pickup_media', 'vehicle_helicopter_media', ),
        'match_icon_big': 11475,
        'match_icon_small': 11478,
        'show_rule_info': True,
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    1005: TD({
        'id': 1005,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 1,
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': TD({
            'name': 'MatchQueueMobaWarm', 
        }),
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1004,
        'prefix_match_name': '战术竞技',
        'desc': '玩起来#debug',
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
        'vehicle_medias': ('vehicle_common_media', 'vehicle_pickup_media', 'vehicle_helicopter_media', ),
    }), 
    1006: TD({
        'id': 1006,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': TD({
            'name': 'MatchQueueMobaWarm', 
        }),
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1006,
        'prefix_match_name': '战术竞技test',
        'match_name': '战术竞技-匹配',
        'desc': '玩起来#debug',
        'show_panel_map': True,
        'chat_share_icon': 40000,
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
        'vehicle_medias': ('vehicle_common_media', 'vehicle_pickup_media', 'vehicle_helicopter_media', ),
        'match_icon_big': 11475,
        'match_icon_small': 11478,
        'show_rule_info': True,
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    1007: TD({
        'id': 1007,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': TD({
            'name': 'MatchQueueMobaWarm', 
        }),
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1006,
        'prefix_match_name': '战术竞技',
        'match_name': '战术竞技-匹配',
        'desc': '玩起来#debug',
        'show_panel_map': True,
        'chat_share_icon': 40000,
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
        'vehicle_medias': ('vehicle_common_media', 'vehicle_pickup_media', 'vehicle_helicopter_media', ),
        'match_icon_big': 11475,
        'match_icon_small': 11478,
        'show_rule_info': True,
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    1008: TD({
        'id': 1008,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': TD({
            'name': 'MatchQueueMobaWarm', 
        }),
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1006,
        'prefix_match_name': '战术竞技',
        'match_name': '战术竞技-匹配',
        'desc': '玩起来#debug',
        'show_panel_map': True,
        'chat_share_icon': 40000,
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
        'vehicle_medias': ('vehicle_common_media', 'vehicle_pickup_media', 'vehicle_helicopter_media', ),
        'match_icon_big': 11475,
        'match_icon_small': 11478,
        'show_rule_info': True,
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    1100: TD({
        'id': 1100,
        'space': (32, 41, 53, 54, 77, 78, 79, 80, 81, 82, ),
        'game_logic': 'HotSpot',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 2, 6, ),
        'match_algo': TD({
            'name': 'MatchQueueHotSpot', 
        }),
        'chat_channel_list': (1, 2, ),
        'mode_type': 1,
        'match_type': 1100,
        'prefix_match_name': '热点战',
        'match_name': '热点战',
        'leisure_sort_key': 10,
        'show_panel_map': True,
        'max_count': 12,
        'max_team_count': 2,
        'hall_waiting_time': 10,
        'match_icon_big': 10705,
        'match_icon_small': 10707,
        'rule_info_one_str': '守住热点以获取积分。首个达到积分目标的队伍获胜。',
    }), 
    1101: TD({
        'id': 1101,
        'space': (32, 41, 53, 54, 77, 78, 79, 80, ),
        'game_logic': 'HotSpot',
        'type': 2,
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 6, ),
        'match_algo': TD({
            'name': 'MatchQueueHotSpotNewer', 
        }),
        'chat_channel_list': (1, 2, ),
        'mode_type': 1,
        'match_type': 1100,
        'prefix_match_name': '热点战',
        'match_name': '热点战',
        'leisure_sort_key': 10,
        'show_panel_map': True,
        'max_count': 10,
        'max_team_count': 2,
        'hall_waiting_time': 10,
        'match_icon_big': 10705,
        'match_icon_small': 10707,
        'rule_info_one_str': '守住热点以获取积分。首个达到积分目标的队伍获胜。',
    }), 
    1600: TD({
        'id': 1600,
        'skip': 'trunk_only',
        'space': (62, ),
        'game_logic': 'RookieGuideNew',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, ),
        'match_type': 1600,
        'prefix_match_name': '新手引导',
        'match_name': '新手引导',
        'max_count': 1,
        'max_team_count': 1,
        'rule_info_icon': TD({
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        }),
        'rule_info_str': TD({
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        }),
    }), 
    2000: TD({
        'id': 2000,
        'space': (32, 41, 53, 54, ),
        'game_logic': 'CampFight',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 2, 6, ),
        'match_algo': TD({
            'name': 'MatchQueueCampFight', 
        }),
        'chat_channel_list': (1, 2, ),
        'mode_type': 1,
        'match_type': 2000,
        'prefix_match_name': '团队竞技',
        'match_name': '团队竞技',
        'leisure_sort_key': 9,
        'show_panel_map': True,
        'max_count': 12,
        'max_team_count': 2,
        'hall_waiting_time': 10,
        'match_icon_big': 10705,
        'match_icon_small': 10707,
        'rule_info_one_str': '击败敌人获取积分。首个达到积分目标的队伍获胜。',
    }), 
}
