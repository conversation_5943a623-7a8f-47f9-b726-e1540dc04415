# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 4-材质类型表.xlsx, sheetname:Sheet1
from taggeddict import taggeddict as TD

data = {
    0: TD({
        'id': 0,
        'type': '默认',
        'physics_material_name': 'Deafult',
        'hit_perform_id': 0,
        'melee_hit_perform_id': 2000,
        'use_decal': 1,
        'hit_sound_material_surface': 'cement',
        'gun_hit_sound_material_surface': 'carpet',
        'melee_hit_sound_material_surface': 'carpet',
    }), 
    1: TD({
        'id': 1,
        'type': '砖墙',
        'physics_material_name': 'Brick01',
        'hit_perform_id': 1,
        'melee_hit_perform_id': 2000,
        'use_decal': 1,
        'hit_sound_material_surface': 'cement',
        'gun_hit_sound_material_surface': 'cement',
        'melee_hit_sound_material_surface': 'cement',
    }), 
    2: TD({
        'id': 2,
        'type': '泥土',
        'physics_material_name': 'Field01',
        'hit_perform_id': 2,
        'melee_hit_perform_id': 2000,
        'use_decal': 1,
        'hit_sound_material_surface': 'dirt',
        'gun_hit_sound_material_surface': 'dirt',
        'melee_hit_sound_material_surface': 'dirt',
    }), 
    3: TD({
        'id': 3,
        'type': '厚金属（实心）',
        'physics_material_name': 'Metal01',
        'hit_perform_id': 3,
        'melee_hit_perform_id': 2002,
        'use_decal': 1,
        'hit_sound_material_surface': 'metal',
        'gun_hit_sound_material_surface': 'metal',
        'melee_hit_sound_material_surface': 'metal',
    }), 
    4: TD({
        'id': 4,
        'type': '木材',
        'physics_material_name': 'Wood01',
        'hit_perform_id': 4,
        'melee_hit_perform_id': 2000,
        'use_decal': 1,
        'hit_sound_material_surface': 'wood',
        'gun_hit_sound_material_surface': 'wood',
        'melee_hit_sound_material_surface': 'wood',
    }), 
    5: TD({
        'id': 5,
        'type': '沙子',
        'physics_material_name': 'Sand',
        'hit_perform_id': 5,
        'hit_sound_material_surface': 'sand',
        'gun_hit_sound_material_surface': 'dirt',
        'melee_hit_sound_material_surface': 'dirt',
    }), 
    8: TD({
        'id': 8,
        'type': '护盾（内）',
        'physics_material_name': 'ShieldOutside',
        'hit_perform_id': 8,
        'hit_sound_material_surface': 'tile',
    }), 
    9: TD({
        'id': 9,
        'type': '护盾（外）',
        'physics_material_name': 'ShieldInside',
        'hit_perform_id': 9,
        'hit_sound_material_surface': 'tile',
        'gun_hit_sound_material_surface': 'eshield',
        'melee_hit_sound_material_surface': 'eshield',
    }), 
    10: TD({
        'id': 10,
        'type': '可穿透铁丝网',
        'physics_material_name': 'Penetrable01',
        'hit_perform_id': 10,
        'melee_hit_perform_id': 2002,
        'use_decal': 1,
        'hit_sound_material_surface': 'metal',
        'gun_hit_sound_material_surface': 'metal',
        'melee_hit_sound_material_surface': 'metal',
        'penetrate_decrease': 0.0,
    }), 
    11: TD({
        'id': 11,
        'type': '水',
        'physics_material_name': 'Water01',
        'hit_perform_id': 11,
        'melee_hit_perform_id': 2003,
        'hit_sound_material_surface': 'water',
        'gun_hit_sound_material_surface': 'water',
        'melee_hit_sound_material_surface': 'water',
        'penetrate_decrease': 0.0,
    }), 
    12: TD({
        'id': 12,
        'type': '草地',
        'physics_material_name': 'Grass01',
        'hit_perform_id': 12,
        'melee_hit_perform_id': 2000,
        'use_decal': 1,
        'hit_sound_material_surface': 'grass',
        'gun_hit_sound_material_surface': 'grass',
        'melee_hit_sound_material_surface': 'grass',
    }), 
    13: TD({
        'id': 13,
        'type': '冰',
        'physics_material_name': 'ice01',
        'hit_perform_id': 13,
        'melee_hit_perform_id': 2002,
        'hit_sound_material_surface': 'tile',
        'gun_hit_sound_material_surface': 'ice',
        'melee_hit_sound_material_surface': 'ice',
    }), 
    14: TD({
        'id': 14,
        'type': '玻璃 (不可破坏)',
        'physics_material_name': 'Glass01',
        'hit_perform_id': 14,
        'use_decal': 1,
        'hit_sound_material_surface': 'tile',
        'gun_hit_sound_material_surface': 'ice',
        'melee_hit_sound_material_surface': 'ice',
    }), 
    15: TD({
        'id': 15,
        'type': '塑料',
        'physics_material_name': 'Plastic01',
        'hit_perform_id': 15,
        'melee_hit_perform_id': 2000,
        'use_decal': 1,
        'hit_sound_material_surface': 'carpet',
        'gun_hit_sound_material_surface': 'carpet',
        'melee_hit_sound_material_surface': 'carpet',
    }), 
    16: TD({
        'id': 16,
        'type': '纸',
        'physics_material_name': 'd_paper01',
        'hit_perform_id': 16,
        'melee_hit_perform_id': 2000,
        'hit_sound_material_surface': 'carpet',
        'gun_hit_sound_material_surface': 'carpet',
        'melee_hit_sound_material_surface': 'carpet',
    }), 
    17: TD({
        'id': 17,
        'type': '可破坏石膏板',
        'physics_material_name': 'd_gypsum01',
        'hit_perform_id': 17,
        'melee_hit_perform_id': 2004,
        'hit_sound_material_surface': 'cement',
        'gun_hit_sound_material_surface': 'cement',
        'melee_hit_sound_material_surface': 'cement',
        'penetrate_decrease': 60.0,
    }), 
    18: TD({
        'id': 18,
        'type': '可破坏木头栅墙',
        'physics_material_name': 'd_wood01',
        'hit_perform_id': 18,
        'melee_hit_perform_id': 2005,
        'hit_sound_material_surface': 'wood',
        'gun_hit_sound_material_surface': 'wood',
        'melee_hit_sound_material_surface': 'wood',
        'penetrate_decrease': 50.0,
        'thick_valid_val': 0.5,
        'thick_ratio': 200.0,
    }), 
    19: TD({
        'id': 19,
        'type': '通用水泥/混凝土',
        'physics_material_name': 'd_cement01',
        'hit_perform_id': 19,
        'melee_hit_perform_id': 2004,
        'use_decal': 1,
        'hit_sound_material_surface': 'cement',
        'gun_hit_sound_material_surface': 'cement',
        'melee_hit_sound_material_surface': 'cement',
    }), 
    20: TD({
        'id': 20,
        'type': '布料',
        'physics_material_name': 'Fabric',
        'hit_perform_id': 20,
        'melee_hit_perform_id': 2006,
        'use_decal': 1,
        'hit_sound_material_surface': 'carpet',
        'gun_hit_sound_material_surface': 'carpet',
        'melee_hit_sound_material_surface': 'carpet',
    }), 
    21: TD({
        'id': 21,
        'type': '植物',
        'physics_material_name': 'Plant',
        'hit_perform_id': 21,
        'melee_hit_perform_id': 2007,
        'hit_sound_material_surface': 'foliage',
        'gun_hit_sound_material_surface': 'foliage',
        'melee_hit_sound_material_surface': 'foliage',
    }), 
    22: TD({
        'id': 22,
        'type': '石头',
        'physics_material_name': 'Stone',
        'hit_perform_id': 22,
        'melee_hit_perform_id': 2000,
        'use_decal': 1,
        'hit_sound_material_surface': 'dirt',
        'gun_hit_sound_material_surface': 'cement',
        'melee_hit_sound_material_surface': 'cement',
    }), 
    32: TD({
        'id': 32,
        'type': '大理石（包含大理石地砖）',
        'physics_material_name': 'Marble',
        'hit_perform_id': 32,
        'use_decal': 1,
        'hit_sound_material_surface': 'tile',
        'gun_hit_sound_material_surface': 'cement',
        'melee_hit_sound_material_surface': 'cement',
    }), 
    33: TD({
        'id': 33,
        'type': '自发光',
        'physics_material_name': 'Emissive',
        'hit_perform_id': 33,
        'hit_sound_material_surface': 'cement',
        'gun_hit_sound_material_surface': 'carpet',
        'melee_hit_sound_material_surface': 'carpet',
    }), 
    34: TD({
        'id': 34,
        'type': '陶瓷',
        'physics_material_name': 'Ceramic',
        'hit_perform_id': 34,
        'hit_sound_material_surface': 'ceramic',
        'gun_hit_sound_material_surface': 'cement',
        'melee_hit_sound_material_surface': 'cement',
    }), 
    37: TD({
        'id': 37,
        'type': '小石子',
        'physics_material_name': 'Pebble',
        'hit_perform_id': 37,
        'use_decal': 1,
        'hit_sound_material_surface': 'gravel',
        'gun_hit_sound_material_surface': 'dirt',
        'melee_hit_sound_material_surface': 'dirt',
    }), 
    38: TD({
        'id': 38,
        'type': '泥泞',
        'physics_material_name': 'Mud',
        'hit_perform_id': 38,
        'hit_sound_material_surface': 'mud',
        'gun_hit_sound_material_surface': 'mud',
        'melee_hit_sound_material_surface': 'mud',
    }), 
    39: TD({
        'id': 39,
        'type': '薄金属（空心）',
        'physics_material_name': 'EmptyMetal',
        'hit_perform_id': 39,
        'melee_hit_perform_id': 2002,
        'use_decal': 1,
        'hit_sound_material_surface': 'metal_thin',
        'gun_hit_sound_material_surface': 'metal',
        'melee_hit_sound_material_surface': 'metal',
    }), 
    40: TD({
        'id': 40,
        'type': '玻璃 (可破坏)',
        'physics_material_name': 'glassBreak',
        'hit_perform_id': 40,
        'hit_sound_material_surface': 'tile',
        'gun_hit_sound_material_surface': 'ice',
        'melee_hit_sound_material_surface': 'ice',
        'penetrate_decrease': 20.0,
    }), 
    1001: TD({
        'id': 1001,
        'type': '人体',
        'hit_perform_id': 1001,
        'melee_hit_perform_id': 2001,
        'penetrate_decrease': 99.0,
    }), 
}
