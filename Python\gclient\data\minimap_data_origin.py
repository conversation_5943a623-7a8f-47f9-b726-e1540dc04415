# -*- coding: utf-8 -*-
# flake8: noqa
# generated by: excel_to_data.py
# generated from 16-小地图信息表.xlsx, sheetname:小地图, post_process.py, GenerateMiniMapData
from taggeddict import taggeddict as TD
data = {
    10: TD({
        'id': 10, 
        'icon': 999, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 1.0, 
        'map_text_list': (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, ), 
        'matrix_a': 0.979893, 
        'matrix_b': -4e-06, 
        'matrix_c': -0.000824, 
        'matrix_d': -0.979555, 
        'matrix_e': 1050.967505, 
        'matrix_f': 1012.001533, 
        'matrix_g': 1.02052, 
        'matrix_h': -5e-06, 
        'matrix_i': -0.000859, 
        'matrix_j': -1.020872, 
        'matrix_k': -1071.663862, 
        'matrix_l': 1033.128497, 
        'max_scale': 2.5, 
        'pixel1': (1121, 1677, ), 
        'pixel2': (291, 514, ), 
        'pixel3': (1531, 509, ), 
        'ref_pos1': (72.02, 654.38, ), 
        'ref_pos2': (-776.01, -532.89, ), 
        'ref_pos3': (489.43, -538.0, ), 
    }), 
    12: TD({
        'id': 12, 
        'icon': 10188, 
        'icon_height': 782, 
        'icon_width': 782, 
        'init_scale': 1.0, 
        'matrix_a': 0.385298, 
        'matrix_b': 0.001813, 
        'matrix_c': -0.000505, 
        'matrix_d': -0.394084, 
        'matrix_e': 390.011395, 
        'matrix_f': 396.993985, 
        'matrix_g': 2.59541, 
        'matrix_h': 0.011939, 
        'matrix_i': -0.003323, 
        'matrix_j': -2.537548, 
        'matrix_k': -1010.920453, 
        'matrix_l': 1002.734729, 
        'max_scale': 2.5, 
        'pixel1': (101, 717, ), 
        'pixel2': (632, 668, ), 
        'pixel3': (138, 106, ), 
        'ref_pos1': (-749, 839, ), 
        'ref_pos2': (629, 721, ), 
        'ref_pos3': (-655, -711, ), 
    }), 
    16: TD({
        'id': 16, 
        'icon': 11061, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 0.2, 
        'matrix_a': 18.318815, 
        'matrix_b': -0.081834, 
        'matrix_c': -0.157113, 
        'matrix_d': -18.462094, 
        'matrix_e': 1025.267247, 
        'matrix_f': 1020.460416, 
        'matrix_g': 0.054587, 
        'matrix_h': -0.000242, 
        'matrix_i': -0.000465, 
        'matrix_j': -0.054163, 
        'matrix_k': -55.491825, 
        'matrix_l': 55.519246, 
        'max_scale': 1.2, 
        'pixel1': (204, 662, ), 
        'pixel2': (176, 1369, ), 
        'pixel3': (1926, 666, ), 
        'ref_pos1': (-45, -19.6, ), 
        'ref_pos2': (-46.2, 18.7, ), 
        'ref_pos3': (49, -19.8, ), 
    }), 
    17: TD({
        'id': 17, 
        'icon': 10620, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 0.2, 
        'matrix_a': 18.318815, 
        'matrix_b': -0.081834, 
        'matrix_c': -0.157113, 
        'matrix_d': -18.462094, 
        'matrix_e': 1025.267247, 
        'matrix_f': 1020.460416, 
        'matrix_g': 0.054587, 
        'matrix_h': -0.000242, 
        'matrix_i': -0.000465, 
        'matrix_j': -0.054163, 
        'matrix_k': -55.491825, 
        'matrix_l': 55.519246, 
        'max_scale': 1.2, 
        'pixel1': (204, 662, ), 
        'pixel2': (176, 1369, ), 
        'pixel3': (1926, 666, ), 
        'ref_pos1': (-45, -19.6, ), 
        'ref_pos2': (-46.2, 18.7, ), 
        'ref_pos3': (49, -19.8, ), 
    }), 
    18: TD({
        'id': 18, 
        'icon': 10621, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 0.2, 
        'matrix_a': 10.362029, 
        'matrix_b': -0.027203, 
        'matrix_c': -0.038989, 
        'matrix_d': -10.275203, 
        'matrix_e': 1098.515972, 
        'matrix_f': 864.808758, 
        'matrix_g': 0.096505, 
        'matrix_h': -0.000255, 
        'matrix_i': -0.000366, 
        'matrix_j': -0.097321, 
        'matrix_k': -105.695859, 
        'matrix_l': 84.444469, 
        'max_scale': 1.2, 
        'pixel1': (456, 1200, ), 
        'pixel2': (882, 77, ), 
        'pixel3': (1310, 1977, ), 
        'ref_pos1': (-62, 1.8, ), 
        'ref_pos2': (-21.3, -107.6, ), 
        'ref_pos3': (20.7, 77.2, ), 
    }), 
    19: TD({
        'id': 19, 
        'icon': 10653, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 1.0, 
        'matrix_a': 0.639453, 
        'matrix_b': 0.000615, 
        'matrix_c': 0.00049, 
        'matrix_d': -0.638197, 
        'matrix_e': 963.018692, 
        'matrix_f': 1087.192324, 
        'matrix_g': 1.563835, 
        'matrix_h': 0.001507, 
        'matrix_i': 0.0012, 
        'matrix_j': -1.566913, 
        'matrix_k': -1507.306462, 
        'matrix_l': 1702.084504, 
        'max_scale': 2.5, 
        'pixel1': (270, 322, ), 
        'pixel2': (309, 1980, ), 
        'pixel3': (1510, 451, ), 
        'ref_pos1': (-1083, -1002, ), 
        'ref_pos2': (-1024, 1596, ), 
        'ref_pos3': (856, -798, ), 
    }), 
    2: TD({
        'id': 2, 
        'icon': 40276, 
        'icon_height': 901, 
        'icon_width': 901, 
        'init_scale': 3.0, 
        'matrix_a': 1.297473, 
        'matrix_b': -0.014761, 
        'matrix_c': 0.007979, 
        'matrix_d': -1.321809, 
        'matrix_e': 258.183511, 
        'matrix_f': 372.598404, 
        'matrix_g': 0.770782, 
        'matrix_h': -0.008607, 
        'matrix_i': 0.004653, 
        'matrix_j': -0.756591, 
        'matrix_k': -200.736663, 
        'matrix_l': 284.126939, 
        'max_scale': 6.0, 
        'pixel1': (336, 524, ), 
        'pixel2': (375, 537, ), 
        'pixel3': (361, 367, ), 
        'ref_pos1': (60, -4, ), 
        'ref_pos2': (90, 5.5, ), 
        'ref_pos3': (80, -123, ), 
    }), 
    20: TD({
        'id': 20, 
        'icon': 11061, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 0.2, 
        'matrix_a': 10.362029, 
        'matrix_b': -0.027203, 
        'matrix_c': -0.038989, 
        'matrix_d': -10.275203, 
        'matrix_e': 1098.515972, 
        'matrix_f': 864.808758, 
        'matrix_g': 0.096505, 
        'matrix_h': -0.000255, 
        'matrix_i': -0.000366, 
        'matrix_j': -0.097321, 
        'matrix_k': -105.695859, 
        'matrix_l': 84.444469, 
        'max_scale': 1.2, 
        'pixel1': (456, 1200, ), 
        'pixel2': (882, 77, ), 
        'pixel3': (1310, 1977, ), 
        'ref_pos1': (-62, 1.8, ), 
        'ref_pos2': (-21.3, -107.6, ), 
        'ref_pos3': (20.7, 77.2, ), 
    }), 
    21: TD({
        'id': 21, 
        'icon': 11061, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 0.2, 
        'matrix_a': 24.69657, 
        'matrix_b': -0.0, 
        'matrix_c': 0.0, 
        'matrix_d': -24.604811, 
        'matrix_e': 1016.356201, 
        'matrix_f': 1018.302405, 
        'matrix_g': 0.040491, 
        'matrix_h': -0.0, 
        'matrix_i': -0.0, 
        'matrix_j': -0.040642, 
        'matrix_k': -41.153739, 
        'matrix_l': 41.386313, 
        'max_scale': 1.0, 
        'pixel1': (557, 1758, ), 
        'pixel2': (1493, 1758, ), 
        'pixel3': (1493, 326, ), 
        'ref_pos1': (-18.6, 29.6, ), 
        'ref_pos2': (19.3, 29.6, ), 
        'ref_pos3': (19.3, -28.6, ), 
    }), 
    22: TD({
        'id': 22, 
        'icon': 11060, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 0.2, 
        'matrix_a': 29.366667, 
        'matrix_b': 0.0, 
        'matrix_c': -0.0, 
        'matrix_d': -29.666667, 
        'matrix_e': -4178.4, 
        'matrix_f': 6543.0, 
        'matrix_g': 0.034052, 
        'matrix_h': 0.0, 
        'matrix_i': 0.0, 
        'matrix_j': -0.033708, 
        'matrix_k': 142.283768, 
        'matrix_l': 220.550562, 
        'max_scale': 1.0, 
        'pixel1': (579, 1913, ), 
        'pixel2': (1460, 1913, ), 
        'pixel3': (1460, 133, ), 
        'ref_pos1': (162, 216, ), 
        'ref_pos2': (192, 216, ), 
        'ref_pos3': (192, 156, ), 
    }), 
    23: TD({
        'id': 23, 
        'icon': 11305, 
        'icon_height': 512, 
        'icon_width': 512, 
        'init_scale': 0.6, 
        'matrix_a': 6.0282, 
        'matrix_b': -0.034707, 
        'matrix_c': -0.132321, 
        'matrix_d': -6.183297, 
        'matrix_e': -885.930586, 
        'matrix_f': 1363.145336, 
        'matrix_g': 0.165867, 
        'matrix_h': -0.000931, 
        'matrix_i': -0.00355, 
        'matrix_j': -0.161706, 
        'matrix_k': 151.78476, 
        'matrix_l': 219.604085, 
        'max_scale': 2.0, 
        'pixel1': (196, 429, ), 
        'pixel2': (342, 368, ), 
        'pixel3': (341, 133, ), 
        'ref_pos1': (184, 206, ), 
        'ref_pos2': (208, 196, ), 
        'ref_pos3': (207, 158, ), 
    }), 
    24: TD({
        'id': 24, 
        'icon': 11061, 
        'icon_height': 512, 
        'icon_width': 512, 
        'init_scale': 1.0, 
        'matrix_a': 4.866667, 
        'matrix_b': 2.033333, 
        'matrix_c': 0.016667, 
        'matrix_d': -3.916667, 
        'matrix_e': -596.0, 
        'matrix_f': 599.6, 
        'matrix_g': 0.205115, 
        'matrix_h': 0.106485, 
        'matrix_i': 0.000873, 
        'matrix_j': -0.254866, 
        'matrix_k': 121.725059, 
        'matrix_l': 216.282797, 
        'max_scale': 1.0, 
        'pixel1': (196, 429, ), 
        'pixel2': (342, 368, ), 
        'pixel3': (341, 133, ), 
        'ref_pos1': (162, 216, ), 
        'ref_pos2': (192, 216, ), 
        'ref_pos3': (192, 156, ), 
    }), 
    25: TD({
        'id': 25, 
        'change_icon_scale': 3, 
        'icon': 998, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'map_text_list': (101, 102, 103, 104, 105, 106, ), 
        'matrix_a': 0.809932, 
        'matrix_b': -0.005455, 
        'matrix_c': -0.002723, 
        'matrix_d': -0.818196, 
        'matrix_e': 512.0, 
        'matrix_f': 512.0, 
        'matrix_g': 1.234644, 
        'matrix_h': -0.008231, 
        'matrix_i': -0.004108, 
        'matrix_j': -1.222174, 
        'matrix_k': -630.034198, 
        'matrix_l': 629.967538, 
        'pixel1': (334, 942, ), 
        'pixel2': (512, 512, ), 
        'pixel3': (188, 518, ), 
        'ref_pos1': (-218, 527, ), 
        'ref_pos2': (0, 0, ), 
        'ref_pos3': (-400, 10, ), 
    }), 
    32: TD({
        'id': 32, 
        'icon': 40217, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 0.2, 
        'matrix_a': 22.294118, 
        'matrix_b': 0.118235, 
        'matrix_c': -0.0, 
        'matrix_d': -22.333333, 
        'matrix_e': 77.541176, 
        'matrix_f': 105.256353, 
        'matrix_g': 0.044855, 
        'matrix_h': 0.000237, 
        'matrix_i': -0.0, 
        'matrix_j': -0.044776, 
        'matrix_k': -3.4781, 
        'matrix_l': 4.694558, 
        'max_scale': 1.2, 
        'pixel1': (82, 104, ), 
        'pixel2': (840, 104, ), 
        'pixel3': (840, 908, ), 
        'ref_pos1': (0.2, -36.48, ), 
        'ref_pos2': (34.2, -36.3, ), 
        'ref_pos3': (34.2, -0.3, ), 
    }), 
    33: TD({
        'id': 33, 
        'icon': 10620, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 0.2, 
        'matrix_a': 18.318815, 
        'matrix_b': -0.081834, 
        'matrix_c': -0.157113, 
        'matrix_d': -18.462094, 
        'matrix_e': 1025.267247, 
        'matrix_f': 1020.460416, 
        'matrix_g': 0.054587, 
        'matrix_h': -0.000242, 
        'matrix_i': -0.000465, 
        'matrix_j': -0.054163, 
        'matrix_k': -55.491825, 
        'matrix_l': 55.519246, 
        'max_scale': 1.2, 
        'pixel1': (204, 662, ), 
        'pixel2': (176, 1369, ), 
        'pixel3': (1926, 666, ), 
        'ref_pos1': (-45, -19.6, ), 
        'ref_pos2': (-46.2, 18.7, ), 
        'ref_pos3': (49, -19.8, ), 
    }), 
    37: TD({
        'id': 37, 
        'icon': 10620, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 0.2, 
        'matrix_a': 18.318815, 
        'matrix_b': -0.081834, 
        'matrix_c': -0.157113, 
        'matrix_d': -18.462094, 
        'matrix_e': 1025.267247, 
        'matrix_f': 1020.460416, 
        'matrix_g': 0.054587, 
        'matrix_h': -0.000242, 
        'matrix_i': -0.000465, 
        'matrix_j': -0.054163, 
        'matrix_k': -55.491825, 
        'matrix_l': 55.519246, 
        'max_scale': 1.2, 
        'pixel1': (204, 662, ), 
        'pixel2': (176, 1369, ), 
        'pixel3': (1926, 666, ), 
        'ref_pos1': (-45, -19.6, ), 
        'ref_pos2': (-46.2, 18.7, ), 
        'ref_pos3': (49, -19.8, ), 
    }), 
    41: TD({
        'id': 41, 
        'icon': 40230, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 1.0, 
        'matrix_a': 5.664195, 
        'matrix_b': -0.071848, 
        'matrix_c': -0.001949, 
        'matrix_d': -5.850911, 
        'matrix_e': 802.546196, 
        'matrix_f': 328.063037, 
        'matrix_g': 0.176547, 
        'matrix_h': -0.002168, 
        'matrix_i': -5.9e-05, 
        'matrix_j': -0.170913, 
        'matrix_k': -141.667718, 
        'matrix_l': 57.810064, 
        'max_scale': 2.0, 
        'pixel1': (823, 678, ), 
        'pixel2': (314, 718, ), 
        'pixel3': (307, 307, ), 
        'ref_pos1': (3.61, -3.11, ), 
        'ref_pos2': (-86.25, 4.83, ), 
        'ref_pos3': (-87.51, -65.4, ), 
    }), 
    46: TD({
        'id': 46, 
        'icon': 40217, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 0.2, 
        'matrix_a': 22.294118, 
        'matrix_b': 0.118235, 
        'matrix_c': -0.0, 
        'matrix_d': -22.333333, 
        'matrix_e': 77.541176, 
        'matrix_f': 105.256353, 
        'matrix_g': 0.044855, 
        'matrix_h': 0.000237, 
        'matrix_i': -0.0, 
        'matrix_j': -0.044776, 
        'matrix_k': -3.4781, 
        'matrix_l': 4.694558, 
        'max_scale': 1.2, 
        'pixel1': (82, 104, ), 
        'pixel2': (840, 104, ), 
        'pixel3': (840, 908, ), 
        'ref_pos1': (0.2, -36.48, ), 
        'ref_pos2': (34.2, -36.3, ), 
        'ref_pos3': (34.2, -0.3, ), 
    }), 
    51: TD({
        'id': 51, 
        'change_icon_scale': 3, 
        'icon': 40233, 
        'icon_height': 8192, 
        'icon_width': 8192, 
        'init_scale': 1.0, 
        'map_grid_size': (8, 8, ), 
        'matrix_a': 2.7318, 
        'matrix_b': -0.000564, 
        'matrix_c': -0.000341, 
        'matrix_d': -2.730775, 
        'matrix_e': -1411.005114, 
        'matrix_f': 9603.507095, 
        'matrix_g': 0.366059, 
        'matrix_h': -7.6e-05, 
        'matrix_i': -4.6e-05, 
        'matrix_j': -0.366196, 
        'matrix_k': 516.94991, 
        'matrix_l': 3516.662771, 
        'max_scale': 1.5, 
        'pixel1': (1888, 6675, ), 
        'pixel2': (6726, 6818, ), 
        'pixel3': (6590, 2091, ), 
        'ref_pos1': (1208, 2961, ), 
        'ref_pos2': (2979, 3013, ), 
        'ref_pos3': (2929, 1282, ), 
    }), 
    53: TD({
        'id': 53, 
        'icon': 40234, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 0.2, 
        'matrix_a': 1.000699, 
        'matrix_b': 0.745569, 
        'matrix_c': -0.01281, 
        'matrix_d': 25.519982, 
        'matrix_e': -588.252684, 
        'matrix_f': -3639.023166, 
        'matrix_g': 0.998928, 
        'matrix_h': -0.029184, 
        'matrix_i': 0.000501, 
        'matrix_j': 0.03917, 
        'matrix_k': 589.446713, 
        'matrix_l': 125.374307, 
        'max_scale': 1.2, 
        'pixel1': (2488, 584, ), 
        'pixel2': (651, 523, ), 
        'pixel3': (1590, 2504, ), 
        'ref_pos1': (3075, 70, ), 
        'ref_pos2': (1240, 126, ), 
        'ref_pos3': (2177, 21, ), 
    }), 
    55: TD({
        'id': 55, 
        'icon': 40234, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 0.2, 
        'matrix_a': 5.22479, 
        'matrix_b': 0.014701, 
        'matrix_c': -0.025228, 
        'matrix_d': -5.233574, 
        'matrix_e': 86.0, 
        'matrix_f': 539.0, 
        'matrix_g': 0.191398, 
        'matrix_h': 0.000538, 
        'matrix_i': -0.000923, 
        'matrix_j': -0.191077, 
        'matrix_k': -15.96293, 
        'matrix_l': 102.944049, 
        'max_scale': 1.2, 
        'pixel1': (86, 485, ), 
        'pixel2': (272, 485, ), 
        'pixel3': (339, 80, ), 
        'ref_pos1': (0, 0, ), 
        'ref_pos2': (35.6, 0.1, ), 
        'ref_pos3': (48.05, -77.25, ), 
    }), 
    6: TD({
        'id': 6, 
        'icon': 10002, 
        'icon_height': 797, 
        'icon_width': 1742, 
        'matrix_a': -0.183392, 
        'matrix_b': -5.241, 
        'matrix_c': -5.228273, 
        'matrix_d': 0.024909, 
        'matrix_e': 1238.38604, 
        'matrix_f': 406.827668, 
        'matrix_g': -0.000909, 
        'matrix_h': -0.191236, 
        'matrix_i': -0.190771, 
        'matrix_j': 0.006692, 
        'matrix_k': 78.736685, 
        'matrix_l': 234.101479, 
        'pixel1': (150, 641, ), 
        'pixel2': (1316, 200, ), 
        'pixel3': (392, 278, ), 
        'ref_pos1': (48.84, 206.46, ), 
        'ref_pos2': (-36.35, -13.57, ), 
        'ref_pos3': (-20.63, 162.61, ), 
    }), 
    60: TD({
        'id': 60, 
        'change_icon_scale': 3, 
        'icon': 40272, 
        'icon_height': 2500, 
        'icon_width': 2500, 
        'init_scale': 1.0, 
        'map_grid_size': (2, 2, ), 
        'matrix_a': 2.422949, 
        'matrix_b': -0.011509, 
        'matrix_c': 0.004175, 
        'matrix_d': -2.427923, 
        'matrix_e': 1311.189397, 
        'matrix_f': 1138.714748, 
        'matrix_g': 0.412724, 
        'matrix_h': -0.001956, 
        'matrix_i': 0.00071, 
        'matrix_j': -0.411878, 
        'matrix_k': -541.966964, 
        'matrix_l': 471.576777, 
        'max_scale': 1.5, 
        'pixel1': (507, 1491, ), 
        'pixel2': (888, 1840, ), 
        'pixel3': (2141, 632, ), 
        'ref_pos1': (-332, 55, ), 
        'ref_pos2': (-175, 198, ), 
        'ref_pos3': (343, -302, ), 
    }), 
    61: TD({
        'id': 61, 
        'change_icon_scale': 3, 
        'icon': 40233, 
        'icon_height': 8192, 
        'icon_width': 8192, 
        'init_scale': 1.0, 
        'map_grid_size': (8, 8, ), 
        'matrix_a': 2.7318, 
        'matrix_b': -0.000564, 
        'matrix_c': -0.000341, 
        'matrix_d': -2.730775, 
        'matrix_e': -1411.005114, 
        'matrix_f': 9603.507095, 
        'matrix_g': 0.366059, 
        'matrix_h': -7.6e-05, 
        'matrix_i': -4.6e-05, 
        'matrix_j': -0.366196, 
        'matrix_k': 516.94991, 
        'matrix_l': 3516.662771, 
        'max_scale': 1.5, 
        'pixel1': (1888, 6675, ), 
        'pixel2': (6726, 6818, ), 
        'pixel3': (6590, 2091, ), 
        'ref_pos1': (1208, 2961, ), 
        'ref_pos2': (2979, 3013, ), 
        'ref_pos3': (2929, 1282, ), 
    }), 
    65: TD({
        'id': 65, 
        'change_icon_scale': 3, 
        'icon': 40272, 
        'icon_height': 2500, 
        'icon_width': 2500, 
        'init_scale': 1.0, 
        'map_grid_size': (2, 2, ), 
        'matrix_a': 2.422949, 
        'matrix_b': -0.011509, 
        'matrix_c': 0.004175, 
        'matrix_d': -2.427923, 
        'matrix_e': 1311.189397, 
        'matrix_f': 1138.714748, 
        'matrix_g': 0.412724, 
        'matrix_h': -0.001956, 
        'matrix_i': 0.00071, 
        'matrix_j': -0.411878, 
        'matrix_k': -541.966964, 
        'matrix_l': 471.576777, 
        'max_scale': 1.5, 
        'pixel1': (507, 1491, ), 
        'pixel2': (888, 1840, ), 
        'pixel3': (2141, 632, ), 
        'ref_pos1': (-332, 55, ), 
        'ref_pos2': (-175, 198, ), 
        'ref_pos3': (343, -302, ), 
    }), 
    7: TD({
        'id': 7, 
        'icon': 10001, 
        'icon_height': 2048, 
        'icon_width': 2048, 
        'init_scale': 1.0, 
        'matrix_a': 1.024051, 
        'matrix_b': -0.0, 
        'matrix_c': 5.1e-05, 
        'matrix_d': -1.024154, 
        'matrix_e': 1023.999997, 
        'matrix_f': 1024.051208, 
        'matrix_g': 0.976514, 
        'matrix_h': 0.0, 
        'matrix_i': 4.9e-05, 
        'matrix_j': -0.976416, 
        'matrix_k': -1000.0, 
        'matrix_l': 999.9, 
        'max_scale': 4.0, 
        'pixel1': (0, 0, ), 
        'pixel2': (0, 2048, ), 
        'pixel3': (2048, 2048, ), 
        'ref_pos1': (-999.9, -999.8, ), 
        'ref_pos2': (-1000, 999.9, ), 
        'ref_pos3': (999.9, 999.9, ), 
    }), 
    77: TD({
        'id': 77, 
        'icon': 40230, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 1.0, 
        'matrix_a': 5.664195, 
        'matrix_b': -0.071848, 
        'matrix_c': -0.001949, 
        'matrix_d': -5.850911, 
        'matrix_e': 802.546196, 
        'matrix_f': 328.063037, 
        'matrix_g': 0.176547, 
        'matrix_h': -0.002168, 
        'matrix_i': -5.9e-05, 
        'matrix_j': -0.170913, 
        'matrix_k': -141.667718, 
        'matrix_l': 57.810064, 
        'max_scale': 2.0, 
        'pixel1': (823, 678, ), 
        'pixel2': (314, 718, ), 
        'pixel3': (307, 307, ), 
        'ref_pos1': (3.61, -3.11, ), 
        'ref_pos2': (-86.25, 4.83, ), 
        'ref_pos3': (-87.51, -65.4, ), 
    }), 
    78: TD({
        'id': 78, 
        'icon': 40230, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 1.0, 
        'matrix_a': 5.664195, 
        'matrix_b': -0.071848, 
        'matrix_c': -0.001949, 
        'matrix_d': -5.850911, 
        'matrix_e': 802.546196, 
        'matrix_f': 328.063037, 
        'matrix_g': 0.176547, 
        'matrix_h': -0.002168, 
        'matrix_i': -5.9e-05, 
        'matrix_j': -0.170913, 
        'matrix_k': -141.667718, 
        'matrix_l': 57.810064, 
        'max_scale': 2.0, 
        'pixel1': (823, 678, ), 
        'pixel2': (314, 718, ), 
        'pixel3': (307, 307, ), 
        'ref_pos1': (3.61, -3.11, ), 
        'ref_pos2': (-86.25, 4.83, ), 
        'ref_pos3': (-87.51, -65.4, ), 
    }), 
    79: TD({
        'id': 79, 
        'icon': 40230, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 1.0, 
        'matrix_a': 5.664195, 
        'matrix_b': -0.071848, 
        'matrix_c': -0.001949, 
        'matrix_d': -5.850911, 
        'matrix_e': 802.546196, 
        'matrix_f': 328.063037, 
        'matrix_g': 0.176547, 
        'matrix_h': -0.002168, 
        'matrix_i': -5.9e-05, 
        'matrix_j': -0.170913, 
        'matrix_k': -141.667718, 
        'matrix_l': 57.810064, 
        'max_scale': 2.0, 
        'pixel1': (823, 678, ), 
        'pixel2': (314, 718, ), 
        'pixel3': (307, 307, ), 
        'ref_pos1': (3.61, -3.11, ), 
        'ref_pos2': (-86.25, 4.83, ), 
        'ref_pos3': (-87.51, -65.4, ), 
    }), 
    80: TD({
        'id': 80, 
        'icon': 40230, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 1.0, 
        'matrix_a': 5.664195, 
        'matrix_b': -0.071848, 
        'matrix_c': -0.001949, 
        'matrix_d': -5.850911, 
        'matrix_e': 802.546196, 
        'matrix_f': 328.063037, 
        'matrix_g': 0.176547, 
        'matrix_h': -0.002168, 
        'matrix_i': -5.9e-05, 
        'matrix_j': -0.170913, 
        'matrix_k': -141.667718, 
        'matrix_l': 57.810064, 
        'max_scale': 2.0, 
        'pixel1': (823, 678, ), 
        'pixel2': (314, 718, ), 
        'pixel3': (307, 307, ), 
        'ref_pos1': (3.61, -3.11, ), 
        'ref_pos2': (-86.25, 4.83, ), 
        'ref_pos3': (-87.51, -65.4, ), 
    }), 
    81: TD({
        'id': 81, 
        'icon': 40230, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 1.0, 
        'matrix_a': 5.664195, 
        'matrix_b': -0.071848, 
        'matrix_c': -0.001949, 
        'matrix_d': -5.850911, 
        'matrix_e': 802.546196, 
        'matrix_f': 328.063037, 
        'matrix_g': 0.176547, 
        'matrix_h': -0.002168, 
        'matrix_i': -5.9e-05, 
        'matrix_j': -0.170913, 
        'matrix_k': -141.667718, 
        'matrix_l': 57.810064, 
        'max_scale': 2.0, 
        'pixel1': (823, 678, ), 
        'pixel2': (314, 718, ), 
        'pixel3': (307, 307, ), 
        'ref_pos1': (3.61, -3.11, ), 
        'ref_pos2': (-86.25, 4.83, ), 
        'ref_pos3': (-87.51, -65.4, ), 
    }), 
    82: TD({
        'id': 82, 
        'icon': 40230, 
        'icon_height': 1024, 
        'icon_width': 1024, 
        'init_scale': 1.0, 
        'matrix_a': 5.664195, 
        'matrix_b': -0.071848, 
        'matrix_c': -0.001949, 
        'matrix_d': -5.850911, 
        'matrix_e': 802.546196, 
        'matrix_f': 328.063037, 
        'matrix_g': 0.176547, 
        'matrix_h': -0.002168, 
        'matrix_i': -5.9e-05, 
        'matrix_j': -0.170913, 
        'matrix_k': -141.667718, 
        'matrix_l': 57.810064, 
        'max_scale': 2.0, 
        'pixel1': (823, 678, ), 
        'pixel2': (314, 718, ), 
        'pixel3': (307, 307, ), 
        'ref_pos1': (3.61, -3.11, ), 
        'ref_pos2': (-86.25, 4.83, ), 
        'ref_pos3': (-87.51, -65.4, ), 
    }), 
}
