# -*- coding: utf-8 -*-
# flake8: noqa
# generated by: excel_to_data.py
# generated from 5-场景地图表.xlsx, sheetname:地图数据表, post_process.py, GenerateDlcID
from taggeddict import taggeddict as TD
data = {
    1: TD({
        'id': 1, 
        'bgm_id': 271, 
        'entrance_list': ((-7, 2, -2, 90, ), ), 
        'is_baked': False, 
        'map': 'chareditor_entry', 
        'map_icon': 40227, 
        'name': 'chareditor_entry#debug', 
        'skip': 'trunk_only', 
    }), 
    2: TD({
        'id': 2, 
        'entrance_list': ((55.6, 5.06, -5.34, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'TestRoom_3C', 
        'name': 'warehouse#debug', 
        'navimesh': 'de1bd388-7eb1-46fb-b37e-4e90f96818b1', 
        'skip': 'trunk_only', 
        'ue_level_path': '/Game/Common/TemplateLevels/SubLevels/TestRoom_3C.TestRoom_3C', 
    }), 
    32: <PERSON>({
        'id': 32, 
        'entrance_list': ((4.82, -0.01, -30.05, 1.0, ), (3.16, 0.24, -31.06, 0.99, ), (7.56, -0.01, -34.55, 0.04, ), (5.1, -0.01, -33.58, 0.47, ), (7.46, -0.02, -31.74, 0.05, ), (5.54, -0.0, -35.15, 0.09, ), (29.28, -0.04, -5.13, -2.04, ), (30.68, -0.05, -6.28, -1.79, ), (28.55, -0.0, -3.43, -2.72, ), (30.58, 0.0, -3.36, -2.29, ), (31.61, -0.01, -4.64, -2.22, ), (27.3, 0.01, -2.26, -3.06, ), (19.58, 0.24, -1.49, 1.83, ), (22.93, 0.01, -1.79, 2.91, ), (28.92, 0.01, -1.33, 3.13, ), (21.68, 0.0, -3.85, 2.47, ), (26.66, -0.02, -6.73, -1.75, ), (30.34, 0.01, -1.25, -2.81, ), (30.75, 0.01, -22.1, -2.63, ), (29.81, 0.0, -30.5, -1.52, ), (30.72, 0.0, -33.08, -1.18, ), (30.57, 0.0, -28.72, -1.61, ), (28.88, 0.0, -27.39, -1.68, ), (28.77, 0.0, -31.83, -1.13, ), (32.49, 0.01, -30.83, -0.9, ), (31.25, 0.01, -25.11, -0.98, ), (32.19, 0.01, -26.77, -1.53, ), (20.43, 0.04, -18.39, 1.49, ), (18.54, -0.01, -18.47, -0.31, ), (17.69, -0.03, -14.4, -2.74, ), (15.09, -0.02, -18.84, -1.56, ), (17.7, -0.07, -21.58, 3.12, ), (17.62, -0.07, -23.7, -3.07, ), (20.76, 0.24, -10.87, -3.05, ), (21.26, 0.24, -14.04, -2.79, ), (5.04, 0.1, -14.47, 1.12, ), (4.74, 0.0, -3.38, 2.0, ), (9.45, 0.03, -2.6, 2.13, ), (7.11, 0.02, -3.15, 3.0, ), (3.09, -0.02, -7.11, 2.04, ), (3.73, -0.03, -4.92, 3.09, ), (7.54, -0.01, -6.01, 2.43, ), (16.58, 0.41, -32.86, -1.44, ), (13.65, -0.0, -35.09, -0.64, ), (3.82, -0.0, -34.39, 0.53, ), (13.02, -0.01, -32.52, -0.6, ), ), 
        'gen_navmap': True, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_lines': (1, 2, 3, 4, ), 
        'loading_pic1': 49004, 
        'loading_pic2': 49004, 
        'map': 'L_CargoShipment', 
        'map_icon': 48004, 
        'map_sound_param': 0.3, 
        'name': '装卸货场', 
        'navimesh': '3cff0ae7-e853-4b3e-80de-3cf80f7e8efa', 
        'ue_level_path': '/Game/Maps/CargoShipment/L_CargoShipment', 
    }), 
    33: TD({
        'id': 33, 
        'entrance_list': ((19.91, -0.35, -22.57, -0.78, ), (15.32, -0.35, -24.29, -1.48, ), (17.98, -0.35, -27.44, -0.73, ), (17.11, -0.35, -20.93, -0.73, ), (17.88, -0.35, -24.26, -1.15, ), (-20.99, -0.35, 28.16, 0.8, ), (-21.59, -0.35, 31.79, 0.38, ), (-23.39, -0.35, 26.56, -0.03, ), (-20.98, -0.35, 29.43, -0.03, ), (-17.18, -0.35, 30.46, 1.2, ), (1.75, -0.35, 28.57, 1.39, ), (0.7, -0.35, 23.77, -0.9, ), (7.46, -0.35, 27.69, -2.74, ), (4.96, -0.35, 29.01, -2.74, ), (-1.05, -0.35, 25.36, -2.18, ), (-2.42, -0.35, -21.16, -0.57, ), (-5.97, -0.35, -21.69, 0.08, ), (-4.71, -0.35, -25.24, 1.76, ), (-8.47, -0.35, -23.35, 1.7, ), (-10.95, -0.35, -22.22, -1.39, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'map': 'L_LoadingYard', 
        'map_icon': 10671, 
        'map_sound_param': 0.3, 
        'name': '中转场', 
        'navimesh': 'a3333acc-0ad6-4641-8f5e-897d58e2558b', 
    }), 
    34: TD({
        'id': 34, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'L_LevalArt', 
        'name': 'TA测试场景', 
    }), 
    35: TD({
        'id': 35, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'ResourceDepot', 
        'name': '单独资源导出场景', 
        'ue_level_path': '/Game/Maps/ResourceDepot/ResourceDepot', 
    }), 
    36: TD({
        'id': 36, 
        'bgm_id': 863, 
        'entrance_list': ((-0.59, 1.4, -0.46, -3.14, ), ), 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'L_WeaponVFXDisplay', 
        'name': '武器靶场', 
        'ue_level_path': '/Game/Maps/WeaponVFXDisplay/L_WeaponVFXDisplay', 
    }), 
    37: TD({
        'id': 37, 
        'entrance_list': ((-72.96, 1.5, -65.14, -2.98, ), (-59.78, 1.5, -51.17, 0.78, ), (-68.83, 1.5, -44, -2.16, ), (-78.31, 1.5, -48.39, -0.1, ), (-46.19, 1.5, -50.11, -1.37, ), (-23.05, 1.5, -60.45, 2.53, ), (-1.19, 1.5, -37.34, 1.93, ), (-30.06, 1.5, -31.66, 3.12, ), (-29.75, 5.5, -11.77, 1.43, ), (-47.75, 5.5, -3.03, -0.66, ), (-44.68, 5.5, -25.56, 1.88, ), (-64.6, 5.5, -13.21, -1.61, ), (-69.32, 5.5, -12.61, 1.13, ), (-67.54, 5.5, -8.7, -2.06, ), (-83.48, 5.5, -21, -2.13, ), (-68.12, 5.5, -57.75, -1.83, ), (-52.55, 5.5, -50.21, -1.18, ), (-45.12, 5.5, -63.86, -2.94, ), (-45.13, 1.5, -63.34, 3.03, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'map': 'L_CloudMansion', 
        'map_icon': 10671, 
        'map_sound_param': 0.6, 
        'name': '云顶豪宅', 
        'navimesh': 'd5ac069b-c873-4461-900f-3cadc6255dd5', 
    }), 
    38: TD({
        'id': 38, 
        'entrance_list': ((-0.59, 1.4, -0.46, -3.14, ), ), 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'L_LooDev_CE', 
        'name': '默认灯光模板（CE编辑器用）', 
        'ue_level_path': '/Game/Tools/Tool_LookDev/Sublevels/L_LooDev_CE', 
    }), 
    39: TD({
        'id': 39, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'L_WeaponOverview', 
        'name': '枪械预览', 
        'ue_level_path': '/Game/Maps/WeaponOverview/L_WeaponOverview', 
    }), 
    40: TD({
        'id': 40, 
        'entrance_list': ((21.9, 16, 68.8, -3.14, ), ), 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'map': 'L_Canteen', 
        'name': '商场食堂', 
        'ue_level_path': '/Game/Maps/Canteen/L_Canteen', 
    }), 
    41: TD({
        'id': 41, 
        'bgm_id': 862, 
        'entrance_list': ((-84.85, 0.54, -10.47, -2.77, ), (-84.68, 0.54, -8.04, -2.8, ), (-82.81, 0.54, -7.13, -2.62, ), (-80.75, 0.54, -8.65, -2.22, ), (-80.62, 0.54, -7.16, -2.4, ), (-82.89, 0.57, -3.72, -2.83, ), (-46.31, 0.54, -74.1, -0.9, ), (-49.05, 0.54, -74.1, -0.81, ), (-51.26, 0.54, -74.1, -0.45, ), (-46.98, 0.54, -72.49, -1.09, ), (-49.36, 0.54, -72.27, -1.13, ), (-44.81, 0.54, -73.17, -1.44, ), (-67.91, 0.51, -1.14, -2.31, ), (-76.34, 0.51, -0.24, -2.55, ), (-80.36, 0.51, 1.78, -2.75, ), (-69.56, 0.53, -10.48, -2.56, ), (-61.6, 4.49, -8.12, -2.2, ), (-72.6, 4.49, -8.44, -2.25, ), (-78.94, 4.49, -8.11, -2.56, ), (-73.15, 4.49, -12.76, -2.06, ), (-62.77, 4.49, -19.36, -1.52, ), (-52.94, 4.49, -18.04, -1.72, ), (-54.3, 4.49, -10.2, -1.98, ), (-57.72, 4.49, -5.69, -1.91, ), (-57.25, 4.51, -51.54, -1.25, ), (-62.86, 4.51, -49.34, -1.34, ), (-67.95, 4.51, -52.74, -0.83, ), (-72.06, 4.49, -40.17, -1.49, ), (-67.39, 2.26, -58.51, -0.96, ), (-58.31, 0.54, -51.33, -1.24, ), (-63.67, 0.54, -51.37, -1.24, ), (-57.63, 0.63, -44.25, -1.14, ), (-72.11, 0.53, -58.07, -0.89, ), (-52.86, 4.51, -57.6, -0.76, ), (-52.99, 4.51, -54.61, -1.07, ), (-54.21, 4.51, -61.76, -0.41, ), (-46.88, 4.51, -50.57, -1.4, ), (-53.71, 0.54, -48.11, -1.25, ), (-81.9, 0.53, -11.0, -2.74, ), (-76.49, 0.53, -4.35, -2.62, ), (-75.79, 0.53, -9.12, 2.92, ), (-63.83, 4.49, -11.18, -2.92, ), (-75.83, 4.49, -18.65, 2.91, ), (-83.34, 4.49, -24.02, 2.99, ), (-84.15, 4.49, -13.6, -3.13, ), (-88.61, 4.48, -25.39, -3.14, ), (-82.7, 4.49, -29.83, 2.68, ), (-56.3, 4.49, -21.04, -2.55, ), (-55.04, 4.49, -14.13, -2.8, ), (-45.61, 0.55, -24.75, -2.45, ), (-66.71, 0.54, -22.77, -3.03, ), (-44.58, 4.51, -57.37, -1.13, ), (-48.21, 4.51, -48.21, -1.84, ), (-48.74, 4.51, -56.62, -1.15, ), (-54.06, 4.52, -64.66, -0.45, ), (-37.02, 4.51, -57.82, -1.35, ), (-41.56, 4.51, -56.2, -1.43, ), (-46.77, 0.54, -65.46, -0.95, ), (-45.94, 0.54, -62.13, -1.05, ), (-42.65, 0.54, -57.38, -1.36, ), (-46.6, 0.54, -51.35, -1.44, ), (-51.89, 0.54, -56.73, -1.23, ), (-53.72, 0.54, -64.5, -0.93, ), (-53.77, 0.54, -72.51, -0.8, ), (-48.63, 0.54, -70.81, -1.14, ), (-45.86, 0.54, -70.53, -1.41, ), (-38.17, 0.54, -62.06, -1.39, ), (-29.08, 0.54, -65.09, -0.18, ), (-29.73, 0.54, -55.43, -0.22, ), (-31.23, 0.54, -51.86, -0.32, ), (-16.71, 0.56, -49.98, -0.57, ), (-17.94, 0.58, -38.94, -0.84, ), (-24.95, 0.55, -41.13, -0.13, ), (-33.34, 0.56, -39.13, -0.68, ), (-54.27, 0.54, -50.9, -0.01, ), (-55.57, 4.51, -58.55, 0.5, ), (-42.98, 4.51, -58.11, -0.02, ), (-32.86, 4.51, -58.87, -0.17, ), (-83.85, 4.49, -15.32, 1.17, ), (-82.79, 4.49, -10.91, 1.42, ), (-83.66, 4.49, -33.28, 0.91, ), (-75.91, 4.49, -36.11, 0.87, ), (-70.24, 4.51, -41.23, 0.45, ), (-76.82, 0.53, -13.09, 1.44, ), (-78.8, 0.51, -2.56, 1.76, ), (-87.07, 0.58, -9.32, 1.42, ), (-69.75, 4.51, -25.57, 0.48, ), (-83.99, 0.53, -55.19, 1.32, ), (-81.51, 0.53, -59.4, 1.07, ), (-78.52, 0.53, -58.0, 1.03, ), (-76.99, 0.53, -48.27, 1.72, ), (-83.45, 4.49, -43.32, 1.84, ), (-77.56, 4.51, -47.46, 1.75, ), (-70.32, 0.53, -37.81, 2.0, ), (-69.14, 0.53, -42.96, 1.93, ), (-69.9, 0.53, -32.44, 2.19, ), (-10.34, -0.33, -31.76, -2.39, ), (-14.55, 0.58, -30.44, -2.49, ), (-26.4, 0.55, -24.99, -2.97, ), (-23.33, 4.48, -16.62, -2.96, ), (-32.08, 4.49, -19.58, -2.97, ), (-42.28, 4.5, -20.26, 2.98, ), (-13.25, 0.56, -42.12, -1.9, ), (-23.74, 4.48, -21.8, -2.86, ), (-37.75, 4.5, -5.52, -2.2, ), (-43.25, 4.49, -13.26, -1.77, ), (-51.04, 4.49, -14.56, -1.68, ), (-51.99, 4.49, -23.16, -1.26, ), (-64.51, 0.59, 2.76, -2.58, ), (-71.36, 0.51, 2.93, -3.06, ), (-53.45, 4.51, -51.47, -1.06, ), (-51.42, 4.51, -56.73, -0.84, ), (-75.04, 0.56, -60.73, -0.16, ), (-79.76, 0.53, -60.62, 0.14, ), (-48.79, 0.55, -44.01, -1.3, ), (-100.62, 0.54, -34.85, 1.34, ), (-96.14, 2.5, -44.82, 1.07, ), ), 
        'gen_navmap': True, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_lines': (5, 6, 7, 8, ), 
        'loading_pic1': 49003, 
        'loading_pic2': 49003, 
        'map': 'L_CloudMansion_02', 
        'map_icon': 48003, 
        'map_sound_param': 0.6, 
        'name': '山庄', 
        'navimesh': '416840a0-effd-44ba-b751-936f224c47d6', 
        'ue_level_path': '/Game/Maps/CloudMansion/L_CloudMansion_02', 
    }), 
    42: TD({
        'id': 42, 
        'entrance_list': ((-1141.767, -24.155, -1174.947, 0, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'map': 'open_world_LSP_v2', 
        'map_icon': 10671, 
        'map_sound_param': 0.3, 
        'name': '4k地图', 
        'navimesh': '628100a0-b819-4ce5-a05a-4e7c79b34626', 
        'ue_level_path': '/Game/STF/Pack03-LandscapePro/Maps/open_world_LSP_v2', 
    }), 
    43: TD({
        'id': 43, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'L_Source_Decal', 
        'name': '贴花资源', 
        'ue_level_path': '/Game/Maps/Source/L_Source_Decal', 
    }), 
    45: TD({
        'id': 45, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'ShowRoom_01', 
        'name': 'showroom', 
        'ue_level_path': '/Game/Maps/ShowRoom/ShowRoom_01', 
    }), 
    47: TD({
        'id': 47, 
        'entrance_list': ((2.87, 1.8, -9.68, -0.33, ), (1.8, 1.8, -4.9, -0.59, ), (4.22, 1.8, -2.64, -1.34, ), (10.64, 1.8, -1.87, -0.61, ), (3.6, 1.8, -11.98, -1.66, ), (3.44, 1.8, -29.21, -1.71, ), (11.85, 1.8, -35.07, -3.05, ), (17.01, 1.8, -32.74, 1.62, ), (5.28, 1.8, -23.76, -1.92, ), (3.81, 1.8, -26.54, 2.96, ), (25.63, 1.8, -33.13, 2.04, ), (29.76, 1.8, -32.1, 1.92, ), (30.94, 1.8, -24.2, 2.39, ), (32.6, 1.8, -23.96, 3.07, ), (32.02, 1.8, -33.71, 1.98, ), (23.72, 1.8, -2.32, -0.15, ), (30.26, 1.8, -5.4, 1.34, ), (30.73, 1.8, -1.94, 1.06, ), (31.7, 1.8, -11.33, 1.85, ), (30.02, 1.8, -13.12, 0.68, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'map': 'L_LevalArt_02', 
        'map_icon': 10671, 
        'map_sound_param': 0.3, 
        'name': '野外切片', 
        'ue_level_path': '/Game/Maps/LevelArt/L_LevelArt_02', 
    }), 
    48: TD({
        'id': 48, 
        'entrance_list': ((2.87, 1.8, -9.68, -0.33, ), (1.8, 1.8, -4.9, -0.59, ), (4.22, 1.8, -2.64, -1.34, ), (10.64, 1.8, -1.87, -0.61, ), (3.6, 1.8, -11.98, -1.66, ), (3.44, 1.8, -29.21, -1.71, ), (11.85, 1.8, -35.07, -3.05, ), (17.01, 1.8, -32.74, 1.62, ), (5.28, 1.8, -23.76, -1.92, ), (3.81, 1.8, -26.54, 2.96, ), (25.63, 1.8, -33.13, 2.04, ), (29.76, 1.8, -32.1, 1.92, ), (30.94, 1.8, -24.2, 2.39, ), (32.6, 1.8, -23.96, 3.07, ), (32.02, 1.8, -33.71, 1.98, ), (23.72, 1.8, -2.32, -0.15, ), (30.26, 1.8, -5.4, 1.34, ), (30.73, 1.8, -1.94, 1.06, ), (31.7, 1.8, -11.33, 1.85, ), (30.02, 1.8, -13.12, 0.68, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'map': 'SoundTest', 
        'map_icon': 10671, 
        'map_sound_param': 0.3, 
        'name': '声音测试', 
        'skip': 'trunk_only', 
        'ue_level_path': '/Game/Maps/SoundTest/SoundTest', 
    }), 
    51: TD({
        'id': 51, 
        'Layer': ('Heightmap', 'Biome', 'LD', 'Lighting', 'ART_Formal', 'Gameplay', ), 
        'entrance_list': ((1369.53, 7.55, 2866.21, 0.39, ), ), 
        'gen_navmap': True, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 49001, 
        'loading_pic2': 49001, 
        'map': 'L_Miami_WC', 
        'map_icon': 48001, 
        'map_sound_param': 1.0, 
        'name': '迈阿密', 
        'navimesh': '6c823c8c-0b1a-449a-9f3e-896047954839', 
        'player_total_num': 80, 
        'ue_level_path': '/Game/Maps/MiamiWorldComposition/L_Miami_WC', 
    }), 
    53: TD({
        'id': 53, 
        'entrance_list': ((28.8, 3.7, -10.81, -3.08, ), (28.79, 3.7, -6.52, -2.75, ), (24.37, 3.7, -10.75, -2.71, ), (24.84, 3.7, -6.22, -2.92, ), (19.7, 3.7, -10.23, 3.06, ), (20.59, 3.7, -6.52, -2.86, ), (12.93, 3.7, -76.85, 0.1, ), (10.87, 3.7, -72.06, 1.32, ), (15.87, 3.7, -74.43, 0.03, ), (17.4, 3.7, -70.15, -0.66, ), (18.98, 3.7, -73.0, -0.11, ), (21.85, 3.7, -71.89, -0.26, ), (9.37, 3.7, -63.9, 1.51, ), (22.07, 3.7, -67.02, -0.61, ), (34.11, 3.7, -61.82, -0.41, ), (34.09, 3.7, -73.47, 0.98, ), (37.17, 3.7, -76.01, -0.1, ), (31.7, 3.7, -68.83, -0.24, ), (26.02, 3.7, -50.78, 2.23, ), (28.24, 3.7, -4.43, -2.41, ), (21.1, 3.7, -1.98, 2.41, ), (23.31, 3.7, -18.76, 1.14, ), (7.26, 3.7, -11.39, 1.52, ), (12.89, 3.7, -12.59, -2.42, ), (12.9, 3.7, -18.54, -1.26, ), (9.02, 3.7, -8.16, -3.02, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_lines': (5, 6, 7, 8, ), 
        'loading_pic1': 49005, 
        'loading_pic2': 49005, 
        'map': 'Test_mpmap', 
        'map_icon': 48005, 
        'map_sound_param': 0.4, 
        'name': '大楼顶', 
        'navimesh': '2d794caf-ad1a-4f4f-8d43-9abb5e904336', 
        'ue_level_path': '/Game/Test/Test_LZH/Test_mpmap', 
    }), 
    54: TD({
        'id': 54, 
        'entrance_list': ((-18.71, 1.0, -34.89, 1.02, ), (-19.46, 1.0, -27.41, 2.13, ), (-21.57, 1.0, -32.17, 1.38, ), (-22.81, 1.0, -27.97, 2.06, ), (-23.45, 1.0, -35.79, 0.88, ), (-23.93, 1.0, -30.28, 1.61, ), (74.72, 1.0, -62.36, -0.65, ), (77.54, 1.0, -60.72, -0.96, ), (81.31, 1.0, -58.34, -1.37, ), (81.67, 1.0, -61.56, -0.92, ), (77.67, 1.0, -63.77, -0.61, ), (76.13, 1.0, -66.71, -0.09, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 49006, 
        'loading_pic2': 49006, 
        'map': 'L_Supermarket', 
        'map_icon': 48006, 
        'map_sound_param': 0.4, 
        'name': '超市', 
        'navimesh': '8541c74f-3529-470f-a715-8eb99a41cf15', 
        'ue_level_path': '/Game/Maps/SuperMarket/L_Supermarket', 
    }), 
    55: TD({
        'id': 55, 
        'entrance_list': ((47.54, 1.0, 94.48, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_RVT', 
        'map_icon': 40227, 
        'map_sound_param': 0.3, 
        'name': 'L_RVT', 
        'navimesh': '93d526e8-a7a2-4163-9f11-aecb2ac43dea', 
        'ue_level_path': '/Game/Test/Test_Hujianbing/RVTLevel/L_RVT.umap', 
    }), 
    56: TD({
        'id': 56, 
        'entrance_list': ((0, 0, 0, 90, ), ), 
        'gen_navmap': True, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 49001, 
        'loading_pic2': 49001, 
        'map': 'L_Miami_Source_CommonHouses', 
        'map_icon': 48001, 
        'map_sound_param': 0.3, 
        'name': 'CommonHouses', 
        'navimesh': '653215a5-63de-4531-bb29-0a4b5691004a', 
        'ue_level_path': '/Game/Maps/Source/MiamiWorldComposition/L_Miami_Source_CommonHouses', 
    }), 
    57: TD({
        'id': 57, 
        'entrance_list': ((0, 0, 0, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_PostMatchTest01', 
        'map_icon': 40227, 
        'map_sound_param': 0.3, 
        'name': '测试结算场景', 
        'navimesh': 'eda521be-1531-bfe6-cd47-45f74e4d9f07', 
        'ue_level_path': '/Game/Maps/PostMatch/L_PostMatchTest01', 
    }), 
    58: TD({
        'id': 58, 
        'entrance_list': ((0, 0, 0, ), ), 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'MatchIntro', 
        'name': '入场展台场景', 
        'ue_level_path': '/Game/Maps/MatchLevel/MatchIntro', 
    }), 
    60: TD({
        'id': 60, 
        'Layer': ('ART', 'Heightmap', 'Lighting', 'Biome', 'Gameplay', ), 
        'entrance_list': ((0, 75, 0, ), ), 
        'gen_navmap': True, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'loading_pic1': 49002, 
        'loading_pic2': 49002, 
        'map': 'L_Island_01', 
        'map_icon': 48002, 
        'map_sound_param': 1.0, 
        'name': '度假岛', 
        'navimesh': '1246aec2-55c0-9708-e091-eaef704382bc', 
        'player_total_num': 60, 
        'ue_level_path': '/Game/Maps/Island/L_Island_01', 
    }), 
    61: TD({
        'id': 61, 
        'entrance_list': ((1369.53, 7.55, 2866.21, 0.39, ), ), 
        'gen_navmap': True, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'left_bottom': (1025, 34, 3211, ), 
        'left_top': (1025, 34, 9588, ), 
        'loading_pic1': 49001, 
        'loading_pic2': 49001, 
        'map': 'L_Miami_WC_PC', 
        'map_icon': 48001, 
        'map_sound_param': 1.0, 
        'name': '迈阿密-Proxy', 
        'navimesh': 'e0ca88cc-7129-6f01-6a34-d86fb83e3b3e', 
        'player_total_num': 80, 
        'right_bottom': (3386, 34, 3211, ), 
        'right_top': (3386, 34, 9588, ), 
    }), 
    62: TD({
        'id': 62, 
        'entrance_list': ((6.93, 1.41, -19.23, -0.516, ), ), 
        'gen_navmap': True, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 49007, 
        'loading_pic2': 49007, 
        'map': 'L_Range', 
        'map_icon': 48007, 
        'map_sound_param': 1.0, 
        'name': '靶场', 
        'navimesh': 'f66cc3e4-5751-303a-ceb9-f639f594505c', 
        'ue_level_path': '/Game/Maps/Range/L_Range', 
    }), 
    63: TD({
        'id': 63, 
        'entrance_list': ((-0.59, 1.4, -0.46, -3.14, ), ), 
        'gen_navmap': True, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_LevelArt_03', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': 'LevelArt', 
        'navimesh': '154829ec-1e56-ceac-0047-581dab61eb1c', 
        'ue_level_path': '/Game/Maps/LevelArt/L_LevelArt_03', 
    }), 
    64: TD({
        'id': 64, 
        'entrance_list': ((-156.37, 1.43, 87.76, 0.39, ), ), 
        'gen_navmap': True, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_WeaponShootingRange', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': 'COD靶场', 
        'navimesh': '2245372c-9dfb-8c4d-1441-a4b60e648a19', 
        'ue_level_path': '/Game/Maps/WeaponShootingRange/L_WeaponShootingRange', 
    }), 
    65: TD({
        'id': 65, 
        'Layer': ('ART', 'Heightmap', 'Lighting', 'Biome', 'Gameplay', ), 
        'entrance_list': ((0, 75, 0, ), ), 
        'gen_navmap': True, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'loading_pic1': 49002, 
        'loading_pic2': 49002, 
        'map': 'L_Island_01_PC', 
        'map_icon': 48002, 
        'map_sound_param': 1.0, 
        'name': '度假岛Proxy', 
        'navimesh': '81f02c71-3dc0-80ac-ca98-551b544b678c', 
        'player_total_num': 60, 
        'ue_level_path': '/Game/Maps/Island/L_Island_01', 
    }), 
    67: TD({
        'id': 67, 
        'entrance_list': ((1329.97, 9.07, 2849.65, 0.39, ), ), 
        'gen_navmap': False, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_Miami_WC_Mini', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '光照验证', 
        'ue_level_path': '/Game/Maps/MiamiWorldCompositionMini/L_Miami_WC_Mini', 
    }), 
    68: TD({
        'id': 68, 
        'entrance_list': ((1390.39, 6.41, 2805.76, 0.39, ), ), 
        'gen_navmap': False, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 49001, 
        'loading_pic2': 49001, 
        'map': 'L_MiamiBuildingBake', 
        'map_icon': 48001, 
        'map_sound_param': 1.0, 
        'name': 'Miami室内光照验证', 
        'ue_level_path': '/Game/Maps/MiamiBuildingBake/L_MiamiBuildingBake', 
    }), 
    69: TD({
        'id': 69, 
        'entrance_list': ((0, 0, 0, 90, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 49001, 
        'loading_pic2': 49001, 
        'map': 'L_Source_Miami_Temp', 
        'map_icon': 48001, 
        'map_sound_param': 1.0, 
        'name': '迈阿密资源总览', 
        'ue_level_path': '/Game/Maps/Source/L_Source_Miami_Temp', 
    }), 
    70: TD({
        'id': 70, 
        'entrance_list': ((-22.6, 2.45, 20, 180, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_LevelFeaturesShowroom_Main', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '关卡特性陈列室', 
        'ue_level_path': '/Game/Maps/LevelFeaturesShowroom/L_LevelFeaturesShowroom_Main', 
    }), 
    71: TD({
        'id': 71, 
        'entrance_list': ((0, 0, 0, ), ), 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'L_MainHall_Cutscene', 
        'name': '组队出场场景', 
        'ue_level_path': '/Game/Maps/MainHall/L_MainHall_Cutscene', 
    }), 
    72: TD({
        'id': 72, 
        'entrance_list': ((0, 0, 0, ), ), 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'L_Snapshot', 
        'name': '枪烘焙场景', 
        'ue_level_path': '/Game/Maps/Snapshot/L_Snapshot', 
    }), 
    73: TD({
        'id': 73, 
        'entrance_list': ((0, 1, 5.2, 0.39, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_LookDev_Messiah', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': 'LookDev_Messiah', 
        'ue_level_path': '/Game/Test/Test_YSL/Calibration/Level/L_LookDev_Messiah', 
    }), 
    74: TD({
        'id': 74, 
        'entrance_list': ((0, 0, 0, ), ), 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'L_CharacterDebut', 
        'name': '分屏组队登场场景', 
        'ue_level_path': '/Game/Maps/MainHall/L_CharacterDebut', 
    }), 
    75: TD({
        'id': 75, 
        'entrance_list': ((-156.37, 1.43, 87.76, 0.39, ), ), 
        'gen_navmap': False, 
        'is_baked': True, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'TerrainTest', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '地形测试', 
        'ue_level_path': '/Game/Test/Test_WZH/Terrain/TerrainTest', 
    }), 
    76: TD({
        'id': 76, 
        'entrance_list': ((0, 0, 0, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_VFXTest', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '特效测试', 
        'ue_level_path': '/Game/Maps/VFX_Test/L_VFXTest', 
    }), 
    77: TD({
        'id': 77, 
        'entrance_list': ((1679.4, 19.3, 2512, 0.39, ), (1740.41, 23.02, 2573.94, -1.88, ), (1728.1, 23.02, 2566.16, -2.88, ), (1715.54, 21.59, 2557.0, -1.89, ), (1693.79, 21.35, 2579.36, 2.5, ), (1688.24, 21.22, 2566.19, 2.9, ), (1682.52, 21.22, 2571.24, -2.65, ), (1673.56, 18.85, 2557.9, 1.55, ), (1659.78, 20.67, 2574.15, 3.06, ), (1669.03, 32.32, 2570.45, 2.7, ), (1645.47, 30.96, 2563.78, -0.9, ), (1635.84, 30.96, 2554.68, -2.69, ), (1653.84, 19.87, 2552.6, 2.53, ), (1630.61, 20.55, 2560.88, 1.91, ), (1629.16, 20.55, 2571.84, 2.83, ), (1642.58, 19.27, 2536.81, 0.71, ), (1619.09, 18.73, 2533.96, 1.85, ), (1624.63, 18.86, 2508.11, 0.84, ), (1633.37, 18.75, 2497.94, 0.93, ), (1642.85, 18.75, 2481.82, -0.34, ), (1654.5, 18.91, 2492.69, -1.41, ), (1650.48, 18.96, 2508.62, 0.66, ), (1663.03, 18.75, 2519.46, 0.7, ), (1677.43, 18.79, 2502.39, 0.61, ), (1698.77, 20.04, 2509.82, 0.35, ), (1704.44, 20.04, 2488.29, 0.71, ), (1736.48, 20.04, 2486.28, -0.79, ), (1747.89, 20.04, 2500.63, -1.14, ), (1748.12, 19.97, 2527.31, -1.9, ), (1731.05, 21.6, 2536.86, -1.83, ), (1733.11, 19.96, 2524.12, -1.01, ), (1737.42, 33.47, 2532.44, -0.97, ), (1741.06, 33.47, 2553.21, -1.97, ), (1736.85, 39.32, 2569.32, -2.27, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_Miami_DownTown_A1_Test', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '城区A1', 
        'ue_level_path': '/Game/Test/Test_ZLT/Map/L_Miami_DownTown_A1_Test', 
    }), 
    78: TD({
        'id': 78, 
        'entrance_list': ((2058.7, 22.06, 2994.58, 2.64, ), (2057.38, 22.06, 2985.8, 3.0, ), (2068.54, 22.06, 2991.09, 3.0, ), (2045.07, 22.06, 2992.47, 2.47, ), (2049.5, 22.06, 2980.15, 2.47, ), (2037.17, 22.06, 2992.16, 2.29, ), (2077.27, 22.06, 2892.91, -0.39, ), (2076.25, 22.06, 2878.61, 0.08, ), (2075.49, 24.51, 2866.6, -0.02, ), (2072.74, 22.06, 2882.89, 0.01, ), (2066.62, 22.06, 2890.95, -0.07, ), (2079.0, 22.06, 2886.14, -0.05, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_Miami_DownTown_C4_Test', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '城区C4', 
        'ue_level_path': '/Game/Test/Test_ZLT/Map/L_Miami_DownTown_C4_Test', 
    }), 
    79: TD({
        'id': 79, 
        'entrance_list': ((2110.27, 21.78, 2798.2, -1.66, ), (2115.29, 22.41, 2767.93, -1.56, ), (2124.16, 21.78, 2785.25, -1.24, ), (2102.33, 21.78, 2816.56, -3.06, ), (2116.86, 22.18, 2824.22, -2.56, ), (2118.95, 21.78, 2778.59, -0.77, ), (1980.01, 26.63, 2758.88, 0.97, ), (1981.02, 28.32, 2783.68, 1.27, ), (1983.12, 26.63, 2825.79, 1.5, ), (1960.34, 28.32, 2811.46, 1.45, ), (1988.39, 28.32, 2798.9, 1.54, ), (1992.51, 26.63, 2764.22, 2.04, ), (2041.93, 26.63, 2761.1, 1.57, ), (2069.65, 24.47, 2827.15, -1.62, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_Museum_HN', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '博物馆', 
        'ue_level_path': '/Game/Test/Test_HN/L_Museum_HN', 
    }), 
    8: TD({
        'id': 8, 
        'bgm_id': 861, 
        'entrance_list': ((-1, 3, 6, 0, ), ), 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'map': 'L_MainHall', 
        'name': '新大厅#debug', 
        'ue_level_path': '/Game/Maps/MainHall/L_MainHall', 
    }), 
    80: TD({
        'id': 80, 
        'entrance_list': ((1829.1, 22.13, 3078.37, 1.04, ), (1838.99, 22.15, 3080.01, 1.23, ), (1837.8, 23.11, 3061.81, 0.89, ), (1841.3, 23.11, 3052.13, 0.95, ), (1845.6, 26.81, 3040.75, 0.67, ), (1853.24, 26.77, 3039.47, 0.72, ), (1855.03, 22.13, 3018.23, 0.29, ), (1871.35, 22.13, 3026.23, 0.15, ), (1920.22, 22.13, 3081.3, -1.8, ), (1914.47, 22.13, 3090.07, -1.72, ), (1905.57, 22.13, 3107.44, -2.17, ), (1883.87, 23.36, 3125.24, -3.11, ), (1871.35, 23.62, 3114.01, 2.64, ), (1895.49, 22.13, 3088.24, -2.07, ), (1902.53, 22.13, 3078.84, -1.76, ), (1904.26, 22.13, 3098.81, -2.41, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_Miami_DownTown_C2_Test', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '物流园', 
        'ue_level_path': '/Game/Test/Test_ZLT/Map/L_Miami_DownTown_C2_Test', 
    }), 
    81: TD({
        'id': 81, 
        'entrance_list': ((12821, 7, 2939, 0.39, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_Plants_LD_DF', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '工厂DF区', 
        'ue_level_path': '/Game/Test/TEST_HWY/Level/L_Plants_LD_DF', 
    }), 
    82: TD({
        'id': 82, 
        'entrance_list': ((1788, 22, 2942, 0.39, ), ), 
        'gen_navmap': False, 
        'is_baked': False, 
        'is_messiah2024_map': True, 
        'is_small_map': True, 
        'loading_pic1': 40227, 
        'map': 'L_Downtown_C1_test', 
        'map_icon': 10671, 
        'map_sound_param': 1.0, 
        'name': '城区C1', 
        'ue_level_path': '/Game/Test/Test_YYL/L_Downtown_C1_test', 
    }), 
}
