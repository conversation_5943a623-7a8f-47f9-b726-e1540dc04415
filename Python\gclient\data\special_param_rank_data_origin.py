# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 19-特殊效果排序表.xlsx, sheetname:特殊效果排序
from taggeddict import taggeddict as TD

data = {
    1: TD({
        'id': 1,
        'state_id': 6,
        'tech_param1': (1, 0, 0, ),
        'rank': 10,
    }), 
    2: TD({
        'id': 2,
        'state_id': 6,
        'tech_param1': (0.9, 0.45, 0, ),
        'rank': 9,
    }), 
    3: TD({
        'id': 3,
        'state_id': 5,
        'tech_param2': (0, 0.3, 1, 2, ),
        'rank': 2,
    }), 
    4: TD({
        'id': 4,
        'state_id': 5,
        'tech_param1': (0.1, 0.04, 0, ),
        'tech_param2': (-1.0, 0.3, -3.0, 0.0, ),
        'rank': 2,
    }), 
    5: TD({
        'id': 5,
        'state_id': 6,
        'tech_param1': (1, 0, 0, ),
        'rank': 10,
    }), 
    6: TD({
        'id': 6,
        'state_id': 5,
        'tech_param1': (1.0, 0, 0, ),
        'tech_param2': (-1.0, 0.03, -3.0, 0.0, ),
        'rank': 10,
    }), 
    7: TD({
        'id': 7,
        'state_id': 5,
        'tech_param1': (1.0, 1.0, 1.0, ),
        'tech_param2': (-0.5, 0.0, 0.0, 0.0, ),
        'rank': 100,
    }), 
    8: TD({
        'id': 8,
        'state_id': 6,
        'tech_param1': (1, 0, 0, ),
        'rank': 10,
    }), 
    9: TD({
        'id': 9,
        'state_id': 5,
        'tech_param1': (1, 1, 0, ),
        'tech_param2': (0, 1000, 0, 0, ),
        'rank': 100,
    }), 
    10: TD({
        'id': 10,
        'state_id': 20,
        'tech_param1': (0, 0, 0, ),
        'tech_param2': (35, 999.5, 2.5, 0.3, ),
        'rank': 1,
    }), 
    11: TD({
        'id': 11,
        'state_id': 20,
        'tech_param1': (0.5, 0.0, 0.0, ),
        'tech_param2': (0, 1000, 0, 0.3, ),
        'rank': 2,
    }), 
    12: TD({
        'id': 12,
        'state_id': 5,
        'tech_param2': (0.1, 0, 0, 0, ),
        'rank': 1,
    }), 
    13: TD({
        'id': 13,
        'state_id': 20,
        'tech_param1': (0, 0, 0, ),
        'tech_param2': (35, 999.5, 2.5, 0.3, ),
        'rank': 1,
    }), 
    100: TD({
        'id': 100,
        'state_id': 0,
        'is_outline': True,
        'tech_param2': (1, 0.3, 0, 1, ),
        'rank': 50,
    }), 
    101: TD({
        'id': 101,
        'state_id': 21,
        'tech_param1': (0.1, 0.5, 1.0, ),
        'tech_param2': (0, 1000, 0.1, 0.3, ),
        'rank': 50,
    }), 
    102: TD({
        'id': 102,
        'state_id': 0,
        'is_outline': True,
        'tech_param2': (1.0, 0.03, -0.3, 1, ),
        'rank': 50,
    }), 
    103: TD({
        'id': 103,
        'state_id': 0,
        'is_outline': True,
        'tech_param2': (0.1, 0, 0, 0.5, ),
        'rank': 50,
    }), 
    104: TD({
        'id': 104,
        'state_id': 0,
        'is_outline': True,
        'tech_param2': (1, 1, 1, 1.5, ),
        'rank': 50,
    }), 
    200: TD({
        'id': 200,
        'state_id': 21,
        'is_force_exclude_occlusion': True,
        'tech_param1': (0.8, 0, 0, ),
        'tech_param2': (1, 1000, 0, 0, ),
        'rank': 50,
    }), 
    201: TD({
        'id': 201,
        'state_id': 6,
        'is_outline': True,
        'tech_param1': (1, 0, 0, ),
        'tech_param2': (1, 0.3, 0, 1, ),
        'rank': 50,
    }), 
    301: TD({
        'id': 301,
        'state_id': 21,
        'tech_param1': (1, 0.63, 0.16, ),
        'tech_param2': (1, 1000, 0.63, 0.16, ),
    }), 
    302: TD({
        'id': 302,
        'state_id': 21,
        'tech_param1': (0, 0.63, 0.91, ),
        'tech_param2': (0, 1000, 0.63, 0.91, ),
    }), 
    303: TD({
        'id': 303,
        'state_id': 21,
        'tech_param1': (0, 0.6, 0.27, ),
        'tech_param2': (0, 1000, 0.6, 0.27, ),
    }), 
    304: TD({
        'id': 304,
        'state_id': 21,
        'tech_param1': (0.89, 0.73, 0.07, ),
        'tech_param2': (0.89, 1000, 0.73, 0.07, ),
    }), 
    305: TD({
        'id': 305,
        'state_id': 21,
        'tech_param1': (0.9, 0, 0.31, ),
        'tech_param2': (0.9, 1000, 0, 0.31, ),
    }), 
    306: TD({
        'id': 306,
        'state_id': 21,
        'tech_param1': (0.89, 0.73, 0.07, ),
        'tech_param2': (0.89, 1000, 0.73, 0.07, ),
    }), 
    307: TD({
        'id': 307,
        'state_id': 21,
        'tech_param1': (0.73, 0.69, 0.11, ),
        'tech_param2': (0.73, 1000, 0.69, 0.11, ),
    }), 
    308: TD({
        'id': 308,
        'state_id': 21,
        'tech_param1': (0.39, 0.55, 0.04, ),
        'tech_param2': (0.39, 1000, 0.55, 0.04, ),
    }), 
    309: TD({
        'id': 309,
        'state_id': 21,
        'tech_param1': (0, 0.37, 0.08, ),
        'tech_param2': (0, 1000, 0.37, 0.08, ),
    }), 
    310: TD({
        'id': 310,
        'state_id': 21,
        'tech_param1': (0.56, 0.13, 0.85, ),
        'tech_param2': (0.56, 1000, 0.13, 0.85, ),
    }), 
    311: TD({
        'id': 311,
        'state_id': 21,
        'tech_param1': (0.57, 0.03, 0.51, ),
        'tech_param2': (0.57, 1000, 0.03, 0.51, ),
    }), 
    312: TD({
        'id': 312,
        'state_id': 21,
        'tech_param1': (0.03, 0.04, 0.27, ),
        'tech_param2': (0.03, 1000, 0.04, 0.27, ),
    }), 
    313: TD({
        'id': 313,
        'state_id': 21,
        'tech_param1': (0, 0.28, 0.62, ),
        'tech_param2': (0, 1000, 0.28, 0.62, ),
    }), 
    314: TD({
        'id': 314,
        'state_id': 21,
        'tech_param1': (0.2, 0.19, 1, ),
        'tech_param2': (0.2, 1000, 0.19, 1, ),
    }), 
    315: TD({
        'id': 315,
        'state_id': 21,
        'tech_param1': (0.17, 0.09, 0.45, ),
        'tech_param2': (0.17, 1000, 0.09, 0.45, ),
    }), 
    316: TD({
        'id': 316,
        'state_id': 21,
        'tech_param1': (0.89, 0.73, 0.07, ),
        'tech_param2': (0.89, 1000, 0.73, 0.07, ),
    }), 
    317: TD({
        'id': 317,
        'state_id': 21,
        'tech_param1': (0.73, 0.69, 0.11, ),
        'tech_param2': (0.73, 1000, 0.69, 0.11, ),
    }), 
    318: TD({
        'id': 318,
        'state_id': 21,
        'tech_param1': (0.39, 0.55, 0.04, ),
        'tech_param2': (0.39, 1000, 0.55, 0.04, ),
    }), 
    319: TD({
        'id': 319,
        'state_id': 21,
        'tech_param1': (0, 0.37, 0.08, ),
        'tech_param2': (0, 1000, 0.37, 0.08, ),
    }), 
    320: TD({
        'id': 320,
        'state_id': 21,
        'tech_param1': (1, 0.2, 0.62, ),
        'tech_param2': (1, 1000, 0.2, 0.62, ),
    }), 
    321: TD({
        'id': 321,
        'state_id': 21,
        'tech_param1': (0.88, 0.08, 0.93, ),
        'tech_param2': (0.88, 1000, 0.08, 0.93, ),
    }), 
    322: TD({
        'id': 322,
        'state_id': 21,
        'tech_param1': (0.66, 0.32, 0.04, ),
        'tech_param2': (0.66, 1000, 0.32, 0.04, ),
    }), 
    323: TD({
        'id': 323,
        'state_id': 21,
        'tech_param1': (0.45, 0.13, 0.03, ),
        'tech_param2': (0.45, 1000, 0.13, 0.03, ),
    }), 
    324: TD({
        'id': 324,
        'state_id': 21,
        'tech_param1': (0.92, 0.38, 0, ),
        'tech_param2': (0.92, 1000, 0.38, 0, ),
    }), 
    325: TD({
        'id': 325,
        'state_id': 21,
        'tech_param1': (0.49, 0, 0, ),
        'tech_param2': (0.49, 1000, 0, 0, ),
    }), 
    326: TD({
        'id': 326,
        'state_id': 21,
        'tech_param1': (1, 0.63, 0.16, ),
        'tech_param2': (1, 1000, 0.63, 0.16, ),
    }), 
    327: TD({
        'id': 327,
        'state_id': 21,
        'tech_param1': (0, 0.63, 0.91, ),
        'tech_param2': (0, 1000, 0.63, 0.91, ),
    }), 
    328: TD({
        'id': 328,
        'state_id': 21,
        'tech_param1': (0, 0.6, 0.27, ),
        'tech_param2': (0, 1000, 0.6, 0.27, ),
    }), 
    329: TD({
        'id': 329,
        'state_id': 21,
        'tech_param1': (0.89, 0.73, 0.07, ),
        'tech_param2': (0.89, 1000, 0.73, 0.07, ),
    }), 
    330: TD({
        'id': 330,
        'state_id': 21,
        'tech_param1': (0.9, 0, 0.31, ),
        'tech_param2': (0.9, 1000, 0, 0.31, ),
    }), 
    331: TD({
        'id': 331,
        'state_id': 21,
        'tech_param1': (0.89, 0.73, 0.07, ),
        'tech_param2': (0.89, 1000, 0.73, 0.07, ),
    }), 
    332: TD({
        'id': 332,
        'state_id': 21,
        'tech_param1': (0.73, 0.69, 0.11, ),
        'tech_param2': (0.73, 1000, 0.69, 0.11, ),
    }), 
    333: TD({
        'id': 333,
        'state_id': 21,
        'tech_param1': (0.39, 0.55, 0.04, ),
        'tech_param2': (0.39, 1000, 0.55, 0.04, ),
    }), 
    334: TD({
        'id': 334,
        'state_id': 21,
        'tech_param1': (0, 0.37, 0.08, ),
        'tech_param2': (0, 1000, 0.37, 0.08, ),
    }), 
    335: TD({
        'id': 335,
        'state_id': 21,
        'tech_param1': (0.56, 0.13, 0.85, ),
        'tech_param2': (0.56, 1000, 0.13, 0.85, ),
    }), 
    336: TD({
        'id': 336,
        'state_id': 21,
        'tech_param1': (0.57, 0.03, 0.51, ),
        'tech_param2': (0.57, 1000, 0.03, 0.51, ),
    }), 
    337: TD({
        'id': 337,
        'state_id': 21,
        'tech_param1': (0.03, 0.04, 0.27, ),
        'tech_param2': (0.03, 1000, 0.04, 0.27, ),
    }), 
    338: TD({
        'id': 338,
        'state_id': 21,
        'tech_param1': (0, 0.28, 0.62, ),
        'tech_param2': (0, 1000, 0.28, 0.62, ),
    }), 
    339: TD({
        'id': 339,
        'state_id': 21,
        'tech_param1': (0.2, 0.19, 1, ),
        'tech_param2': (0.2, 1000, 0.19, 1, ),
    }), 
    340: TD({
        'id': 340,
        'state_id': 21,
        'tech_param1': (0.17, 0.09, 0.45, ),
        'tech_param2': (0.17, 1000, 0.09, 0.45, ),
    }), 
    341: TD({
        'id': 341,
        'state_id': 21,
        'tech_param1': (0.89, 0.73, 0.07, ),
        'tech_param2': (0.89, 1000, 0.73, 0.07, ),
    }), 
    342: TD({
        'id': 342,
        'state_id': 21,
        'tech_param1': (0.73, 0.69, 0.11, ),
        'tech_param2': (0.73, 1000, 0.69, 0.11, ),
    }), 
    343: TD({
        'id': 343,
        'state_id': 21,
        'tech_param1': (0.39, 0.55, 0.04, ),
        'tech_param2': (0.39, 1000, 0.55, 0.04, ),
    }), 
    344: TD({
        'id': 344,
        'state_id': 21,
        'tech_param1': (0, 0.37, 0.08, ),
        'tech_param2': (0, 1000, 0.37, 0.08, ),
    }), 
    345: TD({
        'id': 345,
        'state_id': 21,
        'tech_param1': (1, 0.2, 0.62, ),
        'tech_param2': (1, 1000, 0.2, 0.62, ),
    }), 
    346: TD({
        'id': 346,
        'state_id': 21,
        'tech_param1': (0.88, 0.08, 0.93, ),
        'tech_param2': (0.88, 1000, 0.08, 0.93, ),
    }), 
    347: TD({
        'id': 347,
        'state_id': 21,
        'tech_param1': (0.66, 0.32, 0.04, ),
        'tech_param2': (0.66, 1000, 0.32, 0.04, ),
    }), 
    348: TD({
        'id': 348,
        'state_id': 21,
        'tech_param1': (0.45, 0.13, 0.03, ),
        'tech_param2': (0.45, 1000, 0.13, 0.03, ),
    }), 
    349: TD({
        'id': 349,
        'state_id': 21,
        'tech_param1': (0.92, 0.38, 0, ),
        'tech_param2': (0.92, 1000, 0.38, 0, ),
    }), 
    350: TD({
        'id': 350,
        'state_id': 21,
        'tech_param1': (0.49, 0, 0, ),
        'tech_param2': (0.49, 1000, 0, 0, ),
    }), 
    351: TD({
        'id': 351,
        'state_id': 21,
        'tech_param1': (0.45, 0.13, 0.03, ),
        'tech_param2': (0.45, 1000, 0.13, 0.03, ),
    }), 
    352: TD({
        'id': 352,
        'state_id': 21,
        'tech_param1': (0.92, 0.38, 0, ),
        'tech_param2': (0.92, 1000, 0.38, 0, ),
    }), 
    353: TD({
        'id': 353,
        'state_id': 0,
        'is_outline': True,
        'tech_param2': (0.72, 0.05, 0.15, 0.25, ),
    }), 
}
