# -*- coding: utf-8 -*-
# author: l<PERSON><PERSON><PERSON><PERSON><PERSON>

import math
import MObject
import MType
import MEngine
import MConfig
import sys

import switches
from common.classutils import Components

from SunshineSDK.Custom.Editor.EditorMeta.camera.CameraShakePlacerMeta import CameraShakePlacerMeta
from SunshineSDK.Meta.TypeMeta import UpdateObject
from gclient import cconst
from gclient.data.ets_data.camera import grenade_burstCameraShake
from gclient.framework.camera.engine_placer import EnginePlacer
from gclient.framework.camera import fps_state_fsm
from gclient.framework.util import events
from gclient.framework.util.story_tick import StoryTick
from gshare import formula
from gshare.ugc_ship import math_utils
from gclient.util.debug_log_util import print_s, SomePreset  # noqa

EPSILON = sys.float_info.epsilon


@Components(
    fps_state_fsm.FpsRequisite,
    fps_state_fsm.FpsStateFSM,
    fps_state_fsm.StateStatic,
    fps_state_fsm.StateAim,
    fps_state_fsm.StateReloadAim,
    fps_state_fsm.StateJitter,
    fps_state_fsm.StateRecoil,
    fps_state_fsm.StateRecoilRecover,
    fps_state_fsm.StateRollShake,
    fps_state_fsm.StateBurstShake,
    fps_state_fsm.StateGraphRotate
)
class FpsPlacer(EnginePlacer):

    def __init__(self, master):
        super(FpsPlacer, self).__init__(master)
        self.placer_valid = False
        self.is_blender_in_finished = False
        self.InitParameter()
        # [DEBUG]
        self.InitDebugDraw()
        # [DEBUG]
        self.avoidBreakModelTimer = None
        self.use_tach_camera_space_translation_offset = True  # 现在开关需要gm手动打开

        self.prev_dir_yaw_pitch = None
        self.fps_cam_intertia_duration = 0.1
        self.fps_cam_intertia_tick_timer = None

        # FOV Blending parameters TODO: 或许单独拆成一个类控制好点
        # TODO: 抽象成一个工具类
        self.manual_fov_blender_timer_begin_fov = 0
        self.manual_fov_blender_timer_max_time = 0.3  # Blender过渡时间
        self.manual_fov_blender_timer_countdown = self.manual_fov_blender_timer_max_time

        # relative camera blender
        # TODO: 抽象成一个工具类
        self.cur_player_pos_when_start_camera_blender = MType.Vector3()
        self.cur_camera_pos_when_start_camera_blender = MType.Vector3()
        self.cur_hp_camera_bone_pos_when_start_camera_blender = MType.Vector3()
        self.relative_pos_timer = 0.0
        self.use_relative_camera_blender = False
        self.use_manual_hide_tps_model = False
        self.have_refresh_config = False

    def InitParameter(self):
        self.PitchMin = {cconst.ModelPoseType.Stand: -0.48 * math.pi, cconst.ModelPoseType.Prone: -0.2 * math.pi}
        self.PitchMax = {cconst.ModelPoseType.Stand: 0.48 * math.pi, cconst.ModelPoseType.Prone: 0.2 * math.pi}

        self.aim_pitch_max = 0
        self.aim_pitch_min = 0
        self.pose_pitch_easing = False

        self.AimDirection = MType.Vector3(0, 0, 1)
        self.PositionOffset = MType.Vector3(0, 0, 0)

        self.cameraEasing = None
        self.mover = None

        self.enable_ani_offset = False

        self.recoil_pitch_yaw = []

        self._callComponents("init")

    def InitDebugDraw(self):
        if not (MConfig.IsProfile and MConfig.Platform == 'windows'):
            return

        from gclient.util.plot_helper import FramePlot
        self.plotFramePitch = FramePlot(size=(800, 300), scale=(-0.05, 0.15), capacity=120, name='PitchPlot')
        self.plotFrameYaw = FramePlot(size=(800, 300), scale=(-0.2, 0.2), capacity=120, name='YawPlot')
        self.plotFrameRoll = FramePlot(size=(800, 300), scale=(-0.02, 0.02), capacity=120, name='RollPlot')
        self.plotFrameFov = FramePlot(size=(800, 300), scale=(0, 100), capacity=120, name='FovPlot')
        self.plotFrameAffiliatedFov = FramePlot(size=(800, 300), scale=(0, 70), capacity=120, name='AffiliatedFovPlot')

    def RefreshConfig(self, config=None):
        super(FpsPlacer, self).RefreshConfig(config)
        is_in_sunshine = hasattr(genv, "sunshine_client")
        if not self.have_refresh_config:
            UpdateObject(self, CameraShakePlacerMeta(), list(grenade_burstCameraShake.data['Entities'].values())[-1])
            self.have_refresh_config = True
        elif is_in_sunshine:
            # sunshine 环境下，支持更新配置
            UpdateObject(self, CameraShakePlacerMeta(), list(grenade_burstCameraShake.data['Entities'].values())[-1])

    def StorePrePlacerInfo(self):
        self.master.pre_placer_info['persistRecoilBeginYawPitch'] = self.persistRecoilBeginYawPitch
        self.master.pre_placer_info['shootMoveCameraPixelYawPitch'] = self.shootMoveCameraPixelYawPitch
        self.master.pre_placer_info['forcePersistRecoilRecover'] = self.forcePersistRecoilRecover

    def FetchPrePlacerInfo(self):
        self.persistRecoilBeginYawPitch = self.master.pre_placer_info.get('persistRecoilBeginYawPitch', MType.Vector2())
        self.shootMoveCameraPixelYawPitch = self.master.pre_placer_info.get('shootMoveCameraPixelYawPitch', MType.Vector2())
        self.forcePersistRecoilRecover = self.master.pre_placer_info.get('forcePersistRecoilRecover', False)

    def SetFocusTarget(self, target):
        super(FpsPlacer, self).SetFocusTarget(target)

        if not target:
            return
        if not self.placer or not self.placer.IsValid():
            return

        self.placer.Direction = self.AimDirection
        self.placer.PositionOffset = self.PositionOffset

        self.placer.AffiliatedFov = self.AffiliatedFov

        self.target.CreateSpecifyBone('HP_Camera')
        self.SetBoneFollowTarget(target.owner.hand_model.model)
        self.UseBonePosition(True, 'HP_Camera')

        # print(f"-------------### need blend:{self.need_blender}, timer:{self.avoidBreakModelTimer}")
        if self.avoidBreakModelTimer:
            genv.space.cancel_timer(self.avoidBreakModelTimer)
        if self.need_blender:
            print_s(f"SetFocusTarget: 需要blender, use_manual_hide_tps_model: {self.use_manual_hide_tps_model}", SomePreset.white_fg_red_bg)
            if not self.use_manual_hide_tps_model:
                self.avoidBreakModelTimer = genv.space.add_timer(self.blend_time * 0.79, self.HideTPSModel)
                pass
            self.CreateBlender()
            self.cur_player_pos_when_start_camera_blender = self.master.target.owner.model.model.Transform.translation
            self.cur_camera_pos_when_start_camera_blender = genv.camera.engine_camera.Transform.translation
            self.cur_hp_camera_bone_pos_when_start_camera_blender = self.master.target.owner.model.GetBoneWorldTransform('HP_Camera').translation
            self.relative_pos_timer = 0.0
        else:
            self.BlenderInFinished()
        self.master.controller.BlendInPlacer(self.placer, self.blender)
        self.master.enableCollider = False

        if target.owner and target.owner.IsCurTakeGunWeapon():
            self.SetFocusWeaponCase(target.owner.GetCurWeaponCase())

        # 刷新一下
        cur_pitch = self.master.pre_direction.pitch
        cur_yaw = self.master.pre_direction.yaw
        self.RotateTo(cur_yaw, cur_pitch)

        if genv.avatar.is_replaying:
            return

        self.EnableStoryTick()

        # self.manual_fov_blender_timer_max_time = 0.45  # TODO: 需要走参数Blender过渡时间
        print(f"self.manual_fov_blender_timer_max_time: {self.manual_fov_blender_timer_max_time}")
        if not self.need_blender:
            self.placer.Fov = self.MainFov
            # 如果不需要blender，确保FOV blend状态是完成
            self.manual_fov_blender_timer_countdown = self.manual_fov_blender_timer_max_time
        else:
            currentFov = genv.camera.engine_camera.FieldOfView
            # 重置FOV blend计时器和起始FOV
            self.manual_fov_blender_timer_countdown = 0
            self.manual_fov_blender_timer_begin_fov = currentFov
            # 初始设置为当前FOV
            self.placer.Fov = currentFov
            #
            info = self.cameraEasing.FovEasingInfo
            info.easingFinished = True
            self.cameraEasing.FovEasingInfo = info
    
    def HideTPSModel(self):
        if self.is_blender_in_finished:
            return
        owner = self.target.owner
        print_s(f"HideTPSModel: owner.is_fps_mode: {owner.is_fps_mode}, owner.is_ads: {owner.is_ads}", SomePreset.white_fg_red_bg)
        if owner.is_fps_mode or owner.is_ads:
            print_s("--------------->>>>>>>>>>> self.master.SetOnlyTpsModelVisible(False)", SomePreset.white_fg_red_bg)
            self.master.SetOnlyTpsModelVisible(False)
        
    def ManualHideTPSModel(self):
        # owner = self.target.owner
        # if owner.is_fps_mode or owner.is_ads:
        #     self.master.SetOnlyTpsModelVisible(False)
        # self.master.SetOnlyTpsModelVisible(True)   # 这是必要的
        owner = self.target.owner
        print_s(f"ManualHideTPSModel: owner.is_fps_mode: {owner.is_fps_mode}, owner.is_ads: {owner.is_ads}", SomePreset.white_fg_red_bg)
        if owner.is_fps_mode or owner.is_ads:
            if owner.IsPlayerCombatAvatar:
                self.master.SetTpsModelVisible(False)
                self.master.SetOnlyTpsModelVisible(False)
                pass
                # owner.model.RefreshMoveSpeed()
                # owner.model.ShowLowerModelForFps(True)
                # owner.hand_model.RefreshMoveState()

    def BlenderInFinished(self):
        super(FpsPlacer, self).BlenderInFinished()
        if self.avoidBreakModelTimer:
            genv.space.cancel_timer(self.avoidBreakModelTimer)
        owner = self.target.owner
        self.is_blender_in_finished = True
        # print("---------------<<<<<<<<<<<<<<<<< self.master.SetOnlyTpsModelVisible(False)")
        if not self.use_manual_hide_tps_model:
            # self.master.SetOnlyTpsModelVisible(True)   # 这是必要的
            pass
        if owner.is_fps_mode or owner.is_ads:
            if not self.use_manual_hide_tps_model:
                self.master.SetTpsModelVisible(False)
            if owner.IsPlayerCombatAvatar:
                owner.model.RefreshMoveSpeed()
                owner.model.ShowLowerModelForFps(True)
                owner.hand_model.RefreshMoveState()
        
        if self.need_blender:
            pass

    def RefreshPitchMaxMin(self):
        if not self.placer or not self.placer.IsValid():
            return
        placer = self.placer
        pose_type = self.target and self.target.owner and self.target.owner.pose_type
        cur_pitch = placer.Direction.pitch
        self.aim_pitch_max = self.PitchMax.get(pose_type, self.PitchMax[cconst.ModelPoseType.Stand])
        self.aim_pitch_min = self.PitchMin.get(pose_type, self.PitchMin[cconst.ModelPoseType.Stand])
        max_pitch_diff = cur_pitch - self.aim_pitch_max
        min_pitch_diff = cur_pitch - self.aim_pitch_min
        self.cameraEasing.BindEvent('OnRotateEasingFinished', None)
        self.cameraEasing.RotateEasing(0, 0, 0, 0)
        self.pose_pitch_easing = False
        if min_pitch_diff < 0:
            self.cameraEasing.BindEvent('OnRotateEasingFinished', self.OnRefreshPitchMaxMinCallback)
            self.cameraEasing.RotateEasing(0.0, min_pitch_diff, abs(min_pitch_diff) * 0.625, cconst.CameraEasingType[self.RecoilUpEasing])
            self.pose_pitch_easing = True
        elif max_pitch_diff > 0:
            self.cameraEasing.BindEvent('OnRotateEasingFinished', self.OnRefreshPitchMaxMinCallback)
            self.cameraEasing.RotateEasing(0.0, max_pitch_diff, abs(max_pitch_diff) * 0.625, cconst.CameraEasingType[self.RecoilUpEasing])
            self.pose_pitch_easing = True
        else:
            self.OnRefreshPitchMaxMinCallback(None, None)

    def OnRefreshPitchMaxMinCallback(self, easeTime, easeType):
        self.pose_pitch_easing = False
        if not self.placer:
            return
        self.placer.PitchMax = self.aim_pitch_max
        self.placer.PitchMin = self.aim_pitch_min

    def OnPlacerExpired(self, ientity):
        if ientity is self.placer:
            self.placer_valid = False

    def OnActive(self, info=None):
        if self.is_actived:
            return
        self.fps_cam_intertia_duration = 0.08
        if self.fps_cam_intertia_tick_timer:
            genv.space.cancel_timer(self.fps_cam_intertia_tick_timer)
        self.fps_cam_intertia_tick_timer = genv.space.add_repeat_timer(self.fps_cam_intertia_duration, self.OnFpsCamIntertiaTick)

        self.is_blender_in_finished = False
        if info:
            self.need_blender = info.get('need_blender', False)
            self.blend_type = info.get('blend_type', 0)
            self.blend_time = info.get('blend_time', 1)
            self.manual_fov_blender_timer_max_time = info.get('fov_blend_time', 0)
            self.use_relative_camera_blender = info.get('use_relative_camera_blender', False)
            self.use_manual_hide_tps_model = info.get('use_manual_hide_tps_model', False)

        self.is_actived = True

        if self.placer and self.placer.IsValid():
            self.placer.SetExpireCallback(None)

        placer = MObject.CreateObject("FpsPlacer")
        pose_type = self.target and self.target.owner and self.target.owner.pose_type
        self.aim_pitch_max = self.PitchMax.get(pose_type, self.PitchMax[cconst.ModelPoseType.Stand])
        self.aim_pitch_min = self.PitchMin.get(pose_type, self.PitchMin[cconst.ModelPoseType.Stand])
        placer.PitchMax = self.aim_pitch_max
        placer.PitchMin = self.aim_pitch_min
        placer.AffiliatedFov = self.AffiliatedFov
        self.placer = placer
        # 防止OnDeactive和OnActive之间被释放
        self.master.controller.Retain(self.placer)
        self.placer_valid = True
        self.placer.SetExpireCallback(self.OnPlacerExpired)

        self.RefreshConfig()
        self.ApplyCameraEasing()
        self.EnableBoneFollow(True)
        self.FSMBootstrap('Static')

        self.master.controller.AffiliatedCamera = genv.hud_camera
        self.target and self.SetFocusTarget(self.target)

    def OnDeactive(self):
        if self.avoidBreakModelTimer:
            genv.space.cancel_timer(self.avoidBreakModelTimer)

        if self.fps_cam_intertia_tick_timer:
            genv.space.cancel_timer(self.fps_cam_intertia_tick_timer)
            self.fps_cam_intertia_tick_timer = None
        # 移除旧的 timer 取消逻辑
        # if self.manual_fov_blender_timer:
        #     genv.space.cancel_timer(self.manual_fov_blender_timer)
        self.DisableStoryTick()
        # self.InitParameter() # OnDeactive时不应该完全重置参数，只重置需要重置的部分
        # 重置FOV blend状态
        self.manual_fov_blender_timer_countdown = self.manual_fov_blender_timer_max_time
        if self.placer:
            self.FSMClear()
        super(FpsPlacer, self).OnDeactive()

    def ApplyCameraEasing(self):
        if not self.cameraEasing:
            self.cameraEasing = MObject.CreateObject('CameraEasing')
            self.master.controller.Retain(self.cameraEasing)
            #
            self.real_cameraEasing = MObject.CreateObject('CameraEasing')
            self.master.real_camera_controller.Retain(self.real_cameraEasing)
            import MTypeEx  # noqa
            MTypeEx.InitializeType("BezierCurvePoint")
        self.placer.ApplyEasing(self.cameraEasing)

    def TurnView(self, diffx, diffy, shoot_adsorb=False):
        if not self.is_blender_in_finished:
            return
        if not self.target:
            return
        self.Rotate(diffx, diffy)
        self.target.SetPitchAndYaw(0, self.placer.Direction.yaw)

        if self.state == 'Recoil':
            self.shootMoveCameraPixelYawPitch += MType.Vector2(abs(diffx), abs(diffy))
        if shoot_adsorb:
            self.shootMoveCameraPixelYawPitch += MType.Vector2(3.14, 3.14)

    def Rotate(self, yaw, pitch):
        if not self.placer_valid:
            return
        if abs(yaw) <= EPSILON and abs(pitch) <= EPSILON:
            return
        if self.mover:
            return
        if self.pose_pitch_easing:
            cur_pitch = self.placer.Direction.pitch
            if cur_pitch < self.aim_pitch_min and pitch > 0:
                pitch = 0
            elif cur_pitch > self.aim_pitch_max and pitch < 0:
                pitch = 0
            else:
                self.RefreshPitchMaxMin()
        # print(f'Rotate: yaw: {yaw}, pitch: {pitch}')
        # temp
        self.placer.Rotate(yaw, pitch)

    def RotateTo(self, yaw, pitch, rotate_target=True):
        print(f"Rotate: {yaw}, {pitch}")
        if not self.placer_valid:
            return
        curDirection = self.placer.Direction.get_normalized()
        curDirectionLength = curDirection.length
        pose_type = self.target and self.target.owner and self.target.owner.pose_type
        pitch_max = self.PitchMax.get(pose_type, self.PitchMax[cconst.ModelPoseType.Stand])
        pitch_min = self.PitchMin.get(pose_type, self.PitchMin[cconst.ModelPoseType.Stand])
        newPitch = formula.ClampNumber(pitch, pitch_min, pitch_max)
        newYaw = yaw
        curDirection.set_pitch_yaw(newPitch, newYaw)
        self.placer.Direction = curDirection * curDirectionLength
        if self.target and rotate_target:
            self.target.SetPitchAndYaw(newPitch, newYaw)

    def SetBoneFollowTarget(self, animTarget):
        if not self.cameraEasing:
            return
        self.cameraEasing.AnimTarget = animTarget

    def EnableBoneFollow(self, enable, bone_name='HP_Camera'):
        if not self.cameraEasing:
            return
        self.cameraEasing.BoneNameRotation = 'HP_Camera'
        self.cameraEasing.UseBoneRotation = enable

    def PlotFrameTick(self):
        if not switches.IS_DEBUG:
            return
        if not (MConfig.IsProfile and MConfig.Platform == 'windows'):
            return

        camera = MEngine.GetGameplay().Player.Camera
        camera_controller = genv.camera.controller
        self.plotFramePitch.Push(camera_controller.Transform.pitch)
        self.plotFrameYaw.Push(camera_controller.Transform.yaw)
        self.plotFrameRoll.Push(camera_controller.Transform.roll)
        self.plotFrameFov.Push(camera.FieldOfView)
        self.plotFrameAffiliatedFov.Push(self.placer.AffiliatedFov)
    
    def OnFpsCamIntertiaTick(self):
        if not self.is_actived:
            return
        target = self.master.target
        if not target:
            return
        placer = self.placer
        if not placer or not self.placer_valid:
            return
        curYaw, curPitch = placer.Direction.yaw, placer.Direction.pitch
        if not self.prev_dir_yaw_pitch:
            self.prev_dir_yaw_pitch = [curYaw, curPitch]
        yaw_diff = curYaw - self.prev_dir_yaw_pitch[0]
        pitch_diff = curPitch - self.prev_dir_yaw_pitch[1]
        self.prev_dir_yaw_pitch = [curYaw, curPitch]
        # genv.messenger.Broadcast(events.ON_POINTERS_INERTIA, yaw_diff, pitch_diff, self.fps_cam_intertia_duration - 0.016)
        pass

    def OnStoryTick(self, dtime):
        player = genv.player
        if not player or not player.IsPlayerCombatAvatar:
            return
        target = self.master.target
        if not target:
            return
        placer = self.placer
        if not placer or not self.placer_valid:
            return

        # 更新状态机
        self.FSMTick(dtime)

        # 处理手动FOV Blender
        if self.manual_fov_blender_timer_countdown < self.manual_fov_blender_timer_max_time:
            max_time = self.manual_fov_blender_timer_max_time
            beginFov = self.manual_fov_blender_timer_begin_fov
            endFov = self.MainFov
            fov_diff = endFov - beginFov

            self.manual_fov_blender_timer_countdown += dtime
            if self.manual_fov_blender_timer_countdown >= max_time:
                self.manual_fov_blender_timer_countdown = max_time  # 确保结束状态
                placer.Fov = endFov  # 设置最终FOV
            else:
                # 计算插值FOV
                ratio = self.manual_fov_blender_timer_countdown / max_time
                # 根据差值方向选择插值方式（保持原有逻辑，虽然可能统一处理更好）
                if fov_diff > 0:
                    placer.Fov = beginFov + fov_diff * ratio
                else:
                    # 当目标FOV小于起始FOV时，直接设置为起始FOV直到完成（保持原逻辑）
                    # 考虑是否应该也进行插值：placer.Fov = beginFov + fov_diff * ratio
                    placer.Fov = beginFov  # 保持原ManualFovBlenderTick逻辑，只在fov_diff>0时插值

        rotateYaw = rotatePitch = 0

        if self.JitterEnable and target.owner.is_ads:
            rotateYaw += self.jitterPitchYaw.x
            rotatePitch += self.jitterPitchYaw.y

        finalRotateRoll = self.shakeRoll + self.burstShakeRoll if 'RollShake' in self.insert_state else self.burstShakeRoll
        finalRotateYaw = rotateYaw
        finalRotatePitch = rotatePitch
        if 'BurstShake' in self.insert_state:
            finalRotateYaw += self.burstShakeYawDiff
            finalRotatePitch += self.burstShakePitchDiff

        placer.Roll = finalRotateRoll 
        self.Rotate(-finalRotateYaw, -finalRotatePitch)

        # TODO 骨骼相机偏移
        if placer.ApplyPivotRotate:
            pass
        else:
            hand_model = target.owner.hand_model
            if hand_model:
                # placer.PositionOffset = hand_model.GetBoneWorldTransform('HP_Camera').translation - hand_model.GetBoneWorldTransform('tag_view').translation
                self.enable_ani_offset and self.OnUpdateBoneOffset()

        # 本地需要调试的时候再加回来吧，svn就不提交了
        # genv.debug_log = str(formula.Tuple(self.placer.PositionOffset))

        if self.state.startswith('Recoil'):
            # print_s(f"Recoil: {self.recoil_pitch_yaw}", SomePreset.white_fg_red_bg)
            curYaw, curPitch = placer.Direction.yaw, placer.Direction.pitch
            rotateYaw, rotatePitch = 0, 0
            if self.recoil_pitch_yaw:
                rotateYaw, rotatePitch = curPitch - self.recoil_pitch_yaw[0], curYaw - self.recoil_pitch_yaw[1]
            self.recoil_pitch_yaw = [curPitch, curYaw]

            if rotateYaw != 0 and rotatePitch != 0:
                genv.messenger.Broadcast(events.ON_RECOIL_CAMERA_MOVE, rotateYaw, rotatePitch)
            
        # print_s(f"yaw: {yaw:0.4}, pitch: {pitch:0.4}, roll: {roll:0.4}", SomePreset.white_fg_red_bg)

        # print(f"scene placer fov : {placer.Fov}, add fov:{placer.AffiliatedFov}")
        if self.master.controller_follow_placer:
            cur_dir_len = placer.Direction.length
            # 除0 保护 TODO：看下为啥这个placer.Direction 的长度在上面之后变成了0
            if cur_dir_len > 0.001:
                self.master.controller_follow_placer.Direction = placer.Direction
            self.master.controller_follow_placer.Roll = finalRotateRoll
            self.master.controller_follow_placer.Fov = placer.Fov
            self.master.controller_follow_placer.TargetPosOffset = MType.Vector3(0, 0, 0)
            if self.cameraEasing.UseAdditiveFovOffset:
                # TODO: 引擎里的AdditiveFovOffset 应当叠加fov的，不知为何失效了（AdditiveAffiliatedFovOffset是正常的），故在脚本再叠一下，等引擎修改了就删掉
                # self.master.controller_follow_placer.Fov += self.cameraEasing.CurrAdditiveFovOffset
                pass


        # [DEBUG]
        gui.is_pc and self.PlotFrameTick()
        # [DEBUG]

    def EnableAnimationBoneOffset(self, enable):
        self.enable_ani_offset = enable
        hand_model = self.target.owner.hand_model
        if hand_model:
            hand_model.model.Tach.Transform = MType.Matrix4x3()

    def OnUpdateBoneOffset(self):
        hand_model = self.target.owner.hand_model
        transform_base = hand_model.GetBoneLocalTransform('HP_Camera_base') * hand_model.GetBoneLocalTransform('HP_Camera').inverse
        # translation = transform_base.translation
        # # 旋转
        # transform.yaw = transform_base.yaw
        # transform.roll = transform_base.roll
        # transform.pitch = transform_base.pitch
        hp_bone_local_trans = hand_model.GetBoneLocalTransform('HP_Camera')
        hp_bone_local_pos = hp_bone_local_trans.translation
        # 叠加上 OnUpdateBoneOffset_V2 的偏移
        transform_base.translation += hp_bone_local_pos
        # print_s(f"transform_base pos: {transform_base.translation}", SomePreset.white_fg_red_bg)
        hand_model.model.Tach.Transform = transform_base

    def OnUpdateBoneOffset_V2(self, engine_camera_trans):
        hand_model = self.target.owner.hand_model
        trans = hand_model.model.Tach.Transform
        # controller_trans = self.master.controller.Transform

        view_bone_local_trans = hand_model.GetBoneLocalTransform('HP_Camera_View')
        hp_bone_local_trans = hand_model.GetBoneLocalTransform('HP_Camera')
        hp_bone_roll = hp_bone_local_trans.roll
        hp_bone_yaw = hp_bone_local_trans.yaw
        hp_bone_pitch = hp_bone_local_trans.pitch

        hp_pos_offset = MType.Vector3(0, 0, 0) - hp_bone_local_trans.translation
        hp_pos_offset.x *= -1
        hp_pos_offset.y *= -1
        hp_pos_offset = self.RevertPosBy90Yaw(hp_pos_offset)

        view_pos_offset = MType.Vector3(0, 0, 0) - view_bone_local_trans.translation
        view_pos_offset.x *= -1
        view_pos_offset.y *= -1
        view_pos_offset = self.RevertPosBy90Yaw(view_pos_offset)


        if abs(view_pos_offset.x) < 0.001:
            hp_pos_offset.x = 0
        if abs(view_pos_offset.z) < 0.001:
            hp_pos_offset.z = 0
        if abs(view_pos_offset.y) < 0.001:
            hp_pos_offset.y = 0

        # print_s(f"view_pos_offset: {view_pos_offset}", SomePreset.white_fg_red_bg)
        # print_s(f"hp_pos_offset: {hp_pos_offset}", SomePreset.white_fg_red_bg)

        if not self.use_tach_camera_space_translation_offset:
            view_pos_offset = MType.Vector3(0, 0, 0)
        inverse_trans = engine_camera_trans.inverse
        # 因为下面这个原因，轴向是这样的 
        # 引用:"这段的逻辑就是用相机的local坐标来控制相机位移，同时忽略pitch影响，从而方便graph和动画控制"
        hp_x_axis = engine_camera_trans.x_axis
        hp_z_axis = engine_camera_trans.z_axis
        hp_y_axis = MType.Vector3(0, 1, 0)
        hp_x_axis.y = 0
        hp_x_axis.normalize()
        hp_z_axis.y = 0
        hp_z_axis.normalize()

        hp_x_axis = inverse_trans.transform_v(hp_x_axis)
        hp_y_axis = inverse_trans.transform_v(hp_y_axis)
        hp_z_axis = inverse_trans.transform_v(hp_z_axis)
        hp_bone_offset = hp_x_axis * hp_pos_offset.x + hp_y_axis * hp_pos_offset.y + hp_z_axis * hp_pos_offset.z

        local_view_x_axis = inverse_trans.transform_v(engine_camera_trans.x_axis)
        local_view_y_axis = inverse_trans.transform_v(engine_camera_trans.y_axis)
        local_view_z_axis = inverse_trans.transform_v(engine_camera_trans.z_axis)
        local_view_bone_offset = local_view_x_axis * view_pos_offset.x + local_view_y_axis * view_pos_offset.y + local_view_z_axis * view_pos_offset.z

        # offset_pos = - hp_bone_offset - local_view_bone_offset 
        # offset_pos = - hp_bone_offset 
        offset_pos = - local_view_bone_offset 
        # print_s(f"offset_pos: \t{offset_pos}", SomePreset.white_fg_red_bg)
        # print_s(f"local_view_bone_offset: \t{local_view_bone_offset}", SomePreset.white_fg_red_bg)

        view_bone_trans = hand_model.GetBoneTransform('HP_Camera_View')
        fov_scale = self.placer.AffiliatedFov / self.placer.Fov
        yaw_offset = -(view_bone_trans.yaw + math.pi)  # yaw比较特殊 + 180度
        pitch_offset = -view_bone_trans.pitch
        roll_offset = -view_bone_trans.roll

        # yaw_offset -= (hp_bone_yaw - math.pi / 2)
        # pitch_offset -= hp_bone_pitch
        # roll_offset -= hp_bone_roll
        # 需要规范化到-pi~pi 否则*fov_scale结果是错的
        yaw_offset %= 2 * math.pi
        yaw_offset = yaw_offset if yaw_offset <= math.pi else yaw_offset - 2 * math.pi
        
        trans.pitch = pitch_offset * fov_scale
        # print_s(f"trans.pitch: {trans.pitch}, fov_scale: {fov_scale}", SomePreset.white_fg_red_bg)
        trans.yaw = yaw_offset * fov_scale
        trans.roll = roll_offset

        if hand_model.model.Tach.CameraAttaching:
            hand_model.model.Tach.Transform = trans
            hand_model.model.Tach.RotaionMode = 1
            prev_trans = self.master.controller.Tach.CameraSpaceTransform
            prev_trans.translation = offset_pos
            self.master.controller.Tach.CameraSpaceTransform = prev_trans
    #################################### For Replay ############################

    def EnableStoryTickForReplay(self):
        StoryTick().Add(self.OnStoryTickForReplay)

    def DisableStoryTickForReplay(self):
        StoryTick().Remove(self.OnStoryTickForReplay)

    def OnStoryTickForReplay(self, dtime):
        player = genv.replay_player
        if not player or not player.IsRobotCombatAvatar or player.is_destroyed():
            return
        target = self.target
        if not self.target:
            return

        placer = self.placer
        if not placer.IsValid():
            return

        # 更新状态机
        self.FSMTick(dtime)

        rotateYaw = rotatePitch = 0

        if self.JitterEnable and target.owner.is_ads:
            rotateYaw += self.jitterPitchYaw.x
            rotatePitch += self.jitterPitchYaw.y

        finalRotateRoll = self.shakeRoll + self.burstShakeRoll if 'RollShake' in self.insert_state else self.burstShakeRoll
        finalRotateYaw = rotateYaw
        finalRotatePitch = rotatePitch
        if 'BurstShake' in self.insert_state:
            finalRotateYaw += self.burstShakeYawDiff
            finalRotatePitch += self.burstShakePitchDiff

        placer.Roll = finalRotateRoll
        if self.master.controller_follow_placer:
            self.master.controller_follow_placer.TargetPosOffset = MType.Vector3(0, 0, 0)
            self.master.controller_follow_placer.Roll = finalRotateRoll
            self.master.controller_follow_placer.Direction = placer.Direction
            self.master.controller_follow_placer.Fov = placer.Fov
            if self.cameraEasing.UseAdditiveFovOffset:
                # TODO: 引擎里的AdditiveFovOffset 应当叠加fov的，不知为何失效了（AdditiveAffiliatedFovOffset是正常的），故在脚本再叠一下，等引擎修改了就删掉
                self.master.controller_follow_placer.Fov += self.cameraEasing.CurrAdditiveFovOffset

        self.Rotate(-finalRotateYaw, -finalRotatePitch)

    def ApplyMover(self, beginTargetPosOffset, endTargetPosOffset, beginDirection, endDirection, movetime=0.5,
                   callback=None):
        if not self.target:
            return
        self.mover = MObject.CreateObject('LinearMover')
        self.placer.SetupMover(self.mover)
        self.master.controller.Retain(self.mover)
        self.mover.Target = self.target.model
        self.mover.BeginTargetPosOffset = beginTargetPosOffset
        self.mover.EndTargetPosOffset = endTargetPosOffset
        self.mover.BeginDirection = beginDirection
        self.mover.EndDirection = endDirection
        self.mover.MoveTime = movetime

        def combined_callback():
            if callback:
                callback()
            self.mover = None
        self.mover.BindEvent('RemovedFromTree', combined_callback)
        self.placer.ApplyMover(self.mover)

    def LookAtTargetDirection(self, target_direction, pos_offset, use_time=0, callback=None):
        target_direction.length = self.placer.Direction.length
        self.ApplyMover(self.placer.PositionOffset, self.placer.PositionOffset if not pos_offset else
                        pos_offset, self.placer.Direction,
                        target_direction, movetime=use_time, callback=callback)
    
    def OnPlacerRelease(self):
        # camera不引用placer的时候需要调用一下，防止placer一直被controller引用
        # Retain是为了防止OnDeactivate和OnActivate之间placer被释放掉
        if self.placer and self.placer.IsValid():
            self.master.controller.Release(self.placer)
    
    def HandlePlayerRelativeCameraBlender(self, trans):
        if not self.blender:
            return
        if not self.use_relative_camera_blender:
            return
        relative_pos_diff = self.master.target.owner.model.model.Transform.translation - self.cur_player_pos_when_start_camera_blender
        current_diff = relative_pos_diff 

        beginCameraPos = self.cur_camera_pos_when_start_camera_blender
        endCameraPos = self.cur_hp_camera_bone_pos_when_start_camera_blender
        blend_pos_diff = endCameraPos - beginCameraPos

        final_pos = MType.Vector3()

        scene = MEngine.GetGameplay().Scenario
        slow_time = scene.Speed
        fps = genv.fps_immediate
        dt = 1.0 / fps * slow_time
        self.relative_pos_timer += dt
        ratio = self.relative_pos_timer / self.blend_time
        if self.relative_pos_timer >= self.blend_time:
            self.relative_pos_timer = self.blend_time
            final_pos = endCameraPos
        else:
            if blend_pos_diff.length > 0:
                final_pos = beginCameraPos + blend_pos_diff * ratio
            else:
                final_pos = beginCameraPos  
        trans.translation = final_pos + current_diff
        # print_s(f"timer: {self.relative_pos_timer}, max_time: {max_time}, ratio: {ratio}", SomePreset.white_fg_yellow_bg)
    
    def OnAfterApplyCameraProperties(self, trans):  

        if not self.is_actived:
            return
        if genv.camera.fps_camera_enable_offset:
            return
        
        player = genv.player
        if not player or not player.IsPlayerCombatAvatar:
            return
        target = self.master.target
        if not target:
            return
        placer = self.placer
        if not placer or not self.placer_valid:
            return

        owner = target.owner
        if not owner:
            return
        hand_model = owner.hand_model
        if not hand_model:
            return
        # if not hand_model.model.Tach.CameraAttaching:
        #     return
        hand_model.model.Tach.CameraAttaching = True

        self.use_tach_camera_space_translation_offset = True
        if hand_model.loaded:
            trans.roll = self.placer.Roll

            view_bone_local_trans = hand_model.GetBoneLocalTransform('HP_Camera_View')
            if math.isnan(view_bone_local_trans.m11):
                print_s(f"!!!! HP_Camera__View出现了nan!!!!!!!!!, view_bone_local_trans: {view_bone_local_trans}", SomePreset.white_fg_red_bg)
                return
            hp_bone_local_trans = hand_model.GetBoneLocalTransform('HP_Camera')

            HP_bone_local_trans_for_rotation = MType.Matrix4x3()  # 基准变换
            HP_bone_local_trans_for_rotation.translation = MType.Vector3(0, 0, 0)
            HP_bone_local_trans_for_rotation.set_pitch_yaw_roll(0, math.pi / 2, hp_bone_local_trans.roll)

            hp_pos_offset = MType.Vector3(0, 0, 0) - hp_bone_local_trans.translation
            hp_pos_offset.x *= -1
            hp_pos_offset.y *= -1
            hp_pos_offset = self.RevertPosBy90Yaw(hp_pos_offset)

            view_pos_offset = MType.Vector3(0, 0, 0) - view_bone_local_trans.translation
            view_pos_offset.x *= -1
            view_pos_offset.y *= -1
            view_pos_offset = self.RevertPosBy90Yaw(view_pos_offset)

            # 这段的逻辑就是用相机的local坐标来控制相机位移，同时忽略pitch影响，从而方便graph和动画控制
            hp_x_axis = trans.x_axis
            hp_z_axis = trans.z_axis
            hp_y_axis = MType.Vector3(0, 1, 0)
            hp_x_axis.y = 0
            hp_x_axis.normalize()
            hp_z_axis.y = 0
            hp_z_axis.normalize()
            hp_bone_offset = hp_x_axis * hp_pos_offset.x + hp_y_axis * hp_pos_offset.y + hp_z_axis * hp_pos_offset.z

            local_view_x_axis = trans.x_axis
            local_view_y_axis = trans.y_axis
            local_view_z_axis = trans.z_axis
            local_view_bone_offset = local_view_x_axis * view_pos_offset.x + local_view_y_axis * view_pos_offset.y + local_view_z_axis * view_pos_offset.z
            # print_s(f"View bone offset: {view_pos_offset}", SomePreset.white_fg_red_bg)

            pos_offset = hp_bone_offset + local_view_bone_offset

            trans.translation += pos_offset

            # 使用MType.Quat和*操作符来实现四元数乘法
            view_bone_local_yaw = view_bone_local_trans.yaw
            HP_bone_local_yaw = HP_bone_local_trans_for_rotation.yaw
            local_yaw_offset = view_bone_local_yaw + HP_bone_local_yaw - math.pi   # 因为都加了 math.pi/2

            view_bone_local_pitch = view_bone_local_trans.pitch
            HP_bone_local_pitch = HP_bone_local_trans_for_rotation.pitch
            local_pitch_offset = view_bone_local_pitch + HP_bone_local_pitch

            view_bone_local_roll = view_bone_local_trans.roll
            HP_bone_local_roll = HP_bone_local_trans_for_rotation.roll
            local_roll_offset = view_bone_local_roll + HP_bone_local_roll
                
            # 创建一个四元数来表示偏移旋转（注意参数顺序：pitch, yaw, roll）
            offset_quat_tuple = math_utils.euler_to_quat(
                local_pitch_offset,
                local_yaw_offset,
                local_roll_offset
            )
                
            # 转换为MType.Quat对象
            offset_quat = MType.Quat(offset_quat_tuple[0], offset_quat_tuple[1], offset_quat_tuple[2], offset_quat_tuple[3])
                
            # 获取当前相机的旋转
            cam_rotation = trans.rotation
                
            # 将相机旋转转换为四元数
            cam_quat = MType.Quat(cam_rotation.x, cam_rotation.y, cam_rotation.z, cam_rotation.w)
                
            # 应用旋转偏移 (四元数乘法)
            result_quat = cam_quat * offset_quat
                
            # 应用新的旋转到相机变换上
            trans.rotation = MType.Vector4(result_quat.x, result_quat.y, result_quat.z, result_quat.w)

            self.OnUpdateBoneOffset_V2(trans)
            self.HandlePlayerRelativeCameraBlender(trans)
 
        return trans
    
    def RevertPosBy90Yaw(self, pos):
        revert_z = pos.z
        pos.z = pos.x
        pos.x = revert_z
        return pos
