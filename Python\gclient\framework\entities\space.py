# -*- coding: utf-8 -*-
"""
客户端场景相关
http://km.netease.com/team/yinhe_studio/article/289180
"""
from gclient.util.debug_log_util import SomePreset, print_s
from gclient.util.showroom_util import <PERSON>oseHeroShowRoomUtil
import math
import time
import os
import sys
import functools
import hashlib
import MRender
import MDebug
import MObject
import MCharacter
import MConfig
import MLauncher
import MPhysics
import MEngine
import MNavigate
import MType
import Timer
import GlobalData
import GeneralLog
import switches
from collections import defaultdict
from common.IdManager import IdManager
from common.classutils import Components
from client.ClientSpace import ClientSpace, SpaceClass
from gclient.gameplay.logic_base.entities.combat_item import Combat<PERSON><PERSON><PERSON>odelManager, CombatItemOctreeManager
from gclient.gameplay.util import replay_util
from gclient.util.profile.preload_graph import PreloadGraph
from gclient.framework.util.MHelper import EntityCacheManager
from gclient.framework.util.memory_util import MemoryManager
from gclient.framework.util.performance_util import EPerformanceLevel
from gclient.framework.util.shader_util import PrecompileShaderManager
from gclient.gameplay.util.replay_util import AntiPlugDebugDrawer
from gshare.consts import AIEventType

from gshare.fsm import FiniteStateMachine, StateWrapper
from gshare import ireplay, effect_util
from gshare import consts
from gclient import cconst
from gclient.util.decal_util import HitDecalManager
from gclient.framework.util import MHelper, events
from gclient.framework.models.model_cache import ModelCache
from gclient.framework.entities.camera import Camera
from gclient.gameplay.logic_base.entities.strike_item import StrikeItemManager
from gclient.gameplay.logic_base.entities.destructible_entity import DestructFragmentManager
from gclient.data import space_data, destruct_data, blast_data
from gshare import formula
from gclient.framework.util.async_util import Async, TAG_SPACE
from gclient.framework.commoncomps.space_weather_comp import SpaceWeatherComp
from gclient.framework.commoncomps.space_vehicle_cache_comp import SpaceVehicleCacheComp
from gclient.framework.commoncomps.space_water_trigger import SpaceWaterTriggerComp
from gclient.framework.commoncomps.space_ugc_comp import SpaceUGCComp
from gclient.util.anim_preload_mgr import AnimPreloadManager
from gclient.util.bullet.bullet_manager import BulletMgr


def EmptyScriptNotifyFunc(ientity, callback_type):
    pass


def task_callback(func):
    event = func.__name__

    @functools.wraps(func)
    def wrapper(self, *args):
        if not self.isValid:
            return

        if func(self, *args):
            self.onTaskFinished(event)

    wrapper.event = event
    return wrapper


class SeamlessSpaceLoader(object):
    IS_BATTLE_SPACE = True
    SEAMLESS_WORLD_MAPPING = None
    # SEAMLESS_WORLD_MAPPING: dict[tuple, dict[str, tuple]] = None
    # = {
    #     ("DengLu", "shootingrange_test2"): {
    #         "show_levels": ("shootingrange", "shootingrange_target",),
    #     },
    #     ("DengLu", "DengLu"): {
    #         "show_levels": (),
    #     },
    # }

    @classmethod
    def GetMappingKey(cls, spaceno):
        current_world = MHelper.GetActiveWorld()
        if not current_world:
            return
        target_world_name = space_data.data.get(spaceno, {}).get("map", "")
        return current_world.GetName(), target_world_name

    @classmethod
    def CanSeamlessLoad(cls, spaceno):
        return False
        # return cls.GetMappingKey(spaceno) in cls.SEAMLESS_WORLD_MAPPING

    def __init__(self, spaceno, space, owner):
        self.spaceno = spaceno
        self.space = space
        self.owner = owner

        self.isValid = True
        self.world = MHelper.GetActiveWorld()
        self.terrain = None

        seamless_data = self.SEAMLESS_WORLD_MAPPING[self.GetMappingKey(spaceno)]
        if seamless_data:
            self.target_levels = seamless_data["show_levels"] + ("$root", )

    def Load(self):
        self.space.add_timer(0.1, self.OnLoaded)

    def OnLoaded(self):
        if not self.isValid:
            return
        space = Space()
        if not space:
            return

        for name, level in self.world.Levels.items():
            if name in self.target_levels:
                level.EnterWorld()
            else:
                level.LeaveWorld()

        print("[SeamlessSpaceLoader](%s) on_loaded." % (self.spaceno, ))
        space.on_loaded()

    def Destroy(self):
        self.isValid = False

    def ForceFinishLoading(self):
        gui.ShowLoadingScene(False)
        self.space.RemoveWorldPreloadPivots('intro')
        genv.messenger.Broadcast(events.ON_SPACE_LOADED, self.spaceno)


class SpaceLoader(object):
    IS_BATTLE_SPACE = True

    def __init__(self, spaceno, space, owner):
        self.spaceno = spaceno
        self.space = space
        self.owner = owner

        self.isValid = True
        self.world = None
        self.terrain = None
        self.world_name = ''
        self.tasks = set()

        self.start_time = 0

        self.scene_load_timer = None
        self.physics_ready_timer = None
        self.ready_to_appear_cb = None
        self.data = space_data.data.get(spaceno, {})
        self.loading_pct = 0
        self.max_loading_pct = 1.0
        self.loading_max_progress = 0
        self.loading_progress_timer = None

    def Invalid(self):
        self.isValid = False

    def BeginTask(self, callback):
        print("[SpaceLoader](%s) BeginTask: %s" % (self.spaceno, callback.__name__))
        self.tasks.add(callback.event)
        return callback

    def SetLoadingPct(self, pct):
        real_pct = min(self.max_loading_pct, pct)
        self.loading_pct = max(self.loading_pct, real_pct)
        genv.messenger.Broadcast(events.ON_SPACE_LOADING_PERCENTAGE, self.loading_pct * 100.0)

    def GetOwnerPosition(self):
        # todo OPT
        owner = self.owner
        if owner.is_destroyed():
            return (0, 1.8, 0)
        else:
            return owner.position

    @property
    def IsNavigateReady(self):
        return self.OnPhysicsReady.event not in self.tasks

    @property
    def IsPhysicsReady(self):
        return self.OnNavigateReady.event not in self.tasks

    def onTaskFinished(self, res):
        print("[SpaceLoader](%s) onTaskFinished: %s" % (self.spaceno, res))
        self.tasks.discard(res)

        if self.tasks:
            return

        space = Space()
        if not space:
            return

        print("[SpaceLoader](%s) on_loaded." % (self.spaceno, ))
        space.on_loaded()
        self.SetLoadingPctForFinishTask()
        gpl.OnLeaveLoading()

    def SetLoadingPctForFinishTask(self):
        self.max_loading_pct = 1.0
        self.SetLoadingPct(0.99)

    def Load(self):
        self.world_name = ''
        self.start_time = time.time()

        gpl.OnEnterLoading()
        # 先加载个空world，卸载之前，降降内存峰值。
        if not GlobalData.IsSceneGame:
            MEngine.GetGameplay().Scenario.LoadWorld('', self.OnRealLoadWorld)
        else:
            self.OnWorldLoaded(MHelper.GetActiveWorld())
        if not self.loading_progress_timer:
            self.loading_progress_timer = self.space.add_repeat_timer(0.03, self.LoadingProgressTickTimer)

    def LoadingProgressTickTimer(self):
        if self.loading_pct > 0.95:
            loading_pct = self.loading_pct + (1.0 - self.loading_pct) * 0.1
        else:
            loading_pct = self.loading_pct + 0.001
        self.SetLoadingPct(loading_pct)

        # # TODO: 这里肯定有什么问题, 没救了??
        # if self.loading_pct >= 1.0:
        #     self.ForceFinishLoading()

    def ForceFinishLoading(self):
        self._CancelLoadingProgressTimer()
        # 发布UI事件
        gui.ShowLoadingScene(False)
        genv.messenger.Broadcast(events.ON_SPACE_LOADED, self.spaceno)
        self.space.RemoveWorldPreloadPivots('intro')

    def _CancelLoadingProgressTimer(self):
        if self.loading_progress_timer:
            space = self.space
            if space and not space.is_destroyed():
                self.space.cancel_timer(self.loading_progress_timer)
            self.loading_progress_timer = None
            return True
        return False

    def OnRealLoadWorld(self, _):
        if not self.isValid:
            return
        # 设置默认相机位置
        owner = self.owner
        if not owner.is_destroyed():
            camera_pos = MType.Vector3(*formula.Add3D(self.GetOwnerPosition(), (0, 1.8, 0)))
            self.SetCameraTransform(camera_pos, owner.area.yaw)

        self.world_name = self.data.get("map", "")
        if not self.world_name:
            raise RuntimeError("World %s Is Not Found." % self.data.get("map", ""))

        if self.space.is_high_world:
            self.world_name = self.world_name + '_high'

        MHelper.LoadWorld(
            self.world_name,
            self.BeginTask(self.OnWorldLoaded),
        )

    @task_callback
    def OnWorldLoaded(self, world):
        if world is None:
            # 加载失败回到大厅
            genv.avatar.CallServer("RequestEnterHall")
            raise RuntimeError('Failed To Load New World! world_name = %s' % self.world_name)

        world.spaceno = self.spaceno
        self.world = world
        self.world.BindEvent('ReadyToAppear', self.WorldReadyToAppear)
        gpl.ApplySavePipelineCache()
        # note that some how after world loaded, render option is changed so we force fixed overlay to be applied again.
        gpl.ApplyFixedRenderOptionOverlay()  # noqa
        from gclient.config import LocalConfig
        gpl.ApplyPostRenderOptionOverlay(LocalConfig.GetDefaultConfigName())  # noqa
        gpl.ApplyRenderConfig()
        gpl.ApplySpaceOverrideConfig(self.spaceno)

        # 场景模型加载
        MEngine.GetGameplay().Scenario.BeginLoading(
            self.BeginTask(self.OnSceneLoadNotify),
        )
        self.loading_pct = 0
        self.loading_max_progress = 0
        self.scene_load_timer = Timer.addTimer(60.0 * 5, self.OnSceneLoadNotifyTimeout).timerid

        # 路点加载
        wayPoint = self.data.get('nav', '')
        if wayPoint:
            self.world.NavigateMap = MNavigate.CreateNavigateMapFromGuid(wayPoint)
            self.world.NavigateMap.BindEvent(
                'ResourceUpdated',
                self.BeginTask(self.OnNavigateReady),
            )
        return True

    def WorldReadyToAppear(self, world):
        if self.ready_to_appear_cb:
            self.ready_to_appear_cb()
            self.ready_to_appear_cb = None

    def OnSceneLoadNotifyTimeout(self):
        self.scene_load_timer = None
        if self.isValid:
            self.OnSceneLoadNotify(0)

    @task_callback
    def OnSceneLoadNotify(self, progress):
        self.loading_max_progress = max(self.loading_max_progress, progress)
        print("[OnSceneLoadNotify]: progress: %s, timer_id: %s" % (progress, self.scene_load_timer))
        if progress != 0:
            return

        if MEngine.GetGameplay().Scenario.DelayDeserialization:
            real_posready = True
            root = self.world.Levels['$root']
            for ent in root.RootArea.Entities:
                if ent.Volume and ent.Volume.IsInVolume:
                    level_name = ent.Volume.LevelKey
                    level = self.world.Levels.get(level_name)
                    if level and not level.IsInWorld:
                        real_posready = False
                        break
            if not real_posready:
                return

        if self.scene_load_timer:
            Timer.cancel_timer(self.scene_load_timer)
            self.scene_load_timer = None

        MEngine.GetGameplay().Scenario.EndLoading()

        self.loading_max_progress = max(0.01, max(self.loading_max_progress, progress))
        load_pct = 1.0 - (progress * 1.0 / self.loading_max_progress)
        self.max_loading_pct = 0.7
        self.SetLoadingPct(load_pct)

        # 物理加载 https://km.netease.com/team/messiah/wiki/page/55706
        # 设置默认FocusCenter，与前面相机位置配合，保证物理的FocusArea移到目标位置
        if not GlobalData.IsSceneGame:
            self.physics_ready_timer = Timer.addTimer(30.0 * 10, self.OnPhysicsReadyTimeout).timerid
            position = MType.Vector3(*tuple(self.GetOwnerPosition()))
            self.world.PhysicsSpace.DefaultFocusCenter = position
            self.world.PhysicsSpace.UpdateFocus()
            self.world.PhysicsSpace.AddPosReadyCallback(
                position,
                self.BeginTask(self.OnPhysicsReady),
            )

        self.InitSpaceInfo()
        # 等待IWorld的ReadyToAppear
        self.StartCheckReadyToAppear()
        return True

    def OnPhysicsReadyTimeout(self):
        self.physics_ready_timer = None
        if self.isValid:
            self.OnPhysicsReady(None, True, True)

    @task_callback
    def OnPhysicsReady(self, position, ready, force=False):
        if not ready:
            return
        if not force:
            Timer.addTimer(1.0, functools.partial(self.CheckPhysicsRealPosReady, position, 0))
            return

        if self.physics_ready_timer:
            Timer.cancel_timer(self.physics_ready_timer)
            self.physics_ready_timer = None
        return ready

    def CheckPhysicsRealPosReady(self, position, check_count):
        if self.physics_ready_timer and check_count < 10:  # 还在等物理
            if self.IsPhysicsPosReady(position):
                self.OnPhysicsReady(position, True, True)
            else:
                Timer.addTimer(1.0, functools.partial(self.CheckPhysicsRealPosReady, position, check_count + 1))

    def IsPhysicsPosReady(self, pos):
        if not MEngine.GetGameplay().Scenario.DelayDeserialization:
            return self.world.PhysicsSpace.IsPosReady(pos)
        # 延迟序列化
        if not self.world.PhysicsSpace.IsPosReady(pos):
            return False
        real_posready = True
        root = self.world.Levels['$root']
        for ent in root.RootArea.Entities:
            if ent.Volume and ent.Volume.IsInVolume:
                level_name = ent.Volume.LevelKey
                level = self.world.Levels.get(level_name)
                if level and not level.IsInWorld:
                    real_posready = False
                    break
        return real_posready
    
    @task_callback
    def OnShaderReady(self, _):
        self.world.NavigateMap.BindEvent('ResourceUpdated', None)
        return True
    
    @task_callback
    def OnNavigateReady(self, _):
        self.world.NavigateMap.BindEvent('ResourceUpdated', None)
        return True

    @task_callback
    def OnCheckReadyToAppear(self, start_stamp):
        # 超时保底
        if time.time() - start_stamp > 20.0:
            # [DEBUG]
            raise Exception('OnCheckReadyToAppear TimeOut')
            # [DEBUG]
            return True

        world = self.world
        if not world.AppearableTotalCount:
            return True
        if world.IsReadyToAppear:
            return True
        for level in world.Levels.values():
            if level.IsInWorld:
                if not level.IsReadyToAppear and level.RootArea:
                    for e in level.RootArea.Entities:
                        if not e.IsReadyToAppear:
                            self.space.add_timer(0.25, functools.partial(self.OnCheckReadyToAppear, start_stamp))
                            return False
        return True

    @task_callback
    def OnLookAroundWarmingUp(self, position, yaw, times, current):
        if current > times:
            return True
        self.SetCameraTransform(position, yaw + math.pi * 2.0 / times * current)
        self.space.add_timer(0.1, functools.partial(self.OnLookAroundWarmingUp, position, yaw, times, current + 1))
        self.max_loading_pct = 0.95
        self.SetLoadingPct(0.7 + 0.2 * (1.0 * current / times))

    def SetCameraTransform(self, position, yaw):
        print('[SpaceLoader] SetCameraTransform, position: ', position, yaw)
        icamera = MEngine.GetGameplay().Player.Camera
        transform = MType.Matrix4x3()
        transform.set_pitch_yaw_roll(0.0, yaw + math.pi, 0.0)
        transform.translation = position
        icamera.Transform = transform

    def StartCheckReadyToAppear(self):
        self.BeginTask(self.OnCheckReadyToAppear)
        self.space.add_timer(0.25, functools.partial(self.OnCheckReadyToAppear, time.time()))

        if not GlobalData.IsSceneGame:
            self.BeginTask(self.OnLookAroundWarmingUp)
            camera_pos = MType.Vector3(*formula.Add3D(self.GetOwnerPosition(), (0, 1.8, 0)))
            # 432258 【G83US】【性能优化】修一波量大的脚本trace
            owner = self.owner
            yaw = 0.0 if owner.is_destroyed() else owner.area.yaw
            self.space.add_timer(0.1, functools.partial(self.OnLookAroundWarmingUp, camera_pos, yaw, 16, 1))

    def InitSpaceInfo(self):
        world = self.world
        terrain_level = world.Levels.get('00_terrain', world.Levels[cconst.DEFAULT_ROOT_LEVEL_NAME])
        if terrain_level is None or terrain_level.RootArea is None:
            return

        for cur_entity in terrain_level.RootArea.Entities:
            cur_name = cur_entity.GetName()
            if not cur_name:
                continue
            if cur_name in cconst.TERRAIN_NAME:
                self.terrain = cur_entity
                # self.terrain_height = cur_entity.Transform.translation.y
                # self.terrain.IsOverrideLodThreshold = True
                # self.terrain.LodThreshold = MType.Vector3(300, 0, 0) #MType.Vector3(*render_level.OVERRIDE_LOD_THRESHOLD[genv.render_level])
                break

    def Destroy(self):
        MEngine.GetGameplay().Scenario.EndLoading()
        self.Invalid()


@StateWrapper
class SpaceStateEmpty(object):

    def OnEnter(self):
        # space初始化清理一下cache，hud特效cache住了会残留，效果错
        MCharacter.ClearEffectCache()
        # self.HandleReplaceModelData()

    # def HandleReplaceModelData(self):
    #     #1 替换模型只有在战斗内
    #     #2 只有非dlc资源才能替换模型
    #     now = genv.GetServerNow()
    #     act_time_cache = {}
    #     reload(unit_model_data)  #　先reload 一下数据
    #     taggeddict.unlock_tagged_dict()
    #     for model_id, proto in unit_model_data.data.iteritems():
    #         activity_models = proto.get('activity_models')
    #         if activity_models:
    #             for act_id, replace_model_id in activity_models:
    #                 if act_id in act_time_cache:
    #                    is_in_limit_time = act_time_cache[act_id]
    #                 else:
    #                     act_proto = activity_data.data[act_id]
    #                     is_in_limit_time = time_util.IsInLimitTime(now, act_proto['begin'], act_proto['end'])
    #                     act_time_cache[act_id] = is_in_limit_time
    #                 if is_in_limit_time:
    #                     unit_model_data.data[model_id] = unit_model_data.data[replace_model_id]
    #     taggeddict.lock_tagged_dict()


class ScriptLodModel(object):
    def __init__(self):
        self.lod0 = None
        self.lod1 = None

    def IsReady(self):
        return self.lod0 and self.lod1

    def IsEmpty(self):
        return not self.lod0 and not self.lod1

    @property
    def Distance(self):
        if self.lod0.IsVisible:
            return self.lod0.Primitives[0].Distance
        elif self.lod1.IsVisible:
            return self.lod1.Primitives[0].Distance
        return 0.0


@StateWrapper
class SpaceStateLoading(object):
    def OnEnter(self):
        # Loading前先刷新下战斗的画面设置
        gpl.ApplyBattlePerformance()
        # Loading时开启引擎GC，清理内存
        MemoryManager.instance().engine_gc_enabled = True
        # 开启预编译
        PrecompileShaderManager.instance().PrecompileCategory(self.spaceno, self.is_high_world)
        gui.ShowLoadingScene(True, is_battle=True)

        self.script_lod_model_ticker = self.add_repeat_timer(1.0, self.TickScriptLodModel)
        self.script_lod_models = {}
        if gpl.performance_level <= 1:
            self.script_lod_model_distance = 50
        elif gpl.performance_level == 2:
            self.script_lod_model_distance = 70
        else:
            self.script_lod_model_distance = 0

        # 等当前正在跑的贴花烘培任务都执行完，再进行场景切换
        self.loading_start_checker = None
        self.DoStartLoading()
        MEngine.GetGameplay().Scenario.DeserializationTimeSlice = 1000000

    def OnExit(self):
        self.loading_start_checker and self.cancel_timer(self.loading_start_checker)
        self.loading_start_checker = None

    def DoStartLoading(self):
        # 监听场景中的ientity加载成功后的回调，s18用在 门，破碎物，玻璃 上
        # https://km.netease.com/team/messiah/wiki/page/101752
        self.script_notifier_funcs = {}
        self.tag_ientities = {}
        self.breakitems_entity = None
        self.glasses_entity = None
        self.doors_entity = None
        self.carriable_mgr = None
        self.strike_item_manager = StrikeItemManager(self)
        MObject.SetEntityCallback(self.OnSceneEntityEnterWorld, self.OnSceneEntityLeaveWorld)
        ClientSpace.load(self)  # noqa
        self.start_loading_time = time.time()

        # 大厅靶场无缝切换的特殊逻辑
        if SeamlessSpaceLoader.CanSeamlessLoad(self.spaceno):
            self.is_seamless_load = True
            self.loader = SeamlessSpaceLoader(self.spaceno, self, self.owner)
            self.loader.Load()
            return

        self.is_seamless_load = False

        self.loader = SpaceLoader(self.spaceno, self, self.owner)
        self.loader.Load()
        # lod 场景加载开始
        self.owner.SALogToServer(
            'SpaceStartLoading',
            start_loading_time=self.start_loading_time,
            is_in_background=genv.is_in_background,
            inactive_count=genv.inactive_count,
        )

        # qsec检测
        try:
            import gclient.framework.util.qsec as pyqsec
            if not hasattr(genv, "pyqsec") or not genv.pyqsec:
                genv.pyqsec = pyqsec.PyQSec()
            if genv.pyqsec:
                genv.pyqsec.Check()
        except:
            sys.excepthook(*sys.exc_info())

        # 校验weapon.py是否被篡改
        # avatar = self.owner
        # avatar and avatar.AntiCheckEssentialData("weapon")

    def RegisterScriptNotifierFunc(self, level_name, res_name, callback):
        if not level_name or not res_name or not callback:
            return
        level_funcs = self.script_notifier_funcs.setdefault(level_name, {})
        level_funcs[res_name] = callback

    def UnRegisterScriptNotifierFunc(self, level_name, res_name):
        if not level_name or not res_name:
            return
        if level_name in self.script_notifier_funcs and res_name in self.script_notifier_funcs[level_name]:
            del self.script_notifier_funcs[level_name][res_name]

    def DebugSetPvdName(self):
        classes = ('PhysicsStaticSceneBody', 'PhysicsTerrainComponent', 'RigidBodyComponent', 'PhysicsDynamicBody')
        for level in self.world.Levels.values():
            for ientity in level.RootArea.Entities:
                for b in ientity.RigidBodies:
                    if not getattr(b, "SetName", None):
                        print("unknow SetName", b)
                        continue
                    class_name = b.__class__.__name__
                    if class_name in classes:
                        owner = getattr(b, 'owner', None)
                        if owner:
                            b.SetName('%s(%s)' % (owner.__class__.__name__, owner.id))
                        else:
                            b.SetName('%s(%s)' % (class_name, ientity.GetName()))
                        continue
                    print("unknow class", class_name)
                    genv.d = ientity

        for ent in self.entities.values():
            if getattr(ent, 'model', None) \
                    and getattr(ent.model, 'model', None) \
                    and getattr(ent.model.model, 'CharCtrl', None):
                ent.model.model.CharCtrl.SetName("%s_CharCtrl(%s)" % (ent.__class__.__name__, ent.id))

    def OnSceneEntityEnterWorld(self, ientity, callback_type):
        # 回调类型=1表示是 ScriptNotifier 通知脚本, 0 表示 actorcomponent
        
        ientity_name = ientity.GetName()
        level_name = ientity.Level.GetName()
        if level_name in self.script_notifier_funcs:
            callback = self.script_notifier_funcs[level_name].get(ientity_name, None)
            if callback:
                callback(True, ientity)
                return
        if ientity.Tag:
            tag_str = ientity.Tag.TagString
            if tag_str in consts.UE_DESTRUCT_TAGS:
                self.tag_ientities[(level_name, ientity_name)] = ientity
            elif tag_str == 'POI_Text':
                self.tag_ientities[('L_POI_Name', ientity_name)] = ientity
        if "SM_GPI_" in ientity_name:  # TODO:有正式资源后走绑定流程
            if GlobalData.IsExportPhysics:
                "SM_GPI_Vehicle" in ientity_name and self.add_timer(0.1, lambda: ientity.LeaveArea())
            else:
                self.add_timer(0.1, lambda: ientity.LeaveArea())
        elif ientity_name not in self.world_ientity_dict:
            self.world_ientity_dict[ientity_name] = ientity
        if ientity_name.startswith("ScriptLodModel"):
            self.OnScriptLodModelEnterOrLeaveWorld(ientity, True)
        else:
            self.OnEntityEnterOrLeaveWorldForTrigger(ientity_name, ientity, True)

    def OnSceneEntityLeaveWorld(self, ientity, callback_type):
        ientity_name = ientity.GetName()
        level_name = ientity.Level.GetName()
        if level_name in self.script_notifier_funcs:
            callback = self.script_notifier_funcs[level_name].get(ientity_name, None)
            if callback:
                callback(False, ientity)
                return
        if ientity.Tag:
            tag_str = ientity.Tag.TagString
            if tag_str in consts.UE_DESTRUCT_TAGS:
                self.tag_ientities.pop((level_name, ientity_name), None)
            elif tag_str == 'POI_Text':
                self.tag_ientities.pop(('L_POI_Name', ientity_name), None)
        if ientity_name in self.world_ientity_dict:
            del self.world_ientity_dict[ientity_name]
        if ientity_name.startswith("ScriptLodModel"):
            self.OnScriptLodModelEnterOrLeaveWorld(ientity, False)
        else:
            self.OnEntityEnterOrLeaveWorldForTrigger(ientity_name, ientity, False)

    def OnScriptLodModelEnterOrLeaveWorld(self, ientity, is_enter):
        models = self.script_lod_models
        name = ientity.GetName()
        is_lod1 = name.endswith("_LOD1")
        key = name[:-5] if is_lod1 else name

        if key not in models:
            models[key] = ScriptLodModel()

        if is_lod1:
            models[key].lod1 = ientity if is_enter else None
        else:
            models[key].lod0 = ientity if is_enter else None

        if models[key].IsEmpty():
            models.pop(key, None)

    def TickScriptLodModel(self):
        player = replay_util.GetPlayer()
        is_on_sky = getattr(player, "is_on_sky", True)

        threshold = self.script_lod_model_distance
        for key, model in self.script_lod_models.items():
            if not model.IsReady():
                continue
            lod0 = model.lod0
            lod1 = model.lod1
            if not is_on_sky and model.Distance > threshold > 0:
                lod0.IsVisible = False
                lod1.IsVisible = True
            else:
                lod0.IsVisible = True
                lod1.IsVisible = False


@StateWrapper
class SpaceStateLoaded(object):
    def OnEnter(self, reuse=False):
        CombatItemModelManager.instance().Clear()
        self.combat_item_octree.Clear()

        self.loaded_ticket = IdManager.genid()
        self.loaded_timer = None
        self.world = self.loader.world
        self.FindAndInitTerrain()
        gpl.RefreshTerrainGeometryFactor()
        self.TerrainMaxLoadingDistance = -1
        self.TerrainLodThreshold = MType.Vector3(100, 150, 500)
        gpl.OnSpaceInit()
        s = MEngine.GetGameplay().Scenario.Sound
        s.SetCurrentLanguage('Chinese')
        # 目前PC都是用调试画质，可以删除此项
        # is_newpc_map = genv.space.proto.get("is_messiah2024_map", False)
        if not reuse:
            # Loading结束后刷一下配置，比如低端机隐藏草
            # gpl.ApplyBattleSpaceRenderOptionsAfterLoaded()
            genv.sound_mgr.LoadBattleBnk()    # 预加载音效
            PreloadGraph.instance().Preload()  # warmup graph # 大约增加内存23M
        # else:
        #     gpl.ApplyBattleSpaceRenderOptions()     # 结算看精彩时刻，重设renderoption
        AntiPlugDebugDrawer.instance().ClearAll()
        processor = effect_util.HitEffectProcessor.instance()
        processor.hit_effect_records.clear()
        processor.last_hit_effect_info.clear()
        genv.is_high_optic_ads = False
        MEngine.GetGameplay().Scenario.DeserializationTimeSlice = 3.0
        self.CheckAndDoLoaded()

        # [DEBUG]
        if GlobalData.IsSceneEditorGame:
            self.world.PhysicsSpace.Simulate()
        # [DEBUG]

        # 预加载动画
        AnimPreloadManager().ProcessInGameAnimWarmup()
        AnimPreloadManager().UpdatePreparedGraphCacheStates()
        BulletMgr.instance().PreloadBullet()

    def CheckAndDoLoaded(self):
        owner = self.owner
        print("CheckAndDoLoaded", owner, owner.bind_entity)
        if not owner or not owner.bind_entity:
            self.add_timer(1.0, self.CheckAndDoLoaded)
            return

        if owner.is_replaying and owner.replay_context.is_watching:
            self.ClearEntityMessage()
            owner.CallServer("ReplayOnSpaceReady")
        if GlobalData.Offline:
            self.LoadedShowCachedEntities(self.loaded_ticket)
        else:
            self.LoadedCheckPivotalEntities(self.loaded_ticket)
        # log 场景加载完成
        if self.start_loading_time:
            end_time = time.time()
            self.owner.SALogToServer(
                'SpaceLoadingTime',
                start_loading_time=self.start_loading_time,
                end_loading_time=end_time,
                use_time=end_time - self.start_loading_time,
                inactive_count=genv.inactive_count,
                is_in_background=genv.is_in_background,
                dlc_maps=genv.dlc_manager.total_dlc_maps,
            )
            self.loading_time = end_time - self.start_loading_time

        self.preload_pivot_clear_timer and self.cancel_timer(self.preload_pivot_clear_timer)
        self.preload_pivot_clear_timer = self.add_repeat_timer(2.0, self.TickPreloadPivotsAndDistance)

    def OnExit(self):
        self.loaded_ticket = ""
        if self.loaded_timer:
            self.cancel_timer(self.loaded_timer)
            self.loaded_timer = None

    def FindAndInitTerrain(self):
        world = self.world
        terrain_level = world.Levels.get('00_terrain', world.Levels[cconst.DEFAULT_ROOT_LEVEL_NAME])
        if terrain_level is None or terrain_level.RootArea is None:
            return

        for cur_entity in terrain_level.RootArea.Entities:
            cur_name = cur_entity.GetName()
            if not cur_name:
                continue
            if cur_name in cconst.TERRAIN_NAME:
                self.terrain_inner = cur_entity
                break

    def LoadedCheckPivotalEntities(self, ticket):
        if self.loaded_ticket != ticket:
            return
        self.loaded_timer = None

        owner = self.owner
        if owner.is_replaying:
            context = owner.replay_context
            if context.is_playback or not context.is_fps_mode or context.is_gm_playback:
                return self.LoadedShowCachedEntities(ticket)
            combat_avatar_id = context.target.combat_avatar_id
            game_logic_id = context.target.game_logic_id
            combat_team_id = context.target.combat_team_id
        else:
            game_logic_id = owner.game_logic_info.id
            combat_avatar_id = owner.combat_avatar_info.id
            combat_team_id = owner.combat_team_info.id

        cached_entities = dict(owner.get_cached_entities())
        self.logger.info("[LoadedCheckPivotalEntities]: %s %s %s inspace: %s, cached_entities: %s", combat_avatar_id, game_logic_id, combat_team_id, owner.area.inspace, cached_entities)


        # 检查CombatAvatar以及GameLogic是否都在自己的AOI范围内
        # 都满足条件才会进行LoadedShowCachedEntities操作
        if cached_entities and game_logic_id in cached_entities \
                and (not combat_avatar_id or combat_avatar_id in cached_entities) \
                and (not combat_team_id or combat_team_id in cached_entities):
            self.loaded_timer = self.add_timer(0.0, lambda: self.LoadedShowCachedEntities(ticket))
            return

        self.loaded_timer = self.add_timer(0.25, lambda: self.LoadedCheckPivotalEntities(ticket))

    def LoadedNotifyFinishingLoading(self):
        if self.state != SpaceStateLoaded.NAME:
            return

        if self.game_logic and self.game_logic.IsFinishLoading():
            finish = True
        else:
            finish = False

        if len(genv.space.wait_entities) > 0:
            finish = False

        if finish:
            # 死亡回放/精彩时刻的黑幕，消失
            self.add_timer(1.0, lambda: genv.ScreenFadeOut(0.25))
            self.loader.ForceFinishLoading()
        else:
            self.add_timer(1.0, self.LoadedNotifyFinishingLoading)

    def ApplyMessage(self):
        # 等所有Entity创建好之后，设置标志位，并且应用所有AOI消息
        for entity in list(self.entities.values()):
            try:
                if not entity.is_destroyed():
                    entity.area.apply_cached_area_message()
            except:
                sys.excepthook(*sys.exc_info())

    def _ClearWaitEntities(self):
        genv.space.wait_entities.clear()

    def LoadedShowCachedEntities(self, ticket):  # noqa
        print("LoadedShowCachedEntities", self.loaded_ticket, ticket)
        if self.loaded_ticket != ticket:
            return

        interval = 0.5 if self.is_seamless_load else 2.0
        self.add_timer(interval, self.LoadedNotifyFinishingLoading)
        # [DEBUG]
        if genv.is_jarvis_started:
            self.LoadedNotifyFinishingLoading()
        # [DEBUG]

        # 场景加载完，停止预编译，该显示的物体都已经进入引擎的加载队列了，此时再预编译已经没太大意义，而且可能会造成卡顿
        PrecompileShaderManager.instance().PrecompilePause()

        # 清一下前一局残留的特效
        MCharacter.ClearAllWorldEffects()

        # 初始化默认不用SceneCopy
        MRender.WriteOnBlackBoard("EnableHudSceneCopy", False)
        MRender.WriteOnBlackBoard("EnableHudPostTSAA", False)

        self.loaded_timer = None
        owner = self.owner

        # 死亡回放/精彩时刻的黑幕，消失
        # self.add_timer(1.0, lambda: genv.ScreenFadeOut(0.25))

        # 设置cached_entity_priority，为了保证PlayerCombatAvatar和GameLogic创建的先后顺序
        owner.set_cached_entity_priority({
            owner.combat_team_info.name: 101,
            "Player" + owner.combat_avatar_info.name: 100,
            owner.game_logic_info.name: 99,
            owner.combat_avatar_info.name: 97,
        })

        if self.is_replaying:
            if owner.replay_context.is_watching:
                owner.ReplayPrepareStartWatch()

        # HARDCODE: 相机在哪创建比较好？
        self.camera = Camera(self)
        self.camera.Attach()
        camera_position, camera_yaw = owner.camera_init_pose
        self.camera.ResetTransform(0, camera_yaw, 0, formula.Add3D(camera_position, (0, 1.5, 0)))

        try:
            # 为了修正DuplicatedEntity，Entity每次创建都会让引用计数+1
            genv.entity_create_counter_enabled = True
            genv.entity_create_counter.clear()
            self.area_hooks = []

            # 走引擎的on_enter_space逻辑，会创建网络同步下来的缓存Entity
            owner.EnterSpaceNew(self)

            genv.camera and genv.camera.EnsureEnterArea()

            self.ApplyMessage()
        finally:
            genv.entity_create_counter.clear()
            genv.entity_create_counter_enabled = False

        # 客户端本地创建的Entity，再ShowModel
        entity_creating_cache = self.entity_creating_cache
        del self.entity_creating_cache
        for cached in entity_creating_cache:
            try:
                self.create_entity(*cached)
            except:
                sys.excepthook(*sys.exc_info())

        # PlayerAvatar.ShowModel，目前是空的不做任何事情
        self.entity_show_model(owner)

        self.add_timer(20.0, self._ClearWaitEntities)
        # 服务器同步下来的Entity，先ShowModel
        for entity in list(self.entities.values()):
            try:
                if entity is not owner:
                    if entity == genv.player:
                        genv.space.wait_entities.add(entity)
                    self.entity_show_model(entity)
            except:
                sys.excepthook(*sys.exc_info())

        if self.is_replaying:
            if owner.replay_context.is_watching:
                owner.ReplayDoStartWatch()

        # 回放录像模式下，则默认切换至自由相机
        if self.is_replaying:
            context = owner.replay_context
            position = tuple(context.target.position)
            position = position or owner.position
            # self.camera.ConfigFreeviewPlacer()
            # self.camera.placer.ResetTransform(
            #   MType.Vector3(*position), MType.Vector3(*formula.YawToVector(context.target.yaw + math.pi)))
            self.create_entity("ReplayDummy", None, {"position": position})
            if context.is_playback or context.is_gm_playback:
                owner.ReplayDoStartPlayback(owner.replay_record_data)
            self.replay_game_logic_id = owner.replay_context.game_logic_id
            self.replay_kind = context.kind

        # game_logic对象开始接管游戏逻辑
        if self.game_logic:
            try:
                self.game_logic.InitGameLogicUI()   # 可以根据情况决定是真的init还是refresh
                self.game_logic.OnWorldLoaded()
            except:
                sys.excepthook(*sys.exc_info())

        self._callComponents('on_world_loaded')

        # 为了低端机性能，隐藏部分level
        if gpl.performance_level <= EPerformanceLevel.LEVEL_1:
            levels = self.world.Levels
            for name in self.proto.get("high_performance_level", ()):
                if name in levels:
                    levels[name].LeaveWorld()

        genv.player and genv.player.CallServer('ClientSpaceLoaded')
        self.RestoreWeatherStateOnLoaded()

        if genv.is_from_login:
            genv.is_from_login = False
            GeneralLog.GeneralLog().log('Load',
                account_id=genv.account_info.get('account_id', ''),
                reach_ui='combat')

        # 战斗禁用引擎GC，减少卡顿
        MemoryManager.instance().engine_gc_enabled = False
        # Loading结束后强制触发一下CSM刷新，避免实施投影闪一下出来的问题
        self.add_timer(3.0, lambda: MRender.WriteOnBlackBoard("CSMTriggerForceUpdateCounter", 3))
        self.TimerLogSomething()
        # [DEBUG]
        if not genv.is_jarvis_started:
            self.CheckPythonThreadNum()
        # [DEBUG]


@StateWrapper
class SpaceStateCached(object):
    def OnEnter(self, prepare_replaying):
        # genv.camera.SetFocusTarget(None)
        genv.input_ctrl.ControlPlayer(None)

        player = genv.player
        replay_player = genv.replay_player
        game_logic = self.game_logic
        destroyes = []
        for entity in list(self.entities.values()):
            hasattr(entity, "CacheModel") and entity.CacheModel()
            if entity is player:
                continue
            if entity is replay_player:
                continue
            if entity is game_logic:
                continue
            destroyes.append(entity)

        ModelCache.instance().SwitchMode(ModelCache.MODE_CACHE)

        for entity in destroyes:
            if not entity.is_destroyed():
                try:
                    if hasattr(entity, 'ReuseSpaceDestroy'):
                        entity.ReuseSpaceDestroy()
                    else:
                        entity.destroy()
                except:
                    sys.excepthook(*sys.exc_info())

        if prepare_replaying:
            if game_logic and not game_logic.is_destroyed():
                game_logic.destroy_for_replay()
        else:
            # replay情况下，有 FreezeCurrentStageForReply 单独处理-----其实应该在这里写game_logic的destroy的- -
            game_logic and game_logic.CacheGameLogicUI()

        try:
            # 保证其他entity在player之前销毁
            if game_logic and not game_logic.is_destroyed():
                game_logic.destroy()
            player and player.destroy()
            replay_player and replay_player.destroy()
        except:
            sys.excepthook(*sys.exc_info())

        self.OwnerLeave(False)
        self.CameraLeave(cached=True)
        self.entities.clear()
        self.owner = None
        self.game_logic = None
        # 开启引擎GC，清理内存
        MemoryManager.instance().engine_gc_enabled = True
        self.start_loading_time = None
        self._callComponents('cached')


class SpaceDestructComp(object):
    def CreateWorldDestructible(self):
        if not GlobalData.Offline:
            return
        world_name = self.world_name
        from gclient.data import world_blast_data  # noqa
        destructible_data = world_blast_data.data.get(world_name, {}).get('Destruct', {})
        if not destructible_data:
            return
        self.logger.info("[Destruct] CreateDestructible From world_blast_data %s", world_name)
        blast_data_getter, destruct_data_getter = blast_data.data.get, destruct_data.data.get
        for level_name, level_info in destructible_data.items():
            for name, val in level_info.items():
                blast_id, trans, _ = val['info']
                transform = MType.Matrix4x3(MType.Vector3(*trans))
                translation = transform.translation
                translation = (translation.x, translation.y, translation.z)
                real_blast_path = blast_data_getter(blast_id, '')
                real_blast_path and self.CreateDestructible(
                    blast_id, real_blast_path, destruct_data_getter(blast_id, {}).get('type_id', 0),
                    translation, transform.yaw, res_name=name, level_name=level_name,
                )

    def CreateDestructible(self, blast_id, real_blast_path, d_type, pos, yaw, source=1, res_name='', level_name=''):
        data = {
            "blast_id": blast_id,
            "blast_path": real_blast_path,
            "position": pos,
            "yaw": yaw,
            'source': source,
            'res_name': res_name,
            'asset_type': d_type,
            'level_name': level_name
        }
        # self.logger.info('[Destruct] Create Destructible[%s] [%s]', eid, data)
        eid = IdManager.genid()
        entity = self.create_entity("DestructibleSingle", eid, data)
        entity.source = source
        # self.destruct_entities[eid] = entity
        return entity

    def OnSpaceOnlyEntityInit(self, entity):
        if entity.IsCarriable:
            self.carriable_mgr = entity
        elif entity.IsGlass:
            self.glasses_entity = entity
        elif entity.IsDoor:
            self.doors_entity = entity
        else:
            self.breakitems_entity = entity


class PhysXComp(object):

    # debug_time = -1表示永久，debug_time = 0表示一帧
    def AllOverlapByCircle(self, pos, radius, filter_info, debug=False, debug_time=1., is_vec=False):

        shape = MObject.CreateObject('PhysicsShapeWrapper')
        shape.SetShapeToSphereImmediately(radius)
        mat = MType.Matrix4x3()
        mat.translation = pos if is_vec else MType.Vector3(*pos)
        results = self.world.PhysicsSpace.AllOverlap(shape, mat, filter_info)

        # [DEBUG]
        if debug:
            self.DrawCircleOverlapDebugInfo(mat.translation, radius, results, debug_time)
        # [DEBUG]
        return results

    # debug_time = -1表示永久，debug_time = 0表示一帧
    def AllOverlapByCapsule(self, pos, radius, height, filter_info, debug=False, debug_time=1.):
        if radius == 0:
            # 错误哈希: 4b356d94e43a7bd7d0d29816ad3d7d69
            # 外挂会走到这里来, 强行让 radius 设置为0, 所以这里如果是0就返回-1了
            return -1
        shape = MObject.CreateObject('PhysicsShapeWrapper')
        shape.SetShapeToCapsuleImmediately(radius, height * 2)
        mat = MType.Matrix4x3()
        mat.roll = 1.57
        mat.translation = MType.Vector3(pos.x, pos.y + radius + height, pos.z)
        results = self.world.PhysicsSpace.AllOverlap(shape, mat, filter_info)

        # [DEBUG]
        if debug:
            self.DrawCapsuleOverlapDebugInfo(mat, radius, height, results, debug_time)
        # [DEBUG]
        return results

    # debug_time = -1表示永久，debug_time = 0表示一帧
    def AllOverlapByHorizontalCapsule(self, pos, dir, radius, length, filter_info, debug=False, debug_time=1.):
        # 从pos位置dir朝向的长度为length的胶囊体射线检测
        shape = MObject.CreateObject('PhysicsShapeWrapper')
        shape.SetShapeToCapsuleImmediately(radius, length)
        mat = MType.Matrix4x3()
        yaw = formula.Vector3DToYaw(dir)
        roll = math.atan2(dir[1], math.sqrt(dir[0] * dir[0] + dir[-1] * dir[-1]))
        mat.roll = roll
        mat.yaw = yaw + 1.57
        pos_new = formula.horizontalRelativeOffset(pos, yaw, 0, (radius + length) / 2.0)
        mat.translation = MType.Vector3(pos_new[0], pos[1], pos_new[2])
        a = self.world.PhysicsSpace.AllOverlap(shape, mat, 19)

        # [DEBUG]
        if debug:
            self.DrawCapsuleOverlapDebugInfo(mat, radius, length / 2., a, debug_time)
        # [DEBUG]
        return a

    # debug_time = -1表示永久，debug_time = 0表示一帧
    def AllOverlapByBox(self, pos, x, y, z, filter_info, debug=False, debug_time=1., is_vec=False):
        shape = MObject.CreateObject('PhysicsShapeWrapper')
        shape.SetShapeToBoxImmediately(x, y, z)
        mat = MType.Matrix4x3()
        mat.translation = pos if is_vec else MType.Vector3(*pos)
        result = self.world.PhysicsSpace.AllOverlap(shape, mat, filter_info)
        # [DEBUG]
        if debug:
            self.DrawBoxOverlapDebugInfo(mat, x, y, z, result, debug_time)
        # [DEBUG]
        return result

    # debug_time = -1表示永久，debug_time = 0表示一帧
    def AllOverlapByBox2(self, matrix, x, y, z, filter_info, debug=False, debug_time=1.):
        shape = MObject.CreateObject('PhysicsShapeWrapper')
        shape.SetShapeToBoxImmediately(x, y, z)
        result = self.world.PhysicsSpace.AllOverlap(shape, matrix, filter_info)
        # [DEBUG]
        if debug:
            self.DrawBoxOverlapDebugInfo(matrix, x, y, z, result, debug_time)
        # [DEBUG]
        return result

    def AllOverlapByBox3(self, mat, x, y, z, filter_info, debug=False, debug_time=1.):
        shape = MObject.CreateObject('PhysicsShapeWrapper')
        shape.SetShapeToBoxImmediately(x, y, z)
        result = self.world.PhysicsSpace.AllOverlap(shape, mat, filter_info)
        # [DEBUG]
        if debug:
            self.DrawBoxOverlapDebugInfo(mat, x, y, z, result, debug_time)
        # [DEBUG]
        return result

    def GetPhysicsShapeWrapper(self):
        shape = self.physics_shape_wrap
        if not shape or not shape.IsValid():
            shape = MObject.CreateObject('PhysicsShapeWrapper')
            self.physics_shape_wrap = shape
        return shape

    def DrawRawCastDebugInfo(self, frm, to_pos, results=None, life_time=5, reset=False, color=(1, 1, 0)):
        # debug_time = -1表示永久，debug_time = 0表示一帧
        # [DEBUG]
        ids = []
        results = results or []
        debug_line = MDebug.Polyline()
        debug_line.points = [frm, to_pos]
        debug_line.color = MType.Vector3(*color)
        reset and self._debug_drawings.clear()
        self.AddDebugDrawing(debug_line, ids)
        self.DrawHitResults(results, ids)
        self.DelayClearDebugDrawings(ids, life_time)
        # [DEBUG]
        pass

    def DrawHitResults(self, results, ids):
        # [DEBUG]
        for r in results:
            if r.IsHit:
                debug_sphere = MDebug.Sphere()
                debug_sphere.position = r.Pos
                debug_sphere.radius = 0.05
                debug_sphere.color = MType.Vector3(1, 0, 0)
                self.AddDebugDrawing(debug_sphere, ids)
        # [DEBUG]
        pass

    def DrawCircleOverlapDebugInfo(self, pos, radius, results=None, life_time=1.):
        # [DEBUG]
        ids = []
        results = results or []
        yellow = MType.Vector3(1, 1, 0)
        red = MType.Vector3(1, 0, 0)
        sphere = MDebug.Sphere()
        sphere.radius = radius
        sphere.position = pos
        sphere.color = red if results else yellow
        self.AddDebugDrawing(sphere, ids)
        self.DelayClearDebugDrawings(ids, life_time)
        # [DEBUG]
        pass

    def DrawCapsuleOverlapDebugInfo(self, mat, radius, height, results=None, life_time=1.):
        # [DEBUG]
        ids = []
        results = results or []
        yellow = MType.Vector3(1, 1, 0)
        red = MType.Vector3(1, 0, 0)

        cy = MDebug.Cylinder()
        cy.radius = radius
        cy.endpoint0 = mat.transform_p(MType.Vector3(height, 0, 0))
        cy.endpoint1 = mat.transform_p(MType.Vector3(-height, 0, 0))
        cy.color = red if results else yellow
        self.AddDebugDrawing(cy, ids)

        for pos in (MType.Vector3(height, 0, 0), MType.Vector3(-height, 0, 0)):
            self.DrawCircleOverlapDebugInfo(mat.transform_p(pos), radius, results, life_time)

        self.DelayClearDebugDrawings(ids, life_time)
        # [DEBUG]
        pass

    def DrawBoxOverlapDebugInfo(self, mat, x, y, z, results=None, life_time=1.):
        # [DEBUG]
        ids = []
        results = results or []
        yellow = MType.Vector3(1, 1, 0)
        red = MType.Vector3(1, 0, 0)

        box = MDebug.Box()
        box.scale = MType.Vector3(x, y, z)
        box.position = mat.translation
        box.rotate = MType.Vector3(mat.pitch, mat.yaw, mat.roll)
        box.color = red if results else yellow
        self.AddDebugDrawing(box, ids)
        self.DelayClearDebugDrawings(ids, life_time)
        # [DEBUG]
        pass

    def AddDebugDrawing(self, drawing, ids):
        # [DEBUG]
        self._debug_drawing_count += 1
        self._debug_drawings[self._debug_drawing_count] = drawing
        ids.append(self._debug_drawing_count)
        # [DEBUG]
        pass

    @Async(TAG_SPACE)
    def DelayClearDebugDrawings(self, ids, life_time=1.):
        # 其实yield 0.01并不是真正的一帧，要改成AsyncTick才是一帧
        # [DEBUG]
        if life_time == 0:
            life_time = 0.01
        if life_time > 0:
            yield life_time
            for tmp_id in ids:
                tmp_id in self._debug_drawings and self._debug_drawings.pop(tmp_id)
        # [DEBUG]
        pass


    def EntitiesInRange(self, position, radius):
        result = []
        for charctrl in self.world.PhysicsSpace.GetCharctrlInRange(MType.Vector3(*position), radius, -1):
            entity = self.entities.get(charctrl.ownerid, None)
            entity and result.append(entity)
        return result

    def ClosestSweepWithBox(self, box_shape, frm, dist, to_dir, filter_info, with_trigger=False, to_pos=None, local_trans=None):
        if not self.world:
            return
        if not to_pos:
            to_pos = frm + to_dir * dist
        test_length = to_pos - frm
        test_length = test_length.length
        if test_length < 0.1:
            return
        # SetShapeToBoxImmediately(float length, float height, float width) 宽高长
        mat = MType.Matrix4x3()
        mat.translation = frm
        sweepShape = MObject.CreateObject('PhysicsShapeWrapper')
        sweepShape.SetShapeToBoxImmediately(*box_shape)
        if local_trans:
            trans = MType.Matrix4x3()
            trans.yaw = local_trans.yaw
            sweepShape.SetShapeLocalTransform(trans)
        endVec = to_pos
        if not with_trigger:
            r = self.world.PhysicsSpace.ClosestSweepWithoutTrigger(sweepShape, mat, endVec, filter_info)
        else:
            r = self.world.PhysicsSpace.ClosestSweep(sweepShape, mat, endVec, filter_info)
        return r

    def ClosestSweepWithSphere(self,
                               radius,
                               frm_pos,
                               to_pos,
                               filter_info,
                               with_trigger=True,
                               ):
        sweepShape = self.GetPhysicsShapeWrapper()
        sweepShape.SetShapeToSphereImmediately(radius)
        mat = MType.Matrix4x3()
        mat.translation = frm_pos
        if with_trigger:
            r = self.world.PhysicsSpace.ClosestSweep(sweepShape, mat, to_pos, filter_info)
        else:
            r = self.world.PhysicsSpace.ClosestSweepWithoutTrigger(sweepShape, mat, to_pos, filter_info)
        return r

    def ClosestSweepWithGivenShape(self, sweep_shape, from_trans, to_pos, filter_info, with_trigger=True):
        if with_trigger:
            return self.world.PhysicsSpace.ClosestSweep(sweep_shape, from_trans, to_pos, filter_info)
        else:
            return self.world.PhysicsSpace.ClosestSweepWithoutTrigger(sweep_shape, from_trans, to_pos, filter_info)

    def ClosestSweepWithGivenShapeMask(self, sweep_shape, from_trans, to_pos, mask, with_trigger=True):
        filter_data = MPhysics.CollisionFilterData()
        filter_data.mData0 = mask
        filter_data.mData3 |= consts.PHYSICS_QUERY_USE_MASK
        if with_trigger:
            return self.world.PhysicsSpace.ClosestSweepFD(sweep_shape, from_trans, to_pos, filter_data)
        else:
            return self.world.PhysicsSpace.ClosestSweepWithoutTriggerFD(sweep_shape, from_trans, to_pos, filter_data)

    def AllSweepWithGivenShape(self, sweep_shape, from_trans, to_pos, filter_info, with_trigger=True):
        if with_trigger:
            return self.world.PhysicsSpace.AllSweep(sweep_shape, from_trans, to_pos, filter_info)
        else:
            return self.world.PhysicsSpace.AllSweepWithoutTrigger(sweep_shape, from_trans, to_pos, filter_info)

    def AllSweepWithGivenShapeMask(self, sweep_shape, from_trans, to_pos, mask, with_trigger=True):
        filter_data = MPhysics.CollisionFilterData()
        filter_data.mData0 = mask
        filter_data.mData3 |= consts.PHYSICS_QUERY_USE_MASK
        if with_trigger:
            return self.world.PhysicsSpace.AllSweep(sweep_shape, from_trans, to_pos, filter_data)
        else:
            return self.world.PhysicsSpace.AllSweepWithoutTrigger(sweep_shape, from_trans, to_pos, filter_data)

    def AllSweepWithBox(self, box_shape, frm, dist, to_dir, filter_info, with_trigger=False, to_pos=None, local_trans=None):
        if not self.world:
            return
        if not to_pos:
            to_pos = frm + to_dir * dist
        test_length = to_pos - frm
        test_length = test_length.length
        if test_length < 0.1:
            return
        # SetShapeToBoxImmediately(float length, float height, float width) 宽高长
        mat = MType.Matrix4x3()
        mat.translation = frm
        sweepShape = MObject.CreateObject('PhysicsShapeWrapper')
        sweepShape.SetShapeToBoxImmediately(*box_shape)
        if local_trans:
            trans = MType.Matrix4x3()
            trans.yaw = local_trans.yaw
            sweepShape.SetShapeLocalTransform(trans)
        endVec = to_pos
        if not with_trigger:
            r = self.world.PhysicsSpace.AllSweepWithoutTrigger(sweepShape, mat, endVec, filter_info)
        else:
            r = self.world.PhysicsSpace.AllSweep(sweepShape, mat, endVec, filter_info)
        return r

    def RaycastPosition(self,
                        x,
                        y,
                        dist=100,
                        force_pos=False,
                        return_hit_info=False,
                        filter_info=consts.PHYSICS_COMMON_OBSTACLE,
                        debug=False,
                        debug_time=5.0):
        # force_pos 为True，表示必须返回一个位置
        # trace屏幕上的一个点到世界上的位置，并会触发点击事件
        # self.logger.info("RaycastPosition %s %s" % (x, y))
        camera = MEngine.GetGameplay().Player.Camera
        frm = camera.GetOrigin()
        to = camera.GetRayDirectionFromScreenPoint(x, y)
        # self.logger.info("RaycastPosition Start")
        r = self.RawRaycast(frm, dist, filter_info, False, to_dir=to, debug=debug, debug_time=debug_time)
        # self.logger.info("RaycastPosition End")
        if r is None or not r.IsHit:
            if force_pos:
                return (frm + to * dist)
            return None
        if return_hit_info:
            return r
        else:
            return r.Pos

    def RawRaycastP(self, p):
        # p = genv.player.position
        # p = (1716, 0, 816)
        frm = (p[0], 1000, p[-1])
        to = (p[0], -10000, p[-1])
        import MType
        return self.RawRaycast(frm=MType.Vector3(*frm), dist=0, filter_info=2, to_pos=MType.Vector3(*to))

    def RawRaycast(self, frm, dist, filter_info, with_trigger=True, to_pos=None, to_dir=None, debug=False, debug_time=5):
        if not self.world:
            return
        if not to_pos:
            to_pos = frm + to_dir * dist
        test_length = to_pos - frm
        test_length = test_length.length
        if test_length < 0.1:
            return

        if with_trigger:
            r = self.world.PhysicsSpace.ClosestRaycast(frm, to_pos, filter_info)
        else:
            r = self.world.PhysicsSpace.ClosestRaycastWithoutTrigger(frm, to_pos, filter_info)

        # [DEBUG]
        if debug:
            results = [r]
            self.DrawRawCastDebugInfo(frm,
                                      to_pos,
                                      results,
                                      life_time=debug_time)
        # [DEBUG]
        return r

    def RawRaycastMask(self, frm, dist, mask, with_trigger=True, to_pos=None, to_dir=None):
        if not self.world:
            return
        if not to_pos:
            to_pos = frm + to_dir * dist
        test_length = to_pos - frm
        test_length = test_length.length
        if test_length < 0.1:
            return
        filter_info = MPhysics.CollisionFilterData()
        filter_info.mData0 = mask
        filter_info.mData3 |= consts.PHYSICS_QUERY_USE_MASK
        if with_trigger:
            r = self.world.PhysicsSpace.ClosestRaycastFD(frm, to_pos, filter_info)
        else:
            r = self.world.PhysicsSpace.ClosestRaycastWithoutTriggerFD(frm, to_pos, filter_info)
        return r

    def ClosestRaycast(self, frm, to_pos, filter_info, with_trigger=True):
        if not self.world:
            return
        # 两个位置一样，会刷trace Calling method <ClosestRaycastWithoutTrigger> with invalid parameters.
        if (frm.x == to_pos.x) and (frm.y == to_pos.y) and (frm.z == to_pos.z):
            return
        if with_trigger:
            r = self.world.PhysicsSpace.ClosestRaycast(frm, to_pos, filter_info)
        else:
            r = self.world.PhysicsSpace.ClosestRaycastWithoutTrigger(frm, to_pos, filter_info)
        return r

    def ClosestRaycastMask(self, frm, to_pos, mask, with_trigger=True):
        if not self.world:
            return
        # 两个位置一样，会刷trace Calling method <ClosestRaycastWithoutTrigger> with invalid parameters.
        if (frm.x == to_pos.x) and (frm.y == to_pos.y) and (frm.z == to_pos.z):
            return
        filter_info = MPhysics.CollisionFilterData()
        filter_info.mData0 = mask
        filter_info.mData3 |= consts.PHYSICS_QUERY_USE_MASK

        if with_trigger:
            r = self.world.PhysicsSpace.ClosestRaycastFD(frm, to_pos, filter_info)
        else:
            r = self.world.PhysicsSpace.ClosestRaycastWithoutTriggerFD(frm, to_pos, filter_info)
        return r

    def AllRaycast(self, frm, dist, filter_info, to_pos=None, to_dir=None):
        if not self.world:
            return []
        if not to_pos:
            to_pos = frm + to_dir * dist
        if (to_pos - frm).length < 0.1:
            return []
        r = self.world.PhysicsSpace.AllRaycast(frm, to_pos, filter_info)
        return r

    def AllRaycastMask(self, frm, to, mask):
        world = self.world
        if not world or (to - frm).length < 0.1:
            return
        filter_data = MPhysics.CollisionFilterData()
        filter_data.mData0 = mask
        filter_data.mData3 |= consts.PHYSICS_QUERY_USE_MASK
        return world.PhysicsSpace.AllRaycastFD(frm, to, filter_data)

    def AllRaycastMaskCustom(self, frm, to, mask, use_regular=True, use_complex=False):
        world = self.world
        if not world or (to - frm).length < 0.1:
            return
        filter_data = MPhysics.CollisionFilterData()
        filter_data.mData0 = mask
        filter_data.mData3 |= consts.PHYSICS_QUERY_USE_MASK
        if use_complex:
            filter_data.mData3 |= consts.PHYSICS_QUERY_COMPLEX_MASK
        if use_regular:
            filter_data.mData3 |= consts.PHYSICS_QUERY_REGULAR_MASK
        return world.PhysicsSpace.AllRaycastFD(frm, to, filter_data)

    def ClosestRaycastBone(self, frm, to, exclude_skeleton=None, exclude_ragdoll=True):
        if not exclude_skeleton:
            exclude_skeleton = []
        r = MCharacter.RayCastCollisionBoneExclude(frm, to, exclude_skeleton)
        while r and r.actor:
            r_body = r.actor
            if exclude_ragdoll and r_body.__class__.__name__ == 'RagdollComponent':
                exclude_skeleton.append(r_body)
                r = MCharacter.RayCastCollisionBoneExclude(frm, to, exclude_skeleton)
                continue
            r.Normal = r.hitNormal
            r.Body = r_body
            r.MaterialTypeId = 1001
            r.IsHit = True
            r.Pos = r.hitPos
            return r
        return None

    def SphereSweepCollisionBone(self, frm, to_pos, radius, exclude_skeleton=None):
        if not exclude_skeleton:
            exclude_skeleton = []

        # 1. 检测与静态物体的碰撞
        static_hit = self.ClosestSweepWithSphere(
            radius=radius,
            frm_pos=frm,
            to_pos=to_pos,
            filter_info=cconst.PHYSICS_BULLET,  # 使用子弹的物理过滤器
            with_trigger=False  # 不检测触发器
        )

        # 2. 检测与角色骨骼的碰撞
        bone_hit = MCharacter.SphereSweepCollisionBoneExclude(frm, to_pos, radius, exclude_skeleton)

        # 3. 计算静态碰撞距离，确保使用精确的距离计算
        static_dist = float('inf')
        if static_hit and static_hit.IsHit and static_hit.Body:  # 添加额外的检查
            hit_pos = static_hit.Pos
            static_dist = (hit_pos - frm).length  # 使用向量长度计算

        # 4. 计算骨骼碰撞距离
        bone_dist = float('inf')
        if bone_hit and bone_hit.actor and hasattr(bone_hit, 'hitPos'):  # 添加额外的检查
            hit_pos = MType.Vector3(*bone_hit.hitPos) if isinstance(bone_hit.hitPos, (tuple, list)) else bone_hit.hitPos
            bone_dist = (hit_pos - frm).length  # 使用向量长度计算

        # 5. 创建结果对象
        result = type('HitResult', (), {})()

        # 设置基础属性
        result.IsHit = False
        result.distance = float('inf')

        # 6. 根据最近的碰撞设置结果
        if static_dist < bone_dist and static_hit and static_hit.IsHit and static_hit.Body:
            # 静态物体碰撞
            result.IsHit = True
            result.distance = static_dist
            result.MaterialTypeId = 1  # 静态物体材质
            result.Body = static_hit.Body
            result.actor = static_hit.Body
            result.Pos = static_hit.Pos
            result.Normal = static_hit.Normal
            result.hitPos = static_hit.Pos
            result.hitNormal = static_hit.Normal
            result.position = static_hit.Pos
            result.normal = static_hit.Normal
            result.entity = getattr(static_hit.Body, 'Entity', None)

            # 复制其他可能有用的属性
            for attr in ['Distance', 'Direction', 'Shape']:
                if hasattr(static_hit, attr):
                    setattr(result, attr, getattr(static_hit, attr))

        elif bone_dist < float('inf') and bone_hit and bone_hit.actor:
            # 骨骼碰撞
            result.IsHit = True
            result.distance = bone_dist
            result.MaterialTypeId = 1001  # 人体材质
            result.Body = bone_hit.actor
            result.actor = bone_hit.actor
            result.Pos = bone_hit.hitPos
            result.Normal = bone_hit.hitNormal
            result.hitPos = bone_hit.hitPos
            result.hitNormal = bone_hit.hitNormal
            result.position = bone_hit.hitPos
            result.normal = bone_hit.hitNormal
            result.entity = getattr(bone_hit.actor, 'Entity', None)
        else:
            # 没有碰撞
            result.IsHit = False
            result.MaterialTypeId = 0  # 空气材质
            result.Body = None
            result.actor = None
            result.Pos = to_pos
            result.Normal = MType.Vector3(0, 0, 0)
            result.hitPos = to_pos
            result.hitNormal = MType.Vector3(0, 0, 0)
            result.position = to_pos
            result.normal = MType.Vector3(0, 0, 0)
            result.entity = None
            result.distance = (to_pos - frm).length

        return result

    def RaycastBoneWithPenetrate(self, frm, to_pos, exclude_skeleton=None, exclude_ragdoll=True):
        if not exclude_skeleton:
            exclude_skeleton = []
        r = MCharacter.RayCastCollisionBoneExclude(frm, to_pos, exclude_skeleton)
        result = []
        while r and r.actor:
            r_body = r.actor
            exclude_skeleton.append(r_body)
            if not exclude_ragdoll or r_body.__class__.__name__ != 'RagdollComponent':
                r.Normal = r.hitNormal
                r.Body = r_body
                owner = getattr(r_body, 'owner', None) or getattr(r_body.Parent, 'owner', None)
                r.MaterialTypeId = owner.GetMaterialTypeIdByBoneName(r.name)
                r.IsHit = True
                r.Pos = r.hitPos
                result.append(r)

            r = MCharacter.RayCastCollisionBoneExclude(frm, to_pos, exclude_skeleton)
        return result

    def RaycastBoneWithPenetrateBothway(self, frm, to_pos, exclude_skeleton=None):
        # FIXME: 重做
        origin_exclude_skeleton = list(exclude_skeleton) if exclude_skeleton else []
        r1 = self.RaycastBoneWithPenetrate(frm, to_pos, exclude_skeleton)
        exclude_skeleton = origin_exclude_skeleton
        r2 = self.RaycastBoneWithPenetrate(to_pos, frm, exclude_skeleton)
        r = r1 + r2
        return r

    def RaycastWithPenetrate(self, frm, dist, filter_info, with_trigger=True, to_pos=None, to_dir=None, penetrate_info=None):
        # FIXME: 这个接口有点憨憨，如果你想忽视某个层，请去使用mask,即FilterData
        if not self.world:
            return
        if not to_pos:
            to_pos = frm + to_dir * dist
        test_length = to_pos - frm
        test_length = test_length.length
        if test_length < 0.1:
            return

        if with_trigger:
            r = self.world.PhysicsSpace.ClosestRaycast(frm, to_pos, filter_info)
        else:
            r = self.world.PhysicsSpace.ClosestRaycastWithoutTrigger(frm, to_pos, filter_info)
        results = [r]
        if penetrate_info:
            while r and r.IsHit and r.Body and r.Body.GetCollisionFilterInfo() == penetrate_info:
                hit_pos = r.Pos
                new_dir = hit_pos - frm
                dist = new_dir.length + 0.05
                if dist >= test_length:
                    break
                new_frm = frm + to_dir * dist
                if with_trigger:
                    r = self.world.PhysicsSpace.ClosestRaycast(new_frm, to_pos, filter_info)
                else:
                    r = self.world.PhysicsSpace.ClosestRaycastWithoutTrigger(new_frm, to_pos, filter_info)
                results.append(r)
        # 返回穿透的list和最后击中的result
        r = results[-1]
        if r and r.IsHit and r.Body and r.Body.GetCollisionFilterInfo() == penetrate_info:
            r = None
        else:
            r = results.pop(-1)
        return results, r

    def IsConnectPoint(self, pos1, pos2, filter_info):  # 判断两个点是否联通: 主要用来判断一个点是不是在物理内部
        AllRaycast = self.world.PhysicsSpace.AllRaycast
        r1 = AllRaycast(pos1, pos2, filter_info)
        r2 = AllRaycast(pos2, pos1, filter_info)
        dir = (pos2 - pos1)
        enter = 0
        for r in r1 + r2:
            if r.Normal.dot(dir) <= 0:
                enter += 1
            else:
                enter -= 1
        return enter == 0

    def QueryScreenPointLandTransform(self, dist=10, offset_up=0.1):
        screen_point = gui.device_screen_center
        camera = MEngine.GetGameplay().Player.Camera
        start_pos = camera.GetOrigin()
        dir = camera.GetRayDirectionFromScreenPoint(int(screen_point[0]), int(screen_point[1]))
        to_pos = start_pos + dir * dist
        r = self.world.PhysicsSpace.ClosestRaycastWithoutTrigger(start_pos, to_pos, cconst.PHYSICS_SHOOT_TEST)
        if r and r.IsHit:
            pos, normal = r.Pos, r.Normal
            return self.GetLandTransform(pos, normal, offset_up)

    def GetLandTransform(self, pos, normal, offset_up):
        z_axis = MType.Vector3(0, 0, 1)
        y_axis = normal
        x_axis = z_axis.cross(y_axis)
        x_axis.length = 1
        z_axis = x_axis.cross(y_axis)
        m = MType.Matrix4x3()
        m.translation = pos + normal * offset_up
        m.x_axis, m.y_axis, m.z_axis = x_axis, y_axis, z_axis
        return m

    def DrawRay(self, start_pos, end_pos, reset=True, color=(1, 1, 1)):
        if not switches.DRAW_RAY:
            pass
        if reset or not hasattr(self, 'draw_ray'):
            self.draw_ray = []
        if isinstance(start_pos, tuple) or isinstance(start_pos, list):
            start_pos = MType.Vector3(*start_pos)
        if isinstance(end_pos, tuple) or isinstance(end_pos, list):
            end_pos = MType.Vector3(*end_pos)
        try:
            import MDebug  # noqa
        except:
            import traceback
            traceback.print_exc()
            return
        m = MType.Matrix4x3()
        m.yaw = 0
        polyline = MDebug.Polyline()
        polyline.color = MType.Vector3(*color)
        polyline.points = [start_pos, end_pos]
        self.draw_ray.append(polyline)

    def QueryLandPosition(self, pos, physics=cconst.PHYSICS_DROP_ITEM, offset_up=1.5, offset_down=-200, collisions=None):
        cache_key = (pos, physics, offset_up, offset_down, collisions)
        self.query_land_cache_total += 1
        if cache_key in self.query_land_cache:
            self.query_land_cache_hits += 1
            return self.query_land_cache[cache_key]

        result = self.DoQueryLandPosition(*cache_key)
        self.query_land_cache[cache_key] = result
        return result

    def DoQueryLandPosition(self, pos, physics, offset_up, offset_down, collisions):
        # 如果远离playerAvatar（大概150米），超出物理世界，没有结果
        if not getattr(self.world, 'PhysicsSpace', None):
            return None
        phy_space = self.world.PhysicsSpace
        min_y, max_y = -500, 500
        if len(pos) == 3:
            max_y = pos[1] + offset_up
            min_y = pos[1] + offset_down

        # 使用射线检测
        pos1 = MType.Vector3(pos[0], max_y, pos[-1])
        pos2 = MType.Vector3(pos[0], min_y, pos[-1])

        # 只用regular速度可以快一点
        filter_data = MPhysics.CollisionFilterData()
        filter_data.mData0 = physics
        filter_data.mData3 |= consts.PHYSICS_QUERY_REGULAR_MASK
        r = phy_space.ClosestRaycastWithoutTriggerFD(pos1, pos2, filter_data)
        if collisions:  # 指定碰撞物理
            if r and r.IsHit and r.Body.GetCollisionFilterInfo() in collisions:
                return formula.Tuple(r.Pos)
            else:
                return None

        if r and r.IsHit:
            ret = r.Pos
            return formula.Tuple(ret)

        # 使用navi map
        r = phy_space.QueryGroundPos(pos1, physics, 1)
        if r.Status == 0:
            return (pos[0], r.GroundPos.y, pos[-1])

        return None

    def QueryLandName(self, pos, physics=cconst.PHYSICS_DROP_ITEM):
        # 如果远离playerAvatar（大概150米），超出物理世界，没有结果
        if not getattr(self.world, 'PhysicsSpace', None):
            return None
        phy_space = self.world.PhysicsSpace
        min_y, max_y = -500, 500
        if len(pos) == 3:
            max_y = pos[1] + 10
            min_y = pos[1] - 100
        # 使用射线检测
        pos1 = MType.Vector3(pos[0], max_y, pos[-1])
        pos2 = MType.Vector3(pos[0], min_y, pos[-1])
        r = phy_space.ClosestRaycastWithoutTrigger(pos1, pos2, physics)
        if r and r.IsHit and r.Body:
            return r.Body.Entity.GetName()
        return None

@SpaceClass
@Components(
    FiniteStateMachine,
    SpaceStateEmpty,
    SpaceStateLoading,
    SpaceStateLoaded,
    SpaceStateCached,
    SpaceWeatherComp,
    SpaceVehicleCacheComp,
    SpaceWaterTriggerComp,
    SpaceUGCComp,
    SpaceDestructComp,
    PhysXComp,
)
class Space(ClientSpace):

    def init(self, spaceno, spaceid, owner):
        super(Space, self).init(spaceno, spaceid, owner)

        self.world = None
        self.loader = None
        self.camera = None
        self.terrain_inner = None
        self.game_logic = None
        self.is_relay = False
        self.uistage_type = None
        self.current_stage = None

        self.navigateMap = None
        self.is_in_hall = False
        self.is_seamless_load = False

        self.dist_aoi_guids = {}
        self.replay_game_logic_id = ""
        self.replay_kind = ireplay.ReplayKind.PLAYBACK_FPS  # 区分下是回放还是观战

        self.entity_category = defaultdict(dict)
        self.combat_avatars = self.entity_category["CombatAvatar"]
        self.dummy_entities = {}
        self.area_hooks = []

        self.physics_tickers = set()
        self.physics_tick_inloop = False
        self.physics_tick_pending = []
        self.space_tickers = set()
        self.space_tick_inloop = False
        self.space_tick_pending = []
        self.registry = {}

        self.batch_notify_sound_ticker = None
        self.batch_notify_sound_queue = defaultdict(dict)

        self.query_land_cache = {}
        self.query_land_cache_total = 0
        self.query_land_cache_hits = 0
        self.query_land_cache_ticker = self.add_repeat_timer(0.25, self.query_land_cache.clear)

        self.preload_pivot_clear_timer = None
        self.preload_pivot_clear_interval = 10.0
        self.preload_pivot_empty_stamp = 0.0
        self.world_preload_pivots = defaultdict(list)

        self.timer_log_elk = None
        self.timer_log_qsec = None
        self.timer_live_timer = None
        self.timer_check_python = None

        self.loading_time = 0.0

        self.init_from_dict({})
        self.InitData()
        self.proto = space_data.data.get(spaceno)

        world_name = self.world_name
        self.is_si_island = world_name.startswith('SI_Island')
        self.is_high_world = self.IsHighWorld(world_name)
        genv.space = self
        # 模型加载队列
        self.FSMBootstrap(SpaceStateEmpty)
        genv.ui_display_statistic_mgr.SaLogForUIStatistic()

        CombatItemModelManager.instance().Clear()
        self.combat_item_octree = CombatItemOctreeManager(size=1)
        self.combat_item_live_time = {}

        self.hit_decal_manager = HitDecalManager(self)
        self.fragment_manager = DestructFragmentManager(self)
        self.destruct_batch_cache_info = {}
        # 必须等待ShowModel的Entity
        self.wait_entities = set()
        self.physics_shape_wrap = None

        self.cur_world_sun_color_intensity_orginal = None

    def GetServerStub(self):
        return self.owner.bind_entity.server.stub

    def InitData(self):
        self._debug_drawings = {}
        self._debug_drawing_count = 0

        self.global_store_info = {}  # 一些场景中全局信息
        self.world_ientity_dict = {}  # 从 world 中获取的一些场景 ientity, ientity_name: ientity
        # self.building_data = self.LoadSpaceWorldData('building')
        # self.UnloadSpaceWorldData('building')

    def EnterUIStage(self, uistage_type, uistage):
        self.uistage_type = uistage_type
        self.current_stage = uistage
        gui.EnterUIStage(self.uistage_type, self.current_stage)

    def ExitUIStage(self):
        if self.uistage_type and self.current_stage:
            gui.ExitUIStage(self.uistage_type, self.current_stage)
        self.uistage_type = None,
        self.current_stage = None

    @property
    def terrain(self):
        if not self.terrain_inner:
            return
        if self.terrain_inner.IsValid():
            return self.terrain_inner
        self.terrain_inner = None
        self.FindAndInitTerrain()
        return self.terrain_inner

    @property
    def is_replaying(self):
        return self.owner.is_replaying

    @property
    def can_modify_billboard_distance(self):
        world_name = self.proto.get("map", "")
        return any([keyword in world_name for keyword in ("hy_valley", )])

    @property
    def world_name(self):
        return self.proto.get("map", "")

    @property
    def host_map_name(self):
        if 'map_server' in self.proto:
            return self.proto['map_server']
        return self.proto.get("map", "")

    @property
    def real_world_name(self):
        world = self.world
        return world.GetName() if world else ''

    def RecordCombatItemLiveTime(self, live_time):
        # live_time  * 10
        if live_time < 0:
            return
        t = live_time * 10.0
        index = int(t)
        if index > 600:
            index = 600
        if index >= 30:
            index = (index // 30) * 30
        self.combat_item_live_time[index] = self.combat_item_live_time.get(index, 0) + 1

    def load(self):
        try:
            self.FSMTransfer(SpaceStateLoading)
            self.SetGlobalPhysic()
        except:
            sys.excepthook(*sys.exc_info())
        return False

    def SetGlobalPhysic(self):
        import MPhysics
        pairs_config = MObject.CreateObject('VehicleSurfaceToTireFrictionPairsConfig')
        pairs_config.TireTypeCnt = 5
        pairs_config.AddPair(0, 0, 1.2)  # 外服默认值
        pairs_config.AddPair(0, 1, 3)
        pairs_config.AddPair(0, 2, 3.5)
        pairs_config.AddPair(0, 3, 4)
        pairs_config.AddPair(0, 4, 4.5)
        pairs_config.AddPair(0, 5, 5)
        MPhysics.SetGlobalVehicleSurfaceToTireFriction(pairs_config)

    def on_loaded(self):
        self.FSMTransfer(SpaceStateLoaded)
        self.batch_notify_sound_ticker = self.add_repeat_timer(1.0, self.OnBatchNotifySound)
        sound_mgr = genv.sound_mgr
        sound_mgr.SetState('Game_State', cconst.SOUND_GAME_STATE_INGAME)
        sound_mgr.LoadMedia(cconst.SOUND_INGAME_BNK_FILE)
        sound_mgr.SetState('music_theme', 'None')
        EntityCacheManager.instance().Start(self.world)
        # 一些依赖self.owner的内容，都try里面吧，断线重连的时候self.owner会为None
        if self.owner:
            self.CacheVehicleConfig()

        self.CreateWorldDestructible()
        self.LoadNaviMap()

        # 预加载选择英雄的场景
        # ChooseHeroShowRoomUtil().WarmupCharacterDisplayScene()

    def entity_show_model(self, entity):
        if hasattr(entity, "ShowModel"):
            entity.ShowModel()
            entity._callComponents("show")

    def cache(self, prepare_replaying=False):
        """ 断线后，Space进入Cache状态，缓存所有Model并销毁所有Entity
        """
        self.logger.info("cache: %s", self)
        self.FSMTransfer(SpaceStateCached, prepare_replaying)
        return True

    def reuse(self, avatar):
        """ 重新连上后，Space由Cache状态进入Reuse状态，利用缓存中的Model创建Entity，并调用Model.Refresh刷新模型状态
        """
        if self.state != SpaceStateCached.NAME:
            self.logger.info("reuse: %s %s, state not ok: %s", self, avatar, self.state)
            return False

        self.logger.info("reuse: %s %s", self, avatar)
        self.entity_creating_cache = []
        self.owner = avatar
        # 强行清除上一次可能存留的entity，会有重复创建
        self.owner.clear_on_enter_space()
        self.FSMTransfer(SpaceStateLoaded, True)
        return True

    def on_create_entity(self, entity):
        """ 因AOI在场景中创建新的对象时的回调。
        """
        if entity.id in self.entities:
            genv.entity_create_counter[entity.id] += 1
            old_entity = self.entities[entity.id]
            self.owner.SALogToServer('Debug_Duplicated_Entity', old_entity=str(old_entity), entity=str(entity), replay_kind=self.owner.replay_context.kind)
            try:
                # 尝试销毁它
                self.area_hooks.append(old_entity.area)
                old_entity.destroy()
            except:
                import sys
                sys.excepthook(*sys.exc_info())
            from common.EntityManager import EntityManager
            EntityManager.addentity(entity.id, entity, False)

        super(Space, self).on_create_entity(entity)
        self.enter(entity)

    def create_entity(self, entitytype, entityid, bdict):
        """ 客户端创建单机Entity的接口
        """
        entity = super(Space, self).create_entity(entitytype, entityid, bdict)
        if not entity:
            return
        entity.is_client_only_entity = True
        self.enter(entity)
        return entity

    def enter(self, entity):
        category = entity.__class__.__name__
        self.entity_category[category][entity.id] = entity

        if self.is_loaded():
            entity.area.apply_cached_area_message()
            self.entity_show_model(entity)
        if entity.IsGameLogic:
            self.game_logic = entity
            genv.messenger.Broadcast(events.ON_GAMELOGIC_ENTER_SPACE)
            gpl.ApplyScreenScale()

        if entity.IsCombatAvatar:
            self.combat_avatars[entity.id] = entity

        # [DEBUG]
        # 支持下其他HelenRobot可调试
        if entity and hasattr(genv, "sunshine_client"):
            genv.sunshine_client.entity_manager.AddEntity(entity.id, entity)
        # [DEBUG]

    def leave(self, entity):

        before_leave_space = getattr(entity, "do_before_leave_space", None)
        before_leave_space and before_leave_space()

        category = entity.__class__.__name__
        self.entity_category[category].pop(entity.id, None)

        if entity.IsCombatAvatar:
            self.combat_avatars.pop(entity.id, None)

        if entity.IsDummyCombatAvatar:
            self.dummy_entities.pop(entity.avatar_id, None)

        if hasattr(self, "entity_creating_cache"):
            for i, (_, eid, _) in enumerate(self.entity_creating_cache):
                if eid == entity.id:
                    self.entity_creating_cache.pop(i)
                    return
        entity = self.entities.pop(entity.id, None)
        handler = getattr(entity, "do_on_leave_space", None)
        handler and handler()

        # [DEBUG]
        # 支持下其他场景可调试HelenRobot
        if entity and entity.IsHelenAI and hasattr(genv, "sunshine_client"):
            genv.sunshine_client.entity_manager.DelEntity(entity.id, from_plugin=False)
        # [DEBUG]

    def ClearEntityMessage(self):
        if not self.owner:
            return
        self.owner.clear_on_enter_space()

    def OwnerLeave(self, is_destroy):
        bind_entity = genv.soul_avatar
        if self.owner:
            self.owner.clear_on_enter_space()
            if is_destroy:
                self.owner._space = None
                if bind_entity:
                    bind_entity._space = None

            self.owner.area.inspace = False
            self.entities.pop(self.owner.id, None)
        if bind_entity:
            bind_entity.area.inspace = False
            self.entities.pop(bind_entity.id, None)

    def CameraLeave(self, cached=False):
        if self.camera:
            try:
                self.camera.Detach(delay_leave_world=20 if cached else 0)
                self.camera = None
            except:
                sys.excepthook(*sys.exc_info())

    def RemoveDummyEntity(self, avatar_id):
        guid = self.dummy_entities.pop(avatar_id, None)
        if not guid:
            return
        entity = self.entities.get(guid)
        entity and entity.destroy()
        if hasattr(self, "entity_creating_cache"):
            for i, (_, eid, _) in enumerate(self.entity_creating_cache):
                if eid == guid:
                    self.entity_creating_cache.pop(i)
                    return

    def destroy(self):
        print("space.destroy", genv.GetAllTraceback())
        MObject.SetEntityCallback(EmptyScriptNotifyFunc, EmptyScriptNotifyFunc)

        EntityCacheManager.instance().Stop()
        gpl.OnSpaceDestroy()

        self.tag_ientities = {}
        self.strike_item_manager.Destroy()
        self.strike_item_manager = None
        self.loader.Destroy()
        self.loader = None

        # 手动清理依赖game_logic的timer，避免销毁后调用
        self.batch_notify_sound_ticker and self.cancel_timer(self.batch_notify_sound_ticker)
        self.batch_notify_sound_ticker = None

        # qsec检测
        try:
            import gclient.framework.util.qsec as pyqsec
            if genv.player:
                if not hasattr(genv, "pyqsec") or not genv.pyqsec:
                    genv.pyqsec = pyqsec.PyQSec()
                if genv.pyqsec:
                    genv.pyqsec.Check()
        except:
            sys.excepthook(*sys.exc_info())

        try:
            self.ExitUIStage()  # UI 销毁的Trace
        except:
            sys.excepthook(*sys.exc_info())

        self.registry.clear()

        player = genv.player
        game_logic = self.game_logic
        destroyes = []
        for entity in list(self.entities.values()):
            if entity is not player and entity is not game_logic and entity is not player.combat_team:
                destroyes.append(entity)

        for entity in destroyes:
            try:
                if entity and not entity.is_destroyed():
                    if entity.IsCanReuse:
                        entity.IsCanReuse = False
                    entity.destroy()
            except:
                sys.excepthook(*sys.exc_info())

        try:
            combat_team = player and player.combat_team
            player and player.destroy()
            combat_team and combat_team.destroy()
        except:
            sys.excepthook(*sys.exc_info())

        try:
            # game_logic最后销毁
            game_logic and game_logic.destroy()
            self.game_logic = None
        except:
            sys.excepthook(*sys.exc_info())

        try:
            CombatItemModelManager.instance().Clear()
            self.combat_item_octree.Clear()
            self.hit_decal_manager.Clear()
            self.fragment_manager.Destroy()
            self.fragment_manager = None
            BulletMgr.instance().ClearBulletPool()
            AnimPreloadManager().ClearPreparedGraphCache()
        except:
            sys.excepthook(*sys.exc_info())

        try:
            self.OwnerLeave(True)
            self.CameraLeave()
        except:
            sys.excepthook(*sys.exc_info())

        super(ClientSpace, self).destroy()
        ClientSpace._instance = None

        genv.space = None

    def OnAvatarModelLoaded(self):
        pass

    def AddPhysicsPostTick(self, func):
        if self.physics_tick_inloop:
            self.physics_tick_pending.append((True, func))
        else:
            if not self.physics_tickers:
                self.world.PhysicsSpace.BindEvent("PhysicsPostTick", self.OnPhysicsPostTick)
            self.physics_tickers.add(func)

    def RemovePhysicsPostTick(self, func):
        if self.physics_tick_inloop:
            self.physics_tick_pending.append((False, func))
        else:
            self.physics_tickers.discard(func)
            if not self.physics_tickers:
                self.world.PhysicsSpace.BindEvent("PhysicsPostTick", None)

    def OnPhysicsPostTick(self, dtime, timeRemained):
        for is_add, func in self.physics_tick_pending:
            if is_add:
                self.AddPhysicsPostTick(func)
            else:
                self.RemovePhysicsPostTick(func)
        self.physics_tick_pending = []

        self.physics_tick_inloop = True
        for func in self.physics_tickers:
            try:
                func(dtime, timeRemained)
            except:
                sys.excepthook(*sys.exc_info())
        self.physics_tick_inloop = False

    def AddSpacePostTick(self, func):
        if self.space_tick_inloop:
            self.space_tick_pending.append((True, func))
        else:
            if not self.space_tickers:
                self.world.PhysicsSpace.BindEvent("SpacePostTick", self.OnSpacePostTick)
            self.space_tickers.add(func)

    def RemoveSpacePostTick(self, func):
        if self.space_tick_inloop:
            self.space_tick_pending.append((False, func))
        else:
            self.space_tickers.discard(func)
            if not self.space_tickers:
                self.world.PhysicsSpace.BindEvent("SpacePostTick", None)

    def OnSpacePostTick(self, dtime):
        for is_add, func in self.space_tick_pending:
            if is_add:
                self.AddSpacePostTick(func)
            else:
                self.RemoveSpacePostTick(func)
        self.space_tick_pending = []

        self.space_tick_inloop = True
        for func in self.space_tickers:
            try:
                func(dtime)
            except:
                sys.excepthook(*sys.exc_info())
        self.space_tick_inloop = False

    def OnBatchNotifySound(self):
        queue = self.batch_notify_sound_queue
        self.batch_notify_sound_queue = defaultdict(dict)
        if self.state != SpaceStateLoaded.NAME:
            return
        game_logic = self.game_logic
        if not game_logic or game_logic.is_destroyed():
            return True

        get_entity = self.entities.get
        entity_sounds = []
        for guid, sounds in queue.items():
            entity = get_entity(guid)
            entity and entity_sounds.append((entity, sounds))

        robots = self.entity_category["RobotCombatAvatar" + game_logic.suffix]
        for robot in robots.values():
            if not robot.is_controlled:
                continue
            publish = robot.AIPublishEvent
            event_type = AIEventType.ON_GAME_SOUND
            for entity, sounds in entity_sounds:
                if entity is robot:
                    continue
                for sound_type, position in sounds.items():
                    publish(event_type, entity, position, sound_type)

    def LoadSpaceWorldData(self, data_name):
        spaceno = self.spaceno
        load_py_name = 'gclient.data.layout_data.space_%s.%s' % (spaceno, data_name)
        module = genv.ImportModule(load_py_name)
        if not module:
            print("[SpaceLoader][LoadSpaceWorldData] no file %s" % load_py_name)
            return
        return module.data

    def UnloadSpaceWorldData(self, data_name):
        spaceno = self.spaceno
        load_py_name = 'gclient.data.layout_data.space_%s.%s' % (spaceno, data_name)
        genv.DelModule(load_py_name)
        return
    
    def GetLevelFiles(self, prefix):
        worlds_folder_path = os.path.join(MLauncher.PKGROOT, 'Worlds')
        suffix = ".ilevel"
        processed_names = []
        
        if not os.path.exists(worlds_folder_path):
            print(f"文件夹不存在: {worlds_folder_path}")
            return tuple()
        
        for filename in os.listdir(worlds_folder_path):
            if filename.startswith(prefix) and filename.endswith(suffix):
                processed_name = filename[len(prefix):-len(suffix)]
                processed_names.append(processed_name)
        
        return tuple(processed_names)

    def TryLoadWorldAsLevelsV2(self, world_name, pivot_pos, on_load_level_callback=None, levels=None):
        print_s(f"TryLoadWorldAsLevelsV2: {world_name} {pivot_pos}", SomePreset.white_fg_blue_bg)
        def _callback(_world, level_dict):
            # _world即是当前iworld, level_dict是新load进来的level名字映射
            for lv in level_dict.values():
                level = _world.Levels[lv]
                trans = level.Transform
                tmp = pivot_pos  # MType.Vector3
                trans.translation = tmp
                trans.yaw = math.pi
                level.Transform = trans
                level.NeedPhysics = True
                level.EnterWorld()
            MRender.SetRenderOption("EnableVolumetricLightmap", True)
            if light_level:
                light_level.EnterWorld()
                MEngine.GetGameplay().DelHoldingInstance(light_level)
            print_s(f"TryLoadWorldAsLevelsV2: _callback, set back sun_color_intensity: {self.cur_world_sun_color_intensity_orginal}", SomePreset.white_fg_blue_bg)
            if self.cur_world_sun_color_intensity_orginal is not None:  
                self.world.EnvVolume.SunColorIntensity = self.cur_world_sun_color_intensity_orginal
                self.cur_world_sun_color_intensity_orginal = None
            on_load_level_callback and on_load_level_callback(level_dict)

        # light_level = self.world.Levels.get('TestRoom_Env', None)
        light_level_name = "L_Miami_Lighting_06"
        light_level = self.world.Levels.get(light_level_name, None)
        if light_level:
            MEngine.GetGameplay().AddHoldingInstance(light_level)
            light_level.LeaveWorld()

        # level_name = f"{world_name}@$root"
        print_s(f"TryLoadWorldAsLevelsV2: orginal sun_color_intensity: {self.world.EnvVolume.SunColorIntensity}", SomePreset.white_fg_blue_bg)
        if self.cur_world_sun_color_intensity_orginal is None:
            self.cur_world_sun_color_intensity_orginal = self.world.EnvVolume.SunColorIntensity
        self.world.EnvVolume.SunColorIntensity = 0.0
        # if level_name in self.world.Levels:
        if self.IsWorldAsLevelsLoaded(world_name):
            level_dict = {}
            for lv in self.world.Levels.keys():
                if world_name in lv:
                    level_dict[lv] = lv
            print_s(f"world_name {world_name} already loaded, level_dict: {level_dict}", SomePreset.white_fg_yellow_bg)
            _callback(self.world, level_dict)
        else:
            MRender.SetRenderOption("EnableVolumetricLightmap", False)
            MHelper.LoadWorldLevels(world_name, level=levels, callback=_callback)
    
    def IsWorldAsLevelsLoaded(self, world_name):
        level_name = f"{world_name}@$root"
        return level_name in self.world.Levels

    def TryLoadWorldLevels(self, on_load_level_callback=None):
        def _callback(_world, level_dict):
            # _world即是当前iworld, level_dict是新load进来的level名字映射
            for lv in level_dict.values():
                level = _world.Levels[lv]
                trans = level.Transform
                # tmp = MType.Vector3(*genv.player.position)
                tmp = switches.MATCHINTRO_LEVEL_POS
                trans.translation = tmp
                level.Transform = trans
                level.EnterWorld()
            MRender.SetRenderOption("EnableVolumetricLightmap", True)
            if light_level:
                light_level.EnterWorld()
                genv.lll = light_level
                MEngine.GetGameplay().DelHoldingInstance(light_level)
            self.world.EnvVolume.SunColorIntensity = cur_sun_color_intensity
            on_load_level_callback and on_load_level_callback()

        light_level = self.world.Levels.get('L_Miami_Lighting_06', None)
        if light_level:
            MEngine.GetGameplay().AddHoldingInstance(light_level)
            light_level.LeaveWorld()
        cur_sun_color_intensity = self.world.EnvVolume.SunColorIntensity
        self.world.EnvVolume.SunColorIntensity = 0.0
        MRender.SetRenderOption("EnableVolumetricLightmap", False)
        MHelper.LoadWorldLevels('MatchIntro', callback=_callback)

    def GetSceneEntityInWorld(self, ientity_name):
        if ientity_name not in self.world_ientity_dict:
            return None
        return self.world_ientity_dict[ientity_name]

    def GetSceneEntity(self, level_name, ientity_name):
        world = self.world
        if not world:
            return
        if level_name not in world.Levels:
            return
        for entity in world.Levels[level_name].RootArea.Entities:
            if entity.GetName() == ientity_name:
                return entity
        return None

    def GetEntity(self, guid):
        return self.entities.get(guid, None)

    def GetEntityByID(self, eid):
        return self.entities.get(eid, None)

    def GetEntitiesByType(self, name):
        return self.entity_category.get(name, {})

    def GetEntityInLevelByPrefix(self, level_name, entity_name_prefix):
        if self.world is None:
            return
        entities_in_level = {}
        cur_lvl = self.world.Levels.get(level_name)
        if cur_lvl and cur_lvl.RootArea:
            for e in cur_lvl.RootArea.Entities:
                name = e.GetName()
                if entity_name_prefix and name.find(entity_name_prefix) >= 0:
                    entities_in_level[name] = e
        return entities_in_level

    def GetEntityInLevelByName(self, level_name, entity_name):
        if self.world is None:
            return None
        cur_lvl = self.world.Levels.get(level_name)
        if cur_lvl and cur_lvl.RootArea:
            for e in cur_lvl.RootArea.Entities:
                if entity_name == e.GetName():
                    return e
        return None

    def CombatAvatarsInRange(self, position, radius):
        result = []
        radius_2 = radius * radius
        x, y, z = position
        for guid, avatar in self.combat_avatars.items():
            x_a, y_a, z_a = avatar.position
            x_d = x - x_a
            y_d = y - y_a
            z_d = z - z_a
            if x_d * x_d + y_d * y_d + z_d * z_d <= radius_2:
                result.append(avatar)
        return result

    def AddWorldPreloadPivots(self, reason, point):
        self.world_preload_pivots[reason].append(point)
        self.UpdateWorldPreloadPivots()

    def RemoveWorldPreloadPivots(self, reason):
        if reason:
            self.world_preload_pivots.pop(reason, None)
        else:
            self.world_preload_pivots.clear()
        self.UpdateWorldPreloadPivots()

    def UpdateWorldPreloadPivots(self):
        pivots = []
        for k, v in self.world_preload_pivots.items():
            pivots.extend(v)
        if self.world:
            self.world.PreloadPivots = pivots

    def SetObservationPoints(self, pos_list):
        world = self.world
        world.ObservationPoints = pos_list

    def SetPreloadPivotsAndDistance(self, reason, pos_list, dist_list):
        if pos_list:
            self.AddWorldPreloadPivots(reason, pos_list[0])
        else:
            self.RemoveWorldPreloadPivots(reason)
        self.world.PreloadDistances = dist_list

    def TickPreloadPivotsAndDistance(self):
        world = self.world
        now = time.time()
        if world.PreloadPivots and not world.ObservationPoints:
            if not self.preload_pivot_empty_stamp:
                self.preload_pivot_empty_stamp = now
        elif self.preload_pivot_empty_stamp:
            self.preload_pivot_empty_stamp = None

        if self.preload_pivot_empty_stamp and now > self.preload_pivot_empty_stamp + self.preload_pivot_clear_interval:
            self.SetPreloadPivotsAndDistance('ads', [], [])

    def TimerLogSomething(self):
        def _report_performace():
            self._onCalcStutterCount()
            gpl.SendElkLog('FpsInfo')

        def _report_sec():
            self.owner and self.owner.UploadWtfXxxx()

        def _report_live_time():
            self.owner and self.owner.SALogToServer("CombatItemLiveTime", live_time=self.combat_item_live_time)
            self.combat_item_live_time = {}

        self._InitCalcStutterCount()
        self.timer_log_elk and self.cancel_timer(self.timer_log_elk)
        self.timer_log_elk = self.add_repeat_timer(2 * 60, _report_performace)
        self.timer_log_qsec and self.cancel_timer(self.timer_log_qsec)
        self.timer_log_qsec = self.add_repeat_timer(60, _report_sec)
        self.timer_live_timer and self.cancel_timer(self.timer_live_timer)
        self.timer_live_timer = self.add_repeat_timer(5 * 60, _report_live_time)
        # _report_performace()

    def _InitCalcStutterCount(self):
        self.last_stutter_count = MEngine.GetStutterCount()

    def _onCalcStutterCount(self):
        # 设置: MEngine.SetTolerantDeltaTime 目前是 0.5
        total_stutter_count = MEngine.GetStutterCount()
        gpl.current_stutter_count = total_stutter_count - self.last_stutter_count
        if genv.is_in_background:
            # 在后台的时候就不计数了
            gpl.current_stutter_count = 0
        self.last_stutter_count = total_stutter_count
        return

    def CheckPythonThreadNum(self):
        if not MConfig.IsProfile:
            return

        def _check_python_thread_num():
            import threading  # noqa
            if threading.active_count() > 1:
                count = threading.active_count()
                for thread in threading.enumerate():
                    if isinstance(thread, threading._DummyThread):
                        count -= 1
                if count > 1:
                    pass
                    # raise AssertionError("Too Many Python Threads(%d)" % threading.active_count())

        self.timer_check_python and self.cancel_timer(self.timer_check_python)
        self.timer_check_python = self.add_repeat_timer(3 * 60, _check_python_thread_num)
        _check_python_thread_num()

    # region ########### 一些 helper 函数 ###################
    def SetWorldStoryboardSpeed(self, speed):
        if self.world:
            self.world.Storyboard.Speed = speed
        # 加上声音缓慢
        if speed < 1:
            genv.sound_mgr.SetGlobalParameter('slow_motion', 1)
        else:
            genv.sound_mgr.SetGlobalParameter('slow_motion', 0)

    def GetWorldStoryboardSpeed(self):
        if self.world:
            return self.world.Storyboard.Speed


    def NotifyGameSoundToAI(self, entity, position, sound_type):
        self.batch_notify_sound_queue[entity.id][sound_type] = position

    def BuildTransformFromCenter(self, center, yaw, x, y, z, down_len=10, debug=False, debug_time=1.0, up_offset=0.1,
                                 caster_pos=None, filter_info=cconst.PHYSICS_BULLET, normal=None,
                                 exclude_collision_names=None, up_from=1.8, height_diff=2):
        # 构造一个贴地矩阵，用于让生成的物体贴地
        # x y z: 物体的物理大小
        # center: Vector3
        # up_offset: 做AllOverLap时候加一点点离地高度
        # caster_pos: 如果传入这个参数，那么会检查构造的碰撞体形状是否包含在另一个大的碰撞体里面（比如山）
        # normal: 如果传入这个参数，那么直接使用center和normal开始构造贴地矩阵，不会做第一次从上往下打射线，找到碰撞点的过程；
        # exclude_collision_names: AllOverLap的时候这些名字不算进检查的碰撞里面
        # up_from: 第一步射线检测时候，离地高度，avatar不同pose_type可以不一样
        # height_diff: 判定是否悬空的高度差
        if not normal:
            up = MType.Vector3(0, 1, 0)
            down = MType.Vector3(0, -1, 0)
            frm = center + up * up_from
            r = self.RawRaycast(frm, down_len, filter_info, with_trigger=False, to_dir=down)
            if not r or not r.IsHit:
                return None
            physics_hit_pos = r.Pos
            # 判断有没有打中entity
            entity = getattr(r.Body, 'owner', None)
            if entity:
                print('构造一个贴地矩阵失败，原因：打中了entity')
                return None
            normal = r.Normal
        else:
            physics_hit_pos = center
        # 判断打中点的高度和center的高度是否一致（粗略判定打中点是否悬空）
        if not -height_diff < center.y - physics_hit_pos.y < height_diff:
            print('构造一个贴地矩阵失败，原因：打中的点可能是悬空的（y坐标跟人的y坐标相差大于%s）' % height_diff, center.y - physics_hit_pos.y)
            return None

        r = self.RawRaycast(physics_hit_pos, 2, filter_info,
                            with_trigger=False,
                            to_pos=MType.Vector3(physics_hit_pos.x, physics_hit_pos.y - 2, physics_hit_pos.z))
        if r and r.IsHit:
            entity = getattr(r.Body, 'owner', None)
            if entity and not entity.is_destroyed() and entity.IsVehicle and entity.is_helicopter:
                # 不能丢在直升机上方
                return

        # 地形匹配
        trans = MType.Matrix4x3()
        trans.translation = physics_hit_pos
        y_axis = normal
        old_yaw = MType.Vector3(0, 0, 1)
        old_yaw.yaw = yaw
        x_axis = normal.cross(old_yaw)
        z_axis = x_axis.cross(normal)
        trans.x_axis, trans.y_axis, trans.z_axis = x_axis, y_axis, z_axis
        # 检查有没有碰到别的东西
        check_trans = trans.clone()
        check_trans.translation = trans.translation + trans.y_axis * (y / 2.0 + up_offset)
        results = self.AllOverlapByBox3(check_trans, x, y, z, filter_info, debug=debug, debug_time=debug_time)
        if exclude_collision_names is None:
            exclude_collision_names = []
        print('11', results)
        if results:
            for result in results:
                if getattr(result.Body, 'IsTrigger', False):
                    # 过滤trigger
                    continue
                name = result.Body.Entity.GetName()
                print('55', name, getattr(result.Body, 'owner', None))
                if name and not ('Terrain' in name or 'terrain' in name or name in exclude_collision_names):
                    print('构造一个贴地矩阵失败，原因：落点做OverLap，检测到了一个或多个目标（正常来说是应该没有的），目标的名字和坐标如下：', name, check_trans.translation)
                    return None
        if caster_pos:
            r = self.RawRaycast(MType.Vector3(caster_pos[0], caster_pos[1] + 1.0, caster_pos[-1]), 100, filter_info,
                                with_trigger=False, to_pos=check_trans.translation)
            if r and r.IsHit:
                # 正向射线打到了，看看反向射线打没打到，如果在物体里面，反向射线应该是打不到的，因为物理是单面的。
                r_reverse = self.RawRaycast(check_trans.translation, 100, filter_info, with_trigger=False,
                                            to_pos=MType.Vector3(caster_pos[0], caster_pos[1] + 1.0, caster_pos[-1]))
                if not (r_reverse and r_reverse.IsHit):
                    print('构造一个贴地矩阵失败，原因：落点很可能在一个大的碰撞体里面，比如在山里面')
                    return None
        print('构造一个贴地矩阵成功')
        return trans
    # endregion ########### 一些 helper 函数 ###################

    def LevelEnterOrLeaveWorld(self, level_name, is_enter, with_physics=False):
        if not self.world:
            return
        level = self.world.Levels.get(level_name)
        if not level:
            return
        if is_enter and not level.IsInWorld:
            level.EnterWorld()
            if with_physics:
                level.NeedPhysics = True
        elif not is_enter and level.IsInWorld:
            level.LeaveWorld()
            if with_physics:
                level.NeedPhysics = False

    def ShowEntitiesCount(self):
        counters = defaultdict(int)
        for entity in self.entities.values():
            if entity and not entity.is_destroyed():
                counters[entity.__class__] += 1

        print("============== ShowEntitiesCount ================")
        # [DEBUG]
        for cls, count in counters.items():
            print("\t%s: %s" % (
                cls,
                count,))
        # [DEBUG]

    @property
    def GeometryFactor(self):
        terrain = self.terrain
        if not terrain:
            return 1.0
        else:
            return terrain.Primitives[0].GeometryFactor

    @GeometryFactor.setter
    def GeometryFactor(self, value):
        terrain = self.terrain
        if not terrain:
            return
        terrain.Primitives[0].GeometryFactor = value

    @property
    def TerrainMaxLoadingDistance(self):
        terrain = self.terrain
        if not terrain:
            return -1
        else:
            return terrain.Primitives[0].MaxLoadingDistance

    @TerrainMaxLoadingDistance.setter
    def TerrainMaxLoadingDistance(self, value):
        terrain = self.terrain
        if not terrain:
            return
        terrain.Primitives[0].MaxLoadingDistance = value

    @property
    def TerrainLodThreshold(self):
        terrain = self.terrain
        if not terrain:
            return MType.Vector3()
        else:
            return terrain.LodThreshold

    @TerrainLodThreshold.setter
    def TerrainLodThreshold(self, value):
        terrain = self.terrain
        if not terrain:
            return
        terrain.IsOverrideLodThreshold = True
        terrain.LodThreshold = value

    def IsHighWorld(self, base_world_name):
        from gclient.config import LocalConfig
        is_high_world = False
        high_world_name = '%s_high' % base_world_name
        if not self.is_si_island:
            return
        if gui.is_pc_svn:  # 如果是 WinTrunk 保持跟手机一致
            world_path = os.path.join(MLauncher.PKGROOT, 'Worlds/%s.iworld' % high_world_name)
            if os.path.exists(world_path) and LocalConfig.game_quality_level >= 3:
                is_high_world = True
        else:
            dlc_id = hashlib.md5('Worlds/%s.iworld' % high_world_name).hexdigest()
            dlc_manager = genv.dlc_manager

            world_file = str(dlc_id[0:2] + '/' + dlc_id + '.0')
            if MEngine.FileExist(world_file) and dlc_manager.CheckDlcExists(dlc_id):
                if gui.is_pc_release or LocalConfig.game_quality_level >= 3:
                    is_high_world = True
        return is_high_world

    def StatEntity(self):
        return {name: len(d) for name, d in self.entity_category.items()}

    def StatClientEntity(self):
        from collections import Counter
        c = Counter()

        for k, v in self.entities.items():
            c[v.__class__.__name__] += 1
        return c

    def StatMaxClientEntity(self):
        self.max_entity_count = 0
        self.max_entity_position = (0, 0, 0)
        if hasattr(self, "max_entity_timer"):
            self.cancel_timer(self.max_entity_timer)
        self.max_entity_timer = self.add_repeat_timer(0.1, self.StatMaxClientEntityImply)

    def StatMaxClientEntityImply(self):
        c = self.StatClientEntity()
        # count = sum(c.values())
        count = c.get("ItemSpaceSnare", 0)
        if count > self.max_entity_count:
            self.max_entity_count = count
            self.max_entity_position = genv.player.position
            print("Position: ", self.max_entity_position, " Count: ", count, " detail: ", c)

    def GetNaviMapGuid(self):
        # if 'map_server' in self.proto:
        #     map_server = self.proto['map_server']
        #     for data in space_data.data.values():
        #         if 'map' in data and 'navimesh' in data and data['map'] == map_server:
        #             return data['navimesh']
        return self.proto.get('navimesh', '')

    # region 客户端寻路
    def LoadNaviMap(self):
        g_navigateMapGuid = self.GetNaviMapGuid()
        if not g_navigateMapGuid:
            # 没有寻路图
            return
        world = MEngine.GetGameplay().Scenario.ActiveWorld
        world.NavigateMap = MNavigate.CreateNavigateMapFromGuid(g_navigateMapGuid)
        world.NavigateMap.BindEvent('ResourceUpdated', self.onNavigateMapReady)

    def onNavigateMapReady(self, navigateMap):
        print('NavigateMap loaded.')
        self.navigateMap = navigateMap

    # endregion
