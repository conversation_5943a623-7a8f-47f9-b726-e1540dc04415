# -*- coding:utf-8 -*-
import math

import cc
from common.classutils import Components

from gclient.cconst import PC_KEY_UI, CONTROLLER_KEY_UI
from gclient.framework.ui.widgets.ui_button import UITabButton
from gclient.framework.ui.widgets.ui_tab_group_comp import UITabGroupComp
from gclient.framework.util.desktop_input import DesktopInput
from gclient.framework.util.gameinput_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List, Callable

from functools import partial
from gclient.framework.ui import ui_define
from gclient.framework.ui.commonnodes.common_key_node import KeyNode, KeyNodesManagerComp

from gclient.framework.ui.ui_helper import HelperNode, HelperWindow
from gclient.framework.ui.widgets import UIButton, UIWindow
from gclient.framework.ui.widgets import UIListViewCycle
from gclient.framework.ui.widgets import UIText
from gclient.framework.ui.widgets.ui_listview import UIListView
from gclient.gamesystem.util import unlock_sys_util
# from gclient.gamesystem.util.unlock_sys_util import SystemId

class ITabItem(object):
    def InitTabItem(self, click_cb):
        self.tab_type = None
        self.click_cb = click_cb
        self.onClick = self.OnClickTab

    def SetNodeInfo(self, cur_tab, tab_type, tab_data=None):
        self.tab_type = tab_type
        self.SetSelected(cur_tab == tab_type)

    def OnClickTab(self, widget=None):
        self.click_cb and self.click_cb(self.tab_type)

    def SetSelected(self, is_selected):
        pass


class BaseTabListItem(HelperNode, ITabItem):
    def __init__(self, click_cb, parent, widget):
        super(BaseTabListItem, self).__init__(parent, widget)
        self.InitTabItem(click_cb)
        self.EnableTouch(True, False)


class BaseTabListBtnItem(UIButton, ITabItem):
    def __init__(self, click_cb, widget):
        super(BaseTabListBtnItem, self).__init__(widget)
        self.InitTabItem(click_cb)
        self.InitNode()

    def InitData(self):
        pass

    def InitNode(self):
        pass

    def SetSelected(self, is_selected):
        self.SetEnabled(not is_selected)


class CommonTabNode(HelperNode):
    def __init__(self, click_cb, parent, widget):
        self.click_cb = click_cb
        super(CommonTabNode, self).__init__(parent, widget)

    def InitData(self):
        self.tab_type = None

    def InitNode(self):
        super().InitNode()
        # self.LoadAnimFromFile('UIScript/node_og_ranking_interface_tab.csb')

        seek = self.seek('node_tab').seek

        seek('panel_tab').visible = False
        panel_main = seek('panel_tab_new')
        panel_main.visible = True
        self.btn_tab = btn_tab = panel_main.seek('btn_1', UIButton)
        btn_tab.onClick = self.OnClickTab

        self.img_selected = btn_tab.seek('img_selected')
        self.img_line = btn_tab.seek('img_dot')

        self.txt_name = btn_tab.seek('txt_1', UIText)

    def SetNodeInfo(self, cur_tab, tab_type, tab_data=None):
        self.tab_type = tab_type
        self.SetSelected(cur_tab == tab_type)
        self.txt_name.text = gui.GetStrMsgFromData(tab_data) if isinstance(tab_data, int) else tab_data

    def OnClickTab(self, widget=None):
        self.click_cb and self.click_cb(self.tab_type)

    def SetSelected(self, is_selected):
        self.btn_tab.SetEnabled(not is_selected)
        self.img_selected.visible = is_selected
        self.img_line.visible = not is_selected

        self.StopAnim(True)
        if is_selected:
            self.PlayAnim('click', callback=partial(self.PlayAnim, 'loop', is_loop=True))


def SetPanelVisible(panel, visible):
    if isinstance(panel, (list, tuple)):
        for p in panel:
            SetPanelVisible(p, visible)
    else:
        panel.visible = visible


def CallPanelFunc(panel, func_name, *args, **kwargs):
    if not panel:
        return
    if isinstance(panel, (list, tuple)):
        for p in panel:
            CallPanelFunc(p, func_name, *args, **kwargs)
    else:
        func = getattr(panel, func_name, None)
        if func:
            func(*args, **kwargs)


class CommonTabListComponent(object):
    def __init_data_component__(self, *args, **kwargs):
        self.cur_tab_type = None
        self.tab_types = ()
        self.tab_data_dict = {}
        self.tab_item_dict = {}
        self.tab_panel_dict = {}
        self.tab_switch_callback = None

    def __init_tab_list_component__(
            self, parent=None, listview_path='list_tab', tab_item_cls=None,
            tab_types=None, tab_data_dict=None, tab_panel_dict=None,
            tab_switch_callback=None, scroll_enabled=False, auto_expand=False,
    ):
        if tab_types is not None:
            self.tab_types = tab_types
            self.cur_tab_type = tab_types[0]

        if tab_data_dict is not None:
            self.tab_data_dict = tab_data_dict

        if tab_panel_dict is not None:
            self.tab_panel_dict = tab_panel_dict
            for panel in tab_panel_dict.values():
                SetPanelVisible(panel, False)

        self.tab_switch_callback = tab_switch_callback
        self.scroll_enabled = scroll_enabled

        parent = parent or self
        childex = parent.childex
        self.listview_tab = listview_tab = childex(listview_path, UIListViewCycle)
        on_click_tab_item = self.OnClickTabItem
        if tab_item_cls:
            if issubclass(tab_item_cls, HelperNode):
                tab_item_cls = partial(tab_item_cls, on_click_tab_item, parent)
            else:
                tab_item_cls = partial(tab_item_cls, on_click_tab_item)
        else:
            tab_item_cls = partial(BaseTabListItem, on_click_tab_item, parent)
        item_count = 1 if auto_expand else None
        listview_tab.create(
            item_count,
            callback=self.OnRefreshTabList, obj_type=tab_item_cls, hide_redundant=True, auto_expand=auto_expand)
        listview_tab.SetScrollEnabled(scroll_enabled)

    @property
    def cur_tab_panel(self):
        return self.tab_panel_dict.get(self.cur_tab_type)

    def OnRefreshTabList(self, irange):
        tab_types = self.tab_types
        tab_data_dict_get = self.tab_data_dict.get
        tab_item_dict = self.tab_item_dict = {}
        cur_tab = self.cur_tab_type
        for index, node in irange:
            tab_type = tab_types[index]
            node.SetNodeInfo(cur_tab, tab_type, tab_data_dict_get(tab_type))
            tab_item_dict[tab_type] = node

    def __refresh_tab_list_component__(
            self, show_tab_types=None, tab_data_dict=None, select_first_tab=False, with_callback=True):
        cur_tab_type = self.cur_tab_type
        if show_tab_types:
            self.tab_types = show_tab_types
            if select_first_tab or self.cur_tab_type not in show_tab_types:
                cur_tab_type = show_tab_types[0]

        if tab_data_dict:
            self.tab_data_dict = tab_data_dict

        self.OnClickTabItem(cur_tab_type, with_callback)
        self.RefreshTabList()

    def __switch_tab_component__(self, tab=None, scroll=False, with_callback=True, can_refresh=False):
        tab = tab if tab is not None else self.tab_types[0]
        if self.cur_tab_type == tab and not can_refresh:
            return
        self.OnClickTabItem(tab, with_callback)
        scroll and self.scroll_enabled and self.listview_tab.JumpToIndexEx(self.tab_types.index(tab))

    def __scroll_tab_component__(self, tab):
        if self.tab_types:
            self.listview_tab.JumpToIndexEx(self.tab_types.index(tab), viewport_percent=0.1)

    def __on_show_component__(self, *args):
        for tab_item in self.tab_item_dict.values():
            if not hasattr(tab_item, 'OnShow'):
                break
            tab_item.OnShow()

    def RefreshTabList(self):
        self.listview_tab.total_item_num = len(self.tab_types)

    def OnClickTabItem(self, tab_type, with_callback=True):
        old_tab_type = self.cur_tab_type
        tab_item_dict = self.tab_item_dict
        tab_panel_dict = self.tab_panel_dict
        if old_tab_type in tab_item_dict:
            tab_item_dict[old_tab_type].SetSelected(False)

        if old_tab_type in tab_panel_dict:
            SetPanelVisible(tab_panel_dict[old_tab_type], False)

        self.cur_tab_type = tab_type

        if tab_type in tab_item_dict:
            tab_item_dict[tab_type].SetSelected(True)

        if tab_type in tab_panel_dict:
            cur_panel = tab_panel_dict[tab_type]
            SetPanelVisible(cur_panel, True)

        with_callback and self.tab_switch_callback and self.tab_switch_callback(old_tab_type, tab_type)

        CallPanelFunc(self.cur_tab_panel, 'Refresh')


class TabSelectPanel(HelperNode):
    # tab 选中态标签, 样式为 '标题 x TITLE', 需要根据具体文字内容调整内部节点排列
    def InitNode(self):
        self.txt_title = self.seek('txt_des', UIText)
        self.txt_title_en = self.seek('txt_lobby', UIText)
        self.x_0 = self.seek('txt_x_01', UIText)
        self.x_1 = self.seek('txt_x_02', UIText)
        self.interval = 10
        self.anchor_x = self.x_0.getPositionX()
        self.x_width = self.x_0.getContentSize().width
        self.half_x_width = self.x_width * 0.5

        self.txt_title.setRotationSkewX(0)
        self.txt_title.setItalic(math.radians(10))
        self.txt_title_en.setRotationSkewX(0)
        self.txt_title_en.setItalic(math.radians(10))

    def SetData(self, title, title_en):
        self.txt_title.text = title
        self.txt_title_en.text = title

        anchor_x = self.anchor_x
        half_x_width = self.half_x_width
        interval = self.interval
        x_width = self.x_width

        self.txt_title.SetPositionX(anchor_x - half_x_width - interval - self.txt_title.getContentSize().width * 0.5)
        self.txt_title_en.SetPositionX(anchor_x + half_x_width + interval + self.txt_title_en.getContentSize().width * 0.5)
        self.x_1.SetPositionX(anchor_x + x_width + self.txt_title_en.getContentSize().width + interval * 2)


    def GetAnchorRightWidth(self):
        # 锚点右边的内容长度
        return self.half_x_width * 3 + self.interval * 2 + self.txt_title_en.getContentSize().width

    def GetAnchorLeftWidth(self):
        # 锚点左边的内容长度
        return self.half_x_width + self.interval * 2 + self.txt_title.getContentSize().width


class HallCommonTabNode(UITabButton):
    # node_og_gun_tab_item
    def __init__(self, widget=None):
        super().__init__(widget)
        root = self.childex('node_tab.btn_tab')
        self.pnl_sel = root.childex('panel_slc')
        self.pnl_nml = root.childex('panel_nml')
        self.pnl_lock = root.childex('panel_lock')
        self.txt_des1 = root.childex('panel_slc.txt_des', UIText)
        self.txt_des1.visible = False
        self.txt_des2 = root.childex('panel_nml.txt_des', UIText)
        self.txt_des3 = root.childex('panel_lock.txt_des', UIText)
        self.txt_des4 = root.childex('panel_hov.txt_des', UIText)
        root.childex('panel_slc.panel_txt_bg').visible = True
        # pnl_move 用于程序动画，vx表示动画时间太长需要程序实现
        self.pnl_move = self.pnl_sel.childex('panel_vx_bg.panel_move')
        self.pnl_move_0 = self.pnl_sel.childex('panel_vx_bg.panel_move_0')
        self.vx_left = self.pnl_sel.childex('panel_vx_bg.vx_jiantou_left')
        self.vx_right = self.pnl_sel.childex('panel_vx_bg.vx_jiantou_right')
        self.pnl_txt_move = self.pnl_sel.childex('panel_txt_bg.panel_move', partial(TabSelectPanel, self))
        self.pnl_txt_move_0 = self.pnl_sel.childex('panel_txt_bg.panel_move_0', partial(TabSelectPanel, self))

        self.img_line = root.seek('img_line')
        self.panel_hover = root.seek('panel_hov')
        self.is_select = False
        self.onMouseHover = self.OnHover
        self.onClick = self.OnClick
        self.onClickCancel = self.OnClickCancel
        self.onClickBegin = self.OnClickBegin
        self.pnl_sel.visible = False
        self.panel_hover.visible = False
        self.pnl_move.visible = False
        self.pnl_move_0.visible = False
        self.anim_node = self.seek('node_tab')
        self.onCheckCanSelect = self.OnCheckCanSelect
        self.func = None
        self.MakeSureAction()

    def MakeSureAction(self):
        self.anim_node.MakeSureActionByLoadFile('UIScript/node_og_gun_tab_item.csb')

    def OnClick(self, _=None):
        # self.txt_des2.opacity = 255
        self.func and self.func(_)

    def OnClickCancel(self, _=None):
        return
        # self.txt_des2.opacity = 255

    def OnClickBegin(self, _=None):
        return
        # self.txt_des2.opacity = 128

    def OnHover(self, btn, visible):
        if self.is_select:
            return

        self.panel_hover.visible = visible
        self.pnl_nml.visible = not visible
        if visible:
            self.anim_node.PlayAnim('in')

    def SetSelect(self, is_select):
        self.is_select = is_select
        self.RefreshNode()

        if is_select:
            self.panel_hover.visible = False

            self.vx_left.visible = True
            self.vx_right.visible = True
            self.anim_node.PlayAnim('slc_in', callback=self.OnSelectAnimEnd)
            self.MoveText()
        else:
            self.anim_node.StopAnim(True)
            self.pnl_move_0.visible = False
            self.pnl_move.visible = False
            self.pnl_move.stopAllActions()
            self.pnl_move_0.stopAllActions()
            self.ResetPanelTextMove()

    def ResetPanelTextMove(self):
        self.pnl_txt_move.stopAllActions()
        self.pnl_txt_move_0.stopAllActions()

        start_x = 74
        move_length = self.pnl_txt_move.GetAnchorRightWidth() + self.pnl_txt_move.GetAnchorLeftWidth()
        right_start_x = 74 + move_length

        self.pnl_txt_move.SetPositionX(start_x)
        self.pnl_txt_move_0.SetPositionX(right_start_x)

    def MoveText(self):
        start_x = 74
        start_y = self.pnl_txt_move.getPositionY()
        move_length = self.pnl_txt_move.GetAnchorRightWidth() + self.pnl_txt_move.GetAnchorLeftWidth()
        right_start_x = 74 + move_length

        self.pnl_txt_move.SetPositionX(start_x)
        self.pnl_txt_move_0.SetPositionX(right_start_x)
        self.pnl_txt_move.runAction(
            cc.RepeatForever.create(
                cc.Sequence.create([
                    cc.MoveBy.create(2.5, cc.Vec2(-move_length, 0)),
                    cc.Place.create(cc.Vec2(right_start_x, start_y)),
                    cc.MoveBy.create(2.5, cc.Vec2(-move_length, 0)),
                ])
            )
        )
        self.pnl_txt_move_0.runAction(
            cc.RepeatForever.create(
                cc.Sequence.create([
                    cc.MoveBy.create(5, cc.Vec2(-move_length * 2, 0)),
                    cc.Place.create(cc.Vec2(right_start_x, start_y)),
                ])
            )
        )

    def OnSelectAnimEnd(self):
        self.anim_node.PlayAnim('slc_loop', is_loop=True)

        self.pnl_move_0.visible = True
        self.pnl_move.visible = True
        self.vx_left.visible = False
        self.vx_right.visible = False
        self.pnl_move.SetPositionX(-163)
        self.pnl_move_0.SetPositionX(0)
        self.pnl_move.runAction(
            cc.RepeatForever.create(
                cc.Sequence.create([
                    cc.MoveBy.create(20, cc.Vec2(326, 0)),
                    cc.Place.create(cc.Vec2(-163, 0)),
                ])
            )
        )
        self.pnl_move_0.runAction(
            cc.RepeatForever.create(
                cc.Sequence.create([
                    cc.MoveBy.create(10, cc.Vec2(163, 0)),
                    cc.Place.create(cc.Vec2(-163, 0)),
                    cc.MoveBy.create(10, cc.Vec2(163, 0)),
                ])
            )
        )


    def SetData(self, index, text, sys=None, is_last=False, text_en=''):
        self.index = index
        self.txt_des = text
        self.sys = sys
        self.is_last = is_last
        self.is_unlock = unlock_sys_util.IsUnlock(self.sys)

        self.pnl_txt_move.SetData(text, text_en)
        self.pnl_txt_move_0.SetData(text, text_en)

        self.RefreshNode()

    def RefreshNode(self):
        self.pnl_sel.visible = self.is_select and self.is_unlock
        self.pnl_nml.visible = not self.is_select and self.is_unlock
        self.pnl_lock.visible = not self.is_unlock
        # self.SetEnabled(self.is_unlock)

    @property
    def txt_des(self):
        return self.txt_des1.text

    @txt_des.setter
    def txt_des(self, val):
        self.txt_des1.text = self.txt_des2.text = self.txt_des3.text = self.txt_des4.text = val

    def OnCheckCanSelect(self, from_click=True):
        if from_click and not self.is_unlock:
            unlock_sys_util.PromptSysLocked(self.sys)
        return self.is_unlock

    def UpdateUnlockInfo(self):
        self.is_unlock = unlock_sys_util.IsUnlock(self.sys)
        self.RefreshNode()


class TabData(object):
    text: str
    window: UIWindow
    window_info: dict
    callback: Callable[[], bool]
    lock_func: Callable[[], bool]
    red_func: Callable[[], bool]
    text_en: str
    sys: object

    def __init__(self, text: str, window=None, window_info=None, callback: Callable[[], bool] = None, lock_func: Callable[[], bool] = None, red_func: Callable[[], bool] = None, text_en: str = '', sys=None):
        self.text = text
        self.window = window
        self.window_info = window_info
        self.callback = callback
        self.lock_func = lock_func
        self.red_func = red_func
        self.text_en = text_en
        self.sys = sys


class SubTabData(object):
    text: str

    def __init__(self, text: str, callback, args=None):
        self.text = text
        self.callback = callback
        self.args = args


class HallCommonTabListNode(HelperNode):
    TAB_CLASS = HallCommonTabNode

    # node_og_gun_tab.csb
    def InitData(self):
        self.is_callback = False
        self.switch_timer = None
        self.key_node_enable = False

    def InitNode(self):
        self.LoadAnimFromFile('UIScript/node_og_gun_tab.csb')
        self.root = root = self.childex('root.panel_top')
        self.txt_title = root.seek('txt_top', UIText)
        self.listview_tabs = root.seek('listview_tab', UIListView)
        self.listview_tabs.SetScrollEnabled(False)
        self.listview_tabs.create(item_num=6, callback=self.OnRefreshTabList, obj_type=self.TAB_CLASS)

        self.panel_tips1 = root.seek('panel_tips_1')
        self.panel_tips2 = root.seek('panel_tips_2')
        # 暂时隐藏按键提示
        # keynode_left = root.childex('panel_tips_1.node_pc', partial(KeyNode, PC_KEY_UI.KEY_Q, CONTROLLER_KEY_UI.LB))
        # keynode_right = root.childex('panel_tips_2.node_pc', partial(KeyNode, PC_KEY_UI.KEY_E, CONTROLLER_KEY_UI.RB))
        # self.ctrl.AddKeyNode(keynode_left)
        # self.ctrl.AddKeyNode(keynode_right)
        self.panel_tips1.visible = False
        self.panel_tips2.visible = False

        self.btn_tabs = []
        self.tab_lines = []

        self.base_pos_x = self.listview_tabs.GetWorldPosition().x
        self.base_pos_y = self.panel_tips2.GetWorldPosition().y
        self.base_size_x = 134
        self.cur_tab_idx = -1
        
    def SetData(self, title: str, tab_data: List[TabData]):
        self.is_callback = False
        if len(tab_data) > 0:
            if tab_data[0].callback is not None:
                self.is_callback = True
        self.tab_infos = tab_data
        self.txt_title.text = title
        len_data = len(tab_data)
        self.listview_tabs.total_item_num = len_data
        self.listview_tabs.AdjustSizeByContent()

    def SetKeyNodeEnable(self):
        self.key_node_enable = True
        keynode_left = self.root.childex('panel_tips_1.node_pc', partial(KeyNode, PC_KEY_UI.KEY_Q, CONTROLLER_KEY_UI.LB))
        keynode_right = self.root.childex('panel_tips_2.node_pc', partial(KeyNode, PC_KEY_UI.KEY_E, CONTROLLER_KEY_UI.RB))
        self.ctrl.AddKeyNode(keynode_left)
        self.ctrl.AddKeyNode(keynode_right)
        self.panel_tips1.visible = True
        self.panel_tips2.visible = True

    def Refresh(self):
        # 刷新红点
        len_data = len(self.tab_infos)
        items = self.listview_tabs._items
        for i in range(len_data):
            red_func = self.tab_infos[i].red_func
            items[i].SetRed(red_func and red_func())

    def SetDefaultTab(self, index):
        """
        只显示默认点击的tab，不触发响应
        """
        if index >= len(self.btn_tabs) or index < 0:
            return
        else:
            for tab in self.btn_tabs:
                tab.SetSelect(False)
            self.cur_tab_idx = index
            self.btn_tabs[index].SetSelect(True)

    def GetTabNodes(self):
        return [node for node in self.listview_tabs._items if node.visible]

    def OnRefreshTabList(self, irange):
        len_panel = len(self.tab_infos)
        self.btn_tabs = []
        self.tab_lines = []
        for i, node in irange:
            if i < len_panel:
                tab_info = self.tab_infos[i]
                node.visible = True
                # node.SetData(i, self.tab_infos[i].text)
                node.SetData(i, tab_info.text, tab_info.sys, i == len_panel - 1, tab_info.text_en)
                node.onClick = self.OnClickTab
                if lock_func := self.tab_infos[i].lock_func:
                    node.onCheckCanSelect = lock_func
                else:
                    node.onCheckCanSelect = None
                self.btn_tabs.append(node)
                self.tab_lines.append(node.img_line)
            else:
                node.visible = False

        if self.key_node_enable:
            y = self.panel_tips1.GetWorldPosition().y
            base_pos = self.listview_tabs.GetWorldPosition().x
            half_size = len_panel * self.base_size_x * 0.5 + 40
            self.panel_tips1.SetWorldPosition(cc.Vec2(base_pos - half_size, y))
            self.panel_tips2.SetWorldPosition(cc.Vec2(base_pos + half_size, y))
            self.panel_tips1.visible = len_panel > 1
            self.panel_tips2.visible = len_panel > 1

    def RefreshBtnLines(self):
        for i, line in enumerate(self.tab_lines):
            self.tab_lines[i].visible = i != self.cur_tab_idx and i != self.cur_tab_idx - 1

        self.tab_lines[-1].visible = False

    def OnClickTab(self, tab, info=None):
        self.OnSelectMenu(tab.index, info)

    def OnSelectMenu(self, index, info=None):
        if self.cur_tab_idx == index:
            return
        self.cur_tab_idx = index
        # self.RefreshBtnLines()

        if self.is_callback:
            tab_info = self.tab_infos[index]
            if tab_info and tab_info.callback():
                for i, tab_info in enumerate(self.tab_infos):
                    if i != index:
                        self.btn_tabs[i].SetSelect(False)
                self.btn_tabs[index].SetSelect(True)
        else:
            # 打开当前 tab 窗口
            self.btn_tabs[index].SetSelect(True)
            tab_info = self.tab_infos[index]
            window_info = tab_info.window_info if not info else info
            tab_info.window and tab_info.window.instance(self.ctrl).Show(window_info)

            # 延迟关闭其他窗口，如果在切换的同时关闭，可能会有短暂时间所有窗口都不显示
            self._CancelTimer()
            self.switch_timer = self.add_timer(0.1, lambda _: self._CloseOtherTabs(index))

            for i, tab_info in enumerate(self.tab_infos):
                if i != index:
                    self.btn_tabs[i].SetSelect(False)

    def _CloseOtherTabs(self, idx):
        for i, tab_info in enumerate(self.tab_infos):
            if i != idx:
                tab_info.window and tab_info.window.CloseInst()

    def _CancelTimer(self):
        self.switch_timer and self.cancel_timer(self.switch_timer)
        self.switch_timer = None


class CommonTabButton(HelperNode):
    # node_og_gun_tab_item_fu.csb
    # 通用次级页签的按钮
    def InitData(self):
        self.index = None
        self.data = None
        self.is_select = False

    def InitNode(self):
        self.LoadAnimFromFile('UIScript/node_og_gun_tab_item_fu.csb')
        btn = self.childex('node_tab.btn_tab')
        btn.EnableTouch(True, False)
        btn.SetMouseMoveEventEnable(True)
        btn.onMouseHover = self.OnHover
        btn.onClick = self.OnClick

        self.panel_slc = btn.seek('panel_slc')
        self.panel_nml = btn.seek('panel_nml')
        self.panel_hov = btn.seek('panel_hov')
        self.txt_hov = self.panel_hov.seek('txt_des', UIText)
        self.txt_slc = self.panel_slc.seek('txt_des', UIText)
        self.txt_nml = self.panel_nml.seek('txt_des', UIText)
        self.panel_lock = btn.seek('panel_lock')
        self.img_check = btn.seek('img_gou')
        self.node_red = btn.seek('node_red')
        self.node_red.visible = False

    def OnHover(self, widget, hover):
        if self.is_select:
            return
        self.panel_hov.visible = hover
        self.panel_nml.visible = not hover
        self.PlayAnim('hov')

    def SetData(self, idx, data: SubTabData):
        self.index = idx
        self.data = data
        self.txt_slc.text = self.txt_nml.text = self.txt_hov.text = data.text

    def OnSelect(self, select):
        if self.is_select == select:
            return
        self.is_select = select
        self.panel_nml.visible = not select
        self.panel_slc.visible = select
        if select:
            self.PlayAnim('slc')
            self.panel_hov.visible = False

    def OnClick(self, btn):
        self.ctrl.OnSelectTab(self.index)


class CommonTabSubNode(HelperNode):
    # node_og_gun_tab_fu.csb
    # 通用的顶部次级页签
    TAB_CLASS = CommonTabButton

    def InitData(self):
        self.data_list: List[SubTabData] | None = None
        self.len_data = 0
        self.cur_select_index = None

    def InitNode(self):
        self.LoadAnimFromFile('UIScript/node_og_gun_tab_fu.csb')
        panel_top = self.seek('panel_top')
        self.panel_tips_1 = panel_top.seek('panel_tips_1')
        self.panel_tips_2 = panel_top.seek('panel_tips_2')
        self.key_node_1 = self.panel_tips_1.seek('node_pc', partial(KeyNode, PC_KEY_UI.KEY_A, CONTROLLER_KEY_UI.LT))
        self.key_node_2 = self.panel_tips_2.seek('node_pc', partial(KeyNode, PC_KEY_UI.KEY_D, CONTROLLER_KEY_UI.RT))
        self.ctrl.AddKeyNode(self.key_node_1)
        self.ctrl.AddKeyNode(self.key_node_2)
        self.listview_tabs = panel_top.seek('listview_tab', UIListViewCycle)
        self.listview_tabs.create(obj_type=partial(self.TAB_CLASS, self), callback=self.OnNodeRefresh, auto_expand=True)

    def SetData(self, data_list: List[SubTabData]):
        self.data_list = data_list
        self.len_data = len(data_list)
        self.cur_select_index = 0
        self.listview_tabs.total_item_num = len(data_list)
        self.listview_tabs.AdjustSizeByContent()

    def OnNodeRefresh(self, irange):
        len_data = self.len_data
        for idx, node in irange:
            if idx >= len_data:
                node.visible = False
            else:
                node.visible = True
                node.SetData(idx, self.data_list[idx])
                node.OnSelect(idx == self.cur_select_index)
        if len_data <= 1:
            self.panel_tips_1.visible = False
            self.panel_tips_2.visible = False
        else:
            self.panel_tips_1.visible = True
            self.panel_tips_2.visible = True
            self.panel_tips_2.SetPositionX(self.panel_tips_1.getPosition().x + len_data * 144 + 39)

    def OnSelectTab(self, idx):
        for node in self.listview_tabs._items:
            if node.index == self.cur_select_index:
                node.OnSelect(False)
            if node.index == idx:
                node.OnSelect(True)
        self.cur_select_index = idx
        self.data_list[idx].callback(self.data_list[idx].args)


@Components(KeyNodesManagerComp, UITabGroupComp)
class HallCommonTabListWindow(HelperWindow):
    CSB_NAME = 'UIScript/og_gameplay_select_tab.csb'
    ZORDER = ui_define.SHOW_LEVEL_WINDOW
    SCENE_IDS = (ui_define.UI_SCENEID_WINDOW,)
    CLOSE_ON_ESC = True
    PERMANENT = False
    DESTROY_ON_CLOSE = False
    AUTO_IN_ANIM = False
    AUTO_OUT_ANIM = False
    AUTO_LOOP_ANIM = False
    AUTO_REFRESH = True
    HALL_WINDOW = False

    TITLE = ''
    TAB_DATA = []
    INIT_SELECT = 0

    def InitData(self):
        self._callComponents("init")

    def InitNode(self):
        self.panel_top = self.HelperSeek('panel_top')
        self.panel_top.setLocalZOrder(150)
        self.tab_list_node = self.HelperChildex('panel_top.node_tab', HallCommonTabListNode)

    def OnShow(self, info):
        self.tab_list_node.PlayAnim('in')
        self.tab_list_node.SetData(self.TITLE, self.TAB_DATA)
        self.InitTabGroup([node for node in self.tab_list_node.btn_tabs if node.visible], self.INIT_SELECT)

    def SetTitle(self, title):
        self.tab_list_node.txt_title.text = title

    @property
    def cur_tab_idx(self):
        return self.tab_list_node.cur_tab_idx

    @ListenPcKey(DesktopInput.KEY_Q)
    def OnKeyQ(self, is_down=True):
        if is_down:
            self.HandleButtonL(is_down)

    @ListenPcKey(DesktopInput.KEY_E)
    def OnKeyE(self, is_down=True):
        if is_down:
            self.HandleButtonR(is_down)

    # 开关手动切换页签
    def SetSwitchEnabled(self, enabled):
        for tab in self.tab_list_node.btn_tabs:
            tab.SetEnabled(enabled)
            tab.SetMouseMoveEventEnable(enabled)

        # self.tab_list_node.panel_tips1.visible = enabled
        # self.tab_list_node.panel_tips2.visible = enabled