# -*- coding: utf-8 -*-
from functools import partial

import Timer
import MUI
import MType
import GlobalData
import MEngine
import MMyLighting

from gclient.framework.util.graphics_config_types import SingleOptionMappings, SingleOption, EngineOptionTypes, \
    HallEnableRenderOptions, DisableAutoApplyRenderOptions
from gshare.utils import enum


class WindowType(enum):
    WINDOW = 0  # 窗口化
    FULL_SCREEN_WINDOW = 1  # 窗口无边框全屏
    FULL_SCREEN = 2  # 全屏独占


ResolutionRatio = {
    1: (640, 480),
    2: (720, 576),
    3: (1280, 720),
    4: (1366, 768),
    5: (1600, 900),
    6: (1920, 1080),
    7: (2560, 1440),
    8: (2560, 1600),
    9: (2880, 1620),
    10: (3200, 1800),
    11: (3840, 2160),
}


RefreshRate = {
    0: 0,  # 无限制
    1: 0,  # 跟随显示器
    2: 30,
    3: 60,
    4: 90,
    5: 120,
    6: 144,
    7: 160,
    8: 180,
    9: 200,
    10: 240,
    11: 280,
    12: 300,
    13: 360,
}

SuperResolutionRenderScale = {
    0: 50,
    1: 65,
    2: 75,
    3: 80,
    4: 85
}


def SetFullScreen(window_type=WindowType.WINDOW):
    if window_type == WindowType.WINDOW:
        genv.SetFullScreen(False)
    elif window_type == WindowType.FULL_SCREEN_WINDOW:
        genv.SetFullScreen(True, False)
    elif window_type == WindowType.FULL_SCREEN:
        genv.SetFullScreen(True, True)


def ApplySingleRenderConfig(name, value):
    if gpl.in_hall and name not in HallEnableRenderOptions:
        return
    if name in SingleOptionMappings:
        settings: [SingleOption] = SingleOptionMappings.get(name, {}).get(value, [])
        for single_option in settings:
            if single_option.option_type == EngineOptionTypes.RENDER_OPTIONS:
                gpl.SetRenderOption(single_option.name, single_option.value)
                print('ApplyRenderOption', single_option.name, single_option.value)
            elif single_option.option_type == EngineOptionTypes.BLACK_BOARD:
                gpl.WriteOnBlackBoard(single_option.name, single_option.value)
                print('ApplyBlackBoard', single_option.name, single_option.value)
            elif single_option.option_type == EngineOptionTypes.PP_VOLUME:
                pass
            elif single_option.option_type == EngineOptionTypes.ENV_VOLUME:
                gpl.ApplyEnvOptions(single_option.name, single_option.value)
                print('ApplyEnvVolume', single_option.name, single_option.value)
            elif single_option.option_type == EngineOptionTypes.OTHER:
                pass
    func_name = '_apply_' + name
    if func_name in globals() and callable(globals()[func_name]):
        globals()[func_name](value)


def ApplyRenderConfig():
    if GlobalData.IsSunshineEditor:
        return
    # 这里是自动Apply, 比如切换地图, 这里不要去改窗口逻辑
    if gpl.in_hall:
        keys = [item for item in HallEnableRenderOptions if item not in DisableAutoApplyRenderOptions]
    else:
        all_keys = gpl.render_config.getAllConfigKeys()
        keys = [item for item in all_keys if item not in DisableAutoApplyRenderOptions]
    for name in keys:
        if name == 'full_screen':
            continue
        ApplySingleRenderConfig(name, gpl.render_config.get(name))


def _apply_full_screen(value=None):
    if GlobalData.IsCharEditor:
        return
    if GlobalData.IsSunshineEditor:
        return
    if GlobalData.IsSceneEditorGame:
        return
    if not value:
        value = gpl.render_config.get('full_screen')
    if value == WindowType.WINDOW:
        SetFullScreen(value)
        window_height = MUI.GetScreenHeight()
        window_width = MUI.GetScreenWidth()
        config_width = int(gpl.render_config.get('window_width'))
        config_height = int(gpl.render_config.get('window_height'))
        if not config_width or not config_height:
            return
        if window_height == config_height and window_width == config_height:
            return
        window_full_screen = int(gpl.render_config.get('window_full_screen'))
        if window_full_screen:
            MMyLighting.SetWindowFullScreen()
            return

        # 需要知道当前是哪个屏幕
        monitor_base_x = 0
        monitor_base_y = 0
        for monitor in MMyLighting.GetMonitorDetail():
            if not monitor['hasWindow']:
                continue
            monitor_base_x = monitor['left']
            monitor_base_y = monitor['top']
        max_width = MMyLighting.GetWinSystemMetrics(0)
        max_height = MMyLighting.GetWinSystemMetrics(1)
        # 还需要保证窗口不要太大, 撑爆了
        if config_width > max_width:
            config_width = max_width
        if config_height > max_height:
            config_height = max_height
        pos_x = monitor_base_x + (max_width - config_width) / 2
        pos_y = monitor_base_y + (max_height - config_height) / 2
        MUI.SetWindowInfo(MType.IVec4(pos_x, pos_y, config_width, config_height))
    elif value == WindowType.FULL_SCREEN:
        SetFullScreen(WindowType.WINDOW)
        Timer.addTimer(0.1, DelaySetWindowPos)
    else:
        SetFullScreen(value)


def DelaySetWindowPos():
    resolution_level = gpl.render_config.get('resolution_level')
    if resolution_level == 0:
        win_size = [MMyLighting.GetWinSystemMetrics(0), MMyLighting.GetWinSystemMetrics(1)]
    else:
        win_size = ResolutionRatio.get(resolution_level)
    MUI.SetWindowPos(int(win_size[0]), int(win_size[1]))
    SetFullScreen(WindowType.FULL_SCREEN)


def ApplyMonitor(idx):
    monitors = MMyLighting.GetMonitorDetail()
    if idx >= len(monitors):
        return
    SetFullScreen(WindowType.WINDOW)
    Timer.addTimer(0.0, partial(DelayApplyMonitor1, idx))


def DelayApplyMonitor1(idx):
    MUI.ShowWindow(1)
    Timer.addTimer(0.0, partial(DelayApplyMonitor2, idx))


def DelayApplyMonitor2(idx):
    monitors = MMyLighting.GetMonitorDetail()
    monitor = monitors[idx]
    monitor_base_x = monitor['left']
    monitor_base_y = monitor['top']
    width = monitor['width']
    height = monitor['height']
    MUI.SetWindowInfo(MType.IVec4(monitor_base_x, monitor_base_y, width, height))

    value = gpl.render_config.get('full_screen')
    if value == WindowType.FULL_SCREEN_WINDOW:
        SetFullScreen(WindowType.FULL_SCREEN_WINDOW)
    elif value == WindowType.FULL_SCREEN:
        SetFullScreen(WindowType.FULL_SCREEN)


def _apply_fps_level(value=None):
    if not value:
        value = gpl.render_config.get('fps_level')
    if value == 0:
        # 无限制
        genv.SetFrameLimit(0)
    elif value == 1:
        # 跟随显示器刷新率
        genv.SetFrameLimit(MMyLighting.GetMonitorDisplayFrequency())
    else:
        genv.SetFrameLimit(RefreshRate.get(value, 0))


def _apply_monitor_vsync(value=None):
    if not value:
        value = gpl.render_config.get('monitor_vsync')
    MEngine.EnableVsync(bool(value))
