# -*- coding: utf-8 -*-

"""
性能分级相关的代码

几个重要的概念：
# PerformanceLevel
    性能等级，对机器硬件性能的一个评价，目前分五档，等级越高，对应机器的性能越好
    * IOS 取值范围[LEVEL_1, LEVEL_2, LEVEL_3, LEVEL_4]（IOS的LEVEL_1专门为ip6, ip6p定制）
    * 安卓 取值范围[LEVEL_1, LEVEL_2, LEVEL_3]，比IOS低一档
    * PC/主机 固定取最高档 LEVEL_HIGHEST
"""
import math
import os
import re
import sys
import copy
import random

import MUI
import MEngine
import MRender
import MResource
import MConfig
import MPlatform
import MCharacter
import MSystem
import MEngine
import MShowRoom
import MObject
import MPhysics
import MStatistics
import MPatch
import MType
import MProfile

import GlobalData

from gshare import consts
from gshare import time_util
from gshare.utils import Singleton, enum, EnsureSafeStr
from gshare.consts import EPlatform

from gclient import cconst
from gclient import render_config
from gclient.config import LocalConfig
from gclient.util.decorators import cached_property
from gclient.data import performances_data, space_data, space_render_data, render_param_data
from gclient.framework.util.device_util import WindowDevice
from gclient.framework.util import model_category_data
from gclient.framework.util.graphics_util import ApplySingleRenderConfig
from gclient.framework.util.render_options import hall_render_options, battle_render_options
from gclient.framework.util.render_options.fixed_render_options import fixed_render_options, fixed_base_env_options, fixed_black_board_value
from gclient.framework.util import windows_util
from gclient.framework.util import events


class EThermalStatus(enum):
    """ 设备温度状态，直接用了Android API 里的枚举的值，"""
    # iOS：NSProcessInfoThermalState   Android: PowerManager.THERMAL_STATUS ...
    # 国内的Android机器好像没支持，海外的pixel可以，其他没有测。

    THERMAL_STATUS_NONE = 0         # 不支持、拿不到、没有温控
    THERMAL_STATUS_LIGHT = 1        # 正常
    THERMAL_STATUS_MODERATE = 2     # 热了
    THERMAL_STATUS_SEVERE = 3       # 高温， 过一段时间就降帧了（iOS下，Android没找到机器测）
    THERMAL_STATUS_CRITICAL = 4     # 要烧了
    # iOS 没有下面两个
    THERMAL_STATUS_EMERGENCY = 5    # 设备开始不正常
    THERMAL_STATUS_SHUTDOWN = 6     # 死机


class EPerformanceLevel(enum):
    """ 性能等级 """
    # android 最高需要比 iOS 低一档。比如 iOS 最高 level4，那Android最高就 level3
    LEVEL_0 = 0  # 极低，针对南美超低端的安卓机
    LEVEL_1 = 1
    LEVEL_2 = 2
    LEVEL_3 = 3
    LEVEL_4 = 4
    LEVEL_5 = 5
    LEVEL_6 = 6
    LEVEL_7 = 7
    LEVEL_8 = 8
    LEVEL_9 = 9
    LEVEL_HIGHEST = 10


class EGameQuality(enum):
    """ 画质选项 """
    LOW = 0
    MEDIUM = 1
    HIGH = 2


EResolution = [
    # 分辨率如果作为可选项提供给玩家的话，默认档以上 优先开启 FSR。
    # 比如某机型，当前默认 540P，则提供的高画质档就是 540P+FSR
    # 每一档提升 960 x 540 倍数（n）像素。所以公式是  (4/3 * y) ^2 = 960*540* n，求到 y 之后，按照16:9  的比例反算 x
    # 我们横屏游戏下面做分辨率缩放也只依赖 y，x 没用过，所以填个近似值就行
    # y =  math.sqrt(960*540* n) * 3 / 4; x = y * 16 / 9
    (640.0, 480.0),
    (960.0, 540.0),     # n = 1
    (1030.0, 580.0),    # n = 1.15
    (1120.0, 630.0),    # n = 1.4
    (1280.0, 720.0),    # n = 1.8 
    (1420.0, 820.0),    # n = 2.3
    (1600.0, 900.0),    # n = 2.8 移动端手机端全部限高 900P
    (1600.0, 990.0),    # n = 3.4
    # 移动端都不要默认 >= 1080，可以提供自选项
    (1920.0, 1080.0),   # n = 4
    (2130.0, 1200.0),   # n = 5
    (2350.0, 1320.0),   # n = 6
    (2560.0, 1440.0),   # n = 7
    (2710.0, 1520.0),   # n = 8
    (3035.0, 1700.0),   # n = 10
    (3325.0, 1870.0),   # n = 12
    (3590.0, 2020.0),   # n = 14
    (4096.0, 2160.0),   # n = 16   4K 档有生之年可见
]

EDefaultIOSResolution = {
    EPerformanceLevel.LEVEL_1: 1,
    EPerformanceLevel.LEVEL_2: 2,
    EPerformanceLevel.LEVEL_3: 3,
    EPerformanceLevel.LEVEL_4: 4,
    EPerformanceLevel.LEVEL_5: 5,
}

EDefaultAndroidResolution = {
    EPerformanceLevel.LEVEL_0: 1,
    EPerformanceLevel.LEVEL_1: 2,
    EPerformanceLevel.LEVEL_2: 3,
    EPerformanceLevel.LEVEL_3: 4,
}

EDefaultIOSParticleLOD = {
    EPerformanceLevel.LEVEL_1: 0,
    EPerformanceLevel.LEVEL_2: 1,
    EPerformanceLevel.LEVEL_3: 1,
    EPerformanceLevel.LEVEL_4: 2,
    EPerformanceLevel.LEVEL_5: 2,
}

EDefaultAndroidParticleLOD = {
    EPerformanceLevel.LEVEL_0: 0,
    EPerformanceLevel.LEVEL_1: 0,
    EPerformanceLevel.LEVEL_2: 1,
    EPerformanceLevel.LEVEL_3: 2,
}

EDefaultIOSMaxEffectCount = {
    EPerformanceLevel.LEVEL_1: 16,
    EPerformanceLevel.LEVEL_2: 32,
    EPerformanceLevel.LEVEL_3: 64,
    EPerformanceLevel.LEVEL_4: 64,
    EPerformanceLevel.LEVEL_5: 64,
}

EDefaultAndroidMaxEffectCount = {
    EPerformanceLevel.LEVEL_0: 16,
    EPerformanceLevel.LEVEL_1: 16,
    EPerformanceLevel.LEVEL_2: 32,
    EPerformanceLevel.LEVEL_3: 64,
}

EFpsLimit = {
    1: 30,
    2: 45,
    3: 60,
}
EFpsLimitPC = {
    1: 60,
    2: 144,
    3: 240,
}

ERenderLevelMap = {
    EPerformanceLevel.LEVEL_0: (0, 1, 2, 2),
    EPerformanceLevel.LEVEL_1: (1, 2, 2, 2),
    EPerformanceLevel.LEVEL_2: (2, 3, 4, 4),
    EPerformanceLevel.LEVEL_3: (2, 4, 5, 5),
    EPerformanceLevel.LEVEL_4: (2, 4, 5, 5),
    EPerformanceLevel.LEVEL_5: (3, 5, 6, 6),
    EPerformanceLevel.LEVEL_6: (3, 6, 6, 7),
    EPerformanceLevel.LEVEL_7: (3, 6, 6, 7),
    EPerformanceLevel.LEVEL_8: (3, 6, 6, 7),
    EPerformanceLevel.LEVEL_9: (3, 6, 6, 7),
    EPerformanceLevel.LEVEL_HIGHEST: (3, 6, 6, 7),
}


class ERenderOptionStage(enum):
    EMPTY = 0
    HALL = 1
    BATTLE = 2
    CALCULATION = 3


class PerformanceUtil(Singleton):

    # 相机的近裁面与远裁面
    CAMERA_NEAR_DEFAULT = 0.1
    CAMERA_NEAR_ON_AIRCRAFT = 1.0
    CAMERA_NEAR_SKYDRIVE = 0.3
    CAMERA_NEAR_PARACHUTE = 0.1
    CAMERA_NEAR_HALL_SPACE = 0.01
    CAMERA_NEAR_SWIM = 0.01

    CAMERA_FAR_DEFAULT = 20000    # 迈阿密有远景, 必须要2万才可以看到远景的山和云, 性能上相对于之前的3000Far, 差10万面
    CAMERA_FAR_ON_AIRCRAFT = 20000
    CAMERA_FAR_PARACHUTE = 20000
    CAMERA_FAR_HALL_SPACE = 20000

    # trunk PC因为没有代理，就调大FocusDistance模拟手机上的效果吧
    if MConfig.Platform == 'windows' and os.path.exists(os.path.join(MEngine.AppAssetPathUtf8, 'Package', 'Repository')):
        RENDER_FOCUS_DISTANCE_NORMAL = 500
    else:
        RENDER_FOCUS_DISTANCE_NORMAL = 250
    RENDER_FOCUS_DISTANCE_EXTRA = 0
    RENDER_FOCUS_DISTANCE_FLY = 1000
    RENDER_FOCUS_DISTANCE_FORCE_OPEN_PARACHUTE = 500

    def __init__(self):
        # 硬件的属性
        self.platform = MConfig.Platform
        self.device_info = MPlatform.GetDeviceInfo()
        self.gpu_info = MPlatform.GetGPUInfo()
        self.gpu_info_str = EnsureSafeStr(self.gpu_info)
        self.render_config = render_config._RenderConfig()
        self.screen_size = (MUI.GetScreenWidth(), MUI.GetScreenHeight())
        events.BatchAddListeners(self)
        self.thermal_status = MSystem.GetThermalStatus()
        self.is_astc_support = self.CheckASTCSupport()
        self.is_dynamicui_compress_support = False
        self.compress_texture_format = self.GetCompressTextureFormat()

        if self.platform == EPlatform.Windows:
            self.win_device = WindowDevice()
        from gclient.framework.util.graphics_art_style_util import GraphicsArtStyle
        self.art_style = GraphicsArtStyle()

        # 根据硬件计算出来的属性
        self.performance_level = EPerformanceLevel.LEVEL_3
        self.device_type = 0
        self.resolution_idx = 0
        self.target_resolution = None
        self.target_render_level = 3
        self.screen_scale = 1.0
        self.native_buffer_scale = 1.0
        self.only_render_ui = False
        self.shader_robot_render_level = -1
        self.in_hall = False
        self.global_particle_lod = 2
        self.current_stutter_count = 0
        self.render_option_stage = ERenderOptionStage.EMPTY
        self.is_inactive = False
        self.is_loading = False
        self.delay_deserialization_enabled = False
        self.sky_volumes = {}
        self.physics_tick_interval = 1 / 60.0
        self.profiler_enabled = False
        # [DEBUG]
        self.low_memory_random = None
        # [DEBUG]
        self.enable_dynamic_instancing = False if self.platform == EPlatform.Windows or random.random() < 0.2 else True
        # 大厅的RenderOptions
        self.final_render_options = {}
        # 记录设置过的RenderOptions,过滤掉赋相同的值
        self.render_option_records = {}

        # default_render_options融合了hall和battle的所有选项，用于无状态地一次性刷新所有RenderOption
        self.default_render_options = copy.copy(battle_render_options.battle_render_options)
        self.fixed_render_options = copy.copy(fixed_render_options)
        for key in hall_render_options.hall_render_options:
            if key not in self.default_render_options:
                if key in self.fixed_render_options:
                    self.default_render_options[key] = fixed_render_options[key]
                else:
                    self.default_render_options[key] = MRender.GetRenderOption(key)
        self.trees_lod0_distance = 25.0
        self.shader_profile_stat_used = {}      # {spaceno: {(shader, tech, profile): count}}

        # 本地的画质选项参数模板
        self.support_dlss = False

    @property
    def is_highest_performance(self):
        performance_level = self.performance_level
        if self.shader_robot_render_level >= 0:
            performance_level = self.shader_robot_render_level
        return performance_level >= EPerformanceLevel.LEVEL_3

    def CheckASTCSupport(self):
        astc_support = MConfig.Driver == 'vlk' or 'astc' in MPatch.GetTextureFormat()

        # 有些机器虽然查询出来支持 astc，实际上并不支持
        forbidden_list = [
            'vivo#V2111#',
        ]
        if astc_support:
            for device in forbidden_list:
                if self.device_info.startswith(device):
                    astc_support = False

        return astc_support

    def GetCompressTextureFormat(self):
        # format: Engine\Sources\Runtime\Core\Public\Resource\ResourceBase.h  EPixelFormat
        platform = self.platform
        texture_format = 3  # EPixelFormat_R8G8B8A8
        if platform == 'windows':
            texture_format = 25  # EPixelFormat_BC7
        elif platform == 'ios':
            texture_format = 38  # EPixelFormat_ASTC_5x5_LDR
        elif platform == 'android':
            if self.is_astc_support:
                texture_format = 38     # EPixelFormat_ASTC_5x5_LDR
            else:
                texture_format = 32     # EPixelFormat_ETC2_RGBA
        return texture_format
    
    def SetPerformanceLevel(self, performance_level):
        if self.performance_level == performance_level:
            return
        self.performance_level = performance_level
        genv.messenger.Broadcast(events.ON_PERFORMANCE_LEVEL_CHANGE, performance_level)

    def ActivateSomeDefaults(self):
        # PC/主机 固定取最高档 LEVEL_HIGHEST
        if self.platform in (EPlatform.Windows, EPlatform.Orbis, EPlatform.Switch, EPlatform.Macos):
            video_card_name = windows_util.VIDEO_CARD
            self.SetPerformanceLevel(windows_util.GetRenderLevelForVideoCard(video_card_name))
            self.support_dlss = windows_util.GetVideoCardSupportDLSS(video_card_name)
            print(f"performance_level={self.performance_level}")
            self.resolution_idx = 0
            self.global_particle_lod = 2

        elif self.platform == EPlatform.Android:
            self.SetPerformanceLevel(self._CalcAndroidPerformanceLevel(self.gpu_info))
            self.resolution_idx = EDefaultAndroidResolution[self.performance_level]
            self.global_particle_lod = EDefaultAndroidParticleLOD[self.performance_level]

            if self.performance_level >= EPerformanceLevel.LEVEL_2: # 如果是中端机,且是dpi太低(平板),那就给他加一点分辨率
                if self.screen_size[1] >= 1920:
                    ppi = MPlatform.GetDPI().x
                    if ppi <= 350:
                        add_idx = int((350 - ppi)/(350 - 244)* 3.0) # ppi 350(+0), ppi 244(+3)
                        self.resolution_idx += min(add_idx, 3)

        # ios根据芯片来
        elif self.platform == EPlatform.IOS:
            self.SetPerformanceLevel(self._CalcIOSPerformanceLevel(self.device_info))
            self.resolution_idx = EDefaultIOSResolution[self.performance_level]
            self.global_particle_lod = EDefaultIOSParticleLOD[self.performance_level]

            if 'ipad' in self.device_info.lower():  # ipad 屏幕大，PPI比手机低，需要拉高点默认分辨率
                if self.screen_size[1] > 1400:  # 高分辨率屏幕    https://www.ios-resolution.com/
                    # ppi 越高，表示原生屏幕清晰度越高，所以游戏渲染分辨率低一点也能满足效果
                    ppi = MPlatform.GetDPI().x
                    if ppi <= 264:  # ipad: 264
                        if self.screen_size[1] > 2000:  # ipad pro 12寸 屏幕太大了，还得提默认分辨率
                            self.resolution_idx += 9     # 1440p
                        else:
                            self.resolution_idx += 6     # 1080p
                    elif ppi <= 326:
                        self.resolution_idx += 2     # 720p

        # 未知平台
        else:
            self.SetPerformanceLevel(EPerformanceLevel.LEVEL_3)
            self.resolution_idx = 2
            self.global_particle_lod = 1

        # 根据机型给不同的默认值
        if self.performance_level <= EPerformanceLevel.LEVEL_1:
            self.default_game_quality_level = 1
            self.default_game_fps_limit = 2
            if self.performance_level <= EPerformanceLevel.LEVEL_0:
                self.default_game_quality_level = 0
                self.default_game_fps_limit = 1
            self.default_game_resolution_level = 0
        else:
            self.default_game_quality_level = 1
            self.default_game_resolution_level = 0
            if self.performance_level == EPerformanceLevel.LEVEL_HIGHEST:
                # Beadle: PC默认60帧，开VSync
                self.default_game_fps_limit = 3
            elif self.performance_level > EPerformanceLevel.LEVEL_3:
                self.default_game_fps_limit = 3
            else:
                self.default_game_fps_limit = 2
        
        # PC/主机默认使用调试配置
        if self.platform in (EPlatform.Windows, EPlatform.Orbis, EPlatform.Switch, EPlatform.Macos):
            self.default_game_quality_level = EGameQuality.HIGH
        
        # 默认画质
        if LocalConfig.game_quality_level is None:
            LocalConfig.game_quality_level = self.default_game_quality_level
        # 默认帧率
        if LocalConfig.game_fps_limit is None:
            LocalConfig.game_fps_limit = self.default_game_fps_limit
        # 默认分辨率
        if LocalConfig.game_resolution_level is None:
            LocalConfig.game_resolution_level = self.default_game_resolution_level
        # 默认枪械细节展示
        # 0, 1, 2 对应【关闭】【全部启用】【仅空投枪启用】
        if LocalConfig.gunparts_model_detail_show is None:
            if self.performance_level <= EPerformanceLevel.LEVEL_0:
                LocalConfig.gunparts_model_detail_show = 0
            elif self.performance_level <= EPerformanceLevel.LEVEL_1:
                LocalConfig.gunparts_model_detail_show = 2
            else:
                LocalConfig.gunparts_model_detail_show = 1
        elif isinstance(LocalConfig.gunparts_model_detail_show, bool):
            if LocalConfig.gunparts_model_detail_show:
                if self.performance_level <= EPerformanceLevel.LEVEL_1:
                    LocalConfig.gunparts_model_detail_show = 2
                else:
                    LocalConfig.gunparts_model_detail_show = 1
            else:
                LocalConfig.gunparts_model_detail_show = 0
        # 默认第一人称动画细节
        # 0, 1 对应【关闭】【开启】
        if LocalConfig.fps_animation_detail is None:
            if self.performance_level <= EPerformanceLevel.LEVEL_1:
                LocalConfig.fps_animation_detail = 0
            else:
                LocalConfig.fps_animation_detail = 1

    def Activate(self):
        print('Performance Activate')

        # 初始化的时候切换一下全屏, 其他时候就不要去弄了
        from gclient.framework.util import graphics_util
        graphics_util._apply_full_screen()

        self.ActivateSomeDefaults()

        self.ApplyGlobalParticleLOD(self.global_particle_lod)
        self.RefreshMaxEffectCount()
        self.ApplyRenderOptions()

        # 温度监控(ios11+, android 10+)
        MSystem.SetThermalStatusChangedCallback(self._OnThermalStatusChanged)

    def SetRenderOption(self, key, value):
        try:
            MRender.SetRenderOption(key, value)
            self.render_option_records[key] = value
        except:
            MRender.WriteOnBlackBoard(key, value)
            self.render_option_records[key] = value

    def WriteOnBlackBoard(self, key, value):
        MRender.WriteOnBlackBoard(key, value)
        self.render_option_records[key] = value

    def ApplyGlobalParticleLOD(self, lod):
        self.global_particle_lod = lod
        MRender.SetGlobalParticleLOD(lod)

    def ApplyGlobalMaxEffectCount(self, count):
        MCharacter.SetMaxEffectCount(count)

    def IsEnableDynamicUI(self):
        forbidden_gpu = (
            'Adreno (TM) 405',      # motorola#Moto G (4)#
            'Adreno (TM) 418',      # motorola#XT1572
            'Adreno (TM) 530',      # 小米5
            'Adreno (TM) 505',      # vivo 1603
            'Adreno (TM) 506',      # Xiaomi#MI MAX 2
            'Adreno (TM) 510',      # vivo#vivo V3Max
            'PowerVR Rogue GM9446',       # Oppo Reno5 F
            'PowerVR B-Series BXM-8-256',   # vivo#V2219A
        )

        forbidden = self.gpu_info in forbidden_gpu
        if forbidden:
            return False
        forbidden_device = (
            'samsung#SM-A235F#',        # Adreno (TM) 610
            'samsung#SM-N910V#',        # Adreno (TM) 420
            'Sony#G8141#',              # Adreno (TM) 540
            'Sony#G8341#',              # Adreno (TM) 540
            'LGE#LG-H930#',             # Adreno (TM) 540
            'LGE#LG-LS998#',            # Adreno (TM) 540
        )
        for device in forbidden_device:
            if self.device_info.startswith(device):
                forbidden = True
                break

        return not forbidden

    def ApplyDynamicUIMergeTexture(self):
        # ui 运行时动态合图
        return  # todo: 2024 引擎还没merge us 专用的 合图规则
        self.is_dynamicui_compress_support = self.IsEnableDynamicUI()
        platform = self.platform
        texture_format = 3  # EPixelFormat_R8G8B8A8
        if platform == 'windows':
            width = 2048
            height = 2048
        elif platform == 'ios':
            width = 2048
            height = 2048
        elif platform == 'android':
            width = 2048
            height = 2048
        else:
            return
        if self.is_dynamicui_compress_support:
            texture_format = self.compress_texture_format
            if genv.EngineFeature(cconst.ENGINE_VERSION.V202401):
                single_texture_max_width = 512
                single_texture_max_height = 512
                MUI.SetAtlasInfo(width, height, texture_format, single_texture_max_width, single_texture_max_height,
                                 True)
            else:
                MUI.SetAtlasInfo(width, height, texture_format)

    def EnableUITextMerge(self, enable):
        if genv.EngineFeature(cconst.ENGINE_VERSION.V202302):
            return
        gui.director.enableUITextMerge(enable)

    def ApplySavePipelineCache(self):
        if MConfig.Driver != 'vlk':
            return
        if not genv.EngineFeature(cconst.ENGINE_VERSION.V202302_01):
            return 
        enable = True
        #
        # # 有些手机的GPU vlk 驱动貌似不支持这个特性
        # forbidden_device = [
        #     'samsung#SM-A035',  # Mali-G57
        #     'INFINIX#Infinix X6817',  # Mali-G52
        # ]
        # for device in forbidden_device:
        #     if self.device_info.startswith(device):
        #         enable = False
        #         break

        enable and MEngine.SavePipelineCache()

    def ApplyRenderOptions(self):
        # 如果是旧的方式生成ShaderCache，Fallback Shader就一定会在ShaderCache里，此时启用FallbackShader可以有效减少物体闪一下的问题
        # 但我们项目是minimal ShaderCache + 变种收集，FallbackShader大概率不在ShaderCache里，所以开了反而会有更高的消耗
        MRender.EnableFallbackShader(False)

        self.WriteOnBlackBoard("EnableHudSceneCopy", False)

        # 防止车辆把人撞到地底
        if hasattr(MPhysics, "SetPreventCCTUnderGround"):
            MPhysics.SetPreventCCTUnderGround(True)

        # PrimitiveAOI没必要每帧做
        self.WriteOnBlackBoard("PrimitiveAOIInterval", 10)

        # 暂时关闭全屏拷贝优化，息屏回来有BUG
        self.WriteOnBlackBoard("EnableCopyScreen", True)

        # 最大点光数量
        # 枪匠界面透明材质需要用到
        MRender.SetMaxForwardLights(3)

        # 关掉距离排序（安卓默认是开的，关掉可以高两帧）
        self.SetRenderOption("EnableSortOpaque", False)

        # # 卡顿优化，每帧不超过1ms
        # MObject.SetMaxQueuingTaskTimePerFrame(0.001)

        # 物理Tick间隔
        self.physics_tick_interval = 1.0 / 60.0
        MPhysics.SetPhysicsStepDelta(self.physics_tick_interval)

        # # 关掉粒子的Occlus
        # self.WriteOnBlackBoard("EnableParticleOcclus", False)

        if hasattr(MRender, "SetTextureMipLevelOffsets"):
            from gclient.util import texture_mip_offsets  # noqa
            MRender.SetTextureMipLevelOffsets(texture_mip_offsets.data)

        # PC新引擎优化下Shader编译卡顿
        if self.platform == EPlatform.Windows:
            if hasattr(MRender, "EnableFeedMaterialShader"):
                MRender.EnableFeedMaterialShader(False)
            # if hasattr(MRender, "EnableFeedElementShader"):
            #     MRender.EnableFeedElementShader(False)

        # 低内存机器，物理加载范围要改小一点
        if self.is_extreme_low_memory:
            MPhysics.SetIsOverrideLoadRange(True)
            MPhysics.SetDefaultFocusHalfChunkSize(4)
            MPhysics.SetDefaultChunkEdgeLength(50)

        # IWorld._Update_on_ot限帧
        if hasattr(MEngine, "SetScenarioFrameLimit"):
            MEngine.SetScenarioFrameLimit(10)
        self.WriteOnBlackBoard("ScenarioUpdateInterval", 0.1)

        self.WriteOnBlackBoard("VolumeTreeUpdateThreshold", 1.0)
        self.WriteOnBlackBoard("LodSpaceUpdateThreshold", 1.0)

        # 动态合批
        if hasattr(MObject, "SetEnableDynamicBatch"):
            MObject.SetEnableDynamicBatch(True)
        if hasattr(MObject, "SetDynamicBatchInterval"):
            # IWorld._Update_on_ot限帧会影响动态合批的帧率，所以要相应调整
            if hasattr(MEngine, "SetScenarioFrameLimit"):
                MObject.SetDynamicBatchInterval(5)
            else:
                MObject.SetDynamicBatchInterval(30)

        # 暂时只根据performance_level来决定FocusDistance，因为根据画质来的话，局内跳伞的时候换画质会有问题
        if self.RENDER_FOCUS_DISTANCE_NORMAL < 500:
            min_level = EPerformanceLevel.LEVEL_2
            max_level = EPerformanceLevel.LEVEL_4
            clamp_level = min(max(self.performance_level, min_level), max_level)
            self.RENDER_FOCUS_DISTANCE_NORMAL = 250 + (50.0 / (max_level - min_level)) * (clamp_level - min_level)
        self.SetRenderOption('FocusDistance', self.RENDER_FOCUS_DISTANCE_NORMAL)
        self.default_render_options["FocusDistance"] = self.RENDER_FOCUS_DISTANCE_NORMAL

        # iphone6等低内存机器，飞机上和跳伞时就不要扩大FocusDistance了
        if self.is_extreme_low_memory:
            self.RENDER_FOCUS_DISTANCE_FLY = 250
            self.RENDER_FOCUS_DISTANCE_FORCE_OPEN_PARACHUTE = 250
        # LEVEL1的机器，飞机上的视距限制为750
        elif self.performance_level <= EPerformanceLevel.LEVEL_1:
            self.RENDER_FOCUS_DISTANCE_FLY = 750
            self.RENDER_FOCUS_DISTANCE_FORCE_OPEN_PARACHUTE = 350

        self.ApplyBatchRenderOption(self.default_render_options)

        # 根据玩家的【画质】选项来配置RenderOptions
        self.ApplyBattleSpaceRenderOptions()
        # 应用新的画质设置，目前先设置窗口全屏/分辨率/渲染比例
        self.ApplyRenderConfig()

        # CSM的拍摄盒子，有多大，引擎默认以相机为中心向下拓深100米，这里调大一点，减少高空时穿帮情况
        self.WriteOnBlackBoard("CSM_ViewBoxForward", MType.Vector4(0, 300, 300, 300))

        # 文字动态合批
        self.EnableUITextMerge(True)

        # 开启cProfile卡顿检测
        if random.random() < 0.001:
            self.StartProfile()

    def SetDelayDeserialization(self, value):
        value = bool(value)
        if value == self.delay_deserialization_enabled:
            return
        self.delay_deserialization_enabled = value
        MEngine.GetGameplay().Scenario.DelayDeserialization = value

    def EnableShaderHttpDownload(self, enable):
        if not hasattr(MRender, "EnableShaderHttpFetch"):
            return
        if not enable:
            MRender.EnableShaderHttpFetch(False)
            return

        from common.mobilecommon import asiocore  # noqa
        from gshare.s18_microservices import S18Microservices  # noqa

        svc = S18Microservices.instance().GetService('shader')
        headers = svc.ClientSessionAuth({}, "")
        MRender.SetShaderHttpFetchHeader(headers)
        self.shader_http_pool = asiocore.bhttp_client_pool(svc.host, svc.port, 1)
        if svc.usessl:
            callback = self.shader_http_pool.get_https_post_method()
        else:
            callback = self.shader_http_pool.get_http_post_method()
        MRender.SetShaderHttpFetchData(callback, "/shader/pub/upload", "/shader/pub/download")
        MRender.EnableShaderHttpFetch(enable)


    def IsDelayDeserialization(self):
        return self.delay_deserialization_enabled

    def ConfigParticleTinyCulling(self, level=0):
        # 自动剔除距离相机较远，且体积较小的粒子
        if not hasattr(MRender, "EnableTinyCulling"):
            return
        if level <= 1:
            MRender.EnableTinyCulling(25.0, 0.025)
        elif level <= 3:
            MRender.EnableTinyCulling(25.0, 0.01)
        else:
            MRender.DisableTinyCulling()

    def PostprocessRenderOption_MSAA(self, enable):
        # Beadle: 安卓开MSAA会卡死，暂时先关了
        # # 有些渲染特性需要硬件支持，这里后处理一下
        # self.SetRenderOption('EnableMSAA', enable)

        # iOS 9 开始完整支持 MSAA
        # https://developer.apple.com/metal/Metal-Feature-Set-Tables.pdf
        if self.platform == EPlatform.IOS:
            generation = re.findall(r"Apple A(\d+) GPU", self.device_info)
            if generation and int(generation[0]) < 9:
                self.SetRenderOption('EnableMSAA', False)

    def _OnThermalStatusChanged(self, status):
        self.thermal_status = status
        if status >= EThermalStatus.THERMAL_STATUS_SEVERE:
            # todo: 温度太高, 动态分辨率、降帧，提示一下玩家不要充电玩之类的
            pass

    def _GetGPULevel(self, gpu_info):
        nums = re.findall(r"\d+\d*", gpu_info)
        return int(nums[0]) if nums else -1

    def _CalcAndroidAdrenoLevel(self, gpu_info):
        # https://en.wikipedia.org/wiki/Adreno
        gpu_level = self._GetGPULevel(gpu_info)
        print('Adreno gpu_level:', gpu_level, 'gpu_info:', gpu_info)
        if gpu_level > 700: # Adreno 730, 比 苹果 A13 厉害的
            return EPerformanceLevel.LEVEL_3
        elif gpu_level <= 405: # Adreno 405平均只有24帧，打回LEVEL0
            return EPerformanceLevel.LEVEL_0
        elif gpu_level <= 610: # 比 苹果 A10 差的
            return EPerformanceLevel.LEVEL_1

        return EPerformanceLevel.LEVEL_2    # 剩下的全中端

    def _CalcAndroidMaliLevel(self, gpu_info):
        # https://en.wikipedia.org/wiki/Mali_(GPU)
        gpu_level = self._GetGPULevel(gpu_info)
        print('Mali gpu_level', gpu_level, 'gpu_info', gpu_info)
        gpu_info_lower = gpu_info.lower()
        if 'mali-g' in gpu_info_lower:    # G 系列
            if gpu_level > 100:
                # 2021 年开始，版本号变成3位数
                if gpu_level > 700:     # Mali-G710 ,比 苹果 A13 厉害的
                    return EPerformanceLevel.LEVEL_3
                else:
                    return EPerformanceLevel.LEVEL_2    # 剩下的全中端
            else:
                # 两位数版本号的
                if gpu_level > 76:  # 77 78
                    return EPerformanceLevel.LEVEL_3
                elif gpu_level < 57:
                    return EPerformanceLevel.LEVEL_1
                else:
                    return EPerformanceLevel.LEVEL_2

        elif 'mali-t' in gpu_info_lower:    # T 系列
            # T820，T830，虽然数字比T760大，但是性能却奇差无比
            if gpu_level in (820, 830):
                return EPerformanceLevel.LEVEL_0
            if gpu_level < 760:
                return EPerformanceLevel.LEVEL_0
            else:
                return EPerformanceLevel.LEVEL_1
        else:
            return EPerformanceLevel.LEVEL_0    # 上古系列

    def _CalcAndroidPowervrLevel(self, gpu_info):
        # https://en.wikipedia.org/wiki/PowerVR
        gpu_level = self._GetGPULevel(gpu_info)
        print('PowerVR gpu_level', gpu_level, 'gpu_info', gpu_info)
        gpu_info_lower = gpu_info.lower()
        if 'a-series' in gpu_info_lower:
            return EPerformanceLevel.LEVEL_2
        elif 'b-series' in gpu_info_lower:  # PowerVR B-Series BXM-8-256
            return EPerformanceLevel.LEVEL_2
        else:
            if gpu_level < 8320:
                return EPerformanceLevel.LEVEL_0
            if gpu_level < 8500:
                return EPerformanceLevel.LEVEL_1
            if gpu_level < 10000:
                return EPerformanceLevel.LEVEL_2

        return EPerformanceLevel.LEVEL_1

    def _GetAndroidCpuInfo(self):
        try:
            cur_dir = '/sys/devices/system/cpu/'
            cpu_info = {}
            for name in os.listdir(cur_dir):
                if not name.startswith('cpu'):
                    continue
                cpu_num = name[3:]
                if not cpu_num.isdigit():
                    continue
                cpu_num = int(cpu_num)
                max_freq_file = '/sys/devices/system/cpu/%s/cpufreq/cpuinfo_max_freq' % name
                if not os.path.exists(max_freq_file):
                    # 大核这个时候还没有启动
                    continue
                freq = int(open(max_freq_file, 'r').read())
                # 模糊化, 排除精度问题
                freq = freq / 100 * 100
                if cpu_info.get(cpu_num, 0) == freq:
                    continue
                cpu_info[cpu_num] = freq
            return cpu_info
        except:
            return {}

    def _CalcAndroidBigCoreFrequency(self):
        cpu_info = self._GetAndroidCpuInfo()
        if not cpu_info:
            return 0
        big_core_indices = sorted(list(cpu_info.keys()))[int(len(cpu_info) // 2):]
        big_cores = {index: cpu_info[index] for index in big_core_indices}
        return int(sum(list(big_cores.values())) / len(big_cores))

    def _CalcAndroidPerformanceLevel(self, gpu_info):
        # Todo: 根据iOS那边的芯片分档，查移动端GPU天梯榜反推Android的挡位
        # 比 苹果 A13 厉害的，就是 Android 高端， 比 苹果 A10 差的就是Android 低端，剩下的都是中端
        if MConfig.Driver == 'es2':
            return EPerformanceLevel.LEVEL_0

        # 先根据GPU大致划分一下
        level = EPerformanceLevel.LEVEL_2
        if "Adreno" in gpu_info:
            try:
                level = self._CalcAndroidAdrenoLevel(gpu_info)
            except Exception:
                sys.excepthook(*sys.exc_info())
        elif "Mali" in gpu_info:
            try:
                level = self._CalcAndroidMaliLevel(gpu_info)
            except Exception:
                sys.excepthook(*sys.exc_info())
        elif "PowerVR" in gpu_info:
            try:
                level = self._CalcAndroidPowervrLevel(gpu_info)
            except Exception:
                sys.excepthook(*sys.exc_info())

        # LEVEL2的机器如果CPU频率很低，降为LEVEL1
        if level == EPerformanceLevel.LEVEL_2:
            frequency = self._CalcAndroidBigCoreFrequency()
            if not frequency:
                return level
            if frequency < 2000000:
                level = EPerformanceLevel.LEVEL_1

        return level

    def _CalcIOSPerformanceLevel(self, device_info):
        # 古董机，芯片是SGX开头的
        if re.findall(r"SGX \d+", device_info):
            return EPerformanceLevel.LEVEL_1
        # null也是古董机
        if re.findall(r"#null", device_info):
            return EPerformanceLevel.LEVEL_1

        generation = re.findall(r"Apple A(\d+) GPU", device_info)
        # 找不到就意味着引擎都没覆盖到的新机型，新机型配置一般都比较高，默认来最高档把
        if not generation:
            # 找不到的，一般都是最新的iphone或者最新的ipad，如果是ipad就默认给到LEVEL5，走更高清的画质
            if "ipad" in device_info.lower():
                return EPerformanceLevel.LEVEL_5
            else:
                return EPerformanceLevel.LEVEL_4

        # 根据根据芯片来，A11(iphone8 iphoneX这一代) 以后就算高端， A11 对应metal2 ，A13 GPU新增更多特性，方便以后效果分级
        # https://developer.apple.com/videos/play/tech-talks/602
        # https://developer.apple.com/videos/play/tech-talks/608

        try:
            generation = int(generation[0])
            if generation < 9:  # A8
                # iphone6 && iphone6p 只有1G的机器，暂且归为LEVEL_1吧
                return EPerformanceLevel.LEVEL_1
            elif generation < 11:   # A9 A10  iphone6s iphone7
                return EPerformanceLevel.LEVEL_2
            elif generation < 13:   # A11 A12   iPhone8 iphoneX, iphoneXS iphoneXR
                return EPerformanceLevel.LEVEL_3
            else:   # A13+ iphone 11 开始
                return EPerformanceLevel.LEVEL_4
        except Exception:
            sys.excepthook(*sys.exc_info())
            return EPerformanceLevel.LEVEL_3

    def ApplyHallPerformance(self):
        print('ApplyHallPerformance')
        self.ApplyHallSpaceRenderOptions()

        import gc
        gc.enable()
        gc.collect()

    def ApplyBattlePerformance(self):
        print('ApplyBattlePerformance')
        self.ApplyBattleSpaceRenderOptions()

        import gc
        gc.disable()

    def ApplyFixedRenderOptionOverlay(self):
        print("applying fixed render options ---------------------------------")
        self.ApplyBatchRenderOption(fixed_render_options)
        current_world = MEngine.GetGameplay().Scenario.ActiveWorld
        for key, value in fixed_black_board_value.items():
            self.WriteOnBlackBoard(key, value)
        if current_world:
            current_base_env_volume = current_world.EnvVolume
            if current_base_env_volume:
                for key, value in fixed_base_env_options.items():
                    print(f"setting world {current_world.GetName()} {key} to {value}")
                    setattr(current_base_env_volume, key, value)
            else:
                print(f"no valid env for {current_world}")
        else:
            print(f"not entered world for {self}")
        # todo: change it to be more flexible
        MResource.ToggleBudgetSupport(True)
        self.GetGpuMemory()

    def ApplyScreenScale(self):
        from gclient.framework.util import graphics_util

        if genv.is_in_hall:
            render_scale_slider = hall_render_options.HALL_SCREEN_SCALE
        else:
            super_resolution = self.render_config.get('super_resolution')  # 超分类型
            super_resolution_level = self.render_config.get('super_resolution_level')  # 超分档位
            screen_scale_slider = self.render_config.get('screen_scale_slider')  # 渲染比例
            if super_resolution == 0:
                render_scale_slider = screen_scale_slider
            else:
                render_scale_slider = graphics_util.SuperResolutionRenderScale.get(super_resolution_level)
            render_scale_slider /= 100.0
        render_scale = self.render_config.get('render_scale') / 100.0
        self.screen_scale = render_scale_slider * render_scale
        print('[ScreenScale] set --> ', self.screen_scale)
        self.SetRenderOption("ScreenScale", self.screen_scale)

    def GetGpuMemory(self):
        if MConfig.Platform != 'windows':
            return
        windows_util._GetGpuMemory()
        gpu_mem = windows_util.GPU_MEMORY
        if not gpu_mem:
            return
        print(f"gpu mem: {gpu_mem}, set texture budget to {gpu_mem * 0.6}")
        budget: int = int(gpu_mem * 0.6)
        MResource.SetTextureBudget(budget * 1024 * 1024, budget * 128 * 1024)  # 设置贴图阈值为 gpu mem * 0.6 ，波动上限为 gpu mem * 0.03

    def ApplyPostRenderOptionOverlay(self, parent_config_name):
        print("applying post render options ---------------------------------")
        shadow_level_value = getattr(LocalConfig, f"{parent_config_name}_shadow_level")
        current_world = MEngine.GetGameplay().Scenario.ActiveWorld
        if current_world:
            current_base_env_volume = current_world.EnvVolume
            if current_base_env_volume:
                if shadow_level_value <= 2:
                    current_base_env_volume.CascadeDistributionExponent = 2.5
                    current_base_env_volume.CascadeTransitionFraction = max(current_base_env_volume.CascadeTransitionFraction, 0.25)
            else:
                print(f"no valid env for {current_world}")
        else:
            print(f"not entered world for {self}")

    @events.ListenTo(events.ON_SCREEN_SIZE_CHANGED)
    def TryAutoScreenScale(self):
        print("**********Auto Screen Scale**********")
        userHasChangedScreenScale = getattr(LocalConfig, 'default_config_user_manual_screen_scale', 0)
        print(f"userHasChangedScreenScale{userHasChangedScreenScale}")
        if userHasChangedScreenScale:
            print("User has changed screen scale manually")
            userScreenScale = getattr(LocalConfig, 'default_config_user_manual_screen_scale', 100)
            self.ApplyScreenScale()
            return

        width = float(MUI.GetScreenWidth())
        height = float(MUI.GetScreenHeight())
        if width == 0 or height == 0:
            print('Warning: screen width / height is 0, would not apply auto render scale.')
            return
        screenScale = float(MRender.GetRenderOption("ScreenScale"))
        default_res = 720
        if genv.space:
            map_name = space_data.data[genv.space.spaceno]['map']
            if map_name == 'L_WeaponVFXDisplay' or map_name == 'L_WeaponShootingRange' or map_name == 'L_MainHall':
                default_res = 1440
        # print(f"default_res{default_res}")
        pixelCount = float(default_res * default_res * 16 / 9)
        # print(f"px_count{pixelCount}")
        newScreenScale = min(max(math.sqrt(pixelCount / float(width * height)), 0.18), 2.0)
        # print(f"width{width}")
        # print(f"newScreenScale{newScreenScale}")
        intlized = int(newScreenScale * 100)
        if not intlized == int(screenScale * 100):
            setattr(LocalConfig, 'default_config_screen_scale_slider', intlized)
            setattr(LocalConfig, 'hall_config_screen_scale_slider', intlized)
            self.final_render_options['screen_scale_slider'] = intlized
            self.ApplyScreenScale()

    def ApplyBatchRenderOption(self, render_options):
        if gui.is_mobile:       # todo 暂时暴力 hook 一下
            render_options = copy.copy(render_options)
            render_options.update(battle_render_options.mobile_battle_render_options)
        for key, value in render_options.items():
            self.SetRenderOption(key, value)

    def ApplyHallSpaceRenderOptions(self):
        """ 应用大厅的渲染选项 """
        print("Performance [ApplyHallSpaceRenderOptions]")

        self.in_hall = True
        self.render_option_stage = ERenderOptionStage.HALL

        map_name = space_data.data[8]['map']
        render_options = copy.copy(performances_data.data.get(map_name, {}))
        render_options.pop('ScreenScale', None)
        if not render_options:
            render_options = copy.copy(self.default_render_options)
        
        self.ApplyBatchRenderOption(render_options)

        self.TryAutoScreenScale()

        # 大厅开启软骨
        MCharacter.EnableSoftBone(True)

        parent_config_name = LocalConfig.GetHallConfigName()
        self.SettingDefaultRenderLevel(parent_config_name)      # 应用默认设定

        self.ApplyBatchRenderOption(hall_render_options.hall_render_options)

    def ApplyBattleSpaceRenderOptions(self):
        """ 应用战斗的渲染选项 """
        print("Performance [ApplyBattleSpaceRenderOptions]")
        if not genv.space:
            return

        self.in_hall = False
        self.render_option_stage = ERenderOptionStage.BATTLE

        self.TryAutoScreenScale()

        # 战斗内关闭软骨
        if self.performance_level <= EPerformanceLevel.LEVEL_1:
            MCharacter.EnableSoftBone(False)

        # 场景编辑器里激活游戏，默认打开某些开关
        if GlobalData.IsSceneEditor:
            self.ApplyBatchRenderOption({
                "EnableSunLightShadow": True,
                "ShadowMapSize": 2048,
                "EnableAmbientOcclusion": False,
            })
            return

        self.art_style.SetArtStyle(LocalConfig.graphics_art_style)  # 大厅关闭 ColorGrading

        # 关闭景深
        self.EnableDof(False)
        self.WriteOnBlackBoard("MrGuisBokeh", False)

        parent_config_name = LocalConfig.GetDefaultConfigName()
        self.SettingDefaultRenderLevel(parent_config_name)      # 应用默认设定
        self.ApplyBattleSpaceRenderOptionsOverlay()
        self.ApplyFixedRenderOptionOverlay()
        self.ApplyPostRenderOptionOverlay(parent_config_name)

    def ApplyRenderConfig(self):
        from gclient.framework.util import graphics_util
        graphics_util.ApplyRenderConfig()

    def ApplyBattleSpaceRenderOptionsOverlay(self):
        self.ApplyBatchRenderOption({
            'AMDFSRUseExplicitPrevDepth': False,        # fix: 3P 枪械残影
            'AMDFSRDepthClipTolerance': 0.95,
            'AMDFSRDepthClipToleranceFoliage': 4,
            'CastShadowPointLights': 0,
            'CastShadowSpotLights': 0,
        })

    def ApplyBattleSpaceRenderOptionsAfterLoaded(self):
        """ 应用战斗的渲染选项 """
        print("Performance [ApplyBattleSpaceRenderOptionsAfterLoaded]")

        # 做一下保护，防止结算界面的RenderOption被带进战斗
        if self.render_option_stage != ERenderOptionStage.BATTLE:
            self.ApplyBattleSpaceRenderOptions()

        # 设置一下树的Billboard距离
        space = genv.space
        if space and space.can_modify_billboard_distance:
            distance = float(MRender.GetRenderOption("BillboardDistance"))
            self.SetTreesBillboardDistance(distance)
        else:
            billboardDistance = 80
            self.SetRenderOption("BillboardDistance", billboardDistance)
            self.final_render_options["BillboardDistance"] = billboardDistance

        # 低端机，隐藏草，删除所有雨帘
        if self.performance_level <= EPerformanceLevel.LEVEL_1:
            self.SetGrassVisible(False)
            self.RemoveRainEntities()
            self.RemovePosunEffects()

        # 最低端机，隐藏石头的Overlay
        if self.performance_level <= EPerformanceLevel.LEVEL_0:
            self.SetStoneOverlayEnable(False)

        # 开了投影，就要石头的Lod1就要去掉了，否则会有各种黑块问题
        if self.final_render_options.get("EnableSunLightShadow"):
            self.SetStoneLod1Distance(80)
        else:
            self.SetStoneLod1Distance(25)

        # 刷新下地形LOD1的距离
        self.RefreshTerrainLod1Distance()

        # 刷新恶魔岛的水体
        self.RefreshShowWater()

        # 刷下环境球
        self.RefreshCubeEnv()

        # 刷新一下Candela，ShadowMap等
        if genv.EngineFeature(cconst.ENGINE_VERSION.V202303):
            MRender.ForceRefreshStaticMaps()

        # 根据机型等级，调整代理距离
        if self.performance_level >= EPerformanceLevel.LEVEL_HIGHEST:
            self.ExtendProxyDistances(75.0)
        elif self.performance_level >= EPerformanceLevel.LEVEL_4:
            self.ExtendProxyDistances(25.0)

    def ApplyGunSmithWindowRenderOptions(self):
        print("Performance [ApplyGunSmithWindowRenderOptions]")
        self.ApplyBatchRenderOption({
            "EnableSunLightShadow": False if self.performance_level <= EPerformanceLevel.LEVEL_1 else True,
            "ShadowMapSize": 2048,
            "CSM_Distance1": 1.5,
            "CSM_SlopBias1": 0.001,
            "CSM_Bias1": 0.00001 if self.platform in (EPlatform.Android, EPlatform.IOS) else 0,
            # todo: replace this with table
            "EnableEnhancedFog": False,
        })

    def ApplyCalculationRenderOptions(self):
        print("Performance [ApplyCalculationRenderOptions]")
        if gpl.performance_level <= 2:
            return

        self.render_option_stage = ERenderOptionStage.CALCULATION
        render_options = copy.copy(self.default_render_options)
        render_options.update(hall_render_options.hall_render_options)
        
        self.ApplyBatchRenderOption(render_options)

        MRender.SetMaxForwardLights(1)

    def StartProfile(self, interval=3.0):
        if self.profiler_enabled:
            return
        from gclient.util import profile_util  # noqa
        profile_util.StartProfile(interval)
        self.profiler_enabled = True

    def StopProfile(self):
        if not self.profiler_enabled:
            return
        from gclient.util import profile_util  # noqa
        profile_util.StopProfile()
        self.profiler_enabled = False

    def NotifyProfileInactive(self, inactive):
        if not self.profiler_enabled:
            return
        from gclient.util import profile_util  # noqa
        profile_util.Context.inactive = inactive

    def RefreshTerrainLod1Distance(self):
        level = self.target_render_level
        # 刷新下地形LOD1的距离
        if level <= 0:
            self.SetTerrainLod1Distance(0.0)
        elif level <= 1:
            self.SetTerrainLod1Distance(20.0)
        elif level <= 3:
            self.SetTerrainLod1Distance(100.0)
        elif level <= 5:
            self.SetTerrainLod1Distance(150.0)
        else:
            self.SetTerrainLod1Distance(250.0)

    def RefreshTerrainGeometryFactor(self):
        level = self.target_render_level
        # 刷新下地形LOD1的距离
        if level <= 5:
            self.SetTerrainGeometryFactor(-1.0)
        elif level <= 6:
            self.SetTerrainGeometryFactor(0.0)
        else:
            self.SetTerrainGeometryFactor(1.0)

    def RefreshShowWater(self):
        space = genv.space
        if not space:
            return
        if not space.world:
            return
        world_name = space.real_world_name

        if world_name.startswith("SI_Island"):
            show_sspr_water = False
            if self.target_render_level >= 7:
                show_sspr_water = True

            for e in space.world.Levels['$root'].RootArea.Entities:  # 延迟反序列化, 只能遍历$root Level
                name = e.GetName()
                if 'water' in name:
                    if 'high' in name:
                        e.IsVisible = show_sspr_water
                    else:
                        e.IsVisible = not show_sspr_water

    def RefreshCubeEnv(self):
        space = genv.space
        if not space:
            return
        if not space.world:
            return
        world_name = space.real_world_name

        forward_enabled = not self.final_render_options.get("EnableDeferredRendering", False)
        forward_cube = space.GetSceneEntity("$root", "Forward_CubeEnv")  # 延迟反序列化, 只能遍历$root Level
        if forward_cube and forward_cube.EnvVolume:
            forward_cube.EnvVolume.Enable = forward_enabled

        cubemap_1 = space.GetSceneEntity("$root", "cubemap_1")
        cubemap_high = space.GetSceneEntity("$root", "cubemap_high")
        if forward_enabled:  # 非极致
            if cubemap_high and cubemap_high.ReflectionProbe:
                cubemap_high.ReflectionProbe.Enable = False
            if cubemap_1 and cubemap_1.ReflectionProbe:
                cubemap_1.ReflectionProbe.Enable = True
        else:  # 极致
            if cubemap_high and cubemap_high.ReflectionProbe:
                cubemap_high.ReflectionProbe.Enable = True
                cubemap_high.ReflectionProbe.IsDefault = False
            if cubemap_1 and cubemap_1.ReflectionProbe:
                cubemap_1.ReflectionProbe.Enable = True
                cubemap_1.ReflectionProbe.IsDefault = True

        if world_name.endswith('_high'):
            enable_hdr = True
        else:
            enable_hdr = False
        if enable_hdr or genv.EngineFeature(cconst.ENGINE_VERSION.V202401):
            self.SetRenderOption('EnabledHdrLighting', True)
        else:
            self.SetRenderOption('EnabledHdrLighting', False)

    def ExtendProxyDistances(self, extended_distance):
        if self.IsDelayDeserialization():
            return
        volumes = []
        for name, level in genv.space.world.Levels.items():
            if name.startswith("root_whole_models_sublevel_") and not name.endswith("_previewproxy"):
                for entity in level.RootArea.Entities:
                    if entity.Volume:
                        volumes.append((level, entity))

        extended_scale = MType.Vector3(extended_distance, 0, extended_distance)
        for level, entity in volumes:
            if not hasattr(entity, "original_scale"):
                entity.original_scale = entity.Transform.scale
            original_scale = entity.original_scale
            target_scale = original_scale + extended_scale
            transform = entity.Transform
            transform.scale = target_scale
            entity.Transform = transform
            if not hasattr(level, "original_proxy_distance"):
                level.original_proxy_distance = level.ProxyDistance
            level.ProxyDistance = level.original_proxy_distance + extended_distance

    def ChangeTerrainThresholdWhenAds(self, is_ads):
        if self.target_render_level <= 1:
            return
        if is_ads:
            self.SetTerrainLod1Distance(150)
        else:
            self.RefreshTerrainLod1Distance()

    def DesideWeaponShaderParameters(self, case):
        return
        if not hasattr(case, "ExecAllModelFunc"):
            return
        if self.target_render_level <= 1:
            case.ExecAllModelFunc("SetShaderGraphParameter", "EnableOverlay", 0)
        else:
            case.ExecAllModelFunc("ResetShaderGraphParameter", "EnableOverlay")

    def DesideUsingShaderLod0(self, model):
        if self.target_render_level <= 1:
            model.DisablePrincipleRole()
        else:
            model.MarkPrincipleRole()

    def RemoveRainEntities(self):
        if self.IsDelayDeserialization():
            return
        for level in genv.space.world.Levels.values():
            if "_previewproxy" in level.GetName():
                continue
            if "_trees_" in level.GetName():
                continue
            entities = []
            for entity in level.RootArea.Entities:
                if "_Rain_" in entity.GetName():
                    entities.append(entity)
            for entity in entities:
                entity.LeaveArea()

    def RemovePosunEffects(self):
        if self.IsDelayDeserialization():
            return
        lookup = {
            "1172efb0-8e38-4d98-b6ee-92cc39b8fbc7",  # "Effect/Effect_scene/Xuejing2023/Fx_Scene_Fire_L",
            "1f129074-f2ff-4963-a742-01d35a94906c",  # "Effect/Effect_scene/Xuejing2023/Fx_Scene_Smoke_L"
        }
        for level in genv.space.world.Levels.values():
            if "_previewproxy" in level.GetName():
                continue
            if "_trees_" in level.GetName():
                continue
            entities = []
            for entity in level.RootArea.Entities:
                for primitive in entity.Primitives:
                    if str(primitive.Resource) in lookup:
                        entities.append(entity)
                        break
            for entity in entities:
                entity.LeaveArea()

    def OnSpaceInit(self):
        self.HideSkyLevelVolume()

    def OnSpaceDestroy(self):
        if self.IsDelayDeserialization():
            return
        world = genv.space.world
        if not world:
            return
        hided_sky_volumes = getattr(world, "hided_sky_volumes", {})
        if not hided_sky_volumes:
            return
        DelHoldingInstace = MEngine.GetGameplay().DelHoldingInstance
        for name, entity in hided_sky_volumes.items():
            DelHoldingInstace(entity)
        print("SkyVolume Destroy:", len(hided_sky_volumes))
        world.hided_sky_volumes = {}

    def HideSkyLevelVolume(self):
        if self.IsDelayDeserialization():
            return
        world = genv.space.world
        hided_sky_volumes = getattr(world, "hided_sky_volumes", {})
        if hided_sky_volumes:
            return

        lookup = {}
        for level in world.Levels.values():
            if level.GetName().startswith("root_whole_"):
                for entity in level.RootArea.Entities:
                    if entity.GetName().endswith("sky_show"):
                        lookup[level.GetName()] = entity
        AddHoldingInstance = MEngine.GetGameplay().AddHoldingInstance
        for name, entity in lookup.items():
            AddHoldingInstance(entity)
            entity.LeaveArea()
        print("SkyVolume LeaveArea:", len(lookup))
        world.hided_sky_volumes = lookup

    def RestoreSkyLevelVolume(self):
        if self.IsDelayDeserialization():
            return
        world = genv.space.world
        hided_sky_volumes = getattr(world, "hided_sky_volumes", {})
        if not hided_sky_volumes:
            return

        levels = world.Levels
        DelHoldingInstace = MEngine.GetGameplay().DelHoldingInstance
        for name, entity in world.hided_sky_volumes.items():
            if name not in levels:
                continue
            entity.EnterArea(levels[name].RootArea)
            DelHoldingInstace(entity)
        print("SkyVolume EnterArea:", len(world.hided_sky_volumes))
        world.hided_sky_volumes = {}

    def SetGrassVisible(self, flag):
        if self.IsDelayDeserialization():
            return
        for level in genv.space.world.Levels.values():
            if "_sublevel_" in level.GetName():
                continue
            for entity in level.RootArea.Entities:
                for primitive in entity.Primitives:
                    if "Foliage" in primitive.__class__.__name__:
                        entity.IsVisible = flag

    def SetTreeVisible(self, flag):

        def SetLodDistance(entity, primitive):
            entity.IsVisible = bool(flag)

        self.ScanAllEntitiesByCategory("Trees", SetLodDistance)

    def SetTreesLod0Distance(self, distance):

        def SetLodDistance(entity, primitive):
            if hasattr(primitive, "SetCustomLodDistance"):
                primitive.SetCustomLodDistance(0, distance)

        self.ScanAllEntitiesByCategory("Trees", SetLodDistance)

    def SetTreesBillboardDistance(self, distance):
        lod0_distance = self.trees_lod0_distance

        if self.performance_level >= EPerformanceLevel.LEVEL_4:
            level = self.target_render_level

            def SetLodDistance(entity, primitive):
                if hasattr(primitive, "SetCustomLodDistance"):
                    primitive.SetCustomLodDistance(0, distance if level >= 5 else lod0_distance)
                    primitive.SetCustomLodDistance(1, distance)
        else:

            def SetLodDistance(entity, primitive):
                if hasattr(primitive, "SetCustomLodDistance"):
                    primitive.SetCustomLodDistance(0, lod0_distance)
                    primitive.SetCustomLodDistance(1, distance)

        self.ScanAllEntitiesByCategory("Trees", SetLodDistance)

    def SetStoneLod1Distance(self, distance):

        def SetDistance(entity, primitive):
            if hasattr(primitive, "SetCustomLodDistance"):
                primitive.SetCustomLodDistance(0, distance)

        self.ScanAllEntitiesByCategory("Stones", SetDistance)

    def SetStoneOverlayEnable(self, enable):

        def doProcess(entity, primitive):
            if enable:
                if hasattr(primitive, "ResetShaderGraphParameter"):
                    primitive.ResetShaderGraphParameter(-1, "EnableOverlayMap")
            else:
                if hasattr(primitive, "SetShaderGraphParameter"):
                    primitive.SetShaderGraphParameter(-1, "EnableOverlayMap", '0')

        self.ScanAllEntitiesByCategory("Stones", doProcess)

    def SetStoneDetailEnable(self, enable):

        def doProcess(entity, primitive):
            if enable:
                if hasattr(primitive, "ResetShaderGraphParameter"):
                    primitive.ResetShaderGraphParameter(-1, "EnableDetailMap")
            else:
                if hasattr(primitive, "SetShaderGraphParameter"):
                    primitive.SetShaderGraphParameter(-1, "EnableDetailMap", '0')

        self.ScanAllEntitiesByCategory("Stones", doProcess)

    def ScanAllEntitiesByCategory(self, category, func):
        space = genv.space
        if not space:
            return
        if not space.world:
            return
        if self.IsDelayDeserialization():
            return

        guids = getattr(model_category_data, category, ())
        level_prefix = LevelPrefix.get(category, "")
        # [DEBUG]
        if gui.is_pc_svn:
            level_prefix = ''
        # [DEBUG]
        for level in space.world.Levels.values():
            level_name = level.GetName()
            if level_name.startswith(level_prefix):
                for entity in level.RootArea.Entities:
                    for primitive in entity.Primitives:
                        if str(primitive.Resource) in guids:
                            func(entity, primitive)

    def UpdateWorldLongitude(self, value):
        space = genv.space
        if not space:
            return
        world = space.world
        if not world:
            return
        world.EnvVolume.Longitude = value

    def SetTerrainLod1Distance(self, distance):
        space = genv.space
        if not space or space.is_destroyed():
            return
        space.TerrainLodThreshold = MType.Vector3(distance, 150, 500)

    def SetTerrainGeometryFactor(self, factor):
        space = genv.space
        if not space or space.is_destroyed():
            return
        space.GeometryFactor = factor

    def SetGraphicsArtStyle(self, art_style):
        self.art_style.SetArtStyle(art_style)

    def ApplyRenderOptionConfigs(self, render_option_dict):
        render_option_dict.pop('ScreenScale', None)
        self.ApplyBatchRenderOption(render_option_dict)

    def _ApplySpaceOverrideConfig(self, configs, config_level):
        for i in range(config_level, -1, -1):
            if config_id := configs.get(str(i)):
                all_render_options = render_param_data.data.get(config_id, {})
                render_options = all_render_options.get('render_options', {})
                black_boards = all_render_options.get('black_board', {})
                # ppv = all_render_options.get('ppv', {})
                env_render = all_render_options.get('env_render', {})
                self.ApplyBatchRenderOption(render_options)
                for key, value in black_boards.items():
                    self.WriteOnBlackBoard(key, value)
                # for key, value in ppv.items():
                #     pass

                current_world = MEngine.GetGameplay().Scenario.ActiveWorld
                if current_world:
                    current_env = current_world.EnvVolume
                    if current_env:
                        for env_key, env_value in env_render.items():
                            try:
                                setattr(current_env, env_key, env_value)
                                print(f"Setting World={current_world.GetName()} {env_key}={env_value} succeed")
                            except:
                                print(f"Setting World={current_world.GetName()} {env_key}={env_value} failed")
                                pass
                    else:
                        print(f"no valid env for {current_world}")
                else:
                    print(f"not entered world for {self}")
                break

    def ApplySpaceOverrideConfig(self, spaceno, config_level=None):
        config_level = config_level or self.performance_level
        space_config = space_render_data.data.get(spaceno, {})
        
        self._ApplySpaceOverrideConfig(space_config.get('render_options', {}), config_level)

        # 确定是否启用前向渲染
        enable_forward_render = self.render_config.get('enable_forward_render')
        if space_config.get('force_deferred'):
            enable_forward_render = False
        if MConfig.Driver == 'es3':
            enable_forward_render = True
        
        if enable_forward_render:
            self.ApplyBatchRenderOption({
                'EnableDeferredRendering': False,
                'EnableHeightmapRendering': False,
                'CandelaGIType': 'Off',
                'VTFeedbackFactor': 32,

                'EnableTessellation': 0,    # 关闭 曲面细分 。因为shader再引擎内有缓存，所以必须在地形shader使用前调用，且游戏启动后只能开关一次
                
                'EnableAMDFSR2': False,     # 关 FSR2, 开 FSR1 + sharp + TAA
                'EnableAMDFSR': True,
                'EnableAMDFSRSharpen': True,
                'EnableTSAA': True,

            })
            ApplySingleRenderConfig('candela_level', 0)
        else:
            candela_enable = space_config.get('candela_enable', False)
            candela_level = self.render_config.get('candela_level') if candela_enable else 0
            ApplySingleRenderConfig('candela_level', candela_level)
        
        is_baked = space_data.data.get(spaceno, {}).get('is_baked', False)
        self.SetRenderOption('SkylightRenderMode', 'ReflectionOnly' if is_baked else 'All')
        
        self._ApplySpaceOverrideConfig(space_config.get('post_render_options', {}), config_level)

    def SaveTemporaryConfig(self, parent_config_name, config_name, level_value, force_all_apply=False):
        has_changed = False
        if not hasattr(self, 'user_render_configs'):
            setattr(self, 'user_render_configs', {})
        if f"{parent_config_name}_{config_name}" not in self.user_render_configs:
            has_changed = True
            self.user_render_configs[f"{parent_config_name}_{config_name}"] = level_value
        else:
            has_changed = (self.user_render_configs[f"{parent_config_name}_{config_name}"] != level_value)
            self.user_render_configs[f"{parent_config_name}_{config_name}"] = level_value
        return has_changed or force_all_apply

    def SaveTemporaryConfigsFromList(self, parent_config_name, config_dict, force_all_apply=False):
        result_changed_list = {}
        for key, value in config_dict.items():
            result_changed_list[key] = self.SaveTemporaryConfig(parent_config_name, key, value, force_all_apply)
        return result_changed_list

    def GatherRenderOptionsFromConfigValues(self, used_config_value_dict, template_configs, exclude_configs=None):
        render_quality_config_dict = {}
        performance_option_dict = {}

        for config_key, config_value in template_configs.items():
            if (exclude_configs is not None) and (config_key in exclude_configs):
                continue
            exact_index = config_value
            if config_key in used_config_value_dict:
                exact_index = used_config_value_dict[config_key]
            render_quality_config_dict[config_key] = exact_index
            print(f"render quality change: {config_key}, {exact_index}")

        return render_quality_config_dict, performance_option_dict

    def SettingDefaultRenderLevel(self, parent_config_name):
        # 加载space的渲染设置
        option_dict = None
        if genv.space and hasattr(genv.space, 'proto'):
            map_name = genv.space.proto.get("map", "")
            option_dict = copy.copy(performances_data.data.get(map_name, {}))
            if option_dict:
                option_dict.update(fixed_render_options)
        
        if option_dict is None:
            option_dict = copy.copy(self.default_render_options)
        # 避免FSR被干扰
        option_dict.pop('ScreenScale', None)
        option_dict.pop('EnableDLSS', None)
        option_dict.pop('EnableAMDFSR2', None)

        # 编辑器下的一些默认开关
        if GlobalData.IsSceneEditor:
            option_dict.update({
                "EnableHMotionBlur": False,
            })

        ContactShadowNumSteps = 32 if self.in_hall else 8
        try:
            self.WriteOnBlackBoard("ContactShadowNumSteps", ContactShadowNumSteps)
            print(f"RenderOption(ContactShadowNumSteps={ContactShadowNumSteps} parent config={parent_config_name}) set succeed")
        except:
            print(f"RenderOption(ContactShadowNumSteps={ContactShadowNumSteps} parent config={parent_config_name}) set failed")
            pass

        if (self.in_hall and parent_config_name == LocalConfig.GetHallConfigName()) or \
                (not self.in_hall and parent_config_name == LocalConfig.GetDefaultConfigName()):
            self.ApplyRenderOptionConfigs(option_dict)
        self.final_render_options = option_dict

    def ApplyEnvOptions(self, env_key, env_value):
        current_world = MEngine.GetGameplay().Scenario.ActiveWorld
        if current_world:
            current_env = current_world.EnvVolume
            if current_env:
                try:
                    setattr(current_env, env_key, env_value)
                    print(f"Setting World={current_world.GetName()} {env_key}={env_value} succeed")
                except:
                    print(f"Setting World={current_world.GetName()} {env_key}={env_value} failed")
                    pass
            else:
                print(f"no valid env for {current_world}")
        else:
            print(f"not entered world for {self}")

    def EnableBakeIconShowRoomRenderOptions(self, room_name, extra_options=None):
        options = {
            # "EnableDetailEnhancement": True,
            # "EnableFog": True,
            "EnableTSAA": True,
            "TAAQualityLevel": 1,
            "JitterOffset": 0.15,
            "TAASamples": '4X',
            "StaticBlend": 0.7,
            "DynamicBlend": 0.3,
            "EnableAmbientOcclusion": True,
            "EnableSunLightShadow": True,
            # "EnableFixCastRange": True,
            # "FixSunShadowRange": 3.0,
            # "FixSunShadowRange2": 3.0,
            "EnableShadowFrustrumCulling": True,
            "MinShaderLodLevel": 0,
        }
        if extra_options:
            options.update(extra_options)
        for key in list(options.keys()):
            MShowRoom.SetRenderOption(room_name, key, options[key])

    def SetRenderSceneBlur(self, enable):
        self.SetRenderOption('EnableSceneBlur', enable)

    def SetOnlyDrawUI(self, is_only):
        if is_only == self.only_render_ui:
            return
        self.only_render_ui = is_only
        MRender.SetOnlyDrawUIs(is_only)

    def RefreshMaxEffectCount(self, special_count=0):
        if special_count:
            self.ApplyGlobalMaxEffectCount(special_count)
            return

        # PC/主机 固定取最高档 LEVEL_HIGHEST
        if self.platform in (EPlatform.Windows, EPlatform.Orbis, EPlatform.Switch, EPlatform.Macos):
            max_effect_count = 128
        # 安卓，目前根据GPU来，之后考虑加上内存CPU等信息
        elif self.platform == EPlatform.Android:
            max_effect_count = EDefaultAndroidMaxEffectCount[self.performance_level]
        # ios根据芯片来
        elif self.platform == EPlatform.IOS:
            max_effect_count = EDefaultIOSMaxEffectCount[self.performance_level]
        # 未知平台
        else:
            max_effect_count = 32
        self.ApplyGlobalMaxEffectCount(max_effect_count)

        # 特效相关的限频
        from gshare import effect_util  # noqa
        effect_util.HitEffectProcessor.instance().hit_effect_min_interval = 0.5 - (self.performance_level - 1) * 0.1

        # 声音相关的最大距离
        from gshare import consts  # noqa
        consts.HIT_SOUND_MAX_DIST = 5.0 + (self.performance_level - 1) * 5.0

    def MergeShaderProfileStats(self, spaceno, stats):
        # 将本次收集的数据，合并到shader_profile_stat_used中
        if spaceno in self.shader_profile_stat_used:
            stats_exist = self.shader_profile_stat_used[spaceno]
            for key, count in stats.items():
                if key in stats_exist:
                    stats_exist[key] += count
                else:
                    stats_exist[key] = 1
        else:
            self.shader_profile_stat_used[spaceno] = stats

    def GeneratePrecompileListData(self, spacenos, high=False):
        # high: 高清版地图
        total_used_profile = {}
        for spaceno, stats in self.shader_profile_stat_used.items():
            if spaceno not in spacenos:
                continue
            total_count = float(sum(list(stats.values())))
            stats_list = []
            for key, count in stats.items():
                if key[0] in ('PBR_Triplanar', 'PBR'):
                    continue
                stats_list.append((key, count, count / total_count * 100))
            stats_list.sort(key=lambda x: x[1], reverse=True)
            total_used_profile[spaceno] = len(stats)

            cur_count = 0
            final_index = 0
            for index, _stats in enumerate(stats_list):
                cur_count += _stats[1]
                if cur_count / float(total_count) > 0.82:
                    final_index = index
                    break
            self.WritePrecompileListDataToFile(spaceno, stats_list[:final_index + 1], high)
        print("Total Used Profile Count =", sum(list(total_used_profile.values())))
        return total_used_profile

    def GetPrecompileListRenderLevel(self, spaceno):
        if spaceno == consts.HALL_SPACE_ID:
            # 大厅render_level = 0
            return 0
        else:
            return self.target_render_level

    def GetDeviceLevel(self):
        if self.performance_level <= 7:
            device_level = 0
        elif self.performance_level <= 8:
            device_level = 1
        else:
            device_level = 2
        return device_level

    def WritePrecompileListDataToFile(self, spaceno, stats_list, high):
        render_level = self.GetPrecompileListRenderLevel(spaceno)
        if genv.EngineFeature(cconst.ENGINE_VERSION.DEV):
            if gui.is_pc_svn:
                file_path = os.path.join(MEngine.AppAssetPath, "../", "src", "Package", "Script", "Python", "gclient",
                                         "util", "precompile_list", "precompile_list_dev_%s_%s.py" % (spaceno, render_level))
            else:
                # win trunk patch
                file_path = os.path.join(MEngine.AppAssetPath, "../", "../", "src", "Package", "Script", "Python",
                                         "gclient", "util", "precompile_list", "precompile_list_dev_%s_%s.py" % (spaceno, render_level))
        else:
            if gui.is_pc_svn:
                file_path = os.path.join(MEngine.AppAssetPath, "Package", "Script", "Python", "gclient", "util",
                                         "precompile_list", "precompile_list_%s_%s.py" % (spaceno, render_level))
            else:
                # win trunk patch
                file_path = os.path.join(MEngine.AppAssetPath, "../", "Package", "Script", "Python", "gclient", "util",
                                         "precompile_list", "precompile_list_%s_%s.py" % (spaceno, render_level))
        if high:
            file_path = file_path.replace(".py", "_high.py")
        with open(file_path, "w") as f:
            lines = ["# -*- coding: utf-8 -*-\n", "data = [\n", ]
            for stat in stats_list:
                lines.append("\t" + str(tuple(stat[0])) + ", # %.2f%%\n" % stat[-1])
            lines.append("]")
            f.writelines(lines)

    def ExtractNumberFromString(self, content, converter=float):
        try:
            if isinstance(content, str):
                numbers = [c for c in content if c.isdigit()]
                return converter("".join(numbers))
            else:
                return converter(content)
        except:
            return converter(0)

    def GetMemoryInfo(self):
        """ 获取设备的内存信息，返回一个dict，单位是MB
        例如：{
            "total": 5632.0,
            "used": 4027.453,
        }
        """
        # IOS用MSystem.GetMemoryInfo()
        if MConfig.Platform == 'ios':
            if genv.EngineFeature(cconst.ENGINE_VERSION.V202303):
                memory_info = MSystem.GetMemoryInfo()
                return {
                    "total": memory_info['TotalPhysical'],
                    "used": memory_info['UsedPhysical'],
                }
            else:
                mem_info = {}
                for pair in MSystem.GetMemoryInfo().split():
                    name, value = pair.split(":")
                    mem_info[name] = float(value)
                return {
                    "total": mem_info["totalMemory"],
                    "used": mem_info["usedMemory"],
                }
        # 其他平台用dump系统的接口
        import BaseGeneralLog
        dump_info_getter = BaseGeneralLog.DumpInfo.get
        return {
            "total": self.ExtractNumberFromString(dump_info_getter("total_mem", 0)) / 1024.0 / 1024.0,
            "used": self.ExtractNumberFromString(dump_info_getter("avl_mem", 0)) / 1024.0 / 1024.0,
        }

    @cached_property
    def is_extreme_low_memory(self):
        if self.platform == 'ios':  # 针对ios 判度是否低内存, 说的就是iphone6 和6p
            memory_info = self.GetMemoryInfo()
            ret = 512.0 < memory_info['total'] < 1024.0 * 1.3
        else:
            ret = False
        return ret

    @property
    def is_low_memory(self):
        # [DEBUG]
        if self.platform == "windows" and MConfig.IsProfile:
            if GlobalData.IsShaderCollectionRobot \
                    or GlobalData.IsProfileCollectionRobot \
                    or GlobalData.IsShaderPrecompileListRobot \
                    or GlobalData.IsShaderCacheMissAnalyseRobot \
                    or GlobalData.IsCharEditor \
                    or GlobalData.IsOnlineCharEditor \
                    or GlobalData.IsSceneEditor \
                    or GlobalData.IsSceneGame \
                    or GlobalData.IsExportPhysics \
                    or GlobalData.IsGeneratePivotData \
                    or GlobalData.PlaceEditorMode:
                return False
            if self.low_memory_random is None:
                self.low_memory_random = random.random()
            return self.low_memory_random > 0.5
        # [DEBUG]

        if self.platform == 'ios':  # 针对ios 判度是否低内存, 2G以下都算低内存
            memory_info = self.GetMemoryInfo()
            ret = 512.0 < memory_info['total'] < 1024.0 * 1.5
        else:  # 安卓性能有点差， 不用内存优化的话就更卡了
            ret = False
        return ret

    def SendElkLog(self, operation, **kwargs):
        avatar = genv.avatar
        if not avatar:
            return
        space = genv.space
        scope = 'test' if int(avatar.hostnum) >= 20000 else 'release'

        udp_latency = genv.udp_echo_info.get(avatar.hostnum, 999)
        udp_latency = udp_latency if 0 < udp_latency < 999 else avatar.replay_latency
        memory_info = self.GetMemoryInfo()
        pass_time = time_util.RealNow() - genv.init_time
        if pass_time > 24 * 3600:
            pass_time = 24 * 3600
        data = {
            "ereason": operation,  # 故意用ereason的, 避免重复
            "scope": scope,
            "user_name": avatar.name,
            "user_id": avatar.id,
            "user_show_id": avatar.show_id,
            "platform": MConfig.Platform,
            "pass_time": pass_time,
            "device_model": MPlatform.GetDeviceInfo(),
            "fps": min(MStatistics.GetAverageFps(), 200),
            "latency": int(avatar.replay_latency * 0.5),
            "protocol": genv.gate_client.last_protocol,
            "driver": MConfig.Driver,
            "channel": genv.app_channel,
            "gpu_name": PerformanceUtil.instance().gpu_info_str,
            "host_id": avatar.hostnum,
            "db_hostnum": avatar.db_hostnum,
            "engine_version": genv.engine_version,
            "network_status": genv.network_status,
            "performance_level": self.performance_level,
            'screen_scale': self.native_buffer_scale if self.platform in (EPlatform.IOS,) else self.screen_scale,
            'game_quality_level': LocalConfig.game_quality_level,
            'game_fps_limit': LocalConfig.game_fps_limit,
            'is_bit64': MConfig.IsBit64,
            'country_code': genv.GetLanguageCountryCode(),
            'thermal_status': self.thermal_status,
            'is_astc_support': self.is_astc_support,
            'is_dynamicui_compress_support': self.is_dynamicui_compress_support,
            'stutter_cnt': self.current_stutter_count,
            'ui_windows': [],
            'memory': memory_info,
            'mem_used': memory_info['used'],
            'udp_latency': int(udp_latency * 0.5),
            'latency_diff': int((avatar.replay_latency - udp_latency) * 0.5),
            'simple_gun_status': LocalConfig.gunparts_model_detail_show,
            'level': avatar.level,
            'record_pay_yuanbao': avatar.record_pay_yuanbao,
            'pay_yuanbao': avatar.pay_yuanbao,
            'fec_enable': genv.gate_client.GetNetInfoFec(),
            'fps_sequence': list(genv.fps_sequence),
            'loading_time': space.loading_time if space else 0.0,
            "ip_region": genv.GetIpRegion() or "",
            "country_locale": genv.country_locale,
            "fps_variance": genv.fps_variance.GetMaxAndClear(),
            "latency_variance": genv.latency_variance.GetMaxAndClear(),

            "net_network": genv.net_network.GetMaxAndClear(),

            "net_clientUseMS": genv.traceroute_hall.get("clientUse", 0),
            "net_gameUseMS": genv.traceroute_hall.get("gameUse", 0),
            "net_gateGameUseMS": genv.traceroute_hall.get("gateGameUse", 0),
            "net_networkMS": genv.traceroute_hall.get("network", 0),

            "net_soulClientUseMS": genv.traceroute_battle.get("clientUse", 0),
            "net_soulGameUseMS": genv.traceroute_battle.get("gameUse", 0),
            "net_soulGateGameUseMS": genv.traceroute_battle.get("gateGameUse", 0),
            "net_soulNetworkMS": genv.traceroute_battle.get("network", 0),

            'select_gate_group': genv.select_gate_group,
            'closest_gate_group': genv.closest_gate_group,
            'closest_latency': genv.closest_latency,
            'dynamic_instance': 1 if self.enable_dynamic_instancing else 0
        }

        io_speed = self.GetIOSpeed()
        if io_speed:
            data['io_speed'] = io_speed
        if self.target_resolution:
            data['target_resolution'] = self.target_resolution[-1]
        if genv.battery_cost_speed:
            data['battery_cost_speed'] = genv.battery_cost_speed
        if gui.is_pc:
            data['pc_gpu_info'] = EnsureSafeStr(self.win_device.gpu_info)
        genv.fps_sequence.clear()
        avatar.CallServer("SendElkLog", operation, data)

    def GetIOSpeed(self):
        if not hasattr(MProfile, 'GetIOTime'):
            return 0
        cur_io_speed = MProfile.GetIOSize() / MProfile.GetIOTime() * 1000.0 / 1024 / 1024
        if cur_io_speed > 1024:
            cur_io_speed = 1024
        return cur_io_speed

    def OnEnterInactive(self):
        genv.inactive_count += 1
        self.is_inactive = True
        self.NotifyProfileInactive(True)

    def OnLeaveInactive(self):
        self.is_inactive = False
        self.NotifyProfileInactive(False)

    def OnEnterLoading(self):
        self.is_loading = True

    def OnLeaveLoading(self):
        self.is_loading = False

    def OnStartCinematics(self):
        if self.performance_level <= EPerformanceLevel.LEVEL_2:
            return
        self.ApplyBatchRenderOption({
            'CSM_Dynamic': True,
            'EnableGodRay': True,
            'CSM_Distance1': 20,
            'EnableSunLightShadow': True,
        })

    def OnStopCinematics(self):
        if self.performance_level <= EPerformanceLevel.LEVEL_2:
            return
        self.ApplyBatchRenderOption({
            "EnableGodRay": False,
            "CSM_Distance1": self.final_render_options.get("CSM_Distance1", 1.5),
            "EnableSunLightShadow": self.final_render_options.get("EnableSunLightShadow", False),
            "CSM_Dynamic": self.final_render_options.get("CSM_Dynamic", False),
        })

    @staticmethod
    def EnableDof(enable):
        MEngine.GetGameplay().Player.Camera.EnableDepthOfField = enable

    @staticmethod
    def SetDofParams(params):
        camera = MEngine.GetGameplay().Player.Camera
        if 'FocalDistance' in params:
            camera.FocalDistance = params['FocalDistance']
        if 'FocalRegion' in params:
            camera.FocalRegion = params['FocalRegion']
        if 'NearTransitionRegion' in params:
            camera.NearTransitionRegion = params['NearTransitionRegion']
        if 'FarTransitionRegion' in params:
            camera.FarTransitionRegion = params['FarTransitionRegion']
        if 'Blurriness' in params:
            camera.Blurriness = params['Blurriness']
        # elif mode == client_consts.EDofMode_BOKEH:
        #     if 'DiaphragmDoFSensorWidth' in params:
        #         camera.DiaphragmDoFSensorWidth = params['DiaphragmDoFSensorWidth']
        #     if 'DiaphragmDoFFocalDistance' in params:
        #         camera.DiaphragmDoFFocalDistance = params['DiaphragmDoFFocalDistance']
        #     if 'DiaphragmDoFDepthBlurAmount' in params:
        #         camera.DiaphragmDoFDepthBlurAmount = params['DiaphragmDoFDepthBlurAmount']
        #     if 'DiaphragmDoFDepthBlurRadius' in params:
        #         camera.DiaphragmDoFDepthBlurRadius = params['DiaphragmDoFDepthBlurRadius']
        #     if 'DiaphragmDoFDepthFStop' in params:
        #         camera.DiaphragmDoFDepthFStop = params['DiaphragmDoFDepthFStop']
        #     if 'DiaphragmDoFCloseFarBlur' in params:
        #         camera.DiaphragmDoFCloseFarBlur = params['DiaphragmDoFCloseFarBlur']
        # if 'UE4DepthOfFieldMaxForegroundRadius' in params:
        #     value = params['UE4DepthOfFieldMaxForegroundRadius']
        #     MRender.SetRenderOption('UE4DepthOfFieldMaxForegroundRadius', value)
        # if 'UE4DepthOfFieldMaxBackgroundRadius' in params:
        #     value = params['UE4DepthOfFieldMaxBackgroundRadius']
        #     MRender.SetRenderOption('UE4DepthOfFieldMaxBackgroundRadius', value)
        # if 'UE4DepthOfFieldStrength' in params:
        #     value = params['UE4DepthOfFieldStrength']
        #     MRender.SetRenderOption('UE4DepthOfFieldStrength', value)

    @staticmethod
    def EnableMotionBlur(enable):
        # 暂时就用引擎的默认运动模糊效果
        # MRender.SetRenderOption('EnableHMotionBlur', 1 if enable else 0)

        # 编辑器内调整动态模糊会出现渲染问题
        if not GlobalData.IsSceneEditor:
            MRender.SetRenderOption('EnableHMotionBlur', 1 if enable else 0)

    @staticmethod
    def SetEnableCollectCustomInfos(enable):
        MStatistics.EnableCustomStatistics(enable)

    @staticmethod
    def ClearCollectCustomInfos():
        MStatistics.ClearCustomStatistics()

    @staticmethod
    def CollectPassInfos():
        MStatistics.EnableRenderCustomStatistics(1)
        MStatistics.EnableRenderPassCustomStatistics(0)
        MStatistics.ResetRenderPassCustomStatistics()
        MStatistics.EnableRenderPassCustomStatistics(1)

    @staticmethod
    def OutputPassInfos(path, name):
        MStatistics.OutputCustomStatistics(f"{path}\\{name}_Raw.txt")
        MStatistics.OutputRenderPassCustomStatistics(f"{path}\\{name}_Pass.txt")

    @staticmethod
    def GetPassInfos():
        rt = MStatistics.GetRenderPassCustomStatistics()
        # print(rt)
        return rt

    @staticmethod
    def CollectPrimitiveInfos():
        MStatistics.EnableRenderCustomStatistics(1)
        MStatistics.EnableRenderPrimitiveCustomStatistics(0)
        MStatistics.ResetRenderPrimitiveCustomStatistics()
        MStatistics.EnableRenderPrimitiveCustomStatistics(1)

    @staticmethod
    def OutputPrimitiveInfos(path, name):
        MStatistics.OutputCustomStatistics(f"{path}\\{name}_Raw.txt")
        MStatistics.OutputRenderPrimitiveCustomStatistics(f"{path}\\{name}_Primitive.txt")

    @staticmethod
    def GetPrimitiveInfos():
        rt = MStatistics.GetRenderPrimitiveCustomStatistics()
        # print(rt)
        return rt


LevelPrefix = {
    'tree': 'root_whole_trees'
}

##### 一些机器黑名单
# es3 屏幕闪，vulkan 又不支持，所以GG
# LENOVO#Lenovo L19111#,  # Lenovo A7
# itel#itel L6502#
