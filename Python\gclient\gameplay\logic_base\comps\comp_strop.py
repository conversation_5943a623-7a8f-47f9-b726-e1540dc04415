# -*- coding: utf-8 -*-
import time
import math
import <PERSON>ngine, MType, MCharacter
from common.IdManager import IdManager
from common.classutils import Components
from gclient.config import LocalConfig
from gshare.fsm import FiniteStateMachine, StateWrapper
from gclient.framework.util import events
from gclient import cconst
from gshare.consts import CombatState
from gshare.utils import enum
from gshare import formula


class StropCheckResult(enum):
    Success = 0
    Common = 1
    MarkType = 2


@StateWrapper
class StateStropDeadEnd(object):
    """
    初始状态/完全结束状态
    """
    def OnEnter(self):
        self.owner and self.owner.OnStateStropDeadEndEnter()


@StateWrapper
class StateStropDetected(object):
    """
    DockingDector检测到滑索攀爬线，这时候还没正式进入滑索过程，站在地面的状态
    """
    def OnEnter(self):
        self.owner and self.owner.OnStateStropDetectedEnter()


@StateWrapper
class StateStropConnecting(object):
    """
    DockingConnect节点工作时候，这时候是节点拉着角色飞过去攀爬点的状态
    """
    def OnEnter(self):
        self.last_enter_stamp = time.time()
        self.owner and self.owner.OnStateStropConnectingEnter()


@StateWrapper
class StateStropHolding(object):
    """
    DockingHold节点工作时候，这时候是角色在攀爬线上运动的状态
    """
    def OnEnter(self):
        self.owner and self.owner.OnStateStropHoldingEnter()


@StateWrapper
class StateStropLeaving(object):
    """
    玩家脱离滑索，但是还在滑索graph的状态
    """
    def OnEnter(self):
        self.owner and self.owner.OnStateStropLeavingEnter()
    
    def OnExit(self):
        self.owner and self.owner.OnStateStropLeavingExit()


STATE_STROPING = {
    'StropConnecting',
    'StropHolding',
    'StropLeaving',
}

@Components(
    FiniteStateMachine,
    StateStropDeadEnd,
    StateStropDetected,
    StateStropConnecting,
    StateStropHolding,
    StateStropLeaving,
)
class FSMStrop(object):
    """
    之前在83里面写滑索里面的逻辑，因为实在太复杂了，可以用惨字形容，而且bug修起来也麻烦。
    这里尝试使用状态机。
    """
    def __init__(self, owner):
        super(FSMStrop, self).__init__()
        self.owner = owner  # playeravatar/robot
        self.last_enter_stamp = 0   # 上一次进入connect状态的时间


class StropModelComp(object):

    def __init_component__(self):
        self.strop_h_dir = None
        self.rope_dir = None
        self.strop_type = None
        self.strop_detected = False

        self.cur_strop_mark = None  # 脚本记录下当前是哪根滑索

    def CueStropFound(self):
        # 现在脚本这样绕一下，等唐子豪优化一下DockingDetector的逻辑  TODO
        if self.strop_detected != True:
            self.strop_detected = True
            self.SetVariableZ('strop_detected', True, self.locomotion_graph_id)
        owner = self.owner
        if not owner:
            return
        if owner.IsPlayerCombatAvatar:
            owner.FoundStropNearby()
        if owner.CanEnterStateStropDetected():
            owner.fsm_strop.FSMTransfer(StateStropDetected)

    def CueStropNotFound(self):
        if self.strop_detected != False:
            self.strop_detected = False
            self.SetVariableZ('strop_detected', False, self.locomotion_graph_id)
        owner = self.owner
        if not owner:
            return
        if owner.IsPlayerCombatAvatar:
            owner.FoundStropNearby(cur_marks=[])
        owner.fsm_strop.FSMTransfer(StateStropDeadEnd)
    
    def CueDockingHoldActive(self):
        self.owner and self.owner.fsm_strop.FSMTransfer(StateStropHolding)
    
    def CueReachEdge(self):
        # graph里面判断到达边界
        self.owner and self.owner.fsm_strop.FSMTransfer(StateStropLeaving)
    
    def StropToLocomotion(self):
        # 清剩底层的graph
        self.locomotion_graph_id = self.GetSkeleton().GetGraphStack()[1]
        self.PopToLocomotionGraph()
        self.JumpToState(cconst.UNIT_STATE_IDLE)
        self.ChangeMotionState(cconst.UNIT_STATE_IDLE)
        # 恢复枪
        # weapon_case = self.owner.GetCurWeaponCase(is_fps_weapon=False)
        # if weapon_case and weapon_case.weapon_model:
        #     weapon_case.weapon_model.RemoveHiddenReason(cconst.HIDDEN_REASON_STROP)
        self.JumpOffStrop()
    
    def _OnJumpTo_Strop(self, info):
        # 打断其他动作，都写在这里
        owner = self.owner
        owner.OnShakeDir(0, 0)
        owner.model.RealAnimStop()
        owner.is_super_sprint = False
        # 隐藏枪
        # weapon_case = owner.GetCurWeaponCase(is_fps_weapon=False)
        # if weapon_case and weapon_case.weapon_model:
        #     weapon_case.weapon_model.AddHiddenReason(cconst.HIDDEN_REASON_STROP)
        pose_type = self.pose_type
        if pose_type == cconst.ModelPoseType.Jump:
            skeleton = self.GetSkeleton()
            if skeleton:
                skeleton.JumpToState(self.locomotion_graph_id, 'Locomotion', 0, True)

        skeleton = self.GetSkeleton()
        if self.cur_strop_mark:
            # 脚本设置的，当前滑索
            mark_info = self.cur_strop_mark
        else:
            mark_info = skeleton.GetTargetMark()
        mark_type = mark_info.type

        gun_type = info.get('gun_type', 0)
        stand_jump_no_move = info.get('stand_jump_no_move', 0)
        # pop到Idle graph
        self.locomotion_graph_id = skeleton.GetGraphStack()[1]
        self.PopToLocomotionGraph()
        self.fall_start_height = None
        # 压栈
        # graph = 'TPS/Locomotion/strop.graph'
        # locomotion_graph_id = self.PushGraph(graph, 0)
        # self.locomotion_graph_id = locomotion_graph_id
        locomotion_graph_id = self.locomotion_graph_id
        self.SetVariableI('gun_type', gun_type, gid=locomotion_graph_id)
        self.SetVariableI('stand_jump_no_move', stand_jump_no_move, gid=locomotion_graph_id)
        self.SetVariableF('G_YAW', self.yaw, gid=locomotion_graph_id)
        if mark_type == 18:  # 垂直滑索才需要设置方向
            vec_direction = info.get('strop_direction')  # 游泳时候强行只向上滑
            if not vec_direction:
                start = mark_info.start
                end = mark_info.start + mark_info.dir * mark_info.len
                player_y = self.owner.position[1]
                if start.y > player_y - 2.5 and end.y > player_y - 2.5:
                    vec_direction = 1.0
                elif start.y < player_y + 2.5 and end.y < player_y + 2.5:
                    vec_direction = -1.0
                else:
                    camera_pitch = MEngine.GetGameplay().Player.Camera.Transform.pitch
                    vec_direction = 1.0 if camera_pitch >= cconst.STROP_VERTICAL_DOWN_PITCH else -1.0
            self.SetVariableF('rope_dir', vec_direction, gid=locomotion_graph_id)  # 针对垂直滑索的情况
            self.SetVariableI('strop_type', 1, gid=locomotion_graph_id)
            self.strop_type = 1
            self.rope_dir = vec_direction
        else:  # 水平/倾斜滑索情况 17
            mark_start = mark_info.start
            mark_dir = mark_info.dir
            mark_len = mark_info.len
            mark_end = mark_start + mark_dir * mark_len
            mark_pos = mark_info.pos
            dis_1 = (mark_pos - mark_start).length
            dis_2 = (mark_pos - mark_end).length
            strop_h_dir = None
            if dis_1 < dis_2 and dis_1 <= 5:
                self.SetVariableV3('strop_h_dir', mark_dir, gid=locomotion_graph_id)
                self.SetVariableI('strop_h_type', 1, gid=locomotion_graph_id)
                self.strop_h_dir = mark_dir
                strop_h_dir = mark_dir
                print('=========dis_1 < dis_2=========dir = ', mark_dir)
            elif dis_2 < dis_1 and dis_2 <= 5:
                strop_h_dir = mark_dir * -1
                self.SetVariableV3('strop_h_dir', strop_h_dir, gid=locomotion_graph_id)
                self.SetVariableI('strop_h_type', 1, gid=locomotion_graph_id)
                self.strop_h_dir = strop_h_dir
                print('=========dis_2 < dis_1=========dir = ', strop_h_dir)
            elif formula.RadianDifference(formula.Vector3DToYaw(mark_dir), self.owner.upper_yaw) < math.pi / 2:
                # 策划需求，滑索的方向和自身面向的yaw的方向一致就往那边滑
                strop_h_dir = mark_dir
                self.SetVariableV3('strop_h_dir', strop_h_dir, gid=locomotion_graph_id)
                self.SetVariableI('strop_h_type', 1, gid=locomotion_graph_id)
                self.strop_h_dir = strop_h_dir
                print('=========RadianDifference(mark_dir, yaw) < pi / 2============dir = ', strop_h_dir)
            else:
                strop_h_dir = mark_dir * -1
                self.SetVariableV3('strop_h_dir', strop_h_dir, gid=locomotion_graph_id)
                self.SetVariableI('strop_h_type', 1, gid=locomotion_graph_id)
                self.strop_h_dir = strop_h_dir
                print('=========dis_2 < dis_1=========dir = ', strop_h_dir)
            # else:
            #     self.SetVariableI('strop_h_type', 0, gid=locomotion_graph_id)
            #     print('=========normal===============')
            if self.owner.IsPlayerCombatAvatar and strop_h_dir:
                # todo: 这个没有过渡咋搞
                genv.camera.placer.TurnView(- formula.RadianDifferenceWithDir(self.yaw, strop_h_dir.yaw), 0)
            self.SetVariableI('strop_type', 0, gid=locomotion_graph_id)
            self.strop_type = 0
        self.SetVariableF('strop_speed', cconst.STROP_SPEED, gid=self.locomotion_graph_id)  # 后期可以去掉，在graph确定
        self.SetVariableI('enable_strop', 0, gid=0)  # 关闭底层的empty_hero的strop扫描
        self.FireEvent('@Strop', self.locomotion_graph_id)
        self.ChangeMotionState(cconst.UNIT_STATE_STROP)

    def JumpOffStrop(self):
        # 向上的滑索离开的时候，给一个向上的速度抛出去，现在是直接接了Locomotion的Jump
        # print '=======JumpOffStrop', 'strop_type =', self.strop_type, 'rope_dir =', self.rope_dir, 'strop_h_dir =', self.strop_h_dir
        if self.strop_type is None:
            return

        # 与第一人称保持一致，第三人称模型也切到跳跃状态, 
        # 但是第三人称模型切跳跃状态的时候，通过rope dir等判断local_vec
        self._JumpOffStrop()
        return
        if self.strop_type == 1:
            if (self.rope_dir and self.rope_dir > 0) or self.owner.leave_strop_byjump:
                self._JumpOffStrop()
            else:
                self.owner.jump_top_height = cconst.DEFAULT_JUMP_TOP_HEIGHT
                self.CueIsFalling()
        else:
            self._JumpOffStrop()
            # if self.strop_h_dir and self.strop_h_dir.y > 0:
            #     self._JumpOffStrop()
            # else:
            #     self.owner.jump_top_height = cconst.DEFAULT_JUMP_TOP_HEIGHT
            #     self.CueIsFalling()

    def _JumpOffStrop(self):
        self.FireJumpEventForJump()
        owner = self.owner
        owner.ChangePoseType(cconst.ModelPoseType.Jump)
        skeleton = self.GetSkeleton()

        if self.cur_strop_mark:
            # 脚本设置的，当前滑索
            mark_info = self.cur_strop_mark
        else:
            mark_info = skeleton.GetTargetMark()

        mark_type = mark_info.type
        transform_v = self.model.Transform.inverse.transform_v
        leave_strop_byjump = owner.leave_strop_byjump
        # from gclient.util import debug_draw_util
        # pos = (self.position[0], self.position[1] + 1, self.position[2])
        if mark_type == 18:
            if owner.leave_strop_byjump:
                vec_world = MType.Vector3(0, 1, 0)
            elif self.rope_dir and self.rope_dir > 0:
                vec_world = mark_info.normal + MType.Vector3(0, cconst.STROP_VERTICAL_JUMP_OFF_SPEED_Y, 0)
            else:
                vec_world = MType.Vector3(0, 1, 0)
            vec_world.length = cconst.STROP_LEAVE_SPEED
        else:
            mark_dir = self.strop_h_dir
            mark_dir.length = cconst.STROP_LEAVE_SPEED
            if leave_strop_byjump:
                vec_world = mark_dir + MType.Vector3(0, cconst.STROP_LEAVE_JUMP_SPEED_Y, 0)
            else:
                vec_world = mark_dir + MType.Vector3(0, cconst.STROP_LEAVE_SPEED_Y, 0)
            # owner.debug_row_2 = debug_draw_util.draw_arrow_line(MType.Vector3(*pos), MType.Vector3(*pos) + mark_dir, color=[0, 1, 0])

        if leave_strop_byjump:
            # 计算额外速度矢量
            strop_extra_jump_vec = MType.Vector3(*formula.YawToVector(owner.yaw))
            strop_extra_jump_vec.length = cconst.STROP_LEAVE_JUMP_SPEED
            vec_world += strop_extra_jump_vec
            # owner.debug_row_1 = debug_draw_util.draw_arrow_line(MType.Vector3(*pos), MType.Vector3(*pos) + strop_extra_jump_vec, color=[0, 0, 1])
        # 把jump的方向画出来
        # pos2 = MType.Vector3(*pos) + vec_world
        # owner.debug_row = debug_draw_util.draw_arrow_line(pos, pos2, color=[1, 0, 0])
        # self.SetVariableI('jump_dir_int', self.GetRunStartDirection(), self.locomotion_graph_id)  # TODO
        vec_local = transform_v(vec_world)

        if not (self.rope_dir and self.rope_dir > 0) and not self.owner.leave_strop_byjump:
            vec_local = MType.Vector3(0, 0, 0)
            pass

        self.SetVariableV3('jump_dir', vec_local, self.locomotion_graph_id)
        self.SetVariableI('stand_jump_no_move', 0, self.locomotion_graph_id)
        # 策划需求
        owner = self.owner
        x, z = owner.GetMoveKeysState()
        owner.OnShakeDir(x, z)

    def SetTargetMark(self, mark_info):
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        if not mark_info:
            # 好像不需要清除，一直把前面的顶掉就行，能不能进滑索以脚本判断为准
            # self.cur_strop_mark = None
            return
        # 强设
        self.cur_strop_mark = mark_info
        skeleton.SetTargetMark(mark_info)


class StropHandModelComp(object):
    def __init_component__(self):
        pass

    def _OnJumpTo_Strop(self, info):
        self.JumpToMoveState(cconst.UNIT_STATE_IDLE)
        self.FireEvent('@Strop', self.pose_graph_id)

    def CueStropPunishmentStart(self):
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.StropPunishment, True)

    def CueStropPunishmentEnd(self):
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.StropPunishment, False)
        owner = self.owner
        owner.CheckNeedReloadWeapon()
        if owner.CheckFireMode() in (cconst.FireMode.FIRE_ADS, cconst.FireMode.FIRE_REAL_ADS) and owner.try_fire_start:
            owner.try_enter_ads = True
        owner.AutoEnterAdsState()
        owner.AutoTryGunAttack(cconst.DELAY_SHOOT_REASON_FOR_STROP)


class RobotStropModelComp(StropModelComp):

    def JumpOffStrop(self):
        if not self.owner.is_actor:
            return
        StropModelComp.JumpOffStrop(self)

    def _OnJumpTo_Strop(self, info):
        # 打断其他动作，都写在这里
        owner = self.owner
        owner.OnShakeDir(0, 0)
        owner.model.RealAnimStop()
        owner.is_super_sprint = False
        pose_type = self.pose_type
        if pose_type == cconst.ModelPoseType.Jump:
            skeleton = self.GetSkeleton()
            if skeleton:
                skeleton.JumpToState(self.locomotion_graph_id, 'Locomotion', 0, True)

        skeleton = self.GetSkeleton()
        if self.cur_strop_mark:
            # 脚本设置的，当前滑索
            mark_info = self.cur_strop_mark
        else:
            mark_info = skeleton.GetTargetMark()
        mark_type = mark_info.type

        gun_type = info.get('gun_type', 0)
        stand_jump_no_move = info.get('stand_jump_no_move', 0)
        # pop到Idle graph
        self.locomotion_graph_id = skeleton.GetGraphStack()[1]
        self.PopToLocomotionGraph()
        self.fall_start_height = None
        locomotion_graph_id = self.locomotion_graph_id
        self.SetVariableI('gun_type', gun_type, gid=locomotion_graph_id)
        self.SetVariableI('stand_jump_no_move', stand_jump_no_move, gid=locomotion_graph_id)
        self.SetVariableF('G_YAW', self.yaw, gid=locomotion_graph_id)
        if mark_type == 18:  # 垂直滑索才需要设置方向
            vec_direction = info.get('strop_direction')  # 游泳时候强行只向上滑
            if not vec_direction:
                start = mark_info.start
                end = mark_info.start + mark_info.dir * mark_info.len
                player_y = self.owner.position[1]
                dis_1 = abs(start.y - player_y)
                dis_2 = abs(end.y - player_y)
                if dis_1 > dis_2:
                    vec_direction = 1.0
                else:
                    vec_direction = -1.0

            self.SetVariableF('rope_dir', vec_direction, gid=locomotion_graph_id)  # 针对垂直滑索的情况
            self.SetVariableI('strop_type', 1, gid=locomotion_graph_id)
            self.strop_type = 1
            self.rope_dir = vec_direction
        else:  # 水平/倾斜滑索情况 17
            mark_start = mark_info.start
            mark_dir = mark_info.dir
            mark_len = mark_info.len
            mark_end = mark_start + mark_dir * mark_len
            mark_pos = mark_info.pos
            dis_1 = (mark_pos - mark_start).length
            dis_2 = (mark_pos - mark_end).length
            if dis_1 < dis_2 and dis_1 <= 5:
                self.SetVariableV3('strop_h_dir', mark_dir, gid=locomotion_graph_id)
                self.SetVariableI('strop_h_type', 1, gid=locomotion_graph_id)
                self.strop_h_dir = mark_dir
                print('=========dis_1 < dis_2=========dir = ', mark_dir)
            elif dis_2 < dis_1 and dis_2 <= 5:
                strop_h_dir = mark_dir * -1
                self.SetVariableV3('strop_h_dir', strop_h_dir, gid=locomotion_graph_id)
                self.SetVariableI('strop_h_type', 1, gid=locomotion_graph_id)
                self.strop_h_dir = strop_h_dir
                print('=========dis_2 < dis_1=========dir = ', strop_h_dir)
            elif formula.RadianDifference(formula.Vector3DToYaw(mark_dir), self.owner.upper_yaw) < math.pi / 2:
                # 策划需求，滑索的方向和自身面向的yaw的方向一致就往那边滑
                strop_h_dir = mark_dir
                self.SetVariableV3('strop_h_dir', strop_h_dir, gid=locomotion_graph_id)
                self.SetVariableI('strop_h_type', 1, gid=locomotion_graph_id)
                self.strop_h_dir = strop_h_dir
                print('=========RadianDifference(mark_dir, yaw) < pi / 2============dir = ', strop_h_dir)
            else:
                strop_h_dir = mark_dir * -1
                self.SetVariableV3('strop_h_dir', strop_h_dir, gid=locomotion_graph_id)
                self.SetVariableI('strop_h_type', 1, gid=locomotion_graph_id)
                self.strop_h_dir = strop_h_dir
                print('=========dis_2 < dis_1=========dir = ', strop_h_dir)
            # else:
            #     self.SetVariableI('strop_h_type', 0, gid=locomotion_graph_id)
            #     print('=========normal===============')
            self.SetVariableI('strop_type', 0, gid=locomotion_graph_id)
            self.strop_type = 0
        self.SetVariableF('strop_speed', cconst.STROP_SPEED, gid=self.locomotion_graph_id)  # 后期可以去掉，在graph确定
        self.SetVariableI('enable_strop', 0, gid=0)  # 关闭底层的empty_hero的strop扫描
        self.FireEvent('@Strop', self.locomotion_graph_id)
        self.ChangeMotionState(cconst.UNIT_STATE_STROP)
        self.SetVariableZ('is_detect_collide', False, self.locomotion_graph_id)


class CombatAvatarMember(object):
    pass


class RobotCombatAvatarMember(CombatAvatarMember):
    def __init_component__(self, bdict):
        self.fsm_strop = FSMStrop(self)
        self.fsm_strop.FSMBootstrap()
        self.stroping_sound = None
        self.start_strop_sound = None

        self.leave_strop_byself = False
        self.leave_strop_byjump = False

    def IsEnableStrop(self):
        return True

    def __on_leave_space_component__(self):
        self.fsm_strop.FSMBootstrap(StateStropDeadEnd)

    def __on_load_model_component__(self):
        self.fsm_strop.FSMBootstrap(StateStropDeadEnd)

    def __enter_control_component__(self):
        self.fsm_strop.FSMBootstrap(StateStropDetected)
        self.EnableStropDetected(True)

    def __exit_control_component__(self):
        self.fsm_strop.FSMBootstrap(StateStropDetected)
        self.EnableStropDetected(False)

    def IsInStropState(self):
        return self.fsm_strop.state in STATE_STROPING

    def IsInStropDetectedState(self):
        return self.fsm_strop.state == 'StropDetected'

    def EnableStropDetected(self, value):
        self.model and self.model.SetVariableI('enable_strop', 1 if value else 0, 0)

    def EnterFsmStropState(self, state):
        self.fsm_strop.FSMTransfer(state)

    def OnStrop(self):
        if self.CanEnterStateStropConnecting():
            self.EnterFsmStropState(StateStropConnecting)
            return True
        return False

    def LeaveStrop(self):
        if self.CanEnterStateStropLeaving():
            self.EnterFsmStropState(StateStropLeaving)
            return True
        return False

    def ForceOffStrop(self):
        # 客户端强制下滑索，如上飞机前
        if not self.model_loaded:
            return
        model = self.model
        if model:
            model.FireEvent('@StropToLocomotion', model.locomotion_graph_id)
            model.StropToLocomotion()
        self.EnterFsmStropState(StateStropDeadEnd)

    def ForceOffStropDying(self):
        self.model and self.model.FireEvent('@StropToLocomotion', self.model.locomotion_graph_id)
        self.EnterFsmStropState(StateStropDeadEnd)

    def CueStropToLocomotion(self):
        self.EnterFsmStropState(StateStropDeadEnd)

    def CanStrop(self, check_mark_type=True):
        if self.combat_state != CombatState.ALIVE:
            return StropCheckResult.Common
        if self.is_on_sky:
            print('CanStrop Fail is_on_sky')
            return StropCheckResult.Common
        if check_mark_type:
            model = self.model
            if not model:
                return StropCheckResult.Common
            skeleton = model.GetSkeleton()
            if not skeleton:
                return StropCheckResult.Common
            mark_info = skeleton.GetTargetMark()
            if not mark_info:
                print('CanStrop Fail not mark info')
                return StropCheckResult.Common
            mark_type = mark_info.type
            # 跳跃上滑索这里检测不过。
            if mark_type not in (17, 18):
                print('CanStrop Fail mark type != 17 18 mark_type =', mark_type, 'pos =', mark_info.pos)
                return StropCheckResult.MarkType
        return StropCheckResult.Success

    def _OnStrop(self):
        # 真正进入
        info = {}
        locomotion_graph_id = self.model.locomotion_graph_id
        gun_type = self.model.GetVariableI('gun_type', locomotion_graph_id)
        info['gun_type'] = gun_type
        self.model.JumpToState(cconst.UNIT_STATE_STROP, info=info)

    def OnStateStropDeadEndEnter(self):
        if self.model and hasattr(self, 'is_actor') and self.is_actor:
            self.model.SetVariableI('enable_strop', 1, gid=0)
            self.model.SetVariableI('enable_trigger_notFound', 0, gid=0)
            self.model.SetVariableI('enable_trigger_found', 1, gid=0)

    def CanEnterStateStropDetected(self):
        return self.CanStrop(check_mark_type=False) == StropCheckResult.Success

    def OnStateStropDetectedEnter(self):
        self.model.SetVariableI('enable_trigger_notFound', 1, gid=0)
        self.model.SetVariableI('enable_trigger_found', 0, gid=0)

    def CanEnterStateStropConnecting(self):
        if self.fsm_strop.state != 'StropDetected':
            return False
        if self.CanStrop() != StropCheckResult.Success:
            return False
        return True

    def OnStateStropConnectingEnter(self):
        # self.model.JumpBreak()
        self._OnStrop()  # PushGraph
        self.start_strop_sound = genv.sound_mgr.PlayEventById(70, self.position)
        # TODO
        # self.SyncGame3DSound(70, {'follow': True, })

        mark_info = self.model.GetSkeleton().GetTargetMark()
        strop_pos = mark_info.pos
        self.CallServer("OnStateStropConnectingEnter", (strop_pos.x, strop_pos.y, strop_pos.z))

    def OnStateStropHoldingEnter(self):
        entity = self.model.model
        if entity:
            self.stroping_sound and genv.sound_mgr.StopEvent(self.stroping_sound)
            self.stroping_sound = genv.sound_mgr.Play3DEventFollowTargetBySoundId(71, entity)
            # self.SyncGame3DSound(71, {'follow': True, 'loop': True})

    def CanEnterStateStropLeaving(self):
        return self.fsm_strop.state == 'StropHolding'

    def OnStateStropLeavingEnter(self):
        model = self.model
        if model:
            model.FireEvent('@StropToLocomotion', model.locomotion_graph_id)
        if self.stroping_sound:
            genv.sound_mgr.StopEvent(self.stroping_sound)
            self.stroping_sound = None
        if self.start_strop_sound:
            genv.sound_mgr.StopEvent(self.start_strop_sound)
            self.start_strop_sound = None
        genv.sound_mgr.PlayEventById(72, self.position)
        self.CallServer("OnStateStropLeavingEnter")

    def OnStateStropLeavingExit(self):
        self.model.StropToLocomotion()


class PlayerCombatAvatarMember(CombatAvatarMember):

    def __init_component__(self, bdict):
        self.strop_visual_entity = None  # 滑索交互entity
        self.fsm_strop = FSMStrop(self)
        self.fsm_strop.FSMBootstrap()
        self.stroping_sound = None
        self.start_strop_sound = None

        self.leave_strop_byself = False
        self.leave_strop_byjump = False

    def __on_leave_space_component__(self):
        self.fsm_strop.FSMBootstrap(StateStropDeadEnd)
        if self.strop_visual_entity and not self.strop_visual_entity.is_destroyed():
            self.strop_visual_entity.destroy()
        self.strop_visual_entity = None

    def __on_load_model_component__(self):
        self.model.SetVariableI('enable_strop', 1, 0)
        self.fsm_strop.FSMBootstrap(StateStropDeadEnd)
        MCharacter.SetMarkEdgeDistance(18, cconst.DOCKING_MARK_EDGE_DISTANCE)

    def __on_reload_model_component__(self):
        self.model.SetVariableI('enable_strop', 1, 0)
        self.fsm_strop.FSMBootstrap(StateStropDeadEnd)
        MCharacter.SetMarkEdgeDistance(18, cconst.DOCKING_MARK_EDGE_DISTANCE)

    def FoundStropNearby(self, _=None, cur_marks=None):
        if cur_marks is None:
            cur_marks = []
            marks = self.model.GetSkeleton().GetAllTargetMarks()
            for mark_info in marks:
                if mark_info.type not in (17, 18):
                    continue
                cur_marks.append(mark_info)
        if cur_marks:
            if self.strop_visual_entity:
                self.strop_visual_entity.UpdateMarks(cur_marks)
            else:
                self.strop_visual_entity = self.space.create_entity("StropEntity", entityid=IdManager.genid(), bdict={'marks': cur_marks})
            self.EntityEnterPlayerTrigger(self.strop_visual_entity.id, True)
        else:
            if self.strop_visual_entity:
                self.EntityEnterPlayerTrigger(self.strop_visual_entity.id, False)

    def IsInStropUav(self):
        if not self.strop_visual_entity:
            return False
        strop_entity = self.strop_visual_entity.GetStropEntity()
        if not strop_entity or not hasattr(strop_entity, 'Owner'):
            return False
        return strop_entity.Owner.IsStropUav

    def IsInStropState(self):
        return self.fsm_strop.state in STATE_STROPING

    @events.ListenTo(events.ON_JUMP_TO_STROP)
    def CheckJumpToStrop(self):
        if LocalConfig.is_jump_to_strop:
            if self.IsInStropState():
                self.LeaveStrop()
            else:
                self.OnStrop(by_jump=True)

    @events.ListenTo(events.ON_PLAYER_PARA_STAGE_CHANGE)
    def OnParaStageChangeForStrop(self, para_stage):
        if not self.is_on_sky:
            if self.strop_visual_entity and self.fsm_strop.state == 'StropDetected':
                self.EntityEnterPlayerTrigger(self.strop_visual_entity.id, True)

    def _EnterFsmStropState(self, state):
        self.fsm_strop.FSMTransfer(state)

    def OnStrop(self, by_jump=False):
        # 玩家点击滑索按钮 hud_fuction_comp调过来的
        if self.CanEnterStateStropConnecting():
            self._EnterFsmStropState(StateStropConnecting)
            self.CallServer('NotifyServerZipLine', by_jump)
            return True
        return False
    
    def LeaveStrop(self, by_jump=False):
        # 玩家自己按按钮离开滑索 hud_fuction_comp调过来的
        if self.CanEnterStateStropLeaving():
            self.leave_strop_byself = True
            self.leave_strop_byjump = by_jump
            self._EnterFsmStropState(StateStropLeaving)
            return True
        return False
    
    @events.ListenTo(events.ON_BEFORE_ENTER_AIRCRAFT)
    def ForceOffStrop(self):
        # 客户端强制下滑索，如上飞机前
        if not self.model_loaded:
            return
        model = self.model
        if model:
            model.FireEvent('@StropToLocomotion', model.locomotion_graph_id)
            model.StropToLocomotion()
        self._EnterFsmStropState(StateStropDeadEnd)
        if self.start_strop_sound:
            genv.sound_mgr.StopEvent(self.start_strop_sound)
            self.start_strop_sound = None
        self.stroping_sound and genv.sound_mgr.StopEvent(self.stroping_sound)
        self.SyncUnloadGame3DSound(70)
        self.SyncUnloadGame3DSound(71)
        genv.camera and genv.camera.RestoreCamera()
        # self.RemoveDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_STROP)

    def ForceOffStropDying(self):
        self.model and self.model.FireEvent('@StropToLocomotion', self.model.locomotion_graph_id)
        self._EnterFsmStropState(StateStropDeadEnd)
        self.SyncUnloadGame3DSound(70)
        self.SyncUnloadGame3DSound(71)
    
    def CueStropToLocomotion(self):
        # graph里面状态转换发送的事件回调，Strop_to_StropEnd  avatar_mode_cue.py
        self._EnterFsmStropState(StateStropDeadEnd)

    def CanStrop(self, check_mark_type=True):
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.Strop):
            print('CanStrop Fail StateRelationship')
            return StropCheckResult.Common
        if self.combat_state != CombatState.ALIVE:
            return StropCheckResult.Common
        if self.is_on_sky:
            print('CanStrop Fail is_on_sky')
            return StropCheckResult.Common
        if check_mark_type:
            model = self.model
            if not model:
                return StropCheckResult.Common
            skeleton = model.GetSkeleton()
            if not skeleton:
                return StropCheckResult.Common
            mark_info = skeleton.GetTargetMark()
            if not mark_info:
                print('CanStrop Fail not mark info')
                return StropCheckResult.Common
            mark_type = mark_info.type
            # 跳跃上滑索这里检测不过。
            if mark_type not in (17, 18):
                print('CanStrop Fail mark type != 17 18 mark_type =', mark_type, 'pos =', mark_info.pos)
                return StropCheckResult.MarkType
        return StropCheckResult.Success

    def _StropFound(self, val):
        # 暂时直接覆盖了，按钮有三种状态  发现滑索；没有滑索；离开滑索
        return
        self.FoundStropNearby(val)

    def _OnStrop(self):
        # 真正进入
        info = {}
        locomotion_graph_id = self.model.locomotion_graph_id
        gun_type = self.model.GetVariableI('gun_type', locomotion_graph_id)
        info['gun_type'] = gun_type
        self.leave_strop_byself = False
        self.leave_strop_byjump = False
        self.model.JumpToState(cconst.UNIT_STATE_STROP, info=info)
        self.hand_model.JumpToState(cconst.UNIT_STATE_STROP, info=info)
        # gui.main_ui.SetupStropUI(is_enter)  # 隐藏/恢复各种不能操作的UI，如MainMotion、打药等等

    def OnStateStropDeadEndEnter(self):
        self._StropFound(False)
        if self.model:
            self.model.SetVariableI('enable_strop', 1, gid=0)
            self.model.SetVariableI('enable_trigger_notFound', 0, gid=0)
            self.model.SetVariableI('enable_trigger_found', 1, gid=0)

    def CanEnterStateStropDetected(self):
        return self.CanStrop(check_mark_type=False) == StropCheckResult.Success
    
    def OnStateStropDetectedEnter(self):
        self._StropFound(True)
        self.model.SetVariableI('enable_trigger_notFound', 1, gid=0)
        self.model.SetVariableI('enable_trigger_found', 0, gid=0)
    
    def CanEnterStateStropConnecting(self):
        now = time.time()
        if now - self.fsm_strop.last_enter_stamp < cconst.STROP_ENTER_CD:
            return
        if self.fsm_strop.state != 'StropDetected':
            return False
        if self.CanStrop() != StropCheckResult.Success:
            return False
        return True

    def OnStateStropConnectingEnter(self):
        self.model.JumpBreak()
        self.EnterAdsState(False, False, True)
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.Strop, True)
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.StropPunishment, True)
        # info = {'target_direction': self.model.model.Transform.z_axis}
        # genv.camera and genv.camera.ApplyCamera(cconst.CAMERA_ID_STROP, info)
        self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_STROP)
        self._OnStrop()     # PushGraph
        self._StropFound(False)
        self.start_strop_sound = genv.sound_mgr.PlayEventById(70, self.position)
        self.SyncGame3DSound(70, {'follow': True, 'loop': True})

        mark_info = self.model.GetSkeleton().GetTargetMark()
        strop_pos = mark_info.pos
        self.CallServer("OnStateStropConnectingEnter", (strop_pos.x, strop_pos.y, strop_pos.z))
        if self.IsInStropUav():
            self.CallServer("UseStropUAV", self.strop_visual_entity.GetStropEntity().Owner.id, True)

    def OnStateStropHoldingEnter(self):
        self.FoundStropNearby()
        entity = self.model.model
        if entity:
            self.stroping_sound and genv.sound_mgr.StopEvent(self.stroping_sound)
            self.stroping_sound = genv.sound_mgr.Play3DEventFollowTargetBySoundId(71, entity)
            self.SyncGame3DSound(71, {'follow': True, 'loop': True})
    
    def CanEnterStateStropLeaving(self):
        return self.fsm_strop.state == 'StropHolding'

    def OnStateStropLeavingEnter(self):
        model = self.model
        if model:
            model.FireEvent('@StropToLocomotion', model.locomotion_graph_id)
        if self.stroping_sound:
            genv.sound_mgr.StopEvent(self.stroping_sound)
            self.stroping_sound = None
        if self.start_strop_sound:
            genv.sound_mgr.StopEvent(self.start_strop_sound)
            self.start_strop_sound = None
        genv.sound_mgr.PlayEventById(72, self.position)
        self.SyncGame3DSound(72)
        # 卸载
        self.SyncUnloadGame3DSound(70)
        self.SyncUnloadGame3DSound(71)
        self.CallServer("OnStateStropLeavingEnter")

    def OnStateStropLeavingExit(self):
        leave_strop_byself = self.leave_strop_byself
        if self.IsInStropUav():
            # 转移无人机
            self.CallServer("UseStropUAV", self.strop_visual_entity.GetStropEntity().Owner.id, False)
            if not leave_strop_byself:
                self.OnStateStropLeavingExitForStropUav()
                return

        self.model.StropToLocomotion()
        self.hand_model.JumpToState(cconst.UNIT_STATE_JUMP)
        camera = genv.camera
        if not camera:
            return
        camera.RestoreCamera()
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.Strop, False)
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.StropPunishment, False)
        genv.messenger.Broadcast(events.ON_CHECK_SUPER_SPRINT)
        self.AutoTryGunAttack()

    def OnStateStropLeavingExitForStropUav(self):
        self.model.StropToLocomotion()
        camera = genv.camera
        if not camera:
            return
        camera.RestoreCamera()
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.Strop, False)
        self.RequestHighSkyFreeFallParachute()
        # 这样会导致落地后还一直往前跑。。干掉了
        # genv.input_ctrl.SetMovePressedKey('forward', 1)
        return True

    def SetTargetMark(self, mark_info):
        self.model.SetTargetMark(mark_info)