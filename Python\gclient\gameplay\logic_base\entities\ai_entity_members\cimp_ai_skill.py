# -*- coding: utf-8 -*-
# time: 2023/11/27
# Author GZF
# Helen AI Entity的技能组件


import MType
from gclient import cconst
from gshare.ispell_mgr import SpellResult
from gshare import formula


class CommonAISkillComponent(object):
    def __post_component__(self, bdict):
        pass

    def __before_leave_space_component__(self):
        pass

    def AISkillMelee(self, skill_idx):
        space = self.space
        hit_box = (1, 1, 1, )
        hit_dir = MType.Vector3(*formula.YawToVector(self.yaw))
        hit_start_pos = MType.Vector3(*formula.Add3D(self.position, (0, 1.0, 0)))
        hit_dist = 2
        hit_results = space.AllSweepWithBox(hit_box, hit_start_pos, hit_dist, hit_dir, cconst.PHYSICS_AIR_DROP_BOX,
                                            local_trans=self.model.model.Transform)
        # [DEBUG]
        self.DrawAttackBox(hit_box, hit_dist)
        # [DEBUG]
        skill_id = self.monster_proto.get('skill_list')[skill_idx]
        for hit_res in hit_results:
            if hit_res and hit_res.IsHit and hit_res.Body:
                target = getattr(hit_res.Body, 'owner', None)
                game_logic = space.game_logic
                if target and target.IsCombatAvatar and target.is_alive and not target.is_invincible \
                        and game_logic.CanDamage(self, target):
                    spell_result = SpellResult()
                    spell_result.spell_id = skill_id
                    spell_result.caster = self
                    hit_pos = (target.position[0], target.position[1] + 1,  target.position[2])
                    damage_data = {
                        'hit_part': "UpperBottom",
                        'hit_dir': hit_dir.tuple(),
                        'target_pos': target.position,
                        'hit_pos': hit_pos
                    }
                    hit_effect_data = {
                        'hit_pos': hit_pos,
                        'hit_normal': formula.Tuple(hit_res.Normal),
                        'hit_dir': formula.Tuple(hit_dir),
                        'hit_material_type': 1001,
                    }
                    spell_result.hit_effect.append(hit_effect_data)

                    spell_result.sound_results = [{'event_id': 571}]

                    damage_data.update({"damage": game_logic.CalDamageForMonster(self, target, skill_id)})
                    spell_result.damage_result[target.id] = damage_data
                    genv.spell_core.SendSpellResult(spell_result)

    # [DEBUG]
    def DrawAttackBox(self, hit_box, hit_dist):
        if not getattr(genv, 'enable_monster_attack_box', False):
            genv.monster_attack_box = None
            return
        if not self.model or not self.model.model.IsSelected:
            return
        import MDebug
        box = MDebug.Box()
        box.scale = MType.Vector3(hit_box[0], hit_box[1], hit_dist)
        hit_start_pos = MType.Vector3(*formula.Add3D(self.position, (0, 1.0, 0)))
        hit_dir = MType.Vector3(*formula.YawToVector(self.yaw))
        hit_end_pos = hit_start_pos + hit_dir * hit_dist
        box.position = (hit_start_pos + hit_end_pos) * 0.5

        box.rotate = MType.Vector3(0, self.yaw, 0)
        genv.monster_attack_box = box
    # [DEBUG]

