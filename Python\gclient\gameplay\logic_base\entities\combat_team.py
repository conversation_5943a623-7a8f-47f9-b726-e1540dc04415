# -*- coding: utf-8 -*-
import struct
from client.ClientEntity import ClientAreaEntity
from common.IdManager import IdManager
from common.classutils import Property, CustomMapType, CustomListType
from common.EntityManager import EntityManager

from gclient import cconst
from gclient.data import airdrop_support_data
from gclient.framework.util import events
from gclient.gameplay.util import replay_util, mark_util
from gclient.util import cc_util
from gshare import consts
from gshare.consts import CombatState, OnlineState
from gshare.decorators import with_tag, DelayCall
from gshare.game_logic.igame_logic_squad_fight import RequestBuyItems
from gshare.icombat_team import ICombatTeamMember, ICombatTeamMembers, Mark_Moba, ICombatTeamMember_Moba, \
    ICombatTeamMembers_Moba, ICombatTeamMembers_Nowhere, ICombatTeamMembers_ShootingRange, \
    ICombatTeamMember_ShootingRange, Mark_Moba_Map, ICombatTeamMembers_HotSpot, ICombatTeamMember_HotSpot, \
    ICombatTeamMembers_SquadFight, ICombatTeamMember_SquadFight, ISimpleCombatTeamInfos, ISimpleCombatTeamInfo, \
    ISimpleCombatTeamMembers, IRoomCombatTeamMember, ICombatTeamMembers_GrandTheft, IMarkRespondMap
from gshare.game_logic.igame_logic_parachute_comp import ParaAvatarStage, PARA_ON_SKY_STAGE, WithPaprachuteGamingState
from gshare.italent import TeamTalentData
from gshare.property_util import CustomListMapType
from common.rpcdecorator import rpc_method, CLIENT_STUB
from common.RpcMethodArgs import EntityID, Dict, Int, Str, List


def BuildDummyInfo(info):
    return {
        'position': struct.unpack("3h", info.p),
        'name': info.name,
        'anchor': info.anchor,
        'show_name': info.show_name,
        'faction': info.get_owner().faction,
        'combat_state': info.combat_state,
        'is_alive': True,
        'is_replay_avatar': False,
        'slot': info.slot,
        'avatar_id': info.id,
        'para_stage': info.para_stage,
        'cur_titles':info.cur_titles,
    }


class CRoomCombatTeamMember(IRoomCombatTeamMember):

    def on_setattr(self, key, old, new):
        genv.messenger.Broadcast(events.ON_REPLAY_ROOM_TEAM_MEMBER_UPDATE, self.GetTeamGuid(), self.id, key)


class CSimpleCombatTeamMembers(ISimpleCombatTeamMembers):
    VALUE_TYPE = CRoomCombatTeamMember


class CSimpleCombatTeamInfo(ISimpleCombatTeamInfo):
    Property('member_dict', CSimpleCombatTeamMembers, Property.ALL_CLIENTS)


class CSimpleCombatTeamInfos(ISimpleCombatTeamInfos):
    VALUE_TYPE = CSimpleCombatTeamInfo


class CCombatTeamMember(ICombatTeamMember):
    def on_init(self, parent):
        # print 'CCombatTeamMember on_init, %s, %s' % (parent, type(parent))
        return

    def on_update(self, value):
        if 'is_leave' in value:
            if self.is_leave:
                genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Offline, True)
        return

    def on_setattr(self, key, old, new):
        func = getattr(self, 'on_set_%s' % key, None)
        func and func(old)

    def on_set_is_leave(self, old):
        if self.is_leave:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Offline, True)

    def on_set_online_state(self, old):
        if self.online_state == OnlineState.DISCONNECTED:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Disconnected, True)


class CCombatTeamMembers(ICombatTeamMembers):
    VALUE_TYPE = CCombatTeamMember

    def on_init(self, parent):
        # print 'CCombatTeamMembers on_init, %s, %s' % (parent, type(parent))
        return

    def on_update(self, value):
        #print 'CCombatTeamMembers on_update, %s' % value
        return

    def on_setattr(self, key, old, new):
        print('CCombatTeamMembers on_setattr, %s, %s, %s' % (key, old, new))


class CMark_Moba_Map(Mark_Moba_Map):
    def on_setattr(self, key, old, new):
        # {guid: list}
        member_id = self.get_parent().get_parent().id
        # 服务器回包，创建或者删除标记
        if replay_util.IsEnemyReplay() or (genv.avatar.is_replaying and replay_util.GetRealPlayerId() == member_id):
            # 观战对手不能显示对手的标记
            # 为了解决观战队友时，自己标记会有延迟显示，增加rpc即时处理，不用这里的on_setattr，所以要判断如果是自己的标记，直接return
            print("you are in replay. skip mark on_setattr")
            return
        # 这里增加判断，是否开启队友标记提醒， 如果是不是自己的，就不标记了
        player = genv.player
        if player.id != member_id and player.is_refuse_team_mark:
            if old is not None:
                mark_util.TeammateMarkOnSetattrImp(key, old, None, member_id)
            return
        mark_util.TeammateMarkOnSetattrImp(key, old, new, member_id)


class CMark_Moba(Mark_Moba):
    # {mark_type:{guid: list}}
    VALUE_TYPE = CMark_Moba_Map


class CCombatTeamMember_Moba(ICombatTeamMember_Moba):
    Property('mark', CMark_Moba)  # 标记位置

    def on_update(self, value):
        if 'c_eid' in value or 'cc_mtf' in value:
            genv.messenger.Broadcast(events.ON_SHIELD_VOICE_NEED_CHANGE)
            cc_util.RefreshCombatTeammateVolume(self.id)
        if 'continue_kill_count' in value:
            self._on_set_continue_kill_count(0)
        if 'coins' in value:
            genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_CHANGE, self.id, 'coins')
        if 'online_state' in value:
            self._on_set_online_state(value)
        return

    def on_setattr(self, key, old, new):
        # print '[Moba] member info', key, old, new
        func = getattr(self, "_on_set_%s" % key, None)
        func and func(old)
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_CHANGE, self.id, key)

    def _on_set_last_fire_time(self, old):
        genv.messenger.Broadcast(events.ON_AVATAR_SHOOTING_CHANGE, self.id)

    def _on_set_is_leave(self, old):
        super(CCombatTeamMember_Moba, self)._on_set_is_leave(old)
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_IS_LEAVE_CHANGE, self.id)
        if self.is_leave:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Offline, True)
        space = genv.space
        if not space:
            return
        if self.id in space.dummy_entities:
            dummy_entity = space.GetEntity(space.dummy_entities[self.id])
            if dummy_entity:
                dummy_entity.destroy()

    def _on_set_combat_state(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_COMBAT_STATE_CHANGE, self.id)
        if self.combat_state == CombatState.DYING:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.KnockDown, True)
        elif self.combat_state in (CombatState.DEAD, CombatState.GHOST):
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Dead, True)
        if old is not None:
            if old == CombatState.DYING:
                genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.KnockDown, False)
            elif old in (CombatState.DEAD, CombatState.GHOST) and self.combat_state not in (CombatState.DEAD, CombatState.GHOST):
                genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Dead, False)
        self.DealWithDummyCombatAvatarCombatStateChange()

    def _on_set_para_stage(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_NEXT_TIME_BUY_MATE_CHANGE, self.id)
        new_teaminfo_state = self.GetTeamInfoStateByParaStage()
        if old is not None:
            old_teaminfo_state = self.GetTeamInfoStateByParaStage(old)
            if old_teaminfo_state and old_teaminfo_state != new_teaminfo_state:
                genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, old_teaminfo_state, False)
        if new_teaminfo_state:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, new_teaminfo_state, True)

        if old != self.para_stage:
            self.DealWithDummyCombatAvatarParaStageChange()
        genv.player and genv.player.SetTeammateVisibleOnAirplane(self.id)

    def GetTeamInfoStateByParaStage(self, para_stage=None):
        if not para_stage:
            para_stage = self.para_stage
        if para_stage == ParaAvatarStage.OnAircraft:
            return cconst.TeamInfoState.OnAircraft
        elif para_stage in (ParaAvatarStage.Parachute, ParaAvatarStage.OpenParachute, ParaAvatarStage.FreeFall,
                            ParaAvatarStage.FreeFallWithWeapon):
            return cconst.TeamInfoState.Parachute
        if self.trainer_info or self.trainee_info:
            return cconst.TeamInfoState.Parachute

    def _on_set_vehicle_seat(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_VEHICLE_SEAT_CHANGE, self.id)
        if self.vehicle_seat == -1:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.OnVehicle,
                                     False)
        else:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.OnVehicle,
                                     True)

    def _on_set_vehicle_id(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_VEHICLE_ID_CHANGE, self.id)

    def _on_set_p(self, old):
        if self.is_leave:
            return
        avatar_id = self.id
        space = genv.space
        if avatar_id not in space.combat_avatars:
            if avatar_id not in space.dummy_entities:
                dummy_id = IdManager.genid()
                space.dummy_entities[avatar_id] = dummy_id
                space.create_entity('DummyCombatAvatar', dummy_id, BuildDummyInfo(self))
            else:
                dummy_entity = space.GetEntity(space.dummy_entities[avatar_id])
                if dummy_entity:
                    dummy_entity.position = struct.unpack("3h", self.p)

    def _on_set_select_hero_id(self, old):
        owner = genv.space and genv.space.GetEntityByID(self.id)
        if not owner:
            return
        if not owner.IsPlayerCombatAvatar:
            owner.RefreshToplogoVisibleForSelectHero()

    def DealWithDummyCombatAvatarParaStageChange(self):
        space = genv.space
        avatar_id = self.id

        dummy_entities = space.dummy_entities
        if avatar_id not in dummy_entities:
            return
        dummy_entity = space.GetEntity(dummy_entities[avatar_id])
        if not dummy_entity:
            return
        dummy_entity.para_stage = self.para_stage
        dummy_entity.RefreshToplogoVisibleForAircraft()

    def DealWithDummyCombatAvatarCombatStateChange(self):
        space = genv.space
        avatar_id = self.id

        dummy_entities = space.dummy_entities
        if avatar_id not in dummy_entities:
            return
        dummy_entity = space.GetEntity(dummy_entities[avatar_id])
        dummy_entity and dummy_entity.RefreshToplogoCombatState()

    def _on_set_ask_for_redeploy_time(self, old):
        genv.messenger.Broadcast(events.ON_WATCH_ASK_HELP_TIME, self.id)

    def _on_set_select_skill(self, old):
        genv.messenger.Broadcast(events.ON_TEAMMATE_SELECT_SKILL_CHANGE, self.id)

    def _on_set_next_time_buy_mate(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_NEXT_TIME_BUY_MATE_CHANGE, self.id)

    def _on_set_reborn_time(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_REBORN_TIME_CHANGE, self.id)

    def _on_set_armor(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_ARMOR_CHANGE, self.id)

    def _on_set_maxarmor(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_MAX_ARMOR_CHANGE, self.id)

    def _on_set_tactical_level(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_TACTICAL_LEVEL_CHANGE, self.id)

    def _on_set_hp(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_HP_CHANGE, self.id)

    def _on_set_extra_hp(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_EXTRA_HP_CHANGE, self.id)

    def _on_set_max_hp(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_MAX_HP_CHANGE, self.id)

    def _on_set_trainer_info(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_TRAINER_INFO_CHANGE, self.id)

    def _on_set_help_progress(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_HELP_PROGRESS_CHANGE, self.id)

    def _on_set_trainee_info(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_TRAINEE_INFO_CHANGE, self.id)

    def _on_set_continue_kill_count(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_C_KILL_COUNT_CHANGE, self.id, self.continue_kill_count)

    def _on_set_online_state(self, old):
        genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Disconnected, self.online_state == consts.OnlineState.DISCONNECTED)


class CCombatTeamMembers_Moba(ICombatTeamMembers_Moba):
    VALUE_TYPE = CCombatTeamMember_Moba

    def on_setattr(self, key, old, new):
        print('[CCombatTeamMembers_Moba] member info ', key, old, new)
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_CHANGE)
        genv.messenger.Broadcast(events.ON_SHIELD_VOICE_NEED_CHANGE)

    def GetTeamSelectedHeroes(self, exclude_me=False):
        heroes = []
        for member_id, member in list(self.items()):
            if exclude_me and member_id == genv.player.id:
                continue
            if member.select_hero_id:
                heroes.append(member.select_hero_id)
        return heroes


class CCombatTeamMember_GrandTheft(CCombatTeamMember_Moba):
    pass

class CCombatTeamMembers_GrandTheft(ICombatTeamMembers_GrandTheft):
    VALUE_TYPE = CCombatTeamMember_GrandTheft

    def on_setattr(self, key, old, new):
        print('[CCombatTeamMembers_Moba] member info ', key, old, new)
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_CHANGE)
        genv.messenger.Broadcast(events.ON_SHIELD_VOICE_NEED_CHANGE)

    def GetTeamSelectedHeroes(self, exclude_me=False):
        heroes = []
        for member_id, member in list(self.items()):
            if exclude_me and member_id == genv.player.id:
                continue
            if member.select_hero_id:
                heroes.append(member.select_hero_id)
        return heroes


class CCombatTeamMember_Nowhere(ICombatTeamMember_Moba):
    Property('mark', CMark_Moba)  # 标记位置


class CCombatTeamMembers_Nowhere(ICombatTeamMembers_Nowhere):
    VALUE_TYPE = CCombatTeamMember_Nowhere


class AutoMarkRecordMap(CustomMapType):
    def on_setattr(self, key, old, new):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_AUTO_MARK_ITEM, key)


class CMarkRespondList(CustomListType):
    def on_append(self):
        for key, value in self.get_parent().items():
            if value == self:
                mark_util.MarkRespondImp(key, self.get(-1))
                return


class CTeamRespondMap(IMarkRespondMap):
    VALUE_TYPE = CMarkRespondList

    def on_setattr(self, key, old, new):
        if new is None:
            mark_util.MarkRespondCancelImp(key)
        else:
            mark_util.MarkRespondImp(key, new.get(-1))


class CNoOwnerRespondList(CustomListType):
    def on_append(self):
        mark_util.MarkNoOwnerImp(self.get(-1))


class TeamMapMarks(CustomListMapType):
    def on_setattr(self, key, old, new):
        if new:
            genv.messenger.Broadcast(events.ON_SHOW_ENEMY_MARK, {key: new}, {'gun_sound': True})
            enemy = genv.space.combat_avatars.get(key)
            enemy and enemy.model.AddTechState(200)
        else:
            genv.messenger.Broadcast(events.ON_HIDE_ENEMY_MARK_BY_AVATAR, key)
            enemy = genv.space.combat_avatars.get(key)
            enemy and enemy.model.RemoveTechState(200)


class ExposeEnemyIds(CustomListType):
    def on_pop(self, inx, old):
        space = genv.space
        if not space:
            return
        player = space.GetEntity(old)
        if not player:
            return
        player.OnExposeEnemyIdsPop()


class CTeamTalentData(TeamTalentData):
    Property("expose_enemy_ids", ExposeEnemyIds)  # 2133， 一换一
    Property("map_marks", TeamMapMarks)  # 小地图用箭头显示玩家位置 与全图UAV一致 guid -> (x,y, z yaw)


class CXrayTargets(CustomMapType):
    def on_setattr(self, guid, old, new):
        if not old:
            entity = EntityManager.getentity(guid)
            if entity and entity.IsCombatAvatar:
                entity.model.AddTechState(200)
        elif not new:
            entity = EntityManager.getentity(guid)
            if entity and entity.IsCombatAvatar:
                entity.model.RemoveTechState(200)


@with_tag("IsCombatTeam")
class CombatTeamEntity(ClientAreaEntity):
    Property('leader_guid', '')
    Property('member_dict', CCombatTeamMembers)
    Property('faction', -1)
    Property('is_alive', True)
    Property('match_type', 1002)
    Property('spaceno', 0)
    Property('is_robot_team', False)
    Property('support_last_buy_time', CustomMapType)
    Property('auto_mark_record', AutoMarkRecordMap)
    Property('talent_data', CTeamTalentData)   # 天赋相关数据
    Property('xray_targets', CXrayTargets)   # 全队可见的带标记的敌人
    Property('mark_respond_map', CTeamRespondMap)  # 全队标记回应记录
    Property('no_owner_marks', CNoOwnerRespondList)  # 失去所有者的标记(当物品被别人标记后再取消时会进入这个状态)

    @property
    def guid(self):
        return self.id

    def GetMember(self, member_guid):
        return self.member_dict.get(member_guid, None)

    def IsSingleTeam(self):
        return len(self.member_dict) == 1

    def GetTeammateCount(self):
        # 获取队友数量
        return len(self.member_dict) - 1

    def GetMemberName(self, member_guid):
        member = self.GetMember(member_guid)
        if member:
            return member.get('name', '')
        return ''

    def destroy(self):
        for avatar in self.space.combat_avatars.values():
            avatar._combat_team = None
        super(CombatTeamEntity, self).destroy()

    def CheckMarkRespondedById(self, mark_guid, player_id):
        # 判断这个标记有没有被这个id回应过
        return mark_guid in self.mark_respond_map and player_id in self.mark_respond_map[mark_guid]

    def CheckMarkResponded(self, mark_guid):
        # 判断这个标记有没有人回应过
        return mark_guid in self.mark_respond_map and len(self.mark_respond_map[mark_guid]) > 0


class CSupportLastBuyTime(CustomMapType):
    def on_setattr(self, key, old, new):
        if key not in airdrop_support_data.data:
            return
        proto = airdrop_support_data.data[key]
        if 'cd' not in proto:
            return
        cd = proto['cd']
        genv.messenger.Broadcast(events.ON_AIRDROP_STORE_GOODS_BUY_TIME_CHANGE, key, new, cd)


@with_tag("IsCombatTeam")
class CombatTeamEntity_Moba(CombatTeamEntity):
    Property('member_dict', CCombatTeamMembers_Moba)
    Property('select_hero_start_time', 0.0)
    Property('support_last_buy_time', CSupportLastBuyTime)     # 记录小队共享cd的道具的购买时间,
    Property('buy_airdrop_count', 0)  # 买空投次数
    Property('can_reborn', True)
    Property('own_energy', False)         # 能源玩法

    def _on_set_leader_guid(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_LEADER_CHANGE)

    def _on_set_own_energy(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_SHUTTER_ENERGY_CHANGED, self.id)

    def GetTeamVehicleInfo(self):
        # 获取队友中乘车状况
        res = {}
        for member_id, member_info in self.member_dict.items():
            if member_info.vehicle_id:
                val = res.setdefault(member_info.vehicle_id, [])
                val.append((member_info.vehicle_seat, member_id))
        return res

    def CanMemberFollowPlayerPara(self, member_id):
        # 队伍中member_id这个人能不能邀请跟随自己
        member = self.member_dict.get(member_id)
        if not member or member.is_leave:
            return False
        parachute_stage = genv.player.game_logic.parachute_stage
        if parachute_stage == WithPaprachuteGamingState.Aircraft:
            if member.para_stage != ParaAvatarStage.OnAircraft:
                return False
        elif member.para_stage in PARA_ON_SKY_STAGE:
            return False
        player = genv.player
        player_trainer_info = player.trainer_info
        if (player_trainer_info and member_id in player.trainee_info) or member_id == player.trainer_info or \
                (player.trainer_info and player_trainer_info == member.trainer_info):
            return False
        return True

    def CanPlayerInviteMemberForPara(self, member_id):
        # 能不能邀请队伍中member_id这个队员跳伞
        member = self.member_dict.get(member_id)
        if not member or member.is_leave:
            return False
        parachute_stage = genv.player.game_logic.parachute_stage
        if parachute_stage == WithPaprachuteGamingState.Aircraft:
            if member.para_stage != ParaAvatarStage.OnAircraft:
                return False
        elif member.para_stage in PARA_ON_SKY_STAGE:
            return False
        player = genv.player
        player_trainer_info = player.trainer_info
        if player_trainer_info and member.trainer_info != player_trainer_info and player_trainer_info != member_id:
            # 自己跟随别人跳伞，存在有人不跟随自己队伍。可以点击
            return True
        if not player_trainer_info and member.trainer_info != player.id:
            # 自己不跟随别人跳伞，且存在有人不跟随自己时，可以点击；
            return True
        return False

    @rpc_method(CLIENT_STUB, EntityID(), Int(), List(), Str())
    def BuySupportResult(self, player_id, reason, item_list, reply_teammate_id):
        # 购买了什么东西，reply_teammate_id：是回应这个人购买的
        genv.messenger.Broadcast(events.ON_TEAMMATE_BUY_SUPPORT, player_id, item_list)
        player = self.member_dict.get(player_id)
        if not player:
            return
        item_id = item_list[0]
        support_info = airdrop_support_data.data.get(item_id, {})
        item_name = support_info.get('item_name', '')
        if item_id == 2:
            prompt_id = 5 if reply_teammate_id else 4
        else:
            prompt_id = 965 if reply_teammate_id else 964
        gui.Prompt(prompt_id, extra_data=(item_name,), extra_info={'name': player.name,
                                                                   'icon': support_info.get('buy_icon_id')})

    @rpc_method(CLIENT_STUB, EntityID(), Int())
    def RequestBuySupportItem(self, teammate_id, item_id):
        if teammate_id not in self.member_dict:
            return
        genv.messenger.Broadcast(events.ON_TEAMMATE_ASK_SUPPORT, teammate_id, item_id)


@with_tag("IsCombatTeam")
class CombatTeamEntity_Nowhere(CombatTeamEntity):
    Property('member_dict', CCombatTeamMembers_Nowhere)


class CCombatTeamMember_ShootingRange(ICombatTeamMember_ShootingRange):
    Property('mark', CMark_Moba)  # 标记位置
    
    def on_setattr(self, key, old, new):
        # print '[Moba] member info', key, old, new
        func = getattr(self, "_on_set_%s" % key, None)
        func and func(old)

    def _on_set_select_skill(self, old):
        genv.messenger.Broadcast(events.ON_TEAMMATE_SELECT_SKILL_CHANGE, self.id)

    def _on_set_select_hero_id(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_CHANGE, self.id, 'select_hero_id')



class CCombatTeamMembers_ShootingRange(ICombatTeamMembers_ShootingRange):
    VALUE_TYPE = CCombatTeamMember_ShootingRange


@with_tag("IsCombatTeam")
class CombatTeamEntity_ShootingRange(CombatTeamEntity):
    Property('member_dict', CCombatTeamMembers_ShootingRange)


class CCombatTeamMember_HotSpot(ICombatTeamMember_HotSpot):
    Property('mark', CMark_Moba)  # 标记位置

    def on_setattr(self, key, old, new):
        func = getattr(self, "_on_set_%s" % key, None)
        func and func(old)

    def _on_set_select_skill(self, old):
        genv.messenger.Broadcast(events.ON_TEAMMATE_SELECT_SKILL_CHANGE, self.id)

    def _on_set_hp(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_HP_CHANGE, self.id)

    def _on_set_extra_hp(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_EXTRA_HP_CHANGE, self.id)

    def _on_set_max_hp(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_MAX_HP_CHANGE, self.id)

    def _on_set_armor(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_ARMOR_CHANGE, self.id)

    def _on_set_max_armor(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_MAX_ARMOR_CHANGE, self.id)

    def _on_set_combat_state(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_COMBAT_STATE_CHANGE, self.id)
        if self.combat_state == CombatState.DYING:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.KnockDown, True)
        elif self.combat_state in (CombatState.DEAD, CombatState.GHOST):
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Dead, True)
        if old is not None:
            if old == CombatState.DYING:
                genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.KnockDown, False)
            elif old in (CombatState.DEAD, CombatState.GHOST) and self.combat_state not in (CombatState.DEAD, CombatState.GHOST):
                genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Dead, False)

    def _on_set_select_hero_id(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_CHANGE, self.id, 'select_hero_id')

    def _on_set_pre_select_hero_id(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_CHANGE, self.id, 'pre_select_hero_id')


class CCombatTeamMembers_HotSpot(ICombatTeamMembers_HotSpot):
    VALUE_TYPE = CCombatTeamMember_HotSpot

    def on_setattr(self, key, old, new):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_CHANGE)
        genv.messenger.Broadcast(events.ON_SHIELD_VOICE_NEED_CHANGE)

    def GetTeamSelectedHeroes(self, exclude_me=False):
        heroes = []
        for member_id, member in list(self.items()):
            if exclude_me and member_id == genv.player.id:
                continue
            if member.select_hero_id:
                heroes.append(member.select_hero_id)
        return heroes


@with_tag("IsCombatTeam")
class CombatTeamEntity_HotSpot(CombatTeamEntity):
    Property('member_dict', CCombatTeamMembers_HotSpot)


class CCombatTeamMember_SquadFight(ICombatTeamMember_SquadFight):
    Property('mark', CMark_Moba)  # 标记位置
    # 加这个变量是因为地图UI与这个耦合了，只是客户端变量，防止trace
    Property('vehicle_seat', -1)  # -1默认，0司机，其他乘客

    def on_update(self, value):
        if 'c_eid' in value or 'cc_mtf' in value:
            genv.messenger.Broadcast(events.ON_SHIELD_VOICE_NEED_CHANGE)
        if 'continue_kill_count' in value:
            self._on_set_continue_kill_count(0)
        if 'coins' in value:
            genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_CHANGE, self.id, 'coins')
        if 'select_hero_id' in value:
            genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_CHANGE, self.id, 'select_hero_id')
        if 'hp' in value:
            genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_CHANGE, self.id, 'hp')

    def on_setattr(self, key, old, new):
        func = getattr(self, "_on_set_%s" % key, None)
        func and func(old)
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_CHANGE, self.id, key)

    # def _on_set_last_fire_time(self, old):
    #     genv.messenger.Broadcast(events.ON_AVATAR_SHOOTING_CHANGE, self.id)

    def _on_set_is_leave(self, old):
        ICombatTeamMember_Moba._on_set_is_leave(self, old)
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_IS_LEAVE_CHANGE, self.id)
        if self.is_leave:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Offline, True)
        space = genv.space
        if not space:
            return
        if self.id in space.dummy_entities:
            dummy_entity = space.GetEntity(space.dummy_entities[self.id])
            if dummy_entity:
                dummy_entity.destroy()

    def _on_set_combat_state(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_COMBAT_STATE_CHANGE, self.id)
        if self.combat_state == CombatState.DYING:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.KnockDown, True)
        elif self.combat_state in (CombatState.DEAD, CombatState.GHOST):
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Dead, True)
        if old is not None:
            if old == CombatState.DYING:
                genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.KnockDown, False)
            elif old in (CombatState.DEAD, CombatState.GHOST) and self.combat_state not in (CombatState.DEAD, CombatState.GHOST):
                genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, cconst.TeamInfoState.Dead, False)
        self.DealWithDummyCombatAvatarCombatStateChange()

    def _on_set_para_stage(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_NEXT_TIME_BUY_MATE_CHANGE, self.id)
        new_teaminfo_state = self.GetTeamInfoStateByParaStage()
        if old is not None:
            old_teaminfo_state = self.GetTeamInfoStateByParaStage(old)
            if old_teaminfo_state and old_teaminfo_state != new_teaminfo_state:
                genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, old_teaminfo_state, False)
        if new_teaminfo_state:
            genv.messenger.Broadcast(events.ON_TEAM_INFO_MOTION_STATE_CHANGE, self.id, new_teaminfo_state, True)

        if old != self.para_stage:
            self.DealWithDummyCombatAvatarParaStageChange()

    def GetTeamInfoStateByParaStage(self, para_stage=None):
        if not para_stage:
            para_stage = self.para_stage
        if para_stage == ParaAvatarStage.OnAircraft:
            return cconst.TeamInfoState.OnAircraft
        elif para_stage in (ParaAvatarStage.Parachute, ParaAvatarStage.OpenParachute, ParaAvatarStage.FreeFall,
                            ParaAvatarStage.FreeFallWithWeapon):
            return cconst.TeamInfoState.Parachute

    def _on_set_p(self, old):
        if self.is_leave:
            return
        avatar_id = self.id
        space = genv.space
        if avatar_id not in space.combat_avatars:
            if avatar_id not in space.dummy_entities:
                dummy_id = IdManager.genid()
                space.dummy_entities[avatar_id] = dummy_id
                space.create_entity('DummyCombatAvatar', dummy_id, BuildDummyInfo(self))
            else:
                dummy_entity = space.GetEntity(space.dummy_entities[avatar_id])
                if dummy_entity:
                    dummy_entity.position = struct.unpack("3h", self.p)

    def _on_set_select_hero_id(self, old):
        owner = genv.space and genv.space.GetEntityByID(self.id)
        if not owner:
            return
        if not owner.IsPlayerCombatAvatar:
            owner.RefreshToplogoVisibleForSelectHero()

    def DealWithDummyCombatAvatarParaStageChange(self):
        space = genv.space
        avatar_id = self.id

        dummy_entities = space.dummy_entities
        if avatar_id not in dummy_entities:
            return
        dummy_entity = space.GetEntity(dummy_entities[avatar_id])
        if not dummy_entity:
            return
        dummy_entity.para_stage = self.para_stage
        dummy_entity.RefreshToplogoVisibleForAircraft()

    def DealWithDummyCombatAvatarCombatStateChange(self):
        space = genv.space
        avatar_id = self.id

        dummy_entities = space.dummy_entities
        if avatar_id not in dummy_entities:
            return
        dummy_entity = space.GetEntity(dummy_entities[avatar_id])
        dummy_entity and dummy_entity.RefreshToplogoCombatState()

    def _on_set_select_skill(self, old):
        genv.messenger.Broadcast(events.ON_TEAMMATE_SELECT_SKILL_CHANGE, self.id)

    def _on_set_armor(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_ARMOR_CHANGE, self.id)

    def _on_set_maxarmor(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_MAX_ARMOR_CHANGE, self.id)

    def _on_set_hp(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_HP_CHANGE, self.id)

    def _on_set_extra_hp(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_EXTRA_HP_CHANGE, self.id)

    def _on_set_max_hp(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_MAX_HP_CHANGE, self.id)

    def _on_set_help_progress(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_HELP_PROGRESS_CHANGE, self.id)

    def _on_set_continue_kill_count(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_MEMBER_C_KILL_COUNT_CHANGE, self.id, self.continue_kill_count)


class CCombatTeamMembers_SquadFight(ICombatTeamMembers_SquadFight):
    VALUE_TYPE = CCombatTeamMember_SquadFight

    def on_setattr(self, key, old, new):
        print('[CCombatTeamMembers_SquadFight] member info ', key, old, new)
        self._OnCombatTeamChange()

    @DelayCall(0)
    def _OnCombatTeamChange(self):
        genv.messenger.Broadcast(events.ON_COMBAT_TEAM_CHANGE)
        genv.messenger.Broadcast(events.ON_SHIELD_VOICE_NEED_CHANGE)

    def GetTeamSelectedHeroes(self, exclude_me=False):
        heroes = []
        player_id = genv.player.id
        for member_id, member in list(self.items()):
            if exclude_me and member_id == player_id:
                continue
            if member.select_hero_id:
                heroes.append(member.select_hero_id)
        return heroes

    def IsMemberMaxCoinsReachPrice(self, price):
        player_id = genv.player.id
        for member in self.values():
            if member.id == player_id:
                continue
            if member.coins >= price:
                return True
        return False


class CRequestBuyItems(RequestBuyItems):
    def on_append(self):
        genv.messenger.Broadcast(events.ON_SQUAD_FIGHT_SHOP_REQUEST_CHANGE)

    def on_clear(self):
        genv.messenger.Broadcast(events.ON_SQUAD_FIGHT_SHOP_REQUEST_CHANGE)


@with_tag("IsCombatTeam")
class CombatTeamEntity_SquadFight(CombatTeamEntity):
    Property('member_dict', CCombatTeamMembers_SquadFight)
    Property('req_buy_items', CRequestBuyItems)


@with_tag("IsCombatTeam")
class CombatTeamEntity_GunFight(CombatTeamEntity):
    # fixme:要加个CCombatTeamMember_SquadFight和CCombatTeamMembers_GunFight
    Property('member_dict', CCombatTeamMembers_SquadFight)


@with_tag("IsCombatTeam")
class CombatTeamEntity_GrandTheft(CombatTeamEntity_Moba):
    Property('member_dict', CCombatTeamMembers_GrandTheft)
    Property('rank', 0)
