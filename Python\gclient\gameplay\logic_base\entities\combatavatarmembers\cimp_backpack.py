# -*- coding: utf-8 -*-
# author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import time
import traceback
from collections import defaultdict

from common.RpcMethodArgs import EntityID,Int
from common.classutils import Property
from common.rpcdecorator import rpc_method, CLIENT_STUB
from gclient import cconst
from gclient.config import LocalConfig

from gclient.data import combat_item_data
from gclient.framework.util import events
from gclient.gameplay.logic_base.equips.citem import Backpack
from gclient.gameplay.uicomponents.hud_frontsight_comp import HudFrontsightComp
from gclient.gameplay.uicomponents.hud_jumpword_comp_v2 import Hud<PERSON>umpwordComp as HudJumpwordCompV2
from gshare import weapon_util, consts
from gshare.consts import BackpackSlot


class CombatAvatarMember(object):
    """
    :type-self: gclient.gameplay.logic_base.entities.combat_avatar.CombatAvatar
    """

    Property('coins', 0)
    Property('backpack', Backpack)
    Property('backpack_no', 0)

    Property('cur_weapon_guid', '')
    Property('cur_spec_weapon_guid', '')
    Property('cur_lefthand_weapon_guid', '')

    Property('cur_weapon_guid_server', '')
    Property('cur_spec_weapon_guid_server', '')
    Property('cur_lefthand_weapon_guid_server', '')

    def __init_component__(self, _):
        self.next_weapon_guid = ''
        self.next_spec_weapon_guid = ''
        self.next_lefthand_weapon_guid = ''

        self.cur_weapon_guid = self.cur_weapon_guid_server
        self.cur_spec_weapon_guid = self.cur_spec_weapon_guid_server
        self.cur_lefthand_weapon_guid = self.cur_lefthand_weapon_guid_server

    def _on_set_coins(self, old):
        if self is genv.player or self is genv.replay_player:
            genv.messenger.Broadcast(events.ON_CHANGE_COINS, old)

    def _on_set_cur_weapon_guid_server(self, old):
        # 非主角的玩家，要跟随服务器的值
        if not self.IsPlayerCombatAvatar:
            self.cur_weapon_guid = self.cur_weapon_guid_server

        if not self.cur_spec_weapon_guid and self.cur_weapon_guid:
            self.model and self.model.RaiseCurWeapon(self.IsRobotCombatAvatar)

        if self.is_replay_avatar:
            genv.messenger.Broadcast(events.ON_REPLAY_CHANGE_WEAPON, self.cur_weapon_guid, old)

        self._callComponents('on_set_cur_weapon_guid')

        if self.is_replay_room:
            self._callComponents('cur_weapon_change_for_replay_room')

    def _on_set_cur_lefthand_weapon_guid_server(self, old):
        # 非主角的玩家，要跟随服务器的值
        if not self.IsPlayerCombatAvatar:
            self.cur_lefthand_weapon_guid = self.cur_lefthand_weapon_guid_server

        if self.cur_lefthand_weapon_guid:
            self.model and self.model.RaiseCurLeftHandWeapon()
        else:
            # 都没有cur_lefthand_weapon_guid了，怎么DropCurLeftHandWeapon？？？
            # self.model.DropCurLeftHandWeapon()
            self.model and self.model.ForceDropLeftHandWeapon(old)

    def _on_set_cur_spec_weapon_guid_server(self, old):
        # 非主角的玩家，要跟随服务器的值
        if not self.IsPlayerCombatAvatar:
            self.cur_spec_weapon_guid = self.cur_spec_weapon_guid_server

        if not self.cur_spec_weapon_guid:
            if self.is_replay_avatar:
                self.hand_model and self.hand_model.ForceDropWeapon(old)
            if self.next_weapon_guid and self.cur_weapon_guid != self.next_weapon_guid:
                pass
            else:
                self.model and self.model.RaiseCurWeapon()
        else:
            self.model and self.model.RaiseCurSpecWeapon()

        self._callComponents('on_set_cur_spec_weapon_guid')

    def OnAddWeaponPart(self, weapon_guid, part_id):
        if self.IsPlayerCombatAvatar:
            return
        self.model.AddWeaponPart(weapon_guid, part_id)

    def OnRefreshWeaponParts(self, weapon_guid):
        weapon_case = self.GetWeaponCase(weapon_guid, False)
        weapon_case and weapon_case.InitWeapon()

    def GetWeaponList(self):
        weapon_list = []
        for key, weapon in self.backpack.items():
            weapon_list.append(key)
        return weapon_list

    def GetWeaponPartSlots(self, weapon_guid):
        weapon = self.backpack.Get(weapon_guid)
        if weapon:
            return weapon.part_slots
        return {}

    def GetCurWeaponPartSlots(self):
        cur_weapon = self.GetCurWeapon()
        if cur_weapon:
            return cur_weapon.part_slots
        return {}

    def GetCurWeaponPartOpticType(self):
        return weapon_util.GetWeaponPartOpticType(self.GetCurWeaponPartSlots())

    def GetWeaponByGuid(self, guid):
        return self.backpack.get(guid)

    def GetCurHighPriorityWeapon(self):
        if self.cur_lefthand_weapon_guid:
            return self.backpack.Get(self.cur_lefthand_weapon_guid)
        return self.GetCurWeapon()

    def TestBackpack(self):
        weapon = self.GetCurWeapon()

        begin = time.time()
        for _ in range(100000):
            str(weapon.part_slots.dict())
            # weapon.is_dual_weapon_readonly
        print("elapsed:", time.time() - begin)

        begin = time.time()
        for _ in range(100000):
            # weapon.is_dual_weapon
            str(weapon.part_slots)
        print("elapsed:", time.time() - begin)

    def GetCurWeapon(self):
        return self.backpack.get(self.cur_spec_weapon_guid if self.cur_spec_weapon_guid else self.cur_weapon_guid)

    def GetCurSpecWeapon(self):
        return self.backpack.get(self.cur_spec_weapon_guid)

    def GetCurWeaponGunType(self):
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon or not cur_weapon.is_own_gun:
            return 0
        return cur_weapon.gun_type

    def GetCurLeftHandWeapon(self):
        return self.backpack.Get(self.cur_lefthand_weapon_guid)

    def GetCurWeaponCase(self, is_fps_weapon=True):
        return self.GetWeaponCase(self.cur_spec_weapon_guid if self.cur_spec_weapon_guid else self.cur_weapon_guid, is_fps_weapon)

    def SetCurWeaponCaseVisibility(self, visible, hide_reason, is_fps_weapon=True):
        weapon_case = self.GetCurWeaponCase(is_fps_weapon)
        if not weapon_case:
            return
        if visible:
            weapon_case.RemoveHiddenReason(hide_reason)
        else:
            weapon_case.AddHiddenReason(hide_reason)

    def GetWeaponCase(self, weapon_guid, is_fps_weapon=True):
        if is_fps_weapon:
            hand_model = self.hand_model
            if not hand_model:
                return None
            return hand_model.GetWeaponCase(weapon_guid)
        else:
            model = self.model
            if not model:
                return None
            return model.GetWeaponCase(weapon_guid)

    def GetWeaponCaseByGunId(self, gun_id, is_fps_weapon=True):
        backpack_weapon = self.backpack.GetByGunId(gun_id)
        if not backpack_weapon:
            return
        return self.GetWeaponCase(backpack_weapon.guid, is_fps_weapon)

    def GetWeaponCaseById(self, weapon_id, is_fps_weapon=True):
        backpack_weapon = self.backpack.GetByEquipId(weapon_id)
        if not backpack_weapon:
            return
        return self.GetWeaponCase(backpack_weapon.guid, is_fps_weapon)

    def GetCurLeftHandWeaponCase(self, is_fps_weapon=True):
        return self.GetWeaponCase(self.cur_lefthand_weapon_guid, is_fps_weapon)

    def GetCurSpecWeaponCase(self, is_fps_weapon=True):
        return self.GetWeaponCase(self.cur_spec_weapon_guid, is_fps_weapon)

    def GetMainGunWeaponCase(self, is_fps_weapon=True):
        main_weapon_guid = self.backpack.GetMainGunWeapon()
        return self.GetWeaponCase(main_weapon_guid, is_fps_weapon)

    def GetSubGunWeaponCase(self, is_fps_weapon=True):
        sub_weapon_guid = self.backpack.GetSubGunWeapon()
        return self.GetWeaponCase(sub_weapon_guid, is_fps_weapon)

    def GetMeleeWeaponCase(self, is_fps_weappon=True):
        weapon_guid = self.backpack.GetMeleeWeapon()
        return self.GetWeaponCase(weapon_guid, is_fps_weappon)

    def GetCurThrowableWeapon(self):
        # 取当前手上的可投掷武器，默认先取右手，再取左手
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and (weapon_util.IsWeaponThrowable(cur_weapon.equip_id) or cur_weapon.bind_throw_guid):
            return cur_weapon
        else:
            cur_weapon = self.GetCurLeftHandWeapon()
            if cur_weapon and (weapon_util.IsWeaponThrowable(cur_weapon.equip_id) or cur_weapon.bind_throw_guid):
                return cur_weapon
        return None

    def GetRealEntityOfCurThrowWeapon(self):
        cur_weapon = self.GetCurThrowableWeapon()
        if not cur_weapon:
            return
        bomb_guid = cur_weapon.bind_throw_guid
        return self.space.GetEntityByID(bomb_guid)

    def IsCurTakeMainWeapon(self):
        main_weapon_guid = self.backpack.GetMainGunWeapon()
        if not main_weapon_guid:
            return
        cur_weapon = self.GetCurWeapon()
        return cur_weapon and cur_weapon.guid == main_weapon_guid

    def IsCurTakeSubWeapon(self):
        sub_weapon_guid = self.backpack.GetSubGunWeapon()
        if not sub_weapon_guid:
            return
        cur_weapon = self.GetCurWeapon()
        return cur_weapon and cur_weapon.guid == sub_weapon_guid

    def IsCurTakeMissileWeapon(self):
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and cur_weapon.gun_type == consts.GunType.RL:
            return True
        return False

    def IsCurTakeGunWeapon(self):
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and cur_weapon.is_own_gun:
            return True
        return False

    def IsCurTakeDualGunWeapon(self):
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and cur_weapon.is_dual_weapon:
            return True
        return False

    def IsCurTakeCompoundBowWeapon(self):
        # 复合弓
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and weapon_util.IsWeaponCompoundBow(cur_weapon.equip_id):
            return True
        return False

    def IsCurTakeMeleeWeapon(self):
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and cur_weapon.is_melee:
            return True
        return False

    def IsCurTakeSamuraiWeapon(self):
        cur_weapon_case = self.GetCurWeaponCase()
        if cur_weapon_case and cur_weapon_case.IsSamuraiSword:
            return True
        return False

    def IsCurTakeGunMeleeWeapon(self):
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and cur_weapon.is_gun_melee:
            return True
        return False

    def IsCurTakeRightHandThrowableWeapon(self):
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and weapon_util.IsWeaponRightHandThrowable(cur_weapon.equip_id):
            return True
        return False

    def IsCurTakeLeftHandThrowableWeapon(self):
        cur_weapon = self.GetCurLeftHandWeapon()
        if cur_weapon and weapon_util.IsWeaponLeftHandThrowable(cur_weapon.equip_id):
            return True
        return False

    def IsCurTakeLeftHandWeapon(self):
        return self.cur_lefthand_weapon_guid != ''

    def OnWeaponListChanged(self):
        self._callComponents("on_weapon_list_changed")
        self.hand_model and self.hand_model.RefreshWeaponList()
        self.model and self.model.RefreshWeaponList()

    @rpc_method(CLIENT_STUB, EntityID())
    def OnDropWeapon(self, guid):
        self.model.ForceDropWeapon(guid)

    def OnDyeWeapon(self, guid):
        for is_fps in (True, False):
            weapon_case = self.GetWeaponCase(guid, is_fps_weapon=is_fps)
            if not weapon_case:
                continue
            weapon_item = self.backpack.get(guid)
            if not weapon_item:
                continue
            weapon_case.ChangeWeaponDecorate(
                skin_id=weapon_item.skin_template_id,
                guise_id=weapon_item.guise_template_id,
                hangings_id=weapon_item.ornament_item_id,
                sticker_info=weapon_item.skin_sticker,
            )
            weapon_case.InitWeapon()


PlayerCombatAvatar = None


class PlayerCombatAvatarMember(CombatAvatarMember):
    """
    :type-self: gclient.gameplay.logic_base.entities.combat_avatar.PlayerCombatAvatar
    """
    Property("scan_core_combat_item_guid", '')
    Property('change_weapon_stamp', 0)

    def __init_component__(self, _):
        CombatAvatarMember.__init_component__(self, _)
        self.ammos_for_cur_guns = set()
        self.client_ammo_update_timer = None
        self.client_left_ammo_update_timer = None
        self.scan_score_timer = None
        self._sound_scan_core_event = None
        self.is_fast_raise_weapon = True
        self.gun_fire_timestamp = 0
        self.dual_gun_fire_timestamp = 0
        self.pre_weapon_slot = BackpackSlot.WEAPON_1
        self.teammate_backpack_usages = defaultdict(dict)

    def _on_set_cur_weapon_guid_server(self, old):
        """ 主角不响应服务端的on_set，而是改为客户端先行 """
        pass

    def _on_set_cur_spec_weapon_guid_server(self, old):
        """ 主角不响应服务端的on_set，而是改为客户端先行 """
        pass

    def _on_set_cur_lefthand_weapon_guid_server(self, old):
        """ 主角不响应服务端的on_set，而是改为客户端先行 """
        pass

    # def _on_set_scan_core_combat_item_guid(self, old):
    #     if not self.scan_core_combat_item_guid:
    #         self._CancelScanCoreTimer()
    #         self._StopScanSound()
    #     else:
    #         scan_item = self.total_pickup_item.get(self.scan_core_combat_item_guid)
    #         if scan_item:
    #             self.StartScanCombatItem(scan_item)
    #         else:
    #             self.CallServer('ScanItemBySnare', self.scan_core_combat_item_guid, False)
    #     genv.messenger.Broadcast(events.ON_PLAYER_SCAN_TALENT_CORE_GUID, old)

    def ChangeWeaponClientAhead(self, weapon_guid, server_nodify=False):
        if weapon_guid == self.cur_weapon_guid:
            return
        if not server_nodify:
            self.CallServer('ClientChangeWeapon', weapon_guid, self.change_weapon_stamp)
        self.PlayGunSoundEventById(198)     # 客户端先行后sound_cache提前刷新，导致打断开火音效播不出来，保护一下
        old = self.cur_weapon_guid
        self.cur_weapon_guid = weapon_guid
        self._on_set_cur_weapon_guid(old)
        
        # 服务器通知的切武器需要单独告诉下第三人称的model
        if server_nodify:
            CombatAvatarMember._on_set_cur_weapon_guid_server(self, old)
        self.model.CheckWeaponState()

    def ChangeSpecWeaponClientAhead(self, weapon_guid, server_nodify=False, key_mode=cconst.UseItemMode.ITEM_DOWN):
        if weapon_guid == self.cur_spec_weapon_guid:
            return
        if not server_nodify:
            self.CallServer('ClientChangeSpecWeapon', weapon_guid, self.change_weapon_stamp)
        self.PlayGunSoundEventById(198)  # 客户端先行后sound_cache提前刷新，导致打断开火音效播不出来，保护一下
        old = self.cur_spec_weapon_guid
        self.cur_spec_weapon_guid = weapon_guid
        self._on_set_cur_spec_weapon_guid(old, key_mode)

    def ChangeLefthandWeaponClientAhead(self, weapon_guid, server_nodify=False):
        if weapon_guid == self.cur_lefthand_weapon_guid:
            return
        if not server_nodify:
            self.CallServer('ClientChangeLeftHandWeapon', weapon_guid, self.change_weapon_stamp)
        old = self.cur_lefthand_weapon_guid
        self.cur_lefthand_weapon_guid = weapon_guid
        self._on_set_cur_lefthand_weapon_guid(old)

    @rpc_method(CLIENT_STUB, EntityID())
    def OnServerChangeWeapon(self, guid):
        print("=================================OnServerChangeWeapon:", guid)
        self.ChangeWeaponClientAhead(guid, server_nodify=True)

    @rpc_method(CLIENT_STUB, EntityID())
    def OnServerChangeSpecWeapon(self, guid):
        print("=================================OnServerChangeSpecWeapon:", guid)
        self.ChangeSpecWeaponClientAhead(guid, server_nodify=True)

    @rpc_method(CLIENT_STUB, EntityID())
    def OnServerChangeLefthandWeapon(self, guid):
        print("=================================OnServerChangeLefthandWeapon:", guid)
        self.ChangeLefthandWeaponClientAhead(guid, server_nodify=True)

    def _on_set_cur_weapon_guid(self, old):
        # 第三人称的动作也先行
        # if self.is_fps_mode:
        CombatAvatarMember._on_set_cur_weapon_guid_server(self, old)

        if self.cur_weapon_guid:
            self.SetIsFastRaiseWeapon(False)
            # 系统与功能US #412141
            # 【8月冷启动】使用投掷物/技能过程中，拾取武器需打断当前状态，走一遍举枪
            if self.cur_lefthand_weapon_guid:
                self.OnTakeLeftHandWeapon('')
                self.hand_model.RaiseCurWeapon()
            elif self.cur_spec_weapon_guid:
                self.OnTakeSpecWeapon('')
            else:
                self.hand_model.RaiseCurWeapon()
        # 换枪后先关镜
        self.EnterAdsState(False, is_force_out=True, keep_delay_ads=True)
        genv.messenger.Broadcast(events.ON_CHANGE_WEAPON, self.cur_weapon_guid, old)
        if self.cur_weapon_guid != self.backpack.GetMeleeWeapon():
            self.pre_weapon_slot = BackpackSlot.WEAPON_1 if self.cur_weapon_guid == self.backpack.GetMainGunWeapon() else BackpackSlot.WEAPON_2

    def _on_set_cur_lefthand_weapon_guid(self, old):
        # 第三人称的动作也先行
        CombatAvatarMember._on_set_cur_lefthand_weapon_guid_server(self, old)
        if not self.cur_lefthand_weapon_guid:
            if self.next_weapon_guid and self.cur_weapon_guid != self.next_weapon_guid:
                # self.CallServer('ChangeWeapon', self.next_weapon_guid)
                if self.next_weapon_guid not in self.backpack:
                    self.next_weapon_guid = self.cur_weapon_guid_server
                    self.cur_weapon_guid = ''
                self.ChangeWeaponClientAhead(self.next_weapon_guid)
                self.next_weapon_guid = ''
            elif self.next_lefthand_weapon_guid:
                # self.CallServer('ChangeLeftHandWeapon', self.next_lefthand_weapon_guid)
                self.ChangeLefthandWeaponClientAhead(self.next_lefthand_weapon_guid)
                self.next_lefthand_weapon_guid = ''
            else:
                self.hand_model.ForceDropWeapon(old)
        else:
            if self.model.motion_state in cconst.CLIENT_PRIORITY_UNIT_STATE:
                self.OnTakeLeftHandWeapon('')
                return
            self.next_lefthand_weapon_guid = ''
            self.hand_model.RaiseCurLeftHandWeapon()
        genv.messenger.Broadcast(events.ON_CHANGE_LEFTHAND_WEAPON, self.cur_lefthand_weapon_guid, old)

    def _on_set_cur_spec_weapon_guid(self, old, take_key_mode=cconst.UseItemMode.ITEM_DOWN):
        # 第三人称的动作也先行
        CombatAvatarMember._on_set_cur_spec_weapon_guid_server(self, old)

        # 换枪后先关镜
        self.EnterAdsState(False, is_force_out=True)
        if not self.cur_spec_weapon_guid:
            # print '================_on_set_cur_spec_weapon_guid next_weapon_guid=', self.next_weapon_guid, 'next_spec_weapon_guid=', self.next_spec_weapon_guid, 'old =', old
            if self.next_weapon_guid and self.cur_weapon_guid != self.next_weapon_guid:
                # self.CallServer('ChangeWeapon', self.next_weapon_guid)
                if self.next_weapon_guid not in self.backpack:
                    self.next_weapon_guid = self.cur_weapon_guid_server
                    self.cur_weapon_guid = ''

                self.ChangeWeaponClientAhead(self.next_weapon_guid)
                self.next_weapon_guid = ''
            elif self.next_spec_weapon_guid and self.next_spec_weapon_guid == old:
                # 延时情况下，扔雷之后，马上掏雷，回包的时候next_spec_weapon_guid==old，如果只有下面的elif self.next_spec_weapon_guid, 就不会RaiseCurWeapon了，手消失
                self.next_spec_weapon_guid = ''
                self.hand_model.RaiseCurWeapon()
            elif self.next_spec_weapon_guid:
                # self.CallServer('ChangeSpecWeapon', self.next_spec_weapon_guid)
                self.ChangeSpecWeaponClientAhead(self.next_spec_weapon_guid)
                self.next_spec_weapon_guid = ''
            else:
                self.hand_model.RaiseCurWeapon()
        else:
            if self.model.motion_state in cconst.CLIENT_PRIORITY_UNIT_STATE:
                self.OnTakeSpecWeapon('')
                return
            # 技能的特殊武器raise选要判断输入来源，如白止切刀
            self.hand_model.RaiseCurSpecWeapon(take_key_mode)
            self.TryBreakUseMedicineOnSetCurWeapon()
        genv.messenger.Broadcast(events.ON_CHANGE_SPEC_WEAPON, self.cur_spec_weapon_guid, old)
        if not gui.is_support_touch:
            HudJumpwordCompV2.instance().OnChangeSpecWeapon(self.cur_spec_weapon_guid)

    def RaiseCurWeaponByCurWeaponGuid(self):
        if self.cur_spec_weapon_guid:
            self.hand_model.RaiseCurSpecWeapon()
        elif self.cur_weapon_guid:
            self.hand_model.RaiseCurWeapon()

    def SetWeaponParts(self, gun_id, part_dict, ignore_unlock=True):
        self.CallServer('SetWeaponParts', gun_id, part_dict, ignore_unlock)

    def ResetWeaponPartByGunId(self, gun_id):
        self.CallServer('ResetWeaponPart', gun_id)

    def AddWeaponPartByGunId(self, gun_id, part_id):
        gun = self.backpack.GetByGunId(gun_id)
        if not gun:
            return
        self.AddWeaponPart(gun.guid, part_id)

    def AddWeaponPart(self, weapon_guid, part_id):
        if weapon_guid not in self.backpack:
            return
        weapon_part_slots = self.backpack[weapon_guid].part_slots
        part_type = weapon_util.GetWeaponPartType(part_id)
        if part_type not in weapon_part_slots or weapon_part_slots[part_type]['part_id'] == part_id:
            return
        self.CallServer('AddWeaponPart', weapon_guid, part_id)

    def OnAddWeaponPart(self, weapon_guid, part_id):
        super(PlayerCombatAvatar, self).OnAddWeaponPart(weapon_guid, part_id)
        self.hand_model.AddWeaponPart(weapon_guid, part_id)
        self.model.AddWeaponPart(weapon_guid, part_id)
        # self.hand_model.RaiseCurWeapon()

    def OnRefreshWeaponParts(self, weapon_guid):
        super(PlayerCombatAvatar, self).OnRefreshWeaponParts(weapon_guid)
        weapon_case = self.GetWeaponCase(weapon_guid, True)
        weapon_case and weapon_case.InitWeapon()

    def CorrectCurrentWeapons(self):
        backpack = self.backpack
        if self.cur_spec_weapon_guid and self.cur_spec_weapon_guid not in backpack:
            self.ChangeSpecWeaponClientAhead("")
        if self.cur_lefthand_weapon_guid and self.cur_lefthand_weapon_guid not in backpack:
            self.ChangeLefthandWeaponClientAhead("")
        if self.cur_weapon_guid and self.cur_weapon_guid not in backpack:
            target_weapon = ""
            for slot in (BackpackSlot.WEAPON_1, BackpackSlot.WEAPON_2, BackpackSlot.VICE_WEAPON):
                weapon = backpack.GetBySlot(slot)
                if weapon:
                    target_weapon = weapon.guid
                    break
            if target_weapon:
                self.ChangeWeaponClientAhead(target_weapon)

    def CorrectNextWeaponGuid(self):
        # next_weapon_guid 残留后，武器变化时，更新
        backpack = self.backpack
        if self.next_weapon_guid and self.next_weapon_guid not in backpack:
            self.next_weapon_guid = ''
        if self.next_lefthand_weapon_guid and self.next_lefthand_weapon_guid not in backpack:
            self.next_lefthand_weapon_guid = ''
        if self.next_spec_weapon_guid and self.next_spec_weapon_guid not in backpack:
            self.next_spec_weapon_guid = ''

    def OnWeaponListChanged(self):
        self.CorrectNextWeaponGuid()
        self.CorrectCurrentWeapons()
        super(PlayerCombatAvatar, self).OnWeaponListChanged()
        genv.messenger.Broadcast(events.ON_PLAYER_WEAPON_LIST_CHANGE)
        self.ammos_for_cur_guns.clear()
        for backpack_item in self.backpack.values():
            if not backpack_item.is_gun:
                continue
            ammo_type = backpack_item.ammo_type
            if not ammo_type:
                continue
            self.ammos_for_cur_guns.add(ammo_type)

    def SetIsFastRaiseWeapon(self, is_fast_raise):
        self.is_fast_raise_weapon = is_fast_raise

    def CheckCanTakeWeapon(self):
        # if self.cur_lefthand_weapon_guid and self.GetCurLeftHandWeapon().is_throwable:
        #   return False
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.RaiseWeapon):
            return False
        return True

    def TakeWeaponBySlot(self, slot):
        if slot == BackpackSlot.WEAPON_1:
            self.OnTakeMainGunWeapon()
        elif slot == BackpackSlot.WEAPON_2:
            self.OnTakeSubGunWeapon()
        elif slot == BackpackSlot.VICE_WEAPON:
            self.CheckAndTakeMeleeWeapon()

    def OnTakeMainGunWeapon(self):
        if not self.CheckCanTakeWeapon():
            return
        can_take_melee = True
        if self.cur_spec_weapon_guid:
            self.OnBreakUseMedicine()
            self.OnTakeSpecWeapon('')
            can_take_melee = False
        if self.cur_lefthand_weapon_guid:
            self.hand_model.DropCurLeftHandWeapon()
            self.model.DropCurLeftHandWeapon()
            can_take_melee = False
        main_weapon_guid = self.backpack.GetMainGunWeapon()
        if not main_weapon_guid:
            return
        if main_weapon_guid == self.cur_weapon_guid:
            if not can_take_melee:
                return
            self.OnTakeMeleeWeapon()
        else:
            self.SetIsFastRaiseWeapon(False)
            self.OnTakeWeapon(main_weapon_guid)

    def OnTakeSubGunWeapon(self):
        if not self.CheckCanTakeWeapon():
            return
        can_take_melee = True
        if self.cur_spec_weapon_guid:
            self.OnBreakUseMedicine()
            self.OnTakeSpecWeapon('')
            can_take_melee = False
        if self.cur_lefthand_weapon_guid:
            self.hand_model.DropCurLeftHandWeapon()
            self.model.DropCurLeftHandWeapon()
            can_take_melee = False
        sub_weapon_guid = self.backpack.GetSubGunWeapon()
        if not sub_weapon_guid:
            return
        if sub_weapon_guid == self.cur_weapon_guid:
            if not can_take_melee:
                return
            self.OnTakeMeleeWeapon()
        else:
            self.SetIsFastRaiseWeapon(False)
            self.OnTakeWeapon(sub_weapon_guid)

    def CheckAndTakeMeleeWeapon(self):
        if not self.CheckCanTakeWeapon():
            return
        can_take_melee = True
        if self.cur_spec_weapon_guid:
            self.OnBreakUseMedicine()
            self.OnTakeSpecWeapon('')
            can_take_melee = False
        if self.cur_lefthand_weapon_guid:
            self.hand_model.DropCurLeftHandWeapon()
            self.model.DropCurLeftHandWeapon()
            can_take_melee = False
        can_take_melee and self.OnTakeMeleeWeapon()
        return can_take_melee

    def OnTakeLeftHandWeaponForce(self, guid):
        if guid:
            self.OnTakeLeftHandWeapon(guid)
        elif self.cur_lefthand_weapon_guid:
            self.hand_model.DropCurLeftHandWeapon()
            self.model.DropCurLeftHandWeapon()
            self.hand_model.cue_signal_switch_weapon('', 0)
            self.model.cue_signal_switch_weapon('', 0)

    def OnTakeCurSpecWeaponForce(self, guid):
        if guid:
            # self.CallServer('ChangeLeftHandWeapon', guid)
            self.ChangeLefthandWeaponClientAhead(guid)
        elif self.cur_spec_weapon_guid:
            self.hand_model.DropCurSpecWeapon()
            self.model.DropCurSpecWeapon()
            self.hand_model.cue_signal_switch_weapon('', 0)
            self.model.cue_signal_switch_weapon('', 0)

    def OnTakeMeleeWeapon(self):
        if not self.CheckCanTakeWeapon():
            return
        if self.IsCurTakeMeleeWeapon():
            return
        melee_weapon_guid = self.backpack.GetMeleeWeapon()
        if not melee_weapon_guid:
            return
        self.SetIsFastRaiseWeapon(False)
        self.OnTakeWeapon(melee_weapon_guid)

    def OnTakeMeleeWeaponForce(self):
        # if not self.CheckCanTakeWeapon():
        #     return
        if self.IsCurTakeMeleeWeapon():
            return
        melee_weapon_guid = self.backpack.GetMeleeWeapon()
        if not melee_weapon_guid:
            return
        self.next_weapon_guid = melee_weapon_guid
        self.hand_model.cue_signal_switch_weapon('', 0)
        self.model.cue_signal_switch_weapon('', 0)

    def OnTakeLeftHandWeapon(self, lefthand_weapon_guid):
        if not lefthand_weapon_guid and not self.cur_lefthand_weapon_guid:
            self.next_lefthand_weapon_guid = ''
            return
        if self.cur_spec_weapon_guid:
            self.OnTakeSpecWeapon('')
        # 可能会在掏了枪之后直接把左手雷掏出来，但掏枪会收掉左手雷，所以这里的处理和右手雷不同，用next来避免吞掉掏雷操作
        self.next_lefthand_weapon_guid = lefthand_weapon_guid
        if self.cur_lefthand_weapon_guid:
            self.hand_model.ChangeLeftHandWeapon()
            self.model.ChangeLeftHandWeapon()
        else:
            # self.CallServer('ChangeLeftHandWeapon', lefthand_weapon_guid)
            self.ChangeLefthandWeaponClientAhead(lefthand_weapon_guid)

    def OnTakeSpecWeapon(self, spec_weapon_guid):
        if not spec_weapon_guid and not self.cur_spec_weapon_guid:
            self.next_spec_weapon_guid = ''
            return
        if self.cur_spec_weapon_guid and self.main_spell in cconst.SPELL_ID_MEDICINES:
            # 打断打药
            self.break_use_medicine = True
        self.next_spec_weapon_guid = spec_weapon_guid
        if self.cur_lefthand_weapon_guid:
            self.OnTakeLeftHandWeapon('')
        self.model.ChangeWeapon()
        self.hand_model.ChangeWeapon()

    def OnUnloadSpecWeaponForce(self):
        if not self.cur_spec_weapon_guid:
            return
        self.next_spec_weapon_guid = ''
        self.hand_model.cue_signal_switch_weapon('', 0)
        self.model.cue_signal_switch_weapon('', 0)

    def OnTakeWeapon(self, weapon_guid):
        self.next_weapon_guid = weapon_guid
        self.model.ChangeWeapon()
        self.hand_model.ChangeWeapon()

    def CheckCanTakeWeaponForVehicle(self, weapon_guid):
        if not self.vehicle:
            return True
        if self.vehicle:
            if self.is_lean_out:
                weapon = genv.player.GetWeaponCase(weapon_guid)
                if not weapon:
                    return False
                if weapon.weapon_type != consts.EquipmentType.GUN:
                    return False
                if self.cur_weapon_guid == weapon_guid:
                    self.LeanOutOnVehicle(False)
                    return False
            else:
                self.LeanOutOnVehicle(True, weapon_guid)
                return False
        return True

    def SwitchGunWeapon(self):
        if not self.CheckCanTakeWeapon():
            return
        if self.cur_spec_weapon_guid:
            self.OnTakeSpecWeapon('')
        for equip in (self.backpack.GetBySlot(BackpackSlot.WEAPON_1), self.backpack.GetBySlot(BackpackSlot.WEAPON_2)):
            if equip and self.cur_weapon_guid != equip.guid:# and equip.is_gun:
                self.OnTakeWeapon(equip.guid)
                return True
        return

    def TakeOutPreGun(self):
        if not self.CheckCanTakeWeapon():
            return
        if self.cur_spec_weapon_guid:
            self.OnTakeSpecWeapon('')
        main_weapon = self.backpack.GetMainGunWeapon()
        sub_weapon = self.backpack.GetSubGunWeapon()
        slot_list = (BackpackSlot.WEAPON_1, BackpackSlot.WEAPON_2)
        if not main_weapon and sub_weapon:
            slot_list = (BackpackSlot.WEAPON_2, BackpackSlot.VICE_WEAPON) if not self.vehicle else (BackpackSlot.WEAPON_2,)
        elif main_weapon and not sub_weapon:
            slot_list = (BackpackSlot.WEAPON_1, BackpackSlot.VICE_WEAPON) if not self.vehicle else (BackpackSlot.WEAPON_1,)
        if self.backpack.GetMeleeWeapon() == self.cur_weapon_guid:
            if self.pre_weapon_slot == BackpackSlot.WEAPON_2:
                slot_list = (BackpackSlot.WEAPON_2, BackpackSlot.WEAPON_1)
        for slot in slot_list:
            equip = self.backpack.GetBySlot(slot)
            if equip and self.cur_weapon_guid != equip.guid:
                self.OnTakeWeapon(equip.guid)
                return True

    # 单手使用判断
    def CheckCostAmmo(self, spell_id=None, cost=True):
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon:
            return False
        if not cur_weapon.is_own_gun:
            return False

        ammo = cur_weapon.ammo
        if self.IsPlayerCombatAvatar:
            ammo = cur_weapon.client_ammo

        if weapon_util.IsWeaponCompoundBow(cur_weapon.equip_id):
            # 复合弓
            if cur_weapon.backup_ammos <= 0:
                return False

        if ammo <= 0:
            self.EndAttack()
            if cur_weapon.backup_ammos > 0:
                # @sjh 为什么要延迟？ 因为是从Cue过来的，这时候开始Reload去改Fps_Action,已经不会切Reload动作了。
                # 但是配件的挂接还在这一帧，为了避免闪帧情况，只能延迟一帧Reload
                self.add_timer(0, self.ReloadWeaponStart)
                # self.ReloadWeaponStart()
            else:
                # 空弹声音
                self.PlayGunSoundEventById(cur_weapon.equip_proto.get('single_shot_sound_id', 166), force_switch_dict={'gun_fire_state': 'dry'})
            return False

        if not cost:
            return True

        client_ammo = cur_weapon.client_ammo = max(0, cur_weapon.client_ammo - 1)
        owner = cur_weapon.get_owner()
        if owner and owner.IsPlayerCombatAvatar:
            weapon_case = self.GetCurWeaponCase()
            weapon_case and weapon_case.is_own_gun and weapon_case.RefreshAmmoAmmunition()
        if client_ammo <= 0:
            self.hand_model and self.hand_model.SetIsEmptyAmmo(client_ammo == 0)
            self.EndAttack()
        return True

    # 双手使用判断
    def CheckCostDualAmmo(self, cost=True):
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon or not cur_weapon.is_dual_weapon:
            return False

        ammo = cur_weapon.left_hand_ammo
        if self.IsPlayerCombatAvatar:
            ammo = cur_weapon.client_left_ammo

        if ammo <= 0:
            self.EndAttack(cur_weapon.vice_spell_id)
            if cur_weapon.backup_ammos > 0:
                self.ReloadDualWeaponStart()
            else:
                # 空弹声音
                self.PlayGunSoundEventById(cur_weapon.equip_proto.get('single_shot_sound_id', 166), force_switch_dict={'gun_fire_state': 'dry'})
            return False

        if not cost:
            return True

        client_left_ammo = cur_weapon.client_left_ammo = max(0, cur_weapon.client_left_ammo - 1)
        owner = cur_weapon.get_owner()
        if owner and owner.IsPlayerCombatAvatar:
            weapon_case = self.GetCurWeaponCase()
            weapon_case and weapon_case.is_own_gun and weapon_case.RefreshAmmoAmmunition()
        if client_left_ammo <= 0:
            self.hand_model and self.hand_model.SetIsDualEmptyAmmo(client_left_ammo == 0)
            self.EndAttack(cur_weapon.vice_spell_id)
        return True

    def CheckAutoChangeGun(self):
        if LocalConfig.no_ammo_auto_change_gun:
            gun_has_ammo = self.backpack.GetGunWeaponHasAmmo()
            if gun_has_ammo and not self.cur_spec_weapon_guid:
                # 使用技能的时候不能自动切
                self.OnTakeWeapon(gun_has_ammo)
                return True
        return False

    def CheckNeedReloadWeapon(self):
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon:
            return False
        if not cur_weapon.is_own_gun:
            return False
        ammo = cur_weapon.ammo
        if self.IsPlayerCombatAvatar:
            ammo = cur_weapon.client_ammo

        if ammo <= 0 and cur_weapon.backup_ammos > 0:
            # if cur_weapon.gun_type not in (consts.GunType.RL,):
            # self.ReloadWeaponStart()
            self.add_timer(0, self.ReloadWeaponStart)
            return True

        return False

    def CheckNeedReloadDualWeapon(self):
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon or not cur_weapon.is_dual_weapon:
            return False
        ammo = cur_weapon.left_hand_ammo
        if self.IsPlayerCombatAvatar:
            ammo = cur_weapon.client_left_ammo
        if ammo <= 0 and cur_weapon.backup_ammos > 1:
            self.ReloadDualWeaponStart()
            return True
        return False

    @events.ListenTo(events.ON_UPDATE_WEAPON_REST_AMMO)
    def OnUpdateBowBackupAmmos(self, ammo_type, old, new):
        if ammo_type != consts.CombatItemAmmo.MR:
            return
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon or not weapon_util.IsWeaponCompoundBow(cur_weapon.equip_id):
            return
        if not old and new:
            self.CallServer("ReloadWeapon", cur_weapon.guid, True, time.time())
            self.hand_model and self.hand_model.RaiseCurWeapon()
        if not new:
            self.CheckAutoChangeGun()

    def UpdateGunFireTimestamp(self):
        # if not self.is_shooting:
        #     return
        self.gun_fire_timestamp = time.time()

    def UpdateDualGunFireTimestamp(self):
        if not self.is_dual_shooting:
            return
        self.dual_gun_fire_timestamp = time.time()

    def CheckCanReloadWeapon(self):
        if self.vehicle and not self.is_lean_out:
            return False
        cur_weapon = self.GetCurWeapon()
        cur_weapon_case = self.GetCurWeaponCase()
        if not cur_weapon or not cur_weapon_case or cur_weapon.client_ammo >= cur_weapon_case.GetWeaponAttrValue('stock_capacity', 0):
            return False
        if cur_weapon.backup_ammos <= 0:
            cur_weapon.client_ammo <= 0 and self.PlayGunSoundEventById(cur_weapon.equip_proto.get('single_shot_sound_id', 166), force_switch_dict={'gun_fire_state': 'dry'})
            return False
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.Reload):
            return False
        return True

    def ReloadWeaponStart(self):
        # [DEBUG]
        if getattr(genv, 'forbid_reload_ammo', False):
            return
        # [DEBUG]
        if not self.CheckCanReloadWeapon():
            return
        self._ReloadWeaponStart()
        if self.reload_fire_interval_timer:
            self.cancel_timer(self.reload_fire_interval_timer)
            self.reload_fire_interval_timer = None
        if self.compensate_timer:
            self.cancel_timer(self.compensate_timer)
            self.compensate_timer = None
        self.CallServer("ReloadWeaponStart", time.time())
        # 换弹可以打断药品
        self.OnBreakUseMedicine()

    def _ReloadWeaponStart(self):
        if self.hand_model and self.hand_model.weapon_model:
            self.hand_model.JumpToState(cconst.UNIT_STATE_RELOAD)
            self.model.JumpToState(cconst.UNIT_STATE_RELOAD)

    def ForceReloadWeaponStart(self):
        self._ReloadWeaponStart()

    def ReloadWeaponStop(self):
        # 双枪右手换弹停止
        if not self.IsInReloadState():
            # 不在换弹
            return
        self.model and self.model.ReloadWeaponStop()
        self.hand_model and self.hand_model.ReloadWeaponStop()
        HudFrontsightComp.isInited() and HudFrontsightComp.instance().ShowReloadProgressRemove(cconst.StateRelationship.ReloadEnd)
        camera = genv.camera
        if camera:
            if not camera.is_fps_placer:
                return
            camera.placer.FSMRemove('ReloadAim')
            camera.placer.ClearReloadFovOffset()

    def ReloadWeaponEnd(self):
        self.hand_model.ReloadWeaponEnd()
        # if self.try_fire_start:
        #     cur_weapon = self.GetCurWeapon()
        #     if not cur_weapon:
        #         return
        #     if weapon_util.IsGunSniper(cur_weapon.gun_id):
        #         # 一键开镜自动开枪模式下的狙击枪 不能自动开枪
        #         if self.CheckFireMode() in (cconst.FireMode.FIRE_ADS, cconst.FireMode.FIRE_REAL_ADS):
        #             self.EnterAdsStateByFire(True)
        #             return
        #     self.OnPlayerFireBegin()

######## region 双持左手 #######################
    def CheckCanReloadDualWeapon(self):
        cur_weapon = self.GetCurWeapon()
        cur_weapon_case = self.GetCurWeaponCase()
        if not cur_weapon or not cur_weapon.is_dual_weapon or not cur_weapon_case or cur_weapon.client_left_ammo >= cur_weapon_case.GetWeaponAttrValue('stock_capacity'):
            return False
        if cur_weapon.backup_ammos <= 0:
            return False
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.DualReload):
            return False
        return True

    def ReloadDualWeaponStart(self):
        if not self.CheckCanReloadDualWeapon():
            return
        self._ReloadDualWeaponStart()
        # self.CallServer("ReloadDualWeaponStart", time.time())
        # 换弹可以打断药品
        self.OnBreakUseMedicine()

    def _ReloadDualWeaponStart(self):
        if self.hand_model and self.hand_model.weapon_model:
            self.hand_model.ReloadDualWeaponStart()
            self.model.JumpToState(cconst.UNIT_STATE_RELOAD)

    def ForceReloadDualWeaponStart(self):
        self._ReloadWeaponStart()

    def ReloadDualWeaponStop(self):
        # 双枪左手换弹停止
        if not self.IsInDualReloadState():
            # 不在换弹
            return
        self.model and self.model.ReloadWeaponStop()
        self.hand_model.ReloadDualWeaponStop()
        HudFrontsightComp.instance().ShowReloadLeftProgressRemove(cconst.StateRelationship.DualReloadEnd)

    def ReloadWeaponStopAll(self):
        # 双手换弹停止
        self.model and self.model.ReloadWeaponStop()
        self.hand_model.ReloadWeaponStop()
        self.hand_model.ReloadDualWeaponStop()
        HudFrontsightComp.instance().ShowReloadLeftProgressRemove(cconst.StateRelationship.DualReloadEnd)
        HudFrontsightComp.isInited() and HudFrontsightComp.instance().ShowReloadProgressRemove(
            cconst.StateRelationship.ReloadEnd)
        camera = genv.camera
        if not camera.is_fps_placer:
            return
        camera.placer.FSMRemove('ReloadAim')
        camera.placer.ClearReloadFovOffset()

    def ReloadDualWeaponEnd(self):
        self.hand_model.ReloadDualWeaponEnd()
############### endregion ##################

    @events.ListenTo(events.ON_ADS_STATE_CHANGE)
    def OnAdsChangeForReload(self, is_ads):
        if not is_ads:
            return
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon:
            return
        if not weapon_util.IsWeaponAmmunitionBreakAds(cur_weapon.part_slots):
            return
        if self.IsStateInRelationshipSet(cconst.StateRelationship.ReloadEnd) and cur_weapon.client_ammo > 0:
            self.ReloadWeaponStop()

    @rpc_method(CLIENT_STUB, EntityID(), Int(), Int())
    def OnReloadWeapon(self, guid, ammo, left_ammo=0):
        weapon = self.GetWeaponByGuid(guid)
        if not weapon:
            return
        weapon.client_ammo = ammo
        weapon.client_left_ammo = left_ammo
        self.AutoTryGunAttack(cconst.DELAY_SHOOT_REASON_EMPTY_AMMO)

        # 正确的自动开镜时机应该在reloadend后，否则会打断reload动画
        # if weapon_util.IsWeaponAmmunitionBreakAds(weapon.part_slots):
        #     # p90 上膛后自动开镜
        #     if self.try_enter_ads and not self.is_ads:
        #         self.ReloadWeaponStop()
        #         self.AutoEnterAdsState()

    @rpc_method(CLIENT_STUB, EntityID(), Int(), Int())
    def OnResetChargeAmmo(self, guid, ammo, left_ammo=0):
        weapon = self.GetWeaponByGuid(guid)
        if not weapon:
            return
        print('OnResetChargeAmmo', ammo, left_ammo)
        weapon.client_ammo = ammo
        weapon.client_left_ammo = left_ammo

    @events.ListenTo(events.ON_LINK_STATE_RELATIONSHIP_REMOVE)
    def CheckNeedInspection(self, state):
        if state != cconst.StateRelationship.ReloadEnd:
            return
        function_cls = gui.current_stage.GetFunctionCls()
        if function_cls.isInited():
            func_comp_ins = function_cls.instance()
            if 19 in genv.input_ctrl.pressed_keys or func_comp_ins.btn_reload.widget.isHighlighted():
                self.PlayerInput_OnReload()

    @events.ListenTo(events.ON_UPDATE_WEAPON_CURR_AMMO)
    def OnUpdateWeaponCurrAmmo(self, guid, ammo):
        if guid == self.cur_weapon_guid:
            # weapon_case = self.GetCurWeaponCase()
            # weapon_case and weapon_case.is_own_gun and weapon_case.RefreshAmmoModelValue()
            if not ammo:
                cur_weapon = self.GetCurWeapon()
                if cur_weapon and cur_weapon.backup_ammos <= 0:
                    self.CheckAutoChangeGun()

    def CheckCanInspectionWeapon(self):
        combat_avatar = genv.player
        if not combat_avatar:
            return False
        weapon = combat_avatar.GetCurWeapon()
        if not weapon:
            return False
        if not weapon_util.IsGunInspectionUnlocked(weapon.gun_id):
            return False
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.GunInspection):
            return False
        elif self.model.motion_state == cconst.UNIT_STATE_PRONE:
            return not self.model.is_graph_moving
        return True

    def InspectionWeaponStart(self):
        if not self.CheckCanInspectionWeapon():
            return
        if self.hand_model and self.hand_model.weapon_model:
            self.hand_model.InspectionWeaponStart()

    def InspectionWeaponEnd(self):
        if self.IsStateInRelationshipSet(cconst.StateRelationship.GunInspection) and self.hand_model:
            self.hand_model.InspectionWeaponEnd()
            self.StopSoundByTag(tag='Inspection')

    def ApplyHallBackpack(self, backpack_no):
        self.CallServer('ApplyHallBackpack', backpack_no)

    def AddHallBackpackGun(self, backpack_no, gun_id, is_trial_mode=False):
        self.CallServer('AddItemByConfigGuid', backpack_no, gun_id, is_trial_mode)

    def ChangeWeaponByGunId(self, gun_id):
        gun = self.backpack.GetByGunId(gun_id)
        if not gun:
            return
        if self.cur_weapon_guid == gun.guid:
            return
        # self.CallServer('ChangeWeapon', gun.guid)
        self.ChangeWeaponClientAhead(gun.guid)

    def IsCurWeaponPartsMatchBackpack(self, backpack_no=0, is_fps_weapon=True):
        cur_weapon_case = self.GetCurWeaponCase(is_fps_weapon)
        if not cur_weapon_case:
            return False
        if not cur_weapon_case.is_gun:
            return True
        cur_weapon_parts_client = cur_weapon_case.part_slots
        cur_weapon_parts_server = self.master.GetBackpackWeaponPartSlots(backpack_no, cur_weapon_case.gun_id)
        for part_type, part in cur_weapon_parts_server.items():
            if cur_weapon_parts_client[part_type]['part_id'] != part['part_id']:
                return False
        return True

    def CanBuyItemInBackpack(self, item_id):
        '''
        背包 item_id 是否满了, 决定能否继续购买自救针, 医疗物品, 手雷, 进化能量(护甲)
        '''
        ret = False
        item_proto = combat_item_data.data.get(item_id)
        item_type = item_proto.get("item_type", consts.CombatItemType.NONE)
        item_sub_type = item_proto.get("item_sub_type", consts.EquipmentType.NONE)
        if item_type == consts.CombatItemType.EQUIP:
            if item_sub_type == consts.EquipmentType.EMERGENCY_NEEDLE:
                needle_data = self.backpack.GetBySlot(consts.BackpackSlot.EMERGENCY_NEEDLE)
                # print(genv.GetLogPrefix(), 'needle_data=%s' % (needle_data))
                ret = bool(needle_data)
            else:
                # 先找到所有医疗物品 或者 投掷物品
                if item_sub_type == consts.EquipmentType.MEDICINE:
                    m_equips = self.backpack.GetMedicines()
                elif item_sub_type in (consts.EquipmentType.THROWABLE_WEAPON_RIGHT,
                                       consts.EquipmentType.THROWABLE_WEAPON_LEFT,
                                       consts.EquipmentType.NORMAL_TACTICAL,):
                    m_equips = self.backpack.GetTacticalWeapons()
                else:
                    m_equips = None

                if m_equips:
                    # 存在且小于堆叠数, 或者背包未满
                    can_stack = False
                    for guid, m_data in m_equips.items():
                        if m_data.item_id == item_id and m_data.count < m_data.max_stack:
                            can_stack = True
                            break
                    ret = bool(not can_stack and len(m_equips) >= 2)
        elif item_type == consts.CombatItemType.TACTICAL_ENERGY:
            ret = bool(self.tactical_level >= cconst.MAX_TACTICAL_ENERGY_LEVEL)
        return ret
    # def _CancelScanCoreTimer(self):
    #     if self.scan_score_timer:
    #         self.cancel_timer(self.scan_score_timer)
    #         self.scan_score_timer = None
    #
    # def _StopScanSound(self):
    #     if self._sound_scan_core_event:
    #         genv.sound_mgr.StopEvent(self._sound_scan_core_event)
    #         self._sound_scan_core_event = None

    # def StartScanCombatItem(self, combatitem):
    #     # 客户端实际扫描timer
    #     now = genv.GetServerNow()
    #     self._CancelScanCoreTimer()
    #     duration = combatitem.item_proto.get('core_scan_time', 3)
    #     stamp = combatitem.scan_start_time
    #     if stamp and duration > now - stamp:
    #         self.scan_score_timer = self.add_timer(duration - (now - stamp), lambda :self.PickupItem(combatitem))
    #         self._StopScanSound()
    #         self._sound_scan_core_event = genv.sound_mgr.PlayEventById(276)

    # def CanScanCombatItem(self, combat_item):
    #     # 能否扫描combat_item
    #     if self.scan_core_combat_item_guid:
    #         # 已经有扫描的了
    #         return False
    #     if not combat_item or combat_item.item_type != consts.CombatItemType.BROKEN_TALENT_CORE:
    #         return False
    #     now = genv.GetServerNow()
    #     duration = combat_item.item_proto.get('core_scan_time', 3)
    #     if combat_item.scan_avatar_id != self.id and now - combat_item.scan_start_time <= duration:
    #         # 别人正在在扫描
    #         return False
    #     return True

    @rpc_method(CLIENT_STUB, EntityID())
    def OnTeammateBuyMyAirdropSetting(self, guid):
        if guid == self.id:
            return
        combat_team = self.combat_team
        if not combat_team:
            return
        if guid not in combat_team.member_dict:
            return
        gui.Tips(cconst.THANKS_TIPS_ID_IG, extra_data=('#d1f0ff%s#E' % combat_team.member_dict[guid].name,))
        genv.sound_mgr.PlayEventById(552, is_3d=False)

    def OnDyeWeapon(self, guid):
        CombatAvatarMember.OnDyeWeapon(self, guid)
        genv.messenger.Broadcast(events.ON_DYE_WEAPON, guid)
        gui.Prompt(424)

    @events.ListenTo(events.ON_GUN_KILL_COUNT_CHANGE)
    def OnGunKillCountChanged(self, gun_id):
        if not genv.avatar:
            return
        weapon_case = self.hand_model and self.hand_model.GetCurClientWeaponCase()
        if not weapon_case:
            return
        if weapon_case.weapon_type != consts.EquipmentType.GUN:
            return
        if weapon_case.gun_id != gun_id:
            return
        weapon_case.CreateKillCounterTexture(genv.avatar.tmp_gun_kill_data.get(gun_id))

    def IsCurTakeCarriableWeapon(self):
        """检查当前是否拿着可携带武器"""
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and weapon_util.IsWeaponCarriable(cur_weapon.equip_id):
            return True
        return False

    def CheckAndTakeCarriableWeapon(self):
        """检查并拿起可携带武器"""
        if not self.CheckCanTakeWeapon():
            return
        can_take_carriable = True
        if self.cur_spec_weapon_guid:
            self.OnBreakUseMedicine()
            self.OnTakeSpecWeapon('')
            can_take_carriable = False
        if self.cur_lefthand_weapon_guid:
            self.hand_model.DropCurLeftHandWeapon()
            self.model.DropCurLeftHandWeapon()
            can_take_carriable = False
        can_take_carriable and self.OnTakeCarriableWeapon()
        return can_take_carriable

    def OnTakeCarriableWeapon(self):
        """拿起可携带武器"""
        if not self.CheckCanTakeWeapon():
            return
        if self.IsCurTakeCarriableWeapon():
            return
        if not self.backpack.GetCarriableWeapon():
            return
        self.SetIsFastRaiseWeapon(False)
        self.OnTakeSpecWeapon(self.backpack.GetCarriableWeapon())

    def OnTakeCarriableWeaponForce(self):
        """强制拿起可携带武器"""
        if self.IsCurTakeCarriableWeapon():
            return
        if not self.backpack.GetCarriableWeapon():
            return
        self.next_weapon_guid = self.backpack.GetCarriableWeapon()
        self.hand_model.cue_signal_switch_weapon('', 0)
        self.model.cue_signal_switch_weapon('', 0)
