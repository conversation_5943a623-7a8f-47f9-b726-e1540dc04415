# -*- coding: utf-8 -*-
from gclient.util.debug_log_util import SomePreset, print_s  # noqa
from gshare.ispell_mgr import SpellResult
import six2
import six2.moves.c<PERSON>ickle as cPickle
import math
import sys

import time

import GlobalData
import functools
import random

import MCharacter
import MType
import <PERSON><PERSON><PERSON>
import MEngine
import MPhysics

from gclient.framework.util import <PERSON><PERSON><PERSON><PERSON>
from common.IdManager import IdManager
from common.rpcdecorator import rpc_method, CLIENT_STUB
from common.RpcMethodArgs import EntityID, Int, Dict, Tuple, List, BinData, Str
from common.classutils import Property, CustomListType, CustomIntMapType

from gclient.config import LocalConfig
from gclient.framework.util.performance_util import EPerformanceLevel
from gshare.decorators import IntervalCall
from gshare.icombat_data import ICombatData
from gclient.framework.util.easing import easeInExpo

from gclient.gameplay.uicomponents.hud_jumpword_comp_v2 import <PERSON>d<PERSON>ump<PERSON><PERSON>omp as HudJumpwordCompV2
from gclient.gameplay.util import replay_util
from gshare.icombat_unit import ICombatUnit
from gshare.consts import CombatState, MagicFieldDestructionType
from gshare import effect_util, consts, formula, weapon_util
from gshare.ibuff import IBuffs, IBuff, IBuffShadow, IBuffsShadow
from gshare import ibuff, audio_const

from gclient.cconst import ModelPoseType
from gclient.gameplay.uicomponents.battle_injured_comp_v2 import BattleInjuredBloodComp as BattleInjuredBloodCompV2
from gclient.framework.ui import ui_harmtext, ui_define
from gclient.gameplay.uicomponents.hud_frontsight_comp import HudFrontsightComp
from gclient import cconst
from gclient.gameplay.logic_base.spell import spell_util
from gclient.data import bomb_physics_data, equip_data, spell_data, iingame_kill_feedback_data, iingame_achieve_data, \
    praise_feedback_data, string_message_data, scene_node_data
from gclient.gameplay.util.ig_voice_manager import IgVoiceManager
from gclient.framework.util import events
from gshare import naviagtor_util
from gshare.game_logic.igame_logic_parachute_comp import ParaAvatarStage, ParaAvatarReason
from gshare.iingame_achieve import InGameAchieveType

from HelenFramework.HelenUtils.Formula import y_look_at


TERRIAN_NAME = ('SM_background_dimian', 'Metro_floor', 'SM_Metro_stairs', 'Cube', 'Terrain', '58AP_L02_build_')

# FIXME: 临时做法，后面配置
DAMAGE_EFFECT_TEXT_OFFSET_INFO = [
    (3.0, 1.4, 1.5),
    (10.0, 1.5, 1.8),
    (40.0, 1.8, 2.0),
]


class BuffCategory(ibuff.BuffCategory):
    def on_setattr(self, key, old, new):
        if old is None:
            buff = self.get_owner().GetBuffs().get(next(new.iterkeys()))

            self.get_owner().OnAddBuffCategory(key, buff.level, buff.guid)
        elif new is None:
            buff = self.get_owner().GetBuffs().get(next(old.iterkeys()))
            self.get_owner().OnRemoveBuffCategory(key, buff.level, buff.guid)


class BuffCategoryShadow(ibuff.BuffCategoryShadow):
    def on_setattr(self, key, old, new):
        owner = self.get_owner()
        if not owner or owner.is_fps_avatar:
            return

        if old is None:
            buff = self.get_owner().GetBuffs().get(next(new.iterkeys()))

            self.get_owner().OnAddBuffCategory(key, buff.level, buff.guid)
        elif new is None:
            buff = self.get_owner().GetBuffs().get(next(old.iterkeys()))
            self.get_owner().OnRemoveBuffCategory(key, buff.level, buff.guid)


class CBuff(IBuff):
    def on_setattr(self, key, old, new):
        owner = self.get_owner()
        if owner:
            func = getattr(owner, "OnUpdateBuff_%s" % self.kind, None)
            func and func(self.proto)


class CBuffShadow(IBuffShadow):
    def on_setattr(self, key, old, new):
        owner = self.get_owner()
        if not owner or owner.is_fps_avatar:
            return

        if owner:
            func = getattr(owner, "OnUpdateBuff_%s" % self.kind, None)
            func and func(self.proto)


class Buffs(IBuffs):
    VALUE_TYPE = CBuff
    Property("category", BuffCategory)

    def on_setattr(self, key, old, new):
        owner = self.get_owner()
        if not owner:
            return

        if old is None and new:
            handler = getattr(owner, self.CALLBACK["add"], None)
            handler and handler(new)
        elif new is None and old:
            handler = getattr(owner, self.CALLBACK["remove"], None)
            handler and handler(old)


class BuffsShadow(IBuffsShadow):
    VALUE_TYPE = CBuffShadow
    Property("category", BuffCategoryShadow)

    def on_setattr(self, key, old, new):
        owner = self.get_owner()
        if not owner or owner.is_fps_avatar:
            return

        if old is None and new:
            handler = getattr(owner, self.CALLBACK["add"], None)
            handler and handler(new)
        elif new is None and old:
            handler = getattr(owner, self.CALLBACK["remove"], None)
            handler and handler(old)


class CCombatData(ICombatData):
    def on_setattr(self, key, old, new):
        if key == 'kills':
            genv.messenger.Broadcast(events.ON_PLAYER_KILLS_CHANGE, new)
        elif key == 'assists':
            genv.messenger.Broadcast(events.ON_PLAYER_ASSISTS_CHANGE, new)


class GunKillDataMap(CustomIntMapType):
    def on_setattr(self, key, old, new):
        owner = self.get_owner()
        if owner is genv.replay_player:
            weapon_case = owner.hand_model and owner.hand_model.GetCurClientWeaponCase()
            if not weapon_case:
                return
            if weapon_case.weapon_type != consts.EquipmentType.GUN:
                return
            if weapon_case.gun_id != key:
                return
            weapon_case.CreateKillCounterTexture(new)


class CombatAvatarMember(ICombatUnit):
    """
    :type-self: gclient.gameplay.logic_base.entities.combat_avatar.CombatAvatar
    """
    Property("buffs", Buffs)
    Property("shadow_buffs", BuffsShadow)
    Property('combat_team_id', '')
    Property('ai_attacker_id', '')
    Property("combat_data", CCombatData)
    Property("final_kill_info", b"")
    Property("tmp_gun_kill_data", GunKillDataMap)

    def __init_component__(self, _):
        self.bomb_contact_entity = None
        # 开始播扔雷动作时候的落点，因为扔雷过程中会有位移
        self.armor_recovering = False
        self.armor_effect = None
        self.extra_armor_effect = None
        self.extra_max_armor_effect = None
        self._combat_team = None

        # 回放的时候就不要限频了
        if self.is_replay_avatar:
            self.OnDealDamageResult.setInterval(0.0)

    def __show_component__(self):
        if self.combat_state != CombatState.ALIVE:
            self._on_set_combat_state(None)

    def __fini_component__(self):
        self.armor_effect and self.RemoveCameraEffect(self.armor_effect)

    @property
    def combat_team(self):
        if self._combat_team:
            return self._combat_team
        ret = self.space.GetEntity(self.combat_team_id)
        self._combat_team = ret
        return ret

    @property
    def teammate_info(self):
        combat_team = self.combat_team
        return combat_team.member_dict if combat_team else {}

    def _on_set_hero_id(self, old):
        genv.messenger.Broadcast(events.ON_COMBAT_HERO_ID_CHANGE, self)

    def _on_set_combat_state(self, old):
        if self.combat_state == CombatState.DEAD:
            self.OnDead(old)
            self.RemoveUavResidualBloodTip()
        elif self.combat_state == CombatState.ALIVE:
            if not self.has_client and self.model and (not self.IsRobotCombatAvatar and not self.IsPlayerCombatAvatar):
                self.model.ResetLocomotionGraphID()
                self.model.PopToLocomotionGraph()
                self.model.ResetLocomotionGraphParam()
            if old == CombatState.DYING:
                self.OnDying(False)
            else:
                self.OnReborn()
        elif self.combat_state == CombatState.DYING:
            self.OnDying(True)
            self.RemoveUavResidualBloodTip()
        elif self.combat_state == CombatState.GHOST:
            self.OnGhost()
        if old == CombatState.DYING and old != self.combat_state:
            # 取消队友倒地效果
            if replay_util.IsPlayerTeammate(self.id):
                self.model.HideTeammateDyingEffect()
        genv.messenger.Broadcast(events.ON_COMBAT_STATE_CHANGE, self)

    def _on_set_hp(self, old):
        if self.is_replay_room:
            self._callComponents('hp_change_for_replay_room')
        if self.is_replay_avatar:
            genv.messenger.Broadcast(events.ON_REPLAY_CHANGE_HP, self, old)
            return

        # 下面的逻辑只有主角是峰女并且玩家是主角队友才会跑，尽量减少对其他情况的调用开销。另外观战也别跑了
        # if genv.avatar.is_replaying:
        #    return
        # 屏蔽掉蜂女技能提示，后续有需求再开启
        # player = genv.player
        # if player and player.hero_id == 115 and self.combat_team_id == player.combat_team_id:
        #    self.RefreshUavResidualBloodTip()

    def _on_set_armor(self, old):
        if self.is_replay_avatar:
            genv.messenger.Broadcast(events.ON_REPLAY_CHANGE_ARMOR, self, old)
            if self.combat_state > CombatState.ALIVE:
                return
            self.PlayArmorCameraEffect(old)
        if self.is_replay_room:
            self._callComponents('armor_change_for_replay_room')

    def PlayExtraArmorCameraEffect(self):
        if self.extra_armor and self.extra_armor == self.extra_maxarmor:
            if not self.extra_max_armor_effect:
                self.extra_max_armor_effect = cconst.ICEWOWAN_EXTRA_MAX_ARMOR_EFFECT_ID
                self.AddCameraEffect(self.extra_max_armor_effect)
        else:
            if self.extra_max_armor_effect:
                self.RemoveCameraEffect(self.extra_max_armor_effect)
                self.extra_max_armor_effect = None

    def PlayArmorCameraEffect(self, old_armor):
        if gpl.performance_level <= EPerformanceLevel.LEVEL_0:
            return
        if self.armor == self.maxarmor:
            # 回满
            self.armor_recovering = False
            self.armor_effect and self.RemoveCameraEffect(self.armor_effect)
            self.armor_effect = cconst.TACTICAL_ARMOR_RECOVER_FINISH_EFFECT[self.GetTacticalColorLevel()]
            self.AddCameraEffect(self.armor_effect)
            self.extra_armor_effect and self.RemoveCameraEffect(self.extra_armor_effect)
            self.extra_armor_effect = None
        elif old_armor and not self.armor or old_armor > self.armor:
            # 破甲或者回甲过程中挨打
            self.armor_recovering = False
            self.armor_effect and self.RemoveCameraEffect(self.armor_effect)
            self.armor_effect = 224 if LocalConfig.hit_feedback_color == 0 else 620
            # if gpl.performance_level > EPerformanceLevel.LEVEL_1:
            #     self.AddCameraEffect(self.armor_effect)
            self.extra_armor_effect and self.RemoveCameraEffect(self.extra_armor_effect)
            self.extra_armor_effect = None
        elif self.armor > old_armor and not self.armor_recovering:
            # 回甲中
            self.armor_recovering = True
            self.armor_effect and self.RemoveCameraEffect(self.armor_effect)
            self.armor_effect = cconst.TACTICAL_ARMOR_RECOVER_EFFECT[self.GetTacticalColorLevel()]
            self.AddCameraEffect(self.armor_effect)

        self.PlayExtraArmorCameraEffect()

    def GetTeammateIndex(self, teammate_id):
        # 用这个接口获取队友编号吧
        teammate_info = self.teammate_info
        if teammate_id in teammate_info:
            return teammate_info[teammate_id].slot
        return 1

    def GetTeammateInfo(self, teammate_id):
        return self.teammate_info.get(teammate_id)

    def GetTeammateColorIndex(self, teammate_id):
        # 获取队友的颜色编号, 有可能队友有相同颜色
        teammate_info = self.teammate_info
        if teammate_id in teammate_info:
            return teammate_info[teammate_id].slot
        return 1

    def ClearUnit(self):
        self.PlayGunSoundEventById(198)

    def OnDead(self, old=None):
        self.ClearUnit()
        # 模式判断
        drop_weapon = False
        if genv.player.match_type == cconst.MatchType.HotSpot:
            drop_weapon = True
        self.ForceDropAllWeaponForAvatar(True, drop_weapon)
        if self.model:
            info = cPickle.loads(self.critical_spell)
            info['old_state'] = old
            self.model.OnDead(info=info)
        if self.hand_model:
            self.hand_model.OnDead()

    def OnReborn(self):
        if self.model:
            self.model.OnReborn(info=self.reborn_transform)
            # 还原溶解的效果
            self.model.ResetShaderGraphMaterialFlags()
            self.model.SetShaderGraphParameter('DissolveValue', False)
            # 还原敌人描边
            self.model.RefreshDefaultTechState()
        if self.hand_model:
            self.hand_model.OnReborn()
        genv.messenger.Broadcast(events.ON_COMBAT_AVATAR_REBORN, self)
        # 机器人OnReborn和位置有时序问题，可能会在错误的位置播了声音，加个timer先
        self.add_timer(0.1, self.DelayPlayRebornSound)
        self.ForceDropAllWeaponForAvatar(False)

    def DelayPlayRebornSound(self):
        genv.sound_mgr.PlayEventById(68, self.position)

    def OnGhost(self):
        # self.ForceDropAllWeaponForAvatar(True, drop_weapon=False)
        self.model.OnGhost()
        self.hand_model and self.hand_model.OnGhost()
    
    def DestroyGunWeaponRb(self):
        for key, weapon_case in self.model.weapon_dict.items():
            if weapon_case.is_gun:
                if not hasattr(weapon_case, 'model') or not weapon_case.model:
                    continue
                cur_weapon_entity = weapon_case.model.model
                if cur_weapon_entity:
                    cur_weapon_entity.RigidBodies = []
                # print_s(f"-----------> DestroyWeaponRb, key: {key}", SomePreset.white_fg_red_bg)

    def ForceDropAllWeaponForAvatar(self, is_hide=True, drop_weapon=False):
        cur_weapon_case = self.model.weapon_case
        # drop_weapon = False
        if is_hide:
            self.HideAttachModelForDead()
            self.model.SetVariableZ('EnableCatchWeapon', False, 0)
        else:
            # if cur_weapon_case:
            #     cur_weapon_case.AttachWeaponToAvatarHand()
            self.model.SetVariableZ('EnableCatchWeapon', True, 0)
            self.ShowAttachModelForDead()
        
        if is_hide and drop_weapon:
            self.ShowAttachModelForDead()
            # self.model.SetVariableZ('EnableCatchWeapon', False, 0)

        for weapon_case in self.model.weapon_dict.values():
            if is_hide:
                if not drop_weapon:
                    # 不是扔枪，则隐藏
                    weapon_case.AddHiddenReason(cconst.HIDDEN_REASON_DYING)
            else:
                weapon_case.RemoveHiddenReason(cconst.HIDDEN_REASON_DYING)
                entity = None
                if weapon_case.model:
                    entity = weapon_case.model.model
                if entity:
                    rbs = entity.RigidBodies
                    if len(rbs) != 0:
                        rb = rbs[0]
                        rb.PassiveMode = False
        if cur_weapon_case:
            cur_weapon_entity = None
            if cur_weapon_case.model:
                cur_weapon_entity = cur_weapon_case.model.model

            if drop_weapon and is_hide:
                # 扔枪，则不隐藏
                cur_weapon_case.RemoveHiddenReason(cconst.HIDDEN_REASON_DYING)

                if cur_weapon_entity:
                    cur_weapon_entity.Detach()
                    # cur_weapon_entity.RigidBodies = []
                    rbs = cur_weapon_entity.RigidBodies
                    cur_local_bound = MHelper.GetAttachmentsPrimLocalBound(cur_weapon_entity)
                    # genv.space.DrawBoxOverlapDebugInfo(cur_weapon_entity.Transform, 2*cur_local_bound.extend.x, 2*cur_local_bound.extend.y, 2*cur_local_bound.extend.z, life_time=15)
                    extend = cur_local_bound.extend
                    center = cur_local_bound.max + cur_local_bound.min
                    center = center * 0.5
                    local_size = (2 * extend.x, 2 * extend.y, 2 * extend.z)
                    # print_s(f"-----------> local_size: {local_size}", SomePreset.white_fg_red_bg)
                    if len(rbs) == 0:
                        # print(f"-----------> MHelper.AddDynamicRB(entity) {cur_weapon_entity}")
                        m = MType.Matrix4x3()
                        m.translation = MType.Vector3(0, center.y, -center.z)
                        MHelper.AddDynamicRB(self,
                                            cur_weapon_entity,
                                            size=local_size,
                                            local_trans=m, 
                                            filter_info=15)

                    rb = cur_weapon_entity.RigidBodies[0]
                    rb.owner = None
                    rb.SetAngularVel(MType.Vector3(0, 0, 0))
                    rb.SetLinearVel(MType.Vector3(0, 0, 0))
                    rb.PassiveMode = True
                    transform = self.model.model.Transform
                    x_axis = transform.x_axis
                    y_axis = transform.y_axis
                    y_axis = transform.y_axis
                    z_axis = transform.z_axis

                    # force_vector = MType.Vector3(
                    #     random.uniform(0, 0),
                    #     random.uniform(220, 290),
                    #     random.uniform(0, 0)
                    # )
                    force_vector = genv.drop_weapon_force
                    real_force_vec = x_axis * force_vector.x + y_axis * force_vector.y + z_axis * force_vector.z
                    rb.ApplyForceAtPosInWorldFrame(real_force_vec, MType.Vector3(0, 0, 0), MPhysics.EForceMode.Impulse)
                    # torque_vector = MType.Vector3(
                    #     random.uniform(10000, 20000),
                    #     random.uniform(0, 0),
                    #     random.uniform(-1000, 1)
                    # )
                    torque_vector = genv.drop_weapon_torque
                    real_torque_vector = x_axis * torque_vector.x + y_axis * torque_vector.y + z_axis * torque_vector.z
                    rb.ApplyTorqueInWorldFrame(real_torque_vector, MPhysics.EForceMode.Impulse)
            else:
                if cur_weapon_entity:
                    cur_weapon_entity.RigidBodies = []
                pass


    def OnDying(self, is_dying):
        # 需要重连/观战时恢复
        self.ClearUnit()
        self.ForceDropAllWeaponForAvatar(is_dying)
        self.model.PopToLocomotionGraph()
        self.model.ResetLocomotionGraphParam()
        self.model.OnKnockDown(is_dying)
        # 队友倒地需要换材质以及Outlined
        if is_dying and replay_util.IsPlayerTeammate(self.id):
            self.model.ShowTeammateDyingEffect()
        if not is_dying:
            # 被救起其他人播放音效
            genv.sound_mgr.PlayEventById(270, position=self.position)

    def _DamageUIEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        if is_friend:
            return
        if not target:
            return
        if is_system_damage:
            return
        weapon_id = damage_data.get('weapon_id', None)
        # hardcode 炮塔  TODO
        if target.is_fps_avatar and (weapon_util.IsWeaponOwnGun(weapon_id) or weapon_id in (208, 209)):
            hit_dir = damage_data.get('hit_dir')
            if isinstance(hit_dir, MType.Vector3):
                hit_dir = formula.Tuple(hit_dir)
            if not damage_data.get('is_burn'):
                # 灼伤的不弹受击方向 #by吴越
                damage = damage_data.get('damage')
                genv.messenger.Broadcast(events.ON_INJURED_GUIDE, caster.id, hit_dir, damage)
            # genv.messenger.Broadcast(events.ON_INJURED_GUIDE, caster.id, hit_dir)
        if caster.is_fps_avatar:
            is_break_armor = result.extra and result.extra.get('is_break_armor', False)
            if is_break_armor or (target.armor > 0 and not target.IsHolographicRobot):
                if is_break_armor:
                    genv.messenger.Broadcast(events.ON_BROKEN_ENEMY_ARMOR, target)
                else:
                    genv.messenger.Broadcast(events.ON_HIT_ENEMY_ARMOR, target)

    def _DamageSoundEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        if is_friend and not is_system_damage:
            return
        if not target:
            return

        if caster.is_fps_avatar or target.is_fps_avatar:
            weapon_id = damage_data.get('weapon_id')
            hit_part = damage_data.get('hit_part', '').lower()
            is_hit_head = hit_part == consts.AvatarCollisionBone_Head
            is_break_armor = result.extra and result.extra.get('is_break_armor', False)
            damage = damage_data['damage']

            sound_result = {
                'target': target,
                'caster': caster,
                'weapon_id': weapon_id,
                'is_hit_head': is_hit_head,
                'is_break_armor': is_break_armor,
                'damage': damage
            }
            self.PlayHitSoundEvent(caster.id, sound_result=sound_result)

    def _DamageModelEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        if is_friend and not is_system_damage:
            return
        if not target:
            return
        if not target.model or not target.model.isValid():
            return
        samurai_special_hit = bool(result.extra) and result.extra.get('samurai_special_hit')
        if target.is_fps_avatar:
            target.hand_model and target.hand_model.OnHit(
                info={'hit_part': damage_data.get('hit_part', consts.AvatarCollisionBone_UpperBottom),
                      'hit_dir': damage_data.get('hit_dir')})

            if samurai_special_hit:
                pull_dir = formula.Substract3D(target.position, caster.position)
                target.model.OnPull({'hit_dir': pull_dir})
        elif target.IsMonster:
            target.model.OnHit()
        else:
            target.model.OnHit(info={'hit_part': damage_data.get('hit_part', consts.AvatarCollisionBone_UpperBottom),
                                     'hit_dir': damage_data.get('hit_dir', (0, 0, 0)), 'caster_id': caster.id})

            if target.IsRobotCombatAvatar and target.is_controlled and samurai_special_hit:
                pull_dir = formula.Substract3D(target.position, caster.position)
                target.model.OnPull({'hit_dir': pull_dir})

    def _DamageGameLogicEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        # 玩法重写
        pass

    @IntervalCall(0.1)
    def OnDealDamageResult(self, result):
        damage_result = result.damage_result
        # 别的玩家造成的伤害效果，caster为其他玩家，观战的也会跑这
        caster, spell_id = result.caster, result.spell_id # noqa
        is_system_damage = result.is_system_damage
        space = self.space
        game_logic = space.game_logic
        if not game_logic:
            return
        for target_id, damage_data in damage_result.items():
            target = space.combat_avatars.get(target_id)
            damage = damage_data.get('damage', 0.0)
            if not target:
                continue
            if damage_data.get('immune_reason'):
                # 格挡了
                weapon_case = target.GetWeaponCase(target.cur_spec_weapon_guid,
                                                   is_fps_weapon=(target.is_fps_avatar and target.is_fps_mode))
                weapon_case and weapon_case.OnSpellImmune()
                continue
            if not damage:
                continue
            is_friend = target.IsCombatAvatar and game_logic.IsFriend(target, self)
            if target.is_alive:
                self._DamageUIEffect(result, caster, target, damage_data, is_system_damage, is_friend)
                self._DamageSoundEffect(result, caster, target, damage_data, is_system_damage, is_friend)
                self._DamageModelEffect(result, caster, target, damage_data, is_system_damage, is_friend)
                self._DamageGameLogicEffect(result, caster, target, damage_data, is_system_damage, is_friend)

        # if self.is_replay_avatar:
        #     from gclient.gameplay.util.replay_util import AntiPlugDebugDrawer
        #     genv.spell_result = result
        #     drawer = AntiPlugDebugDrawer.instance()
        #     start_pos = result.verify_start_pos or result.caster_pos
        #     for effect_data in result.hit_effect:
        #         hit_pos = effect_data.get("hit_pos")
        #         if not hit_pos:
        #             continue
        #         drawer.DrawSphere(start_pos)
        #         drawer.DrawSphere(hit_pos, color=(1, 0, 0))
        #         drawer.DrawLine(start_pos, hit_pos)

    @rpc_method(CLIENT_STUB, EntityID())
    def OnBeInjured(self, caster):
        pass

    def ProcessSpellResult(self, result, spell_result):
        worker = spell_util.GetSpellWorker(result.spell_id, result.level)
        if worker and hasattr(worker, 'ProcessSpellResult'):
            worker.ProcessSpellResult(result, spell_result)
        return

    def RefreshUavResidualBloodTip(self):
        player = genv.player
        tip_id = '%s_uav_blood_tip' % self.id
        if self.combat_state == CombatState.ALIVE and self.hp < 50:
            if tip_id in player.common_mark_dict:
                return
            mark_info = scene_node_data.data[20].copy()
            mark_info.update({
                'pos': self.position,
                'ientity': self.model.model,
            })
            player.CreateCommonMarkToplogoSceneOnly(tip_id, mark_info)
        else:
            if tip_id in player.common_mark_dict:
                player.DestroyCommonMarkToplogoSceneOnly(tip_id)

    def AddUavResidualBloodTip(self):
        mark_info = scene_node_data.data[20].copy()
        mark_info.update({
            'pos': self.position,
            'ientity': self.model.model,
        })
        genv.player.CreateCommonMarkToplogoSceneOnly('%s_uav_blood_tip' % self.id, mark_info)

    def RemoveUavResidualBloodTip(self):
        tip_id = '%s_uav_blood_tip' % self.id
        if genv.player and tip_id in genv.player.common_mark_dict:
            genv.player.DestroyCommonMarkToplogoSceneOnly(tip_id)

    # region 精彩时刻 里用来回放的
    @rpc_method(CLIENT_STUB, Int(), Dict())
    def OnInGameAchieveCompleted(self, kind, data):
        """ 完成局内成就
        """
        gui.Prompt(28, extra_info={'type': kind, 'kill': data.get('kill')})

    @rpc_method(CLIENT_STUB, Int(), Dict())
    def OnInGameFeedbackCompleted(self, kind, data):
        if not data:
            return
        info = {
            'armor_limit': data.get('armor_limit', 0),
            'kind': kind,
            'money': data.get('money', 0),
            'spell': data.get('spell', 0),
            'tactical_energy': data.get('tactical_energy', 0),
            'add_from': data.get('add_from', 1),
            'content': data.get('content', 0),
            'kill_score': data.get('kill_score', 0),
        }
        # if not hud_jumpword_comp_ins.isInited() or not hud_jumpword_comp_ins.IsShowing():
        #     self.add_timer(0.33, functools.partial(hud_jumpword_comp_ins.PushJumpword, info))
        # else:
        HudJumpwordCompV2.instance().PushJumpword(info)

    # endregion
    @rpc_method(CLIENT_STUB, Int(), List(), List())
    def PlayEffectPathInWorld(self, effect_id, pos, direction):
        return
        # path = effect_data.data.get(effect_id, {}).get('path')
        # if not path:
        #     return
        # MHelper.PlayEffectInWorld(path, pos, rotation=direction)


PlayerCombatAvatar = None


class PlayerCombatAvatarMember(CombatAvatarMember):
    """
    :type-self: gclient.gameplay.logic_base.entities.combat_avatar.PlayerCombatAvatar
    """
    Property("_maxssp", 4.0, Property.ALL_CLIENTS)  # 最大冲刺持续时间
    Property("ssp", 4.0, Property.ALL_CLIENTS)  # 冲刺持续时间
    Property("_minssp", 0.5, Property.ALL_CLIENTS)  #
    Property('settle_data', b'')
    Property('dying_killer', '')
    Property('dead_killer', '')
    Property('disengage_jamming_time', 0.0)
    Property('weekly_free_hero_ids', CustomListType)

    def _on_set_combat_team_id(self, old):
        self._combat_team = None
        self.RefreshIgVoiceQueues()

    @property
    def maxssp(self):
        return self._maxssp + self.combat_attr.CalResult(self._maxssp, 'supersprint_time')

    @maxssp.setter
    def maxssp(self, value):
        self._maxssp = value

    @property
    def minssp(self):
        return self._minssp + self.combat_attr.CalResult(self._minssp, 'supersprint_interval')

    @minssp.setter
    def minssp(self, value):
        self._minssp = value

    def __init_component__(self, _):
        CombatAvatarMember.__init_component__(self, _)
        self.bomb_path_effect = None
        self.tmp_forbidden_create_bomb_effect = False
        self.ig_voice_mgr = IgVoiceManager()
        self.RefreshIgVoiceQueues()
        self.bomb_path_effect_start_pos = None
        self.path_queryer = {}
        # [DEBUG]
        self.debug_spheres = []
        # [DEBUG]

    def RefreshIgVoiceQueues(self):
        self.ig_voice_mgr.RefreshQueues()

    def __post_component__(self, _):
        self.ssp = self.maxssp = float(self.design_combat_properties.get("supersprint_time", 4.0))
        self.minssp = float(self.design_combat_properties.get('supersprint_interval', 0.5))
        self.auto_send_interval_last_yaw = 0.0
        self.auto_send_interval_last_pos = self.position
        self.auto_send_interval_duration = 0.0

        self.weapon_guid_before_dying = ''
        # 临时关闭，跟TopSpeed有冲突了`
        # self.add_repeat_timer(0.1, functools.partial(self.AutoSendIntervalOnTick, 0.1))

        self.damage_text_mode = 0  # TODO: 后面有UI支持了改掉, 现在默认不开启
        self.damage_text_cache_info = {}  # entity_id: [text_key, timer_id, cache_num]
        self.shotgun_damage_text_timer = None
        self.shotgun_damage_cache = 0.0

    def __fini_component__(self):
        CombatAvatarMember.__fini_component__(self)
        genv.input_ctrl.ControlPlayer(None)
        self.ClearBombEffectPath()
        self.tmp_forbidden_create_bomb_effect = False
        self.ig_voice_mgr.Destroy()

    def _on_set_hero_id(self, old):
        if self.model and self.model.model:
            self.model.born_position = self.position
        genv.messenger.Broadcast(events.ON_PLAYER_COMBAT_HERO_ID_CHANGE)

    def _on_set_hp(self, old):
        genv.messenger.Broadcast(events.ON_CHANGE_HP, self, old)

    def _on_set_maxhp(self, old):
        genv.messenger.Broadcast(events.ON_CHANGE_HP, self, self.hp)

    def _on_set_extra_hp(self, old):
        genv.messenger.Broadcast(events.ON_CHANGE_EXTRA_HP, self, self.extra_hp)

    def _on_set_armor(self, old):
        genv.messenger.Broadcast(events.ON_CHANGE_ARMOR, self, old)

    def AutoSendIntervalOnTick(self, dtime):
        """ 检测玩家是否静止不动，是的话则调大SendInterval """
        if not self.AutoSendIntervalCheckStatic():
            self.auto_send_interval_duration = 0.0
            self.AutoSendIntervalSetInterval(0.095)
            return
        self.auto_send_interval_duration += dtime
        if self.auto_send_interval_duration > 0.5:
            self.AutoSendIntervalSetInterval(0.5)

    def AutoSendIntervalSetInterval(self, interval):
        poseSender = self.model.poseSender
        if poseSender:
            poseSender.SendInterval = interval

    def AutoSendIntervalCheckStatic(self):
        if not self.model_loaded:
            return False
        print("speed_level(%s), motion_state(%s), upper_motion_state(%s) pose_type(%s)" % (
            self.speed_level, self.model.motion_state, self.model.upper_motion_state, self.model.pose_type))
        if self.speed_level:
            return False
        model = self.model
        if model.motion_state != cconst.UNIT_STATE_IDLE:
            return False
        if model.upper_motion_state:
            return False
        if self.vehicle_id:
            return False
        yaw = model.yaw
        if abs(self.auto_send_interval_last_yaw - yaw) > 0.01:
            self.auto_send_interval_last_yaw = yaw
            return False
        pos = self.position
        if not formula.InRange3D(pos, self.auto_send_interval_last_pos, 0.01):
            self.auto_send_interval_last_pos = pos
            return False
        return False

    @events.ListenTo(events.ON_CHANGE_ARMOR)
    def OnChangeArmor(self, player, old_armor):
        if self.combat_state > CombatState.ALIVE:
            return
        self.PlayArmorCameraEffect(old_armor)

    def _on_set_settle_data(self, old):
        print('=============_on_set_settle_data', old, self.settle_data)
        # [DEBUG]
        genv.settle_data = self.settle_data
        if GlobalData.IsShaderCollectionRobot or GlobalData.IsShaderPrecompileListRobot:
            return
        # [DEBUG]
        genv.messenger.Broadcast(events.ON_SETTLE_DATA_COMING)
        # 转发到game_logic里面做结算
        self.CallGameLogic('ShowCalculationUI', self.settle_data, cconst.MOBA_CALCULATION_DELAY_TIME)

    def _on_set_combat_state(self, old):
        CombatAvatarMember._on_set_combat_state(self, old)
        self.BroadcastDeadHiddenUI()
        self.BroadcastDyingHiddenUI()
        genv.messenger.Broadcast(events.ON_PLAYER_COMBAT_STATE_CHANGE)

    def BroadcastDeadHiddenUI(self):
        is_hidden = self.combat_state in (CombatState.DEAD, CombatState.GHOST)
        if is_hidden:
            genv.messenger.Broadcast(events.ON_HIDE_IG_SHOP, True, ui_define.UI_HIDDENREASON_DEAD)
        genv.messenger.Broadcast(events.ON_HIDE_UI_FOR_REASON, ui_define.UI_HIDDENREASON_DEAD, is_hidden)

    def BroadcastDyingHiddenUI(self):
        is_hidden = self.combat_state == CombatState.DYING
        if is_hidden:
            genv.messenger.Broadcast(events.ON_HIDE_IG_BACKPACK)
        genv.messenger.Broadcast(events.ON_HIDE_UI_FOR_REASON, ui_define.UI_HIDDENREASON_DYING, is_hidden)

    def ClearUnitForCalculation(self):
        self.break_use_medicine = True
        self.EndAttack()
        self.ClearBombEffectPath()
        self.tmp_forbidden_create_bomb_effect = False
        self.EnterAdsState(False, is_force_out=True)
        # 隐藏准心
        HudFrontsightComp.isInited() and HudFrontsightComp.instance().Close()
        self.ChangePoseType(ModelPoseType.Stand)
        self.model.ResetLocomotionGraphParam()
        self.ClearStateRelationship()
        # 关闭自动拾取 TODO
        self.StopSoundByTag('Pant')
        self.StopSoundByTag('Heartbeat')
        self.CancelEmoteState(False, False)

    def ForceOffStropDying(self):
        pass

    def ClearUnit(self):
        self.break_use_medicine = True
        self.EndAttack()
        cur_weapon = self.GetCurWeapon()
        if cur_weapon and cur_weapon.vice_spell_id:
            self.EndAttack(cur_weapon.vice_spell_id)
        self.ClearDelayShootReason()
        self.ClearDelayShootReason(True)

        self.try_fire_start = False
        self.try_dual_fire_start = False
        self.ClearBombEffectPath()
        self.tmp_forbidden_create_bomb_effect = False
        self.EnterAdsState(False, is_force_out=True)
        self.ReloadWeaponStop()
        self.ReloadDualWeaponStop()
        BattleInjuredBloodCompV2.instance().PlayClearAllAnim()
        # 隐藏准心
        HudFrontsightComp.isInited() and HudFrontsightComp.instance().Close()
        # 清手持状态
        self.OnTakeSpecWeapon('')
        self.OnTakeLeftHandWeapon('')
        # self.OnTakeMeleeWeaponForce()
        self.ForceOffStropDying()

        self.ChangePoseType(ModelPoseType.Stand)
        self.model.ResetLocomotionGraphParam()
        self.hand_model.ClearStatus()
        self.ClearStateRelationship()
        self.StopSoundByTag('Pant')
        self.StopSoundByTag('Heartbeat')
        self.CancelEmoteState(False, False)
        self.try_enter_ads = False

    def ResetUnit(self):
        HudFrontsightComp.instance().Show()
        BattleInjuredBloodCompV2.instance().OnClearAll()
        self.ChangePoseType(ModelPoseType.Stand)
        self.hand_model.ResetStatus()

    def OnDead(self, old=None):
        self.ClearUnit()
        # 模式判断
        drop_weapon = False
        if genv.player.match_type == cconst.MatchType.HotSpot:
            drop_weapon = True
        self.ForceDropAllWeaponForAvatar(True, drop_weapon)
        self.hand_model.OnDead()
        critical_info = cPickle.loads(self.critical_spell)
        self.model.OnDead(info=critical_info)
        genv.input_ctrl.ControlPlayer(None, handle_cursor=False, clear_inactive=False)
        # 结算数据下发后，才收到dead，导致结算相机本来设置了FreeView，然后被强行设置成Dead了。TODO
        camera = genv.camera
        if camera:
            # cur_dir = genv.camera.placer.placer.Direction
            if not self.IsInExecute():
                camera.ApplyCamera(cconst.CAMERA_ID_DEATH)
                camera.placer.FollowKiller(self.dead_killer)
            else:
                camera.ApplyCamera(cconst.CAMERA_ID_COMMON_TPS, {'need_blender': False, })

            # genv.camera.placer.AimToHitDirection(cur_dir, cur_dir)
        genv.sound_mgr.PlayEventById(audio_const.SoundEventID.HeartBeat_Self_Dead)
        genv.sound_mgr.SetGlobalParameter('player_dying_hp', 0)
        self.SetAllWwiseParameter('player_dying_hp', 0)

    def OnReborn(self):
        self.ResetUnit()
        self.ForceDropAllWeaponForAvatar(False)
        self.DestroyGunWeaponRb()
        input_ctrl = genv.input_ctrl
        input_ctrl.ControlPlayer(self, handle_cursor=False)
        input_ctrl.RemoveBanInputReason(cconst.GameInputType.MouseMove, cconst.BAN_INPUT_REASON_DEAD)
        input_ctrl.RemoveBanInputReason(cconst.GameInputType.MouseInput, cconst.BAN_INPUT_REASON_DEAD)
        input_ctrl.RemoveBanInputReason(cconst.GameInputType.MoveInput, cconst.BAN_INPUT_REASON_DEAD)
        input_ctrl.RemoveBanInputReason(cconst.GameInputType.MouseMove, cconst.BAN_INPUT_REASON_GRAPH_CONTROL)
        # self.ForceClearSwimState()
        # resetunit已经跑过了
        # if self.hand_model:
        #     self.hand_model.OnReborn()
        if self.model:
            self.model.OnReborn(info=self.reborn_transform)
        camera = genv.camera
        if camera:
            camera.RestoreCamera()
        genv.sound_mgr.PlayEventById(68, self.position)
        genv.sound_mgr.SetGlobalParameter('player_dying_hp', self.cur_maxhp)
        genv.sound_mgr.SetState('life_state', "out_of_dying")
        self.SetAllWwiseParameter('player_dying_hp', self.cur_maxhp)
        genv.messenger.Broadcast(events.ON_COMBAT_AVATAR_REBORN, self)
        genv.input_ctrl.ReDownAllInactive()

    def OnDying(self, is_dying):
        if is_dying:
            self.EndAttack()
            cur_weapon = self.GetCurWeapon()
            if cur_weapon and cur_weapon.vice_spell_id:
                # 大部分枪只用调用一次
                self.EndAttack(cur_weapon.vice_spell_id)
            self.ClearDelayShootReason()
            self.ClearDelayShootReason(True)
            self.try_fire_start = False
            self.try_dual_fire_start = False
            self.ClearBombEffectPath()
            self.ClearPrePlaceEntity()
            self.ForceOffStropDying()
            self.weapon_guid_before_dying = self.cur_weapon_guid
            self.OnTakeSpecWeapon('')
            self.OnTakeLeftHandWeapon('')
            self.OnTakeMeleeWeaponForce()
            self.try_enter_ads = False
        else:
            genv.sound_mgr.PlayEventById(270)
        self.EnterAdsState(False, is_force_out=True)
        self.model.PopToLocomotionGraph()
        self.model.ResetLocomotionGraphParam()
        self.model.OnKnockDown(is_dying)
        self.hand_model.OnKnockDown(is_dying)
        self.ForceDropAllWeaponForAvatar(is_dying)
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.KnockDown, is_dying)
        self.RefreshSpeedAndMotion()
        if is_dying:
            genv.camera.ApplyCamera(cconst.CAMERA_ID_DYING)
        else:
            genv.camera.ApplyCamera(cconst.CAMERA_ID_DEFAULT, {'need_blender': False, })
        
        if not is_dying:
            for buff_guid, buff in self.GetBuffs().items():
                self.RestoreBuffCameraEffect(buff)

    def OnGhost(self):
        # 450146 【8月冷启动】【PC端体验优化】自动切出鼠标优化。 这里幽灵状态就不开启鼠标了吧
        genv.input_ctrl.ControlPlayer(None, handle_cursor=False, clear_inactive=False)
        self.hand_model.OnGhost()
        self.model.OnGhost()
        # self.ForceDropAllWeaponForAvatar(True)

    def _PrepareShotgunDamageText(self, result, caster, target, damage_data, is_system_damage, is_friend):
        self.shotgun_damage_cache += damage_data['damage']
        self.shotgun_damage_text_timer and self.cancel_timer(self.shotgun_damage_text_timer)
        self.shotgun_damage_text_timer = self.add_timer(0.0, functools.partial(
            self._DamageTextEffect, result, caster, target, damage_data, is_system_damage, is_friend, True
        ))

    def _DamageTextEffect(self, result, caster, target, damage_data, is_system_damage, is_friend, is_shotgun=False):
        _damage = damage_data['damage']
        if is_shotgun:
            _damage = self.shotgun_damage_cache
            self.shotgun_damage_cache = 0.0
            self.shotgun_damage_text_timer = None
        if is_friend:
            return
        if not target or target is self:
            return
        if is_system_damage:
            return
        if target.IsMagicField and target.destruction == MagicFieldDestructionType.NONE:
            # 导弹打击炸到自己预警法术场
            return
        if damage_data.get('is_burn'):
            # 灼伤的不弹伤害挑字
            return
        _color = (255, 48, 48) if damage_data.get('hit_part', consts.AvatarCollisionBone_UpperBottom).lower() == consts.AvatarCollisionBone_Head else (255, 255, 255)
        target_id, target_pos = target.id, target.position
        cur_dis_3d = formula.Distance3D(target_pos, caster.position)
        last_limit = 0.0
        for _limit_dis, _left, _right in DAMAGE_EFFECT_TEXT_OFFSET_INFO:
            if cur_dis_3d < _limit_dis:
                off_pos = (0.0, formula.LerpNumber(_left, _right, (cur_dis_3d - last_limit) / 3.0), 0.0)
                break
            last_limit = _limit_dis
        else:
            off_pos = (0.0, 1.9, 0.0)
        if self.damage_text_mode > 1:
            cur_target_info = self.damage_text_cache_info.setdefault(target_id, [None, None, 0.0])
            cur_target_info[-1] += _damage
            if cur_target_info[0] is None:
                cur_target_info[0] = ui_harmtext.ShowDamageText(
                    target_pos, str(int(_damage)), itype=6, color=(255, 48, 48), off_pos=off_pos, offset_2d=(35, 0)
                )
            else:
                ui_harmtext.UpdateDamageText(
                    cur_target_info[0], str(int(cur_target_info[-1])), formula.Add3D(target_pos, off_pos),
                    itype=6, color=(255, 48, 48), offset_2d=(35, 0), restart=True
                )
                cur_target_info[1] and self.cancel_timer(cur_target_info[1])
                if self.damage_text_mode == 3:
                    ui_harmtext.ShowDamageText(target_pos, str(int(_damage)), color=_color, off_pos=off_pos,
                                               offset_2d=(0, 20))
            cur_target_info[1] = self.add_timer(1.5, functools.partial(self._ClearDamageTextCache, target_id))

        if self.damage_text_mode == 1:
            ui_harmtext.ShowDamageText(target_pos, str(int(_damage)), color=_color, off_pos=off_pos, offset_2d=(0, 20))

    def _ClearDamageTextCache(self, entity_id):
        self.damage_text_cache_info.pop(entity_id, None)

    def _DamageUIEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        if is_friend:
            return
        if not target:
            return
        if is_system_damage:
            return
        if target.IsMagicField and target.destruction == MagicFieldDestructionType.NONE:
            # 导弹打击炸到自己预警法术场
            return
        if target.IsCombatAvatar and not target.IsHolographicRobot:
            spell_id = result.spell_id
            ui_effect_func = getattr(self, '_DamageUIEffect%s' % spell_id, None)
            if ui_effect_func:
                ui_effect_func(result, caster, target, damage_data, is_system_damage, is_friend)
            elif result.extra and result.extra.get('is_break_armor', False):
                genv.messenger.Broadcast(events.ON_BROKEN_ENEMY_ARMOR, target)
            elif target.armor > 0:
                if damage_data['damage'] >= target.armor:
                    genv.messenger.Broadcast(events.ON_BROKEN_ENEMY_ARMOR, target)
                else:
                    genv.messenger.Broadcast(events.ON_HIT_ENEMY_ARMOR, target)
                # if weapon_util.GetWeaponGunType(damage_data.get('weapon_id')) in (consts.GunType.SG, ):
                #     # 霰弹多发子弹伤害，每颗子弹是一个封包，客户端先扣一下armor
                #     target.armor = max(target.armor - damage_data['damage'], 0)
        elif target.IsSimpleCombatUnit and not target.IsMonster:
            img_icon = self.GetImgIconByTarget(target)
            genv.messenger.Broadcast(events.ON_HIT_ENEMY_ARMOR, target, img_icon)
        genv.messenger.Broadcast(events.ON_FRONTSIGHT_HIT_ANIM,
                                 damage_data.get('hit_part', consts.AvatarCollisionBone_UpperBottom), damage_data)

    # 参考hud_brokenarmor_comp_v2.py 的 BROKEN_ARMOR_ICON
    def GetImgIconByTarget(self, target):
        if target.IsMagicField:
            if target.IsThisMagicFieldRealField():
                return 2
            else:
                return 3
        elif target.IsVehicle:
            return 3
        else:
            return 1

    def _DamageUIEffect234(self, result, caster, target, damage_data, is_system_damage, is_friend):
        # 这里的逻辑貌似都默认先行了，而234是法术场伤害，在服务端触发结算的，来到客户端，armor已经扣了
        if result.extra and result.extra.get('is_break_armor', False):
            genv.messenger.Broadcast(events.ON_BROKEN_ENEMY_ARMOR, target)
        else:
            genv.messenger.Broadcast(events.ON_HIT_ENEMY_ARMOR, target)

    def _DamageSoundEffect(self, result, caster, target, damage_data, is_system_damage, is_friend, server_check=False):
        # if is_friend and not is_system_damage:
        #   return
        if not target:
            return
        if self.settle_data:
            # 结算了
            return
        # 我打别人
        if caster.id == target.id:
            # 毒圈伤害的caster是自己
            if caster.id == genv.player.id:
                genv.sound_mgr.PlayEventById(audio_const.SoundEventID.HitSound_BeHit_Body, is_3d=False)
            return
        # PlayHitSoundEvent只处理被打对象是combatavatar的
        if target.IsCombatAvatar or target.IsMonster:
            weapon_id = damage_data.get('weapon_id')
            hit_part = damage_data.get('hit_part', '').lower()
            is_hit_head = hit_part == consts.AvatarCollisionBone_Head
            damage = damage_data['damage']

            sound_result = {
                'target': target,
                'caster': caster,
                'weapon_id': weapon_id,
            }
            if server_check:
                # server只播碎甲
                is_break_armor = (target.armor > 0 and damage_data['damage'] > target.armor)
                if not is_break_armor:
                    return
                sound_result['is_break_armor'] = is_break_armor
            else:
                sound_result['is_hit_head'] = is_hit_head
                sound_result['damage'] = damage
            self.PlayHitSoundEvent(caster.id, sound_result=sound_result)

        elif target.IsVehicle:
            genv.sound_mgr.PlayEventById(281, is_3d=False)

    def _DamageModelEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        if is_friend and not is_system_damage:
            return
        if not target:
            return
        if not target.model or not target.model.isValid():
            return
        samurai_special_hit = bool(result.extra) and result.extra.get('samurai_special_hit')
        if target.is_fps_avatar:
            target.hand_model and target.hand_model.OnHit(
                info={'hit_part': damage_data.get('hit_part', consts.AvatarCollisionBone_UpperBottom),
                      'hit_dir': damage_data.get('hit_dir')})
        elif target.IsCombatAvatar:
            target.model.OnHit(info={'hit_part': damage_data.get('hit_part', consts.AvatarCollisionBone_UpperBottom),
                                     'hit_dir': damage_data.get('hit_dir', (0, 0, 0)), 'caster_id': caster.id})
            if target.IsRobotCombatAvatar and target.is_controlled and samurai_special_hit:
                pull_dir = formula.Substract3D(target.position, caster.position)
                target.model.OnPull({'hit_dir': pull_dir})
        elif target.IsMonster:
            target.model.OnHit()

    def _DamageGameLogicEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        # 玩法重写
        pass

    def _PrepareShotgunDamageTextV2(self, result, caster, target, damage_data, is_system_damage, is_friend):
        self.shotgun_damage_cache += damage_data['damage']
        self.shotgun_damage_text_timer and self.cancel_timer(self.shotgun_damage_text_timer)
        self.shotgun_damage_text_timer = self.add_timer(0.0, functools.partial(
            self._DamageTextEffectV2, result, caster, target, damage_data, is_system_damage, is_friend, True
        ))

    def _DamageTextEffectV2(self, result, caster, target, damage_data, is_system_damage, is_friend, is_shotgun=False):
        _damage = damage_data['damage']
        if is_shotgun:
            _damage = self.shotgun_damage_cache
            self.shotgun_damage_cache = 0.0
            self.shotgun_damage_text_timer = None
        if is_friend:
            return
        if not target or target is self:
            return
        if is_system_damage:
            return
        if target.IsMagicField and target.destruction == MagicFieldDestructionType.NONE:
            # 导弹打击炸到自己预警法术场
            return
        if damage_data.get('is_burn'):
            # 灼伤的不弹伤害挑字
            return
        if hasattr(target, 'combat_state') and target.combat_state != CombatState.ALIVE:
            # 濒死状态下不播伤害跳字
            return
        hit_pos = None
        if len(result.hit_effect) > 0:
            hit_effect_getter = result.hit_effect[0].get
            hit_pos = hit_effect_getter('hit_pos')
        is_hit_head = damage_data.get('hit_part',
                                      consts.AvatarCollisionBone_UpperBottom).lower() == consts.AvatarCollisionBone_Head
        HudFrontsightComp.instance().ShowDamageText(target, int(_damage), is_hit_head, hit_pos)

    def OnDealDamageResult(self, result):
        damage_result = result.damage_result
        # 自己造成的伤害效果，caster为player
        caster, spell_id = result.caster, result.spell_id # noqa
        is_system_damage = result.is_system_damage
        if is_system_damage:
            return self.OnDealSystemDamageResult(result, damage_result)
        if caster is not self:
            return
        game_logic = self.game_logic
        if not game_logic:
            return
        for target_id, damage_data in damage_result.items():
            target = self.space.GetEntityByID(target_id)
            if not target:
                continue

            if damage_data.get('immune_reason'):
                # 格挡了
                weapon_case = target.GetWeaponCase(target.cur_weapon_guid, is_fps_weapon=False)
                weapon_case and weapon_case.OnSpellImmune()
                genv.messenger.Broadcast(events.ON_HIT_ENEMY_IMMUNE)
                continue

            is_friend = target.IsCombatAvatar and not game_logic.CanDamage(target, caster)
            args = (result, caster, target, damage_data, is_system_damage, is_friend)
            if target.is_alive:
                # self._DamageUIEffect(*args)
                self._DamageSoundEffect(*args)
                self._DamageModelEffect(*args)
                self._DamageGameLogicEffect(*args)
                    
    def ShowDamageEffect(self, cur_result):
        result = cur_result
        if not isinstance(cur_result, SpellResult):
            result = SpellResult()
            result.Unpack(cur_result)
        damage_result = result.damage_result
        caster, spell_id = self, result.spell_id # noqa
        game_logic = self.game_logic
        if not game_logic:
            return
        for target_id, damage_data in damage_result.items():
            target = self.space.GetEntityByID(target_id)
            if not target:
                continue
            is_friend = target.IsCombatAvatar and not game_logic.CanDamage(target, caster)
            args = (result, caster, target, damage_data, result.is_system_damage, is_friend)
            if target.is_alive:
                self._DamageUIEffect(*args)
                self._DamageSoundEffect(*args, server_check=True)
            if LocalConfig.damage_harmtext:
                if weapon_util.GetWeaponGunType(damage_data.get('weapon_id')) in (consts.GunType.SG,):
                    self._PrepareShotgunDamageTextV2(*args)
                else:
                    self._DamageTextEffectV2(*args)

    def OnDealSystemDamageResult(self, result, damage_result):
        caster, spell_id = self, result.spell_id # noqa
        for target_id, damage_data in damage_result.items():
            target = self.space.GetEntityByID(target_id)
            if not target or target is not self:
                continue
            if target.is_alive and target.combat_state != CombatState.DYING:
                self._DamageUIEffect(result, caster, target, damage_data, True, True)
                self._DamageSoundEffect(result, caster, target, damage_data, True, True)
            # self._DamageModelEffect(result, caster, target, damage_data, True, True)

    def OnChangeFpsMode(self):
        self.DealWithBombPathEffect()
        self.DealWithModelHidden()
        # for buff_guid, buff in self.GetBuffs().iteritems():
        #     self.RestoreBuffCameraEffect(buff)
    
    def DealWithModelHidden(self):
        return
        # FPS模式下隐藏影子
        if self.is_fps_mode:
            self.model.AddHiddenReason(cconst.HIDDEN_REASON_3P_HIDE_SHADOW)
        else:
            self.model.RemoveHiddenReason(cconst.HIDDEN_REASON_3P_HIDE_SHADOW)

    def DealWithBombPathEffect(self):
        if not self.bomb_path_effect:
            return
        throw_item = self.throw_item
        self.AddBombPathEffect(throw_item.speed, throw_item.physics_proto)

    @events.ListenTo(events.ON_CHANGE_SPEC_WEAPON)
    def OnChangeSpecWeaponForBombEffect(self, guid, old=''):
        if not guid and not self.cur_lefthand_weapon_guid:
            self.ClearBombEffectPath()

    @events.ListenTo(events.ON_CHANGE_LEFTHAND_WEAPON)
    def OnChangeLeftHandWeaponForBombEffect(self, guid, old=''):
        if not guid and not self.cur_spec_weapon_guid:
            self.ClearBombEffectPath()

    def AddBombPathEffect(self, bomb_speed, weapon_proto):
        # 手雷特效路径
        # bomb_speed可调
        self.ClearBombEffectPath()
        if self.tmp_forbidden_create_bomb_effect:
            self.tmp_forbidden_create_bomb_effect = False
            return
        # Dying Death之后不要创建了
        if self.combat_state in (CombatState.DYING, CombatState.DEAD):
            return
        mat = MType.Matrix4x3()
        mat.translation = MType.Vector3(*self.position)
        effect = effect_util.WrapperPlayEffectInWorld2(cconst.BOMB_PATH_EFFECT_ID, mat, insure_play=True)
        if not effect:
            return
        self.bomb_path_effect = MCharacter.GetEffectEntity(effect)
        if len(self.bomb_path_effect.Primitives) >= 1:
            p = self.bomb_path_effect.Primitives[0]
            p.MiscFlags = 1 | 8
       
            if self.is_fps_mode:
                p.CameraAttaching = MType.ECameraAttaching.Full
            else:
                p.CameraAttaching = MType.ECameraAttaching.NONE
            param = MObject.CreateObject("ParticleParameterConstantVector3")
            param.Value = MType.Vector3(0, 0, -bomb_speed)
            p.AddParameter("initVelocity", param)
            p.BindEvent("OnEvent", self.OnBombEffectParticleEvents)
            self.SetBombPathEffectOffset(weapon_proto)
            genv.messenger.Broadcast(events.ON_ADD_BOMB_PATH_EFFECT)

    # [DEBUG]
    def CalculateContactPosition(self, bomb_speed):
        # 测试用，模拟手雷特效路径
        import MDebug
        for sphere in self.debug_spheres:
            sphere.visible = False
        trans = self.bomb_path_effect.Transform
        start = trans.translation
        velocity = -trans.z_axis * bomb_speed + MType.Vector3(0, -10, 0)
        r = self.space.ClosestRaycast(start, start + velocity * (1 / 60.0), cconst.PHYSICS_THROWN, False)
        idx = 1
        delta_time = (1 / 60.0)
        while idx < 50 and not (r and r.IsHit):
            velocity = -trans.z_axis * bomb_speed + MType.Vector3(0, -10 * delta_time * idx, 0)
            print('CalculateContactPosition %s--->%s velocity:%s' % (start, start + velocity * delta_time, velocity))
            sphere = MDebug.Sphere()
            sphere.position = start
            sphere.radius = 0.01
            sphere.color = MType.Vector3(1, 0, 0)
            self.debug_spheres.append(sphere)
            start = start + velocity * delta_time
            r = self.space.ClosestRaycast(start, start + velocity * (1 / 60.0), cconst.PHYSICS_THROWN, False)
            idx += 1
        if r and r.IsHit:
            print('==CalculateContactPosition contact_pos =', r.Pos)

    # [DEBUG]

    def CalculateBombEffectPath(self, bomb_speed, detach_pos, begin=3, simulate_times=10, bias_square=0.25 * 0.25,
                                gravity=10, delta_time=1 / 600.0):
        # [DEBUG]
        # import MDebug
        # for sphere in self.debug_spheres:
        #     sphere.visible = False
        # [DEBUG]
        if self.bomb_path_effect:
            trans = self.bomb_path_effect.Transform
        else:
            return None, None
        start = trans.translation.tuple()
        velocity_h = formula.Mul3D(trans.z_axis.tuple(), -bomb_speed)
        # 前面几次没什么用 从后面开始算 省点次数
        idx = begin
        min_distance_square = 100
        min_time_pass = 0
        ret_pos = None
        while idx < begin + simulate_times:
            time_pass = delta_time * idx
            distance_h = formula.Mul3D(velocity_h, time_pass)
            distance_v = (0, -0.5 * gravity * time_pass * time_pass, 0)
            pos = formula.Add3D(formula.Add3D(start, distance_h), distance_v)
            idx += 1
            distance_square = formula.Distance3DSquare(pos, detach_pos)
            if distance_square < min_distance_square:
                min_distance_square = distance_square
                ret_pos = pos
                min_time_pass = time_pass
            # [DEBUG]
            # sphere = MDebug.Sphere()
            # sphere.position = MType.Vector3(*pos)
            # sphere.radius = 0.01
            # sphere.color = MType.Vector3(1, 0, 0)
            # self.debug_spheres.append(sphere)
            # [DEBUG]
            if distance_square < bias_square:
                break
        # 返回最近的
        return ret_pos, formula.Add3D((0, -gravity * min_time_pass, 0), velocity_h)

    def OnBombEffectParticleEvents(self, events):
        for event in events:
            if event.Event == MType.EParticleEvent.Collision:
                p = self.bomb_path_effect.Primitives[0]
                r = p.GetCollisionResult(event.Collision)
                if r.Pos:
                    pos = r.Pos
                    bomb_contact_entity = self.bomb_contact_entity
                    if bomb_contact_entity:
                        bomb_contact_entity.model.RemoveHiddenReason(cconst.HIDDEN_REASON_BOMB)
                        # bomb_contact_entity.position = new_pos
                        bomb_contact_entity.normal = r.Normal
                        m = y_look_at(r.Normal.tuple())
                        m.tranlation = r.Pos
                        if ientity := bomb_contact_entity.model.model:
                            ientity.Transform = m
                        if r.Body:
                            bomb_contact_entity.contact_name = r.Body.Entity.GetName() if r.Body.Entity else ''
                        # y = bomb_contact_entity.model.model.Transform.y_axis
                        # from gclient.util import debug_draw_util as DebugUtil
                        # genv.dddtt = DebugUtil.draw_arrow_line(pos, pos + y, 0.1, color =(1,1,0)) 
                    else:
                        pre_place_spell_id = None
                        throw_item = self.throw_item
                        if throw_item:
                            if throw_item.grenade_proto.get('use_pre_model_contact'):
                                pre_place_spell_id = throw_item.spell_id

                        if pre_place_spell_id:
                            param = {
                                'owner_id': self.id,
                                'position': pos.tuple(),
                                'spell_id': pre_place_spell_id,
                                'level': 1,
                                'is_valid': False,
                            }
                            self.bomb_contact_entity = genv.space.create_entity('PlaceEntity', None, bdict=param)
                        else:
                            self.bomb_contact_entity = genv.space.create_entity('CommonItem', None, bdict={
                                'position': pos.tuple(),
                                'model_id': 39,
                            })
                    self.bomb_contact_entity.valid_time = time.time()
                else:
                    if self.bomb_contact_entity:
                        self.bomb_contact_entity.model.AddHiddenReason(cconst.HIDDEN_REASON_BOMB)
                return

    def SetBombPathEffectOffset(self, weapon_proto):
        pose_type = self.pose_type
        bomb_path_effect = self.bomb_path_effect
        if self.is_fps_mode:
            if genv.use_fps_camera_control_v2:
                # 因为在新一版的挂接规则下，手臂不直接挂相机了，在使用ECameraAttaching.Full情况下，需要挂到相机上
                iCamera = MEngine.GetGameplay().Player.Camera
                bomb_path_effect.Attach(iCamera)
            else:
                bomb_path_effect.Attach(self.hand_model.model)
            bomb_path_effect.Tach.EnableTachVisible = False
            bomb_path_effect.Tach.Hardpoint = 'HP_grenade_fx'
            yaw_offset = 0
        else:
            bomb_path_effect.Attach(self.model.model)
            bomb_path_effect.Tach.EnableTachVisible = False
            bomb_path_effect.Tach.Hardpoint = 'HP_Camera'
            yaw_offset = math.pi
        # 获取策划表中的偏移
        pos_str = 'pos_0' if pose_type == 0 else 'pos_1'
        bomb_start_pos = weapon_proto.get(pos_str, (0, 0, 0))
        # [DEBUG]
        if getattr(genv, 'bomb_param', {}):
            bomb_start_pos = genv.bomb_param.get('start_pos', (0, 0, 0))
        # [DEBUG]
        pitch_str = 'pitch_0' if self.pose_type == 0 else 'pitch_1'
        pitch_offset = weapon_proto.get(pitch_str, -0.2)
        # [DEBUG]
        if getattr(genv, 'bomb_param', {}):
            pitch_offset = genv.bomb_param.get('pitch_yaw_roll', (0, 0, 0))[0]
        # [DEBUG]
        trans = bomb_path_effect.Tach.Transform
        trans.translation = MType.Vector3(*bomb_start_pos)
        self.bomb_path_effect_start_pos = MType.Vector3(*bomb_start_pos)
        trans.set_pitch_yaw_roll(pitch_offset, yaw_offset, 0)
        bomb_path_effect.Tach.Transform = trans
        self.AdjustBombEffectPathPitch()

    def AdjustBombEffectPathPitch(self):
        # 镜头滑动的时候，也要调用改特效的pitch
        bomb_path_effect = self.bomb_path_effect
        if not bomb_path_effect:
            return
        cur_weapon = self.GetCurThrowableWeapon()
        if not cur_weapon:
            return
        spell_id = equip_data.data.get(cur_weapon.equip_id, {}).get('spell_id', 3)
        physics_id = spell_data.data.get(spell_id, {}).get(1, {}).get('grenade', {}).get('physics_id', 3)
        weapon_proto = bomb_physics_data.data.get(physics_id, {})
        pitch_str = 'pitch_0' if self.pose_type == 0 else 'pitch_1'
        pitch_offset = weapon_proto.get(pitch_str, -0.2)
        # [DEBUG]
        if getattr(genv, 'bomb_param', {}):
            pitch_offset = genv.bomb_param.get('pitch_yaw_roll', (0, 0, 0))[0]
        # [DEBUG]
        camera_pitch = genv.camera.engine_camera.Transform.pitch
        if camera_pitch > 0:
            tach_trans = bomb_path_effect.Tach.Transform
            # 为了修复一个问题：当镜头往上抬到pi / 2时候，特效线会突然反向，原因是pitch_offset为正数，加上之后超过pi / 2就反向了
            # 处理方法：在靠近pi / 2的时候让pitch_offset变成0 使用了缓动函数，具体是将camera_pitch从(0, pi / 2)-->(0, 1)--[1.0 - easing()]-->(1, 0)-->(策划填的pitch_offset, 0)
            pitch_offset = pitch_offset * (1.0 - easeInExpo(camera_pitch * 2 / math.pi))
            tach_trans.pitch = pitch_offset
        else:
            tach_trans = bomb_path_effect.Tach.Transform
            tach_trans.pitch = pitch_offset

        if genv.use_fps_camera_control_v2:
            boneName = bomb_path_effect.Tach.Hardpoint
            # todo: 不确定这个会不会太耗了
            HP_Camera_bone_local_trans = self.hand_model.skeleton.GetBoneTransform('tag_camera_scripted')
            local_trans = None
            if self.is_fps_mode:
                local_trans = self.hand_model.skeleton.GetBoneTransform(boneName)
            else:
                local_trans = self.model.skeleton.GetBoneTransform(boneName)
            pos_diff = local_trans.translation - HP_Camera_bone_local_trans.translation
            # bone的上面直接算出来的偏移总是比tach的偏移差一点，没找到原因，先写死一个偏移bias
            pos_bias = MType.Vector3(0, 0, -0.2)
            tach_trans.translation = self.bomb_path_effect_start_pos + pos_diff + pos_bias
            # print(f'position: {tach_trans.translation}, pos_diff:{pos_diff}, bonelocalPos:{local_trans.translation}, HP_Camera_bone_local_trans:{HP_Camera_bone_local_trans.translation}')

        bomb_path_effect.Tach.Transform = tach_trans

    def AdjustBombEffectPitch(self):
        # debug用
        bomb_path_effect = self.bomb_path_effect
        if not bomb_path_effect:
            return
        cur_weapon = self.GetCurThrowableWeapon()
        if not cur_weapon:
            return
        pitch_offset = genv.bomb_param.get('pitch_yaw_roll', (-0.2, 0, 0))[0]
        camera_pitch = genv.camera.engine_camera.Transform.pitch
        if camera_pitch > 0:
            tach_trans = bomb_path_effect.Tach.Transform
            # 为了修复一个问题：当镜头往上抬到pi / 2时候，特效线会突然反向，原因是pitch_offset为正数，加上之后超过pi / 2就反向了
            # 处理方法：在靠近pi / 2的时候让pitch_offset变成0 使用了缓动函数，具体是将camera_pitch从(0, pi / 2)-->(0, 1)--[1.0 - easing()]-->(1, 0)-->(策划填的pitch_offset, 0)
            pitch_offset = pitch_offset * (1.0 - easeInExpo(camera_pitch * 2 / math.pi))
            tach_trans.pitch = pitch_offset
        else:
            tach_trans = bomb_path_effect.Tach.Transform
            tach_trans.pitch = pitch_offset

        if genv.use_fps_camera_control_v2:
            boneName = bomb_path_effect.Tach.Hardpoint
            # todo: 不确定这个会不会太耗了
            HP_Camera_bone_local_trans = self.hand_model.skeleton.GetBoneTransform('tag_camera_scripted')
            local_trans = None
            if self.is_fps_mode:
                local_trans = self.hand_model.skeleton.GetBoneTransform(boneName)
            else:
                local_trans = self.model.skeleton.GetBoneTransform(boneName)
            pos_diff = local_trans.translation - HP_Camera_bone_local_trans.translation
            # bone的上面直接算出来的偏移总是比tach的偏移差一点，没找到原因，先写死一个偏移bias
            pos_bias = MType.Vector3(0, 0, -0.2)
            tach_trans.translation = self.bomb_path_effect_start_pos + pos_diff + pos_bias
            # print(f'position: {tach_trans.translation}, pos_diff:{pos_diff}, bonelocalPos:{local_trans.translation}, HP_Camera_bone_local_trans:{HP_Camera_bone_local_trans.translation}')

        bomb_path_effect.Tach.Transform = tach_trans

    def AdjustBombEffectPathStartPos(self):
        # debug用
        if not self.bomb_path_effect:
            return
        bomb_start_pos = genv.bomb_param.get('start_pos', (0, 0, 0))
        trans = self.bomb_path_effect.Tach.Transform
        trans.translation = MType.Vector3(*bomb_start_pos)
        self.bomb_path_effect_start_pos = MType.Vector3(*bomb_start_pos)
        self.bomb_path_effect.Tach.Transform = trans

    def AdjustBombEffectPathSpeed(self):
        # debug用
        if not self.bomb_path_effect:
            return
        speed = genv.bomb_param.get('speed', 40.0)
        p = self.bomb_path_effect.Primitives[0]
        param = MObject.CreateObject("ParticleParameterConstantVector3")
        param.Value = MType.Vector3(0, 0, -speed)
        print('AdjustBombEffectPathSpeed', speed)
        p.AddParameter("initVelocity", param)

    def ClearBombEffectPath(self):
        if self.bomb_path_effect and self.bomb_path_effect.IsValid():
            genv.messenger.Broadcast(events.ON_DEL_BOMB_PATH_EFFECT)
            for com in self.bomb_path_effect.Primitives:
                if com is not None and com.IsActive and hasattr(com, 'Deactivate'):
                    com.Deactivate(False)
            self.bomb_path_effect.LeaveArea()
            self.bomb_path_effect = None
            if self.bomb_contact_entity:
                self.bomb_contact_entity.destroy()
                self.bomb_contact_entity = None

    @rpc_method(CLIENT_STUB, Int(), Dict())
    def OnInGameAchieveCompleted(self, kind, data):
        """ 完成局内成就
        """
        gui.Prompt(28, extra_info={'type': kind, 'kill': data.get('kill')})

    @rpc_method(CLIENT_STUB, Int(), Dict())
    def OnInGameFeedbackCompleted(self, kind, data):
        if not data:
            return
        info = {
            'armor_limit': data.get('armor_limit', 0),
            'kind': kind,
            'money': data.get('money', 0),
            'spell': data.get('spell', 0),
            'tactical_energy': data.get('tactical_energy', 0),
            'add_from': data.get('add_from', 1),
            'content': data.get('content', 0),
            'kill_score': data.get('kill_score', 0),
        }
        HudJumpwordCompV2.instance().PushJumpword(info)

    @rpc_method(CLIENT_STUB, List(), Dict())
    def OnInGameSpFeedbackCompleted(self, kind_list, data):
        sp_info_getter = iingame_kill_feedback_data.data.get
        if len(kind_list) > 2:
            kind_list.sort(key=lambda kind: sp_info_getter(kind, {}).get('KillFeekbackRank', 1), reverse=True)
        info = {
            'armor_limit': data.get('armor_limit', 0),
            'kind_list': kind_list,
            'money': data.get('money', 0),
            'spell': data.get('spell', 0),
            'tactical_energy': data.get('tactical_energy', 0),
            'add_from': data.get('add_from', 1),
            'content': data.get('content', 0),
            'kill_score': 0,
            'args': [data.get(kind, None) for kind in kind_list],
        }
        HudJumpwordCompV2.instance().PushJumpword(info)

    @rpc_method(CLIENT_STUB, BinData())
    def OnSendingAITransforms(self, data):
        transforms = cPickle.loads(data)
        # TODO: transforms = {"robot_id": {"position": (100, 0, 100), "yaw": 0.2}}
        genv.messenger.Broadcast(events.ON_REFRESH_ROBOT_INFO, transforms)

    @rpc_method(CLIENT_STUB, Int(), EntityID())
    def RecvIgTeammateVoice(self, voice_id, teammate_id):
        if genv.player.id != teammate_id and genv.player.is_refuse_team_mark:
            return
        self.ig_voice_mgr and self.ig_voice_mgr.EnterVoice(voice_id, teammate_id)
        # 先借用一下  TODO
        if voice_id == 1158:
            self.ShowTeammateBorderHelpAnim(teammate_id)

    @rpc_method(CLIENT_STUB, Int(), EntityID())
    def RecvIgTeammateSound(self, sound_id, teammate_id):
        if genv.player.id != teammate_id:
            return
        genv.sound_mgr.PlayEventById(sound_id)

    def RecvRandomThanksVoice(self, teammate_id):
        voice_id = random.choice((1061, 1060, 1088))
        self.RecvIgTeammateVoice(voice_id, teammate_id)

    @rpc_method(CLIENT_STUB, Int(), EntityID(), Int())
    def RecvIgEnemyVoice(self, voice_id, player_id, hero_id):
        self.ig_voice_mgr and self.ig_voice_mgr.EnemyEnterVoice(voice_id, player_id, hero_id)

    @rpc_method(CLIENT_STUB, Int())
    def RecvCommentaryVoice(self, voice_id):
        self.ig_voice_mgr and self.ig_voice_mgr.EnterVoice(voice_id, self.id)

    @rpc_method(CLIENT_STUB, Tuple(), Tuple())
    def OnSpellPenetrateFailed(self, start_pos, end_pos):
        six2.print_func("[OnSpellPenetrateFailed]: %s %s", start_pos, end_pos, file=sys.stderr)

    def DebugEnableDumpPlayerPos(self):
        self.RemoveStoryTick(self.DebugDumpPlayerPosOnTick)
        self.AddStoryTick(self.DebugDumpPlayerPosOnTick)

    def DebugDisableDumpPlayerPos(self):
        self.RemoveStoryTick(self.DebugDumpPlayerPosOnTick)

    def DebugDumpPlayerPosOnTick(self, dt):
        print("=========================:", self.model.motion_state, self.model.upper_motion_state, self.speed_level)

    @rpc_method(CLIENT_STUB, EntityID(), Int())
    def RecvIgLikeTarget(self, teammate_id, reason):
        if not cconst.OPEN_LIKE_BUTTON:
            return
        if teammate_id == self.id:
            return
        if reason in praise_feedback_data.data:
            genv.messenger.Broadcast(events.ON_RECEIVE_THANKS_FLAG, teammate_id, reason)

    @rpc_method(CLIENT_STUB, EntityID(), Int(), Dict())
    def RecvTeammateAchievement(self, teammate_id, achieve_id, extra_data):
        # iingame_achieve_data
        # 大逃杀才弹
        if self.match_type not in (cconst.MatchType.BattleRoyale, cconst.MatchType.BattleRoyaleRank, cconst.MatchType.MobaShootingRange, cconst.MatchType.MobaShootingRangeSingle):
            return
        if achieve_id not in iingame_achieve_data.data:
            return
        prompt_id = iingame_achieve_data.data.get(achieve_id, {}).get('team_achieve_id')
        if not prompt_id:
            return
        kill = extra_data.get('kill')
        if kill is None:
            kill = achieve_id
        if '%s' in string_message_data.data.get(prompt_id, {}).get('message', ''):
            extra_data = (kill,)
        else:
            extra_data = None
        if teammate_id != self.id or prompt_id != 642:
            # 自己的642不在这里弹
            gui.Prompt(prompt_id, extra_data, extra_info={'player_id': teammate_id, 'zan_players': []})
        if teammate_id != self.id:
            # 队友成就点赞直接用tips_id算了
            genv.messenger.Broadcast(events.ON_RECEIVE_THANKS_FLAG, teammate_id, prompt_id)

    @rpc_method(CLIENT_STUB, EntityID(), Int(), Dict())
    def RecvAchievementBroadcast(self, avt_id, achieve_id, extra_data):
        # iingame_achieve_data
        if self is not genv.player:
            return

        # 大逃杀才弹
        if self.match_type not in (cconst.MatchType.BattleRoyale, cconst.MatchType.BattleRoyaleRank, cconst.MatchType.MobaShootingRange, cconst.MatchType.MobaShootingRangeSingle):
            return
        if achieve_id not in iingame_achieve_data.data:
            return

        is_my_team = extra_data.get('is_my_team', False)
        achieve_proto = iingame_achieve_data.data[achieve_id]

        # 赛事
        commentary_id  = None
        commentary_enemy_id = None

        # 策划不填表，先都写死
        if achieve_id == InGameAchieveType.ACE_STRIKER:
            commentary_id = 20008
            commentary_enemy_id = 20009
        elif achieve_id == InGameAchieveType.FIRST_KILL:
            commentary_id = 20006
            commentary_enemy_id = 20007

        if is_my_team:
            commentary_id and self.RecvCommentaryVoice(commentary_id)
        else:
            commentary_enemy_id and self.RecvCommentaryVoice(commentary_enemy_id)

        # 队友和自己
        member_ids = list(self.combat_team.member_dict.keys())
        if len(member_ids) > 1:
            member_ids.remove(self.id)
            teammate = random.choice(member_ids)

            voice_achieve_id = None
            voice_achieve_team_id = None
            voice_achieve_enemy_id = None

            if achieve_id == InGameAchieveType.ACE_STRIKER:
                voice_achieve_team_id = 1178
                voice_achieve_enemy_id = 10050
            elif achieve_id == InGameAchieveType.FIRST_KILL:
                voice_achieve_team_id = 10048
                voice_achieve_enemy_id = 10049

            if avt_id == self.id:
                if voice_achieve_id:
                    self.add_timer(5, functools.partial(self.RecvIgTeammateVoice, voice_achieve_id, self.id))
                elif voice_achieve_team_id:
                    self.add_timer(5, functools.partial(self.RecvIgTeammateVoice, voice_achieve_team_id, teammate))
            else:
                if is_my_team:
                    voice_achieve_team_id and self.add_timer(5, functools.partial(self.RecvIgTeammateVoice, voice_achieve_team_id, teammate))
                else:
                    voice_achieve_enemy_id and self.add_timer(5, functools.partial(self.RecvIgTeammateVoice, voice_achieve_enemy_id, teammate))

        if not is_my_team:
            return

        prompt_id = iingame_achieve_data.data.get(achieve_id, {}).get('team_achieve_id')
        if not prompt_id:
            return

        kill = extra_data.get('kill')
        if kill is None:
            kill = achieve_id
        if '%s' in string_message_data.data.get(prompt_id, {}).get('message', ''):
            extra_data = (kill,)
        else:
            extra_data = None
        if avt_id != self.id or prompt_id != 642:
            # 自己的642不在这里弹
            gui.Prompt(prompt_id, extra_data, extra_info={'player_id': avt_id, 'zan_players': []})

    @rpc_method(CLIENT_STUB, EntityID())
    def OnAirdropContract(self, airdrop_id):
        airdrop = self.space.GetEntityByID(airdrop_id)
        if not airdrop:
            return
        if formula.Distance3D(self.position, airdrop.position) > 50:
            return
        genv.sound_mgr.PlayEventById(128, airdrop.position)
        
    def FindPathDetail(self, src, dest, flag=naviagtor_util.NAV_POLY_FLAG_COMMON, callback=None):
        ticket = IdManager.genid()
        self.path_queryer[ticket] = callback
        self.CallServer("FindPathDetail", ticket, src, dest, flag)

    @rpc_method(CLIENT_STUB, Str(), List())
    def OnFindPathDetail(self, ticket, points):
        callback = self.path_queryer.pop(ticket, None)
        callback and callback(points)

    @rpc_method(CLIENT_STUB, Str())
    def DoShoot(self, robot_id):
        if robot_id not in self.space.combat_avatars:
            return
        robot = self.space.combat_avatars[robot_id]
        cur_weapon = robot.GetCurWeapon()
        if cur_weapon:
            spell_id = cur_weapon.equip_proto.get('spell_id', 1)
            robot.HelenRobotShootStart(spell_id, '')

    @events.ListenTo(events.ON_PLAYER_PARA_STAGE_CHANGE)
    def OnPlayerParaStageChangedForSound(self, para_stage):
        if self is not genv.player:
            return
        combat_team = self.combat_team
        if len(combat_team.member_dict.keys()) > 1 and combat_team.leader_guid == self.id:
            if para_stage in (ParaAvatarStage.OnAircraft, ):
                self.ig_voice_mgr and self.ig_voice_mgr.EnterVoice(audio_const.TeamVoiceEventID.ParachuteCommand, self.id)
            elif para_stage in (ParaAvatarStage.FreeFall, ):
                self.CallServer('CombatTeamPlayVoice', audio_const.TeamVoiceEventID.ParachuteStart, self.id)

        if para_stage in (ParaAvatarStage.LandGround, ParaAvatarStage.Finish):
            if self.para_reason == ParaAvatarReason.Reborn:
                self.CallServer('CombatTeamPlayVoice', audio_const.TeamVoiceEventID.Revive, self.id)


class MonsterCombatUnit(CombatAvatarMember):
    @property
    def unit_model_id(self):
        # 先写死做测试哈
        return self.monster_proto.get('model_id', 2008)

    def OnDealDamageResult(self, result):
        damage_result = result.damage_result
        caster, spell_id = result.caster, result.spell_id # noqa
        is_system_damage = result.is_system_damage
        if is_system_damage:
            return
        if caster is not self:
            return
        space = self.space
        game_logic = space.game_logic
        if not game_logic:
            return
        for target_id, damage_data in damage_result.items():
            target = space.GetEntityByID(target_id)
            if not target:
                continue
            is_friend = target.IsCombatAvatar and not game_logic.CanDamage(target, caster)
            if target.is_alive:
                self._DamageUIEffect(result, caster, target, damage_data, is_system_damage, is_friend)
                self._DamageSoundEffect(result, caster, target, damage_data, is_system_damage, is_friend)
                self._DamageModelEffect(result, caster, target, damage_data, is_system_damage, is_friend)
                self._DamageGameLogicEffect(result, caster, target, damage_data, is_system_damage, is_friend)

    def _DamageUIEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        if not target or not target.is_fps_avatar:
            return
        if 445 not in self.camera_effect_ids:
            self.AddCameraEffect(445, keep_time=1)
        hit_dir = result.damage_result[target.id].get('hit_dir')
        hit_dir and genv.messenger.Broadcast(events.ON_INJURED_GUIDE, self.id, hit_dir)

    def _DamageSoundEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        if not target or not target.is_fps_avatar:
            return
        if (sound_results := result.sound_results) and result.damage_result:
            for sr in sound_results:
                if event_id := sr['event_id']:
                    genv.sound_mgr.PlayEventById(event_id, is_3d=False)

    def _DamageModelEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        if is_friend:
            return
        if not target:
            return
        if not target.model or not target.model.isValid():
            return
        if target.is_fps_avatar:
            target.hand_model and target.hand_model.OnHit(
                info={'hit_part': damage_data.get('hit_part', consts.AvatarCollisionBone_UpperBottom),
                      'hit_dir': damage_data.get('hit_dir')})
        else:
            target.model.OnHit(info={'hit_part': damage_data.get('hit_part', consts.AvatarCollisionBone_UpperBottom),
                                     'hit_dir': damage_data.get('hit_dir', (0, 0, 0)), 'caster_id': caster.id})

    def _DamageGameLogicEffect(self, result, caster, target, damage_data, is_system_damage, is_friend):
        pass
