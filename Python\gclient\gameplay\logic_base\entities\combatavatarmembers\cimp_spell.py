# -*- coding: utf-8 -*-
import math
import time
from collections import defaultdict


import functools

from gclient.gameplay.logic_base.entities.cure_uav import CureUav
from gclient.util.bullet.bullet_separate_effect_manager import BulletSeparateEffectMgr
from gclient.util.debug_log_util import SomePreset, print_s  # noqa
import switches
import MRender
import random
from functools import partial

from common.RpcMethodArgs import EntityID, Bool, List, Int, Dict
from common.rpcdecorator import rpc_method, CLIENT_STUB

from common.classutils import Property
from gclient.gamesystem.uisettingnew.setting_graphics_audio_window import ConvertVFovToHFov
from gshare.decorators import LimitCall
from gshare import time_util
from gclient import cconst
from gclient.cconst import FireMode, FireModeSelector
from gclient.data import effect_data, material_type_data, game_const_data, weapon_data
from gclient.framework.ui import ui_define
from gclient.framework.util.performance_util import EPerformanceLevel
from gclient.gameplay.logic_base.spell import spell_util
from gclient.gameplay.logic_base.spell.cspell_mgr import CSpellMgr
from gclient.gameplay.logic_base.spell.spell_core import spell_core_main
from gclient.gameplay.logic_moba.ui.moba_hud_shield_remain_comp import MobaHudShieldRemainComp
from gclient.gameplay.uicomponents.hud_frontsight_comp import HudFrontsightComp
from gclient.util.decorators import ReplaySpectatorDecorator
from gshare import effect_util, weapon_util, formula, consts
from gshare.async_util import Async, TAG_SPACE
from gshare.consts import GunType, WeaponPartFeature
from gshare.icombat_attr import AttrType
from gshare.ispell_mgr import SpellResult
from gclient.config import LocalConfig
from gclient.framework.util import events, MHelper
from gclient.gameplay.uicomponents.hud_loadingbar_comp import SegLoadingBarComp
from gclient.gameplay.uicomponents.prompt_manager import HudPromptComp
from gclient.util.bullet.bullet_manager import BulletMgr
from common.IdManager import IdManager
import MType
import MEngine
import MCharacter
from gclient.data import spell_data
from gclient.framework.util.render_options.fixed_render_options import fixed_ads_render_options, fixed_render_options
from gshare.utils import Functor


class CombatAvatarMember(object):
    """
    :type-self: gclient.gameplay.logic_base.entities.combat_avatar.CombatAvatar
    """
    Property('spell_mgr', CSpellMgr)
    Property("is_ads_status", 0)
    Property("is_shooting_status", 0)
    Property("is_drone_curing", False)
    Property("drone_curing_type", 0)
    Property("cure_drone_id", '')
    Property("drone_end_time", 0.0)
    Property("ban_skill", False)
    Property("info_chaos", False)
    Property('skill_relate_item', '')
    # 前面那个技能衍生物的id--用来最多可以放2个衍生物的技能
    Property('pre_skill_relate_item', '')
    Property("parkour", 0)
    Property("parkour_energy", 100.0)

    def __init_component__(self, _):
        # 心跳扫描
        self.heartbeat_start_timer = None
        self.heartbeat_end_timer = None
        self.heartbeat_reset_timer = None
        self.heartbeat_delay_start_timer = None
        self.heartbeat_endtime = None
        self.heartbeat_num = 0
        self.heartbeat_avatar_cache_info = {}
        self.heartbeat_spell_state = cconst.HeartbeatSpellState.Empty

        self.prone_for_forbidden_ads = False
        self.cure_uav_effect = None

        # 定向爆炸
        self.place_explode_entity = None
        # 旋涡
        self.place_paranoia_guids = []
        # 火墙
        self.firewall_mode = 0  # 火墙模式 0:垂直 1:水平
        self.place_firewall_effects = []
        # 砖墙手雷
        self.wallbomb_mode = 0  # 钻墙手雷模式 0 钻墙 1 不钻
        # 导弹打击
        self.missile_indicate_effect_id = None
        self.missile_indicate_pos = None
        self.missile_indicate_tick = None
        # 极寒领域
        self.cold_fields = {}
        # 掩体放置物相关
        self.place_entity_guid = None
        self.place_entity_rotate = 0
        # 特效放置物相关
        self.place_effect_id = None
        self.place_effect_spell_id = None
        # 镜头特效
        self.camera_effect_ids = {}
        self.camera_effect_timers = {}

        self.camera_effect_models = {}
        self.camera_effect_models_timers = {}

        self._is_ads = False
        self.light_attack_mode = False
        self.ballistic_effect_history = defaultdict(float)

        trans = MType.Matrix4x3()
        trans.translation = MType.Vector3(0, -2000, 0)
        trans.set_pitch_yaw_roll(0, 0, 0)
        self.invalid_trans = trans

        # 电磁黑人抬手特效
        self.electical_proto = None
        self.hold_effect_id = None
        self.cannon_entity = None
        # 超级冲刺
        self.client_parkour = 0
        # 蜂女
        self.cure_uav_camera_effect = None
        # nova
        self.is_using_nova_second_skill = False
        # 武士
        self.samurai_sprint_start_time = 0
        self.samurai_sprint_tick = 0
        self.samurai_sprint_effect_id = 0
        self.samurai_sprint_pos = None
        # result_cache 用于暂存一个弹道的 只存一个
        self.result_cache = None

    def __fini_component__(self):
        self.RemoveAllCameraEffect()
        self.ClearParanoiaEntity()

    @property
    def is_ads(self):
        return self._is_ads

    @is_ads.setter
    def is_ads(self, value):
        self._is_ads = value
        if self.is_replay_avatar:
            genv.messenger.Broadcast(events.ON_REPLAY_ADS_STATE_CHANGE, value)

    @property
    def is_real_ads(self):
        return self.is_ads_status

    @is_real_ads.setter
    def is_real_ads(self, value):
        return

    @property
    def is_shooting(self):
        return self.is_shooting_status

    @property
    def is_hold_breath(self):
        if not self.hand_model:
            return False
        if not self.hand_model.hold_breath_fsm:
            return False
        return self.hand_model.hold_breath_fsm.IsHoldBreath()

    def _on_set_is_ads_status(self, old):
        # 开镜闪光
        if self.is_replay_avatar:
            return
        cur_weapon_case = self.GetCurWeaponCase(False)
        if not cur_weapon_case or not cur_weapon_case.is_gun or not cur_weapon_case.weapon_model:
            return
        if not self.is_ads_status:
            if cconst.ADS_FLASH_PARAM_DEBUG:
                return
            optic_part = cur_weapon_case.GetWeaponPartModel(consts.WeaponPartType_Optic)
            if optic_part:
                optic_part.StopAdsFlash()
            self.RemoveStoryTick(self.AdsFlashTick)
            return
        if self is genv.player:
            return
        if self.space.game_logic.IsFriend(self, genv.player) and (not cconst.ADS_FLASH_PARAM_DEBUG):
            return
        is_need_flash = weapon_util.IsWeaponOpticPartNeedAdsFlash(self.GetCurWeaponPartSlots())
        if not is_need_flash:
            return
        # frames = 5 if gpl.performance_level <= 1 else 10
        self.AddStoryTick(self.AdsFlashTick, 10)

    def _on_set_is_drone_curing(self, old):
        self.RefreshCureUavEffect(self.is_drone_curing)

    def _on_set_drone_curing_type(self, old):
        self.RefreshCureUavEffectType()

    def _on_set_cure_drone_id(self, old):
        if self != genv.player and not self.is_replay_avatar:
            self.RefreshEmmaFriendTips()

    def _on_set_parkour(self, old):
        if self != genv.player and not self.is_replay_avatar:
            return
        self.client_parkour = max(self.parkour, 0)
        if self.parkour:
            self.AddCameraEffectModel(633)
            self.AddCameraEffect(938)
            self.hand_model.PlayEffectById(950)
            genv.sound_mgr.PlayEventById(768, is_3d=False)
        else:
            self.RemoveCameraEffect(938)
            self.RemoveCameraEffectModel(633)
            self.hand_model.ClearAllSkeletonEffect()
            genv.sound_mgr.PlayEventById(1417, is_3d=True)
        genv.messenger.Broadcast(events.ON_CHANGE_PARKOUR)

    def AdsFlashTick(self, dtime):
        if not genv.camera:
            return
        cur_weapon_case = self.GetCurWeaponCase(False)
        if not cur_weapon_case or not cur_weapon_case.is_gun or not cur_weapon_case.weapon_model:
            return
        player = genv.player
        if not player or player.is_destroyed():
            return
        player_pos = player.position
        self_pos = self.position
        delta_pos = formula.Substract3D(player_pos, self_pos)
        yaw = self.upper_yaw
        if self.is_lean_out:
            # 车上探身的时候角色yaw不会变
            vec = self.model.GetBoneWorldTransform('biped Head').z_axis
            yaw = MType.Vector3(*formula.Rotate2D((vec.x, vec.z), 0.3)).yaw
        self_dir = formula.YawToVector(yaw)
        player_dir = genv.camera.placer.placer.Direction
        camera_angle = math.degrees(formula.Angle3D(player_dir, formula.Mul3D(delta_pos, -1.0)))
        aim_angle = math.degrees(formula.Angle2D((delta_pos[0], delta_pos[-1]), (self_dir[0], self_dir[-1])))
        distance = formula.Distance3D(player_pos, self_pos)
        cur_fov = MEngine.GetGameplay().Player.Camera.FieldOfView
        is_visible = True
        # 这是用射线检测做了个性能优化？
        raycast_res = self.space.RawRaycast(MType.Vector3(*player_pos) + MType.Vector3(0, 1.5, 0), distance,
                                            cconst.PHYSICS_SHOOT_TEST, with_trigger=False, to_pos=MType.Vector3(*cur_weapon_case.weapon_model.position))
        if raycast_res and raycast_res.IsHit and raycast_res.Body:
            raycast_target = getattr(raycast_res.Body.Parent, 'owner', None)
            if not raycast_target or not raycast_target.IsCombatAvatar:
                is_visible = False
        optic_part = cur_weapon_case.GetWeaponPartModel(consts.WeaponPartType_Optic)
        if optic_part:
            optic_part.UpdateAdsFlash(distance, aim_angle, camera_angle, cur_fov, is_visible)

    def _on_set_bomb_mode(self, new):
        genv.messenger.Broadcast(events.ON_CHANGE_BOMB_MODE, self.bomb_mode)

    @rpc_method(CLIENT_STUB, Dict())
    def OnSpellResultFull(self, cur_result):
        if not isinstance(cur_result, SpellResult):
            result = SpellResult()
            result.Unpack(cur_result)
        else:
            result = cur_result
            if result.caster:
                result.caster_pos = result.caster.position
        self.OnSpellResultImpl(result)

    @rpc_method(CLIENT_STUB, List())
    def OnSpellResult(self, cur_result):
        if not isinstance(cur_result, SpellResult):
            result = SpellResult()
            result.Unpack(cur_result)
        else:
            result = cur_result
            if result.caster:
                result.caster_pos = result.caster.position

        self.OnSpellResultImpl(result)

    def OnSpellResultDrawLine(self, result):
        if genv.replay_player and result.caster and result.caster.id == genv.replay_player.id:
            # print (result.caster, result.shoot_start_pos, result.shoot_end_pos)
            src = result.verify_start_pos
            dst = result.shoot_end_pos
            if not src:
                src = result.caster_pos
            if not dst:
                dst = result.hit_effect
                if dst:
                    dst = dst[0]['hit_pos']
            if src and dst:
                genv.space.DrawRay(src, dst, reset=False)

    @events.ListenTo(events.ON_AFTER_APPLY_CAMERA_PROPERTIES)
    def OnAfterApplyCameraProp(self):
        if self.result_cache:
            result = self.result_cache
            self.ProcessBulletEffectByResult(result.caster, result)
            self.result_cache = None

    def OnSpellResultImpl(self, result):
        # [DEBUG]
        if self.is_replay_avatar and genv.is_jarvis_started:
            gjarvis.OnSpellResult(result)
        # [DEBUG]
        caster = result.caster
        show_effect = False if caster and caster.model.HaveHiddenReason(cconst.HIDDEN_REASON_LIGHT_ATTACK) else True
        hit_effect = result.hit_effect
        # [DEBUG]
        # self.OnSpellResultDrawLine(result)
        # [DEBUG]
        _damage_result = result.damage_result
        player = genv.player

        if genv.show_hit_effect and hit_effect and show_effect:
            show_hit_effect = True
            if gpl.performance_level <= EPerformanceLevel.LEVEL_0:
                show_hit_effect = False
            elif gpl.performance_level <= EPerformanceLevel.LEVEL_1:
                # level1的机器只显示自己的弹孔吧
                if _damage_result:
                    show_hit_effect = False
                elif caster is not player:
                    show_hit_effect = False
            if show_hit_effect:
                for he in hit_effect:
                    effect_util.PlayHitEffect(result.caster, he)

        if not caster:
            return
        caster_id = caster.id
        if result.ballistic_effect and show_effect:
            # todo test
            if caster.IsPlayerCombatAvatar:
                # self.ProcessBulletEffectByResult(result.caster, result)
                # @sjh 很奇怪... 客户端先行的和rpc过来的会处理两次，加个过滤
                _extra = result.extra
                now = time.time()
                prob = _extra.get('BallisticEffectIntervalProb', 0.0)
                for ballistic_effect in result.ballistic_effect:
                    if now > self.ballistic_effect_history[caster_id] or random.random() < prob:
                        self.result_cache = result
                        self.ballistic_effect_history[caster_id] = now + _extra.get('BallisticEffectInterval', 0.098)
                        if extra_material_type := _extra.get('material_type'):
                            frm, to = MType.Vector3(*ballistic_effect['create_pos']), MType.Vector3(*ballistic_effect['hit_pos'])
                            self.PlayShootResultJumpBulletSound(caster, to, frm, MType.Vector3(*formula.Substract3D(to, frm)), material_type=extra_material_type)
            else:
                now = time.time()
                # 每个人打出的枪线有0.5秒内置CD
                if now > self.ballistic_effect_history[caster_id]:
                    self.ballistic_effect_history[caster_id] = now + cconst.TRAIL_BALLISTIC_EFFECT_PARAM["BallisticEffectInterval"]
                    self.ProcessBulletEffectByResult(result.caster, result)
                    _extra = result.extra
                    for ballistic_effect in result.ballistic_effect:  # 处理3p弹道击飞小物件
                        frm, to = MType.Vector3(*ballistic_effect['create_pos']), MType.Vector3(*ballistic_effect['hit_pos'])
                        if hits := self.space.AllRaycastMask(frm, to, (1 << cconst.PHYSICS_AIR_DROP_BOX) | (1 << cconst.PHYSICS_DYNAMIC_BREAK)):
                            for r in hits:
                                body_owner = getattr(r.Body, 'owner', None)
                                if body_owner:
                                    if body_owner.IsFragment:
                                        body_owner.OnHit(r.Body.Entity)
                                    elif body_owner.IsStrikeItem:
                                        spell_core_main.OnHitStrikeItem((to - frm).get_normalized(), r.Pos, body_owner, _extra.get('gun_id', ''))
                        self.CaculateShootResultThreatenSound(caster, to, frm)
                        if extra_material_type := _extra.get('material_type'):
                            self.PlayShootResultJumpBulletSound(caster, to, frm, MType.Vector3(*formula.Substract3D(to, frm)), material_type=extra_material_type)

        if sound_results := result.sound_results:
            for sound_result in sound_results:
                self.PlayHitSoundEventOnMaterial(caster_id, sound_result)

        _damage_result and self.OnDealDamageResult(result)

        if player and player.IsHelenAI and player.enable_client_ai:
            # 客户端开了AI, 发出声音威胁
            caster.Helen_OnMakeDamage(result)

    def CaculateShootResultThreatenSound(self, caster, end_pos, start_pos):
        space = caster.space
        game_logic = space.game_logic
        if not game_logic or not caster.model or not caster.model.isValid():
            return
        all_hits = space.AllRaycastMask(start_pos, end_pos, 1 << cconst.PHYSICS_SELF_TRIGGER)
        if not all_hits:
            return
        for r in sorted(all_hits, key=lambda rs: (rs.Pos - start_pos).length):
            body_owner = getattr(r.Body, 'owner', None)
            distance = (r.Pos - start_pos).length
            whizby_distance = "far"
            if distance > 20:
                pass
            elif 10 <= distance < 20:
                whizby_distance = "medium"
            elif distance < 10:
                whizby_distance = "near"
            if body_owner and body_owner.IsCombatAvatar and body_owner.id == genv.player.id and game_logic.IsEnemy(caster, body_owner):
                genv.player.PlayGeneralSoundEventById(833, pos=r.Pos.tuple(), is_3d=True, noGameObject=True, switchGroup={'whizby_distance': whizby_distance})

    def PlayShootResultJumpBulletSound(self, caster, to, frm, dir, material_type):
        if material_type:
            position = caster.position
            direction = formula.Substract3D(position, to)
            dist = formula.Length3D(direction)
            if dist < game_const_data.JUMP_BULLET_RANGE:
                judge = random.randint(1, 10)
                if judge > 5:
                    genv.player.PlayGeneralSoundEventById(834, pos=to.tuple(), is_3d=True, noGameObject=True, switchGroup={"material_surface": material_type_data.data.get(material_type, {}).get('hit_sound_material_surface', 'cement')})

    def ProcessBulletEffectByResult(self, caster, result):
        if not result.extra:
            return
        if not result.ballistic_effect:
            return
        caster = result.caster
        is_fpp = caster.IsPlayerCombatAvatar
        for ballistic_effect in result.ballistic_effect:
            input_data = effect_util.ConfigBallisticInputData(caster.is_ads, result, ballistic_effect)
            BulletMgr.instance().SpawnBullet(result.caster, None, result.spell_id, input_data, weapon_guid=result.weapon_guid, result=result, is_fpp=is_fpp)
            BulletSeparateEffectMgr.instance().SpawnBulletSeparateEffect(result.caster, None, input_data, weapon_guid=result.weapon_guid, result=result)
            if caster.is_fps_avatar and (caster.is_fps_mode or caster.is_ads):
                pass
            else:
                effect_util.ProcessThreatenSound(result, ballistic_effect)
        # else:
        #     input_data = effect_util.ConfigBallisticInputData(caster.is_ads, result)
        #     BulletMgr.instance().SpawnBullet(result.caster, None, result.spell_id, input_data, weapon_guid=result.weapon_guid, result=result, is_fpp=is_fpp)
        #     BulletSeparateEffectMgr.instance().SpawnBulletSeparateEffect(result.caster, None, input_data, weapon_guid=result.weapon_guid, result=result)
        #     if caster.is_fps_avatar and (caster.is_fps_mode or caster.is_ads):
        #         pass
        #     else:
        #         effect_util.ProcessThreatenSound(result)

    # [DEBUG]
    def TestSpawnBullet(self):
        if getattr(genv, '_last_bullet_guid', None):
            BulletMgr.instance().DestroyBulletByGuid(genv._last_bullet_guid)
        caster = self
        input_data = {
            'ballistic_velocity': 0,
            'ballistic_position': (0, 0, 0),
            'ballistic_scale': (1, 1, 1),
        }
        create_pos = spell_core_main.GetShootStartPos(self)
        end_pos = (create_pos[0] + 200, create_pos[1], create_pos[2])
        result = SpellResult()
        result.extra = {
            'create_pos': create_pos,
            'end_pos': end_pos,
            'shoot_dir': (0, 0, 0),
            'material_type': 1,
        }
        import MCharacter
        MCharacter.PurgeAll()
        spell_id = 1
        bullet_guid = BulletMgr.instance().SpawnBullet(caster, None, spell_id, input_data, weapon_guid=self.cur_weapon_guid, result=result)
        print('==== bullet guid: ', bullet_guid)
        genv._last_bullet_guid = bullet_guid
    # [DEBUG]

    @rpc_method(CLIENT_STUB, List())
    def BatchOnSpellResult(self, results):
        for r in results:
            self.OnSpellResult(r)

    @rpc_method(CLIENT_STUB, List())
    def BatchOnSpellResultFull(self, results):
        for r in results:
            self.OnSpellResultFull(r)

    @rpc_method(CLIENT_STUB, List())
    def ForwardSpellResult(self, cur_result):
        if not isinstance(cur_result, SpellResult):
            result = SpellResult()
            result.Unpack(cur_result)
        else:
            result = cur_result
            if result.caster:
                result.caster_pos = result.caster.position

        caster = result.caster
        caster and caster.OnSpellResultImpl(result)

    @rpc_method(CLIENT_STUB, List())
    def BatchForwardSpellResult(self, results):
        for r in results:
            self.ForwardSpellResult(r)

    @rpc_method(CLIENT_STUB, Dict())
    def ForwardSpellResultFull(self, cur_result):
        # print("ForwardSpellResultFull", self, cur_result)
        if not isinstance(cur_result, SpellResult):
            result = SpellResult()
            result.Unpack(cur_result)
        else:
            result = cur_result
            if result.caster:
                result.caster_pos = result.caster.position

        caster = result.caster
        caster and caster.OnSpellResultImpl(result)

    @rpc_method(CLIENT_STUB, List())
    def BatchForwardSpellResultFull(self, results):
        for r in results:
            self.ForwardSpellResultFull(r)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def SetAdsLoadPoint(self, pos_list):
        point_list = []
        dist = []
        if pos_list:
            fov = MEngine.GetGameplay().Player.Camera.FieldOfView
            detect_distance = 100 * math.tan(fov / 2.0 * math.pi / 180)
            for pos in pos_list:
                if isinstance(pos, tuple) or isinstance(pos, list):
                    pos = MType.Vector3(*pos)
                point_list.append(pos)
                dist.append(detect_distance)
        self.space.SetObservationPoints(point_list)
        point_list and self.space.SetPreloadPivotsAndDistance('ads', point_list, dist)

    def RefreshCureUavEffect(self, is_add):
        if is_add:
            if not (self == genv.main_player or self.is_replay_avatar):
                self.cure_uav_effect = self.model.PlaySkeletonEffect(effect_data.data[638]['effect_string'], -1, True)
            if self == genv.main_player or self.is_replay_avatar:
                if gpl.performance_level <= EPerformanceLevel.LEVEL_1:
                    self.AddCameraEffect(187)
                else:
                    if self.drone_curing_type:
                        self.AddCameraEffect(636)
                    else:
                        self.AddCameraEffect(635)
            cure_uav = self.space.GetEntity(self.cure_drone_id)
            if cure_uav:
                if self == genv.main_player or self.is_replay_avatar:
                    if self.drone_curing_type:
                        cure_uav.PlaySuperCureSound()
                    else:
                        cure_uav.PlayCureSound()
                else:
                    cure_uav.PlayCureSound3D()
                cure_uav.CreateConnectByTarget()
                cure_uav.PlayUAVEffect()
        else:
            if self.cure_uav_effect:
                self.model.ClearSkeletonEffect(self.cure_uav_effect)
                self.cure_uav_effect = None
            cure_uav = self.space.GetEntity(self.cure_drone_id)
            if cure_uav:
                cure_uav.ClearAllEffect()
                cure_uav.ClearCureSound()
            if self == genv.main_player or self.is_replay_avatar:
                if gpl.performance_level <= EPerformanceLevel.LEVEL_1:
                    self.RemoveCameraEffect(187)
                else:
                    self.RemoveCameraEffect(636)
                    self.RemoveCameraEffect(635)

    def GetCureUavDuration(self):
        duration = self.drone_end_time - genv.GetServerNow()
        return max(duration, 0)

    def RefreshCureUavEffectType(self):
        cure_uav = self.space.GetEntity(self.cure_drone_id)
        if self.is_drone_curing and (self == genv.main_player or self.is_replay_avatar):
            if gpl.performance_level > EPerformanceLevel.LEVEL_1:
                if self.drone_curing_type:
                    self.AddCameraEffect(636)
                    self.RemoveCameraEffect(635)
                else:
                    self.AddCameraEffect(635)
                    self.RemoveCameraEffect(636)
            if self.drone_curing_type:
                cure_uav and cure_uav.PlaySuperCureSound()
            else:
                cure_uav and cure_uav.PlayCureSound()
        if self.drone_curing_type:
            if cure_uav and cure_uav.caster_id != genv.player.id and self == genv.player:
                genv.sound_mgr.PlayEventById(722)

    def JumpToSwitchToss(self):
        self.hand_model and self.hand_model.JumpToSwitchToss()
        self.model.FireEvent('@switch_toss', self.model.upper_graph_id)

    def JumpToSkillToss(self):
        self.hand_model and self.hand_model.JumpToSkillToss()
        # 第三人称
        self.model.FireEvent('@skill_toss', self.model.upper_graph_id)

    def JumpToSkillHold(self):
        self.hand_model and self.hand_model.JumpToSkillHold()
        # 第三人称
        self.model.FireEvent('@skill_hold', self.model.upper_graph_id)

    def JumpToSkillTossSecondary(self):
        self.hand_model and self.hand_model.JumpToSkillTossSecondary()
        self.model.FireEvent('@skill_toss2', self.model.upper_graph_id)

    # region #########技能 旋涡#########################################
    def OnHoldItem214(self, item):
        # 创建放置物
        self.CreateParanoiaEntity(item.spell_id)
        genv.messenger.Broadcast(events.ON_SKILL_RANGE_SHOW, item.spell_id, True)

    def CreateParanoiaEntity(self, spell_id):
        print('==========CreateParanoiaEntity')
        self.ClearParanoiaEntity()
        spell_level = 1  # TODO
        spell_proto = spell_util.GetSpellProto(spell_id, spell_level)
        paranoia_proto = spell_proto['paranoia']
        info_list = self.GetParanoiaPlacePosition(paranoia_proto)
        self._CreateParanoiaEntity(info_list, spell_id, spell_level)

    def OnSpellItem214(self, item):
        print('=============OnSpellItem214')
        self.DirectionExplode()
        genv.messenger.Broadcast(events.ON_SKILL_RANGE_SHOW, item.spell_id, False)

    def UseItemAutoEnd214(self, item):
        genv.messenger.Broadcast(events.ON_SKILL_RANGE_SHOW, item.spell_id, False)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def ClearParanoiaEntity(self):
        self.RemoveStoryTick(self.PlaceParanoiaEntityTick)
        get_entity = self.space.GetEntity
        for entity_guid in self.place_paranoia_guids:
            entity = get_entity(entity_guid)
            entity and entity.destroy()
        self.place_paranoia_guids = []

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def _CreateParanoiaEntity(self, info_list, spell_id, spell_level):
        for info in info_list:
            pos = info[:3]
            scale = info[-1]
            guid = IdManager.genid()
            genv.space.create_entity('PlaceEntity', guid, bdict={
                'position': pos,
                'spell_id': spell_id,
                'level': spell_level,
                'scale': scale
            })
            self.place_paranoia_guids.append(guid)
        self.AddStoryTick(self.PlaceParanoiaEntityTick)

    def PlaceParanoiaEntityTick(self, dtime):
        if not self.place_paranoia_guids:
            return
        _entity = self.space.GetEntity(self.place_paranoia_guids[0])
        if not _entity:
            return
        paranoia_proto = _entity.paranoia_proto
        info = self.GetParanoiaPlacePosition(paranoia_proto)
        get_entity = self.space.GetEntity
        for _info, entity_guid in zip(info, self.place_paranoia_guids):
            entity = get_entity(entity_guid)
            if not entity:
                continue
            trans = entity.model.model.Transform
            pos = _info[:3]
            scale = _info[-1]
            trans.translation = MType.Vector3(*pos)
            trans.scale = MType.Vector3(scale, scale, scale)
            entity.model.model.Transform = trans

    def GetParanoiaPlacePosition(self, proto):
        # 返回线路上球的位置，从近到远
        camera = MEngine.GetGameplay().Player.Camera
        camera_pos = camera.Transform.translation
        device_screen_center = gui.device_screen_center
        cur_dir = camera.GetRayDirectionFromScreenPoint(int(device_screen_center[0]), int(device_screen_center[1]))
        max_distance = proto.get('distance', 20)
        max_distance = max_distance + self.combat_attr.CalResult(max_distance,
                                                                 AttrType.to_string(AttrType.ParanoiaDistance),
                                                                 equip_id=214)
        interval = proto.get('interval', 5)
        base_radius = proto.get('radius', 2)
        radius = base_radius + self.combat_attr.CalResult(base_radius, AttrType.to_string(AttrType.ParanoiaRadius), equip_id=214)
        min_radius = radius / 10.0
        end_pos = camera_pos + cur_dir * max_distance
        n = int(max_distance / interval)
        ret = []
        for idx in range(1, n + 1):
            if interval * idx + radius >= max_distance:
                break
            pos = camera_pos + interval * idx * cur_dir
            _radius = formula.LinearMapNumber(interval * idx, (0, max_distance), (min_radius, radius))
            ret.append((pos.x, pos.y, pos.z, _radius / base_radius))
        ret.append((end_pos.x, end_pos.y, end_pos.z, 1.0))
        return ret
    # endregion #########技能 旋涡#########################################

    # region #########定向爆炸######因为观战的时候也要显示指示器，要写到这里######
    def OnHoldItem52(self, item):
        # 创建放置物
        print('========OnHoldItem52===============')
        self.ClearPlaceExplodeEntity()
        spell_id = item.spell_id
        spell_level = 1  # TODO
        spell_proto = spell_util.GetSpellProto(spell_id, spell_level)
        cast_proto = spell_proto['cast']
        pos = self.GetPlacePosition(cast_proto)
        self._CreateExplodeEntity((pos.x, pos.y, pos.z), spell_id, spell_level)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def _CreateExplodeEntity(self, pos, spell_id, spell_level):
        self.place_explode_entity = genv.space.create_entity('PlaceEntity', None, bdict={
            'owner_id': self.id,
            'position': pos,
            'spell_id': spell_id,
            'level': spell_level,
        })
        self.AddStoryTick(self.PlaceExplodeEntityTick)

    def OnSpellItem52(self, item):
        print('=============OnSpellItem52')
        self.DirectionExplode()

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def ClearPlaceExplodeEntity(self):
        self.RemoveStoryTick(self.PlaceExplodeEntityTick)
        if self.place_explode_entity:
            self.place_explode_entity.destroy()
            self.place_explode_entity = None

    def PlaceExplodeEntityTick(self, dtime):
        if not self.place_explode_entity:
            return
        cast_proto = self.place_explode_entity.cast_proto
        pos = self.GetPlacePosition(cast_proto)
        trans = self.place_explode_entity.model.model.Transform
        trans.translation = pos
        self.place_explode_entity.model.model.Transform = trans

    def GetPlacePosition(self, cast_proto):
        # 是否穿墙
        piercing_wall = cast_proto['piercing_wall']
        # 距离
        length = cast_proto['position'][-1]
        # 最大可穿墙的墙体厚度
        thickness = cast_proto.get('thickness', 5)
        camera = MEngine.GetGameplay().Player.Camera
        translation = camera.Transform.translation
        device_screen_center = gui.device_screen_center
        cur_dir = camera.GetRayDirectionFromScreenPoint(int(device_screen_center[0]), int(device_screen_center[1]))
        space = genv.space
        r = space.RawRaycast(camera.GetOrigin(), length, cconst.PHYSICS_BULLET, False, to_dir=cur_dir)
        des = translation + cur_dir * length
        if not piercing_wall:
            if r and r.IsHit:
                return r.Pos
            else:
                return des
        else:
            # 查询落点附近有没有东西阻挡
            results = space.AllOverlapByCircle(des, 1.0, cconst.PHYSICS_BULLET, is_vec=True)
            if not results:
                # 还要判断是不是在物体里面，在空心物体里面也要卡物体边沿
                # 反向打一次射线，如果打不到东西，则认为是在物体里面，有没有问题呢？ TODO
                # 还需要检测物体的厚度，超过最大厚度不能穿
                if r and r.IsHit:
                    # 正向射线打中东西了
                    r_reverse = space.RawRaycast(des, length, cconst.PHYSICS_BULLET, False, to_pos=r.Pos - cur_dir * 0.2)
                    if not (r_reverse and r_reverse.IsHit):
                        # 反向射线没打中，认为在物体里面了。
                        # [DEBUG]
                        if switches.IS_DEBUG:
                            print('正向射线打中了，反向射线没打中，认为落点在物体里面')
                        # [DEBUG]
                        return r.Pos
                    else:
                        # 反向射线打中了物体
                        r_parent = getattr(r.Body, 'Parent', None)
                        r_reverse_parent = getattr(r_reverse.Body, 'Parent', None)
                        if r_parent and r_reverse_parent and r_parent.GetName() == r_reverse_parent.GetName():
                            # 反向射线打中了同一个物体
                            # [DEBUG]
                            if switches.IS_DEBUG:
                                print('正向射线和反向射线打中了同一个物体，墙体是否太厚？', (r.Pos - r_reverse.Pos).length > thickness)
                            # [DEBUG]
                            if (r.Pos - r_reverse.Pos).length > thickness:
                                return r.Pos
                        else:
                            # 反向射线打中的不是同一个物体，如果两个Pos的距离“足够近”，那么认为是“同一层”东西，因为我们地图经常会有两个很靠近的东西，如地面和地基
                            # [DEBUG]
                            if switches.IS_DEBUG:
                                print('正向射线和反向射线打中的不是同一个物体，他们的距离 =', (r.Pos - r_reverse.Pos).length)
                            # [DEBUG]
                            if (r.Pos - r_reverse.Pos).length < 0.2:
                                return r.Pos
                return des
            else:
                # [DEBUG]
                if switches.IS_DEBUG:
                    print('落点AllOverLap检测到碰撞，碰撞数目 =', len(results), [result.Body.Entity.GetName() for result in results])
                # [DEBUG]
                if results and r and r.IsHit:
                    return r.Pos
                return des

    def DirectionExplode(self):
        self.JumpToSkillToss()

    @events.ListenTo(events.ON_BEFORE_ENTER_AIRCRAFT)
    def ClearDirectionExplode(self):
        self.ClearPlaceExplodeEntity()
        # 播一下动作，把逻辑走完
        self.DirectionExplode()
    # endregion ##########定向爆炸##############

    # region ###########导弹打击############
    def OnHoldItem58(self, item):
        self.OnHoldItemMissile(item)

    def OnHoldItem96(self, item):
        self.OnHoldItemMissile(item)

    def OnHoldItem97(self, item):
        self.OnHoldItemMissile(item)

    def OnHoldItem98(self, item):
        self.OnHoldItemMissile(item)

    def OnHoldItem202(self, item):
        self.OnHoldItemMissile(item)

    def OnSpellItem58(self, item):
        self.OnSpellItemMissile(item)

    def OnSpellItem96(self, item):
        self.OnSpellItemMissile(item)

    def OnSpellItem97(self, item):
        self.OnSpellItemMissile(item)

    def OnSpellItem98(self, item):
        self.OnSpellItemMissile(item)

    def OnSpellItem202(self, item):
        self.OnSpellItemMissile(item)

    def OnHoldItemMissile(self, item):
        self.CancelMissileAttackIndicateTick()
        self._OnHoldItemMissile(item.spell_id)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def _OnHoldItemMissile(self, spell_id, spell_level=1):
        born_pos = self.position
        born_pos = (born_pos[0], born_pos[1] - 100, born_pos[-1])
        self.missile_indicate_effect_id = effect_util.WrapperPlayEffectInWorld(66, born_pos, insure_play=True)
        self.missile_indicate_tick = functools.partial(self.MissileAttackIndicateTick, spell_id, spell_level)
        self.missile_indicate_tick(None)
        self.AddStoryTick(self.missile_indicate_tick)

    def OnSpellItemMissile(self, item):
        self.CancelMissileAttackIndicateTick()
        self.JumpToSkillToss()

    def MissileAttackIndicateTick(self, spell_id, spell_level, dt):
        camera = MEngine.GetGameplay().Player.Camera
        trans = camera.Transform
        camera_pos_vec = trans.translation
        device_screen_center = gui.device_screen_center
        camera_dir = camera.GetRayDirectionFromScreenPoint(int(device_screen_center[0]), int(device_screen_center[1]))
        space = self.space
        spell_proto = spell_util.GetSpellProto(spell_id, spell_level)
        max_dist = spell_proto.get('missile', {}).get('max_dist', 20)
        r = space.RawRaycast(camera_pos_vec, max_dist, cconst.PHYSICS_SHOOT_TEST, with_trigger=False, to_dir=camera_dir)
        if r and r.IsHit:
            # 关闭超出距离提示
            genv.messenger.Broadcast(events.ON_MISSILE_SPELL_OUT_OF_RANGE, False)
            pos = r.Pos
            self.missile_indicate_pos = pos
            if self.missile_indicate_effect_id:
                effect_entity = MCharacter.GetEffectEntity(self.missile_indicate_effect_id)
                trans = effect_entity.Transform
                trans.translation = pos
                effect_entity.Transform = trans
        else:
            # 弹超出距离提示
            genv.messenger.Broadcast(events.ON_MISSILE_SPELL_OUT_OF_RANGE, True)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def CancelMissileAttackIndicateTick(self):
        self.missile_indicate_tick and self.RemoveStoryTick(self.missile_indicate_tick)
        self.missile_indicate_tick = None
        # 删除落点特效
        effect_util.ClearWorldEffectImmediately(self.missile_indicate_effect_id)
        self.missile_indicate_effect_id = None
    # endregion #########导弹打击###########

    # region #########光线打击
    def OnSpellItemLightAttack(self, item):
        self.JumpToSkillToss()

    # endregion ########光线打击

    # region #########心跳扫描##########################
    def DelayStartHeartbeatScan(self):
        # spell里面调用，开始计时，记状态
        if self.heartbeat_delay_start_timer:
            self.cancel_timer(self.heartbeat_delay_start_timer)
        self.heartbeat_delay_start_timer = self.add_timer(cconst.HEARTBEAT_SCAN_DELAY_TIME, self.StartHeartbeatScan)

    def StartHeartbeatScan(self):
        # print '=========StartHeartbeatScan============='
        self.RecordStartHeartbeatScan()
        self.heartbeat_spell_state = cconst.HeartbeatSpellState.Spelling
        genv.messenger.Broadcast(events.ON_HEARTBEAT_SCAN_START)
        if self.heartbeat_end_timer:
            self.cancel_timer(self.heartbeat_end_timer)
        self.heartbeat_end_timer = self.add_timer(cconst.HEARTBEAT_SCAN_ONCETIME, self.StopHeartbeatScan)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def RecordStartHeartbeatScan(self):
        self.heartbeat_endtime = time.time() + cconst.HEARTBEAT_SCAN_ONCETIME

    def StopHeartbeatScan(self):
        # print '=========StopHeartbeatScan============='
        self.heartbeat_spell_state = cconst.HeartbeatSpellState.Waiting
        genv.messenger.Broadcast(events.ON_HEARTBEAT_SCAN_END)
        if self.heartbeat_start_timer:
            self.cancel_timer(self.heartbeat_start_timer)
        self.heartbeat_start_timer = self.add_timer(cconst.HEARTBEAT_SCAN_WAITTIME, self.StartHeartbeatScan)

    def OnHeartbeatStop(self):
        # spell里面调用，放下心跳扫描仪的时候，计时重置spell_state的状态，需求是放下心跳扫描仪，过了cd时间，重新拿起来马上进行扫描
        if self.heartbeat_reset_timer:
            self.cancel_timer(self.heartbeat_reset_timer)
        self.heartbeat_reset_timer = self.add_timer(cconst.HEARTBEAT_SCAN_CDTIME, self.ResetHeartbeatScan)

    def ResetHeartbeatScan(self):
        if self.heartbeat_end_timer:
            self.cancel_timer(self.heartbeat_end_timer)
            self.heartbeat_end_timer = None
        if self.heartbeat_start_timer:
            self.cancel_timer(self.heartbeat_start_timer)
            self.heartbeat_start_timer = None
        if self.heartbeat_delay_start_timer:
            self.cancel_timer(self.heartbeat_delay_start_timer)
            self.heartbeat_delay_start_timer = None
        self.heartbeat_spell_state = cconst.HeartbeatSpellState.Empty

    def CancelHeartbeatResetTimer(self):
        if self.heartbeat_reset_timer:
            self.cancel_timer(self.heartbeat_reset_timer)
            self.heartbeat_reset_timer = None
    # endregion #########心跳扫描##########################

    # region ###################赛博忍术
    @rpc_method(CLIENT_STUB, EntityID())
    def HideNinjaBeacon(self, beacon_id):
        beacon = self.space.GetEntity(beacon_id)
        if beacon and not beacon.is_destroyed():
            beacon.model.AddHiddenReason(cconst.HIDDEN_REASON_SPELL)
    # endregion ###################赛博忍术

    # region #############放置物（掩体生成器之类）
    def OnHoldItem5(self, item):
        # 掩体生成器
        self.CreatePrePlaceEntity(item.spell_id, level=1)

    def OnHoldItem24(self, item):
        # 主动防御装置
        self.CreatePrePlaceEntity(item.spell_id, level=1)

    def OnHoldItem208(self, item):
        # 基础炮台
        self.CreatePrePlaceEntity(item.spell_id, level=1)

    def OnHoldItem200(self, item):
        # 粒子护盾
        self.CreatePrePlaceEffect(item.spell_id, level=1)

    def OnHoldItem209(self, item):
        # 加特林
        self.CreatePrePlaceEntity(item.spell_id, level=1)

    def OnHoldItem225(self, item):
        # 冰女冰墙
        self.CreatePrePlaceEntity(item.spell_id, level=1)

    def OnHoldItem317(self, item):
        fsm = self.GetItemFsm(item.guid)
        if fsm.key_cache and cconst.UseItemMode.ITEM_UP in fsm.key_cache:
            # 缓存了弹起指令，清一下cache
            fsm.key_cache = []

        spell_proto = spell_util.GetSpellProto(item.spell_id)
        shock_proto = spell_proto.get('shock', {})
        base_time = shock_proto.get('chargetime', [3,])[0]
        auto_release_time = shock_proto.get('auto_release_time', 8)
        name = spell_proto.get('name', '')
        SegLoadingBarComp.instance().ShowUI(base_time, name, auto_release_time, False)
        self.AddHoldingTimer(auto_release_time, item.guid, cconst.UseItemMode.ATTACK_DOWN)
        self.CreateElecticalFieldEffect(item.spell_id, level=1)
        genv.messenger.Broadcast(events.ON_SKILL_RANGE_SHOW, item.spell_id, True)

    def OnHoldItem1001(self, item):
        self.CreatePrePlaceEntity(item.spell_id, level=1)

    # HANK遥控战车
    def OnHoldItem467(self, item):
        self.CreatePrePlaceEntity(item.spell_id, level=1)

    # 毒气炸弹
    def OnHoldItem204(self, item):
        bomb = self.model.weapon_case.GetBindBomb()
        bomb.model.skeleton.FireEvent(-1, 'reset')
        bomb.model.skeleton.SetVariableF(-1, 'IsBindBomb', 1)
        print(f'========OnHoldItem204=========, class : {bomb.__class__}')

    # 毒气炸弹
    def OnSpellItem204(self, item):
        return
        bomb = self.model.weapon_case.GetBindBomb()
        bomb.model.skeleton.SetVariableF(-1, 'IsBindBomb', 0)
        print(f'========OnSpellItem204=========, class : {bomb.__class__}')

    def OnSpellItem5(self, item):
        # 掩体生成器
        self.TryAttack(item.spell_id, item_guid=item.guid)

    def OnSpellItem24(self, item):
        # 主动防御装置
        self.TryAttack(item.spell_id, item_guid=item.guid)

    def OnSpellItem208(self, item):
        # 基础炮台
        self.JumpToSkillToss()

    def OnSpellItem200(self, item):
        # 粒子护盾
        self.JumpToSkillToss()

    def OnSpellItem209(self, item):
        # 加特林
        self.JumpToSkillToss()

    def OnSpellItem225(self, item):
        # 冰女冰墙
        self.JumpToSkillToss()

    def OnSpellItem1001(self, item):
        # 猫女DJ台
        # cache下entity位置
        if throw_item := self.throw_item:
            throw_item.final_trans = self.place_entity.model.model.Transform
        self.ClearPrePlaceEntity()
        self.JumpToSkillToss()

    def OnSpellItem400(self, item):
        self.JumpToSkillToss()

    def OnSpellItem401(self, item):
        self.JumpToSkillToss()

    def OnSpellItem467(self, item):
        # 指挥战车
        self.JumpToSkillToss()

    def OnPreusingItem(self, item):
        self.JumpToSkillHold()

    def OnSpellItem317(self, item):
        self.ClearHoldingTimer()
        self.ClearElecticalFieldEffect()
        if SegLoadingBarComp.instance().visible:
            SegLoadingBarComp.instance().Close()
        self.JumpToSkillToss()
        genv.messenger.Broadcast(events.ON_SKILL_RANGE_SHOW, item.spell_id, False)

    def UseItemAutoEnd317(self, item):
        genv.messenger.Broadcast(events.ON_SKILL_RANGE_SHOW, item.spell_id, False)

    def CreatePrePlaceEntity(self, spell_id, level):
        # 创建放置物
        self.ClearPrePlaceEntity()
        self._CreatePrePlaceEntity(spell_id, level)

    def CreatePrePlaceEffect(self, spell_id, level):
        # 创建放置物特效
        print('[Skill] spell_id %s' % spell_id)
        self.ClearPrePlaceEffect()
        self._CreatePrePlaceEffect(spell_id, level)

    @property
    def place_entity(self):
        return self.space.GetEntity(self.place_entity_guid)

    @property
    def place_effect(self):
        return MCharacter.GetEffectEntity(self.place_effect_guid)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def _CreatePrePlaceEntity(self, spell_id, level):
        pos = self.position
        self.place_entity_guid = IdManager.genid()
        print('==========place_entity_guid===========', self.place_entity_guid)
        genv.space.create_entity('PlaceEntity', self.place_entity_guid, bdict={
            'owner_id': self.id,
            'position': (pos[0], pos[1] - 300, pos[-1]),
            'spell_id': spell_id,
            'level': level,
            'is_valid': False,
        })
        self.PrePlaceEntityTick(None)
        self.AddStoryTick(self.PrePlaceEntityTick)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def ClearPrePlaceEntity(self):
        # 删除放置物
        self.RemoveStoryTick(self.PrePlaceEntityTick)
        place_entity = self.place_entity
        if place_entity:
            place_entity.destroy()
            self.place_entity_guid = None
        self.place_entity_rotate = 0
        genv.messenger.Broadcast(events.ON_SHOW_SPELL_FORBID, False)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def ClearPrePlaceEffect(self):
        # 删除放置物特效
        self.RemoveStoryTick(self.PrePlaceEffectTick)
        if self.place_effect_id:
            MCharacter.ClearWorldEffectImmediately(self.place_effect_id)
        self.place_effect_id = None
        genv.messenger.Broadcast(events.ON_SHOW_SPELL_FORBID, False)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def _CreatePrePlaceEffect(self, spell_id, level):
        spell_proto = spell_data.data.get(spell_id, {}).get(level, {})
        pos = self.position
        trans = MType.Matrix4x3()
        trans.translation = MType.Vector3(*pos)
        trans.scale = MType.Vector3(1.0, 1.0, 1.0)
        effect_id = spell_proto.get('pre_effect_id', 944)
        self.place_effect_id = effect_util.WrapperPlayEffectInWorld2(effect_id, trans, -1, insure_play=True)
        self.place_effect_spell_id = spell_id
        self.PrePlaceEffectTick(None)
        self.AddStoryTick(self.PrePlaceEffectTick)
        pass

    def PrePlaceEntityTick(self, dtime):
        # 放置物tick，主要用来更新位置
        place_entity = self.place_entity
        if not place_entity:
            return
        spell_id = place_entity.spell_id
        spell_proto = spell_util.GetSpellProto(spell_id, place_entity.level)
        placer_func = getattr(self, 'GetPrePlaceEntityPosition%d' % spell_id, None)
        if not placer_func:
            placer_func = self.GetPrePlaceEntityPosition
        error_msg_id, trans = placer_func(spell_proto)
        if error_msg_id is None:
            place_entity.model.model.Transform = trans
            if not place_entity.is_valid:
                genv.messenger.Broadcast(events.ON_SHOW_SPELL_FORBID, False)
            place_entity.is_valid = True
            place_entity.SetForbidState(False)
        else:
            self.OnSpellCheckTips(self.place_effect_spell_id, spell_proto)
            if error_msg_id == -1:
                if place_entity.is_valid:
                    genv.messenger.Broadcast(events.ON_SHOW_SPELL_FORBID, True)
                place_entity.model.model.Transform = trans
                place_entity.is_valid = False
                place_entity.SetForbidState(True)

    def PrePlaceEffectTick(self, dtime):
        # 放置物特效tick
        spell_id = self.place_effect_spell_id
        place_entity = MCharacter.GetEffectEntity(self.place_effect_id)
        placer_func = getattr(self, 'GetPrePlaceEntityPosition%d' % spell_id, None)
        if not placer_func:
            placer_func = self.GetPrePlaceEntityPosition
        error_msg_id, trans = placer_func(None)
        place_entity.Transform = trans

    def GetPrePlaceEntityPosition(self, spell_proto):
        # 获取掩体参数
        block_proto = spell_proto.get('block', {})
        offset = block_proto.get('createposition', (0, 0, 5))
        # [DEBUG]
        if getattr(genv, 'block_param', None):
            offset = genv.block_param['createposition']
        # [DEBUG]

        # 先获取中心点
        matrix = MType.Matrix4x3()
        matrix.translation = MType.Vector3(*self.position)
        matrix.set_pitch_yaw_roll(0, self.upper_yaw, 0)
        center = matrix.transform_p(MType.Vector3(*offset))
        up = MType.Vector3(0, 1, 0)
        down = MType.Vector3(0, -1, 0)
        frm = center + up * 3
        space = genv.space
        r = space.RawRaycast(frm, 10, cconst.PHYSICS_BULLET,
                             with_trigger=False, to_dir=down)
        # [DEBUG]
        # genv.space.draw_ray = []
        # genv.space.DrawRay(frm, frm + 10 * down, False, (1, 1, 1))
        # [DEBUG]
        if not r or not r.IsHit:
            pos_new = frm + down * 20
            return 1, pos_new
        else:
            entity = getattr(r.Body, 'owner', None)
            if entity and entity.IsVehicle and entity.is_helicopter:
                # 不能丢在直升机上方
                trans = MType.Matrix4x3()
                trans.translation = r.Pos
                return 1, trans
        physics_hit_pos = r.Pos
        # 判断有没有打中entity
        entity = getattr(r.Body, 'owner', None)
        if entity and not entity.IsVehicle:
            return 2, physics_hit_pos

        # 判断打中点的高度和玩家的高度是否一致（粗略判定打中点是否悬空）
        if not -2 < center.y - physics_hit_pos.y < 2:
            return 3, physics_hit_pos
        # 判断打中的是不是地表
        # hit_terrain = False
        # parent = getattr(r.Body, 'Parent', None)
        # if parent:
        #   hit_name = parent.GetName()
        #   for terrain_name in TERRIAN_NAME:
        #     if terrain_name in hit_name:
        #       hit_terrain = True
        #       break
        # if not hit_terrain:
        #   return 3, physics_hit_pos

        # 匹配地形
        # 判断是否能隔墙放置
        if block_proto.get('p_wall', True):
            caster_pos = MType.Vector3(*self.position)
            for diff in (1, 2):
                _r = space.RawRaycast(caster_pos + MType.Vector3(0, diff, 0), 10, cconst.PHYSICS_BULLET, with_trigger=False, to_pos=physics_hit_pos + MType.Vector3(0, 0.1, 0))
                if _r and _r.IsHit:
                    return 7, physics_hit_pos
        yaw_diff = block_proto.get('yaw_diff', 0)
        # [DEBUG]
        if getattr(genv, 'block_param', None):
            yaw_diff = genv.block_param['yaw_diff']
        # [DEBUG]
        trans = MType.Matrix4x3()
        trans.translation = physics_hit_pos
        normal = r.Normal
        # TA要求y轴始终向上
        y_axis = MType.Vector3(0, 1, 0)
        old_yaw = MType.Vector3(0, 0, 1)
        old_yaw.yaw = self.upper_yaw + yaw_diff
        x_axis = normal.cross(old_yaw)
        z_axis = x_axis.cross(normal)
        trans.x_axis, trans.y_axis, trans.z_axis = x_axis, y_axis, z_axis
        pitch = trans.pitch
        roll = trans.roll
        q_pi = math.pi / 4
        if pitch < -q_pi or pitch > q_pi or roll < -q_pi or roll > q_pi:
            print(180 * pitch / math.pi, 180 * roll / math.pi)
            return 10, physics_hit_pos
        trans.scale = MType.Vector3(1, 1, 1)

        # [DEBUG]
        # genv.space.DrawRay(trans.translation, trans.translation + x_axis * 1, False, (1, 0, 0))
        # genv.space.DrawRay(trans.translation, trans.translation + z_axis * 1, False, (0, 1, 0))
        # genv.space.DrawRay(trans.translation, trans.translation + y_axis * 1, False, (0, 0, 1))
        # [DEBUG]

        # 检查形状大小
        bl_corner = block_proto.get('bl_corner', (-1, -1))
        tr_corner = block_proto.get('tr_corner', (1, 1))
        height = block_proto.get('height', (0, 2))
        # [DEBUG]
        if getattr(genv, 'block_param', None):
            bl_corner = genv.block_param['bl_corner']
            tr_corner = genv.block_param['tr_corner']
            height = genv.block_param['height']
        # [DEBUG]
        left, bottom = bl_corner
        right, top = tr_corner
        down, up = height
        # down -= 1
        bottom_left_down = trans.transform_p(MType.Vector3(left, down, bottom))
        bottom_left_up = trans.transform_p(MType.Vector3(left, up, bottom))
        top_left_down = trans.transform_p(MType.Vector3(left, down, top))
        top_left_up = trans.transform_p(MType.Vector3(left, up, top))
        bottom_right_down = trans.transform_p(MType.Vector3(right, down, bottom))
        bottom_right_up = trans.transform_p(MType.Vector3(right, up, bottom))
        top_right_down = trans.transform_p(MType.Vector3(right, down, top))
        top_right_up = trans.transform_p(MType.Vector3(right, up, top))
        ups = [bottom_left_up, top_left_up, bottom_right_up, top_right_up]
        downs = [bottom_left_down, top_left_down, bottom_right_down, top_right_down]
        # 四个脚浮空检查
        if block_proto.get('hit_scene'):
            for frm, to in zip(ups, downs):
                to += 0.5 * (to - frm)  # 延长一点
                r = space.RawRaycast(frm, 20, cconst.PHYSICS_BULLET, with_trigger=False, to_pos=to)
                if not (r and r.IsHit):
                    # 如果有一根射线没打中东西，认为其中一个脚浮空，不能放置
                    return 5, physics_hit_pos
            ups.extend(ups)
            downs.extend([MType.Vector3(physics_hit_pos.x, physics_hit_pos.y, physics_hit_pos.z)] * 4)
            for frm, to in zip(ups, downs):
                to.y += 0.2  # 抬高一点
                r = space.RawRaycast(frm, 20, cconst.PHYSICS_BULLET, with_trigger=False, to_pos=to)
                # [DEBUG]
                # genv.space.DrawRay(frm, to, False, (1, 0, 0))
                # [DEBUG]
                if r and r.IsHit:
                    # 打中了东西，不能放置
                    return 4, physics_hit_pos
                else:
                    entity = getattr(r.Body, 'owner', None)
                    if entity and entity.IsVehicle and entity.is_helicopter:
                        # 不能丢在直升机上方
                        trans = MType.Matrix4x3()
                        trans.translation = r.Pos
                        return 1, trans
                # 反向检测
                r = space.RawRaycast(to, 20, cconst.PHYSICS_BULLET, with_trigger=False, to_pos=frm)
                if r and r.IsHit:
                    # 打中了东西，不能放置
                    return 4, physics_hit_pos

        if block_proto.get('hit_avatar'):
            inv = trans.inverse
            for avatar in self.space.combat_avatars.values():
                pos = MType.Vector3(*avatar.position)
                local_pos = inv.transform_p(pos)
                if left < local_pos.x < right and down - 1.8 < local_pos.z < up and bottom < local_pos.z < top:
                    return 6, avatar

        return None, trans

    def GetPrePlaceEntityPosition200(self, spell_proto):
        max_length = 6
        space = self.space
        camera = MEngine.GetGameplay().Player.Camera
        camera_trans = camera.Transform
        device_screen_center = gui.device_screen_center
        shoot_dir = camera.GetRayDirectionFromScreenPoint(int(device_screen_center[0]), int(device_screen_center[1]))
        frm = camera_trans.translation
        r = space.RawRaycast(frm, 100, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=shoot_dir)
        if r.IsHit and (r.Pos - frm).length <= max_length:
            hit_pos = r.Pos
        else:
            hit_pos = frm + shoot_dir * max_length
        # 地面检测
        down = MType.Vector3(0, -1, 0)
        r_g = genv.space.RawRaycast(hit_pos, 0.3, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=down)
        if r_g and r_g.IsHit:
            hit_pos = MType.Vector3(hit_pos.x, r_g.Pos.y + 0.1, hit_pos.z)
        trans = MType.Matrix4x3()
        trans.translation = hit_pos
        player_trans = genv.player.model.model.Transform
        lookat_direction = trans.translation - player_trans.translation
        trans.look_at(lookat_direction, MType.Vector3(0, 1, 0))
        trans.pitch = 0
        trans.scale = MType.Vector3(1, 1, 1)
        return None, trans

    def GetPrePlaceEntityPosition225(self, spell_proto):
        max_length = 15
        space = self.space
        camera = MEngine.GetGameplay().Player.Camera
        camera_trans = camera.Transform

        device_screen_center = gui.device_screen_center
        shoot_dir = camera.GetRayDirectionFromScreenPoint(int(device_screen_center[0]), int(device_screen_center[1] * 1.2))
        frm = camera_trans.translation + shoot_dir * 1.0
        r = space.RawRaycast(frm, 20, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=shoot_dir)
        block_proto = spell_proto.get('block', {})
        left, bottom = block_proto.get('bl_corner', (-1, -1))
        right, top = block_proto.get('tr_corner', (1, 1))
        down, up = block_proto.get('height', (0, 2))

        # [DEBUG]
        if getattr(genv, 'block_param', None):
            left, bottom = genv.block_param['bl_corner']
            right, top = genv.block_param['tr_corner']
            down, up = genv.block_param['height']
        # [DEBUG]
        # 看冰墙是否穿过了最远的距离
        is_valid_pos = False
        if r.IsHit:
            if (r.Pos - frm).length <= max_length:
                is_valid_pos = True
            entity = getattr(r.Body, 'owner', None)
            if entity and entity.IsVehicle and entity.is_helicopter:
                return -1, self.invalid_trans

        if not is_valid_pos:
            pos = frm + shoot_dir * max_length
            r = space.RawRaycast(pos, 10, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=MType.Vector3(0, -1, 0))
            if r.IsHit:
                pos = r.Pos
                r = space.RawRaycast(frm, 20, cconst.PHYSICS_BULLET, with_trigger=False, to_pos=r.Pos)
                if r.IsHit:
                    pos = r.Pos

            else:
                return -1, self.invalid_trans
        else:
            pos = r.Pos

        # 检查冰墙是否与人穿插
        # 也不能放炮台
        entities = space.EntitiesInRange((pos.x, pos.y, pos.z), max_length) + list(space.entity_category['BarrieCannon'].values())
        trans = MType.Matrix4x3()
        trans.translation = pos
        trans.set_pitch_yaw_roll(0, self.yaw, 0)
        inv = trans.inverse
        valid_pos = []
        for entity in entities:
            local_pos = inv.transform_p(MType.Vector3(*entity.position))
            if left <= local_pos.x <= right and local_pos.z > bottom and down - 1.8 < local_pos.y < up:
                valid_pos.append(local_pos)
        valid_pos.sort(key=lambda p: p.z)
        last_z = bottom
        for p in valid_pos:
            if p.z - last_z > top - bottom:
                break
            else:
                last_z = p.z
        final_local_pos = MType.Vector3(0, 0, last_z - bottom)
        final_pos = trans.transform_p(final_local_pos)

        trans = MType.Matrix4x3()
        trans.translation = final_pos
        trans.set_pitch_yaw_roll(0, self.yaw, 0)

        # 让冰墙贴地
        ice_block_proto = spell_proto.get('icewall', {})
        ice_left, ice_bottom = ice_block_proto.get('tr_corner')
        ice_right, ice_top = ice_block_proto.get('bl_corner')
        h = 0.8
        four_corner = [trans.transform_p(MType.Vector3(ice_left, h, ice_right,)),
                       trans.transform_p(MType.Vector3(ice_left, h, ice_top,)),
                       trans.transform_p(MType.Vector3(ice_bottom, h, ice_right,)),
                       trans.transform_p(MType.Vector3(ice_bottom, h, ice_top,))]

        min_y = trans.translation.y
        for _r in four_corner:
            if self.space.IsConnectPoint(frm, _r, cconst.PHYSICS_BULLET):
                r = space.RawRaycast(_r, 2.5, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=MType.Vector3(0, -1, 0))

                if r.IsHit:
                    min_y = min(min_y, r.Pos.y)
                else:
                    return -1, trans
        final_pos.y = min_y
        trans.translation = final_pos

        # 检查是否有车
        inv = trans.inverse
        vehicle_entities = self.space.entity_category.get('Vehicle')
        if vehicle_entities:
            for guid, vehicle in vehicle_entities.items():
                model = vehicle.model.model
                if model and model.Primitives[0]:
                    local_bound = model.Primitives[0].LocalBound
                    center_pos = model.Transform.transform_p((local_bound.max + local_bound.min) * 0.5)
                    local_pos = inv.transform_p(center_pos)
                    if left - 2.2 <= local_pos.x <= right + 2.2 and bottom - 2.2 < local_pos.z < top + 2.2 and down - 1.8 < local_pos.y < up:
                        return -1, trans

        # [DEBUG]
        bottom_left_down = trans.transform_p(MType.Vector3(left, down, bottom))
        bottom_left_up = trans.transform_p(MType.Vector3(left, up, bottom))
        top_left_down = trans.transform_p(MType.Vector3(left, down, top))
        top_left_up = trans.transform_p(MType.Vector3(left, up, top))
        bottom_right_down = trans.transform_p(MType.Vector3(right, down, bottom))
        bottom_right_up = trans.transform_p(MType.Vector3(right, up, bottom))
        top_right_down = trans.transform_p(MType.Vector3(right, down, top))
        top_right_up = trans.transform_p(MType.Vector3(right, up, top))
        genv.space.draw_ray = []
        genv.space.DrawRay(bottom_left_down, bottom_left_up, False, (0, 0, 1))
        genv.space.DrawRay(top_left_down, top_left_up, False, (0, 0, 1))
        genv.space.DrawRay(bottom_right_down, bottom_right_up, False, (0, 0, 1))
        genv.space.DrawRay(top_right_down, top_right_up, False, (0, 0, 1))

        genv.space.DrawRay(bottom_left_down, bottom_right_down, False, (0, 0, 1))
        genv.space.DrawRay(top_left_down, top_right_down, False, (0, 0, 1))
        genv.space.DrawRay(bottom_left_up, bottom_right_up, False, (0, 0, 1))
        genv.space.DrawRay(top_left_up, top_right_up, False, (0, 0, 1))

        genv.space.DrawRay(bottom_left_down, top_left_down, False, (0, 0, 1))
        genv.space.DrawRay(bottom_left_up, top_left_up, False, (0, 0, 1))
        genv.space.DrawRay(bottom_right_down, top_right_down, False, (0, 0, 1))
        genv.space.DrawRay(bottom_right_up, top_right_up, False, (0, 0, 1))
        # [DEBUG]

        return None, trans

    def GetPrePlaceEntityPosition234(self, spell_proto):

        hold_time = time_util.GetTimeNow() - self.start_hold_time

        scale = MType.Vector3(0.1 * hold_time, 0.1 * hold_time, 0.1 * hold_time)

        trans = MType.Matrix4x3()
        trans.translation = MType.Vector3(*self.position)
        trans.set_pitch_yaw_roll(0, self.yaw, 0)
        trans.scale = scale

        return None, trans

    # 猫女得猫咪Dj台
    def GetPrePlaceEntityPosition300(self, spell_proto):
        valide, hit_pos = self.GetPrePlaceEntityHitPos300(spell_proto)
        if valide == -1:
            trans = MType.Matrix4x3()
            trans.translation = hit_pos
            trans.set_pitch_yaw_roll(0, self.upper_yaw + math.pi, 0)
            trans.scale = MType.Vector3(2, 2, 2)
            return -1, trans

        up = MType.Vector3(0, 1, 0)
        r = genv.space.AllOverlapByBox(hit_pos + up * 0.4, 0.9, 0.4, 0.9, cconst.PHYSICS_BULLET, is_vec=True)
        if r:
            trans = MType.Matrix4x3()
            trans.translation = hit_pos
            trans.set_pitch_yaw_roll(0, self.upper_yaw + math.pi, 0)
            trans.scale = MType.Vector3(2, 2, 2)
            return -1, trans

        trans = MType.Matrix4x3()
        trans.translation = hit_pos
        trans.set_pitch_yaw_roll(0, self.upper_yaw + math.pi, 0)
        trans.scale = MType.Vector3(2, 2, 2)
        return None, trans

    def GetPrePlaceEntityHitPos300(self, spell_proto):
        max_length = 15
        space = self.space
        camera = MEngine.GetGameplay().Player.Camera
        camera_trans = camera.Transform
        device_screen_center = gui.device_screen_center
        shoot_dir = camera.GetRayDirectionFromScreenPoint(int(device_screen_center[0]), int(device_screen_center[1]))
        frm = camera_trans.translation
        r = space.RawRaycastMask(frm, 100, cconst.PHYSICS_DJCAT_DETECT_MASK, with_trigger=False, to_dir=shoot_dir)  # 1
        up = MType.Vector3(0, 1, 0)
        down = MType.Vector3(0, -1, 0)
        orgine_hit_pos = None
        if r.IsHit and (r.Pos - frm).length <= max_length:
            normal = r.Normal
            # 先往外探测
            orgine_hit_pos = r.Pos
            frm = r.Pos + normal * 0.5
            r_g = space.RawRaycastMask(frm, 3.5, cconst.PHYSICS_DJCAT_DETECT_MASK, with_trigger=False, to_dir=down)  # 2
            hit_pos_g = None
            hit_dis_g = 100.0
            if r_g and r_g.IsHit:
                # 往外往下没打倒东西，
                hit_pos_g = r_g.Pos
                hit_dis_g = (r_g.Pos - r.Pos).length
            # 再往里探测
            frm = r.Pos - normal * 0.3 + up * 3
            r_u = space.RawRaycastMask(frm, 3, cconst.PHYSICS_DJCAT_DETECT_MASK, with_trigger=False, to_dir=down)  # 3
            hit_pos_u = None
            hit_dis_u = 100.0
            if r_u and r_u.IsHit:
                hit_pos_u = r_u.Pos
                hit_dis_u = (r_u.Pos - r.Pos).length
            # 看下哪个距离近
            if hit_dis_g < hit_dis_u:
                hit_pos = hit_pos_g
            else:
                hit_pos = hit_pos_u
            if not hit_pos:
                return -1, r.Pos
        else:
            # 没有打到东西
            hit_pos = frm + shoot_dir * max_length
            orgine_hit_pos = hit_pos
            frm = MType.Vector3(hit_pos.x, self.position[1], hit_pos.z) + up * 2
            r_g = space.RawRaycastMask(frm, 3, cconst.PHYSICS_DJCAT_DETECT_MASK, with_trigger=False, to_dir=down)  # 2
            if not r_g or not r_g.IsHit:
                # 贴地也没有，位置不合法
                return -1, hit_pos
            hit_pos = MType.Vector3(hit_pos.x, r_g.Pos.y, hit_pos.z)
        # 从当前位置再往hit_pos打一根射线校准基点位置
        r_g = space.ClosestRaycastMask(camera_trans.translation, hit_pos + up * 0.5, cconst.PHYSICS_DJCAT_DETECT_MASK, with_trigger=False)  # 3 or 4
        if not r_g or not r_g.IsHit:
            return None, hit_pos
        # 再做一次贴地检测
        normal = r_g.Normal
        frm = r_g.Pos - normal * 0.2 + up * 3
        hit_pos = frm
        r_g = genv.space.RawRaycastMask(frm, 6, cconst.PHYSICS_DJCAT_DETECT_MASK, with_trigger=False, to_dir=down)  # 4 or 5
        if not r_g or not r_g.IsHit:
            return -1, hit_pos
        hit_pos = r_g.Pos
        # 最后校验下位置是否合法
        r = space.ClosestRaycastMask(camera_trans.translation, hit_pos + up * 0.3, cconst.PHYSICS_DJCAT_DETECT_MASK, with_trigger=False)  # 3 or 4
        if r and r.IsHit:
            return -1, orgine_hit_pos
        return None, hit_pos

    def OnRotatePlaceEntity(self):
        # 旋转
        place_entity = self.place_entity
        if not place_entity:
            return
        if place_entity.can_rotate:
            self.place_entity_rotate += 1
    # endregion ##########放置物（掩体生成器之类）

    # region ###################赛博领域
    def OnHoldItem1006(self, item):
        self.CreatePrePlaceEffect(item.spell_id, level=1)

    def OnSpellItem1006(self, item):
        # 赛博领域
        genv.messenger.Broadcast(events.ON_TRIGGER_WALL_KICK, 1.5, 0)
        self.JumpToSkillToss()

    def _CreateCyberDomainPlaceEntity(self, spell_id):
        pos = self.position
        trans = MType.Matrix4x3()
        trans.translation = MType.Vector3(*pos)
        trans.scale = MType.Vector3(1, 1, 1)
        self.place_effect_id = effect_util.WrapperPlayEffectInWorld2(110001, trans, -1, insure_play=True)
        self.place_effect_spell_id = spell_id
        self.PrePlaceEffectTick(None)
        self.AddStoryTick(self.PrePlaceEffectTick)

    def GetPrePlaceEntityPosition306(self, spell_proto):
        max_length = 6
        space = self.space
        camera = MEngine.GetGameplay().Player.Camera
        camera_trans = camera.Transform
        device_screen_center = gui.device_screen_center
        shoot_dir = camera.GetRayDirectionFromScreenPoint(int(device_screen_center[0]), int(device_screen_center[1]))
        frm = camera_trans.translation
        r = space.RawRaycast(frm, 20, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=shoot_dir)
        if r.IsHit and (r.Pos - frm).length <= max_length:
            hit_pos = r.Pos
        else:
            hit_pos = frm + shoot_dir * max_length
        # 地面检测
        down = MType.Vector3(0, -1, 0)
        r_g = genv.space.RawRaycast(hit_pos, 15, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=down)
        if r_g and r_g.IsHit:
            hit_pos = MType.Vector3(hit_pos.x, r_g.Pos.y + 0.1, hit_pos.z)
        place_entity = MCharacter.GetEffectEntity(self.place_effect_id)

        if place_entity:
            trans = place_entity.Transform
        else:
            trans = MType.Matrix4x3()
        trans.translation = hit_pos
        scale = trans.scale
        player_trans = genv.player.model.model.Transform
        lookat_direction = trans.translation - player_trans.translation
        trans.look_at(lookat_direction, MType.Vector3(0, 1, 0))  # 这个函数会改effect的scale
        trans.pitch = 0
        trans.scale = scale
        trans.translation = player_trans.translation
        return None, trans
    # endregion ###################赛博领域

    def ThrowBomb(self, spell_id):
        # 触发扔手雷
        self.hand_model.JumpToThrowBomb(spell_id)
        # 自己第三人称扔手雷
        self.model.FireEvent('@grenade_throw', self.model.upper_graph_id)

    def ThrowBombReal(self, current_spell):
        # 扔手雷动作cue，真正扔出手雷
        current_spell.bomb.FireBomb(self)

    @events.ListenTo(events.ON_COMBAT_STATE_CHANGE)
    def OnCombatStateChangeForCameraEffect(self, avatar):
        # 倒地时候清空所有屏幕特效
        if avatar.is_fps_avatar and avatar.combat_state == consts.CombatState.DYING:
            self.RemoveAllCameraEffect()

    def AddCameraEffect(self, effect_id, keep_time=-1, color=None):
        self.RemoveCameraEffect(effect_id)
        camera_effect_id = MHelper.PlayCameraEffectById(effect_id, keep_time)
        if not camera_effect_id:
            return
        effect_entity = MCharacter.GetEffectEntity(camera_effect_id)
        for prim in effect_entity.Primitives:
            prim.CustomRenderSet = cconst.RENDER_SET_POST_BLEND
        if color:
            for prim in effect_entity.Primitives:
                prim.InstanceColor = MType.Vector4(color[0] / 255.0, color[1] / 255.0, color[2] / 255.0, 1.0)
        self.camera_effect_ids[effect_id] = camera_effect_id
        if keep_time > 0:
            self.CancelRemoveCameraEffectTimer(effect_id)
            self.camera_effect_timers[effect_id] = \
                self.add_timer(keep_time, functools.partial(self.RemoveCameraEffect, effect_id))
        return camera_effect_id

    def RemoveCameraEffect(self, effect_id):
        self.CancelRemoveCameraEffectTimer(effect_id)
        camera_effect_id = self.camera_effect_ids.pop(effect_id, None)
        if camera_effect_id:
            MHelper.ClearEffectInWorld(camera_effect_id)

    def AddCameraEffectModel(self, model_id, keep_time=-1, color=None):
        # todo: 临时方案
        from gclient.framework.models.simple_model import SimpleModel
        from gclient.data import unit_model_data
        self.RemoveCameraEffectModel(model_id)
        data = unit_model_data.data.get(model_id, {})
        if not data:
            return
        camera_effect_model = SimpleModel(self)
        camera_effect_model.Load(data)
        camera_effect_model.AttachToParent(genv.camera.controller, 'HP_Camera', 'Scene Root')
        self.camera_effect_models[model_id] = camera_effect_model
        if keep_time > 0:
            self.CancelRemoveCameraEffectModelTimer(model_id)
            self.camera_effect_timers[model_id] = \
                self.add_timer(keep_time, functools.partial(self.RemoveCameraEffectModel, model_id))
        camera_effect_model.SetVariableF("AnimOffset", 0.5)

    def RemoveCameraEffectModel(self, model_id):
        self.CancelRemoveCameraEffectModelTimer(model_id)
        camera_effect_model = self.camera_effect_models.pop(model_id, None)
        print(camera_effect_model)
        if not camera_effect_model:
            return
        camera_effect_model.SetVariableF("AnimOffset", -1.0)
        camera_effect_model.model.Detach()
        camera_effect_model.Destroy()
        # 把entity visible设置成False, 并且从camera移除

    def GetCameraEffectModel(self, model_id):
        return self.camera_effect_models.get(model_id)

    def CancelRemoveCameraEffectTimer(self, effect_id):
        _timer = self.camera_effect_timers.pop(effect_id, None)
        _timer and self.cancel_timer(_timer)

    def CancelRemoveCameraEffectModelTimer(self, model_id):
        _timer = self.camera_effect_timers.pop(model_id, None)
        _timer and self.cancel_timer(_timer)

    def RemoveAllCameraEffect(self):
        for effect_id, _timer in self.camera_effect_timers.items():
            _timer and self.cancel_timer(_timer)
        self.camera_effect_timers = {}
        for effect_id, camera_effect_id in self.camera_effect_ids.items():
            MHelper.ClearEffectInWorld(camera_effect_id)
        self.camera_effect_ids = {}

    # region ##########火墙指示器特效
    def OnHoldItem250(self, item):
        # 火墙指示器
        self.CreateFireWallPlaceEntity(item.spell_id, self.firewall_mode)

    def OnHoldItem309(self, item):
        # 火墙指示器
        self.CreateFireWallPlaceEntity(item.spell_id, self.firewall_mode)

    def OnSpellItem250(self, item):
        # 火墙
        self.JumpToSkillToss()

    def OnSpellItem309(self, item):
        # 火墙
        self.JumpToSkillToss()

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def ChangeSpellMode226(self):
        self.firewall_mode += 1
        self.firewall_mode %= 2

    def ChangeSpellMode227(self):
        self.JumpToSwitchToss()

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def ChangeSpellMode231(self):
        self.firewall_mode += 1
        self.firewall_mode %= 2

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def CreateFireWallPlaceEntity(self, spell_id, firewall_mode):
        print('==========CreateFireWallPlaceEntity')
        self.firewall_mode = firewall_mode  # 不要觉得这一行代码是多此一举，录像是可以把函数参数传进来的
        self.ClearFireWallPlaceEntity()
        spell_level = 1  # TODO
        spell_proto = spell_util.GetSpellProto(spell_id, spell_level)
        firewall_proto = spell_proto.get('firewall', {})
        self.firewall_proto = firewall_proto
        info_list = self.GetFireWallPlacePosition(firewall_proto)
        self._CreateFireWallPlaceEntity(info_list, spell_id, spell_level)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def _CreateFireWallPlaceEntity(self, info_list, spell_id, spell_level):
        spell_proto = spell_util.GetSpellProto(spell_id, spell_level)
        firewall_proto = spell_proto.get('firewall', {})
        effects = firewall_proto.get("place_effects", [402, ])
        for info in info_list:
            pos = info[:3]
            pos = [pos[0], pos[1] + 3, pos[2]]
            scale = info[3]
            yaw = info[-1]
            effect_ids = []
            for effect_id in effects:
                if effect_id == -1:
                    continue
                trans = MType.Matrix4x3()
                trans.translation = MType.Vector3(*pos)
                trans.scale = MType.Vector3(scale, scale, scale)
                trans.set_pitch_yaw_roll(0, yaw, 0)
                e_id = effect_util.WrapperPlayEffectInWorld2(effect_id, trans, -1, insure_play=True)
                effect_ids.append(e_id)

            self.place_firewall_effects.append(effect_ids)
        self.AddStoryTick(self.PlaceFireWallEntityTick)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def ClearFireWallPlaceEntity(self):
        self.RemoveStoryTick(self.PlaceFireWallEntityTick)
        for effect_ids in self.place_firewall_effects:
            for effect_id in effect_ids:
                effect_util.ClearWorldEffectImmediately(effect_id)
        self.place_firewall_effects = []

    def PlaceFireWallEntityTick(self, dtime):
        firewall_proto = self.firewall_proto
        effects = firewall_proto.get("place_effects", [402, ])
        info_list = self.GetFireWallPlacePosition(firewall_proto)
        info_list_len = len(info_list)
        len_effects = len(self.place_firewall_effects)
        for idx in list(range(info_list_len)):
            info = info_list[idx]
            pos = info[:3]
            pos = [pos[0], pos[1] + 3, pos[2]]
            scale = info[3]
            yaw = info[-1]
            if idx < len_effects:
                effect_ids = self.place_firewall_effects[idx]
                for effect_id in effect_ids:
                    entity = MCharacter.GetEffectEntity(effect_id)
                    if not entity:
                        continue
                    entity.IsVisible = True
                    trans = entity.Transform
                    trans.translation = MType.Vector3(*pos)
                    trans.scale = MType.Vector3(scale, scale, scale)
                    trans.set_pitch_yaw_roll(0, yaw, 0)
                    entity.Transform = trans
            else:
                effect_id_list = []
                for effect_id in effects:
                    if effect_id == -1:
                        continue
                    trans = MType.Matrix4x3()
                    trans.translation = MType.Vector3(*pos)
                    trans.scale = MType.Vector3(scale, scale, scale)
                    trans.set_pitch_yaw_roll(0, yaw, 0)
                    e_id = effect_util.WrapperPlayEffectInWorld2(effect_id, trans, -1, insure_play=True)
                    effect_id_list.append(e_id)
                self.place_firewall_effects.append(effect_id_list)

        if info_list_len < len_effects:
            for idx in range(info_list_len, len_effects):
                effect_ids = self.place_firewall_effects[idx]
                for effect_id in effect_ids:
                    entity = MCharacter.GetEffectEntity(effect_id)
                    if not entity:
                        continue
                    entity.IsVisible = False

    def GetFireWallPlacePositionV(self, proto):
        max_distance = proto.get('place_distance', 10)
        cur_dir = formula.YawToVector(self.yaw)
        start_pos = formula.Add3D(formula.Mul3D(cur_dir, 2), self.position)
        interval = proto.get('place_interval', 2)
        return self._GetFireWallPlacePosition(max_distance, interval, cur_dir, start_pos, self.yaw - math.pi / 2)

    def GetFireWallPlacePositionH(self, proto):
        max_length = 15
        space = genv.space
        max_distance = proto.get('place_distance', 8) / 2
        dir_left = self.yaw + math.pi / 2
        dir_right = self.yaw - math.pi / 2
        middle_point = gui.device_screen_center
        camera = MEngine.GetGameplay().Player.Camera
        camera_trans = camera.Transform
        to_dir = camera.GetRayDirectionFromScreenPoint(int(middle_point[0]),
                                                       int(middle_point[1] * 1.2))
        frm = camera_trans.translation + to_dir * 1.0
        r = space.RawRaycast(frm, 20, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=to_dir)
        # 看冰墙是否穿过了最远的距离
        is_valid_pos = False
        if r.IsHit:
            if (r.Pos - frm).length <= max_length:
                is_valid_pos = True

        if not is_valid_pos:
            pos = frm + to_dir * max_length
            r = space.RawRaycast(pos, 10, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=MType.Vector3(0, -1, 0))
            if r.IsHit:
                pos = (r.Pos.x, r.Pos.y, r.Pos.z)
                r = space.RawRaycast(frm, 20, cconst.PHYSICS_BULLET, with_trigger=False, to_pos=r.Pos)
                if r.IsHit:
                    pos = (r.Pos.x, r.Pos.y, r.Pos.z)
            else:
                return []
        else:
            pos = (r.Pos.x, r.Pos.y, r.Pos.z)

        # 先算左边
        interval = proto.get('place_interval', 2)
        cur_dir = formula.YawToVector(dir_left)
        start_pos_l = formula.Add3D(cur_dir, pos)
        ret = self._GetFireWallPlacePosition(max_distance, interval, cur_dir, start_pos_l, self.yaw)

        # 再算右边
        cur_dir = formula.YawToVector(dir_right)
        start_pos_r = formula.Add3D(cur_dir, pos)
        ret.extend(self._GetFireWallPlacePosition(max_distance, interval, cur_dir, start_pos_r, self.yaw - math.pi))
        return ret

    def GetFireWallPlacePosition(self, proto):
        # 返回线路上火墙指示器的位置，从近到远
        # dir
        if self.firewall_mode == 0:
            return self.GetFireWallPlacePositionV(proto)
        else:
            return self.GetFireWallPlacePositionH(proto)

    def _GetFireWallPlacePosition(self, max_distance, interval, cur_dir, start_pos, yaw):
        _ = self
        cur_dir = MType.Vector3(*cur_dir)
        start_pos = MType.Vector3(*start_pos)
        n = int(max_distance / interval) + 1
        ret = []
        last_height = None
        # 这里打射线会很耗，可能需要
        for idx in range(0, n + 1):
            pos = start_pos + interval * idx * cur_dir
            if last_height:
                pos.y = last_height

            s_pos = MType.Vector3(pos.x, pos.y + 3, pos.z)
            e_pos = pos
            r_dir = e_pos - s_pos
            r_dir.length = 1.0
            p_results, r = genv.player.space.RaycastWithPenetrate(s_pos,
                                                                  6,
                                                                  cconst.PHYSICS_COMMON_OBSTACLE,
                                                                  with_trigger=False,
                                                                  to_dir=r_dir)
            if r and r.IsHit and r.Body:
                pos = r.Pos
                last_height = pos.y
                ret.append((pos.x, pos.y, pos.z, 1.0, yaw))
                continue
            # 第一个没打到的地方break掉
            break
        return ret
    # endregion ##########火墙指示器特效

    # region ##########电磁干扰特效
    def CreateElecticalFieldEffect(self, spell_id, level=1):
        # 创建放置物
        self.ClearElecticalFieldEffect()
        self._CreateElecticalFieldEffect(spell_id, level)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def _CreateElecticalFieldEffect(self, spell_id, level=1):
        spell_proto = spell_util.GetSpellProto(spell_id, level)
        shock_proto = spell_proto.get('shock', {})
        hold_effect = shock_proto.get('hold_effect', 0)
        if not hold_effect:
            return
        self.hold_effect_id = hold_effect_id = effect_util.WrapperPlayEffectInWorld(hold_effect, self.position, -1, insure_play=True)
        entity = MCharacter.GetEffectEntity(hold_effect_id)
        entity.Attach(self.model.model)
        if entity.Tach:
            entity.Tach.EnableTachVisible = False

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def ClearElecticalFieldEffect(self):
        self.StopSoundByTag('Electric')
        if self.hold_effect_id:
            effect_util.ClearWorldEffectImmediately(self.hold_effect_id)

    def PlaceElecticalFieldTick(self, dtime):
        dtime = time_util.GetTimeNow() - self.start_hold_time
        entity = MCharacter.GetEffectEntity(self.hold_effect_id)
        if entity:
            t = entity.Tach.Transform
            t.scale = MType.Vector3(1, 1, 1 + dtime)
            print(t.scale)
            entity.Tach.Transform = t
    # endregion ##########电磁干扰特效

    # region ##########炮台回收 #########################################
    def CanRecycleCannon(self, spell_id):
        spell_proto = spell_util.GetSpellProto(spell_id)
        cannon_proto = spell_proto.get('cannon', {})
        if not cannon_proto:
            self.cannon_entity = None
            return False

        if not self.skill_relate_item:
            self.cannon_entity = None
            return False

        if not self.cannon_entity:
            self.cannon_entity = self.space.entities.get(self.skill_relate_item, None)
            if not self.cannon_entity:
                return False

        if self.cannon_entity.id != self.skill_relate_item:
            self.cannon_entity = None
            return False
        if self.cannon_entity.is_destroyed():
            self.cannon_entity = None
            return False
        if not self.cannon_entity.IsBarrieCannon:
            return False
        cannon_entity = self.cannon_entity
        recycle_range = cannon_proto.get('recycle_range', 5)
        if not cannon_entity.dissolving and formula.InRange3D(self.position, cannon_entity.position, recycle_range):
            return True
        return False

    def RecycleCannon(self, spell_id):
        self.CallServer("CallSpellLogic", spell_id, "Recycle", [])

    # endregion

    # region ########## 超级冲刺
    @LimitCall(0.1)
    def StartParkour(self, spell_id, spell_level, item_guid):
        if self.parkour != 0:
            return
        if self.ban_skill:
            return
        spell_util.CastSpell(self, spell_id, spell_level, item_guid)
    # endregion ##########

    # region ########## 简单加buff类技能
    @LimitCall(0.1)
    def AddBuffSpell(self, spell_id, spell_level, item_guid):
        if self.ban_skill:
            return
        spell_util.CastSpell(self, spell_id, spell_level, item_guid)
    # endregion ##########

    # region ########## 治疗无人机
    def OnSpellItem60(self, item):
        self.OnSpellItemCureUav(item)

    def OnSpellItem102(self, item):
        self.OnSpellItemCureUav(item)

    def OnSpellItem103(self, item):
        self.OnSpellItemCureUav(item)

    def OnSpellItem104(self, item):
        self.OnSpellItemCureUav(item)

    def OnSpellItem469(self, item):
        self.OnSpellItemCruiseUav(item)

    def OnSpellItemSecondary469(self, item):
        self.OnSpellItemCruiseUav(item)

    def OnSpellItem213(self, item):
        self.OnSpellItemCureUav(item)

    def OnSpellItem353(self, item):
        self.OnSpellItemCureUav(item)

    def OnSpellItem354(self, item):
        self.OnSpellItemCureUav(item)

    OnSpellItem424 = OnSpellItem425 = OnSpellItem354

    def OnSpellItemCruiseUav(self, item):
        spell_id = item.spell_id
        spell = self.spell_mgr.current_spell.get(spell_id)
        cure_self = True
        if spell and getattr(spell, 'select_target', None) != self:
            cure_self = False
        if self.hand_model:
            self.hand_model.JumpToSkillToss()
        # 第三人称
        self.model.FireEvent('@skill_toss', self.model.upper_graph_id)
        self.OnUavToss(cure_self)

    def OnSpellItemCureUav(self, item):
        spell_id = item.spell_id
        spell = self.spell_mgr.current_spell.get(spell_id)
        cure_self = True
        if spell and getattr(spell, 'select_target', None) != self:
            cure_self = False
        if self.hand_model:
            if cure_self:
                self.hand_model.JumpToSkillToss()
            else:
                self.hand_model.JumpToSkillToss2()
        # 第三人称
        self.model.FireEvent('@skill_toss', self.model.upper_graph_id)
        self.OnUavToss(cure_self)

    @ReplaySpectatorDecorator('ReplayForCombatAvatar')
    def OnUavToss(self, cure_self):
        if self.hand_model:
            self.hand_model.OnExtraAttachToss('@skill_toss' if cure_self else '@skill_toss2')
    # endregion ########## 治疗无人机

    # region ########## 武士

    def OnHoldItem310(self, item):
        self.OnHoldSamuraiSprint(item)

    def OnSpellItem310(self, item):
        self.CancelSamuraiSprintTick()

    def OnHoldItem1015(self, item):
        self.OnHoldSamuraiSprint(item)

    def OnSpellItem1015(self, item):
        self.CancelSamuraiSprintTick()

    def OnHoldSamuraiSprint(self, item):
        if not switches.ENABLE_SAMURAI_SPRINT_TIPS:
            return
        self.CancelSamuraiSprintTick()
        self._OnHoldSamuraiSprint(item.spell_id)

    def _OnHoldSamuraiSprint(self, spell_id, spell_level=1):
        born_pos = self.position
        born_pos = (born_pos[0], born_pos[1] - 100, born_pos[-1])
        self.samurai_sprint_start_time = time.time()
        self.samurai_sprint_effect_id = effect_util.WrapperPlayEffectInWorld(748, born_pos, insure_play=True)
        self.samurai_sprint_tick = functools.partial(self.SamuraiSprintTick, spell_id, spell_level)
        self.samurai_sprint_tick(None)
        self.AddStoryTick(self.samurai_sprint_tick)

    def SamuraiSprintTick(self, spell_id, spell_level, dt):
        camera = MEngine.GetGameplay().Player.Camera
        trans = camera.Transform
        camera_pos_vec = trans.translation
        device_screen_center = gui.device_screen_center
        camera_dir = camera.GetRayDirectionFromScreenPoint(int(device_screen_center[0]), int(device_screen_center[1]))
        space = self.space
        spell_proto = spell_util.GetSpellProto(spell_id, spell_level)

        samurai_proto = spell_proto.get('samurai', {})
        holding_time = time.time() - self.samurai_sprint_start_time
        hold_times = samurai_proto.get('hold_time', (0.6, 1.2, 1.8))
        min_tips_distance, max_tips_distance = samurai_proto.get('sprint_tips_distance', (10, 20))
        max_speed_hold_time = hold_times[-1]
        if holding_time > max_speed_hold_time:
            max_dist = max_tips_distance
        else:
            max_dist = min_tips_distance + (max_tips_distance - min_tips_distance) * holding_time / max_speed_hold_time

        r = space.RawRaycast(camera_pos_vec, max_dist, cconst.PHYSICS_SHOOT_TEST, with_trigger=False, to_dir=camera_dir)
        if r and r.IsHit:
            pos = r.Pos
            self.samurai_sprint_pos = pos
        else:
            pos = camera_pos_vec + camera_dir * max_dist
            # r = space.RawRaycast(pos, 10, cconst.PHYSICS_BULLET, with_trigger=False, to_dir=MType.Vector3(0, -1, 0))
            # if r.IsHit:
            #     pos = r.Pos
            #     r = space.RawRaycast(camera_pos_vec, 20, cconst.PHYSICS_BULLET, with_trigger=False, to_pos=r.Pos)
            #     if r.IsHit:
            #         pos = r.Pos
            # else:
            #     pos = self.samurai_sprint_pos

        if self.samurai_sprint_effect_id:
            # dis = (pos - camera_pos_vec).length
            # scale = 1.0
            # if dis > 5.0:
            #     scale = (dis - 5) * 0.05 + 1
            # pos = pos - camera_dir * 0.8 * scale
            effect_entity = MCharacter.GetEffectEntity(self.samurai_sprint_effect_id)
            trans = effect_entity.Transform
            # pos.y += 0.5
            trans.translation = pos
            camera_dir_l = (camera_dir.x, camera_dir.y, camera_dir.z)
            trans.yaw = formula.Vector3DToYaw(camera_dir_l)
            # trans.pitch = - formula.VectorToPitch(camera_dir_l)

            # trans.scale = MType.Vector3(scale, scale, scale)
            effect_entity.Transform = trans

    def CancelSamuraiSprintTick(self):
        if not switches.ENABLE_SAMURAI_SPRINT_TIPS:
            return
        self.samurai_sprint_tick and self.RemoveStoryTick(self.samurai_sprint_tick)
        self.samurai_sprint_tick = None
        # 删除落点特效
        effect_util.ClearWorldEffectImmediately(self.samurai_sprint_effect_id)
        self.samurai_sprint_effect_id = None

    # endregion ##########

    # region #########技能释放提示##########################
    def OnSpellCheckTips(self, spellID, spell_proto=None):
        if spell_proto is None:
            spell_proto = spell_util.GetSpellProto(spellID)
        targetPrompt = spell_proto.get('skill_ui_check', 1272)
        if not HudPromptComp.instance().IsProtoInPrompt(targetPrompt):
            print(f"[OnSpellCheckTips] skill_ui_check value is {spell_proto.get('skill_ui_check', 1272)}")
            gui.Prompt(targetPrompt)
    # endregion


class PlayerCombatAvatarMember(CombatAvatarMember):
    """
    :type-self: gclient.gameplay.logic_base.entities.combat_avatar.PlayerCombatAvatar
    """
    # 幻象
    Property("holographic_robot_id", "")
    # 炮塔
    Property('cannon_id', '')

    def __init_component__(self, _):
        CombatAvatarMember.__init_component__(self, _)
        self.last_shoot_idx = -1
        self.shoot_idx = 0
        self.shoot_immediately = True
        self._delay_shoot_reason = 0
        self.dual_shoot_immediately = True
        self._delay_dual_shoot_reason = 0
        self.is_shoot_breaking = False  # 开枪打断动作中
        self.is_fire_by_right = False   # 右开火键开火
        self.fire_by_right_timestamp = 0
        self.fireend_by_right_timestamp = 0
        self.is_fire_by_left = False   # 左开火键开火
        self.fire_by_left_timestamp = 0
        self.fireend_by_left_timestamp = 0

        self.try_fire_start = False
        self.try_dual_fire_start = False
        self.try_enter_ads = False

        self._is_ads = False
        self._is_real_ads = False
        self._is_shooting = False
        self._is_dual_shooting = False

        # self.delay_attack_timer = None
        self.is_enter_ads_by_fire = False
        self.force_no_fall_hurt = False

        self.refresh_observe_timer = None
        self.camera_id_before_ads = None

        self.light_attack_pos = None
        self.break_use_medicine = False

        self.light_attack_range_eid = None

        self.reload_fire_interval_timer = None
        self.reload_dual_fire_interval_timer = None
        # 开火要打断一段时间冲刺状态
        self.fire_block_sprint_timer = None
        self.compensate_timer = None
        self.shoot_rescue_timer = None
        self.dual_shoot_rescue_timer = None
        self.ads_optics_open_timer = None
        self.ads_optics_close_timer = None
        self.ads_render_fade_in_timer = None
        self.ads_render_fade_out_timer = None
        self.ads_render_finish_in_timer = None
        # self.ads_render_finish_out_timer = None
        self.cannon_pre_place_effect = None
        self.heat_data = {}
        self.last_pos = None
        self.smoke_tick_timer = None
        self.playing_smoke = None
        self.smoke_eid = None
        self.spell_start = False
        self.damage_spell_dict = {}

    def _on_set_holographic_robot_id(self, old):
        genv.messenger.Broadcast(events.ON_HOLOGRAPHIC_ROBOT_CHANGE)

    def __post_component__(self, _):
        self._spell_result_cache = []
        self.AddStoryTick(self.TickDealSpellResultRpc)

    def __on_leave_space_component__(self):
        self.force_no_fall_hurt = False
        effect_util.ClearWorldEffect(self.light_attack_range_eid)
        self.light_attack_range_eid = None

    def CacheDealSpellResultRpc(self, data):
        self._spell_result_cache.append(data)
        if data.get("damage_result"):
            # 带伤害的要暂存下，等后端真正造成伤害影响表现
            self.damage_spell_dict[data.get("id", "")] = data

    @rpc_method(CLIENT_STUB, EntityID())
    def CauseDamage(self, spell_id):
        result = self.damage_spell_dict.pop(spell_id, None)
        # print(f"CauseDamage: {spell_id}\n {result}")
        self.ShowDamageEffect(result)
    
    def TickDealSpellResultRpc(self, dt):
        if self.is_destroyed():
            return
        if not self._spell_result_cache:
            return

        cache = self._spell_result_cache
        self._spell_result_cache = []
        if len(cache) == 1:
            self.CallServerGameLogic("DealSpellResult", cache[0])
        else:
            self.CallServerGameLogic("BatchDealSpellResult", cache)

    def AddCameraEffect(self, effect_id, keep_time=-1, color=None):
        if self.settle_data and not genv.avatar.is_replaying:
            # 结算屏蔽屏幕特效
            return
        eid = CombatAvatarMember.AddCameraEffect(self, effect_id, keep_time, color)
        if eid and self.light_attack_mode:
            effect_entity = MCharacter.GetEffectEntity(eid)
            effect_entity.IsVisible = False

    def SetAllCameraEffectVisible(self, visible):
        for eid in self.camera_effect_ids.values():
            effect_entity = MCharacter.GetEffectEntity(eid)
            if effect_entity:
                effect_entity.IsVisible = visible

    def OnSpellResultImpl(self, result):
        CombatAvatarMember.OnSpellResultImpl(self, result)

        if not result.ballistic_effect:
            genv.messenger.Broadcast(events.ON_SPELL_EVENT, result)

    def NavigatorCannonToPosition(self):
        if not self.cannon_id:
            return
        spell_id = 326  # "指挥战车"
        cam = MEngine.GetGameplay().Player.Camera
        frm_pos = cam.Transform.translation
        middle_point = gui.device_screen_center
        direction = cam.GetRayDirectionFromScreenPoint(int(middle_point[0]), int(middle_point[1]))
        to_pos = frm_pos + 500 * direction
        res = self.space.ClosestRaycast(frm_pos, to_pos, cconst.PHYSICS_SHOOT_TEST, False)
        if res and res.IsHit:
            normal = res.Normal
            if normal.dot(MType.Vector3(0, 1, 0)) <= 0.01:
                gui.Prompt(1509)
                return
            pos = (res.Pos.x, res.Pos.y, res.Pos.z)
            self.CallServer('CallSpellLogic', spell_id, 'MoveToPosition', [pos])
        else:
            gui.Prompt(1509)

    def HidePrePlaceCannonEffect(self):
        if self.cannon_pre_place_effect:
            MHelper.ClearEffectInWorld(self.cannon_pre_place_effect[0], immediately=True)
            self.cannon_pre_place_effect = None
            self.RemoveStoryTick(self.StoryTickForCannonPreEffect)

    def ShowPrePlaceCannonEffect(self):
        if self.cannon_pre_place_effect:
            return
        effect_id = MHelper.PlayEffectInWorldByEffectId(170, self.position, insure_play=True)
        effect_entity = MCharacter.GetEffectEntity(effect_id)
        if effect_id:
            self.cannon_pre_place_effect = (effect_id, effect_entity)
            self.AddStoryTick(self.StoryTickForCannonPreEffect)

    def StoryTickForCannonPreEffect(self, delta=0):
        if not self.cannon_pre_place_effect:
            return
        camera = MEngine.GetGameplay().Player.Camera
        start_pos = camera.GetOrigin()
        max_dist = 300
        middle_point = gui.device_screen_center
        shoot_screen_pos = (middle_point[0], middle_point[1])
        shoot_dir = camera.GetRayDirectionFromScreenPoint(int(shoot_screen_pos[0]), int(shoot_screen_pos[1]))
        res = self.space.RawRaycast(start_pos, max_dist, cconst.PHYSICS_SHOOT_TEST, with_trigger=False,
                                           to_dir=shoot_dir)    # noqa
        if not res or not res.IsHit:
            target_pos = start_pos + shoot_dir * max_dist
        else:
            target_pos = res.Pos
        entity = self.cannon_pre_place_effect[1]
        t = entity.Transform
        t.translation = target_pos
        entity.Transform = t

    # 遥控炮塔
    def IsConductorNavigatorCannon(self):
        cannon = self.space.GetEntityByID(self.cannon_id)
        if not cannon or not cannon.IsNavigateBarrieCannon:
            return
        if cannon.dissolving:
            return
        return True

    # region #########光线打击
    def OnHoldItem203(self, item):
        self.OnHoldItemLightAttack(item)

    def UseItemAutoEnd203(self, item):
        genv.input_ctrl.RemoveBanInputReason(cconst.GameInputType.MoveInput, cconst.BAN_INPUT_REASON_LIGHT_ATTACK)
        if genv.camera.is_free_placer:
            pre_direction = genv.camera.pre_direction
            genv.camera.RestoreCamera()
            genv.camera.placer.RotateTo(pre_direction.yaw, pre_direction.pitch)
        self.OnExitLightAttackMode()

    def OnSpellItem203(self, item):
        self.light_attack_pos = genv.camera.placer.placer.FocusPos.tuple()
        self.OnSpellItemLightAttack(item)

    def OnHoldItemLightAttack(self, item):
        genv.input_ctrl.AddBanInputReason(cconst.GameInputType.MoveInput, cconst.BAN_INPUT_REASON_LIGHT_ATTACK)
        yaw = MEngine.GetGameplay().Player.Camera.Transform.yaw
        genv.camera.ApplyCamera(cconst.CAMERA_ID_LIGHT_ATTACK,
                                info={'target_direction': (-math.sin(yaw), -cconst.LIGHT_ATTACK_HEIGHT_TOP, -math.cos(yaw)),
                                      'target_distance': cconst.LIGHT_ATTACK_HEIGHT_TOP})
        self.OnEnterLightAttackMode()

    def OnEnterLightAttackMode(self):
        self.light_attack_mode = True
        for avatar in self.space.combat_avatars.values():
            if self.game_logic.IsEnemy(self, avatar) and avatar.model:
                avatar.model.AddHiddenReason(cconst.HIDDEN_REASON_LIGHT_ATTACK)
        self.SetAllCameraEffectVisible(False)
        self.SortAndActiveWorldField()
        self.light_attack_range_eid = effect_util.WrapperPlayEffectInWorld(189, self.position, -1, insure_play=True)
        if self.follow_entity:
            self.follow_entity.model.AddHiddenReason(cconst.HIDDEN_REASON_LIGHT_ATTACK)
        genv.messenger.Broadcast(events.ON_SHOW_LIGHT_ATTACK_WINDOW, True)
        genv.messenger.Broadcast(events.ON_HIDE_UI_FOR_REASON, ui_define.UI_HIDDENREASON_ON_LIGHT_ATTACK, True)

    def OnExitLightAttackMode(self):
        self.light_attack_mode = False
        for avatar in self.space.combat_avatars.values():
            if self.game_logic.IsEnemy(self, avatar) and avatar.model:
                avatar.model.RemoveHiddenReason(cconst.HIDDEN_REASON_LIGHT_ATTACK)
        self.SetAllCameraEffectVisible(False)
        self.SortAndActiveWorldField()
        effect_util.ClearWorldEffectImmediately(self.light_attack_range_eid)
        self.light_attack_range_eid = None
        if self.follow_entity:
            self.follow_entity.model.RemoveHiddenReason(cconst.HIDDEN_REASON_LIGHT_ATTACK)
        genv.messenger.Broadcast(events.ON_SHOW_LIGHT_ATTACK_WINDOW, False)
        genv.messenger.Broadcast(events.ON_HIDE_UI_FOR_REASON, ui_define.UI_HIDDENREASON_ON_LIGHT_ATTACK, False)

    def CanVisibleInLightAttackMode(self, avatar):
        return not self.light_attack_mode or not self.space.game_logic.IsEnemy(avatar, self)

    # endregion ########光线打击

    @property
    def main_spell(self):
        if self.hand_model:
            current_weapon_case = self.hand_model.GetCurClientWeaponCase()
            if not current_weapon_case:
                return
            return current_weapon_case.spell_id
        else:
            current_weapon = self.GetCurWeapon()
            if not current_weapon:
                return
            return current_weapon.spell_id

    @property
    def vice_spell(self):
        current_weapon = self.GetCurWeapon()
        if not current_weapon:
            return
        return current_weapon.vice_spell_id

    @property
    def is_ads(self):
        return self._is_ads

    @is_ads.setter
    def is_ads(self, val):
        self._is_ads = val
        sensi_switch_mode = LocalConfig.controller_sensi_switch_mode if gui.is_using_gamepad else LocalConfig.sensi_switch_mode
        if sensi_switch_mode == cconst.SensiSwitchMode.ADS_B:
            self.UpdatePlayerSensitivity()
        elif sensi_switch_mode == cconst.SensiSwitchMode.ADS_M:
            weapon_case = self.GetCurWeaponCase()
            if weapon_case:
                interval = weapon_case.GetWeaponAttrValue('AdsUpAnimTimeConf' if val else "AdsDownAnimTimeConf", 0)
            else:
                interval = 0
            self.UpdatePlayerSensitivity(interval)
        camera = genv.camera
        if camera and camera.is_fps_placer:
            camera.placer.EnableAdditiveFovOffset(self._is_ads)
        broadcast = genv.messenger.Broadcast
        broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.EnterAds, self._is_ads)
        broadcast(events.ON_HIDE_UI_FOR_REASON, ui_define.UI_HIDDENREASON_ADS, self._is_ads)
        broadcast(events.ON_ADS_STATE_CHANGE, self._is_ads)
        if not val or not self.vehicle:
            broadcast(events.ON_UI_OPACITY_CHANGE, cconst.OPACITY_REASON_ADS, val)

    @property
    def is_real_ads(self):
        return self._is_real_ads

    @is_real_ads.setter
    def is_real_ads(self, val):
        self._is_real_ads = val
        # 远处加载
        self.hand_model and self.hand_model.SetIsRealAds(val)
        self.RefreshObservationPoint(val)
        sensi_switch_mode = LocalConfig.controller_sensi_switch_mode if gui.is_using_gamepad else LocalConfig.sensi_switch_mode
        if sensi_switch_mode == cconst.SensiSwitchMode.ADS_E:
            self.UpdatePlayerSensitivity()
        if val:
            weapon_case = self.GetCurWeaponCase()
            if weapon_case and (optic_part := weapon_case.GetWeaponPartModel(consts.WeaponPartType_Optic)):
                camera = MEngine.GetGameplay().Player.Camera
                # 相机垂直fov
                camera_vfov = camera.FieldOfView
                # 相机水平fov
                camera_hfov = ConvertVFovToHFov(camera_vfov)
                # 枪械水平fov
                gun_vfov = weapon_case.GetAttrValue('AffiliatedFovTarget')
                # 枪械垂直fov
                gun_hfov = ConvertVFovToHFov(gun_vfov)
                x = math.tan(math.radians(gun_hfov) / 2.0) / math.tan(math.radians(camera_hfov) / 2.0)
                y = math.tan(math.radians(gun_vfov) / 2.0) / math.tan(math.radians(camera_vfov) / 2.0)
                optic_part.SetShaderGraphParameter('FovFixX', str(x))
                optic_part.SetShaderGraphParameter('FovFixY', str(y))
        genv.messenger.Broadcast(events.ON_REAL_ADS_STATE_CHANGE, self._is_real_ads)
        val and self.AutoTryGunAttack(cconst.DELAY_SHOOT_REASON_FOR_FIRE_REAL_ADS)
        self.CallServer('EnterAdsStatus', val)

    @property
    def is_shooting(self):
        if self._is_shooting != self.is_shooting_status:
            return self._is_shooting
        return self.is_shooting_status

    @is_shooting.setter
    def is_shooting(self, val):
        if self._is_shooting == val:
            return
        self._is_shooting = val
        self.CallServer('EnterIsShootingStatus', val)
        self.UpdatePlayerSensitivity()
        self.UpdateConfigGyroscopeSensByState()
        self.UpdatePlayerShootState(val)
        # self.UpdateMotionBlurParams()

    @property
    def is_dual_shooting(self):
        return self._is_dual_shooting

    @is_dual_shooting.setter
    def is_dual_shooting(self, val):
        self._is_dual_shooting = val

    def __on_load_model_component__(self):
        self.UpdateMotionBlurParams()

    def _OnPlayerFire_Sniper(self, fire_mode, is_begin):
        # 狙击枪
        if is_begin:
            if self.is_ads:
                if fire_mode in (FireMode.FIRE_ADS, FireMode.FIRE_REAL_ADS):
                    self.is_real_ads and self.TryAttack()
                else:
                    self.TryAttack()
            else:
                if fire_mode in (FireMode.FIRE_ADS, FireMode.FIRE_REAL_ADS):
                    # 一键开镜自动开枪
                    self.EnterAdsState(True)
                    self.is_enter_ads_by_fire = True
                else:
                    self.TryAttack()
        else:
            if self.is_ads and self.is_enter_ads_by_fire:
                if self.is_real_ads:
                    # 完全开镜 开枪
                    self.TryAttack()
                    self.add_timer(0.2, functools.partial(self.EnterAdsStateByFire, False))
                else:
                    # 否则 关镜
                    self.EnterAdsStateByFire(False)
            else:
                self.EnterAdsStateByFire(False)
                self.EndAttack()

    def _OnPlayerFire_Launcher(self, fire_mode, is_begin):
        # rpg发射器
        if is_begin:
            if self.is_ads:
                if self.is_real_ads:
                    self.TryAttack()
                    self.add_timer(1.0, functools.partial(self.EnterAdsStateByFire, False))
            else:
                self.EnterAdsState(True)
                self.is_enter_ads_by_fire = True
                if fire_mode == FireMode.FIRE_ONLY:
                    # 腰射 需要自动开枪
                    self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_FIRE_REAL_ADS)
                    self.SetIsShootBreaking(True)
        else:
            if self.is_ads and self.is_enter_ads_by_fire:
                if self.is_real_ads:
                    # 完全开镜 开枪
                    self.TryAttack()
                    self.add_timer(1.0, functools.partial(self.EnterAdsStateByFire, False))
                else:
                    # 否则 关镜
                    self.EnterAdsStateByFire(False)
            else:
                self.EnterAdsStateByFire(False)
                self.EndAttack()

    def _OnPlayerFire_Bow(self, fire_mode, is_begin):
        # 复合弓
        if is_begin:
            self.TryAttack()
            if not self.is_ads and fire_mode in (FireMode.FIRE_ADS, FireMode.FIRE_REAL_ADS):
                self.EnterAdsState(True)
                self.is_enter_ads_by_fire = True
        else:
            self.EndAttack()
            self.EnterAdsStateByFire(False)

    def _OnPlayerFire_Normal(self, fire_mode, is_begin):
        # 正常步枪
        if is_begin:
            if self.is_ads:
                if fire_mode == FireMode.FIRE_REAL_ADS and not self.is_real_ads:
                    self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_FIRE_REAL_ADS)
                    self.SetIsShootBreaking(True)
                else:
                    self.TryAttack()
            else:
                if fire_mode in (FireMode.FIRE_ADS, FireMode.FIRE_REAL_ADS):
                    # 一键开镜自动开枪
                    self.EnterAdsState(True)
                    self.is_enter_ads_by_fire = True
                    if fire_mode != FireMode.FIRE_REAL_ADS:
                        self.TryAttack()
                    else:
                        self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_FIRE_REAL_ADS)
                        self.SetIsShootBreaking(True)
                else:
                    self.TryAttack()
        else:
            self.EndAttack()
            self.EnterAdsStateByFire(False)

    def _OnPlayerFire_SingleShotGun(self, fire_mode, is_begin):
        # 单发的霰弹枪
        is_single_shotgun_runningshot = LocalConfig.single_shotgun_fire_mode == cconst.SingleShotGunFireMode.RunningShot
        # 单发和连发要做区分
        if is_begin:
            if is_single_shotgun_runningshot:
                if self.is_ads:
                    if fire_mode == FireMode.FIRE_REAL_ADS and not self.is_real_ads:
                        self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_FIRE_REAL_ADS)
                        self.SetIsShootBreaking(True)
                    else:
                        self.TryAttack()
                else:
                    if fire_mode in (FireMode.FIRE_ADS, FireMode.FIRE_REAL_ADS):
                        # 一键开镜自动开枪
                        self.EnterAdsState(True)
                        self.is_enter_ads_by_fire = True
                        if fire_mode != FireMode.FIRE_REAL_ADS:
                            self.TryAttack()
                        else:
                            self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_FIRE_REAL_ADS)
                            self.SetIsShootBreaking(True)
                    else:
                        self.TryAttack()
            else:
                if fire_mode == FireMode.FIRE_ONLY:
                    self.TryAttack()
                else:
                    if not self.is_ads:
                        self.EnterAdsState(True)
                        self.is_enter_ads_by_fire = True
        else:
            if is_single_shotgun_runningshot:
                self.EndAttack()
                self.EnterAdsStateByFire(False)
            else:
                if fire_mode == FireMode.FIRE_ONLY:
                    self.EndAttack()
                    self.EnterAdsStateByFire(False)
                else:
                    self.TryAttack()
                    self.EndAttack()
                    self.add_timer(0.2, functools.partial(self.EnterAdsStateByFire, False))

    def OnPlayerFireBegin(self):
        self.try_fire_start = True
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon:
            return
        if cur_weapon.is_own_gun:
            fire_mode = self.CheckFireMode()
            gun_type = cur_weapon.gun_type
            equip_id = cur_weapon.equip_id
            gun_id = cur_weapon.gun_id
            is_sniper = weapon_util.IsGunSniper(gun_id)
            is_shotgun_single_shot = weapon_util.IsShotGunSingleShot(gun_id)
            is_semi_auto_sniper = weapon_util.IsSemiAutoSniper(gun_id)
            is_launcher = gun_type in (consts.GunType.RL,)
            is_bow = weapon_util.IsWeaponCompoundBow(equip_id)
            is_dual_weapon = cur_weapon.is_dual_weapon
            if is_bow:
                self._OnPlayerFire_Bow(fire_mode, True)
            elif is_launcher:
                self._OnPlayerFire_Launcher(fire_mode, True)
            elif is_shotgun_single_shot:
                self._OnPlayerFire_SingleShotGun(fire_mode, True)
            elif is_sniper and not is_semi_auto_sniper:
                self._OnPlayerFire_Sniper(fire_mode, True)
            elif is_dual_weapon:
                self.TryAttack()
            else:
                self._OnPlayerFire_Normal(fire_mode, True)
        else:
            self.TryAttack()

    def OnPlayerFireEnd(self):
        if not self.reload_fire_interval_timer:
            self.DoPlayerFireEnd()
        else:
            self.DoPlayerFireEnd()
            self.compensate_timer and self.cancel_timer(self.compensate_timer)
            self.compensate_timer = None
            # 存在一发延迟补偿
            weapon_case = self.GetCurWeaponCase()
            if not weapon_case:
                return
            cur_time = time.time()
            fire_interval = weapon_case.GetWeaponAttrValue('FireIntervalTimeConf', 0)
            cur_fire_interval = cur_time - self.gun_fire_timestamp
            if fire_interval >= cur_fire_interval:
                self.compensate_timer = self.add_timer(fire_interval - cur_fire_interval + 0.01, self.DoPlayerFireEnd)

    def DoPlayerFireEnd(self):
        # if genv.input_ctrl.IsMouseLDown():
        #     return
        self.try_fire_start = False
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon:
            return
        is_gun = cur_weapon.is_own_gun
        fire_mode = self.CheckFireMode(is_end=True)
        if self.is_fire_by_right and self.is_fire_by_left:
            if is_gun and fire_mode in (FireMode.FIRE_ADS, FireMode.FIRE_REAL_ADS) and self.is_ads and self.is_enter_ads_by_fire:
                self.EnterAdsStateByFire(False)
            return
        if is_gun:
            # 一键开镜自动开枪
            gun_id = cur_weapon.gun_id
            gun_type = cur_weapon.gun_type
            equip_id = cur_weapon.equip_id
            is_sniper = weapon_util.IsGunSniper(gun_id)
            is_bow = weapon_util.IsWeaponCompoundBow(equip_id)
            is_launcher = gun_type in (consts.GunType.RL,)
            is_dual_weapon = cur_weapon.is_dual_weapon
            is_shotgun_single_shot = weapon_util.IsShotGunSingleShot(gun_id)
            is_semi_auto_sniper = weapon_util.IsSemiAutoSniper(gun_id)
            if is_bow:
                self._OnPlayerFire_Bow(fire_mode, False)
            elif is_launcher:
                self._OnPlayerFire_Launcher(fire_mode, False)
            elif is_shotgun_single_shot:
                self._OnPlayerFire_SingleShotGun(fire_mode, False)
            elif is_sniper and not is_semi_auto_sniper:
                self._OnPlayerFire_Sniper(fire_mode, False)
            elif is_dual_weapon:
                self.EndAttack()
            else:
                self._OnPlayerFire_Normal(fire_mode, False)
        else:
            self.EnterAdsStateByFire(False)
            self.EndAttack()

    def OnPlayerDualFireBegin(self):
        if self.IsCurTakeLeftHandThrowableWeapon():
            curr_item = self.GetCurLeftHandWeapon() or self.GetCurWeapon()
            if curr_item:
                return self.UseCombatItem(curr_item.guid, cconst.UseItemMode.ATTACK_DOWN)
        else:
            self.try_dual_fire_start = True
            cur_weapon = self.GetCurWeapon()
            if not cur_weapon or not cur_weapon.is_dual_weapon:
                return
            self.TryAttack(spell_id=cur_weapon.vice_spell_id)

    def OnPlayerDualFireEnd(self):
        if self.IsCurTakeLeftHandThrowableWeapon():
            curr_item = self.GetCurLeftHandWeapon() or self.GetCurWeapon()
            if curr_item:
                return self.UseCombatItem(curr_item.guid, cconst.UseItemMode.ATTACK_UP)
        else:
            self.try_dual_fire_start = False
            cur_weapon = self.GetCurWeapon()
            if not cur_weapon or not cur_weapon.is_dual_weapon:
                return
            self.EndAttack(spell_id=cur_weapon.vice_spell_id)

    def OnBreakUseMedicine(self, by_attack=False):
        if self.main_spell in cconst.SPELL_ID_MEDICINES:
            # 打断打药
            self.break_use_medicine = True
            self.OnTakeSpecWeapon('')
            if by_attack:
                self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_RAISEWEAPON)
                self.try_fire_start = True

    def TryBreakCurUseMedicine(self):
        cur_weapon = self.backpack.get(self.cur_spec_weapon_guid)
        if cur_weapon and cur_weapon.equip_type == consts.EquipmentType.MEDICINE:
            if self.main_spell in cconst.SPELL_ID_MEDICINES:
                # 打断打药
                self.break_use_medicine = True

    def TryBreakUseMedicineOnSetCurWeapon(self):
        cur_weapon = self.backpack.get(self.cur_spec_weapon_guid)
        if cur_weapon and cur_weapon.equip_type != consts.EquipmentType.MEDICINE:
            if self.main_spell in cconst.SPELL_ID_MEDICINES:
                # 打断打药
                self.break_use_medicine = True

    def OnPlayerAttackBegin(self):
        self.OnBreakUseMedicine(by_attack=True)
        self.CancelHelpTeammate()
        if (self.IsCurTakeGunWeapon() or self.IsCurTakeMeleeWeapon()) and not self.IsCurTakeLeftHandThrowableWeapon():
            # 左手拿雷时 右开火键不能开枪
            return self.OnPlayerFireBegin()
        # elif self.IsCurTakeSamuraiWeapon():
        #     # SamuraiWeapon是技能物品，也是近战物品，可以进行近战
        #     return self.OnPlayerFireBegin()
        else:
            curr_item = self.GetCurLeftHandWeapon() or self.GetCurWeapon()
            if curr_item:
                ret = self.UseCombatItem(curr_item.guid, cconst.UseItemMode.ATTACK_DOWN)
                if curr_item.equip_type == consts.EquipmentType.CRACK_SCANNER:
                    # hardcode一下，扫描器拿起之后直接开枪，如果以后有别的建议填表实现
                    self.OnPlayerFireBegin()
                return ret

    def OnPlayerAttackEnd(self):
        if self.IsStateInRelationshipSet(cconst.StateRelationship.AutoFire):
            return
        if (self.IsCurTakeGunWeapon() or self.IsCurTakeMeleeWeapon()) and not self.IsCurTakeLeftHandThrowableWeapon():
            return self.OnPlayerFireEnd()
        elif self.IsCurTakeSamuraiWeapon():
            return self.OnPlayerFireEnd()
        else:
            self.try_fire_start = False
            curr_item = self.GetCurLeftHandWeapon() or self.GetCurWeapon()
            if curr_item:
                return self.UseCombatItem(curr_item.guid, cconst.UseItemMode.ATTACK_UP)

    def PlayFireAction(self, spell_id, info=None):
        if self.hand_model:
            self.ReloadWeaponStop()
            self.hand_model.JumpToFire(info, spell_id=spell_id)

        # 3P开火的时候先把之前的Uppergraph干掉，例如切枪的
        self.model and self.model.PopUpperGraph()
        self.model and self.model.JumpToFire(info, spell_id=spell_id)

    def PlayFireReleaseAction(self, spell_id=None, info=None):
        # 松开 开火键 注意这时候，current_spell是否已经被清掉未知。
        self.hand_model and self.hand_model.JumpToFireEnd()
        self.model and self.model.JumpToFireEnd()

    def PlayDualFireAction(self, spell_id, info=None):
        if self.hand_model:
            self.ReloadDualWeaponStop()
            self.hand_model.JumpToDualFire(info, spell_id=spell_id)
        self.model and self.model.JumpToDualFire(info, spell_id=spell_id)

    def PlayDualFireReleaseAction(self, spell_id=None, info=None):
        # 松开 开火键 注意这时候，current_spell是否已经被清掉未知。
        self.hand_model and self.hand_model.JumpToDualFireEnd()
        self.model and self.model.JumpToDualFireEnd()

    def PlayMeleeStartAction(self, spell_id, info=None):
        if not self.IsCurTakeMeleeWeapon() and not self.IsCurTakeGunMeleeWeapon() and not self.IsCurTakeSamuraiWeapon():
            return
        self.EnterAdsState(False, is_force_out=True)
        self.hand_model and self.hand_model.JumpToMeleeStart(info, spell_id=spell_id)
        self.model and self.model.JumpToMeleeStart(info, spell_id=spell_id)

    def PlayMeleeEndAction(self, spell_id=None, info=None):
        self.hand_model and self.hand_model.JumpToMeleeEnd()
        self.model and self.model.JumpToMeleeEnd()

    def ThrowBomb(self, spell_id):
        CombatAvatarMember.ThrowBomb(self, spell_id)

    def ThrowBombReal(self, current_spell):
        # 扔手雷动作cue，真正扔出手雷
        CombatAvatarMember.ThrowBombReal(self, current_spell)

    def GetCurLeftHandSpellId(self):
        lefthand_weapon = self.GetCurLeftHandWeapon()
        if lefthand_weapon:
            return lefthand_weapon.spell_id
        return None

    def CheckCanEnterAds(self):
        hand_model = self.hand_model
        if not hand_model:
            return False
        if not self.hand_model.cur_client_weapon_guid:
            return
        # if hand_model.action_state in (cconst.UNIT_STATE_RAISEWEAPON, cconst.UNIT_STATE_DROPWEAPON):
        #   return False
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.EnterAds):
            return False
        if not self.IsCurTakeGunWeapon():
            return False
        if self.IsCurTakeMissileWeapon() and self.IsStateInRelationshipSet(cconst.StateRelationship.Reload, cconst.StateRelationship.Reloading, cconst.StateRelationship.ReloadEnd):
            return False
        cur_weapon = self.GetCurWeapon()
        if not cur_weapon:
            return False
        if weapon_util.IsWeaponAmmunitionBreakAds(cur_weapon.part_slots):
            if cur_weapon.client_ammo <= 0:
                return False
            elif self.IsStateInRelationshipSet(cconst.StateRelationship.Reload, cconst.StateRelationship.Reloading):
                return False
        if self.model.motion_state == cconst.UNIT_STATE_EXIT_PRONE or self.prone_for_forbidden_ads:
            return False
        elif self.model.motion_state == cconst.UNIT_STATE_PRONE:
            fire_mode = self.CheckFireMode()
            is_graph_moving = self.model.is_graph_moving
            if fire_mode == FireMode.FIRE_REAL_ADS and is_graph_moving:
                # 开镜后开火模式，如果趴着移动，开枪，此时可以射击的话，让它开枪
                return self.CheckCanGunAttack(self.main_spell)
            elif fire_mode == FireMode.FIRE_ADS:
                return True
            return not is_graph_moving
        return True

    def CheckFireMode(self, is_end=False):
        gun_type = self.GetCurWeaponGunType()
        if LocalConfig.is_advance_basic_setting and LocalConfig.fire_mode_diy:
            gun_type_str = GunType.to_string(gun_type)
            fire_mode = LocalConfig.get(gun_type_str + '_fire_mode')
            lfire_mode = LocalConfig.get(gun_type_str + '_lfire_mode')
        else:
            fire_mode = LocalConfig.fire_mode if LocalConfig.is_advance_basic_setting else LocalConfig.fire_mode_simple
            lfire_mode = LocalConfig.lfire_mode
        if is_end:
            mode = fire_mode if self.fireend_by_right_timestamp >= self.fireend_by_left_timestamp else lfire_mode
        else:
            if self.is_fire_by_right and fire_mode in (FireMode.FIRE_ADS, FireMode.FIRE_REAL_ADS):
                mode = fire_mode
            elif self.is_fire_by_left and lfire_mode in (FireMode.FIRE_ADS, FireMode.FIRE_REAL_ADS):
                mode = lfire_mode
            else:
                mode = fire_mode if self.is_fire_by_right else lfire_mode
        return mode

    def GetFireMode(self, is_left_fire):
        gun_type = self.GetCurWeaponGunType()
        if LocalConfig.is_advance_basic_setting and LocalConfig.fire_mode_diy:
            gun_type_str = GunType.to_string(gun_type)
            fire_mode = LocalConfig.get(gun_type_str + '_fire_mode')
            lfire_mode = LocalConfig.get(gun_type_str + '_lfire_mode')
        else:
            fire_mode = LocalConfig.fire_mode if LocalConfig.is_advance_basic_setting else LocalConfig.fire_mode_simple
            lfire_mode = LocalConfig.lfire_mode
        return fire_mode if not is_left_fire else lfire_mode

    def RefreshObservationPoint(self, is_ads):
        if is_ads:
            optic_type = self.GetCurWeaponPartOpticType()
            if optic_type < consts.WeaponPartOpticType.Triple:
                return
            screen_center = gui.device_screen_center
            detect_pos = []
            raycast_result = spell_core_main.GetShootResult(self, 520, screen_center)
            if not raycast_result:
                return
            if not raycast_result.is_hit:
                detect_pos.append(formula.Tuple(raycast_result.start_pos + raycast_result.shoot_dir * 520))
            else:
                pos = formula.Tuple(raycast_result.physics_hit_pos)
                if formula.Distance3DSquare(self.position, pos) < 80 * 80:
                    return
                detect_pos.append(pos)
            self.LockBeforeReadyToAppear()
            self.SetAdsLoadPoint(detect_pos)
            if not self.refresh_observe_timer:
                self.refresh_observe_timer = self.add_repeat_timer(1, functools.partial(self.RefreshObservationPoint, is_ads))
        else:
            self.SetAdsLoadPoint([])
            self.refresh_observe_timer and self.cancel_timer(self.refresh_observe_timer)
            self.refresh_observe_timer = None

    @Async(TAG_SPACE)
    def LockBeforeReadyToAppear(self):
        space = self.space
        if not space or space.is_destroyed():
            return
        space.world.IsLockingProxy = True
        for i in list(range(20)):
            yield 0.1
            if not space or space.is_destroyed():
                return
            if space.world.IsReadyToAppear:
                break
        space.world.IsLockingProxy = False

    def SetAdsOpticPreStencil(self, optic_model, value: bool, opaque_hide: bool):
        if value:
            optic_model.SetCustomRenderSet(cconst.RENDER_SET_HUD_PRE_STENCIL)
        else:
            optic_model.SetCustomRenderSet(cconst.RENDER_SET_HUD_SIGHT)
        if opaque_hide:
            print("try opaque_hide")
            MRender.WriteOnBlackBoard("HudStencil_DoOpaqueClip", True)
        else:
            MRender.WriteOnBlackBoard("HudStencil_DoOpaqueClip", False)

    def SetAdsDoF(self, value: bool, fade_time: float):
        if value:
            cur_weapon = self.GetCurWeaponCase()
            if not cur_weapon or not cur_weapon.is_gun:
                genv.performance.EnableDof(False)
                return
            genv.performance.EnableDof(True)
            # 这里我尝试了大量位置方案，最后发现都有漏洞，不如当场每帧更新
            self.UpdateAdsDofParams()

            # 使用add_repeat_timer来替代反复添加单次计时器
            self.ads_dof_fade_time = fade_time
            self.ads_dof_pass_time = 0

            # 不加这个可能新的覆盖旧的，内存泄漏式timer
            if self.ads_render_finish_in_timer is not None:
                self.cancel_timer(self.ads_render_finish_in_timer)
                self.ads_render_finish_in_timer = None
            self.ads_render_finish_in_timer = self.add_repeat_timer(0.033, self.UpdateAdsDofDuringFade)
        else:
            genv.performance.EnableDof(False)
            # 当value为False时，停止重复更新DoF参数
            if hasattr(self, 'ads_render_finish_in_timer') and self.ads_render_finish_in_timer:
                self.cancel_timer(self.ads_render_finish_in_timer)
                self.ads_render_finish_in_timer = None

    def UpdateAdsDofParams(self):
        """更新DoF参数的辅助方法"""
        cur_weapon = self.GetCurWeaponCase()
        if not cur_weapon.is_gun:
            return

        cur_opt = cur_weapon.GetWeaponPartModel(consts.WeaponPartType_Optic)
        if cur_opt:
            cur_opt_pos = MType.Vector3(cur_opt.position[0], cur_opt.position[1], cur_opt.position[2])
        else:
            # print("Render:: enter ads dof, but can not find optic model, DOF params will derive from HP_acog")
            cur_weapon_body = cur_weapon.weapon_body
            root = cur_weapon_body.root_part.model
            cur_opt_pos = root.GetBoneWorldPosition("HP_acog")

        cur_opt_z = abs(genv.camera.engine_camera.Transform.inverse.transform_p(cur_opt_pos)[2])
        is_pt = weapon_util.GetWeaponGunType(cur_weapon.weapon_id) in (GunType.PT,)
        focus_dist = max(cur_opt_z + ((-0.025) if is_pt else 0.15) - 0.02, 0.01)
        focus_ratio = 0.3 if is_pt else 0.3
        blurriness = 0.5 if is_pt else 0.27
        blurriness = 0.5
        params = {
            "FocalDistance": focus_dist,
            "FocalRegion": 10000,
            "NearTransitionRegion": focus_dist * focus_ratio,
            "FarTransitionRegion": 10000,
            "Blurriness": blurriness,
        }
        genv.performance.SetDofParams(params)

    def UpdateAdsDofDuringFade(self):
        """在渐变过程中更新DoF参数的计时器回调函数"""
        # 如果在换弹，则延迟结束时间
        if not self.IsStateInRelationshipSet(cconst.StateRelationship.Reload, cconst.StateRelationship.Reloading):
            self.ads_dof_pass_time += 0.033
        self.UpdateAdsDofParams()

        # 检查是否已完成渐变
        if self.ads_dof_pass_time >= self.ads_dof_fade_time + 0.033:
            self.cancel_timer(self.ads_render_finish_in_timer)
            self.ads_render_finish_in_timer = None

    def EndAdsDoF(self):
        # print(f"############EndAdsDoF")
        if hasattr(self, 'ads_render_finish_in_timer') and self.ads_render_finish_in_timer:
            self.cancel_timer(self.ads_render_finish_in_timer)
            self.ads_render_finish_in_timer = None

    def SetAdsRender(self, value: bool, fade_time: float):
        if value:
            for key, val in fixed_ads_render_options.items():
                gpl.SetRenderOption(key, val)
        else:
            for key in fixed_ads_render_options:
                val = fixed_render_options.get(key)
                if val is not None:
                    gpl.SetRenderOption(key, val)
            self.EndAdsDoF()
        self.SetAdsDoF(value, fade_time)

    def EnterAdsState(self, value, is_force_out=False, keep_delay_ads=False):
        if self.is_ads == value:
            # 优化
            return

        cur_weapon = self.GetCurWeaponCase()
        if not cur_weapon:
            return

        if value and not self.CheckCanEnterAds():
            return

        # 打断滑铲
        # value and self.OnSlideBreak()

        self.hand_model.JumpToAds(value)
        self.model.JumpToAds(value)

        # ADS 渲染相关的内容
        weapon_attr_getter = cur_weapon.GetWeaponAttrValue
        ads_up_time = weapon_attr_getter('AdsUpAnimTimeConf', 0)
        ads_down_time = weapon_attr_getter('AdsDownAnimTimeConf', 0)

        # 裁切
        if hasattr(cur_weapon, "GetWeaponPartModel"):
            cur_optic = cur_weapon.GetWeaponPart(consts.WeaponPartType_Optic)
            cur_optic_model = cur_weapon.GetWeaponPartModel(consts.WeaponPartType_Optic)
            if cur_optic and cur_optic_model:
                opaque_hide = consts.WeaponPartFeature.NeedReflector in weapon_util.GetWeaponPartFeature(weapon_util.GetRealOpticPartIdByDlc(cur_optic.part_id))
                if value:
                    self.ads_optics_close_timer and self.cancel_timer(self.ads_optics_close_timer)
                    self.ads_optics_close_timer = None
                    self.ads_optics_open_timer = self.add_timer(0.0, functools.partial(self.SetAdsOpticPreStencil, cur_optic_model, value, opaque_hide))
                else:
                    self.ads_optics_open_timer and self.cancel_timer(self.ads_optics_open_timer)
                    self.ads_optics_open_timer = None
                    self.ads_optics_close_timer = self.add_timer(ads_down_time * 0.5, functools.partial(self.SetAdsOpticPreStencil, cur_optic_model, value, False))

        if value:
            self.ads_render_fade_out_timer and self.cancel_timer(self.ads_render_fade_out_timer)
            self.ads_render_fade_out_timer = None
            self.ads_render_fade_in_timer = self.add_timer(ads_up_time * 0.5, functools.partial(self.SetAdsRender, value, ads_up_time * 0.5))
        else:
            self.ads_render_fade_in_timer and self.cancel_timer(self.ads_render_fade_in_timer)
            self.ads_render_fade_in_timer = None
            self.ads_render_fade_out_timer = self.add_timer(ads_down_time * 0.5, functools.partial(self.SetAdsRender, value, ads_down_time * 0.5))

        self.is_ads = value
        if not value and is_force_out:
            if not keep_delay_ads:
                self.try_enter_ads = False
            if self.is_real_ads:
                self.is_real_ads = value

        if not value and self.prone_is_ads:
            self.prone_is_ads = False

        camera = genv.camera
        # 3p debug需求, 叠加3p ads pos
        if camera.camera_id and value and not getattr(genv, "debug_3p_aim", False):
            get_info = getattr(camera.placer, 'GetCameraStoreInfo', None)
            info = get_info() if get_info else {}
            self.camera_id_before_ads = (camera.camera_id, info) if not self.is_fps_mode else None
            # tps模式下开镜
            camera.SetAimCamera(value)

        if not camera.is_fps_placer:
            return

        camera.placer.FSMAppend('Aim', True, bool(value))
        aim_time = weapon_attr_getter('AdsUpAnimTimeConf', 0.2) if value else weapon_attr_getter('AdsDownAnimTimeConf', 0.2)
        if HudFrontsightComp.isInited():
            is_bow = weapon_util.IsWeaponCompoundBow(cur_weapon.weapon_id)
            if value:
                HudFrontsightComp.instance().AimPointFadeOut(aim_time, need_move=not is_bow)
            else:
                HudFrontsightComp.instance().AimPointFadeIn(aim_time, need_move=not is_bow)

        # 操作开镜即进入walk
        self.RefreshSpeedAndMotion(force_refresh=True)

        if self.camera_id_before_ads and not value:
            # tps模式下关镜
            self.camera_id_before_ads[1].update({'target_direction': camera.placer.placer.Direction, 'need_blender': False})
            camera.ApplyCamera(self.camera_id_before_ads[0], info=self.camera_id_before_ads[1])
            self.camera_id_before_ads = None

        # 设置一些参数
        # sunshine模式下每帧设置下参数
        # if genv.sunshine and enable_dof:
        #     params = {
        #         "FocalDistance": weapon_case_getter("FocalDistance", 0.5),
        #         "FocalRegion": weapon_case_getter("FocalRegion", 0.5),
        #         "NearTransitionRegion": weapon_case_getter("NearTransitionRegion", 0.5),
        #         "FarTransitionRegion": weapon_case_getter("FarTransitionRegion", 10000.0),
        #         "Blurriness": weapon_case_getter("Blurriness", 1.0),
        #     }
        #     genv.performance.SetDofParams(params)

    def EnterAdsStateByFire(self, value):
        if not value and not self.is_enter_ads_by_fire:
            return
        self.try_enter_ads = False
        self.EnterAdsState(value)
        self.is_enter_ads_by_fire = value
        self.SetIsShootBreaking(False)

    def EnterAdsHoldBreath(self, enter):
        cur_weapon = self.GetCurWeaponCase()
        if not cur_weapon:
            return
        # can_this_weapon_hold_breath = cur_weapon.GetWeaponAttrValue('can_this_weapon_hold_breath', False)
        # if not can_this_weapon_hold_breath:
        #     return
        if self.hand_model.force_use_hold_breath:
            self.hand_model.JumpToAdsHoldBreath(enter)
            return

        # 有些武器没有part_slots...
        if not hasattr(cur_weapon, 'part_slots'):
            return

        if not cur_weapon.part_slots:
            return

        # if not weapon_util.IsWeaponOpticCanHoldBreath(cur_weapon.part_slots):
        if not cur_weapon.IsWeaponHasFeature(WeaponPartFeature.CanHoldBreath):
            return

        self.hand_model.JumpToAdsHoldBreath(enter)

    def AutoEnterAdsState(self):
        if self.is_ads:
            return
        if not self.try_enter_ads:
            return
        self.EnterAdsState(True)

    def SetIsShootBreaking(self, is_breaking):
        self.is_shoot_breaking = is_breaking

    def AddDelayShootReason(self, reason, is_dual=False):
        if is_dual:
            self._delay_dual_shoot_reason |= 1 << reason
            self.dual_shoot_immediately = False
            self.dual_shoot_rescue_timer and self.cancel_timer(self.dual_shoot_rescue_timer)
        else:
            self._delay_shoot_reason |= 1 << reason
            self.shoot_immediately = False
            self.shoot_rescue_timer and self.cancel_timer(self.shoot_rescue_timer)

    def ClearDelayShootReason(self, is_dual=False):
        if is_dual:
            self.dual_shoot_immediately = True
            self._delay_dual_shoot_reason = 0
        else:
            self.shoot_immediately = True
            self._delay_shoot_reason = 0

    def HaveDelayShootReason(self, reason, is_dual=False):
        if is_dual:
            return self._delay_dual_shoot_reason & (1 << reason)
        else:
            return self._delay_shoot_reason & (1 << reason)

    def RemoveDelayShootReason(self, reason, is_dual=False):
        if is_dual:
            self._delay_dual_shoot_reason &= ~(1 << reason)
            if self._delay_dual_shoot_reason == 0:
                self.dual_shoot_immediately = True
        else:
            self._delay_shoot_reason &= ~(1 << reason)
            if self._delay_shoot_reason == 0:
                self.shoot_immediately = True

    def CheckCanGunAttack(self, spell_id):
        hand_model = self.hand_model
        if not hand_model:
            return False
        weapon_case = self.GetCurWeaponCase()
        if not weapon_case:
            return
        if not weapon_util.IsWeaponOwnGun(weapon_case.weapon_id):
            return
        if not weapon_case.is_using:
            return
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.Fire):
            return False
        if not self.CheckCostAmmo(spell_id, cost=False):
            self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_EMPTY_AMMO)
            return False
        else:
            self.RemoveDelayShootReason(cconst.DELAY_SHOOT_REASON_EMPTY_AMMO)
        if weapon_case.is_need_rechamber and self.shoot_immediately:
            # 需要拉栓 不能开枪
            self.hand_model.ActionEndToRechamber()
            return False
        if self.vehicle and not self.is_lean_out:
            return False
        if weapon_util.IsWeaponLauncher(weapon_case.weapon_id) and not self.is_real_ads:
            return False
        # if self.IsStateInRelationshipSet(cconst.StateRelationship.Reload):
        cur_time = time.time()
        fire_interval = weapon_case.GetWeaponAttrValue('FireIntervalTimeConf', 0)
        cur_fire_interval = cur_time - self.gun_fire_timestamp
        if cur_fire_interval <= fire_interval:
            self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_FIRE_INTERVAL)
            self.reload_fire_interval_timer and self.cancel_timer(self.reload_fire_interval_timer)
            self.reload_fire_interval_timer = self.add_timer(fire_interval - cur_fire_interval, Functor(self.AutoTryGunAttack, cconst.DELAY_SHOOT_REASON_FOR_FIRE_INTERVAL))
            print('========================== CheckCanGunAttack reload_fire_interval_timer', fire_interval - cur_fire_interval)
            return False
        return True

    def TryAttack_shotgun_spell(self, spell_id, spell_level, spell_code, extra=None):
        self.TryAttack_gun_spell(spell_id, spell_level, spell_code)

    def DelayShootRescueProject(self):
        self.shoot_rescue_timer and self.cancel_timer(self.shoot_rescue_timer)
        self.AutoTryGunAttack()
        # [DEBUG]
        print("触发保底延迟开火！！！！！！！！！！！！")
        # [DEBUG]

    def TryAttack_gun_spell(self, spell_id, spell_level, spell_code, extra=None):
        # self.delay_attack_timer and self.cancel_timer(self.delay_attack_timer)
        # self.delay_attack_timer = None
        if not self.CheckCanGunAttack(spell_id):
            return
        if not self.shoot_immediately:
            # # 不能立即开火 但是客户端需要切手部状态 延迟开火
            # print '========================== TryAttack_gun_spell not shoot_immediately', self.state_relationship_set
            if self.CheckStateRelationship(cconst.StateRelationship.Fire)[0] == cconst.StateRelationshipResult.Break:
                # print '====================== TryAttack_gun_spell fire break delay'
                if self.IsStateInRelationshipSet(cconst.StateRelationship.Sprint, cconst.StateRelationship.SuperSprint):
                    # print '====================== TryAttack_gun_spell sprint delay'
                    self.OnStopSprint(by_state=cconst.StateRelationship.Fire)
                    self.SetIsShootBreaking(True)
                if self.IsStateInRelationshipSet(cconst.StateRelationship.Reload):
                    self.ReloadWeaponStop()
            self.shoot_rescue_timer = self.add_timer(0.4, self.DelayShootRescueProject)
            return

        if not self.is_dual_shooting:
            self.shoot_idx = 0
            self.last_shoot_idx = -1
        self.is_shooting = True
        result = spell_util.CastSpell(self, spell_id, spell_level, self.cur_weapon_guid, extra=extra)
        camera = genv.camera
        if camera and camera.is_tfps_placer:
            camera.placer.EnablePersistRecoilRecover(False)

        return result

    def CheckCanDualGunAttack(self, spell_id):
        self.reload_dual_fire_interval_timer and self.cancel_timer(self.reload_dual_fire_interval_timer)
        self.reload_fire_interval_timer = None
        hand_model = self.hand_model
        if not hand_model:
            return False
        weapon_case = self.GetCurWeaponCase()
        if not weapon_case:
            return
        if not weapon_util.IsWeaponOwnGun(weapon_case.weapon_id):
            return
        if not weapon_case.is_using:
            return
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.DualFire):
            return False
        if not self.CheckCostDualAmmo(cost=False):
            return False
        if self.vehicle and not self.is_lean_out:
            return False
        cur_time = time.time()
        fire_interval = weapon_case.GetWeaponAttrValue('FireIntervalTimeConf', 0)
        cur_fire_interval = cur_time - self.dual_gun_fire_timestamp
        if cur_fire_interval <= fire_interval:
            self.AddDelayShootReason(cconst.DELAY_SHOOT_REASON_FOR_FIRE_INTERVAL, is_dual=True)
            self.reload_dual_fire_interval_timer = self.add_timer(fire_interval - cur_fire_interval, Functor(self.AutoTryDualGunAttack, cconst.DELAY_SHOOT_REASON_FOR_FIRE_INTERVAL))
            return False
        #  释放左手技能时，不能开枪，也可以加左手技能释放状态来管理
        # if hand_model.GetVariableI('ACTOR_FPS_IsLeftHandNotUsing') == 0:
        #     return False
        if self.IsStateInRelationshipSet(cconst.StateRelationship.Skill):
            return False
        return True

    def DelayDualShootRescueProject(self):
        self.dual_shoot_rescue_timer and self.cancel_timer(self.dual_shoot_rescue_timer)
        self.AutoTryDualGunAttack()

    def TryAttack_dual_gun_spell(self, spell_id, spell_level, spell_code, extra=None):
        if not self.CheckCanDualGunAttack(spell_id):
            return
        if not self.dual_shoot_immediately:
            # # 不能立即开火 但是客户端需要切手部状态 延迟开火
            # print '========================== TryAttack_gun_spell not shoot_immediately', self.state_relationship_set
            if self.CheckStateRelationship(cconst.StateRelationship.DualFire)[0] == cconst.StateRelationshipResult.Break:
                # print '====================== TryAttack_gun_spell fire break delay'
                if self.IsStateInRelationshipSet(cconst.StateRelationship.Sprint, cconst.StateRelationship.SuperSprint):
                    # print '====================== TryAttack_gun_spell sprint delay'
                    self.OnStopSprint(by_state=cconst.StateRelationship.DualFire)
                    self.SetIsShootBreaking(True)
                if self.IsStateInRelationshipSet(cconst.StateRelationship.DualReload):
                    self.ReloadDualWeaponStop()
            self.dual_shoot_rescue_timer = self.add_timer(0.4, self.DelayDualShootRescueProject)
            return

        if not self.is_shooting:
            self.shoot_idx = 0
            self.last_shoot_idx = -1
        self.is_dual_shooting = True

        result = spell_util.CastSpell(self, spell_id, spell_level, self.cur_weapon_guid, extra=extra)

        camera = genv.camera
        if camera and camera.is_tfps_placer:
            camera.placer.EnablePersistRecoilRecover(False)

        return result

    def TryAttack_launcher_spell(self, spell_id, spell_level, spell_code, extra=None):
        self.TryAttack_gun_spell(spell_id, spell_level, spell_code)

    def TryAttack_bow_spell(self, spell_id, spell_level, spell_code, extra=None):
        self.TryAttack_gun_spell(spell_id, spell_level, spell_code)

    def CheckCanItemAttack(self, spell_id, spell_code):
        # if spell_code != 'inject_spell' and self.combat_state != CombatState.ALIVE:
        #   # 自救针
        #   return False
        # if spell_code not in ('bomb_spell', 'inject_spell') and self.CheckStateRelationship(cconst.StateRelationship.Item) == cconst.StateRelationshipResult.Cancel:
        #   return False
        return True

    def TryAttack_item_spell(self, item_guid, spell_id, spell_level, spell_code, extra=None):
        if not self.CheckCanItemAttack(spell_id, spell_code):
            return
        return spell_util.CastSpell(self, spell_id, spell_level, item_guid, extra=extra)

    def TryAttack_flash_spell(self, spell_id, spell_level, spell_code, extra=None):
        # 开镜闪光
        return spell_util.CastSpell(self, spell_id, spell_level, extra=extra)

    def CheckCanMeleeAttack(self, spell_id):
        hand_model = self.hand_model
        if not hand_model:
            return False
        weapon_case = self.GetCurWeaponCase()
        if not weapon_case:
            return
        if not weapon_util.IsWeaponOwnMelee(weapon_case.weapon_id):
            return
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.MeleeAttack):
            return False
        if self.vehicle:
            return False
        return True

    def TryAttack_melee_spell(self, spell_id, spell_level, spell_code, extra=None):
        if not self.CheckCanMeleeAttack(spell_id):
            return
        return spell_util.CastSpell(self, spell_id, spell_level, extra=extra)

    def CheckCanSamuraiMeleeAttack(self, spell_id):
        hand_model = self.hand_model
        if not hand_model:
            return False
        weapon_case = self.GetCurWeaponCase()
        if not weapon_case:
            return
        if not weapon_case.IsSamuraiSword:
            return False
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.MeleeAttack):
            return False
        if self.vehicle:
            return False
        return True

    def CheckCanSamuraiSprintAttack(self, spell_id):
        hand_model = self.hand_model
        if not hand_model:
            return False
        weapon_case = self.GetCurWeaponCase()
        if not weapon_case:
            return
        if not weapon_case.IsSamuraiSword:
            return False
        if self.vehicle:
            return False
        return True

    def TryAttack_samurai_spell(self, spell_id, spell_level, spell_code, extra=None):
        checkReuslt = self.CheckCanSamuraiMeleeAttack(spell_id)
        if extra and extra.get('IsSamuraiSprint', False):
            checkReuslt = self.CheckCanSamuraiSprintAttack(spell_id)
        if not checkReuslt:
            return
        return spell_util.CastSpell(self, spell_id, spell_level, extra=extra)

    def TryAttack_ads_shield_spell(self, spell_id, spell_level, spell_code, extra=None):
        pass

    def TryAttack(self, spell_id=None, spell_level=1, item_guid='', extra=None):
        if not self.is_alive:
            return
        if not self.space:
            return

        if spell_id is None:
            spell_id = self.main_spell

        code = spell_util.GetSpellCode(spell_id)
        if not code:
            return

        attack_func = getattr(self, 'TryAttack_%s' % code, None)
        if not attack_func and item_guid:
            attack_func = partial(self.TryAttack_item_spell, item_guid)
        if attack_func:
            result = attack_func(spell_id, spell_level, code, extra)
        else:
            result = None

        genv.messenger.Broadcast(events.ON_PLAYER_TRY_ATTACK, spell_id)
        return result

    def AutoTryGunAttack(self, delay_shoot_reason=None):
        if not self.shoot_immediately:
            if delay_shoot_reason:
                self.RemoveDelayShootReason(delay_shoot_reason)
            else:
                self.ClearDelayShootReason()
            if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.Fire):
                # # [DEBUG]
                # print("延迟开火失败：当前状态禁止进入开火 !!!!!!!!!!!!!!!!!!!!!!!!!!", self.state_relationship_set)
                # # [DEBUG]
                return
            if self.IsStateInRelationshipSet(cconst.StateRelationship.Emote):
                return
            weapon_case = self.GetCurWeaponCase()
            if not weapon_case or not weapon_case.is_gun:
                return
            # 单发模式下，如果是因为发射间隔导致的延迟开火，则不需要按下鼠标左键
            if self.try_fire_start or delay_shoot_reason == cconst.DELAY_SHOOT_REASON_FOR_FIRE_INTERVAL and \
               weapon_case.is_gun and weapon_case.GetCurrentFireModeSelector() == FireModeSelector.FIRE_SEMI:
                if weapon_util.IsGunSniper(weapon_case.gun_id)\
                        and not weapon_util.IsWeaponCompoundBow(weapon_case.weapon_id):
                    # 一键开镜自动开枪模式下的狙击枪 不能自动开枪
                    if self.CheckFireMode() in (FireMode.FIRE_ADS, FireMode.FIRE_REAL_ADS):
                        self.EnterAdsStateByFire(True)
                        return
                print('================================== AutoTryGunAttack', delay_shoot_reason, self.shoot_immediately)
                self.shoot_rescue_timer and self.cancel_timer(self.shoot_rescue_timer)
                self.OnPlayerFireBegin()
        if self.compensate_timer:
            self.cancel_timer(self.compensate_timer)
            # 只有在鼠标抬起的状态下才会继续终止开火
            if not genv.input_ctrl.IsMouseLDown():
                self.DoPlayerFireEnd()
        # # [DEBUG]
        # else:
        #     print("延迟开火失败：shoot_immediately = True !!!!!!!!!!!!!!!!!!!!!!!!!!")
        # # [DEBUG]

    def AutoTryDualGunAttack(self, delay_shoot_reason=None):
        if not self.dual_shoot_immediately:
            if delay_shoot_reason:
                self.RemoveDelayShootReason(delay_shoot_reason, is_dual=True)
            else:
                self.ClearDelayShootReason(is_dual=True)
            if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.DualFire):
                return
            if self.IsStateInRelationshipSet(cconst.StateRelationship.Emote):
                return
            if self.try_dual_fire_start:
                cur_weapon = self.GetCurWeapon()
                if not cur_weapon or not cur_weapon.is_dual_weapon:
                    return
                print('================================== AutoTryDualGunAttack', delay_shoot_reason, self.dual_shoot_immediately)
                self.OnPlayerDualFireBegin()

    def EndAttack(self, spell_id=None):
        spell_id = spell_id or self.main_spell
        code = spell_util.GetSpellCode(spell_id)
        if not code:
            return
        spell_util.StopSpell(self, spell_id)
        endAttack_func = getattr(self, 'EndAttack_%s' % code, None)
        if not endAttack_func:
            endAttack_func = getattr(self, 'EndAttack_item_spell', None)
        if endAttack_func:
            result = endAttack_func(spell_id, code)
        else:
            result = None

        # sjh @ 结束攻击后尝试恢复sprint

        # 如果还在cd中，则等待cd结束恢复sprint,不然直接尝试恢复sprint
        if self.fire_block_sprint_timer:
            # print('change time func !!!!!!!!!!!!!!!!!!', self._timers[self.fire_block_sprint_timer])
            self._timers[self.fire_block_sprint_timer] = self._try_recover_sprint
        else:
            self.RefreshSpeedAndMotion()
        return result

    def _try_recover_sprint(self):
        # print('try recover sprint!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!')
        self.fire_block_sprint_timer = None
        self.is_sprint = True
        self.RefreshSpeedAndMotion()

    def EndAttack_bandage_spell(self, spell_id, code):
        if not LocalConfig.continuous_use_medicine:
            return
        if self.break_use_medicine:
            return
        item_id = 46  # hardcode TODO
        self.CallServer('UseNextMedicine', item_id)

    def EndAttack_medikit_spell(self, spell_id, code):
        if not LocalConfig.continuous_use_medicine:
            return
        if self.break_use_medicine:
            return
        item_id = 47
        self.CallServer('UseNextMedicine', item_id)

    def CheckFireModeSelector(self, spell_id):
        weapon_case = self.GetCurWeaponCase()
        current_selector = weapon_case.GetCurrentFireModeSelector()
        if current_selector == FireModeSelector.FIRE_AUTOMATIC:
            return False
        elif current_selector == FireModeSelector.FIRE_SEMI:
            if self.shoot_idx >= 0:
                self.EndAttack(spell_id)
        elif current_selector == FireModeSelector.FIRE_BURST_TRIBLE:
            if self.shoot_idx == 0:
                self.last_shoot_idx = self.shoot_idx
                self.shoot_idx += 1
                if weapon_case:
                    fire_interval = weapon_case.GetWeaponAttrValue('FireIntervalTimeConf', 0.05)
                    self.add_timer(fire_interval, functools.partial(self.AutoTryAttack, spell_id, fire_interval, 2))
            return True
        return False

    def AutoTryAttack(self, spell_id, fire_interval, remains):
        spell_util.OnSpellStrike(self, spell_id)
        self.last_shoot_idx = self.shoot_idx
        self.shoot_idx += 1
        if remains > 0:
            self.add_timer(fire_interval, functools.partial(self.AutoTryAttack, spell_id, fire_interval, remains - 1))
        else:
            # self.add_timer(fire_interval, functools.partial(self.EndAttack, spell_id))
            self.EndAttack(spell_id)

    @rpc_method(CLIENT_STUB, EntityID())
    def OnUseNextMedicine(self, guid):
        if not LocalConfig.continuous_use_medicine or self.break_use_medicine:
            return
        cur_weapon = self.backpack.get(self.cur_spec_weapon_guid)
        if cur_weapon and cur_weapon.equip_type == consts.EquipmentType.MEDICINE:
            # 正在使用药品
            return
        item = self.backpack.get(guid)
        if not item:
            return
        self.UseCombatItem(guid, cconst.UseItemMode.ITEM_DOWN)

    def EndAttack_gun_spell(self, spell_id, spell_code):
        # self.PlayGunSoundEventById(198)
        self.SetIsShootBreaking(False)
        # self.delay_attack_timer and self.cancel_timer(self.delay_attack_timer)
        # self.delay_attack_timer = None
        if not self.is_shooting:
            # 本身不在开火状态
            return
        camera = genv.camera
        if camera and camera.is_tfps_placer and self.shoot_idx > 1:
            camera.placer.EnablePersistRecoilRecover(True)
        if not self.is_dual_shooting:
            self.shoot_idx = 0
        self.is_shooting = False
        self.model and self.model.UpdateMoveRotationMode()
        self.RefreshSpeedAndMotion(force_refresh=True)
        self.UpdateCombatStatusTimestamp(cconst.CombatStatusEnum.Fire)

    def EndAttack_dual_gun_spell(self, spell_id, spell_code):
        if not self.is_shooting:
            self.shoot_idx = 0
        self.is_dual_shooting = False
        self.RefreshSpeedAndMotion(force_refresh=True)
        self.UpdateCombatStatusTimestamp(cconst.CombatStatusEnum.Fire)

    def EndAttack_shotgun_spell(self, spell_id, spell_code):
        self.EndAttack_gun_spell(spell_id, spell_code)

    def EndAttack_launcher_spell(self, spell_id, spell_code):
        self.PlayGunSoundEventById(198)
        self.SetIsShootBreaking(False)
        # self.EnterAdsState(False, is_force_out=True)
        camera = genv.camera
        if camera and camera.is_tfps_placer and self.shoot_idx > 1:
            camera.placer.EnablePersistRecoilRecover(True)
        self.shoot_idx = 0
        self.is_shooting = False
        self.model and self.model.UpdateMoveRotationMode()
        self.RefreshSpeedAndMotion(force_refresh=True)

    def EndAttack_bow_spell(self, spell_id, spell_code):
        self.EndAttack_gun_spell(spell_id, spell_code)

    def TryAttack_fall_hurt_spell(self, spell_id, spell_level, code, extra=None):
        if self.force_no_fall_hurt or not self.model or not self.model.fall_start_height:
            # 有些情况下不受伤害
            return
        fall_diff_height = self.model.fall_start_height - self.position[1]
        if fall_diff_height <= consts.FALL_HURT_START_HEIGHT:
            return
        game_logic = self.game_logic
        if not game_logic:
            return
        damage_ratio = game_logic.CalFallHurtDamage(fall_diff_height)
        damage = self.maxhp * damage_ratio
        spell_result = SpellResult.CreateSimple(self, consts.FALL_HURT_SPELL_ID)
        spell_result.damage_result[self.id] = {'damage': damage}
        genv.spell_core.SendSpellResult(spell_result)
        # [DEBUG]
        print('DEBUG: FALL HURT, from height %s, dest height %s, diff %s, damage %s' % (self.model.fall_start_height, self.position[1], fall_diff_height, damage))
        # [DEBUG]

    def TryAttack_stealth_spell(self, spell_id, spell_level, code, extra=None):
        return spell_util.CastSpell(self, spell_id, spell_level, extra=extra)

    @rpc_method(CLIENT_STUB, Bool())
    def OnOpenMovingShield(self, is_open):
        if is_open:
            genv.camera.ApplyCamera(cconst.CAMERA_ID_ALS_TPS)
            MobaHudShieldRemainComp.instance().Show(info={'spell': self.spell_mgr.get(37)})
        else:
            MobaHudShieldRemainComp.isInited() and MobaHudShieldRemainComp.instance().Close()
            self.combat_state == 0 and self.is_fps_mode and genv.camera.RestoreCamera()
            if self.cur_spec_weapon_guid:
                cur_item = self.GetWeaponByGuid(self.cur_spec_weapon_guid)
                self.UseCombatItem(cur_item.guid, cconst.UseItemMode.CANCEL_DOWN)

    @rpc_method(CLIENT_STUB, Int(), Int(), EntityID())
    def ClientSpellStop(self, spell_id, level, item_guid):
        spell_util.StopSpell(self, spell_id)

    @events.ListenTo(events.ON_CURSOR_VISIBLE)
    def OnCursorVisibleChangedForSpell(self, visible):
        if visible and self.try_fire_start:
            self.OnPlayerFireEnd()

    def PlayMeleeSkill(self, skill_type=0, info={}):
        self.EnterAdsState(False, is_force_out=True)
        self.hand_model and self.hand_model.PlayMeleeSkill(skill_type, info)
        self.model and self.model.PlayMeleeSkill(skill_type, info)

    def _on_set_ban_skill(self, old):
        genv.messenger.Broadcast(events.ON_BAN_SKILL, self.ban_skill)
        if self.ban_skill:
            self.OnBanSkill()

    def _on_set_info_chaos(self, old):
        # genv.messenger.Broadcast(events.ON_SETTING_HIDE_TEAMMATE_NAME_CHANGED, self.info_chaos)
        genv.messenger.Broadcast(events.ON_INFO_CHAOS, self.info_chaos)

    @events.ListenTo(events.ON_PLAYER_PARA_STAGE_CHANGE)
    def OnPlayerParaStageChangedForBreakMedicine(self, para_stage):
        if self.is_on_sky:
            self.OnBreakUseMedicine()

    def UpdateMotionBlurParams(self):
        print("Motion blur is now in graphics options and not adjusted per weapon basis, the function in cimp_spell.py"
              "UpdateMotionBlurParams() will be ignored.")
        # weapon_case = self.GetCurWeaponCase()
        # if not weapon_case:
        #     return
        # weapon_getattr = weapon_case.GetWeaponAttrValue
        # enable_motion_blur = weapon_getattr('EnableHMotionBlur', False)
        # genv.performance.EnableMotionBlur(enable_motion_blur)
        # # sunshine模式下每次都设置下参数
        # if genv.sunshine and enable_motion_blur:
        #     params = {
        #         "HMotionBlurLevel": weapon_getattr('HMotionBlurLevel', 3),
        #         "ExposeTime": weapon_getattr('ExposeTime', 0.1),
        #         "BlurThreshold": weapon_getattr('BlurThreshold', 10),
        #         "MaxBlurPixels": weapon_getattr('MaxBlurPixels', 16),
        #     }

    @events.ListenTo(events.ON_LOGIN_WITH_RELAY_MAIN)
    def OnLoginWithRelayMain(self, is_relay):
        self.PlayGunSoundEventById(198)

    @events.ListenTo(events.ON_PLAYER_COMBAT_STATE_CHANGE)
    def OnPlayerCombatStateChangeForSpell(self):
        self.PlayGunSoundEventById(198)

    def StartSpikeTransfer(self):
        if not self.holographic_robot_id:
            return
        if not self.CheckCanEnterStateRelationship(cconst.StateRelationship.SpikeTransfer):
            return
        self.model.JumpToState(cconst.UNIT_STATE_SPIKE_TRANSFER)
        self.hand_model.JumpToState(cconst.UNIT_STATE_SPIKE_TRANSFER)
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.SpikeTransfer, True)
        return True

    def OnBreakSpikeTransferByState(self, state=None):
        self.model.ExitSpikeTransferState()
        self.hand_model.ExitSpikeTransferState()
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.SpikeTransfer, False)

    def DoSpikeTransfer(self):
        # self.OnBreakSpikeTransferByState()
        if not self.holographic_robot_id:
            return
        skill_item = self.backpack.GetBySlot(consts.BackpackSlot.SKILL_WEAPON)
        if not skill_item:
            return
        spell_id = skill_item.spell_id
        self.CallServer('CallSpellLogic', spell_id, 'Transfer', [])
        return True

    def OnPlayerSamuraiSwordBlockStart(self):
        weapon_case = self.GetCurWeaponCase()
        if not weapon_case.CheckCanUseSamuraiBlock():
            return False
        weapon_case.OnRaiseWeapon_samurai_block_spell()
        return True

    def OnPlayerSamuraiSwordBlockEnd(self):
        weapon_case = self.GetCurWeaponCase()
        if not weapon_case.IsSamuraiSword:
            return
        if not weapon_case.using_spell_mode == 2:
            # 如果不是block技能就不要弹出
            return
        weapon_case.OnDropWeapon_samurai_block_spell()
        # weapon_case.OnRaiseWeapon_samurai_spell(False)
        return True

    def OnPlayerSamuraiSwordSprintDown(self):
        weapon_case = self.GetCurWeaponCase()
        if not weapon_case.CheckCanUseSamuraiSprint():
            return False
        weapon_case.OnRaiseWeapon_samurai_sprint_spell()
        return True

    def OnPlayerSamuraiSwordSprintUp(self):
        weapon_case = self.GetCurWeaponCase()
        if not weapon_case.IsSamuraiSword:
            return
        weapon_case.OnUseWeapon_samurai_sprint_spell()
        return True

    def CalDecreaseHeat(self, decrease, current_time, heat_data):
        current_heat = heat_data.get('heat', 0)
        if current_heat == 0:
            heat_data['heat'] = 0
        # 计算上次停火到当前时间时散出的热量
        last_stop_time = heat_data.get('stop_time', 0)
        passed_time = current_time - last_stop_time if last_stop_time > 0 else 0
        decrease_heat = decrease * float(passed_time)
        # 计算出保留下来的剩余热量。
        heat_data['heat'] = (current_heat - decrease_heat) if (current_heat - decrease_heat) > 0 else 0
        if heat_data['heat'] > 100:
            heat_data['heat'] = 100
        return heat_data

    def SpellStart(self, weapon_case):
        if not weapon_case or not weapon_case.gun_id:
            return
        self.spell_start = True
        # 停止之前的特效
        self.CancelSmokeTimer()
        # 开火计算热量流失
        gun_id = weapon_case.gun_id
        decrease = weapon_data.data.get(gun_id, {}).get('overheat_decrease_multiplier', 1)
        if self.heat_data.get(gun_id, None) is None:
            self.heat_data[gun_id] = {}
        current_time = time.time()
        self.heat_data[gun_id]['start_time'] = current_time
        self.CalDecreaseHeat(decrease, current_time, self.heat_data[gun_id])

    def SpellStop(self, weapon_case):
        gun_id = weapon_case.gun_id
        if self.heat_data.get(gun_id, None) is None:
            return
        shoot_idx = self.shoot_idx if self.shoot_idx else 1
        # 停火计算热量增加
        capacity = weapon_case.GetWeaponAttrValue('stock_capacity')
        heat = float(shoot_idx) / float(capacity) * 100

        current_time = time.time()
        self.heat_data[gun_id]['stop_time'] = current_time
        self.heat_data[gun_id]['heat'] += heat

    def PlaySmokeEffect(self, model, sfx_id, weapon_case, effect_point):
        gun_id = weapon_case.gun_id
        heat_data = self.heat_data.get(gun_id, None)
        if not heat_data:
            return
        # 下次开火才能再播特效。不能单纯依赖停火
        if not self.spell_start:
            return
        self.spell_start = False
        # 播放前立刻停掉正在播放的特效
        self.StopSmokeEffect(model, True)
        self.smoke_tick_timer and self.cancel_timer(self.smoke_tick_timer)
        self.smoke_tick_timer = self.add_repeat_timer(0.3, functools.partial(self.SmokeTickTimerCheck, model, sfx_id, gun_id, effect_point))

    def CancelSmokeTimer(self):
        self.smoke_tick_timer and self.cancel_timer(self.smoke_tick_timer)
        self.smoke_tick_timer = None

    def SmokeFullTimerCheck(self):
        self.CancelSmokeTimer()

    def SmokeTickTimerCheck(self, model, sfx_id, gun_id, effect_point):
        if self.is_destroyed():
            self.CancelSmokeTimer()
            return
        heat_data = self.heat_data.get(gun_id, None)
        if not heat_data:
            self.CancelSmokeTimer()
            return
        if not model:
            self.CancelSmokeTimer()
            return
        if not hasattr(model, 'effect_dict'):
            self.CancelSmokeTimer()
            return

        # 重新计算热量
        decrease = weapon_data.data.get(gun_id, {}).get('overheat_decrease_multiplier', 1)
        current_time = time.time()
        self.CalDecreaseHeat(decrease, current_time, heat_data)
        heat = heat_data.get('heat', 0)
        if heat <= 0:
            self.CancelSmokeTimer()
            self.StopSmokeEffect(model, True)
            return
        heat_rate = heat / 100
        if not self.playing_smoke or not self.playing_smoke.IsValid():
            # 创建特效
            skeleton = model.GetSkeleton()
            self.smoke_eid = model.PlayEffectById(sfx_id, -1, insure_play=True, bone=effect_point)
            ents = skeleton.GetEffectEntities(self.smoke_eid)
            for particle in ents[0].Primitives:
                particle.DeferredTick = True
                particle.MiscFlags = 4  # 无限包围盒
            for single_effect_entity in ents:
                for p in single_effect_entity.Primitives:
                    p.NotClipInHUDSight = True
            self.playing_smoke = ents[0].Primitives[0]
            self.playing_smoke.InstanceColor = MType.Vector4(1, 1, 1, heat_rate)
        else:
            self.playing_smoke.InstanceColor = MType.Vector4(1, 1, 1, heat_rate)
        self.last_pos = self.position

    def StopSmokeEffect(self, model, immediately=False):
        if self.playing_smoke:
            self.playing_smoke.IsActive and self.playing_smoke.Deactivate(False)
            self.playing_smoke = None
        if immediately:
            if self.smoke_eid:
                model.ClearSkeletonEffectImmediately(self.smoke_eid)
                self.smoke_eid = None


class MonsterSpell(CombatAvatarMember):

    def OnSpellResultImpl(self, result):
        self.OnDealDamageResult(result)
