# -*- coding:utf-8 -*-

import MObject
import MEngine
import MPhysics
import MType
import random
from common.IdManager import IdManager
from client.ClientEntity import ClientAreaEntity
from gshare.decorators import cached_property
from gclient import cconst
from gshare.decorators import SetClsDefaultFaction, with_tag
from gclient.framework.util.resource_loader import ResourceLoader
from gclient.data import world_blast_data, StrikeItem_data


@with_tag('IsStrikeItem')
@SetClsDefaultFaction(-1)
class StrikeItem(ClientAreaEntity):

    def init_from_dict(self, bdict):
        super(StrikeItem, self).init_from_dict(bdict)
        self.strike_info = StrikeItem_data.data.get(bdict.get('res_id', ''), {})
        info_getter = self.strike_info.get
        self.shape_path = info_getter('physics_path', None)
        self.penetrate_val = info_getter('penetrate_val', -1)
        self.physics_material_id = info_getter('physics_material_id', 3)
        self.density = info_getter('density', 1000)
        ientity = bdict.get('exist_ientity', None)
        self.ientity = self.InitIEntity(ientity) if ientity else self.CreateIEntity(bdict)

    def destroy(self):
        ientity = self.ientity
        if ientity and ientity.IsValid():
            for rb in ientity.RigidBodies:
                if rb:
                    rb.owner = None
                    rb.LeaveSpace()
        self.ientity = None
        super(StrikeItem, self).destroy()

    def InitIEntity(self, ientity):
        # 走场景绑定进来的ientity
        ientity.IsCastDynamicShadow = True
        if ientity.RigidBodies:
            self.OnIEntityResourceReady(ientity)
        else:
            ientity.BindEvent("ResourceReady", self.OnIEntityResourceReady)
        ientity.BindEvent("ResourceUpdated", self.OnIEntityResourceReady)
        ientity.Primitives[0].ReceiveDecals = False
        return ientity

    def CreateIEntity(self, unit_info):
        ientity = MObject.CreateObject("IEntity")
        ientity.IsMovable = False
        ientity.IsAnimated = True
        ientity.IsCastDynamicShadow = True
        ientity.SetName('StrikeItem')

        transform = unit_info['transform']
        transform.scale = unit_info['scale']
        model_path = unit_info['model_path']
        ResourceLoader.LoadResource(ientity, (model_path,))
        model = ientity.Primitives[0]
        model.HasPhysics = False

        shape_path = unit_info['rb_path']
        pmw = MObject.CreateObject('PhysicsMaterialWrapper')
        pmw.SetRestitution(0.1)
        pmw.SetStaticFriction(1.0)
        pmw.SetDynamicFriction(1.0)
        pmw.SetFrictionCombineType(MPhysics.EPhysicsMaterialCombineType.MinValue)
        pmw.SetRestitutionCombineType(MPhysics.EPhysicsMaterialCombineType.MinValue)
        if not pmw.CreateResource():
            return
        p_rb_template_wrapper = MObject.CreateObject('PhysicsRBTemplateWrapper')
        physic_shape = MObject.CreateObject('PhysicsShapeWrapper')
        physic_shape.SetShapeFromResPath(shape_path)
        p_rb_template_wrapper.SetToRuntimeRBTemplate([physic_shape], [pmw])

        # 设置刚体的CollisionFilterInfo
        p_rb_template_wrapper.SetCollisionFilterInfo(cconst.PHYSICS_DYNAMIC_BREAK)
        # 设置刚体的线性速度衰减系数
        p_rb_template_wrapper.SetLinearDamping(0.9)
        # 设置刚体的角速度衰减系数`
        p_rb_template_wrapper.SetAngularDamping(0.9)
        # 设置刚体的最大线性速度
        p_rb_template_wrapper.SetMaxLinearSpeed(40)
        # 设置刚体是否使用连续模拟
        p_rb_template_wrapper.SetEnableCCD(True)
        # 设置刚体的运动类型
        p_rb_template_wrapper.SetMotionType(MPhysics.EPhysicsMotionType.Dynamic)
        p_rb_template_wrapper.SetDensity(self.density)
        if not p_rb_template_wrapper.CreateResource():
            return
        p_rb = MObject.CreateObject('PhysicsDynamicBody')
        p_rb.SetRBByWrapper(p_rb_template_wrapper)

        # Transform
        ientity.RigidBodies = [p_rb, ]
        ientity.Transform = transform
        ientity.EnterArea(MEngine.GetGameplay().Scenario.ActiveWorld.DefaultLevel.RootArea)
        p_rb.owner = self
        scale = transform.scale
        p_rb.Mass = 500.0 * scale.x * scale.y * scale.z
        p_rb.MOI = MType.Vector3(0.4, 0.4, 0.4) * (scale.x * scale.z * p_rb.Mass)

        p_rb.EnableContactNotify = True
        p_rb.BindEvent('Contacted', lambda contact_info: self.ContactCallback(contact_info))

        return ientity

    def OnIEntityResourceReady(self, ientity):
        if self.is_destroyed():
            return
        rigid_bodies = ientity.RigidBodies
        if rigid_bodies:
            for rb in rigid_bodies:
                rb.LeaveSpace()

        if self.shape_path:
            pmw = MObject.CreateObject('PhysicsMaterialWrapper')
            pmw.SetTypeID(self.physics_material_id)
            pmw.SetRestitution(1.0)
            pmw.SetStaticFriction(0.5)
            pmw.SetDynamicFriction(0.5)
            pmw.SetFrictionCombineType(MPhysics.EPhysicsMaterialCombineType.MinValue)
            pmw.SetRestitutionCombineType(MPhysics.EPhysicsMaterialCombineType.MinValue)
            if not pmw.CreateResource():
                return
            physic_shape = MObject.CreateObject('PhysicsShapeWrapper')
            physic_shape.SetShapeFromResPath(self.shape_path)
            p_rb_template_wrapper = MObject.CreateObject('PhysicsRBTemplateWrapper')
            p_rb_template_wrapper.SetToRuntimeRBTemplate([physic_shape], [pmw])
            # 设置刚体的CollisionFilterInfo
            p_rb_template_wrapper.SetCollisionFilterInfo(cconst.PHYSICS_DYNAMIC_BREAK)
            # 设置刚体的线性速度衰减系数
            p_rb_template_wrapper.SetLinearDamping(0.4)
            # 设置刚体的角速度衰减系数`
            p_rb_template_wrapper.SetAngularDamping(0.4)
            # 设置刚体的最大线性速度
            p_rb_template_wrapper.SetMaxLinearSpeed(40)
            # 设置刚体是否使用连续模拟
            p_rb_template_wrapper.SetEnableCCD(True)
            # 设置刚体的运动类型
            p_rb_template_wrapper.SetMotionType(MPhysics.EPhysicsMotionType.Dynamic)
            # 新加的接口，设置密度，自动计算质量，质心，惯性张量
            p_rb_template_wrapper.SetDensity(self.density)
            if not p_rb_template_wrapper.CreateResource():
                return
            p_rb = MObject.CreateObject('PhysicsDynamicBody')
            p_rb.SetRBByWrapper(p_rb_template_wrapper)
            p_rb.owner = self
            p_rb.penetrate_val = self.penetrate_val
            ientity.RigidBodies = [p_rb, ]
            genv.sss = ientity
            # p_rb.EnableContactNotify = True
            # p_rb.BindEvent('Contacted', lambda contact_info: self.ContactCallback(contact_info))

    def ContactCallback(self, contact_info):
        entity = contact_info.BodyB if contact_info.BodyA.Entity == self.ientity else contact_info.BodyA
        ComponentType = entity.ComponentType
        if ComponentType == "VehicleComponent":
            vehicle = entity.owner
            vehicle.cct.OnHit(entity, contact_info.Pos)
        else:
            print("StrkeItem Contact Info: ", contact_info, ComponentType)

    def AddForce(self, force_vector, force_pos):
        entity = self.ientity
        if not entity:
            return
        # ApplyForceInWorldFrame(force_vector, MPhysics.EForceMode.Impulse) 是直接施加在质心，不会改变角速度
        entity.RigidBodies[0].ApplyForceAtPosInWorldFrame(force_vector, force_pos, MPhysics.EForceMode.Impulse)
        torque_vector = MType.Vector3(
            random.uniform(5, 10),
            random.uniform(5, 10),
            random.uniform(5, 10)
        )
        entity.RigidBodies[0].ApplyTorqueInWorldFrame(torque_vector, MPhysics.EForceMode.Impulse)

    def AddTorque(self, torque_vector):
        entity = self.ientity
        if not entity:
            return
        entity.RigidBodies[0].ApplyTorqueInWorldFrame(torque_vector, MPhysics.EForceMode.Impulse)


class StrikeItemManager(object):
    def __init__(self, space):
        self.space = space
        self.name_set = set()
        self.InitStrikeItems()

    def Destroy(self):
        self.space = None

    @cached_property
    def config_data(self):
        res = {}
        wbd = world_blast_data.data.get(self.space.world_name, {}).get('StrikeItem', {})
        for level_name, level_info in wbd.items():
            for res_name, val in level_info.items():
                blast_id, trans, index = val['info']
                res[res_name] = (blast_id, level_name)
        return res

    def InitStrikeItems(self):
        ientity_getter, register_func = self.space.tag_ientities.get, self.space.RegisterScriptNotifierFunc
        for name, (_, level) in self.config_data.items():
            ientity = ientity_getter((level, name), None)
            if ientity:
                self.DoInitStrikeItem(ientity)
            register_func(level, name, self.OnIEntityEnterOrLeave)

    def OnIEntityEnterOrLeave(self, is_enter, ientity):
        if is_enter:
            self.DoInitStrikeItem(ientity)
        else:
            self.DoDestroyStrikeItem(ientity)

    def DoInitStrikeItem(self, ientity):
        ientity_name = ientity.GetName()
        if ientity_name in self.name_set:
            return
        # 这个阶段创建entity会被cache,所以不会有返回值
        if info := self.config_data.get(ientity_name, None):
            self.space.create_entity("StrikeItem", IdManager.genid(), {
                'exist_ientity': ientity,
                'transform': ientity.Transform,
                'position': ientity.Transform.translation.tuple(),
                'res_id': info[0],
            })
            self.name_set.add(ientity_name)

    def DoDestroyStrikeItem(self, ientity):
        pass

