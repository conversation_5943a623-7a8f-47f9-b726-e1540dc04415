# -*- coding:utf-8 -*-

import functools
import math
import M<PERSON>ype
import time
import <PERSON>hysics
import MObject
import random
import switches
import MEngine
import six2.moves.cPickle as cPickle
import <PERSON><PERSON><PERSON>
from gclient import cconst
from common.classutils import Components
from gclient.cconst import ModelPoseType
from gclient.config import LocalConfig
from gclient.cconst import StateRelationship
from gclient.gameplay.logic_base.comps.comp_spike_transfer import SpikeTransferPlayerAvatarModelComp, \
    SpikeTransferAvatarModelComp
from gclient.gameplay.logic_base.comps.comp_strop import <PERSON>ropModelComp
from gshare.game_logic.igame_logic_parachute_comp import ParaAvatarStage
from gclient.data import gun_attachments_data, match_client_data, moving_speed_data, ghost_energy_data, tps_gun_anim_data, actor_variables_data, \
    lobby_item_data, item_feature_data, gun_skin_template_data, hero_execution_data
from gclient.framework.util import <PERSON><PERSON><PERSON><PERSON>, events
from gclient.framework.util.resource_loader import Resource<PERSON>oader
from gclient.framework.util.story_tick import StoryTick
from gclient.gameplay.logic_base.comps.model_hero_attach_comp import ModelHeroAttachComp
from gclient.gameplay.logic_base.equips.equip_case import EquipCaseFactory
from gclient.gameplay.logic_base.models.avatar_model_cue import PlayerAvatarModelCue, AvatarModelCue
from gclient.framework.models.simple_model import RegisterModelClass, SimpleModel
from gclient.gameplay.logic_base.models.model_material_comp import ModelMaterialChangeComp
from gclient.gameplay.logic_base.comps import comp_ladder
from gclient.framework.models.posesender_model import PoseSenderModel
from gclient.util.material_rank_component import MaterialRankComponent
from gshare import formula, consts, weapon_util, effect_util, audio_const
from gclient.framework.ui import ui_define
from gshare.async_util import Async
from gshare.consts import ModelFilterReason, ModelCharCtrlReason, CombatState
from gclient.gameplay.logic_base.models.model_stealth_comp import ModelStealthComp
from gclient.gameplay.logic_base.models.para_model_comp import ParachuteUnitModelComp, ParachuteGraphModel, \
    ParachuteModelComp, ParachuteControllerComp
from gclient.gameplay.logic_base.models import anim_const
from gshare.utils import Functor
from gclient.gameplay.logic_base.comps.comp_emote import EmoteAvatarModelComp, EmotePlayerAvatarModelComp
from gclient.gameplay.logic_base.comps.comp_beard import ModelBeardComp
from gclient.gameplay.logic_base.models.model_attch_comp import ModelAttachComp
from gclient.gameplay.logic_base.spell import spell_util
from gclient.data import game_const_data, hero_data
from gshare.icombat_attr import AttrType
from gclient.gameplay.logic_base.models.input_processor import PCInputBufferProcessor
from gclient.gameplay.logic_base.comps.comp_join_skeleton import JoinSkeletonComp
from gclient.gameplay.logic_base.comps.comp_model_cloth import CombatModelClothComp
from gclient.gameplay.logic_base.models.model_feature_lod_comp import ModelFeatureLODComponent
from gclient.gameplay.util import replay_util
CHARCTRL_POSE_HEIGHT = {0: 0.8, 1: 0.3, 2: 0.8, 3: 0.3}
QUAT_PI = 0.78
ONETHIRD_PI = 1.046

# 对应LocalConfig.game_fps_limit
FPS_LIMITS = {
    1: 30.0,
    2: 45.0,
    3: 60.0,
}
# 死亡动画 0：普通，1：夸张，2：随机选择0和1
DEATH_POSE_TYPE = 2

SHOW_SLIDE_JUMP_DEBUG = False

TPS_ACTION_AVAILABLE_WEAPON_IDS = [1, 15]

EFFECT_STR_TEMPLATE = '%s:Scene Root:-1:01000000:r%f,%f,%f:p%f,%f,%f:s%f,%f,%f'


@Components(AvatarModelCue,
            ModelMaterialChangeComp,
            MaterialRankComponent,
            ModelStealthComp,
            comp_ladder.LadderModelComp,
            ParachuteUnitModelComp, ParachuteGraphModel,
            ModelBeardComp,
            ModelHeroAttachComp,
            EmoteAvatarModelComp,
            ModelAttachComp,
            JoinSkeletonComp,
            CombatModelClothComp,
            ModelFeatureLODComponent,
            SpikeTransferAvatarModelComp,
            StropModelComp,
            )
@RegisterModelClass
class AvatarModel(PoseSenderModel):

    def __init__(self, owner, position=None, yaw=0):
        super(AvatarModel, self).__init__(owner, position, yaw)
        self.is_graph_moving = False
        self.charctrl = None
        self.charctrl_reason = 0
        self.normal_speed = 0
        self.motion_state = cconst.UNIT_STATE_IDLE
        self.upper_motion_state = ''
        self.locomotion_graph_id = None
        self.action_graph_id = None
        self.action_graph_path = ''
        self.upper_graph_id = None
        self.upper_graph_path = ''
        self.hit_graph_id = None
        self.die_graph_id = None

        self.weapon_dict = {}
        self.cur_client_weapon_guid = ''
        self.cur_client_lefthand_weapon_guid = ''
        self.old_cur_client_weapon_guid = ''  # 卸下前的武器，武士放完技能举武士刀的时候不要raise，但是swich的时候早就把之前的guid清空了，只能记一下

        self.lower_models = []  # 下身模型
        self.lower_models_m = []  # 下半身模型替换材质

        self.charctrl_resize_height_reason = 0
        self.vehicle_graph_id = None
        self.need_graph_yaw_control_ids = []  # 需要控制yaw的graph
        self.enabled_auto_frame_limit = False
        self.enabled_invisible_frame_limit = -1

        self.hip_direction = cconst.ModelHipDirection.F
        self.hidden_timer = None
        self.execute_graph_id = None

        self.graph_yaw = 0.0
        self.pull_graph_id = 0
        self.samurai_hit_graph_id = None
        self.weapon_list = []
        self.hand_left_ik_tps = 'HP_Hand_Left_IK_tps'
        self.graph_list = []
        self.graph_pose_type = ModelPoseType.Stand
        
        self._last_jump_velocity = MType.Vector3()
        self.hold_breath_speed_scale = 1.0

        self.overlay_action_graph_id = None
        self.is_hit_fly = False

    @property
    def lefthand_weapon_case(self):
        return self.owner.GetCurLeftHandWeaponCase(False)

    def _GetAvailableModelInfo(self, model_data):
        final_model_proto = genv.dlc_manager.GetHeroModelFinalUnitModelProto(model_data)
        return final_model_proto['id'], final_model_proto

    def GetModelsFromData(self, data):
        # return data.get("models_low" if self.is_low_quality else "models", [])
        if not switches.ENABLE_CHARACTER_MODEL_LOD:
            return data.get("models_low" if self.is_low_quality else "models", [])

        if self.is_low_quality:
            return data.get("models_low", [])
        
        # 非低质量模式下，优先使用 lodmodels，否则使用 models
        models = data.get("lodmodels") or data.get("models", [])

        # 开了布料的情况下，得把对应的models替换成clothModel
        models = self.TryReplaceModelsByClothConfig(models)
        return models
    
    def InitSkeletonConfigs(self):
        avatar_model_owner = self.owner
        skeleton = self.model.Skeleton
        if skeleton:
            self._skeletonIsValid = True
            skeleton.owner = self.owner
            skeleton.HideTPose = True
            skeleton.SetExpireCallback(self.SkeletonExpireCallback)
            if skeleton.__class__ == MObject.ActorComponent:
                self.BindEventSignalNotify()  # 绑定model_cue事件

            # 禁用最底层Graph同步
            skeleton.SetEnableBottomGraphSync(False)

            # 角色共享变量值同步
            sync_variables_list = []
            for id, vars in actor_variables_data.data.items():
                id in range(1016, 1022) and sync_variables_list.append(vars['name'])

            sync_variables_list.append('ACTOR_TPS_Hand_IK_Bias')
            sync_variables_list.append('ACTOR_TPS_Hand_IK_Name')
            skeleton.SetGraphSyncActorVariables(sorted(sync_variables_list))
            user_tag = 'Self' if self.owner.IsPlayerCombatAvatar else 'Other'
            skeleton.UserTag = user_tag
            if not avatar_model_owner.IsPlayerCombatAvatar:
                self.EnableAutoFrameLimit()
        

        if avatar_model_owner.IsPlayerCombatAvatar:
            self.RefreshGraphSyncMode()

    def Load(self, data, done=None):
        super(SimpleModel, self).Load(data, done)
        print("load", self)
        self.model and self.DestroyCppModel()
        self.AddLoadTask("CreateModel")
        self.model_id, data = self._GetAvailableModelInfo(data)

        models = self.GetModelsFromData(data)

        resource_names = []
        for model in models:
            # 不要单独的头发
            if 'hair' not in model:
                resource_names.append(model)
        combat_unique_models = data.get("combat_unique_models")
        combat_unique_models and resource_names.extend(combat_unique_models)
        self.resource_names = resource_names
        self.lower_models = data.get("lower_models", [])
        self.lower_models_m = data.get("lower_models_material", [])
        skeleton_file = self.GetMotionSkeletonFile(data.get("skeleton", ""))
        model = MHelper.CreateModel(
            lambda _: self.RemoveLoadTask("CreateModel"),
            self.resource_names,
            skeleton_file=skeleton_file,
            graph_file="TPS/Locomotion/tps_empty.graph",
            scale=consts.TPS_AVATAR_MODEL_SCALE,  # HARDCODE: 局内第三人称模型缩放1.1倍 by Beadle
            cachable=False,
            texture_extend_distance=self.texture_extend_distance,
        )
        print("model", model)
        self.model = model
        self._modelIsValid = True
        MHelper.StoryboardCancelFrameLimit(self.model)
        self.model.SetExpireCallback(self.ModelExpireCallback)
        self.InitModelTag()

        self.base_graph_id = 0
        self.model.owner = self.owner

        self.InitSkeletonConfigs()

        if self.owner.IsRobotCombatAvatar:
            self.locomotion_graph_id = self.PushGraph('TPS/Locomotion/tps_idle_1.graph')
        else:
            self.locomotion_graph_id = self.PushGraph(data.get("basic_graph", ""))

        if self.owner.IsCombatAvatar and not self.owner.moving_platform_guid:
            self.position = self.owner.area.position
            self.yaw = self.owner.area.yaw
        if self.owner.IsPlayerCombatAvatar or self.owner.is_replay_avatar:
            genv.messenger.Broadcast(events.ON_PLAYER_MODEL_CREATED)
        # 局内的角色就不要Matcap了，节省一点开销
        if not self.owner.IsPlayerCombatAvatar:
            self.SetShaderGraphParameter("EnableMatcap", False)
        
        self._callComponents('model_loaded')
        self.SetVariableI('is_other', 1, self.locomotion_graph_id)
        self.RefreshDefaultTechState()
        genv.messenger.Broadcast(events.ON_AVATAR_SKELETON_RELOAD_OR_LOAD)
        self.InitSkeletonConfigAfterJoinSkeleton()
    
    def RefreshDefaultTechState(self):
        owner = self.owner
        if not owner:
            return
        player = replay_util.GetPlayer()
        if not player:
            return
        game_logic = genv.space.game_logic
        if game_logic and game_logic.IsEnemy(player, owner):
            self.limit_dis_range = [5, 200]
            self.is_outline_dynamic = True
            self.AddTechState(cconst.ENEMY_TECH_STATE_CONFIG_ID)
            self.SetOutlinedAlpha(1.0)
            self.outlined_thickness_base = 0.1
            self.outlined_color = (1.0, 0.0, 0.0)
        else:
            self.RemoveTechState(cconst.ENEMY_TECH_STATE_CONFIG_ID)

    def ReloadSkeleton(self, data):
        # 换骨骼
        self._skeletonIsValid = False
        self.model.Skeleton.SetExpireCallback(None)
        self.CheckRecreateSkeletonOnReloaded(data)
        self.InitSkeletonConfigs()
        # print_s("[AvatarModel] ReloadSkeleton", SomePreset.black_fg_yellow_bg)
        genv.messenger.Broadcast(events.ON_AVATAR_SKELETON_RELOAD_OR_LOAD)
    
    def Reload(self, data, done=None, use_ready_to_appear=True):
        super(AvatarModel, self).Reload(data, done, use_ready_to_appear)
        self._callComponents('model_reloaded')
        self.InitSkeletonConfigAfterJoinSkeleton()
    
    def InitSkeletonConfigAfterJoinSkeleton(self):
        # 一些需要在骨骼拼接完成后初始化的骨骼配置
        skeleton = self.model.Skeleton
        if not skeleton:
            return
        # 软骨切换成局内的配置
        skeleton.SwitchSoftBoneParams(".*", "combat")
        # owner = self.owner
        # if owner and owner.IsPlayerCombatAvatar:
        #     skeleton.ClearCollisionBones()

    def InitModelTag(self):
        # 避免玩家能爬到其他玩家身上
        self.model.Tag = MObject.CreateObject("TagComponent")
        self.model.Tag.TagString = anim_const.CANT_CLIMB_TAG

    def ReloadPrimitives(self, data, use_ready_to_appear=True):
        # 换模型
        self.model_id, data = self._GetAvailableModelInfo(data)
        self._modelIsValid = False
        self.model.SetExpireCallback(None)
        models = self.GetModelsFromData(data)
        resource_names = []
        self.lower_models = data.get("lower_models", [])
        self.lower_models_m = data.get("lower_models_material", [])
        for model in models:
            # 不要单独的头发
            if 'hair' not in model:
                resource_names.append(model)
        combat_unique_models = data.get("combat_unique_models")
        combat_unique_models and resource_names.extend(combat_unique_models)
        self.resource_names = resource_names
        self.DeleteAllShellComponent()
        ResourceLoader.ChangePrimitives(
            entity=self.model,
            resources=self.resource_names,
            use_ready_to_appear=use_ready_to_appear,
            done=lambda: self.RemoveLoadTask("ReloadResource"),
        )
        self._modelIsValid = True
        self.model.SetExpireCallback(self.ModelExpireCallback)

    def CheckRecreateSkeletonOnReloaded(self, data):
        # avatar reload需要重新创建骨骼
        skeleton_file = self.GetMotionSkeletonFile(data.get("skeleton", ""))
        graph_file = None if self.force_no_graph else data.get("basic_graph", "")
        self.base_graph_id = 0 if graph_file else None
        skeleton = self.model.Skeleton
        if not skeleton or skeleton.ScriptResourcePath != skeleton_file or skeleton.GraphFile != \
                ('Graph/%s' % graph_file if graph_file else ''):
            self.model.Skeleton.ClearAllEffects()  # 先清理一下上一个骨骼上的特效
            MHelper.LoadSkeletonAndGraph(
                entity=self.model,
                skeleton_file=skeleton_file,
                graph_file="TPS/Locomotion/tps_empty.graph",
            )
            
            self.MakeSureBones()
            self.RefreshGraphSyncMode()
            # 重新push下graph
            self.locomotion_graph_id = None
            self.action_graph_id = None
            self.upper_graph_id = None
            if self.owner.IsRobotCombatAvatar:
                self.locomotion_graph_id = self.PushGraph('TPS/Locomotion/tps_idle_1.graph')
            else:
                self.locomotion_graph_id = self.PushGraph(data.get("basic_graph", ""))

            if self.owner.IsCombatAvatar and not self.owner.moving_platform_guid:
                self.position = self.owner.area.position
                self.yaw = self.owner.area.yaw

            self.PushActionGraph(self.action_graph_path)
    
    def RefreshGraphSyncMode(self):
        self.EnableGraphSyncSlave()

    # def CheckRecreateSkeletonOnReloaded(self, data):
    #     # avatar reload不需要重新创建骨骼
    #     s = self.skeleton
    #     if not s:
    #         super(AvatarModel, self).CheckRecreateSkeletonOnReloaded(data)
    #         return
    #     if self.owner.IsRobotCombatAvatar:
    #         graph_file = 'TPS/Locomotion/tps_idle_1.graph'
    #     else:
    #         graph_file = data.get("basic_graph", "")
    #     stack_info = s.GetGraphStackInfo()
    #     if len(stack_info) >= 2 and stack_info[1].layerName == '%s%s' % (GRAPH_DIR, graph_file):
    #         return
    #     self.PopToLocomotionGraph()
    #     self.PopGraph(self.locomotion_graph_id)
    #     self.locomotion_graph_id = self.PushGraph(graph_file)

    # def ReloadResourceDone(self, data):
    #     # 资源reload完成再reload骨骼
    #     # 目的是为了优化挂接的model的表现效果
    #     self._skeletonIsValid = False
    #     if not self.model:
    #         return
    #     self.model.Skeleton.SetExpireCallback(None)
    #     self.CheckRecreateSkeletonOnReloaded(data)
    #     skeleton = self.model.Skeleton
    #     if skeleton:
    #         self._skeletonIsValid = True
    #         skeleton.HideTPose = True
    #         skeleton.SetExpireCallback(self.SkeletonExpireCallback)
    #         if skeleton.__class__ == MObject.ActorComponent and not self.force_no_graph:
    #             self.BindEventSignalNotify()  # 绑定model_cue事件
    #     self.RemoveLoadTask("ReloadResource")

    def RefreshAutoFrameLimit(self):
        if self.enabled_auto_frame_limit:
            self.enabled_auto_frame_limit = False
            self.EnableAutoFrameLimit()

    def EnableAutoFrameLimit(self):
        if self.enabled_auto_frame_limit:
            return
        actor = self.skeleton
        if not actor:
            return
        self.enabled_auto_frame_limit = True

        # 参数解析：Skeleton.SetAutoFrameLimit(10, 30, 40, 10)
        # 动画采样在10m距离内，不限帧，在40m范围之外限帧10fps，在10m~40m距离时，帧率在30fps~10fps之间线性插值。
        near_frames = FPS_LIMITS.get(LocalConfig.game_fps_limit, 60.0) + 1.0
        args = (10.0, near_frames, 100.0, 10.0)
        # print("[EnableAutoFrameLimit]:", self.owner, args)
        actor.SetAutoFrameLimit(*args)

    def DisableAutoFrameLimit(self):
        if not self.enabled_auto_frame_limit:
            return
        self.enabled_auto_frame_limit = False
        actor = self.skeleton
        if not actor:
            return
        # print "[DisableAutoFrameLimit]:", self.owner
        actor.SetAutoFrameLimit(0, -1, 0, -1)

    def EnableInvisibleFrameLimit(self, frames):
        if self.enabled_invisible_frame_limit == frames:
            return
        model = self.model
        if not model:
            return
        model.Storyboard.FrameLimit = frames
        self.enabled_invisible_frame_limit = frames

    def DisableInvisibleFrameLimit(self):
        if self.enabled_invisible_frame_limit < 0:
            return
        model = self.model
        if not model:
            return
        self.enabled_invisible_frame_limit = -1
        model.Storyboard.FrameLimit = -1

    @property
    def yaw(self):
        if self.model:
            return self.model.Transform.yaw
        else:
            return self.born_yaw

    @yaw.setter
    def yaw(self, value):
        model = self.model
        if model:
            set_yaw = False
            if model.Skeleton:
                self._SetGraphYaw(value)
                set_yaw = True

            if not set_yaw:
                trans = model.Transform
                trans.yaw = value
                model.Transform = trans

    @property
    def gun_type(self):
        return self.GetCurClientWeaponGunType()

    @property
    def pose_type(self):
        if not self.owner:
            return consts.ModelPoseType.Stand
        return self.owner.pose_type

    @property
    def weapon_case(self):
        return self.owner.GetCurWeaponCase(False)

    @property
    def client_weapon_case(self):
        return self.owner.GetWeaponCase(self.cur_client_weapon_guid, False)

    def SetPitchAndYaw(self, pitch, yaw):
        self.yaw = yaw

    def Refresh(self):
        owner = self.owner
        if not owner:
            return
        owner.OnWeaponListChanged()
        if owner.IsCurTakeGunWeapon() or owner.IsCurTakeMeleeWeapon():
            self.RaiseCurWeapon()
            if owner.IsCurTakeLeftHandWeapon():
                self.RaiseCurLeftHandWeapon()
        else:
            self.RaiseCurSpecWeapon()
        combat_state = owner.combat_state
        if combat_state == CombatState.DEAD:
            owner.OnDead()
        elif combat_state == CombatState.GHOST:
            owner.OnGhost()
        elif combat_state == CombatState.DYING:
            self.OnKnockDown(True)
        pose_type = owner.pose_type
        if pose_type in (cconst.ModelPoseType.Prone, cconst.ModelPoseType.Crouch, cconst.ModelPoseType.Ladder):
            self._SetModelPoseType(pose_type)
        self.ResetLocomotionGraphParam()  # 注意这里会重置pose type
        if self.owner.is_in_swim:
            self.owner.SetCurWeaponCaseVisibility(False, cconst.HIDDEN_REASON_SWIM, False)
        if self.owner.motion_state == cconst.UNIT_STATE_EMOTE:
            weapon_case = self.GetCurClientWeaponCase()
            weapon_case and weapon_case.AttachWeaponToAvatarBack()
        self._callComponents('on_refresh_model')

    def OnPoseTypeChange(self, pose_type):
        self._CheckCharctrlSizeForReason()

    def OnSpeedLevelChange(self):
        if not self.model:
            return
        locomotion_graph_id = self.locomotion_graph_id
        SetVariableI = self.model.Skeleton.SetVariableI
        SetVariableI(locomotion_graph_id, 'LastMoveGait', self.owner.last_speed_level)
        # 往第0层也穿一下
        SetVariableI(0, 'LastMoveGait', self.owner.last_speed_level)
        SetVariableI(locomotion_graph_id, 'MoveGait', self.owner.speed_level)
        SetVariableI(0, 'MoveGait', self.owner.speed_level)

    def Towards(self, x, z, speed=None):
        owner = self.owner
        if not owner or owner.is_destroyed():
            return
        model = self.model
        if not model:
            return
        Skeleton = model.Skeleton
        if not Skeleton:
            return
        locomotion_graph_id = self.locomotion_graph_id
        if not locomotion_graph_id:
            return
        if owner.combat_state != CombatState.DYING:
            # 倒地动作不参与graph同步
            return
        SetVariableI = Skeleton.SetVariableI
        SetVariableF = Skeleton.SetVariableF
        poseSender = self.poseSender
        if x or z:
            can_run = not (owner.is_shooting and owner.pose_type == cconst.ModelPoseType.Prone)
            SetVariableI(locomotion_graph_id, 'is_run', can_run)
            cur_speed = speed if speed else self.GetOwnerSpeed()
            yaw_diff = self.GetMoveYawDiff(x, z)
            SetVariableF(locomotion_graph_id, 'G_MOVE_SPEED', cur_speed)
            SetVariableF(locomotion_graph_id, 'local_yaw', yaw_diff)
            if poseSender:
                poseSender.PredictMoveDeltaTime = anim_const.PREDICT_MOVE_DELTA_TIME
        else:
            SetVariableI(locomotion_graph_id, 'is_run', 0)
            if poseSender:
                poseSender.PredictMoveDeltaTime = 0

    def CreateCharCtrl(self, width, height, physics_info, **kwargs):
        self.charctrl = MHelper.CreateCharCtrl(width, height, physics_info, **kwargs)
        if self.owner.IsPlayerCombatAvatar:
            self.charctrl.EnableStepUpSearch = True # stepoffset过大时避免被头顶障碍卡住
        self.charctrl.owner = self.owner
        self.charctrl.ownerid = self.owner.id
        self.charctrl.MaterialTypeId = 1001 # 人体材质类型
        self.model.CharCtrl = self.charctrl
        if self.charctrl:
            self.charctrl.Pos = MType.Vector3(*self.position)
            self.charctrl.Yaw = self.yaw
            self.charctrl.SetOverlapRecoverWithChar(False)
        self.ApplyMotion(True)
        self.ApplyGravity()
        self.SetEnableRideVelocity(False)

    def SetEnableRideVelocity(self, enable):
        if not self.charctrl or not self.charctrl.IsValid():
            return
        self.charctrl.EnableRideVelocity = enable

    def SetCharCtrlProne(self, enable):
        print('SetCharCtrlProne', enable)
        if not self.charctrl:
            return
        if self.charctrl.IsProne() == enable:
            return
        if enable:
            self.charctrl.MaxAngularSpeed = 30
            self.charctrl.StepOffset = 0.1
        else:
            self.charctrl.MaxAngularSpeed = 1000
            self.charctrl.StepOffset = 0.5
        self.charctrl.SetProne(enable)

    def ResizeCharCtrlFpsModeRadius(self, is_fps_mode):
        return
        # if not self.charctrl:
        #     return
        # new_radius = cconst.PLAYER_CHARCTRL_RADIUS if is_fps_mode else cconst.PLAYER_CHARCTRL_TPS_RADIUS
        # halfheight = cconst.PLAYER_CHARCTRL_HALFHEIGHT
        # filter_info = self.charctrl.CollisionFilterInfo
        # is_prone = self.charctrl.IsProne()
        # self.CreateCharCtrl(new_radius, halfheight, filter_info)
        # self._CheckCharctrlSizeForReason()
        # is_prone and self.SetCharCtrlProne(is_prone)
        # self.charctrl.EnableStepUpSearch = is_fps_mode  # stepoffset过大时避免被头顶障碍卡住

    def ResizeCharCtrlPoseHeight(self, pose_type):
        new_height = CHARCTRL_POSE_HEIGHT.get(pose_type)
        if not new_height:
            return
        self.charctrl.ResizeHeight(new_height)

    def ResizeCharctrHeightForReason(self, reason):
        self.charctrl_resize_height_reason |= 1 << reason
        self._CheckCharctrlSizeForReason()

    def _CheckCharctrlSizeForReason(self):
        if not self.charctrl:
            return
        if not self.charctrl_resize_height_reason:
            self.ResizeCharCtrlPoseHeight(self.owner.pose_type)
            return
        height_reason = cconst.FindLowestBit1(self.charctrl_resize_height_reason)
        height = cconst.CHAR_CTRL_HEIGHT_SIZE.get(height_reason)
        if height is not None:
            self.charctrl.ResizeHeight(height)

    def RemoveCharctrlHeightReason(self, reason):
        self.charctrl_resize_height_reason &= ~(1 << reason)
        self._CheckCharctrlSizeForReason()

    def ClearCharctrlHeightReason(self):
        self.charctrl_resize_height_reason = 0
        self._CheckCharctrlSizeForReason()

    def ApplyGravity(self, enable=True):
        if not self.charctrl:
            return
        if enable:
            self.charctrl.Gravity = MType.Vector3(0, -9.8, 0)
        else:
            self.charctrl.Gravity = MType.Vector3(0, 0, 0)

    def IsGraphSyncSlave(self):
        return self.GetGraphSyncMode() == 'Slave'

    def GetGraphSyncMode(self):
        if not self.isValid():
            return
        return self.model.Skeleton.GetGraphSyncMode()

    def EnableGraphSyncMaster(self, enable=True):
        if not self.isValid():
            return False
        self.model.Skeleton.SetGraphSyncMaster(enable)
        if self.poseSender:
            self.poseSender.EnableSendGraphState = enable

    def EnableGraphSyncSlave(self, enable=True):
        if not self.isValid() or not hasattr(self, 'filter'):
            return False
        self.model.Skeleton.SetGraphSyncSlave(enable)
        # Beadle: 不要打开InertializationForSlave，2023引擎会有概率出现客户端卡死
        # self.model.Skeleton.SetEnableInertializationForSlave(True)
        # self.filter.SetMotionMaskExt(True, True, True, enable, enable, enable)

    def EnableDockingDetector(self, enable=True):
        self.SetVariableI('enableDockingDetector', enable, self.locomotion_graph_id)

    def EnableIsLookAt(self, isLookAt):
        self.SetVariableI('isLookAt', isLookAt, self.locomotion_graph_id)

    def ApplyMotion(self, flag=True):
        super(AvatarModel, self).ApplyMotion(flag)
        if self.charctrl:
            self.charctrl.PassiveMode = not flag

    def ApplyFilterMotion(self, apply):
        super(AvatarModel, self).ApplyFilterMotion(apply)
        if getattr(self, "filter", None):
            if self.charctrl:
                self.charctrl.PassiveMode = apply
        
    def CreateFilter(self, debug=False):
        filter = super(AvatarModel, self).CreateFilter(debug)
        if self.charctrl:
            self.charctrl.PassiveMode = True
        return filter

    def EnableCharCtrlWithReason(self, reason):
        if not self.charctrl:
            return
        self.charctrl_reason &= ~(1 << reason)
        if not self.charctrl_reason:
            self.charctrl.Enable = True

    def UnableCharCtrlWithReason(self, reason):
        if not self.charctrl:
            return
        self.charctrl_reason |= 1 << reason
        self.charctrl.Enable = False
        genv.messenger.Broadcast(events.ON_PLAYER_CHARCTRL_ENABLED, self.owner, False)

    def IsMoving(self):
        return self.is_graph_moving

    def ChangeMotionState(self, motion_state):
        if self.motion_state == motion_state:
            return
        self.motion_state = motion_state

    def ChangeUpperMotionState(self, upper_motion_state):
        if self.upper_motion_state == upper_motion_state:
            return
        self.upper_motion_state = upper_motion_state

    def SetHipDirection(self, x, z):
        owner = self.owner
        is_shooting = owner.is_shooting
        if is_shooting:
            return
        last_move_dir = owner.last_move_dir
        move_dir = genv.input_ctrl.input_move_dir
        move_dir_enum = cconst.MoveDirection
        hip_dir_enum = cconst.ModelHipDirection
        last_hip_dir = self.hip_direction
        # 这个pass是干啥得
        # if is_shooting:
        #     pass
        self.SetVariableI('LastHipDirection', last_hip_dir, self.locomotion_graph_id)
        if z == 0:
            # 无z方向移动
            if last_hip_dir == hip_dir_enum.B:
                # 之前臀向正后，则右走臀向朝右后，左走臀向朝左后
                hip_dir = hip_dir_enum.RB if x > 0 else hip_dir_enum.LB
            else:
                if x > 0:
                    # 当前往右走
                    # 之前移动方向是左前/右后，则臀向朝右后，否则臀向潮右前
                    if last_move_dir == move_dir_enum.RightBackward:
                        hip_dir = hip_dir_enum.RB
                    else:
                        hip_dir = hip_dir_enum.RB if last_hip_dir == hip_dir_enum.LF else hip_dir_enum.RF
                else:
                    # 当前往左走
                    # 之前移动方向是左后/右前，则臀向朝左后，否则臀向潮左前
                    if last_move_dir == move_dir_enum.LeftBackward:
                        hip_dir = hip_dir_enum.LB
                    else:
                        hip_dir = hip_dir_enum.LB if last_hip_dir == hip_dir_enum.RF else hip_dir_enum.LF
        else:
            # 有z方向移动
            if x == 0:
                # 无x方向移动，臀向正前或右后
                hip_dir = hip_dir_enum.F if z > 0 else hip_dir_enum.B
            elif x < 0:
                # 当前移动方向是左前/左后，则臀向朝左前/左后
                hip_dir = hip_dir_enum.LF if move_dir == move_dir_enum.LeftForward else hip_dir_enum.LB
            else:
                # 当前移动方向是右前/右后，则臀向朝右前/右后
                hip_dir = hip_dir_enum.RF if move_dir == move_dir_enum.RightForward else hip_dir_enum.RB
        self.hip_direction = hip_dir
        self.SetVariableI('HipDirection', self.hip_direction, self.locomotion_graph_id)

    def _SetModelPoseType(self, pose_type):
        if pose_type == ModelPoseType.Crouch:
            graph_pose_type = 1
        elif pose_type == ModelPoseType.Prone:
            graph_pose_type = 2
        elif pose_type == ModelPoseType.Ladder:
            graph_pose_type = 7
        else:
            graph_pose_type = 0
        self.upper_graph_id and self.SetVariableI('pose_type', graph_pose_type, self.upper_graph_id)
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        graph_stack = skeleton.GetGraphStack()
        if not graph_stack or len(graph_stack) < 2:
            return
        pose_graph_id = graph_stack[1]
        self.SetVariableI('pose_type', graph_pose_type, pose_graph_id)
        if self.graph_pose_type != graph_pose_type:
            self.SetVariableI("LastPoseType", self.graph_pose_type, pose_graph_id)
            self.FireEvent(anim_const.POSE_TYPE_CHANGE_EVENT, pose_graph_id)
        self.graph_pose_type = graph_pose_type
        if pose_type == ModelPoseType.Stand:
            self.FireEvent('@stand', pose_graph_id)
        elif pose_type == ModelPoseType.Crouch:
            self.FireEvent('@crouch', pose_graph_id)
        elif pose_type == ModelPoseType.Prone:
            self.FireEvent('@lie', pose_graph_id)

    def _SetGraphYaw(self, yaw):
        self.graph_yaw = yaw
        self.SetVariableF('G_YAW', yaw, self.locomotion_graph_id)
        self.SetVariableF('G_YAW', yaw, 0)  # 第0层也塞一下
        for graph_id in self.need_graph_yaw_control_ids:
            self.SetVariableF('G_YAW', yaw, graph_id)
        if self.parachute_graph_id:
            self.SetVariableF('G_YAW', yaw, self.parachute_graph_id)

    def SetGraphYawHalfLife(self, halflife):
        self.SetVariableF('yaw_halflife', halflife, self.locomotion_graph_id)

    def SetUpperGraphCommonParam(self):
        self._SetModelPoseType(self.pose_type)
        self.SetVariableI('is_run', self.IsMoving(), self.upper_graph_id)
        self.SetVariableI('is_jump', self.pose_type == cconst.ModelPoseType.Jump, self.upper_graph_id)

    def ResetLocomotionGraphParam(self):
        self.SetVariableV3('move_Vec', MType.Vector3(), self.locomotion_graph_id)
        self.SetVariableI('is_run', 0, self.locomotion_graph_id)
        self.SetVariableI('can_stop_pose_play', 0, self.locomotion_graph_id)
        self.FireEvent('@Stop', self.locomotion_graph_id)
        self._SetModelPoseType(cconst.ModelPoseType.Stand)
        self.AnimArmWeapon()
        # self.FireEvent('creepIn', self.locomotion_graph_id)
        self.JumpBreak()
        self.OnSlide(False)

    def RefreshHandIK(self, hand_left_ik_tps):
        self.hand_left_ik_tps = hand_left_ik_tps
        self.HandIkToWeapon()

    def CheckWeaponModelEnableIK(self, weapon_case):
        weapon_model = weapon_case.model
        model = weapon_model.model
        if not model:
            return False
        # 过滤一下3P因为隐藏阴影的原因导致模型不可见的情况
        is_weapon_visible = model.IsVisible or weapon_model.hidden_reason == 1 << cconst.HIDDEN_REASON_3P_HIDE_SHADOW
        if not is_weapon_visible or not model.Tach:  # 枪械隐藏了或者枪械没有挂接上去 就不用ik了
            return False
        return True

    def HandIkToWeapon(self, enable=True, hand_type=0):
        # self.CreateSpecifyBone(hand_bone)
        weapon_case = self.weapon_case
        if not weapon_case or not weapon_case.model:
            enable = False
        if enable:
            enable = self.CheckWeaponModelEnableIK(weapon_case)
        self.SetVariableI('ACTOR_TPS_IKHand', hand_type, 0)
        self.SetVariableI('ACTOR_TPS_HandIkEnable', enable, 0)
        if enable:
            weapon_model = weapon_case.model
            weapon_ik_bone = self.hand_left_ik_tps
            weapon_tach_bone = weapon_model.model.Tach.Basepoint
            mat_ik = weapon_model.GetBoneTransform(weapon_ik_bone)
            mat_tach = weapon_model.GetBoneTransform(weapon_tach_bone)

            hand_tach_bone = weapon_model.model.Tach.Hardpoint
            mat = mat_tach.inverse.transform_p(mat_ik.translation)
            self.SetVariableV3('ACTOR_TPS_Hand_IK_Bias', mat, 0)
            self.SetVariableS('ACTOR_TPS_Hand_IK_Name', hand_tach_bone, 0)
            use_highlevel = gui.is_pc and genv.highlevel_tps_weapon
            self.SetVariableI('highlevel_tps_weapon', int(use_highlevel), 0)

    def FireLocomotionEvent(self, event):
        if self.locomotion_graph_id is not None:
            self.FireEvent(event, self.locomotion_graph_id)

    def JumpToIdle(self):
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        self.ResetLocomotionGraphID()
        self.PopToLocomotionGraph()
        self.JumpToState(cconst.UNIT_STATE_IDLE)
        self.ChangeMotionState(cconst.UNIT_STATE_IDLE)

    def ResetLocomotionGraphID(self):
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        self.locomotion_graph_id = skeleton.GetGraphStack()[1]

    def PopToLocomotionGraph(self):
        if self.parachute_graph_id:
            return
        if self.vehicle_graph_id:
            return
        if self.execute_graph_id:
            return
        if self.motion_state == cconst.UNIT_STATE_DEAD and not self.owner.is_alive:
            return
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        stack = skeleton.GetGraphStack()
        stack_len = 2
        graph_id = self.locomotion_graph_id
        if self.action_graph_id:
            stack_len = 2
            graph_id = self.locomotion_graph_id
        # 后面补的and判断是为了处理knockdown的情况... 也不太优雅，理论上knockdown可以改改，后面有空再改吧
        while len(stack) > stack_len and (stack[-1] != graph_id and stack[-1] != self.locomotion_graph_id):
            graph_id = stack.pop(-1)
            if graph_id not in self.need_graph_yaw_control_ids:
                self.PopGraph(graph_id)
        self.upper_graph_id = None
        self.hit_graph_id = None
        self.die_graph_id = None
        self.samurai_hit_graph_id = None
        self.ChangeUpperMotionState('')
        self.ActionEndToIdle()

    def PopAllSpellGraph(self):
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        stack_info = skeleton.GetGraphStackInfo()
        for graph in stack_info:
            split_path = graph.layerName.split('/')
            if len(split_path) > 3 and split_path[2] == 'Spell':
                skeleton.PopGraphByID(graph.handle)

    def PushActionGraph(self, graph, blendTime=0):
        if not self.weapon_case:
            return
        self.action_graph_path = graph
        weapon_id = self.weapon_case.weapon_id
        if not self.CanUseTPSActionGraph(weapon_id):
            return

        # self.WarmUpWeaponAnimations(self.weapon_case.body_weapon_id)
        if self.action_graph_id:
            self.ReplaceGraph(self.action_graph_id, graph)
        else:
            # self.action_graph_id and self.PopGraph(self.action_graph_id)
            self.action_graph_id = self.PushGraph(graph, blendTime)
        self.SetVariableF('play_speed', self.owner.CalAttrResult(AttrType.UseCureItemSpeed, 1.0), self.action_graph_id)
        self.PinWeaponGraph(graph)
        self.SetMpFpsAction(cconst.MpFpsAction.RaiseWeapon, 0)  # 暂时先不刷新其他的数据
        # self.RefreshAfterPushActionGraph()
        if self.action_graph_id:
            self.SetVariableI('pose_type', self.pose_type, self.action_graph_id)
            self.SetVariableI('gun_type', self.gun_type, self.action_graph_id)
        return self.action_graph_id

    def PushGraph(self, graph, transtime=0, ratio=0):
        graph_id = super(AvatarModel, self).PushGraph(graph, transtime, ratio)
        self.graph_list.append(graph_id)
        return graph_id

    def PopGraph(self, graph_id, blendTime=0):
        if graph_id in self.graph_list:
            self.graph_list.remove(graph_id)
        super(AvatarModel, self).PopGraph(graph_id, blendTime)

    def PushUpperGraph(self, graph, transtime=0.0):
        if self.die_graph_id or self.execute_graph_id:
            return
        if genv.use_tps_action_graph and self.weapon_case:
            weapon_id = self.weapon_case.weapon_id
            if self.CanUseTPSActionGraph(weapon_id):
                return
        skeleton = self.GetSkeleton()
        if not skeleton:
            return

        if self.upper_graph_id and self.upper_graph_path == graph:
            self.PopUpperGraph()

        self.upper_graph_id = upper_graph_id = self.PushGraph(graph, transtime)
        self.upper_graph_path = graph
        if upper_graph_id:
            skeleton.SetVariableI(upper_graph_id, 'pose_type', self.pose_type)
            skeleton.SetVariableI(upper_graph_id, 'gun_type', self.gun_type)
            if self.overlay_action_graph_id:
                # 需要把overlay graph往上移动一位
                self.ShiftGraph(self.overlay_action_graph_id, 1)

    def PopUpperGraph(self, blend_time=0.2):
        if genv.use_tps_action_graph and self.weapon_case:
            weapon_id = self.weapon_case.weapon_id
            if self.CanUseTPSActionGraph(weapon_id):
                self.ChangeUpperMotionState('')
                return
            else:
                # 如果不能用tps常驻action_graph，需要把action_graph_id也pop掉
                self.PopActionGraph()
        if self.upper_graph_id:
            self.PopGraph(self.upper_graph_id, blendTime=blend_time)
        self.upper_graph_id = None
        self.upper_graph_path = ''
        self.ChangeUpperMotionState('')
    
    def CanUseTPSActionGraph(self, weapon_id):
        if not genv.use_tps_action_graph:
            return False
        if weapon_id not in TPS_ACTION_AVAILABLE_WEAPON_IDS:
            return False
        return True
    
    def PopActionGraph(self):
        if self.action_graph_id and self.action_graph_id > 0:
            self.action_graph_id and self.PopGraph(self.action_graph_id)
        self.action_graph_id = None

    def PopHitGraph(self):
        if self.hit_graph_id:
            self.PopGraph(self.hit_graph_id, 0.2)
        self.hit_graph_id = None

    def PopPullGraph(self):
        if self.pull_graph_id:
            self.PopGraph(self.pull_graph_id, 0)
        self.pull_graph_id = None

    def PopSamuraiHitGraph(self):
        if self.samurai_hit_graph_id:
            self.PopGraph(self.samurai_hit_graph_id, 0)
        self.samurai_hit_graph_id = None

    def CueSamuraiBlockEnd(self):
        self.PopSamuraiHitGraph()

    def CommonOverlayActionEnd(self, pop_time=0.2):
        self.PopOverlayActionGraph(pop_time)

    def PushOverlayActionGraph(self, graph, blendTime=0):
        # if self.lefthand_weapon_case:
        #     self.WarmUpWeaponAnimations(self.lefthand_weapon_case.weapon_id)
        self.overlay_action_graph_id and self.PopGraph(self.overlay_action_graph_id)
        self.overlay_action_graph_id = self.PushGraph(graph, blendTime)
        return self.overlay_action_graph_id

    def PopOverlayActionGraph(self, blendtime=0.0):
        if self.overlay_action_graph_id:
            self.overlay_action_graph_id and self.PopGraph(self.overlay_action_graph_id, blendtime)
            self.overlay_action_graph_id = None

    def JumpToFire(self, info=None, transtime=0.1, reset=False, spell_id=0, is_auto_fire=True):
        if not self.action_graph_id:
            return
        if self.GetVariableF('Mp_Fps_Actions', self.action_graph_id) == cconst.MpFpsAction.Fire:
            self.FireEvent('@fire', self.action_graph_id)
        else:
            self.FireEvent('@fire', self.action_graph_id)
            self.SetMpFpsAction(cconst.MpFpsAction.Fire, 0)
            self.fire_start_time = time.time()

        self.SetVariableI('isAutoFire', int(is_auto_fire), self.action_graph_id)
        self.SetVariableI('isKeyUp', 0, self.action_graph_id)
        self.SetVariableI('isKeyDown', 1, self.action_graph_id)

        if self.weapon_case and self.weapon_case.weapon_model:
            self.weapon_case.weapon_model.JumpToFireStart()

    def JumpToFireEnd(self):
        if not self.action_graph_id:
            return
        if self.action_graph_id:
            self.SetVariableI('isKeyDown', 0, self.action_graph_id)
            self.FireEvent('@keyup', self.action_graph_id)
            self.FireEvent('@SpellStop', self.action_graph_id)
        if self.weapon_case and self.weapon_case.weapon_model:
            self.weapon_case.weapon_model.JumpToFireEnd()

    def JumpToDualFire(self, info=None, transtime=0.1, reset=False, spell_id=0):
        pass

    def JumpToDualFireEnd(self):
        pass

    def JumpToMeleeStart(self, info=None, transtime=0.1, reset=False, spell_id=0):
        if self.action_graph_id:
            # if not self.weapon_model:
            #     return
            if self.GetVariableF('Mp_Fps_Actions', self.action_graph_id) == cconst.MpFpsAction.Melee:
                self.FireEvent('@melee', self.action_graph_id)
            else:
                self.FireEvent('@melee', self.action_graph_id)
                self.SetMpFpsAction(cconst.MpFpsAction.Melee, 0)

            self.SetVariableI('isKeyUp', 0, self.action_graph_id)
            self.SetVariableI('isKeyDown', 1, self.action_graph_id)
            # self.SetVariableI('isKeyUp', 0, self.pose_graph_id)
            # self.SetVariableI('isKeyDown', 1, self.pose_graph_id)

            # # 450553 【US】使用武士刀、棒球棍滑铲接近战攻击，会有滑铲动作残留
            # self.FireEvent("sliding_done", self.pose_graph_id)
            return

    def JumpToMeleeEnd(self):
        pass

    def JumpToAds(self, is_ads):
        # 直接给3P的pos graph塞个变量就好
        self.SetVariableI('isADS', int(is_ads), self.locomotion_graph_id)

    def JumpToRechamber(self):
        self.JumpToState(cconst.UNIT_STATE_RECHAMBER)

    def _OnJumpTo_Rechamber(self, info):
        self.PopUpperGraph()
        self.PushUpperGraph('TPS/Locomotion/tps_rechamber.graph')
        anim_id = self.weapon_case.equip_proto.get('anim_data_id', None)
        if not anim_id:
            return
        anim_data = tps_gun_anim_data.data.get(anim_id, {}).get(1)
        if not anim_data:
            return
        rechamber_anim = anim_data.get("RechamberAnimName")
        rechamber_anim and self.SetVariableS("RechamberAnimName", rechamber_anim, self.upper_graph_id)
        self.SetMpFpsAction(cconst.MpFpsAction.Rechamber, 0)

    def JumpToInspection(self):
        self.JumpToState(cconst.UNIT_STATE_INSPECTION)

    def _OnJumpTo_Inspection(self, info):
        self.PopUpperGraph()
        self.PushUpperGraph('TPS/Locomotion/tps_inspection.graph')
        # print("avatar_model.py AvatarModel _OnJumpTo_Inspection")
        anim_id = self.weapon_case.equip_proto.get('anim_data_id', None)
        if not anim_id:
            return
        anim_data = tps_gun_anim_data.data.get(anim_id, {}).get(1)
        if not anim_data:
            return
        inspection_anim = anim_data.get("InspectionAnimName")
        inspection_anim and self.SetVariableS("InspectionAnimName", inspection_anim, self.upper_graph_id)
        self.SetMpFpsAction(cconst.MpFpsAction.Inspection, 0)

    def _OnJumpTo_Idle(self, info):
        self.PopUpperGraph()
        self.PopHitGraph()
        self.PopToLocomotionGraph()
        self.AnimArmWeapon(self.gun_type)
        self.ChangeUpperMotionState('')
        self.SetMpFpsAction(cconst.MpFpsAction.Idle, 0)

    def _OnJumpTo_Reload(self, info):
        # todo hardcode
        self.PopUpperGraph()
        self.PushUpperGraph('TPS/Gun/reload_m4.graph')
        self.ChangeUpperMotionState(cconst.UNIT_STATE_RELOAD)
        self.SetMpFpsAction(cconst.MpFpsAction.Reload, 0)
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        # self.weapon_case.is_gun and self.weapon_case.AttachWeaponChargerToLeftHandNew()
        anim_id = weapon_case.equip_proto.get('anim_data_id', None)
        if not anim_id:
            return
        anim_data = tps_gun_anim_data.data.get(anim_id, {}).get(1)
        if not anim_data:
            return
        reload_anim = anim_data.get("ReloadAnimName")
        reload_anim and self.SetVariableS("ReloadAnimName", reload_anim, self.upper_graph_id)
        empty_reload_anim = anim_data.get("EmptyReloadAnimName")
        empty_reload_anim and self.SetVariableS("EmptyReloadAnimName", empty_reload_anim, self.upper_graph_id)

    def _OnJumpTo_FireModeChange(self, info):
        self.ChangeUpperMotionState(cconst.UNIT_STATE_FIRE_MODE_CHANGE)
        self.SetMpFpsAction(cconst.MpFpsAction.Fire_Mode_Select, 0)

    def FireJumpEventForJump(self):
        self.FireLocomotionEvent('@JumpStartNew')

    def GetSlideExtraJumpSpeed(self, yaw_diff, is_fall=False, is_parkour=False):
        jump_velocity = MType.Vector3(0, 0, 1)
        jump_velocity.yaw = yaw_diff
        cur_local_velocity = self.GetVariableV3('SYS_ENTITY_LOCAL_VELOCITY', self.locomotion_graph_id)
        cur_speed = self.GetVariableF('SYS_ENTITY_SPEED', self.locomotion_graph_id)
        cur_local_velocity.y = 0
        cur_local_velocity_n = cur_local_velocity.clone()
        cur_local_velocity_n.length = 1

        cos = cur_local_velocity_n.dot(jump_velocity)

        if is_fall:
            low_bound = game_const_data.SLIDE_FALL_SPEED_LOW_BOUND
            high_bound = game_const_data.SLIDE_FALL_SPEED_HIGH_BOUND
            arg_0 = game_const_data.SLIDE_JUMP_SPEED_ARG1
            arg_1 = game_const_data.SLIDE_JUMP_SPEED_ARG2
            arg_2 = game_const_data.SLIDE_JUMP_SPEED_ARG3
        else:
            low_bound = game_const_data.SLIDE_JUMP_SPEED_LOW_BOUND
            high_bound = game_const_data.SLIDE_JUMP_SPEED_HIGH_BOUND
            arg_0 = game_const_data.SLIDE_JUMP_SPEED_ARG1
            arg_1 = game_const_data.SLIDE_JUMP_SPEED_ARG2
            arg_2 = game_const_data.SLIDE_JUMP_SPEED_ARG3

        if cur_speed < low_bound:
            ret = 1 if cos >= 0 else -1
            ret *= arg_0
            # [DEBUG]
            if SHOW_SLIDE_JUMP_DEBUG:
                print('当前滑铲速度%s, 是否跳跃%s, cos值: %s,  low_bound:%s, high_bound:%s, result:%s' % (cur_local_velocity.length, not is_fall, cos, low_bound, high_bound, ret))
            # [DEBUG]
            return ret
        elif cur_speed < high_bound:
            coef = game_const_data.PARKOUR_SLIDE_JUMP_MAX_SPEED_COEF if is_parkour else 1.0
            ret = min(max(cur_local_velocity.dot(jump_velocity) * arg_1[0] + arg_1[1], arg_1[2]), arg_1[3] * coef)
            # [DEBUG]
            if SHOW_SLIDE_JUMP_DEBUG:
                print('当前参数：%s' % str(arg_1))
                print('当前滑铲速度%s, 是否跳跃%s, cos值: %s,  low_bound:%s, high_bound:%s, result:%s' % (
                    cur_local_velocity.length, not is_fall, cos, low_bound, high_bound, ret))
                print('计算公式min(max(speed * cos(%s) * %s + %s, %s), %s)' % (cos, arg_1[0], arg_1[1], arg_1[2], arg_1[3]))
            # [DEBUG]
            return ret
        else:
            ret = 1 if cos >= 0 else -1
            ret *= arg_2
            # [DEBUG]
            if SHOW_SLIDE_JUMP_DEBUG:
                print('当前滑铲速度%s, 是否跳跃%s, cos值: %s,  low_bound:%s, high_bound:%s, result:%s' % (
                    cur_local_velocity.length, not is_fall, cos, low_bound, high_bound, ret))
            # [DEBUG]
            return ret

    def _OnJumpTo_Jump(self, info):
        jump_time_interval = time.time() - self.owner.last_jump_timestamp
        owner = self.owner
        x, y = owner.GetMoveKeysState()
        cur_speed = self.GetOwnerSpeed()
        yaw_diff = min(3.14159, math.atan2(-x, y))
        jump_velocity = MType.Vector3(0, 0, 1)
        jump_velocity.yaw = yaw_diff
        jump_velocity_world = self.model.Transform.transform_v(jump_velocity)

        slide_extra_jump_speed = 0
        jump_from_slide = info.get('jump_from_slide')
        is_parkour = info.get('is_parkour', False)
        if jump_from_slide:
            slide_extra_jump_speed = self.GetSlideExtraJumpSpeed(yaw_diff, is_parkour=is_parkour)

        jump_velocity.length = cur_speed + slide_extra_jump_speed
        jump_velocity_world.length = cur_speed + slide_extra_jump_speed
        vec_y, is_tired_Jump = owner.GetRealJumpVecY(jump_time_interval, accumulated_time=info.get('accumulated_time', 0), jump_from_slide=jump_from_slide)
        jump_up_acc = MType.Vector3(*owner.GetRealJumpUpAcc())

        self.SetVariableV3('jump_up_acc', jump_up_acc, self.locomotion_graph_id)
        if is_parkour:
            spell_proto = spell_util.GetSpellProto(235)
            parkour_proto = spell_proto.get('parkour', {})
            yaxis_factor = parkour_proto.get('yaxis_factor', 1)
            jump_velocity = MType.Vector3(jump_velocity.x, vec_y * yaxis_factor, jump_velocity.z)
            jump_velocity_world = MType.Vector3(jump_velocity_world.x, vec_y * yaxis_factor, jump_velocity_world.z)
            # 超速显示速度线
            if jump_velocity_world.x**2 + jump_velocity_world.y**2 + jump_velocity_world.z**2 > 49:
                owner.AddCameraEffect(747)
            if yaxis_factor > 1:
                # 如果配表没有加成效果就不要播音效了
                genv.sound_mgr.PlayEventById(769, is_3d=False)
        else:
            jump_velocity = MType.Vector3(jump_velocity.x, vec_y, jump_velocity.z)
            jump_velocity_world = MType.Vector3(jump_velocity_world.x, vec_y, jump_velocity_world.z)
        cross_type = 0 if owner.speed_level < 3 else 1
        self.FireJumpEventForJump()

        self.SetVariableI('jump_move_gait', self.owner.speed_level, self.locomotion_graph_id)
        self.SetVariableI('Tired_Jump', is_tired_Jump, self.locomotion_graph_id)
        self.SetVariableI('jump_dir_int', self.GetRunStartDirection(), self.locomotion_graph_id)
        self.SetVariableV3('jump_dir', jump_velocity, self.locomotion_graph_id)
        self.SetVariableV3('jump_velocity_world', jump_velocity_world, self.locomotion_graph_id)
        self.SetVariableI('CrossType', cross_type, self.locomotion_graph_id)

        # [DEBUG]
        if SHOW_SLIDE_JUMP_DEBUG:
            print('起跳速度jump_dir(%s, %s, %s), 世界速度jump_velocity_world(%s, %s, %s)' % (jump_velocity.x, jump_velocity.y, jump_velocity.z,
                                                          jump_velocity_world.x, jump_velocity_world.y, jump_velocity_world.z))
        # [DEBUG]

        self._last_jump_velocity = jump_velocity
        stand_jump_no_move = 0
        if x == 0 and y == 0:
            stand_jump_no_move = 1
        self.SetVariableI('stand_jump_no_move', stand_jump_no_move, self.locomotion_graph_id)

        if x or y:
            jump_vec = MType.Vector3(0, 0, 1)
            jump_vec.yaw = yaw_diff
            self.SetVariableV3('jump_vec', jump_vec, self.locomotion_graph_id)
        else:
            self.SetVariableV3('jump_vec', MType.Vector3(), self.locomotion_graph_id)

    def JumpLand(self):
        self.SetVariableI('is_left_mantle', 0, self.locomotion_graph_id)
        self.SetVariableI('is_right_mantle', 0, self.locomotion_graph_id)
        self.SetVariableI('is_back_mantle', 0, self.locomotion_graph_id)
        self.FireLocomotionEvent('@JumpEnd')

        is_owner_player = self.owner is genv.player
        self.ChangeMotionState(cconst.UNIT_STATE_IDLE)
        if is_owner_player:
            self.owner.ChangePoseType(ModelPoseType.Stand)

    def JumpBreak(self):
        self.FireLocomotionEvent('@JumpEnd')
        self.FireEvent('@FlyStop', self.locomotion_graph_id)

    def UpdateSlideMoveVec(self):
        cur_speed = self.GetOwnerSpeed()
        x, z = self.owner.GetMoveKeysState()
        yaw_diff = self.GetMoveYawDiff(x, z)
        move_vec = MType.Vector3(0, 0, 1)
        move_vec.yaw = yaw_diff
        move_vec.length = self.GetSlideSpeed(normal_speed=cur_speed)
        self.SetVariableV3('slide_speed', move_vec, self.locomotion_graph_id)

    def OnSlide(self, slide, to_stand=False):
        if slide:
            self.FireEvent('@sliding', self.locomotion_graph_id)
            self.ChangeMotionState(cconst.UNIT_STATE_SLIDE)
        else:
            # self.SetVariableI('sliding_to_stand', to_stand, self.locomotion_graph_id)
            # to_stand and self.FireEvent('sliding_to_stand', self.locomotion_graph_id)
            self.FireEvent('sliding_end', self.locomotion_graph_id)
            self.ChangeMotionState(cconst.UNIT_STATE_IDLE)

    def OnCrouch(self, crouch):
        if crouch:
            self.ChangeMotionState(cconst.UNIT_STATE_CROUCH)
            self.JumpToState(cconst.UNIT_STATE_CROUCH)
        else:
            self.ChangeMotionState(cconst.UNIT_STATE_STAND)
            self.JumpToState(cconst.UNIT_STATE_STAND)

    def OnLadder(self, ladder, next_state=None):
        if ladder:
            self.ChangeMotionState(cconst.UNIT_STATE_CLIMB_LADDER)
            self.JumpToState(cconst.UNIT_STATE_CLIMB_LADDER)
        else:
            self.ChangeMotionState(next_state)
            self._SetModelPoseType(cconst.ModelPoseType.Stand)

    def SetEnableSlope(self, enable):
        self.SetVariableI('enable_slope', enable, self.locomotion_graph_id)

    def OnProne(self, prone, to_stand=True):
        # 进入/离开完成
        if prone:
            self.JumpToState(cconst.UNIT_STATE_PRONE)
        elif to_stand:
            self.JumpToState(cconst.UNIT_STATE_STAND)
            # add protect for locomotion graph id changed
            skeleton = self.GetSkeleton()
            if skeleton:
                self.ResetLocomotionGraphID()
                self.SetVariableI('pose_type', 0, self.locomotion_graph_id)
                self.FireEvent('@stand', self.locomotion_graph_id)
        self.SetCharCtrlProne(prone)

    def OnSwoop(self, swoop, to_stand=False):
        if swoop:
            self.FireEvent('@swooping', self.locomotion_graph_id)
            self.ChangeMotionState(cconst.UNIT_STATE_SWOOP)
        else:
            # self.SetVariableI('sliding_to_stand', to_stand, self.locomotion_graph_id)
            # to_stand and self.FireEvent('sliding_to_stand', self.locomotion_graph_id)
            self.FireEvent('swooping_end', self.locomotion_graph_id)
            self.ChangeMotionState(cconst.UNIT_STATE_IDLE)
        self.SetCharCtrlProne(swoop)

    def _OnJumpTo_Crouch(self, info):
        self._SetModelPoseType(cconst.ModelPoseType.Crouch)
        self.RefreshMoveSpeed()

    def _OnJumpTo_Stand(self, info):
        self._SetModelPoseType(cconst.ModelPoseType.Stand)
        self.RefreshMoveSpeed()

    def CheckExecuteOnDead(self):
        return self.owner.IsInExecute()

    def _OnJumpTo_SpikeTransfer(self, info):
        return

    def _OnJumpTo_Dead(self, info):
        if self.CheckExecuteOnDead():
            return
        # self.add_timer(0.1, functools.partial(self.PlayDie, info))
        self.PlayDie(info)

    def _OnJumpTo_EnterProne(self, info):
        self.FireLocomotionEvent('@Stop')
        self._SetModelPoseType(cconst.ModelPoseType.Prone)

    def _OnJumpTo_ExitProne(self, info):
        self.FireLocomotionEvent('CreepEnd')
        self.FireEvent('@crouch' if self.owner.prone_end_for_crouch else '@stand', self.locomotion_graph_id)
        # self.owner.hand_model.OnCrouch(True)

    def _OnJumpTo_Prone(self, info):
        return

    def _OnJumpTo_KnockDown(self, info):
        # 倒地状态前后左右移动
        # skeleton = self.GetSkeleton()
        # if self.owner is genv.replay_player or not self.IsGraphSyncSlave():
        # if skeleton.GetGraphStackInfo()[-1].layerName != 'Graph/TPS/Locomotion/knockdown.graph':
        self.locomotion_graph_id = self.PushGraph('TPS/Locomotion/knockdown.graph')
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.HighFall, False)

    def _OnJumpTo_RescueOther(self, info):
        graph_id = self.PushGraph('TPS/Locomotion/rescue.graph')
        if graph_id:
            self.need_graph_yaw_control_ids = [graph_id]  # 目前就一个
            self._SetGraphYaw(self.owner.hand_model.yaw)

    def _OnJumpTo_PickUp(self, info):
        self.PopUpperGraph()
        self.PushUpperGraph('TPS/Locomotion/tps_pickup.graph')
        self.ChangeUpperMotionState(cconst.UNIT_STATE_PICKUP)

    def _OnJumpTo_Execution(self, info):
        weapon_case = self.GetCurClientWeaponCase()
        if weapon_case:
            weapon_case.AddHiddenReason(cconst.HIDDEN_REASON_EXECUTE)
            self.HandIkToWeapon(False)
        execution_info = info.get('execute_info') if info else cconst.DEFAULT_EXECUTE_INFO
        if not execution_info:
            execution_info = cconst.DEFAULT_EXECUTE_INFO
        execute_anim_number = execution_info[1]
        graph_file = hero_execution_data.data[execution_info[0]][execute_anim_number].get('execution_graph_path',
                                                                                        'TPS/Execution/general.graph')
        graph_id = self.PushGraph(graph_file)
        if graph_id:
            self.execute_graph_id = graph_id
            self.SetVariableI('IsVictim', 0, graph_id)
            self.SetVariableI('ExcuteAnimNum', execute_anim_number, graph_id)

    def _OnJumpTo_BeExecuted(self, info):
        weapon_case = self.GetCurClientWeaponCase()
        if weapon_case:
            weapon_case.AddHiddenReason(cconst.HIDDEN_REASON_EXECUTE)
        execution_info = info.get('execute_info') if info else cconst.DEFAULT_EXECUTE_INFO
        if not execution_info:
            execution_info = cconst.DEFAULT_EXECUTE_INFO
        execute_anim_number = execution_info[1]
        graph_file = hero_execution_data.data[execution_info[0]][execute_anim_number].get('execution_graph_path',
                                                                                          'TPS/Execution/general.graph')

        graph_id = self.PushGraph(graph_file)
        if graph_id:
            self.execute_graph_id = graph_id
            self.SetVariableI('IsVictim', 1, graph_id)
            self.SetVariableI('ExcuteAnimNum', execute_anim_number, graph_id)

    def _OnJumpTo_OnVehicle(self, info):
        self.PopUpperGraph()
        graph_name = self.owner.vehicle.vehicle_proto.get('character_graph_str', '')
        vehicle_graph_id = self.PushGraph(graph_name)
        if vehicle_graph_id:
            self.vehicle_graph_id = vehicle_graph_id
        # is_left = self.owner.vehicle.IsLeftSeat(self.owner.vehicle_seat)
        # self.SetVariableI('is_left', is_left)
        # is_lean_out = info and info.get('is_lean_out')
        # if is_lean_out:
        #     self.LeanOutOnVehicle(True)
        # is_driver = info.get('is_driver', False) if info else False
        # self.SetVariableI('IsDriver', is_driver, vehicle_graph_id)
        # self.SetVariableI('gun_type', self.owner.GetCurWeaponGunType())
        # cur_client_weapon_case = self.GetCurClientWeaponCase()
        # if cur_client_weapon_case:
        #     anim_data_id = cur_client_weapon_case.equip_proto.get('anim_data_id', 0)
        #     self.SetVariableI('AnimDataID', anim_data_id)
        # vehicle = self.owner and self.owner.vehicle
        # if not vehicle:
        #     return
        # status = vehicle.GetDriveStatus(self.owner.vehicle_seat)
        # self.SetDriveStatus(status, False)
        # self.ChangeMotionState(cconst.UNIT_STATE_DRIVE_VEHICLE if self.owner.vehicle_seat == 0 else
        #                        cconst.UNIT_STATE_ON_VEHICLE)
    
    def AddRagdollContactCallback(self):
        return

    def PlayDie(self, info):
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        if self.locomotion_graph_id != skeleton.GetGraphStack()[1]:
            self.ResetLocomotionGraphID()
            self.PopToLocomotionGraph()
        self.ResetLocomotionGraphParam()
        self.PopHitGraph()
        self.PopUpperGraph()

        SetVariableI = skeleton.SetVariableI
        SetVariableZ = skeleton.SetVariableZ
        SetVariableF = skeleton.SetVariableF

        self.die_graph_id = die_graph_id = self.PushGraph('TPS/Locomotion/die_action.graph')

        SetVariableI(die_graph_id, 'LastMoveGait', self.owner.last_speed_level)

        death_pose = random.randint(0, 1) if DEATH_POSE_TYPE == 2 else DEATH_POSE_TYPE
        SetVariableI(die_graph_id, 'death_pose', death_pose)

        self.FireEvent('RIP', die_graph_id)

        if switches.USE_DEATH_RAGDOLL:
            SetVariableZ(die_graph_id, 'EnableRagdoll', 1)
            SetVariableF(die_graph_id, 'DeathTimer', 0)
            SetVariableF(die_graph_id, 'ragdoll_stiffness_map_x_max', 0.8)
            SetVariableF(die_graph_id, 'ragdoll_muscle_map_x_max', 10)
            SetVariableZ(die_graph_id, 'enable_ragdoll_fadeout', 1)
            self.AddRagdollContactCallback()
            SetVariableZ(self.locomotion_graph_id, 'IsInDead', 1)
            self.skeleton.UseDynamicVisibilityBox = True
        else:
            SetVariableZ(die_graph_id, 'EnableRagdoll', 0)
        
        is_in_parachute = self.CheckIsInParachute()
        SetVariableZ(die_graph_id, 'IsInParachute', is_in_parachute)

        old_state = info.get('old_state', CombatState.ALIVE)
        is_in_fallDown = old_state == CombatState.DYING
        SetVariableZ(die_graph_id, 'IsInFallDown', is_in_fallDown)
        # print_s(f"is_in_fallDown: {is_in_fallDown}, old_state: {old_state}, owner.combat_state: {self.owner.combat_state}", SomePreset.white_fg_red_bg)

        if not info:
            SetVariableF(-1, 'deathIsheadshot', 0)
            SetVariableF(-1, 'deathDirection', 1.0)
        else:
            # print(f"----------> pose_type: {self.pose_type}")
            hit_dir = info.get('hit_dir', None)
            if not hit_dir:
                return
            hit_part = info.get('hit_part', consts.AvatarCollisionBone_UpperBottom).lower()
            deathDirection = self.CalcDeathDir(hit_dir)
            SetVariableF(-1, 'deathIsheadshot', 1 if hit_part == consts.AvatarCollisionBone_Head else 0)
            death_body_part = self.GetDeathBodyPart(hit_part)
            SetVariableF(die_graph_id, 'DeathBodyPart', death_body_part)
            SetVariableF(die_graph_id, 'muscle', 10)
            SetVariableF(die_graph_id, 'ragdoll_stiffness', 4000)
            SetVariableF(die_graph_id, 'ragdoll_damping', 5)
            SetVariableF(-1, 'deathDirection', deathDirection)
            SetVariableF(die_graph_id, 'model_pose_type', self.pose_type)
            vec_hit_dir = self.CalculateHitDir(hit_dir)
            self.SetVariableV3('HitDir', vec_hit_dir, die_graph_id)
            hit_pos = info.get('hit_pos', None)
            hit_point_local_bias = self.CalculateHitPointLocalBias(hit_pos)
            self.SetVariableV3('HitPointLocalBias', hit_point_local_bias, die_graph_id)
            self.FireEvent('OnHit', self.die_graph_id)
            kill_anim = info.get('SpecialDieActionPose')
            if kill_anim:
                pass
                # TODO: 这里有问题，会传没有的动画，先屏蔽 
                # self.SetVariableS('SpecialDieActionPose', kill_anim, die_graph_id)
                # SetVariableI(die_graph_id, 'IsSpecialDieAction', True)
        SetVariableI(die_graph_id, 'Isdeathtest', switches.ENABLE_STAND_DIE)
        self.ChangeMotionState(cconst.UNIT_STATE_DEAD)
    
    def CheckIsInParachute(self):
        return (self.owner.para_stage == ParaAvatarStage.FreeFall or
                self.owner.para_stage == ParaAvatarStage.FreeFallWithWeapon or
                self.owner.para_stage == ParaAvatarStage.OpenParachute or
                self.owner.para_stage == ParaAvatarStage.CloseParachute or
                self.owner.para_stage == ParaAvatarStage.Parachute)
    
    def CalculateHitDir(self, hit_dir):
        if not hit_dir:
            return MType.Vector3(0, 0, 0)

        vec_hit_dir = MType.Vector3(*hit_dir)
        vec_hit_dir.normalize()
        vec_hit_dir *= 360
        return vec_hit_dir
    
    def GetDeathBodyPart(self, hit_part):
        # print(f"----------> hit_part: {hit_part}")
        # 1.头
        if hit_part == consts.AvatarCollisionBone_Head:
            # print("----------> 打中头")
            return 0
        # 2.胸
        elif hit_part in [consts.AvatarCollisionBone_UpperTop, consts.AvatarCollisionBone_UpperBottom, consts.AvatarCollisionBone_Lower]:
            # print("----------> 打中胸")
            return 1
        # 3.下肢
        elif hit_part in [consts.AvatarCollisionBone_Limbs_L_Thigh, consts.AvatarCollisionBone_Limbs_L_Calf, consts.AvatarCollisionBone_Limbs_R_Thigh, consts.AvatarCollisionBone_Limbs_R_Calf]:
            # print("----------> 打中下肢")
            return 2
        else:
            # print("----------> 打中其他, 默认为胸")
            return 1
    
    def CalculateHitPointLocalBias(self, hit_pos):
        if not hit_pos:
            return MType.Vector3(0, 0, 0)
        hit_pos = MType.Vector3(*hit_pos)
        hit_pos_local = self.model.Transform.inverse.transform_p(hit_pos)
        print(f"hit_pos_local: {hit_pos_local}")
        hit_point_local_bias = hit_pos_local
        return hit_point_local_bias

    def LeaveVehicle(self):
        if self.vehicle_graph_id:
            self.PopGraph(self.vehicle_graph_id)
            self.vehicle_graph_id = None
        self.ChangeMotionState(cconst.UNIT_STATE_IDLE)

    def PopExecuteGraph(self):
        if self.execute_graph_id:
            self.PopGraph(self.execute_graph_id)
            self.execute_graph_id = None
        if weapon_case := self.GetCurClientWeaponCase():
            weapon_case.RemoveHiddenReason(cconst.HIDDEN_REASON_EXECUTE)

    def CalcDeathDir(self, hit_dir):
        if not hit_dir:
            return 1.0
        hit_dir = formula.Tuple(hit_dir)

        a = formula.YawToVector(self.yaw)[::2]
        b = hit_dir[::2]
        if formula.Length2D(b) < 0.01:
            return 1.0

        rad = formula.RotateAngle2D(a, b)  # x、z轴是左手系，正向为顺时针
        unit = math.pi / 4
        if -unit <= rad < unit:
            ret = 1.0  # 前
        elif unit <= rad < unit * 3:
            ret = 3.0  # 右
        elif -unit * 3 <= rad < -unit:
            ret = 2.0  # 左
        else:
            ret = 0.0  # 后
        return ret
    
    def SetShaderGraphParameters(self, params):
        for key, value in params.items():
            self.SetShaderGraphParameter(key, value)
            
    def StartDissolve(self, info):
        pos = info.get('hit_pos', self.position)
        params = {
            'DissolveValue': True,
            'DissolveStartTime': MEngine.GetGameTime(),
            'DissolveSpeed': cconst.COIN_DISSOLVE_SPEED,
            'DissolveRadius': cconst.COIN_DISSOLVE_RADIUS,
            'HitPositionX': pos[0],
            'HitPositionY': pos[1],
            'HitPositionZ': pos[2]
        }
        self.SetShaderGraphParameters(params)

    def PlayDeadEffectInWorld(self, eff_data):
        eid = eff_data.get('id', 0)
        if eid == 0:
            return
        world_pos = eff_data.get('world_pos', (0, 0, 0))
        return effect_util.WrapperPlayEffectInWorld(eid, MType.Vector3(*world_pos), cconst.COIN_VG_STAY_TIME)
        
    def GetDeadEffectTransformByDir(self, pos, dir, theta_p=0, theta_r=0):
        f = dir
        f.length = 1
        right = f.cross(MType.Vector3(0, 1, 0))
        right.length = 1
        # 转一下
        f = formula.Rotate(f, right, theta_p)
        up = right.cross(f)
        up.length = 1
        # 转一下
        f = formula.Rotate(f, up, theta_r)
        # # 重新计算r
        right = f.cross(up)
        right.length = 1
        m = MType.Matrix4x3()
        m.x_axis, m.y_axis, m.z_axis = right, -f, -up
        m.translation = pos
        # [DEBUG]
        if switches.DEBUG_COIN_EFFECT:
            import MDebug
            px = MDebug.Polyline()
            px.points = [pos, pos + m.x_axis]
            px.color = MType.Vector3(1, 0, 0)
            self.debug_coin.append(px)
            py = MDebug.Polyline()
            py.points = [pos, pos + m.y_axis]
            py.color = MType.Vector3(0, 1, 0)
            self.debug_coin.append(py)
            pz = MDebug.Polyline()
            pz.points = [pos, pos + m.z_axis]
            pz.color = MType.Vector3(0, 0, 1)
            self.debug_coin.append(pz)
        # [DEBUG]
        return m

    def CheckMatchConditions(self):
        if not switches.G101_DEATH_COIN_EFFECT:
            return False
        if not genv.space or not genv.space.game_logic:
            return False
        match_type = genv.space.game_logic.show_match_type
        match_data = match_client_data.data.get(match_type, {})
        return match_data.get("show_coin", False)

    def PlayVgEffect(self, eid, info):
        eid = self.PlayDeadEffectInWorld({
            'id': eid,
        })
        shoot_speed = MObject.CreateObject("ParticleParameterConstantVector3")
        shoot_speed.Value = MType.Vector3(0, 0, 0)
        if 'hit_dir' in info:
            hit_dir = info['hit_dir']
            shoot_speed.Value = MType.Vector3(*hit_dir)
        char_speed = MObject.CreateObject("ParticleParameterConstantVector3")
        char_speed.Value = self.GetVariableV3('SYS_ENTITY_VELOCITY')
        e_ent = MCharacter.GetEffectEntity(eid)

        hit_pos = info.get('hit_pos', (0, 0, 0))
        tg_pos = info.get('target_pos', (0, 0, 0))
        hit_pos_v4 = "(" + str(hit_pos[0] + tg_pos[0]) + "," + str(hit_pos[1] + tg_pos[1]) + "," + str(hit_pos[2] + tg_pos[2]) + ", 0)"

        player = replay_util.GetPlayer()
        if self.owner is player:
            genv.sound_mgr.PlayEventById(audio_const.SoundEventID.DeadEffect_1p, self.position)
        else:
            genv.sound_mgr.PlayEventById(audio_const.SoundEventID.DeadEffect_3p, self.position)
        if e_ent and self.model:
            e_ent.Attach(self.model)
            for prim in e_ent.Primitives:
                prim.AddParameter("shoot_speed", shoot_speed)
                prim.AddParameter("char_speed", char_speed)
                # prim.SetParticleShaderParameter(-1, "LightColor0", "(0,256,0,0)")
                # SetParticleShaderParameter方法会开辟新的动态mateiral所以一帧只能调一次？
                prim.SetParticleShaderParameter(-1, "LightPos0_MessiahUnit", hit_pos_v4)

    def PlayBurstEffect(self, info):
        # [DEBUG]
        if switches.DEBUG_COIN_EFFECT_BURST:
            return
        hit_pos_v4 = "(0,0,0,0)"
        eid = None
        # [DEBUG]
        if 'hit_dir' in info and 'hit_pos' in info:
            hit_part = info.get('hit_part', consts.AvatarCollisionBone_UpperBottom).lower()
            hit_pos = info['hit_pos']
            hit_dir = info['hit_dir']

            tg_pos = info.get('target_pos', (0, 0, 0))
            hit_pos_v4 = "(" + str(hit_pos[0] + tg_pos[0]) + "," + str(hit_pos[1] + tg_pos[1]) + "," + str(hit_pos[2] + tg_pos[2]) + ", 0)"

            if hit_part == consts.AvatarCollisionBone_Head:
                # 爆头向上喷 直接原喷
                eid = self.PlayDeadEffectInWorld({
                    'id': 2477,
                    'world_pos': hit_pos,
                })
            else:
                # 其他旋转下再喷
                s_pos = (self.position[0], info['hit_pos'][1], self.position[2])
                cross_v = formula.Cross3D(info['hit_dir'], formula.Substract3D(s_pos, info['hit_pos']))
                dir = 1 if cross_v[1] > 0 else -1
                m = self.GetDeadEffectTransformByDir(MType.Vector3(*hit_pos), -MType.Vector3(*hit_dir), cconst.COIN_EFFECT_PITCH, dir * cconst.COIN_EFFECT_ROLL)
                eid = effect_util.WrapperPlayEffectInWorld2(2476, m, -1)
        if eid is not None:
            e_ent = MCharacter.GetEffectEntity(eid)
            for prim in e_ent.Primitives:
                prim.SetParticleShaderParameter(-1, "LightPos0_MessiahUnit", hit_pos_v4)
                # self.add_timer(0.05, lambda p=prim: p.SetParticleShaderParameter(-1, "LightColor0", "(0,256,0,0)"))


    def PlayDeadCoinEffect(self, info):
        # 移除敌人描边
        self.is_outline_dynamic = False
        self.RemoveTechState(cconst.ENEMY_TECH_STATE_CONFIG_ID)
        if not self.CheckMatchConditions():
            return
        if 'hit_pos' in info and 'target_pos' in info:
            hit_offset = formula.Substract3D(info['hit_pos'], info['target_pos'])
            info['hit_pos'] = formula.Add3D(formula.Substract3D(self.position, info['target_pos']), hit_offset)
        self.debug_coin = []
        self.SetShaderGraphParameter('Clipping', True)
        self.debug_timer = self.add_timer(0.01, functools.partial(self.StartDissolve, info))
        # VG 特效
        self.add_timer(cconst.COIN_VG_DELAY, functools.partial(self.PlayVgEffect, 2479, info))
        # 喷溅特效 射出方向
        self.add_timer(cconst.COIN_BURST_DELAY, functools.partial(self.PlayBurstEffect, info))
        # self.PlayBurstEffect(info)
        if switches.G101_DEATH_COIN_EFFECT_TARGET and 'hit_pos' in info:
            self.PlayDeadEffectInWorld({
                'id': 2478,
                'world_pos': info['hit_pos'],
            })
    
    def PlaySkinDeadAnimAndEffect(self, info):
        if self.owner.final_kill_info:
            final_kill_info = cPickle.loads(self.owner.final_kill_info)
            guise_item_id = final_kill_info.get('guise_item_id')
            if item_feature_data.data.get(guise_item_id, {}).get('kill_effect'): # yo
                guise_template_id = lobby_item_data.data.get(guise_item_id, {}).get('guise', {}).get('template_id')
                guise_proto = gun_skin_template_data.data[guise_template_id]
                kill_fx = guise_proto.get('kill_fx')
                kill_anim = guise_proto.get('SpecialDieActionPose')
                can_play = (not kill_fx) or genv.dlc_manager.CheckEffectDlcExists(kill_fx)
                if kill_anim and can_play:
                    info['SpecialDieActionPose'] = kill_anim # 死亡动画传过去
                if kill_fx and can_play:
                    self.PlaySkinDeadEffect(kill_fx, info)

    def PlaySkinDeadEffect(self, sfx, info):
        # 播放皮肤的死亡特效
        self.ChangeShaderGraphMaterialById(7)
        self.SetShaderGraphParameter('cFXStartTime', MEngine.GetGameTime())
        self.SetShaderGraphParameter('cFXNmlSpeed', str(1))
        trans = self.GetBoneWorldTransform('biped Spine2')
        if trans:
            if 'hit_dir' in info:
                hit_dir = info['hit_dir']
                yaw = formula.Vector3DToYaw(hit_dir)
                trans.set_pitch_yaw_roll(0, yaw, 0)
            effect_util.WrapperPlayEffectInWorld2(sfx, trans, insure_play=True)

    def OnDead(self, info):
        if not self.isValid():
            return
        self.PlayDeadCoinEffect(info)
        self.PlaySkinDeadAnimAndEffect(info)
        self.RemoveHiddenReason(cconst.HIDDEN_REASON_COMMON)
        self.UnableCharCtrlWithReason(ModelCharCtrlReason.DEAD)
        if self.owner.IsPlayerCombatAvatar:
            self.SetCustomRenderSet(cconst.RENDER_SET_DEFAULT)
        # else:
        #   self.UnableFilterWithReason(ModelFilterReason.DEAD)
        self.JumpToState(cconst.UNIT_STATE_DEAD, info)
        owner = self.owner
        self.hidden_timer and owner.cancel_timer(self.hidden_timer)
        if owner.IsInExecute():
            # self.AddHiddenReason(cconst.HIDDEN_REASON_GHOST)
            pass
        elif info and info.get('force_no_action'):
            self.AddHiddenReason(cconst.HIDDEN_REASON_GHOST)
        else:
            self.hidden_timer = owner.add_timer(3.0, self.OnDelayHide)

    def OnDelayHide(self):
        # 隐藏的时候， 物理也要设一下
        self.AddHiddenReason(cconst.HIDDEN_REASON_GHOST)
        if self.die_graph_id:
            self.FireEvent('OnDeadOver', self.die_graph_id)

    def OnReborn(self, info):
        if not self.isValid():
            return
        owner = self.owner
        self.RaiseCurWeapon()
        if owner.IsPlayerCombatAvatar:
            self.SetCustomRenderSet(cconst.RENDER_SET_SHADOW_PROXY)

        # 先设置位置 解决被拉
        if info:
            self.position = (info[0], info[1], info[2])
            self.yaw = info[-1]
        if not (owner.IsPlayerCombatAvatar or (owner.IsRobotCombatAvatar and owner.is_controlled)):
            self.EnableFilterWithReason(ModelFilterReason.DEAD)

        self.EnableGraphSyncSlave(True)
        self.PopToLocomotionGraph()
        self.ResetShaderGraphMaterial()
        self.EnableCharCtrlWithReason(ModelCharCtrlReason.DEAD)
        self.ChangeMotionState(cconst.UNIT_STATE_IDLE)
        self.RemoveHiddenReason(cconst.HIDDEN_REASON_GHOST)
        self.hidden_timer and owner.cancel_timer(self.hidden_timer)
        self.hidden_timer = None
        self.ClearCharctrlHeightReason()
        self.SetVariableZ('IsInDead', 0, self.locomotion_graph_id)
        self.skeleton.UseDynamicVisibilityBox = False

    def OnGhost(self):
        self.hidden_timer and self.owner.cancel_timer(self.hidden_timer)
        self.hidden_timer = None
        if self.owner.IsInExecute():
            return
        self.AddHiddenReason(cconst.HIDDEN_REASON_GHOST)

    def OnHit(self, info):
        # 进入支持反复触发 hit 事件实现来回受击效果
        hit_part = info.get('hit_part', '').lower()
        hit_type = self.CalculateHitType(hit_part)
        is_run = self.GetVariableI('is_run', self.locomotion_graph_id)
        is_run = 0 if not is_run else is_run
        if self.hit_graph_id:
            self.FireEvent('@hit', self.hit_graph_id)
        else:
            self.hit_graph_id = self.PushGraph('TPS/Locomotion/tps_hit.graph')
        hit_dir = info.get('hit_dir', None)
        if hit_dir:
            hit_dir_index = self.CalcDeathDir(hit_dir)
            self.SetVariableF('hit_dir', hit_dir_index, self.hit_graph_id)
        self.SetVariableI('hit_type', hit_type, self.hit_graph_id)
        self.SetVariableI('is_run', is_run, self.hit_graph_id)
        self.SetVariableI('gun_type', self.gun_type, self.hit_graph_id)
        self.SetVariableI('pose_type', self.pose_type, self.hit_graph_id)
        self.AnimArmWeapon()
    
    def CalculateHitType(self, hit_part):
        # 0: 头, 1: 胸, 2: 打中右腿, 3: 打中左腿
        if not hit_part:
            # print("----------> 没有hit_part, 默认被打中胸")
            return 1
        if hit_part == consts.AvatarCollisionBone_Head:
            # print("----------> 打中头")
            return 0
        elif hit_part in [consts.AvatarCollisionBone_UpperTop, consts.AvatarCollisionBone_UpperBottom, consts.AvatarCollisionBone_Lower]:
            # print("----------> 打中胸")
            return 1
        elif hit_part in [consts.AvatarCollisionBone_Limbs_R_Thigh, consts.AvatarCollisionBone_Limbs_R_Calf]:
            # print("----------> 打中右腿")
            return 2
        elif hit_part in [consts.AvatarCollisionBone_Limbs_L_Thigh, consts.AvatarCollisionBone_Limbs_L_Calf]:
            # print("----------> 打中左腿")
            return 3
        else:
            # print("----------> 打中其他, 默认打中胸")
            return 1

    def OnPull(self, info):
        if self.pull_graph_id:
            return
        else:
            self.pull_graph_id = self.PushGraph('TPS/Locomotion/tps_knockup.graph')
        hit_dir = info.get('hit_dir', (0, 0, 0))
        self.SetVariableV3('hit_dir', MType.Vector3(*hit_dir), self.pull_graph_id)

    def OnHitSamurai(self, info):
        # 武士格挡效果用push graph来先行
        if self.samurai_hit_graph_id:
            self.FireEvent('@under_attack', self.samurai_hit_graph_id)
        else:
            self.samurai_hit_graph_id = self.PushGraph('TPS/Spell/tps_skill_samurai_hit.graph')

    def OnKnockDown(self, is_knock_down):
        self.PopHitGraph()
        if is_knock_down:
            self.JumpToState(cconst.UNIT_STATE_KNOCK_DOWN)
            self.ResizeCharctrHeightForReason(cconst.CharctrlResizeHeightReason.Dying)
        else:
            # Beadle：修正复活后依然倒地的问题
            self.FireLocomotionEvent('ActionEnd')
            self.FireLocomotionEvent('@Save')
            self.RemoveCharctrlHeightReason(cconst.CharctrlResizeHeightReason.Dying)
            self.ResetLocomotionGraphID()

    def OnSaveEnd(self):
        skeleton = self.GetSkeleton()
        if not skeleton:
            return

        self.ResetLocomotionGraphID()
        self.PopToLocomotionGraph()

        self.AnimArmWeapon(self.gun_type)
        self.ChangeUpperMotionState('')

    def StartRescueOther(self, rescue):
        if rescue:
            self.DropCurWeapon()
            self.ResetLocomotionGraphParam()
            self.JumpToState(cconst.UNIT_STATE_RESCUE)
            self.ResizeCharctrHeightForReason(cconst.CharctrlResizeHeightReason.Rescue)
        else:
            self._CheckPopNeedYawGraphId()
            self.PopToLocomotionGraph()
            self.RaiseCurWeapon()
            owner = self.owner
            if not owner._CheckBeforeStand():
                owner.DoCrouch(True)
            self.RemoveCharctrlHeightReason(cconst.CharctrlResizeHeightReason.Rescue)
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.RescueOther, rescue)

    def _CheckPopNeedYawGraphId(self):
        if self.need_graph_yaw_control_ids:
            self.need_graph_yaw_control_ids.pop()

    def RescueOtherFinish(self):
        self.FireEvent('rescue_end')

    def RefreshMoveSpeed(self):
        pass

    def GetOwnerSpeed(self):
        owner = self.owner
        if not owner:
            return 0
        speed_level = owner.speed_level
        if not owner.IsPlayerCombatAvatar and not owner.IsRobotCombatAvatar and owner.combat_state == CombatState.DYING:
            speed_level = cconst.MoveSpeedLevel.KnockDown
        move_dir = owner.move_dir
        return self._GetOwnerSpeed(owner, speed_level, move_dir)

    def _GetOwnerSpeed(self, owner, speed_level, move_dir):
        # 拆出来给其他地方调用
        move_speed_data = moving_speed_data.data.get(move_dir)
        if not move_speed_data:
            return 0
        move_speed_data = move_speed_data.get

        # normal_speed_factor, sprint_speed_factor, supersprint_speed_factor, \
        #     equip_speed_factor, global_speed_factor, fire_jog_speed_factor, fire_walk_speed_factor = 1, 1, 1, 1, 1, 1, 1
        cur_weapon_case = owner.GetCurWeaponCase(owner.is_fps_avatar)
        if not cur_weapon_case:
            return 0
        weapon_attr_getter = cur_weapon_case.GetWeaponAttrValue
        equip_speed_factor = weapon_attr_getter('equip_movement_speed', default=1)
        global_speed_factor = 1 + owner.combat_attr.CalResult(1.0, 'global_movement_speed')

        speed_level_enum = cconst.MoveSpeedLevel
        pose_type = owner.pose_type
        motion_state = self.motion_state

        if motion_state == cconst.UNIT_STATE_ENTER_PRONE:
            self.normal_speed = move_speed_data('enter_lie_speed', 0)
        elif motion_state == cconst.UNIT_STATE_EXIT_PRONE:
            self.normal_speed = move_speed_data('quit_lie_speed', 0)
        elif motion_state == cconst.UNIT_STATE_SWIM:
            if speed_level == speed_level_enum.Sprint or speed_level == speed_level_enum.SuperSprint:
                self.normal_speed = move_speed_data('swim_sprint_speed', 0)
            else:
                self.normal_speed = move_speed_data('swim_surface_speed', 0)
        elif pose_type == cconst.ModelPoseType.Prone:
            if self.owner.is_shooting:
                self.normal_speed = 0
            else:
                self.normal_speed = move_speed_data('lie_speed', 0) if not owner.is_ads else move_speed_data('lie_walk_speed', 0)
        elif speed_level == speed_level_enum.Idle:
            self.normal_speed = 0.0
        elif speed_level == speed_level_enum.Walk:
            self.normal_speed = move_speed_data('crouch_walk_speed' if pose_type == ModelPoseType.Crouch else 'walk_speed', 0)
            self.normal_speed *= weapon_attr_getter('ads_movement_speed' if owner.is_ads else 'movement_speed', default=1)
            if owner.is_shooting:
                self.normal_speed *= weapon_attr_getter('fire_walk_movement_speed_factor', default=1)
        elif speed_level == speed_level_enum.Jog:
            self.normal_speed = move_speed_data('crouch_jog_speed' if pose_type == ModelPoseType.Crouch else 'jog_speed', 0)
            self.normal_speed *= weapon_attr_getter('ads_movement_speed' if owner.is_ads else 'movement_speed', default=1)
            if owner.is_shooting:
                self.normal_speed *= weapon_attr_getter('fire_movement_speed_factor', default=1)
        elif speed_level == speed_level_enum.Sprint:
            self.normal_speed = move_speed_data('sprint_speed', 0)
            self.normal_speed *= weapon_attr_getter('sprint_movement_speed_factor', default=1)
        elif speed_level == speed_level_enum.SuperSprint:
            self.normal_speed = move_speed_data('super_sprint_speed', 0)
            self.normal_speed *= weapon_attr_getter('supersprint_movement_speed_factor', default=1)
        elif speed_level == speed_level_enum.KnockDown:
            self.normal_speed = move_speed_data('knockdown_speed', 0)
        return self.normal_speed * equip_speed_factor * global_speed_factor * genv.debug_speedup_factor * self.hold_breath_speed_scale

    def GetMoveYawDiff(self, x, z):
        owner = self.owner
        if not owner:
            return 0

        return min(3.141, round(math.atan2(-x, z), 2))  # 6.4版本临时关闭这个奔跑限制角度的功能，后续版本加回来

        speed_level = owner.speed_level
        if speed_level in (cconst.MoveSpeedLevel.Sprint, cconst.MoveSpeedLevel.SuperSprint):
            return min(3.141, round(math.atan2(-x * math.tan(cconst.SprintMoveSidelingRadian), z), 2))
        else:
            return min(3.141, round(math.atan2(-x, z), 2))

    def GetSlideSpeed(self, normal_speed=None):
        owner = self.owner
        if not owner:
            return normal_speed
        cur_weapon_case = owner.GetCurWeaponCase()
        if not cur_weapon_case:
            return normal_speed
        if not normal_speed:
            normal_speed = self.GetOwnerSpeed()
        slide_speed_factor = cur_weapon_case.GetWeaponAttrValue('slide_speed_factor', default=1)
        slide_speed = normal_speed * slide_speed_factor
        repeat_slide_interval = owner.last_slide_timestamp - owner.exit_slide_timestamp
        if 0 < repeat_slide_interval < owner.design_combat_properties.get("full_slide_interval", 2.0) if cconst.FULL_SLIDE_INTERVAL_ENABLE else 0.8:
            slide_speed = moving_speed_data.data[8]['sprint_speed']
            # @sjh 俊凯给了一个magic number
            slide_speed *= 0.6

        return slide_speed

    def GetStopYawOffset(self):
        if not self.model or not self.model.CharCtrl:
            return 0
        velocity = self.model.CharCtrl.RealVel
        if velocity.length <= 0.01:
            return 0
        yawOffset = formula.AngleSub(velocity.yaw, self.yaw)
        yawOffset = round(max(min(yawOffset, 3.14), -3.14), 2)
        return yawOffset

# region weapon

    def GetCurClientWeaponCase(self):
        return self.GetWeaponCase(self.cur_client_weapon_guid)

    def GetCurClientWeaponGunType(self):
        cur_client_weapon_case = self.GetCurClientWeaponCase()
        if not cur_client_weapon_case:
            return 0
        return weapon_util.GetWeaponGunType(cur_client_weapon_case.weapon_id)

    def GetCurClientLeftHandWeaponCase(self):
        return self.GetWeaponCase(self.cur_client_lefthand_weapon_guid)

    def GetWeaponCase(self, guid):
        if guid in self.weapon_list:
            if guid not in self.weapon_dict:
                self.AddWeapon(guid)
        return self.weapon_dict.get(guid)

    def RefreshWeaponList(self):
        if not self.isValid():
            return
        weapon_list = self.owner.GetWeaponList()
        # 新增
        self.weapon_list = weapon_list
        # need_add = []
        # for weapon in weapon_list:
        #     if weapon in self.weapon_dict:
        #         continue
        #     need_add.append(weapon)
        # 删除
        need_del = []
        for weapon in list(self.weapon_dict.keys()):
            if weapon in weapon_list:
                continue
            # 删除
            need_del.append(weapon)

        for cur_del in need_del:
            self.DestroyWeapon(cur_del)

        # for cur_add in need_add:
        #     self.AddWeapon(cur_add)
        
        # self.RefreshTpsWeaponsVisibility()
    
    def RefreshTpsWeaponsVisibility(self):
        if not self.owner.IsCombatAvatar:
            return
        player = replay_util.GetPlayer()
        if self.owner is player:
            for weapon in self.weapon_dict.values():
                if player.is_fps_mode:
                    weapon.AddHiddenReason(cconst.HIDDEN_REASON_3P_HIDE_SHADOW)
                else:
                    weapon.RemoveHiddenReason(cconst.HIDDEN_REASON_3P_HIDE_SHADOW)

    def AddWeapon(self, guid):
        equip = self.owner.backpack.Get(guid)
        equip_id = equip.equip_id
        equip_guid = equip.guid
        skin_id = equip.skin_template_id
        guise_id = equip.guise_template_id
        if not equip_id or not equip_guid:
            return
        if equip_guid in self.weapon_dict and self.weapon_dict[equip_guid].is_match_server:
            return
        extra = {
            "use_highlevel": gui.is_pc and genv.highlevel_tps_weapon
        }
        weapon_case = EquipCaseFactory.Create(equip_id, self.owner, equip_guid, equip_id, skin_id, guise_id, is_fps_weapon=False, extra=extra)
        if not weapon_case:
            return
        weapon_case.InitWeapon()
        self.weapon_dict[guid] = weapon_case
        # 判断车上隐藏
        if self.owner.vehicle_id and not self.owner.is_lean_out:
            weapon_case.AddHiddenReason(cconst.HIDDEN_REASON_VEHICLE)

    def ChangeWeapon(self):
        if not self.isValid():
            return
        self.DropCurWeapon()

    def ChangeLeftHandWeapon(self):
        if not self.isValid():
            return
        self.DropCurLeftHandWeapon()

    def _CheckDropClientWeapon(self):
        owner = self.owner
        if not owner:
            return
        cur_weapon_guid = owner.cur_spec_weapon_guid if owner.cur_spec_weapon_guid else owner.cur_weapon_guid
        if self.cur_client_weapon_guid and self.cur_client_weapon_guid != cur_weapon_guid:
            self.ForceDropWeapon(self.cur_client_weapon_guid)
        if self.cur_client_lefthand_weapon_guid and self.cur_client_lefthand_weapon_guid != self.owner.cur_lefthand_weapon_guid:
            self.ForceDropWeapon(self.cur_client_lefthand_weapon_guid)

    def ForceDropWeapon(self, guid):
        # 强制把客户端当前的武器卸下 无动作 只清状态
        # 处理客户端和服务器不同步时候的异常情况
        if not self.isValid():
            return
        self.AnimArmWeapon(0)
        owner = self.owner
        if owner.is_fps_avatar and owner.is_alive:
            self.SetGraphDynamicAnim()
        cur_weapon = self.GetWeaponCase(guid)
        cur_weapon and cur_weapon.ForceDropWeaponForAvatar()
        if guid == self.cur_client_lefthand_weapon_guid:
            self.cur_client_lefthand_weapon_guid = ''
        elif guid == self.cur_client_weapon_guid:
            self.old_cur_client_weapon_guid = self.cur_client_weapon_guid
            self.cur_client_weapon_guid = ''

    def DropCurWeapon(self, guid=None):
        if not self.isValid():
            return
        if self.owner.is_on_sky:
            self.SetOpenFireAction(4)
            if guid:
                cur_weapon = self.GetWeaponCase(guid)
            else:
                cur_weapon = self.owner.GetCurWeaponCase(is_fps_weapon=False)
            cur_weapon and cur_weapon.weapon_model and cur_weapon.weapon_model.AttachWeaponToAvatarBack()
            return
        if guid:
            cur_weapon = self.GetWeaponCase(guid)
        else:
            cur_weapon = self.owner.GetCurWeaponCase(is_fps_weapon=False)
        if cur_weapon:
            cur_weapon.DropWeaponForAvatar()
            if cur_weapon.weapon_type != consts.EquipmentType.MELEE and not self.vehicle_graph_id:
                self.JumpToState(cconst.UNIT_STATE_DROPWEAPON, {'weapon_id': cur_weapon.weapon_id})

    def DropCurSpecWeapon(self):
        if not self.isValid():
            return
        spec_weapon = self.owner.GetCurSpecWeaponCase(is_fps_weapon=False)
        spec_weapon and spec_weapon.DropWeaponForAvatar()

    def DropCurLeftHandWeapon(self):
        if not self.isValid():
            return
        lefthand_weapon = self.owner.GetCurLeftHandWeaponCase(is_fps_weapon=False)
        lefthand_weapon and lefthand_weapon.DropWeaponForAvatar()

    def ForceDropLeftHandWeapon(self, old):
        lefthand_weapon = self.owner.GetWeaponCase(old, is_fps_weapon=False)
        lefthand_weapon and lefthand_weapon.ForceDropWeaponForAvatar()

    def RaiseCurWeapon(self, attach_weapon=True):
        # attach_weapon是为了处理3P切枪时武器挂接和切枪动作分离用的
        # tps_leave_combat.graph里cue_signal_switch_weapon触发的raiseWeapon不处理武器挂接逻辑
        # 交给cue_signal_attach_weapon处理
        if not self.isValid():
            return
        if self.vehicle_graph_id and not self.owner.is_lean_out:
            return
        if self.motion_state in (cconst.UNIT_STATE_EXECUTION, cconst.UNIT_STATE_BE_EXECUTED):
            return
        if self.parachute_graph_id:
            if self.owner and self.owner.para_stage != ParaAvatarStage.FreeFallWithWeapon:
                return
            self.SetOpenFireAction(3)
            if not self.isValid():
                return
        # if self.owner.combat_state != CombatState.ALIVE:
        #     # 倒地时拿武器动作了
        #     return
        self._CheckDropClientWeapon()
        self.cur_client_weapon_guid = self.owner.cur_weapon_guid
        new_weapon = self.owner.GetCurWeaponCase(is_fps_weapon=False)
        new_weapon and new_weapon.RaiseWeaponForAvatar()
        if new_weapon and not self.vehicle_graph_id:
            if new_weapon.CheckIfQuickRaise(self.old_cur_client_weapon_guid):
                self.AnimArmWeapon()
                return
            self.JumpToState(cconst.UNIT_STATE_RAISEWEAPON, {'weapon_id': new_weapon.weapon_id})
        if self.parachute_graph_id and self.owner.IsPlayerCombatAvatar:
            self.CheckEnableHandIKToWeapon()

    def RaiseCurSpecWeapon(self, take_key_mode=cconst.UseItemMode.ITEM_DOWN):
        if not self.isValid():
            return
        self._CheckDropClientWeapon()
        self.cur_client_weapon_guid = self.owner.cur_spec_weapon_guid
        spec_weapon = self.owner.GetCurSpecWeaponCase(is_fps_weapon=False)
        spec_weapon and spec_weapon.RaiseWeaponForAvatar()

    def RaiseCurLeftHandWeapon(self):
        if not self.isValid():
            return
        self._CheckDropClientWeapon()
        self.cur_client_lefthand_weapon_guid = self.owner.cur_lefthand_weapon_guid
        lefthand_weapon = self.owner.GetCurLeftHandWeaponCase(is_fps_weapon=False)
        lefthand_weapon and lefthand_weapon.RaiseWeaponForAvatar()

    def DestroyWeapon(self, guid):
        if guid not in self.weapon_dict:
            return
        weapon_case = self.weapon_dict[guid]
        del self.weapon_dict[guid]
        weapon_case.Destroy()

    def AnimArmWeapon(self, gun_type=None, left_hand_case_gun_type=False):
        gun_type = gun_type if gun_type is not None else self.gun_type
        # self.SetVariableI('gun_type', gun_type, self.locomotion_graph_id)
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        graph_stack = skeleton.GetGraphStack()
        length = len(graph_stack)
        if not graph_stack or length < 2:
            return
        gid = graph_stack[1]
        SetVariableI = skeleton.SetVariableI
        SetVariableI(gid, 'gun_type', gun_type)
        if left_hand_case_gun_type and length > 2:
            SetVariableI(graph_stack[2], 'gun_type', gun_type)
        cur_client_weapon_case = self.GetCurClientWeaponCase()
        if not cur_client_weapon_case:
            return
        equip_proto_getter = cur_client_weapon_case.equip_proto.get
        SetVariableI(gid, 'equip_id', cur_client_weapon_case.weapon_id)
        SetVariableI(gid, 'TpsMoveType', equip_proto_getter('tps_move_action_type', 0))
        anim_data_id = equip_proto_getter('anim_data_id', 0)
        SetVariableI(gid, 'AnimDataID', anim_data_id)
        if left_hand_case_gun_type and length > 2:
            SetVariableI(graph_stack[2], 'AnimDataID', anim_data_id)
        SetVariableI(gid, 'IsTpsBonefilterArm', equip_proto_getter('is_tps_bonefilter_arm', 0))

    def SetIsDualWeapon(self, is_dual):
        self.SetVariableI('ACTOR_TPS_IsDual', is_dual, self.locomotion_graph_id)

    def SetGraphDynamicAnim(self):
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        anim_id = weapon_case.equip_proto.get('anim_data_id', None)
        if not anim_id:
            return
        anim_data = tps_gun_anim_data.data.get(anim_id, {})
        if not anim_data:
            return
        SetVariableS = skeleton.SetVariableS
        graph_stack = skeleton.GetGraphStack()
        if not graph_stack or len(graph_stack) < 2:
            return
        stackid = graph_stack[1]
        is_dual = weapon_case.is_gun and weapon_case.is_dual_weapon
        anim_idx = 2 if is_dual else 1
        for key, value in anim_data[anim_idx].items():
            if key.endswith('AnimName'):
                SetVariableS(stackid, key, value)
                SetVariableS(-1, key, value)

    def AddWeaponPart(self, weapon_guid, part_id):
        cur_weapon = self.weapon_dict.get(weapon_guid)
        if not cur_weapon:
            return
        cur_weapon.AddWeaponPart(part_id)

    def ReloadWeaponStop(self):
        if self.upper_motion_state == cconst.UNIT_STATE_RELOAD:
            self.PopUpperGraph()
        weapon_case = self.weapon_case
        weapon_case and weapon_case.is_gun and weapon_case.ResetBackupCharger()
        self.owner.StopSoundByTag(tag='Reload')

    def PlayMeleeSkill(self, skill_type=0, info={}):
        pass
        # skeleton = self.GetSkeleton()
        # if not skeleton:
        #     return
        # skeleton.SetVariableI(-1, 'skill_type', skill_type)

    def RefreshUnderbarrelPose(self):
        # 换下挂后 刷新姿态
        if not self.owner.IsCurTakeGunWeapon():
            return
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        underbarrel_part_id = weapon_case.weapon_body.part_slots.get(consts.WeaponPartType_Underbarrel, {}).get('part_id')
        gun_attachments_data_getter = gun_attachments_data.data.get
        foregrip_pose_type = gun_attachments_data_getter(underbarrel_part_id, {}).get('foregrip_pose_type', 0)
        self.SetVariableI('ForegripPoseType', foregrip_pose_type, self.base_graph_id)
        self.SetVariableI('ForegripPoseType', foregrip_pose_type, self.locomotion_graph_id)

# endregion weapon

    def SetCustomRenderSet(self, renderSet):
        super(AvatarModel, self).SetCustomRenderSet(renderSet)
        for weapon_case in self.weapon_dict.values():
            weapon_case.SetCustomRenderSet(renderSet)

        for attach_model in self.attach_model_dict.values():
            attach_model.SetCustomRenderSet(renderSet)

    def Destroy(self):
        if self._is_destroyed:
            return
        for guid in list(self.weapon_dict.keys()):
            self.DestroyWeapon(guid)
        super(AvatarModel, self).Destroy()

    def ExitState(self, state):
        if not self.isValid():
            return
        call_func = getattr(self, '_OnExit_%s' % state, None)
        if call_func:
            call_func()

    def SetIsEmptyReload(self, is_empty):
        self.upper_graph_id and self.SetVariableF('isEmptyReload', is_empty, self.upper_graph_id)
        self.action_graph_id and self.SetVariableF('isEmptyReload', is_empty, self.action_graph_id)
        if self.weapon_case and self.weapon_case.is_gun:
            self.weapon_case.SetIsEmptyReload(is_empty)

    def SetMpFpsAction(self, action, blendTime):
        if not gui.is_pc or not genv.highlevel_tps_weapon:
            return
        owner = self.owner
        weapon_case = owner.model.weapon_case
        if not weapon_case or not weapon_case.use_highlevel or not weapon_case.is_gun:
            return
        if action in (cconst.MpFpsAction.RaiseWeapon, cconst.MpFpsAction.DropWeapon):
            # 收枪
            action == cconst.MpFpsAction.RaiseWeapon and self.SetVariableI('ACTOR_FPS_IsRaiseOver', 0)
        if action == cconst.MpFpsAction.Reload:   # 换弹
            self.SetIsEmptyReload(0.0 if owner.GetCurWeapon().client_ammo > 0 else 1.0)
        if self.action_graph_id:
            self.SetVariableF('Mp_Fps_BlendTime', blendTime, self.action_graph_id)
            self.SetVariableF('Mp_Fps_Actions', action, self.action_graph_id)
        self.mp_fps_action = action
        if model := weapon_case.model:
            model.SetMpFpsAction(action, blendTime)

    def PinWeaponGraph(self, graph):
        self.PinGraphs('GraphForTpsWeapon', [graph, ])

    def ActionEndToIdle(self, force_idle=False):
        mp_fps_blendtime = 0.2
        self.SetMpFpsAction(cconst.MpFpsAction.Idle, mp_fps_blendtime)
        weapon_case = self.weapon_case
        weapon_case and weapon_case.is_gun and weapon_case.ResetBackupCharger()

    def EnableHandIKToWeapon(self, enable=True, hand_type=0, ik_type=cconst.HAND_IK_TYPE_LEFT):
        if enable and self.weapon_case and weapon_util.IsWeaponCompoundBow(self.weapon_case.weapon_id):
            # 复合弓tps不需要IK
            return
        self.HandIkToWeapon(enable, hand_type)

    def CreateAIAgent(self):
        if not self.model:
            return
        ai_agent = MObject.CreateObject("AIAgentComponent")
        self.model.AIAgent = ai_agent
        return ai_agent

    def DeleteAIAgent(self):
        if not self.model:
            return
        self.model.AIAgent = None

    def _TriggerCb(self, triggerinfo):
        pass

    def UpdateLocomotionMoveArgs(self, x, y):
        self.SetVariableF("LocomotionFMove", y, self.locomotion_graph_id)
        self.SetVariableF("LocomotionSMove", x, self.locomotion_graph_id)

    def DebugCheckTPos(self):
        if not self.skeleton:
            return False
        bone_name = cconst.AVATAR_BONE_RHAND  # 检测右手
        init_trans = self.skeleton.GetBoneInitLocalTransform(bone_name)
        cur_trans = self.GetBoneLocalTransform(bone_name)
        # 先看位置
        if (init_trans.translation - cur_trans.translation).length > 0.01:
            return False
        if abs(init_trans.yaw - cur_trans.yaw) > 0.01:
            return False
        if abs(init_trans.pitch - cur_trans.pitch) > 0.01:
            return False
        return True

    def EnableSignalTypes(self, signal_types):
        if not self.isValid():
            return
        self.model.Skeleton.EnableSignalTypes(signal_types)

    def DisableSignalTypes(self, signal_types):
        if not self.isValid():
            return
        self.model.Skeleton.DisableSignalTypes(signal_types)
    
    def RefreshGraphGender(self):
        owner = self.owner
        if not owner:
            return
        hero_id = owner.hero_id
        hero_proto = hero_data.data.get(hero_id)
        if not hero_proto:
            return
        gender = hero_proto.get('hero_gender', 0)
        self.SetVariableI('HeroGender', gender, self.locomotion_graph_id)


@Components(PlayerAvatarModelCue, comp_ladder.LadderPlayerAvatarModelComp, ParachuteModelComp, ParachuteControllerComp,
            ParachuteGraphModel, ParachuteUnitModelComp, EmotePlayerAvatarModelComp, SpikeTransferPlayerAvatarModelComp,)
@RegisterModelClass
class PlayerAvatarModel(AvatarModel):

    def __init__(self, owner, position=None, yaw=0):
        super(PlayerAvatarModel, self).__init__(owner, position, yaw)
        self.self_trigger_slot = None
        self.carriable_trigger_slot = None

        self.is_ban_handik = False  # 禁止ik
        self.is_ban_right_handik = False  # 禁止ik
        self.stop_timer = None
        self.timelineImp = None

        self._move_vec = MType.Vector3()
        self.last_move_dir = cconst.MoveDirection.Forward

        self.input_processor = None
        self._cache_last_processed_input = {"x": 0.0, "z": 0.0, "speed": None, "timestamp": 0.0, "x_clear_timestamp": 0.0, "z_clear_timestamp": 0.0, "x_before_clear_value": 0.0, "z_before_clear_value": 0.0}
        self.input_process_timer = None

        self._hijack_move_vec_dict = {}
        self._hijack_move_vec = None
        self.jump_start_pos = None
        self.is_hit_fly = False  # 标记下当前是否在hitfly中

    def Load(self, data, done=None):
        super(PlayerAvatarModel, self).Load(data, done)
        StoryTick().Add(self.OnModelTick, frames=20)
        self.EnableMoveToDocking(not LocalConfig.is_auto_docking)
        self.SetEnterProneTime()
        self.SetGraphYawHalfLife(0.02)
        self.SetVariableI('is_other', 0, self.locomotion_graph_id)

    def onLoaded(self):
        self.loaded = True
        if self.owner.is_destroyed():
            return
        self.load_done and self.load_done()
        self.load_done = None
        self.reload_done and self.reload_done()
        self.reload_done = None
        self.Refresh()

    def CreateCharCtrl(self, width, height, physics_info, **kwargs):
        super(PlayerAvatarModel, self).CreateCharCtrl(width, height, physics_info, **kwargs)
        if not self.owner.is_replaying:
            self.charctrl.BindEvent("SupportedChanged", self.CharctrlSupportedChanged)

    def CharctrlSupportedChanged(self, is_supported):
        if not genv.avatar or genv.avatar.is_replaying:
            return
        if not self.owner or self.owner.is_destroyed():
            return
        if is_supported:
            if self.pose_type == ModelPoseType.Jump:
                self.JumpBreak()
            if self.owner.can_high_sky_para:
                genv.messenger.Broadcast(events.ON_HIGH_SKY_PARA, False)
                self.owner.can_high_sky_para = False

    def Refresh(self):
        super(PlayerAvatarModel, self).Refresh()
        genv.messenger.Broadcast(events.ON_HIDE_UI_FOR_REASON, ui_define.UI_HIDDENREASON_CLIMB_LADDER, False)
        self.RefreshLocomotionMovementConfigs()

    def SetPitchAndYaw(self, pitch, yaw):
        super(PlayerAvatarModel, self).SetPitchAndYaw(pitch, yaw)
        self.owner and self.owner.AdjustBombEffectPathPitch()

    def _SetGraphYaw(self, yaw):
        super(PlayerAvatarModel, self)._SetGraphYaw(yaw)
        self.owner and self.owner.CallServer("OnFaceYawChanged", yaw)

    def ReloadPrimitives(self, data, use_ready_to_appear=True):
        # 换模型
        self.model_id, data = self._GetAvailableModelInfo(data)
        self._modelIsValid = False
        self.model.SetExpireCallback(None)
        models = self.GetModelsFromData(data)
        resource_names = []
        self.lower_models = data.get("lower_models", [])
        self.lower_models_m = data.get("lower_models_material", [])
        for model in models:
            # 不要单独的头发
            if 'hair' not in model:
                resource_names.append(model)
        combat_unique_models = data.get("combat_unique_models")
        combat_unique_models and resource_names.extend(combat_unique_models)
        self.resource_names = resource_names
        self.DeleteAllShellComponent()
        ResourceLoader.ChangePrimitives(
            entity=self.model,
            resources=self.resource_names,
            use_ready_to_appear=use_ready_to_appear,
            done=lambda: self.RemoveLoadTask("ReloadResource"),
        )
        self._modelIsValid = True
        self.model.SetExpireCallback(self.ModelExpireCallback)

    def ShowLowerModelForFps(self, is_show=True):
        if not switches.SHOW_LOWER_MODEL_FOR_FPS:
            return
        if is_show and not self.owner.CheckCanShowLowerModelForFps():
            return
        if not self.owner.is_fps_mode:
            return
        if self.owner.is_dead_state:
            return
        if self.owner.is_lean_out:
            return
        if not self.lower_models:
            return
        if not self.model or not self.model.Primitives:
            return
        prims = self.model.Primitives

        for prim in prims:
            res_path = prim.ResourcePath
            # if is_show and prim.is_lower_model and res_path in self.lower_models:
            # sjh@ 避免P3视角跳跃后会走到这里把非LowerModel隐藏
            if is_show:
                if prim.is_lower_model and res_path in self.lower_models:
                    idx = self.lower_models.index(res_path)
                    if idx < len(self.lower_models_m):
                        mat_path = self.lower_models_m[idx]
                        prim.ApplyCustomMaterial(mat_path)
                prim.CustomRenderSet = cconst.RENDER_SET_DEFAULT
            else:
                prim.CustomRenderSet = cconst.RENDER_SET_SHADOW_PROXY
                # prim.ResetCustomMaterial()

    def ResetLowerModelForFps(self, is_fps_mode=False):
        # 这里把材质弄回来
        if is_fps_mode:
            return
        if not self.lower_models:
            return
        if not self.model or not self.model.Primitives:
            return
        prims = self.model.Primitives
        for prim in prims:
            prim.ResetCustomMaterial()

    def CueShowLowerModel(self):
        self.ShowLowerModelForFps(True)

    def CueHideLowerModel(self):
        self.ShowLowerModelForFps(False)

    def OnRotateCamera(self, diffx, diffy):
        move_dir = genv.input_ctrl.input_move_dir
        move_dir_enum = cconst.MoveDirection
        hip_dir_enum = cconst.ModelHipDirection
        if diffx > 0.2:
            # 镜头快速右转, 保证角色面向朝右，即左移动时臀向左后，右移动是臀向右前
            if move_dir == move_dir_enum.Left and self.hip_direction == hip_dir_enum.LF:
                self.hip_direction = hip_dir_enum.LB
            elif move_dir == move_dir_enum.Right and self.hip_direction == hip_dir_enum.RB:
                self.hip_direction = hip_dir_enum.RF
        elif diffx < -0.2:
            # 镜头快速左转，保证角色面向朝左，即右移动时臀向右后，左移动时臀向左前
            if move_dir == move_dir_enum.Right and self.hip_direction == hip_dir_enum.RF:
                self.hip_direction = hip_dir_enum.RB
            elif move_dir == move_dir_enum.Left and self.hip_direction == hip_dir_enum.LB:
                self.hip_direction = hip_dir_enum.LF
        self.SetVariableI('HipDirection', self.hip_direction, self.locomotion_graph_id)
        self.SetVariableI('CameraRotateDirection', 1 if diffx > 0 else -1, self.locomotion_graph_id)

    def UpdateMoveRotationMode(self, rotation_mode=None):
        pass

    @property
    def move_state(self):
        return self.owner.hand_model.move_state

    def ChangeMotionState(self, motion_state):
        if self.motion_state == motion_state:
            return
        broad_cast_func = genv.messenger.Broadcast
        name_to_values = StateRelationship.name_to_values   
        broad_cast_func(events.ON_LINK_STATE_RELATIONSHIP, name_to_values.get(self.motion_state), False)
        self.motion_state = motion_state
        broad_cast_func(events.ON_LINK_STATE_RELATIONSHIP, name_to_values.get(motion_state), True)

    def ChangeUpperMotionState(self, upper_motion_state):
        if self.upper_motion_state == upper_motion_state:
            return
        self.upper_motion_state = upper_motion_state

    def GetRunStartDirection(self):
        move_dir = self.owner.move_dir
        if move_dir == 8:
            return 0
        elif move_dir == 4:
            return 1
        elif move_dir == 2:
            return 2
        elif move_dir == 6:
            return 3
        return 0

    def OnDead(self, info):
        if not self.isValid():
            return
        self.PlayDeadCoinEffect(info)
        self.ResetCustomMaterial()
        self.PlaySkinDeadAnimAndEffect(info)
        self.EnableSelfTrigger(False)
        self.EnableCarriableTrigger(False)
        self.RemoveHiddenReason(cconst.HIDDEN_REASON_COMMON)
        # self.UnableCharCtrlWithReason(ModelCharCtrlReason.DEAD)
        if self.owner.IsPlayerCombatAvatar:
            self.SetCustomRenderSet(cconst.RENDER_SET_DEFAULT)
        # else:
        #   self.UnableFilterWithReason(ModelFilterReason.DEAD)
        self.JumpToState(cconst.UNIT_STATE_DEAD, info)
        owner = self.owner
        self.hidden_timer and owner.cancel_timer(self.hidden_timer)
        if owner.IsInExecute() or (info and info.get('force_no_action')):
            self.AddHiddenReason(cconst.HIDDEN_REASON_GHOST)
        else:
            self.hidden_timer = owner.add_timer(3.0, Functor(self.AddHiddenReason, cconst.HIDDEN_REASON_GHOST))

    def CheckExecuteOnDead(self):
        owner = self.owner
        if owner.IsInExecute() and owner.executor:
            owner.JumpOutBeExecuteState()
            camera = genv.camera
            camera.ApplyCamera(cconst.CAMERA_ID_DEATH)
            camera.placer.FollowKiller(owner.dead_killer)
            target = genv.space and genv.space.GetEntityByID(owner.executor)
            if target and target.model:
                target_model = target.model
                if filter := getattr(target_model, 'filter', None):
                    filter.ApplyMotion = True
                target_model.PopExecuteGraph()
                target_model.HandIkToWeapon(True)
        return False

    def OnReborn(self, info):
        super(PlayerAvatarModel, self).OnReborn(info)
        self.EnableGraphSyncMaster(True)
        self.EnableSelfTrigger(True)
        self.EnableCarriableTrigger(True)

    def SetLocomotionCycleDir(self):
        move_dir = genv.input_ctrl.input_move_dir
        locomotion_graph_id = self.locomotion_graph_id
        if cconst.OppositeDirections.get(self.last_move_dir) == move_dir:
            self.FireEvent('MoveDirectionChange', locomotion_graph_id)
        self.SetVariableI("LastMoveDirection", self.last_move_dir, locomotion_graph_id)
        self.SetVariableI('MoveDirection', move_dir, locomotion_graph_id)
        owner = self.owner
        if owner and owner.shark_model:
            owner.shark_model.SetVariableI('MoveDirection', move_dir, 0)
        self.last_move_dir = move_dir

    def ResetLocomotionGraphParam(self):
        self.JumpEnd()
        super(PlayerAvatarModel, self).ResetLocomotionGraphParam()

    def UpdateGraphSpeed(self):
        cur_speed = self.GetOwnerSpeed()
        x, z = self.owner.GetMoveKeysState()
        yaw_diff = self.GetMoveYawDiff(x, z)
        move_vec = MType.Vector3(0, 0, 1)
        move_vec.yaw = yaw_diff
        move_vec.length = cur_speed
        self.SetVariableF('Direction', yaw_diff, self.locomotion_graph_id)
        self.SetVariableV3('move_Vec', move_vec, self.locomotion_graph_id)
        self.SetVariableF('G_MOVE_SPEED', cur_speed, self.locomotion_graph_id)

    def InputBufferModeTick(self, force_tick=False):
        self.input_processor.Tick()
        x, z, speed = self.input_processor.GetCurrentInput()
        if getattr(genv, "print_input_buffer", False):
            print(self.input_processor.PrintBuffer())
        if x != self._cache_last_processed_input["x"] or z != self._cache_last_processed_input["z"] or speed != self._cache_last_processed_input["speed"] or force_tick:
            self._ApplyTowards(x, z, speed)

    def EnableInputBufferMode(self, expire_time=0.1):
        self.input_processor = PCInputBufferProcessor(expire_time)
        self.input_process_timer and self.cancel_timer(self.input_process_timer)
        self.input_process_timer = self.add_repeat_timer(0.01, self.InputBufferModeTick)

    def DisableInputBufferMode(self):
        self.input_processor = None
        self.input_process_timer and self.cancel_timer(self.input_process_timer)
        self.input_process_timer = None

    def Towards(self, x, z, speed=None):
        if self.input_processor:
            self.input_processor.AddInput(x, z, speed)
            self.InputBufferModeTick(force_tick=True)
            return
        else:
            self._ApplyTowards(x, z, speed)

    def _ApplyTowards(self, x, z, speed=None):
        owner = self.owner
        if not owner or owner.is_destroyed():
            return
        if owner.is_on_sky and (x or z):
            return
        model = self.model
        if not model:
            return
        Skeleton = model.Skeleton
        if not Skeleton:
            return
        locomotion_graph_id = self.locomotion_graph_id
        if not locomotion_graph_id:
            return
        SetVariableI = Skeleton.SetVariableI
        SetVariableF = Skeleton.SetVariableF
        SetVariableV3 = Skeleton.SetVariableV3
        FireEvent = Skeleton.FireEvent
        poseSender = self.poseSender
        if x or z:
            self.stop_timer and self.cancel_timer(self.stop_timer)
            self.stop_timer = None
            self._CancelDelayStopTimer()
            self.is_graph_moving = True

            Skeleton.ApplyMotion = True
            self.SetHipDirection(x, z)
            self.SetLocomotionCycleDir()
            FireEvent(locomotion_graph_id, '@Run')
            can_run = not (owner.is_shooting and owner.pose_type == cconst.ModelPoseType.Prone)
            SetVariableI(locomotion_graph_id, 'is_run', can_run)
            if self.upper_graph_id:
                SetVariableI(self.upper_graph_id, 'is_run', can_run)
            cur_speed = speed if speed else self.GetOwnerSpeed()
            yaw_diff = self.GetMoveYawDiff(x, z)
            move_vec = MType.Vector3(0, 0, 1)
            move_vec.yaw = yaw_diff
            move_vec.length = cur_speed
            SetVariableF(locomotion_graph_id, 'Direction', yaw_diff)
            SetVariableV3(locomotion_graph_id, 'move_Vec', move_vec)
            SetVariableF(locomotion_graph_id, 'G_MOVE_SPEED', cur_speed)
            move_vec.length = self.GetSlideSpeed(normal_speed=cur_speed)
            SetVariableV3(locomotion_graph_id, 'slide_speed', move_vec)
            SetVariableF(locomotion_graph_id, 'move_pose', owner.speed_level)
            SetVariableF(locomotion_graph_id, 'local_yaw', yaw_diff)

            self._move_vec = move_vec

            is_jump_pose_type = self.pose_type == ModelPoseType.Jump
            if is_jump_pose_type:
                jump_velocity = MType.Vector3(0, 0, 1)
                jump_velocity.yaw = yaw_diff
                if self.is_hit_fly:
                    jump_velocity.length = formula.LinearMapNumber(abs(yaw_diff), [math.pi, 0], anim_const.HITFLY_INPUT_COEF)
                SetVariableV3(locomotion_graph_id, 'jump_vec', jump_velocity)
            else:
                SetVariableV3(locomotion_graph_id, 'jump_vec', MType.Vector3())

            if self.hit_graph_id:
                jump_velocity = MType.Vector3(0, 0, 1)
                jump_velocity.yaw = yaw_diff
                jump_velocity.length = formula.LinearMapNumber(abs(yaw_diff), [math.pi, 0], anim_const.HITFLY_INPUT_COEF)
                SetVariableV3(self.hit_graph_id, 'jump_vec', jump_velocity)

            if poseSender and not is_jump_pose_type:
                # 跳跃的情况下需要关了这个预测，不然落地会因为预测导致最后落地速度突然减小
                poseSender.PredictMoveDeltaTime = anim_const.PREDICT_MOVE_DELTA_TIME
        else:
            self._move_vec = MType.Vector3()
            SetVariableV3(locomotion_graph_id, 'jump_vec', self._move_vec)
            if self.hit_graph_id:
                SetVariableV3(self.hit_graph_id, 'jump_vec', self._move_vec)
            self.stop_timer and self.cancel_timer(self.stop_timer)
            self.RealAnimStop()
            # if self.input_processor:
            #     # buffer模式延迟
            #     # self.stop_timer = self.add_timer(self.input_processor.expiration_time, self.RealAnimStop)
            #     self.RealAnimStop()
            # else:
            #     self.RealAnimStop()
            SetVariableI(locomotion_graph_id, 'JogOutDirection', owner.GetJogOutDir())
            if poseSender:
                poseSender.PredictMoveDeltaTime = 0
        SetVariableI(locomotion_graph_id, 'input_direction', genv.input_ctrl.input_move_dir)

        # self.UpdateLocomotionMoveArgs(x, z)
        self._UpdateLastInputLocalYaw(x, z)
        self._UpdateCacheMoveInput(x, z, speed)

        if owner.combat_state == consts.CombatState.DYING:
            owner.CallServer('OnDyingMove', x, z)

    def _UpdateCacheMoveInput(self, x, z, speed):
        self._cache_last_processed_input["x"] = x
        self._cache_last_processed_input["z"] = z
        self._cache_last_processed_input["speed"] = speed
        self._cache_last_processed_input["timestamp"] = time.time()

    def _UpdateLastInputLocalYaw(self, x, z):
        """
        更新stop_local_yaw, 用于处理stop时的动作方向融合问题
        保证如果在短时间内同时松开wd的情况下，记录的最后朝向的yaw是斜向45度的方向。
        如果用local_yaw，计算的值会是90度or 0度
        """
        now = time.time()
        threshold = 0.1
        if self._cache_last_processed_input["x"] != 0 and x == 0:
            self._cache_last_processed_input["x_clear_timestamp"] = now
            self._cache_last_processed_input["x_before_clear_value"] = self._cache_last_processed_input["x"]
        if self._cache_last_processed_input["z"] != 0 and z == 0:
            self._cache_last_processed_input["z_clear_timestamp"] = now
            self._cache_last_processed_input["z_before_clear_value"] = self._cache_last_processed_input["z"]

        if not (x or z):
            x_clear_timestamp = self._cache_last_processed_input["x_clear_timestamp"]
            z_clear_timestamp = self._cache_last_processed_input["z_clear_timestamp"]
            x_val = self._cache_last_processed_input["x_before_clear_value"] if now - x_clear_timestamp < threshold else 0
            z_val = self._cache_last_processed_input["z_before_clear_value"] if now - z_clear_timestamp < threshold else 0
            yaw_diff = self.GetMoveYawDiff(x_val, z_val)
            self.SetVariableF("stop_local_yaw", yaw_diff, self.locomotion_graph_id)
        else:
            yaw_diff = self.GetMoveYawDiff(x, z)
            self.SetVariableF("stop_local_yaw", yaw_diff, self.locomotion_graph_id)

    def _DelaySetStop(self):
        self.FireEvent('@StopTest', self.locomotion_graph_id)
        self._delay_stop_timer = None

    def _CancelDelayStopTimer(self):
        if hasattr(self, '_delay_stop_timer'):
            self.cancel_timer(self._delay_stop_timer)
            self._delay_stop_timer = None

    def GetCurMoveDirection(self, is_world=True):
        # 如果存在劫持移动向量，优先返回它
        if self._hijack_move_vec is not None:
            return formula.Rotate2D(self._hijack_move_vec, self.yaw) if is_world else self._hijack_move_vec
        return formula.Rotate2D(self._move_vec, self.yaw) if is_world else self._move_vec
    
    def AddHijackMoveVec(self, vec, tag):
        """
        添加劫持移动向量
        :param vec: 移动向量 (MType.Vector3)
        :param tag: 标识符
        """
        # 添加到字典中，记录添加的时间戳以确定最近添加的
        import time
        self._hijack_move_vec_dict[tag] = {
            'vec': vec,
            'timestamp': time.time()
        }
        
        # 更新当前生效的移动向量为最近添加的
        self._UpdateHijackMoveVec()
    
    def RemoveHijackMoveVec(self, tag):
        """
        移除劫持移动向量
        :param tag: 标识符
        """
        if tag in self._hijack_move_vec_dict:
            del self._hijack_move_vec_dict[tag]
            # 更新当前生效的移动向量
            self._UpdateHijackMoveVec()
    
    def _UpdateHijackMoveVec(self):
        """
        更新当前生效的劫持移动向量
        选择最近一次添加的向量作为当前生效的向量
        """
        if not self._hijack_move_vec_dict:
            self._hijack_move_vec = None
        else:
            # 找到时间戳最大（最近添加）的向量
            latest_tag = max(self._hijack_move_vec_dict.keys(), 
                           key=lambda t: self._hijack_move_vec_dict[t]['timestamp'])
            self._hijack_move_vec = self._hijack_move_vec_dict[latest_tag]['vec']

    def RealAnimStop(self):
        # if not self.stop_timer:
        #     return
        self.is_graph_moving = False
        self.SetVariableF('StopYawOffset', self.GetVariableF('VelocityYawOffset', self.locomotion_graph_id), self.locomotion_graph_id)
        self.SetVariableI('is_run', 0, self.locomotion_graph_id)
        self.SetVariableI('is_run', 0, self.upper_graph_id)
        self.FireEvent('@Stop', self.locomotion_graph_id)
        self.stop_timer = None

    def EnableMoveToDocking(self, enable=True):
        self.SetVariableI('IsMoveToDocking', int(enable), self.locomotion_graph_id)

    def SetEnterProneTime(self):
        enter_prone_time = self.owner.design_combat_properties.get("enter_prone_time", 1.0)
        self.SetVariableF('EnterProneDuration', enter_prone_time, self.locomotion_graph_id)

    def RefreshDockingDirOnShakeDir(self, x, y):
        if x < -0.7:
            # 左
            detect_dir = MType.Vector3(0.9, 0.2, 0.2)
        elif x > 0.7:
            # 右
            detect_dir = MType.Vector3(-0.9, 0.2, 0.2)
        elif y < -0.5:
            # 后
            detect_dir = MType.Vector3(0, 0.2, -0.2)
        else:
            # 前
            detect_dir = MType.Vector3(0, 0.2, 1)
        self.SetVariableV3('dock_dir', detect_dir, self.locomotion_graph_id)

    def RefreshMoveSpeed(self):
        owner = self.owner
        if not owner.is_alive:
            return
        speed = self.GetOwnerSpeed()
        self.SetMoveSpeed(speed)
        x, z = owner.GetMoveKeysState()
        self.Towards(x, z, speed)

    def RefreshMoveActionSpeed(self, speed):
        if not self.owner.is_alive:
            return
        if self.locomotion_graph_id is None or self.locomotion_graph_id < 0:
            return
        s = self.GetSkeleton()
        s and s.SetGraphActionPlaybackSpeed(self.locomotion_graph_id, 'Linear', speed, 0)

    def SetMoveSpeed(self, speed):
        if not self.model:
            return
        locomotion_graph_id = self.locomotion_graph_id
        Skeleton = self.model.Skeleton
        Skeleton.SetVariableF(locomotion_graph_id, 'G_MOVE_SPEED', speed)
        move_vec = Skeleton.GetVariableV3(locomotion_graph_id, 'move_Vec')
        if move_vec:
            move_vec.length = speed
            Skeleton.SetVariableV3(locomotion_graph_id, 'move_Vec', move_vec)

    def RefreshSlideVariable(self, is_parkour=False):
        skeleton = self.model.Skeleton
        locomotion_graph_id = self.locomotion_graph_id
        if is_parkour:
            spell_proto = spell_util.GetSpellProto(235)
            parkour_proto = spell_proto.get('parkour', {})
            skeleton.SetVariableF(locomotion_graph_id, 'slide_friction', parkour_proto.get('friction', 0.15))
            skeleton.SetVariableF(locomotion_graph_id, 'slide_speed_factor', parkour_proto.get('speed_factor', 2.0))
            skeleton.SetVariableF(locomotion_graph_id, 'slide_max_speed', parkour_proto.get('max_speed', 17))
            skeleton.SetVariableF(locomotion_graph_id, 'slide_max_time', parkour_proto.get('max_time', 3.5))
            skeleton.SetVariableI(locomotion_graph_id, 'is_parkour', True)
        else:
            slide_speed_factor = 1.3
            owner = self.owner
            if cconst.FULL_SLIDE_INTERVAL_ENABLE and owner:
                repeat_slide_interval = owner.last_slide_timestamp - owner.exit_slide_timestamp
                if 0 < repeat_slide_interval < owner.design_combat_properties.get("full_slide_interval", 2.0):
                    slide_speed_factor = owner.design_combat_properties.get("incomplete_slide_factor", 0.9)
            skeleton.SetVariableF(locomotion_graph_id, 'slide_friction', 0.4)
            skeleton.SetVariableF(locomotion_graph_id, 'slide_speed_factor', slide_speed_factor)
            skeleton.SetVariableF(locomotion_graph_id, 'slide_max_speed', 10)
            skeleton.SetVariableI(locomotion_graph_id, 'is_parkour', False)

    def OnSlide(self, slide, to_stand=False, extra=None):
        if slide:
            self.UpdateSlideMoveVec()
            self.FireEvent('@sliding', self.locomotion_graph_id)
            self.ChangeMotionState(cconst.UNIT_STATE_SLIDE)
            self.RefreshSlideVariable(extra.get('is_parkour'))
        else:
            # self.SetVariableI('sliding_to_stand', to_stand, self.locomotion_graph_id)
            # to_stand and self.FireEvent('sliding_to_stand', self.locomotion_graph_id)
            self.FireEvent('sliding_end', self.locomotion_graph_id)
            self.ChangeMotionState(cconst.UNIT_STATE_IDLE)

    def OnProne(self, prone, to_stand=True):
        # 进入/离开完成
        super(PlayerAvatarModel, self).OnProne(prone, to_stand)
        if not prone and self.motion_state == cconst.UNIT_STATE_EXIT_PRONE:
            if to_stand:
                self.ChangeMotionState(cconst.UNIT_STATE_STAND)
            else:
                self.ChangeMotionState(cconst.UNIT_STATE_CROUCH)
            self.RefreshMoveSpeed()
        if not prone:
            self.ShowLowerModelForFps(True)

    def OnSwoop(self, swoop, to_stand=False):
        if swoop:
            # todo: 给graph塞参数
            # self.UpdateSlideMoveVec()
            self.FireEvent('@swooping', self.locomotion_graph_id)
            self.ChangeMotionState(cconst.UNIT_STATE_SWOOP)
            # self.RefreshSlideVariable(extra.get('is_parkour'))
        else:
            # self.SetVariableI('sliding_to_stand', to_stand, self.locomotion_graph_id)
            # to_stand and self.FireEvent('sliding_to_stand', self.locomotion_graph_id)
            self.FireEvent('swooping_end', self.locomotion_graph_id)
            self.ChangeMotionState(cconst.UNIT_STATE_IDLE)

    def CancelExitProneByEnterProne(self):
        if self.motion_state != cconst.UNIT_STATE_EXIT_PRONE:
            return
        self.FireEvent('@StopCreepEnd', self.locomotion_graph_id)
        self.FireEvent('@lie', self.locomotion_graph_id)
        self.ChangeMotionState(cconst.UNIT_STATE_ENTER_PRONE)
        self.RefreshMoveSpeed()

    def BanHandIKToWeapon(self, is_ban, ban_type):
        self.is_ban_handik = is_ban

    def BanRightHandIKToWeapon(self, is_ban, ban_type):
        self.is_ban_right_handik = is_ban

    def EnableHandIKToWeapon(self, enable=True, hand_type=0, ik_type=cconst.HAND_IK_TYPE_LEFT):
        if enable and self.client_weapon_case and weapon_util.IsWeaponCompoundBow(self.client_weapon_case.weapon_id):
            # 复合弓tps不需要IK
            return
        if enable and hand_type == 0 and self.IsForceUnableLeftHandIK():
            return
        self.HandIkToWeapon(enable, hand_type)

    def IsForceUnableLeftHandIK(self):
        if self.is_ban_handik:
            return True
        if self.pose_type in (consts.ModelPoseType.Slide, ):
            return True
        if self.cur_client_lefthand_weapon_guid:
            return True
        if not self.cur_client_weapon_guid:
            return True
        if self.owner.IsStateInRelationshipSet(cconst.UNIT_STATE_PICKUP, cconst.UNIT_STATE_CROSS, cconst.UNIT_STATE_SUPERSPRINT, cconst.StateRelationship.GunRefit, cconst.StateRelationship.DualGun):
            return True
        if self.para_state and self.para_state not in (cconst.UNIT_STATE_LANDGROUND, cconst.UNIT_STATE_OPEN_FIRE_FREEFALL):
            return True

    def JumpToFire(self, info=None, transtime=0.1, reset=False, spell_id=0, is_auto_fire=True):
        super(PlayerAvatarModel, self).JumpToFire(info, transtime, reset, spell_id, is_auto_fire)
        if self.owner.shoot_immediately:
            self.JumpToState(cconst.UNIT_STATE_SPELL, info=info, spell_id=spell_id)
            # if self.upper_graph_id:
            #     self.FireEvent('@fire', self.upper_graph_id)
            #     self.SetVariableI('isKeyDown', 1, self.upper_graph_id)
            # else:
            #     self.JumpToState(cconst.UNIT_STATE_SPELL, info=info, spell_id=spell_id)
        self.UpdateGraphSpeed()

    def JumpToFireEnd(self):
        super(PlayerAvatarModel, self).JumpToFireEnd()
        if self.upper_graph_id:
            self.SetVariableI('isKeyDown', 0, self.upper_graph_id)
            self.FireEvent('@keyup', self.upper_graph_id)
            self.FireEvent('@SpellStop', self.upper_graph_id)

        self.UpdateGraphSpeed()

    def JumpToDualFire(self, info=None, transtime=0.1, reset=False, spell_id=0):
        self.JumpToFire(info, transtime, reset, spell_id)

    def JumpToDualFireEnd(self):
        self.JumpToFireEnd()

    def JumpToMeleeStart(self, info=None, transtime=0.1, reset=False, spell_id=0):
        # if self.upper_motion_state == cconst.UNIT_STATE_SPELL:
        #     return
        super(PlayerAvatarModel, self).JumpToMeleeStart(info, transtime, reset, spell_id)
        if self.upper_graph_id:
            self.FireEvent('@melee', self.upper_graph_id)
            self.SetVariableI('isKeyDown', 1, self.upper_graph_id)
        else:
            self.JumpToState(cconst.UNIT_STATE_SPELL, info=info, spell_id=spell_id)

        self.UpdateGraphSpeed()

    def JumpToMeleeEnd(self):
        super(PlayerAvatarModel, self).JumpToMeleeEnd()
        if self.upper_graph_id:
            self.SetVariableI('isKeyDown', 0, self.upper_graph_id)
            self.FireEvent('@keyup', self.upper_graph_id)
            self.FireEvent('@SpellStop', self.upper_graph_id)

        self.UpdateGraphSpeed()

    def JumpFall(self):
        cur_speed = self.GetOwnerSpeed()
        self.ChangeMotionState(cconst.UNIT_STATE_JUMP)
        x, y = self.owner.GetMoveKeysState()
        yaw_diff = min(3.14159, math.atan2(-x, y))
        jump_velocity = MType.Vector3(0, 0, 1)
        jump_velocity.yaw = yaw_diff
        slide_extra_jump_speed = 0
        if self.is_sliding_to_air:
            slide_extra_jump_speed = self.GetSlideExtraJumpSpeed(yaw_diff)
        jump_velocity.length = cur_speed + slide_extra_jump_speed
        jump_velocity = MType.Vector3(jump_velocity.x, 0, jump_velocity.z)
        jump_dir_int = self.GetRunStartDirection()

        # if self.owner.motion_state == 'SamuraiSprint':
        #     real_vel = self.GetVariableV3('ACTOR_TPS_RECORD_JUMP_VELOCITY', self.locomotion_graph_id)
        #     jump_velocity = MType.Vector3(0, 0, real_vel.z)
        #     jump_dir_int = cconst.MoveDirection.Forward

        self.FireJumpEventForJump()
        self.SetVariableI('jump_move_gait', self.owner.speed_level, self.locomotion_graph_id)
        self.SetVariableI('jump_dir_int', jump_dir_int, self.locomotion_graph_id)
        self.SetVariableV3('jump_dir', jump_velocity, self.locomotion_graph_id)
        self.FireLocomotionEvent('JumpToTop')
    
    def FireJumpEventForJump(self):
        super(PlayerAvatarModel, self).FireJumpEventForJump()
        self.SetJumpState(True)
        poseSender = self.poseSender
        if poseSender:
            poseSender.PredictMoveDeltaTime = 0

    def JumpBreak(self):
        super(PlayerAvatarModel, self).JumpBreak()
        self.SetJumpState(False)

    def SetJumpState(self, is_jump):
        self.poseSender and self.poseSender.SetJumpState(is_jump)

    def JumpToState(self, state, info=None, transtime=0.1, reset=False, spell_id=0):
        super(PlayerAvatarModel, self).JumpToState(state, info, transtime, reset, spell_id)
        if not state:
            return
        if state in (cconst.UNIT_STATE_SPELL, cconst.UNIT_STATE_CLIMB_LADDER, cconst.UNIT_STATE_EMOTE):
            return
        if state in ghost_energy_data.data:
            self.owner.TellServerMotionState(state, spell_id)
        else:
            self.owner.TellServerMotionState(consts.UNIT_STATE_IDLE)

    def JumpLand(self):
        super(PlayerAvatarModel, self).JumpLand()
        isHighFall = (self.owner.jump_top_height - self.position[1]) >= 10 and not self.owner.HasTalent(consts.TalentCode.ReduceFallHurt)
        isHighFall and genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.HighFall, True)
        self.SetVariableI('isHighFall', 1 if isHighFall else 0, self.locomotion_graph_id)
        self.owner.TellServerMotionState(consts.UNIT_STATE_IDLE)
        self.RefreshMoveActionSpeed(self.owner.move_action_speed)
        self.owner.jump_top_height = cconst.DEFAULT_JUMP_TOP_HEIGHT
        self.SetInJumpFall(False)
        self.SetJumpState(False)
        # 落地时 改回原来的预测时长
        poseSender = self.poseSender
        x, y, _, _ = genv.input_ctrl.GetMoveKeysState()
        if poseSender and (x or y):
            poseSender.PredictMoveDeltaTime = anim_const.PREDICT_MOVE_DELTA_TIME
        genv.messenger.Broadcast(events.ON_PLAYER_JUMP_LAND)

    def _OnJumpTo_EnterProne(self, info):
        self.ChangeMotionState(cconst.UNIT_STATE_ENTER_PRONE)
        self.RefreshMoveSpeed()
        super(PlayerAvatarModel, self)._OnJumpTo_EnterProne(info)

    def _OnJumpTo_Prone(self, info):
        super(PlayerAvatarModel, self)._OnJumpTo_Prone(info)
        self.ChangeMotionState(cconst.UNIT_STATE_PRONE)
        self.RefreshMoveSpeed()

    def _OnJumpTo_ExitProne(self, info):
        self.ChangeMotionState(cconst.UNIT_STATE_EXIT_PRONE)
        self.RefreshMoveSpeed()
        super(PlayerAvatarModel, self)._OnJumpTo_ExitProne(info)

    def _OnJumpTo_Jump(self, info):
        x, y, _, _ = genv.input_ctrl.GetMoveKeysState()
        self.RefreshDockingDirOnShakeDir(x, y)
        super(PlayerAvatarModel, self)._OnJumpTo_Jump(info)
        self.DelayCheckJumpStuck()
        owner = self.owner
        if not owner or owner.is_replaying:
            return
        cannot_climb = not owner.CheckCanEnterStateRelationshipForAction(cconst.StateRelationship.Climb) or not owner.CheckCanEnterStateRelationship(cconst.StateRelationship.Cross)
        self.SetVariableI('can_docking', int(not cannot_climb), self.locomotion_graph_id)
        self.jump_start_pos = self.position
        if x < -0.4:
            self.SetVariableI('is_left_mantle', 1, self.locomotion_graph_id)
        elif x > 0.4:
            self.SetVariableI('is_right_mantle', 1, self.locomotion_graph_id)
        elif y < -0.4:
            self.SetVariableI('is_back_mantle', 1, self.locomotion_graph_id)

    def _OnJumpTo_Spell(self, info):
        if self.parachute_graph_id:
            self.SetOpenFireAction(2)
            return
        if info is None:
            info = {}
        graph = info.get('vice_tps_graph')
        if not graph:
            graph = info.get('tps_graph')
        if graph:
            graph_name = graph
            # weapon_case = self.weapon_case
            # if weapon_case and weapon_case.IsSamuraiWithSamuraiSword():  # todo hardcode
            #     graph_name = 'TPS/Spell/tps_skill_samurai.graph'
        else:
            graph_name = 'TPS/Spell/m4.graph' # todo hardcode
        sync_param = info.get('sync_param', False)  # 是否需要设置一些Locomotion里面的变量，如pose_type, is_run等等
        self.PopUpperGraph()
        spell_id = info.get('spell_id')
        vehicle_seat = self.owner and self.owner.vehicle_seat
        vehicle = self.owner and self.owner.vehicle
        if (not vehicle or (vehicle.is_helicopter and vehicle_seat > 0)) or spell_id not in (23, 24):
            spell_proto = spell_util.GetSpellProto(spell_id)
            trans_time = spell_proto.get('tps_trans_time', 0)

            if genv.use_tps_action_graph:
                if self.action_graph_id:
                    self.ReplaceGraph(self.action_graph_id, graph_name)
                else:
                    self.PushUpperGraph(graph_name, transtime=trans_time)
                    self.upper_graph_id and self.SetVariableI('isKeyDown', 1, self.upper_graph_id)
            else:
                self.PushUpperGraph(graph_name, transtime=trans_time)
                self.upper_graph_id and self.SetVariableI('isKeyDown', 1, self.upper_graph_id)
        sync_param and self.SetUpperGraphCommonParam()
        self.UpdateWeaponAttrValue('FireIntervalTimeConf', self.upper_graph_id)
        self.ChangeUpperMotionState(cconst.UNIT_STATE_SPELL)
        self.SetMpFpsAction(cconst.MpFpsAction.Fire, 0)

    def UpdateWeaponAttrValue(self, attr_name, graph_id):
        weapon_case = self.owner.GetCurWeaponCase()
        if not weapon_case or not weapon_case.is_gun:
            return
        self.SetVariableF(attr_name, weapon_case.GetWeaponAttrValue(attr_name, 0), graph_id)

    def _OnJumpTo_RaiseWeapon(self, info):
        if self.owner.is_on_sky:
            return
        if self.upper_graph_id:
            self.PopUpperGraph()
        if self.gun_type and self.pose_type not in (consts.ModelPoseType.Slide, ):
            # 系统与功能US #446458【0129版本】【0201外放】问题修复：取消选择武器进入空手状态的动作不对
            # BUG-US #453356 【US】滑铲过程中从持枪切换到空手，第三人称动作会错
            self.PushUpperGraph('TPS/Locomotion/tps_enter_combat.graph', 0.2)
            cur_speed = self.GetOwnerSpeed()
            self.SetVariableF("G_MOVE_SPEED", cur_speed, self.upper_graph_id)
        self.AnimArmWeapon(self.gun_type)
        self.SetGraphDynamicAnim()
        self.SetGraphArgsOnRaiseWeapon()
        self._SetModelPoseType(self.owner.pose_type)
        weapon_id = info.get('weapon_id', 0)
        weapon_map = {16: 7, 3: 4}
        self.SetVariableI('EQUIP_WEAPON', weapon_map.get(weapon_id, 0), self.upper_graph_id)
        speed = 1.5 + self.owner.combat_attr.CalResult(1.5, 'ChangeWeaponSpeed')
        self.SetVariableF('play_speed', speed, self.upper_graph_id)
        self.ChangeUpperMotionState(cconst.UNIT_STATE_RAISEWEAPON)

    def _OnJumpTo_DropWeapon(self, info):
        if self.upper_graph_id:
            self.PopUpperGraph()
        weapon_id = info.get('weapon_id', 0)
        cur_weapon_id = self.weapon_case.weapon_id
        if self.pose_type not in (consts.ModelPoseType.Slide, ):
            # BUG-US #453356 【US】滑铲过程中从持枪切换到空手，第三人称动作会错
            if self.CanUseTPSActionGraph(cur_weapon_id) and self.CanUseTPSActionGraph(weapon_id):
                pass
            else:
                self.PushUpperGraph('TPS/Locomotion/tps_leave_combat.graph', 0.2)
                self.action_graph_id = None
            self.SetGraphArgsOnDropWeapon()
            cur_speed = self.GetOwnerSpeed()
            self.SetVariableF("G_MOVE_SPEED", cur_speed, self.upper_graph_id)
            cur_client_weapon_case = self.GetCurClientWeaponCase()
            if cur_client_weapon_case:
                anim_data_id = cur_client_weapon_case.equip_proto.get('anim_data_id', 0)
                self.SetVariableI('AnimDataID', anim_data_id, self.upper_graph_id)
        self.SetGraphDynamicAnim()
        self._SetModelPoseType(self.owner.pose_type)
        weapon_map = {16: 7, 3: 4}
        self.SetVariableI('EQUIP_WEAPON', weapon_map.get(weapon_id, 0), self.upper_graph_id)
        speed = 1.5 + self.owner.combat_attr.CalResult(1.5, 'ChangeWeaponSpeed')
        self.SetVariableF('play_speed', speed, self.upper_graph_id)
        self.ChangeUpperMotionState(cconst.UNIT_STATE_DROPWEAPON)
    
    def SetGraphArgsOnRaiseWeapon(self):
        weapon_case = self.owner.GetCurWeaponCase(True)
        if not weapon_case:
            return
        attr_getter = weapon_case.GetWeaponAttrValue
        for attr_name, val_name in anim_const.RAISE_WEAPON_GRAPH_ARGS.items():
            # print(f'{attr_name} = {attr_getter(attr_name, 0.1)}')
            self.SetVariableF(val_name, attr_getter(attr_name, 0.1), self.upper_graph_id)
    
    def SetGraphArgsOnDropWeapon(self):
        weapon_case = self.owner.GetCurWeaponCase(True)
        if not weapon_case:
            return
        attr_getter = weapon_case.GetWeaponAttrValue
        for attr_name, val_name in anim_const.DROP_WEAPON_GRAPH_ARGS.items():
            # print(f'{attr_name} = {attr_getter(attr_name, 0.1)}')
            self.SetVariableF(val_name, attr_getter(attr_name, 0.1), self.upper_graph_id)

    def _OnJumpTo_Rechamber(self, info):
        super(PlayerAvatarModel, self)._OnJumpTo_Rechamber(info)
        self.SetGraphArgsOnRechamber()

    def SetGraphArgsOnRechamber(self):
        weapon_case = self.owner.GetCurWeaponCase(True)
        if not weapon_case:
            return
        attr_getter = weapon_case.GetWeaponAttrValue
        for attr_name, val_name in anim_const.RECHAMBER_WEAPON_GRAPH_ARGS.items():
            self.SetVariableF(val_name, attr_getter(attr_name, 1.0), self.upper_graph_id)

    def _OnJumpTo_OnVehicle(self, info):
        self.JumpEnd()
        self.PopToLocomotionGraph()
        graph_name = self.owner.vehicle.vehicle_proto.get('character_graph_str', '')
        self.skeleton.SetAllGraphSleeping(True)
        vehicle_graph_id = self.PushGraph(graph_name)
        if vehicle_graph_id:
            self.vehicle_graph_id = vehicle_graph_id
        is_left = self.owner.vehicle.IsLeftSeat(self.owner.vehicle_seat)
        self.SetVariableI('is_left', is_left)
        is_lean_out = info and info.get('is_lean_out')
        if is_lean_out:
            self.LeanOutOnVehicle(True)
        is_driver = info.get('is_driver', False) if info else False
        self.SetVariableI('IsDriver', is_driver, vehicle_graph_id)
        self.SetVariableI('gun_type', self.owner.GetCurWeaponGunType())
        cur_client_weapon_case = self.GetCurClientWeaponCase()
        if cur_client_weapon_case:
            anim_data_id = cur_client_weapon_case.equip_proto.get('anim_data_id', 0)
            self.SetVariableI('AnimDataID', anim_data_id)
        vehicle = self.owner and self.owner.vehicle
        if not vehicle:
            return
        status = vehicle.GetDriveStatus(self.owner.vehicle_seat)
        self.SetDriveStatus(status, False)
        self.ChangeMotionState(cconst.UNIT_STATE_DRIVE_VEHICLE if self.owner.vehicle_seat == 0 else
                               cconst.UNIT_STATE_ON_VEHICLE)

    def _OnJumpTo_KnockDown(self, info):
        # 倒地状态前后左右移动
        skeleton = self.GetSkeleton()
        if skeleton and self.locomotion_graph_id != skeleton.GetGraphStack()[1]:
            self.PopGraph(self.locomotion_graph_id)
        self.locomotion_graph_id = self.PushGraph('TPS/Locomotion/knockdown.graph')
        if self.owner is genv.player and not genv.player.is_replaying:
            self._SetGraphYaw(self.owner.hand_model.yaw)
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.HighFall, False)

    def OnSaveEnd(self):
        super(PlayerAvatarModel, self).OnSaveEnd()
        owner = self.owner
        if owner.execute_target or owner.IsInExecute():
            return
        if not owner._CheckBeforeStand():
            owner.DoCrouch(True)
        self._SetGraphYaw(self.yaw)
        if self.owner.IsPlayerCombatAvatar:
            if genv.camera:
                genv.camera.RestoreCamera()
            self.owner.RefreshKeyForRun()

    def _OnJumpTo_Swim(self, info):
        self.owner.ChangePoseType(cconst.ModelPoseType.Swim)
        self.SetIsCastDynamicShadow(False)
        self.ApplyGravity(False)
        self.ChangeMotionState(cconst.UNIT_STATE_SWIM)
        self.SetVariableI('IsSwimming', 1, self.locomotion_graph_id)
        self.RefreshMoveSpeed()

    def OnExitSwim(self):
        self.owner.ChangePoseType(cconst.ModelPoseType.Stand)
        self.SetIsCastDynamicShadow(True)
        self.JumpToState(cconst.UNIT_STATE_STAND)
        if self.motion_state != cconst.UNIT_STATE_STROP:
            self.ChangeMotionState(cconst.UNIT_STATE_STAND)
        self.ApplyGravity(True)
        skeleton = self.skeleton
        if not skeleton:
            return
        graph_stack = skeleton.GetGraphStack()
        if len(graph_stack) < 2:
            return
        locomotion_graph_id = self.skeleton.GetGraphStack()[1]
        self.SetVariableI('IsSwimming', 0, locomotion_graph_id)

    def LeaveVehicle(self):
        self.skeleton.SetAllGraphSleeping(False)
        if self.vehicle_graph_id:
            self.PopGraph(self.vehicle_graph_id)
            self.vehicle_graph_id = None
        self.ChangeMotionState(cconst.UNIT_STATE_IDLE)

    def SetDriveStatus(self, state, is_driver):
        owner = self.owner
        if not owner or not owner.vehicle:
            return
        if is_driver and owner.vehicle_seat != 0:
            return
        self.SetVariableI("drive_status", state)
        self.FireEvent("@Turn")

    def LeanOutOnVehicle(self, is_lean_out):
        owner = self.owner
        if not owner:
            return
        if not self.vehicle_graph_id:
            return
        if is_lean_out:
            self.FireEvent('@LeanOut', self.vehicle_graph_id)
            self.RaiseCurWeapon()
            genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.OnVehicle, False)
            genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.LeanOutVehicle, True)
        else:
            self.FireEvent('@LeanBack', self.vehicle_graph_id)
            self.DropCurWeapon()
            genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.LeanOutVehicle, False)

    def ForceNoLeanOut(self):
        if not self.vehicle_graph_id:
            return
        skeleton = self.GetSkeleton()
        if not skeleton:
            return
        skeleton.JumpToState(self.vehicle_graph_id, 'DriveSit', 0, False)

    def SetLookAtPosInLocalTransform(self, look_at_pos):
        if not self.vehicle_graph_id:
            return
        if look_at_pos.pitch < -ONETHIRD_PI:
            look_at_pos.pitch = -ONETHIRD_PI
        elif look_at_pos.pitch > ONETHIRD_PI:
            look_at_pos.pitch = ONETHIRD_PI
        bone_trans = self.GetBoneWorldTransform('HP_sit')
        t = bone_trans.inverse
        look_at_pos = t.transform_p(look_at_pos)
        # is_weapon_seat = self.owner.IsWeaponSeat()
        # if is_weapon_seat:
        #   if look_at_pos.yaw < -QUAT_PI:
        #     look_at_pos.yaw = -QUAT_PI
        #   elif look_at_pos.yaw > QUAT_PI:
        #     look_at_pos.yaw = QUAT_PI
        self.SetVariableV3('look_at_pos', look_at_pos, self.vehicle_graph_id)

    @Async
    def DelayCheckJumpStuck(self):
        # 卡住时，再按一下跳，检测是否卡住脱困
        if self.owner.pose_type != cconst.ModelPoseType.Jump:
            return
        pos = self.position
        yield 0.3
        if not self.owner:
            return
        if not self.isValid():
            return
        if self.owner.pose_type != cconst.ModelPoseType.Jump:
            return
        if formula.Distance3D(pos, self.position) < 0.01:
            self.position = formula.HorizontalRelativeOffset3D((pos[0], pos[1] + 0.5, pos[-1]), self.yaw, 0, 0.5)
            vec_local = MType.Vector3(0, 5, 5)  # 斜向上45%
            self.SetVariableV3('jump_dir', vec_local, self.locomotion_graph_id)
        return

# region Weapon

# endregion Weapon
    def Destroy(self):
        if self._is_destroyed:
            return
        self.self_trigger_slot = None
        self.carriable_trigger_slot = None
        StoryTick().Remove(self.OnModelTick)
        self.DisableMovementStoryTick()
        self.DisableInputBufferMode()
        super(PlayerAvatarModel, self).Destroy()

    def EnableSelfTrigger(self, enable):
        if not self.isValid():
            return
        if self.self_trigger_slot is None:
            trans = MType.Matrix4x3()
            trans.translation = MType.Vector3(0, 1, 0)
            self.self_trigger_slot = MHelper.addMovingSphereTrigger(cconst.SELF_TRIGGER_RADIUS, self.model, self.owner.PlayerTriggerCb, filter_info=cconst.PHYSICS_SELF_TRIGGER, local_trans=trans)
        p = self.model.RigidBodies[self.self_trigger_slot]
        self._EnableSelfTriggerImp(p, enable)

    def _EnableSelfTriggerImp(self, p, enable):
        if enable:
            space = self.owner.space
            if not space:
                return
            if not space.world:
                return
            physics_space = space.world.PhysicsSpace
            p.owner = self.owner
            p.EnterSpace(physics_space)
        else:
            p.LeaveSpace()

    def EnableCarriableTrigger(self, enable):
        if not self.isValid():
            return
        if self.carriable_trigger_slot is None:
            self.carriable_trigger_slot = MHelper.addMovingSphereTrigger(6.0, self.model, self.owner.TriggerForCarriable)
        p = self.model.RigidBodies[self.carriable_trigger_slot]
        filter_data = MPhysics.CollisionFilterData()
        filter_data.mData0 = (1 << consts.PHYSICS_CARRIABLE)
        filter_data.mData3 |= consts.PHYSICS_QUERY_USE_MASK
        p.SetCollisionFilterData(filter_data)
        self._EnableSelfTriggerImp(p, enable)

    def OnModelTick(self, dtime):
        self.CalculateLeanAmount(dtime)
        # self.CalculateVelocityYawOffset(dtime)
        self.DebugDraw()
        self.OnTransfigureModelTick(dtime)
    
    def OnTransfigureModelTick(self, dtime):
        owner = self.owner
        if owner and owner.shark_model:
            cur_speed = self.GetOwnerSpeed()
            move_dir = genv.input_ctrl.input_move_dir
            x, z = self.owner.GetMoveKeysState()
            yaw_diff = self.GetMoveYawDiff(x, z)
            move_vec = MType.Vector3(0, 0, 1)
            move_vec.yaw = yaw_diff
            move_vec.length = cur_speed
            owner.shark_model.SetVariableF('move_speed', cur_speed, 0)
            owner.shark_model.SetVariableI('MoveDirection', move_dir, 0)
            owner.shark_model.SetVariableV3('player_move_vec', move_vec, 0)


    def CalculateLeanAmount(self, dtime):
        if not self.isValid():
            return
        model = self.model
        if not model.CharCtrl:
            return
        # 基于加速度计算倾斜Blend Weight
        self.MAX_ACCELERATION = MAX_ACCELERATION = 20
        self.MAX_SPEED = MAX_SPEED = 5.0

        # 计算角色加速度
        charctrl = model.CharCtrl
        self.diffVel = charctrl.RealVel - charctrl.LastRealVel
        if self.diffVel.length < 0.01:
            # 太小 clamp
            self.diffVel = MType.Vector3()
        self.acceleration = self.diffVel * (1.0 / dtime)
        self.acceleration.length = min(self.acceleration.length, MAX_ACCELERATION)

        in_air = not charctrl.IsSupported
        if in_air:
            # 角色相对速度值
            lean_vector = model.Transform.inverse.transform_v(charctrl.RealVel)
            lean_vector = lean_vector * (1.0 / MAX_SPEED)
        else:
            # 角色相对加速度值
            lean_vector = model.Transform.inverse.transform_v(self.acceleration)
            lean_vector = lean_vector * (1.0 / MAX_ACCELERATION)

        # if lean_vector.length < 0.01:
        #   lean_vector = MType.Vector3()
        leanBlendF, leanBlendB, leanBlendL, leanBlendR = \
            formula.NormalizeFloatList([
                abs(max(0.0, lean_vector.z)),
                abs(min(0.0, lean_vector.z)),
                abs(max(0.0, lean_vector.x)),
                abs(min(0.0, lean_vector.x))
            ])

        skeleton = self.skeleton
        if not skeleton:
            return
        SetVariableF = skeleton.SetVariableF
        locomotion_graph_id = self.locomotion_graph_id
        # SetVariableF('LeanBlendAmount', leanBlendAmount, locomotion_graph_id)
        SetVariableF(locomotion_graph_id, 'LeanBlendF', leanBlendF)
        SetVariableF(locomotion_graph_id, 'LeanBlendB', leanBlendB)
        SetVariableF(locomotion_graph_id, 'LeanBlendL', leanBlendL)
        SetVariableF(locomotion_graph_id, 'LeanBlendR', leanBlendR)

    def CalculateVelocityYawOffset(self, dtime):
        velocity = self.model.CharCtrl.RealVel
        if velocity.length <= 0.01:
            return 0
        yawOffset = formula.AngleSub(velocity.yaw, self.yaw)
        yawOffset = round(max(min(yawOffset, 3.14), -3.14), 2)
        self.SetVariableF('VelocityYawOffset', yawOffset, self.locomotion_graph_id)

    def DebugDraw(self):
        if not switches.DRAW_RAY:
            return
        self.DrawRealVelocity()
        self.DrawInputDirection()
        self.DrawModelDirection()
        self.DrawAccelerationDirection()

    def DrawDebugAxis(self, start_pos, end_pos, color=(1, 1, 1)):
        if isinstance(start_pos, tuple) or isinstance(start_pos, list):
            start_pos = MType.Vector3(*start_pos)
        if isinstance(end_pos, tuple) or isinstance(end_pos, list):
            end_pos = MType.Vector3(*end_pos)
        try:
            import MDebug  # noqa
        except:
            import traceback
            traceback.print_exc()
            return

        axis = MDebug.Cylinder()
        axis.endpoint0 = start_pos
        axis.endpoint1 = end_pos
        axis.color = MType.Vector3(*color)
        axis.radius = 0.4 * 0.1
        axis.type = 1
        return axis

    def DrawRealVelocity(self):
        # 速度方向 红色
        self.real_velocity_ray = None
        start_pos = self.model.Transform.translation + MType.Vector3(0, 0.2, 0) + self.model.Transform.z_axis
        end_pos = start_pos + self.model.CharCtrl.RealVel
        self.real_velocity_ray = self.DrawDebugAxis(start_pos, end_pos, (1, 0, 0))

    def DrawInputDirection(self):
        # 输入方向 绿色
        self.input_direction_ray = None
        g_yaw = self.GetVariableF('G_YAW', self.locomotion_graph_id)
        yawDelta = cconst.MoveYawDeltaFollowJoystick[genv.input_ctrl.input_move_dir]
        real_yaw = formula.AngleAdd(g_yaw, yawDelta)
        dir = MType.Vector3(0, 0, 1)
        dir.yaw = real_yaw
        start_pos = self.model.Transform.translation + MType.Vector3(0, 0.22, 0) + self.model.Transform.z_axis
        end_pos = start_pos + dir * 2.0
        self.input_direction_ray = self.DrawDebugAxis(start_pos, end_pos, (0, 1, 0))

    def DrawModelDirection(self):
        # 角色面向 蓝色
        self.model_direction_ray = None
        model_yaw = self.yaw
        dir = MType.Vector3(0, 0, 1)
        dir.yaw = model_yaw
        start_pos = self.model.Transform.translation + MType.Vector3(0, 0.24, 0) + self.model.Transform.z_axis
        end_pos = start_pos + dir * 2.0
        self.model_direction_ray = self.DrawDebugAxis(start_pos, end_pos, (0, 0, 1))

    def DrawAccelerationDirection(self):
        # 加速度方向 白色
        self.acceleration_direction_ray = None
        start_pos = self.model.Transform.translation + MType.Vector3(0, 0.26, 0) + self.model.Transform.z_axis
        end_pos = start_pos + self.acceleration
        self.acceleration_direction_ray = self.DrawDebugAxis(start_pos, end_pos, (1, 1, 1))

    def CueSamuraiCross(self):
        weapon_case = self.owner.GetCurSpecWeaponCase()
        weapon_case and weapon_case.OnSprintToClimb('@samurai_cross')

    def CueSamuraiClimb(self):
        weapon_case = self.owner.GetCurSpecWeaponCase()
        weapon_case and weapon_case.OnSprintToClimb('@samurai_climb')

    def CueSamuraiOverLow(self):
        weapon_case = self.owner.GetCurSpecWeaponCase()
        weapon_case and weapon_case.OnSprintToClimb('@samurai_over_low')

    def CueSamuraiOverMiddle(self):
        weapon_case = self.owner.GetCurSpecWeaponCase()
        weapon_case and weapon_case.OnSprintToClimb('@samurai_over_middle')

    def ResetPosResult(self):
        self.owner and self.owner.ResetPosResult()

    def GetWeaponCase(self, guid):
        return self.weapon_dict.get(guid)

    def RefreshWeaponList(self):
        if not self.isValid():
            return
        weapon_list = self.owner.GetWeaponList()
        # 新增
        self.weapon_list = weapon_list
        need_add = []
        for weapon in weapon_list:
            if weapon in self.weapon_dict:
                continue
            need_add.append(weapon)
        # 删除
        need_del = []
        for weapon in list(self.weapon_dict.keys()):
            if weapon in weapon_list:
                continue
            # 删除
            need_del.append(weapon)

        for cur_del in need_del:
            self.DestroyWeapon(cur_del)

        for cur_add in need_add:
            self.AddWeapon(cur_add)

        self.RefreshTpsWeaponsVisibility()
    
    def CheckWeaponState(self):
        # sjh@ 临时补救，1P的切枪滞后于3P的逻辑，导致3P在cue的时候刷新的武器状态不对
        if getattr(self, '_pending_for_raise_weapon', False):
            self.RaiseCurWeapon()
            self._pending_for_raise_weapon = False

# =======================新的移动方程测试=================================
    def RefreshLocomotionMovementConfigs(self):
        self.SetVariableF("LocomotionAccel", anim_const.MOVE_ACCEL, self.locomotion_graph_id)
        self.SetVariableF("LocomotionFriction", anim_const.MOVE_FRICTION, self.locomotion_graph_id)
        self.SetVariableF("LocomotionStopSpeed", anim_const.MOVE_STOP_SPEED, self.locomotion_graph_id)
        self.SetVariableF("LocomotionMoveWishSpeed", anim_const.MOVE_WISH_SPEED, self.locomotion_graph_id)
        self.SetVariableI("VelocityCalcType", 0, self.locomotion_graph_id)

    def EnableMovementStoryTick(self):
        if getattr(self, '_is_movement_storytick_enable', False):
            return
        self._is_movement_storytick_enable = True
        from gclient.framework.util.story_tick import StoryTick
        StoryTick().Add(self.OnMovementStoryTick)
        self.RefreshLocomotionMovementConfigs()
        self.SetVariableI("VelocityCalcType", 1, self.locomotion_graph_id)

    def DisableMovementStoryTick(self):
        if not getattr(self, '_is_movement_storytick_enable', False):
            return
        self._is_movement_storytick_enable = False
        from gclient.framework.util.story_tick import StoryTick
        StoryTick().Remove(self.OnMovementStoryTick)
        self.SetVariableI("VelocityCalcType", 0, self.locomotion_graph_id)

    def OnMovementStoryTick(self, dtime):
        if not genv.player or not genv.camera.placer:
            return
        direction = genv.camera.placer.placer.Direction
        self.UpdateMoveVelocity(self.owner._cache_fmove, self.owner._cache_smove, direction.pitch, direction.yaw)

    def UpdateMoveVelocity(self, fmove, smove, pitch, yaw):
        cp, _, cy, sy = math.cos(pitch), math.sin(pitch), math.cos(yaw), math.sin(yaw)
        # forward = MType.Vector3(sy * cp, -sp, cp * cy)
        # right = MType.Vector3(cy, 0, -sy)
        forward = MType.Vector3(sy * cp, 0, cp * cy).get_normalized()
        right = MType.Vector3(cy, 0, -sy).get_normalized()
        delta_time = self.GetVariableF("SYS_ENTITY_DELTA_TIME", self.locomotion_graph_id)
        # print("===== fmove: ", fmove, "smove: ", smove, "delta_time: ", delta_time)
        wish_speed = self.GetOwnerSpeed()
        wish_dir = (forward * fmove - right * smove).get_normalized()
        wish_dir[1] = 0
        acc = anim_const.MOVE_ACCEL
        friction = anim_const.MOVE_FRICTION
        stop_speed = anim_const.MOVE_STOP_SPEED
        cur_vel = self.GetVariableV3("LastFrameVelocity", self.locomotion_graph_id)
        cur_vel = self.Friction(friction, stop_speed, cur_vel, delta_time)
        cur_vel = self.Accelerate(wish_dir, wish_speed, acc, cur_vel, delta_time)
        self.SetVariableV3("CurFrameVelocity", cur_vel, self.locomotion_graph_id)

    def Friction(self, friction, stop_speed, cur_vel, delta_time):
        speed = formula.Length3D(cur_vel)
        drop = (stop_speed if speed < stop_speed else speed) * delta_time * friction
        newspeed = speed - drop
        if newspeed < 0:
            newspeed = 0
        if newspeed != speed:
            cur_vel = cur_vel * (newspeed / speed)
        return cur_vel

    def Accelerate(self, wish_dir, wish_speed, accel, cur_vel, delta_time):
        currentspeed = cur_vel.dot(wish_dir)
        add_speed = wish_speed - currentspeed
        acc_wish_speed = anim_const.MOVE_WISH_SPEED if anim_const.MOVE_WISH_SPEED > wish_speed else wish_speed
        if add_speed <= 0:
            return cur_vel
        # accel_speed = accel * wish_speed * delta_time
        accel_speed = accel * acc_wish_speed * delta_time
        if accel_speed > add_speed:
            accel_speed = add_speed
        cur_vel = cur_vel + wish_dir * accel_speed
        return cur_vel
# =======================新的移动方程测试 End=================================

# =============蹬墙跳===================
    def SetWallKickEnable(self, enable):
        self.SetVariableI("WallKickEnable", enable, self.locomotion_graph_id)
# =============蹬墙跳 End===================

    def RefreshGraphSyncMode(self):
        self.EnableGraphSyncMaster()
        # sjh@ 主角的第三人称模型会隐藏，需要设置隐藏状态下也正常更新，才能保证网络同步过去的表现正确
        self.model.Skeleton.SetInvisibleFrameLimit(-1)

    def SetJumpVerticalSpeedForbidReason(self, enable, reason):
        if not hasattr(self, '_jump_vertical_speed_forbid_reasons'):
            self._jump_vertical_speed_forbid_reasons = {}
        if enable:
            self._jump_vertical_speed_forbid_reasons[reason] = True
        else:
            self._jump_vertical_speed_forbid_reasons.pop(reason, None)
        self.RefreshJumpVerticalSpeedState()
    
    def RefreshJumpVerticalSpeedState(self):
        if not hasattr(self, '_jump_vertical_speed_forbid_reasons'):
            return
        if any(self._jump_vertical_speed_forbid_reasons.values()):
            self.SetVariableF('EnableJumpVerticalVelocity', 0, self.locomotion_graph_id)
        else:
            self.SetVariableF('EnableJumpVerticalVelocity', 1, self.locomotion_graph_id)

# ===============================击飞状态 Start============================================
    def _OnJumpTo_HitFly(self, info):
        hit_vel = info.get('hit_vel')
        if not hit_vel:
            print("===============sjh @ _OnJumpTo_HitFly: hit_dir is None")
            return

        self.is_hit_fly = True
        if not isinstance(hit_vel, MType.Vector3):
            hit_vel = MType.Vector3(*hit_vel)

        print(f"===============sjh @ _OnJumpTo_HitFly: hit_vel={hit_vel}")
        self.hit_graph_id = self.PushGraph('TPS/Spell/hit_move.graph')
        self.SetVariableV3("HitWorldDirection", hit_vel, self.hit_graph_id)
        owner = self.owner
        owner.AddImpactBreakableReason(cconst.ImpactBreakableReason.HIT_FLY)
        x, y = owner.GetMoveKeysState()
        yaw_diff = min(3.14159, math.atan2(-x, y))
        jump_velocity = MType.Vector3(0, 0, 1)
        jump_velocity.yaw = yaw_diff
        jump_velocity.length = formula.LinearMapNumber(abs(yaw_diff), [math.pi, 0], anim_const.HITFLY_INPUT_COEF)
        self.SetVariableV3('jump_vec', jump_velocity, self.hit_graph_id)
        
# ===============================击飞状态 End============================================