# -*- coding: utf-8 -*-
import math
import time
import random
import MType

from collections import deque
from gclient import config
from gclient import cconst
from gclient.cconst import ModelPoseType
from gclient.framework.models.model_cue_base import ModelCueBase, CueEndCallbackDef, CueCallbackDef
from gclient.framework.util import events, MHelper
from gclient.framework.util.performance_util import EPerformanceLevel
from gclient.gameplay.logic_base.equips.item_case import ItemCase
from gclient.gameplay.logic_base.models import anim_const
from gclient.util.debug_log_util import SomePreset, print_s
from gshare import consts
from gshare.async_util import Async, TAG_SPACE
from gshare import formula

from gclient.data import weapon_data, effect_data

SignalParseCache = {}


class AvatarModelCue(ModelCueBase):

    def __init_component__(self):
        ModelCueBase.__init_component__(self)
        self.gun_sfx_min_interval = 0.3 if gpl.performance_level <= EPerformanceLevel.LEVEL_1 else 0.2
        self.gun_sfx_cache = {}
        self.is_sliding_to_air = False
        self.is_pause_sliding = False
        self.enable_wall_kick_check = {anim_const.WallKickCheckTagDefine.FORWARD: False, anim_const.WallKickCheckTagDefine.BACKWARD: False}
        self.single_smoke_eid = None

    @CueCallbackDef('AvatarModel', cconst.CUE_AUTO_PUSH_GRAPH)
    def cue_signal_auto_push_graph(self, trigger, signal_str):
        # print 'cue_signal_auto_push_graph ===============', trigger >> 16, signal_str, self.owner
        graph_id = trigger >> 16
        skeleton = self.skeleton
        if skeleton and signal_str == 'Graph/TPS/Locomotion/tps_idle.graph':
            graph_stack = skeleton.GetGraphStack()
            if graph_id != graph_stack[1]:
                graph_idx = graph_stack.index(graph_id)
                skeleton.ShiftGraph(graph_id, 1 - graph_idx)

    @CueCallbackDef('AvatarModel', cconst.CUE_AUTO_POP_GRAPH)
    def cue_signal_auto_pop_graph(self, trigger, signal_str):
        # print 'cue_signal_auto_pop_graph ===============', trigger >> 16, signal_str, self.owner
        pass

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_SOUND_EVENT)
    def cue_signal_sound_event(self, trigger, signal_str):
        owner = self.owner
        # 观战的时候，被观战者的is_fps_avatar == True
        if owner.is_fps_avatar:
            # 主角处于第一人称的时候，不需要播第三人称的音效
            if owner.is_fps_mode:
                return
            # 主角处于第三人称并处于开镜状态，也不需要播第三人称的音效
            elif owner.is_ads:
                return
        else:
            if gpl.performance_level <= 1:
                if owner.distance_from_camera > 75.0:
                    return

        if signal_str not in SignalParseCache:
            SignalParseCache[signal_str] = signal_str.split(':')[:2]
        sound_type, event_id = SignalParseCache[signal_str]

        sound_id = 0
        # TODO: 未来如果这里的分支太多，要想办法改掉这几个if else
        if sound_type == "Gun":
            sound_id = owner.PlayGunSoundEventById(int(event_id), owner.sound_cache_gun_direction)
        elif sound_type == "General":
            sound_id = owner.PlayGeneralSoundEventById(int(event_id))

        if sound_id:
            self.cue_sound_cache[(trigger >> 16, event_id)] = sound_id

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_SOUND_EVENT_WITH_PARAM)
    def cue_sound_event_with_param(self, trigger, signal_str):
        owner = self.owner
        # 观战的时候，被观战者的is_fps_avatar == True
        if owner.is_fps_avatar:
            # 主角处于第一人称的时候，不需要播第三人称的音效
            if owner.is_fps_mode:
                return
            # 主角处于第三人称并处于开镜状态，也不需要播第三人称的音效
            elif owner.is_ads:
                return
        else:
            # if gpl.performance_level <= 1:
            if owner.distance_from_camera > 75.0:
                return

        if signal_str not in SignalParseCache:
            SignalParseCache[signal_str] = signal_str.split(':')[:2]
        foot_lr, event_id = SignalParseCache[signal_str]

        owner.SetSwitch("Left_Right", foot_lr)
        sound_id = 0
        sound_id = owner.PlayGeneralSoundEventById(int(event_id))
        if sound_id:
            self.cue_sound_cache[(trigger >> 16, event_id)] = sound_id

    def CuePauseSlidingSound(self):
        owner = self.owner
        if owner.pose_type != ModelPoseType.Slide or owner.is_slide_to_end:
            return
        pos = MType.Vector3(*self.position)
        r = genv.space.ClosestRaycast(pos, pos + MType.Vector3(0, -0.2, 0), cconst.PHYSICS_BULLET, with_trigger=False)
        if r and r.IsHit:
            return
        self.is_pause_sliding = True
        owner.StopSoundByTag('Slide')
        owner.PlayGeneralSoundEventById(34)

    def CueRecoverSlidingSound(self):
        owner = self.owner
        if owner.pose_type != ModelPoseType.Slide or owner.is_slide_to_end:
            return
        if not self.is_pause_sliding:
            return
        self.is_pause_sliding = False
        owner.PlayGeneralSoundEventById(32)

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_SOUND_EVENT_EXTRA)
    def cue_signal_sound_event_extra(self, trigger, signal_str):
        owner = self.owner
        # 观战的时候，被观战者的is_fps_avatar == True
        if owner.is_fps_avatar:
            # 主角处于第一人称的时候，不需要播第三人称的音效
            if owner.is_fps_mode:
                return
            # 主角处于第三人称并处于开镜状态，也不需要播第三人称的音效
            elif owner.is_ads:
                return

        weapon_case = self.GetCurClientWeaponCase()
        if not weapon_case or weapon_case.weapon_type != consts.EquipmentType.GUN:
            return
        if signal_str not in SignalParseCache:
            SignalParseCache[signal_str] = signal_str.split(':')[:2]
        sound_type, sound_index = SignalParseCache[signal_str]
        if weapon_case.guise_template_data and 'fire_sfx' in weapon_case.guise_template_data:
            skin_sound_sfx_sound_list = weapon_case.guise_template_data.get('fire_sfx')
            if skin_sound_sfx_sound_list and genv.dlc_manager.CheckSoundDlcExists(skin_sound_sfx_sound_list[0]):
                sound_sfx_sound_list = skin_sound_sfx_sound_list
            else:
                sound_sfx_sound_list = weapon_data.data.get(weapon_case.gun_id, {}).get('fire_sfx')
        else:
            sound_sfx_sound_list = weapon_data.data.get(weapon_case.gun_id, {}).get('fire_sfx')
        if not sound_sfx_sound_list:
            return
        sound_index = int(sound_index) - 1
        event_id = sound_sfx_sound_list[sound_index] if sound_index < len(sound_sfx_sound_list) else None
        if not event_id:
            return
        sound_id = 0
        if sound_type == "Gun":
            sound_id = owner.PlayGunSoundEventById(int(event_id))
        elif sound_type == "General":
            sound_id = owner.PlayGeneralSoundEventById(int(event_id))
        if sound_id:
            self.cue_sound_cache[(trigger >> 16, event_id)] = sound_id

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_TPS_GUN_FIRE_BULLET_EFFECT)
    def cue_signal_tps_gun_fire_bullet_sfx(self, trigger, signal_str):
        if not self.enable_gun_other_effect:
            return
        if self.owner.is_out_sight:
            return
        self.PlayTPSGunFireEffect(signal_str, 'tps_fire_shell_sfx_id')

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_TPS_GUN_FIRE_MUZZLE_EFFECT)
    def cue_signal_gun_fire_muzzle_effect(self, trigger, signal_str):  # 枪口特效
        self.PlayTPSGunFireEffect(signal_str, 'tps_fire_sfx_id')
        self.PlayTPSGunFireEffect(signal_str, 'tps_residue_smoke', True)

    def PlayTPSGunFireEffect(self, signal_str, effect_key, is_smoke=False):
        if not self.enable_gun_fire_effect:
            return
        owner = self.owner
        if owner.is_out_sight:
            return
        if owner.is_fps_avatar:
            if owner.is_fps_mode:
                return
            elif owner.is_ads:
                return
        else:
            if gpl.performance_level <= 1:  # noqa
                if owner.distance_from_camera > 50.0:
                    return
        # check weapon case
        if not owner.sound_cache_is_own_gun:
            return
        signal = signal_str.split(':')
        sfx_part, sfx_point, max_life, tach_config, is_dual, is_group = signal
        is_group = int(is_group)
        is_dual = int(is_dual)
        part_type = consts.WeaponPartTypeDataKeyInt.get(sfx_part)
        if not part_type:
            return
        weapon_case = owner.GetCurWeaponCase(False)
        if not weapon_case or not weapon_case.is_own_gun:
            return
        dual_weapon_body = weapon_case.dual_weapon_body
        if is_dual and not dual_weapon_body:
            return
        GetWeaponPartModelFunc = dual_weapon_body.GetWeaponPartModel if is_dual else weapon_case.GetWeaponPartModel
        part_model = GetWeaponPartModelFunc(part_type)
        if part_type == consts.WeaponPartType_Muzzle and not part_model:
            # 有些枪默认没有枪口 那挂点在枪管
            part_model = GetWeaponPartModelFunc(consts.WeaponPartType_Barrel)
        if not part_model:
            return
        # limit min distance
        limit = owner.sfx_cache_max_gun_dist
        if limit > 0 and not formula.InRange2D(owner.position, genv.player_pos, limit):
            return
        part_model_model = part_model.model
        if not part_model_model:
            return
        sfx_group_id = getattr(weapon_case, effect_key, None)
        if not sfx_group_id:
            return
        # limit min interval
        now = time.time()
        key = (sfx_part, is_group, sfx_group_id, is_dual)
        if now < self.gun_sfx_cache.get(key, 0.0):
            return
        self.gun_sfx_cache[key] = now + self.gun_sfx_min_interval

        if is_group:
            part_model.PlayRandomSkeletonEffect(sfx_group_id, float(max_life), insure_play=True)
        else:
            skeleton = part_model_model.Skeleton
            if not skeleton:
                return
            gpl.performance_level > 1 and skeleton.SetInsureEffectPlay(True)  # noqa
            path = effect_data.data[sfx_group_id].get('path')
            effect_string = '%s:%s:-1:%s' % (path, sfx_point, tach_config)
            if is_smoke and self.single_smoke_eid:
                skeleton.ClearEffect(self.single_smoke_eid)
            eid = skeleton.PlayEffect(effect_string, float(max_life))
            if is_smoke:
                self.single_smoke_eid = eid
            gpl.performance_level > 1 and skeleton.SetInsureEffectPlay(False)  # noqa

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_GUN_SFX)
    def cue_signal_gun_sfx(self, trigger, signal_str):
        if not self.enable_gun_fire_effect:
            return
        owner = self.owner
        if owner.is_out_sight:
            return
        if owner.is_fps_avatar:
            if owner.is_fps_mode:
                return
            elif owner.is_ads:
                return
        else:
            if gpl.performance_level <= 1:  # noqa
                if owner.distance_from_camera > 50.0:
                    return

        # check weapon case
        if not owner.sound_cache_is_own_gun:
            return
        # parse signal str
        if signal_str not in SignalParseCache:
            SignalParseCache[signal_str] = signal_str.split(':')
        signal = SignalParseCache[signal_str]
        sfx_part, sfx_group_id, sfx_name, sfx_point, max_life, effect_str = signal[:6]
        is_dual = len(signal) > 6 and int(signal[-1])  # 双持左手
        # limit min interval
        now = time.time()
        key = (sfx_part, sfx_group_id, sfx_name, is_dual)
        if now < self.gun_sfx_cache.get(key, 0.0):
            return
        self.gun_sfx_cache[key] = now + self.gun_sfx_min_interval
        # limit min distance
        limit = owner.sfx_cache_max_gun_dist
        if limit > 0 and not formula.InRange2D(owner.position, genv.player_pos, limit):
            return
        part_type = consts.WeaponPartTypeDataKeyInt.get(sfx_part)
        if not part_type:
            return
        weapon_case = owner.GetCurWeaponCase(False)
        if not weapon_case or not weapon_case.is_own_gun:
            return
        dual_weapon_body = weapon_case.dual_weapon_body
        if is_dual and not dual_weapon_body:
            return
        GetWeaponPartModelFunc = dual_weapon_body.GetWeaponPartModel if is_dual else weapon_case.GetWeaponPartModel
        part_model = GetWeaponPartModelFunc(part_type)
        if part_type == consts.WeaponPartType_Muzzle and not part_model:
            # 有些枪默认没有枪口 那挂点在枪管
            part_model = GetWeaponPartModelFunc(consts.WeaponPartType_Barrel)
        if not part_model:
            return
        part_model_model = part_model.model
        sfx_group_id = int(sfx_group_id)
        if sfx_group_id:
            part_model.PlayRandomSkeletonEffect(sfx_group_id, float(max_life), insure_play=True)
        else:
            if not part_model_model:
                return
            skeleton = part_model_model.Skeleton
            if not skeleton:
                return
            gpl.performance_level > 1 and skeleton.SetInsureEffectPlay(True)  # noqa
            skeleton.PlayEffect(signal_str.split(':', 2)[2], float(max_life))
            gpl.performance_level > 1 and skeleton.SetInsureEffectPlay(False)  # noqa

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_SOUND_BREAK)
    def cue_signal_sound_break(self, trigger, signal_str):
        self.owner.StopSoundByTag(signal_str)

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_SWITCH_WEAPON)
    def cue_signal_switch_weapon(self, trigger, signal_str):
        pass
        # switch_status = float(signal_str) > 0
        # if not self.weapon_case:
        #     return
        # if switch_status:
        #     # raise
        #     pass
        # else:
        #     # drop
        #     if self.owner.IsCurTakeLeftHandWeapon() and not self.owner.next_weapon_guid:
        #         self.ForceDropWeapon(self.owner.cur_lefthand_weapon_guid)
        #     else:
        #         self.ForceDropWeapon(self.owner.cur_spec_weapon_guid if self.owner.cur_spec_weapon_guid else self.owner.cur_weapon_guid)

    def _ProcessCueEnd(self, prev, trigger):
        if prev.startswith('hit'):
            self.PopHitGraph()
        elif prev.startswith('Pull'):
            self.PopPullGraph()
        else:
            self.JumpToState(cconst.UNIT_STATE_IDLE)

    def CueTpsFireBomb(self):
        owner = self.owner
        print('=========CueTpsFireBomb========owner =', owner, 'owner.throw_item =', owner.throw_item)
        if owner and owner.throw_item:
            owner.throw_item.OnFireBombForAvatar()

    def CueAddParachute(self):
        self.AddParachuteModel()

    @CueEndCallbackDef('AvatarModel', 'Recover_to_SaveEnd')
    def CueSaveEnd(self):
        self.OnSaveEnd()

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_ATTACH_NORMAL_MODEL)
    def cue_signal_attach_normal_model(self, trigger, signal_str):
        is_attach, model_id, hardpoint, basepoint = signal_str.split(':')
        is_attach = int(is_attach) >= 1
        model_id = int(model_id)
        self.CheckCreateAttachModel(model_id, is_attach, hardpoint, basepoint)

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_EXECUTE_THIRD_PERSON_MODEL)
    def cue_signal_execute_third_model(self, trigger, signal_str):
        is_create, model_id = signal_str.split(':')
        is_create = int(is_create) >= 1
        model_id = int(model_id)
        self.owner.ExecuteCreateThirdPersonModel(is_create, model_id)

    def CueExecuteHideModel(self):
        if not self.owner or not self.owner.IsInExecute():
            return
        self.AddHiddenReason(cconst.HIDDEN_REASON_EXECUTE)

    def CueExecuteShowModel(self):
        self.RemoveHiddenReason(cconst.HIDDEN_REASON_EXECUTE)

    def CuePulseArrowShow(self):
        owner = self.owner
        if not owner:
            return
        if owner.is_fps_avatar:
            return
        weapon_case = self.GetCurClientWeaponCase()
        if weapon_case and isinstance(weapon_case, ItemCase):
            weapon_case.ShowAttachModel()

    def CuePulseArrowHide(self):
        if self.owner and self.owner.is_fps_avatar:
            return
        weapon_case = self.GetCurClientWeaponCase()
        if weapon_case and isinstance(weapon_case, ItemCase):
            weapon_case.HideAttachModel()

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_RELOAD_CHARGER_CONTROL)
    def cue_signal_reload_charger_control(self, trigger, signal_str):
        if not gui.is_pc or not genv.highlevel_tps_weapon:
            return
        owner = self.owner
        weapon_case = owner.model.weapon_case
        if not weapon_case or not weapon_case.use_highlevel:
            return
        if not weapon_case.IsWeapon:
            return
        signal = signal_str.split(':')
        control, attach_status = signal[:2]
        control = float(control) > 0
        is_dual = len(signal) > 2 and int(signal[-1])
        if control:
            # 装上
            weapon_case.AttachBackupChargerToWeapon(is_dual)
        else:
            # 拆下
            weapon_case.AttachWeaponChargerToLeftHandOld(is_dual)

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_RELOAD_END)
    def cue_signal_reload_end(self, trigger, signal_str):
        # print("==================== cue_signal_realod_end_3p")
        if not gui.is_pc or not genv.highlevel_tps_weapon:
            return
        owner = self.owner
        weapon_case = owner.model.weapon_case
        if not weapon_case or not weapon_case.use_highlevel:
            return
        if not weapon_case.IsWeapon:
            return
        weapon_case.RefreshBackupChargerModel()

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_CHARGE_VISIBLE_CHANGE)
    def cue_signal_charge_visible_change(self, trigger, signal_str):
        if not gui.is_pc or not genv.highlevel_tps_weapon:
            return
        owner = self.owner
        weapon_case = owner.model.weapon_case
        if not weapon_case or not weapon_case.use_highlevel:
            return
        if not weapon_case.IsWeapon:
            return
        signal = signal_str.split(':')
        backup, vis = signal[:2]
        is_backup = int(backup) > 0
        visible = int(vis) > 0
        weapon_case.SetBackupChargerVisible(is_backup, visible)

    def CueAttackChargerNew(self):
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        weapon_case.is_gun and weapon_case.AttachWeaponChargerToLeftHandNew()

    def CueChangeChargerPhysxControl(self):
        """
        # 切换弹夹的控制模式，从动画切到物理
        """
        owner = self.owner
        if owner.IsRobotCombatAvatar:
            return
        weapon_case = self.weapon_case
        if not weapon_case or not weapon_case.is_gun or weapon_case.is_destroyed:
            return
        weapon_case.physx_control_charger_model = weapon_case.weapon_charger_model
        if not weapon_case.physx_control_charger_model or not weapon_case.physx_control_charger_model.isValid():
            return
        charger_model = weapon_case.physx_control_charger_model
        charger_model.DetachFromParentPart()
        if not charger_model.model.RigidBodies:
            charger_model.CreateRigidBody(self.OnChargerCollision)
        if len(charger_model.model.RigidBodies) == 0:
            return
        # 让弹夹跟随物理
        rb = charger_model.model.RigidBodies[0]
        rb.EnableGravity = True
        rb.Mass = 1.0
        rb.PassiveMode = False
        rb.ControllEntity = True
        import MPhysics
        rb.MotionType = MPhysics.EPhysicsMotionType.Dynamic
        left_hand_trans = self.GetBoneWorldTransform('biped L Hand')
        init_speed = 2
        init_vel = left_hand_trans.y_axis * init_speed
        # [DEBUG]
        # from gclient.util import debug_draw_util
        # genv.pp = debug_draw_util.draw_arrow_line(left_hand_trans.translation, left_hand_trans.translation + init_vel, color=(1,0,0))
        # [DEBUG]
        rb.LinearVel = init_vel
        rb.AngularVel = MType.Vector3(0, 0, 0)
        rb.AngularDamping = 1.0
        # rb.DebugDrawer.Show = True

    def OnChargerCollision(self, trigger_info):
        weapon_case = self.weapon_case
        if not weapon_case or not weapon_case.is_gun:
            return
        owner = self.owner
        # 这里有销毁物理的操作,延迟一帧
        owner.add_timer(0, lambda: weapon_case.OnChargerCollision(trigger_info))

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_ATTACH_WEAPON)
    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_ATTACH_WEAPON)
    def cue_signal_attach_weapon(self, trigger, signal_str):
        is_attach = int(signal_str) > 0
        weapon_case = self.owner.GetCurWeaponCase(is_fps_weapon=False)
        if not weapon_case:
            return
        # sjh@ 会因为enter_combat.graph被其他动作打断导致武器挂接失败，先回退
        # if is_attach:
        #     weapon_case.AttachWeaponToAvatarHand()
        # else:
        #     weapon_case.AttachWeaponToAvatarBack()

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_CHANGE_TPS_WEAPON_HAND)
    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_CHANGE_TPS_WEAPON_HAND)
    def cue_signal_change_tps_weapon_hand(self, trigger, signal_str):
        weapon_case = self.weapon_case
        if weapon_case:
            attach_func = getattr(weapon_case, 'AttachWeaponToAvatar%s' % signal_str, None)
            attach_func and attach_func()

    def CueTransfigureAnimEnd(self):
        self.weapon_case.RemoveHiddenReason(cconst.HIDDEN_REASON_COMMON)
        self.DeleteAllShellComponent()
    
    def CueSwitchCamera(self):
        pass

    def CueSwitchCameraWithStrParam(self, param_str):
        pass

    @CueEndCallbackDef('AvatarModel', "HitFly_to_HItFlyEnd")
    @CueEndCallbackDef('PlayerAvatarModel', "HitFly_to_HItFlyEnd")
    def HitFlyEnd(self):
        # self.owner.hand_model.JumpToState(cconst.UNIT_STATE_CLIMB)
        if self.hit_graph_id:
            self.PopGraph(self.hit_graph_id)
            self.hit_graph_id = None
        self.is_hit_fly = False
        self.ActionEndToIdle()
        self.RecoverJumpVelocity()
        self.owner.RemoveImpactBreakableReason(cconst.ImpactBreakableReason.HIT_FLY)

    def RecoverJumpVelocity(self):
        locomotion_graph_id = self.locomotion_graph_id
        velocity = self.GetVariableV3('ACTOR_TPS_RECORD_JUMP_VELOCITY', locomotion_graph_id)
        input_speed = formula.Length2D(velocity)

        SetVariableF = self.SetVariableF
        SetVariableF('jump_yaw_0', self.yaw, locomotion_graph_id)
        self.SetVariableV3('jump_dir_expect_local', velocity, locomotion_graph_id)
        SetVariableF('jump_max_speed', input_speed, locomotion_graph_id)
        SetVariableF('WallKickJumpMaxSpeed', input_speed, locomotion_graph_id)

        # 继承y方向速度
        self.SetVariableV3('JumpFallVerticalSpeedOffset', MType.Vector3(0, velocity.y, 0), locomotion_graph_id)
        # 强行进JumpFall
        owner = self.owner
        owner.ResetJumpFallSpeed()
        SetVariableF('JumpFallBlendTime', 0, locomotion_graph_id)
        if not self.in_jump_fall:
            # 非JumpFall状态才需要这个事件，否则会重新进JumpUp
            self.FireEvent("JumpToTop", locomotion_graph_id)
        if owner.IsPlayerCombatAvatar:
            x, y = owner.GetMoveKeysState()
            yaw_diff = min(3.14159, math.atan2(-x, y))
            jump_velocity = MType.Vector3(0, 0, 1)
            jump_velocity.yaw = yaw_diff
            jump_velocity.length = formula.LinearMapNumber(abs(yaw_diff), [math.pi, 0], anim_const.HITFLY_INPUT_COEF)
            self.SetVariableV3('jump_vec', jump_velocity, locomotion_graph_id)
    
    def ResetJumpFallVerticalSpeedOffset(self):
        self.SetVariableV3('JumpFallVerticalSpeedOffset', MType.Vector3(0, 0, 0), self.locomotion_graph_id)

class PlayerAvatarModelCue(AvatarModelCue):

    def __init_component__(self):
        AvatarModelCue.__init_component__(self)
        self.fall_start_height = None
        # self.prone_before_jump = False
        self.has_closed_parachute = False
        self.is_sliding_to_air = False
        self.playing_camera_effect_dict = {}  # 第三人称屏幕特效
        self.is_falling = False  # 这个应该是一个单独的poseType，控制测试的溅射范围，先单独写一个
        self.in_jump_fall = False

        self.forbid_climb_reason = set()

    def __fini_component__(self):
        AvatarModelCue.__fini_component__(self)
        for eid in self.playing_camera_effect_dict.values():
            MHelper.ClearEffectInWorld(eid, True)

    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_DOCKING_DETECT)
    def cue_docking_dectect(self, trigger, signal_str):
        ske = self.skeleton
        if not ske:
            return
        target_mark = ske.GetTargetMark()
        if not target_mark or target_mark.type < 0:
            return
        mark_normal = target_mark.normal
        face_dir = self.model.Transform.z_axis
        if abs(face_dir.dot(mark_normal)) < 0.7:
            # 角色面向与攀爬线法线夹角过大
            return
        self.FireEvent('@cross_valid', self.locomotion_graph_id)

    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_SINGAL_EVENT)
    def cue_signal_event(self, trigger, signal_str):
        owner = self.owner
        if owner and owner.IsIgnoreCue:
            return
        ModelCueBase.cue_signal_event(self, trigger, signal_str)

    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_SINGAL_PLAYER_EVENT)
    def cue_signal_player_event(self, trigger, signal_str):
        self.cue_signal_event(trigger, signal_str)

    @CueCallbackDef('PlayerAvatarModel', cconst.NODE_TRANSITION_EXCUTE_SIGNAL)
    def cue_type_node_transition_excute(self, trigger, curinput):
        owner = self.owner
        if owner and owner.IsIgnoreCue:
            return
        ModelCueBase.cue_type_node_transition_excute(self, trigger, curinput)

    @CueCallbackDef('AvatarModel', cconst.CUE_TYPE_ENABLE_HANDIK)
    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_ENABLE_HANDIK)
    def cue_signal_enable_handik(self, trigger, signal_str):
        # 参数说明(5个参数):
        # 1.bool: 是否开启 (默认:true)
        # 2.string: 手部骨骼 (默认:tag_weapon_left)
        # 3.string: 枪上骨骼 (默认:HP_gun_1)
        # 4.list: ik类别 (默认:LeftHand, 可选:LeftHand/RightHand/Both)
        # 5.float: 左手ik混合时间 (默认:0.1, 范围:0-5) ps: 现在只有第一人称在用
        signal = signal_str.split(':')
        len_signal = len(signal)
        enable_param = signal[0] if len_signal > 0 else "1"
        hand_bone_param = signal[1] if len_signal > 1 else "tag_weapon_left"
        gun_bone_param = signal[2] if len_signal > 2 else "HP_gun_1"  # noqa
        ik_type_param = signal[3] if len_signal > 3 else cconst.HAND_IK_TYPE_LEFT
        left_ik_blend_time_param = signal[4] if len_signal > 4 else "0.1"  # noqa
        
        enable = int(enable_param) > 0
        if self.motion_state in (cconst.UNIT_STATE_STROP, cconst.UNIT_STATE_SWIM):
            enable = False
        ik_type = ik_type_param
        if len_signal == 1:
            self.EnableHandIKToWeapon(enable)
        else:
            hand_bone = hand_bone_param
            hand_type = 0 if hand_bone == 'HP_WL' else 1
            self.EnableHandIKToWeapon(enable, hand_type, ik_type)

    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_BAN_HANDIK)
    def cue_signal_ban_handik(self, trigger, signal_str):
        signal = signal_str.split(':')
        is_ban = int(signal[0]) <= 0
        ban_type = 1 if len(signal) <= 1 else int(signal[1])
        ik_type = cconst.HAND_IK_TYPE_LEFT if len(signal) <= 2 else signal[-1]
        if ik_type in (cconst.HAND_IK_TYPE_LEFT, cconst.HAND_IK_TYPE_BOTH):
            self.BanHandIKToWeapon(is_ban, ban_type)
        if ik_type in (cconst.HAND_IK_TYPE_RIGHT, cconst.HAND_IK_TYPE_BOTH):
            self.BanRightHandIKToWeapon(is_ban, ban_type)

    @CueEndCallbackDef('PlayerAvatarModel', 'JumpStart_to_JumpFall')
    def JumpToTop(self):
        owner = self.owner
        if not owner:
            return
        owner.jump_top_height = self.position[1]

    def CueJumpToTop(self):
        owner = self.owner
        if not owner:
            return
        if owner.vehicle:
            return
        owner.jump_top_height = self.position[1]
        pos = MType.Vector3(*self.position) + MType.Vector3(0, 1, 0)
        r = genv.space.ClosestRaycast(pos, pos + MType.Vector3(0, -100, 0), cconst.PHYSICS_BULLET, with_trigger=False)
        self.SetInJumpFall(True)
        if r and r.IsHit:
            # h = gt**2 * 0.5
            height = self.position[1] - r.Pos.y
            if height < 0:
                return
            t = math.sqrt(height * cconst.JUMP_FALL_BLEND_TIME_FACTOR / 9.8)
            self.SetVariableF('JumpFallBlendTime', t, self.locomotion_graph_id)
    
    def SetInJumpFall(self, in_jump_fall):
        self.in_jump_fall = in_jump_fall

    @CueEndCallbackDef('PlayerAvatarModel', 'JumpDown_to_JumpEnd')
    def JumpEnd(self):
        if self.motion_state in (cconst.UNIT_STATE_SWIM, cconst.UNIT_STATE_DRIVE_VEHICLE, cconst.UNIT_STATE_CLIMB_LADDER):
            return
        if self.motion_state in (cconst.UNIT_STATE_DEAD, cconst.UNIT_STATE_KNOCK_DOWN):
            self.owner.hand_model and self.owner.hand_model.JumpLand()
            return
        hand_model = self.owner.hand_model
        if hand_model:
            hand_model.JumpLand()
            # 3P退出攀爬后同步退出1P的攀爬
            hand_model.FireEvent("ClimbEnd", hand_model.pose_graph_id)
        self.jump_start_pos = None
        self.JumpLand()
        self.CueFallingEnd()
        self.owner.CheckAutoEnterSlide()
        self.ClearWallKickCheckState()
        self.ResetJumpFallVerticalSpeedOffset()

    def SetForbidClimbReason(self, reason, is_forbid):
        if is_forbid:
            self.forbid_climb_reason.add(reason)
        else:
            self.forbid_climb_reason.discard(reason)

    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_ENV_SENSOR_EVENT)
    def cue_signal_env_sensor_event(self, trigger, signal):
        last_wall_kick_time = getattr(self.owner, "_last_trigger_wall_kick_timestamp", 0)
        if self.forbid_climb_reason:
            return
        if time.time() - last_wall_kick_time < anim_const.CLIMB_AFTER_WALL_KICK_CD:
            return
        sensor_event, trigger_state = signal.split(':')
        predict_pos = self.GetVariableV3("SYS_ENTITY_PREDICT_POS", self.locomotion_graph_id)
        player_pos = self.jump_start_pos or self.position
        delta_height = predict_pos[1] - player_pos[1]
        res = self.CalcEnvSensorCrossType(sensor_event, trigger_state, predict_pos, delta_height)
        self.DebugDrawEnvSensorCrossType(sensor_event, trigger_state, res)
        if res != anim_const.DockingCrossType.NONE:
            self.SetVariableI('IsDock', 0, self.locomotion_graph_id)
            if res == anim_const.DockingCrossType.CROSS_ON:
                if self.owner.CheckCanEnterStateRelationship(cconst.StateRelationship.Climb):
                    self.FireEvent("ToCrossOn", self.locomotion_graph_id)
                    self.ClearWallKickCheckState()
            elif res == anim_const.DockingCrossType.CROSS_OVER:
                if self.owner.CheckCanEnterStateRelationship(cconst.StateRelationship.Cross):
                    self.FireEvent("ToCrossOver", self.locomotion_graph_id)
                    self.ClearWallKickCheckState()
        ## 废弃版本，代码先保留一下
        # sensor_event, trigger_state = signal.split(':')
        # if self.is_falling:
        #     return
        # # [DEBUG]
        # enable_debug = getattr(genv, "enable_climb_debug", False)
        # if enable_debug:
        #     if sensor_event == anim_const.EnvSensorEventDefine.DISPLAY:
        #         # self.ProcessEnvSensorDebugDisplay()
        #         return
        #     else:
        #         self.ProcessEnvSensorDebugResult(sensor_event, trigger_state)
        # # [DEBUG]
        # if sensor_event in anim_const.IGNORE_ENV_SENSOR_EVENT:
        #     return
        # climb_type = self.CalcEnvSensorClimbType(sensor_event, trigger_state)
        # if climb_type == anim_const.CLIMB_ILLEGAL_TYPE:
        #     return
        # self.SetVariableI("ClimbV2_Event_Type", climb_type, self.locomotion_graph_id)
        # change_event = "ClimbV2_Climb_Event" if climb_type in anim_const.CLIMB_TYPE_SET else "ClimbV2_Cross_Event"
        # self.FireEvent(change_event, self.locomotion_graph_id)
        # self.owner.hand_model and self.owner.hand_model.SetClimbEventType(climb_type)

    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_ENABLE_JUMP_VERTICAL_SPEED)
    def cue_signal_enable_jump_vertical_speed(self, trigger, signal_str):
        self.owner.ResetJumpFallSpeed()
        self.SetJumpVerticalSpeedForbidReason(int(signal_str) != 0, anim_const.JUMP_VERTICAL_SPEED_FORBID_REASON_DASH)
        real_vel = self.GetVariableV3('ACTOR_TPS_RECORD_JUMP_VELOCITY', self.locomotion_graph_id)
        input_speed = formula.Length2D(real_vel)
        real_vel.y = 0
        self.SetVariableV3('jump_dir_expect_local', real_vel, self.locomotion_graph_id)
        self.SetVariableV3('jump_dir', real_vel, self.locomotion_graph_id)
        self.SetVariableF('jump_max_speed', input_speed, self.locomotion_graph_id)
        # 强行进JumpFall
        self.SetVariableF('JumpFallBlendTime', 0, self.locomotion_graph_id)
        if not self.in_jump_fall:
            # 非JumpFall状态才需要这个事件，否则会重新进JumpUp
            self.FireEvent("JumpToTop", self.locomotion_graph_id)

    def CheckIsStairs(self, predict_pos):
        player_pos = MType.Vector3(*self.position)
        dis = formula.Distance3D(player_pos, predict_pos) / 4
        dir = MType.Vector3(*formula.Normalize3D(formula.Substract3D(predict_pos, player_pos)))
        sample_pos1 = player_pos + dir * dis
        sample_pos2 = player_pos + dir * dis * 2
        sample_pos3 = player_pos + dir * dis * 3
        down_dir = MType.Vector3(0, -anim_const.CLIMB_ON_MIN_HEIGHT, 0)
        r1 = genv.space.ClosestRaycast(sample_pos1, sample_pos1 + down_dir, cconst.PHYSICS_BULLET, with_trigger=False)
        r2 = genv.space.ClosestRaycast(sample_pos2, sample_pos2 + down_dir, cconst.PHYSICS_BULLET, with_trigger=False)
        r3 = genv.space.ClosestRaycast(sample_pos3, sample_pos3 + down_dir, cconst.PHYSICS_BULLET, with_trigger=False)
        if r1 and r2 and r3 and r1.IsHit and r2.IsHit and r3.IsHit:
            if r3.Pos.y - r2.Pos.y <= anim_const.CLIMB_ON_MIN_HEIGHT \
                    and r2.Pos.y - r1.Pos.y <= anim_const.CLIMB_ON_MIN_HEIGHT:
                return True
        return False

    def CalcEnvSensorCrossType(self, sensor_event, trigger_state, predict_pos, delta_height):
        hand_model = self.owner.hand_model
        if not hand_model:
            return anim_const.DockingCrossType.NONE

        # if self.owner.IsCurTakeCarriableWeapon():
        #     return anim_const.DockingCrossType.NONE
        speed_level = hand_model.GetCacheMoveSpeedLevel()

        x, y = self.owner.GetMoveKeysState()
        if not self.in_jump_fall and delta_height < anim_const.CLIMB_ON_ACTION_CHANGE_HEIGHT:
            if x == 0 and y == 0:
                return anim_const.DockingCrossType.NONE
        else:
            if y <= 0:
                return anim_const.DockingCrossType.NONE

        if trigger_state != anim_const.EnvSensorTriggerStateDefine.IN_AIR and sensor_event == anim_const.EnvSensorEventDefine.CROSS:
            back_pos = self.GetVariableV3("EnvSensor_BackPos", self.locomotion_graph_id)
            width = formula.Distance2D(predict_pos, back_pos)
            if width < anim_const.CLIMB_OVER_MAX_WIDTH and \
                    0.1 < delta_height < anim_const.CLIMB_OVER_MAX_HEIGHT and \
                    speed_level in (cconst.MoveSpeedLevel.SuperSprint, cconst.MoveSpeedLevel.Sprint):
                return anim_const.DockingCrossType.CROSS_OVER
        # print(f"delta_height: {delta_height} sensor_event: {sensor_event} {sensor_event not in anim_const.IGNORE_ENV_SENSOR_EVENT}  {speed_level}")
        if sensor_event not in anim_const.IGNORE_ENV_SENSOR_EVENT:
            if anim_const.CLIMB_ON_MIN_HEIGHT < delta_height < anim_const.CLIMB_ON_MAX_HEIGHT and not self.CheckIsStairs(predict_pos):
                self.RefreshCrossOnSpeedScale(anim_const.DockingCrossType.CROSS_ON, delta_height)
                self.RefreshCrossOnType(anim_const.DockingCrossType.CROSS_ON, delta_height)
                return anim_const.DockingCrossType.CROSS_ON
        
        return anim_const.DockingCrossType.NONE
    
    def RefreshCrossOnSpeedScale(self, cross_type, delta_height):
        if cross_type != anim_const.DockingCrossType.CROSS_ON:
            return
        hand_model = self.owner.hand_model
        speed_level = hand_model.GetCacheMoveSpeedLevel()
        speed_scale = 1.0
        if delta_height < anim_const.CLIMB_ON_SPEED_UP_HEIGHT and speed_level in (cconst.MoveSpeedLevel.SuperSprint, cconst.MoveSpeedLevel.Sprint):
            speed_scale = self.owner.design_combat_properties.get('cross_on_speedup_scale', 2.0)
        self.SetVariableF("CrossSpeedScale", speed_scale, self.locomotion_graph_id)
        if hand_model:
            hand_model.SetVariableF("CrossSpeedScale", speed_scale, hand_model.pose_graph_id)

    def RefreshCrossOnType(self, cross_type, delta_height):
        if cross_type != anim_const.DockingCrossType.CROSS_ON:
            return
        # 0时双手，1是单手
        action_type = 1 if delta_height < anim_const.CLIMB_ON_ACTION_CHANGE_HEIGHT else 0
        hand_model = self.owner.hand_model
        if hand_model:
            hand_model.SetVariableI("CrossOnType", action_type, hand_model.pose_graph_id)
    
    def DebugDrawEnvSensorCrossType(self, sensor_event, trigger_state, cross_type):
        # [DEBUG]
        if not getattr(genv, "enable_climb_debug", None):
            return
        from gclient.util import debug_draw_util
        from gclient.framework.destruct import cdestruct_debug
        predict_pos = self.GetVariableV3("SYS_ENTITY_PREDICT_POS", self.locomotion_graph_id)
        player_pos = self.position
        delta_height = predict_pos[1] - player_pos[1]
        suffix_txt = ""
        
        if not hasattr(self, "_climb_debug_deque"):
            self._climb_debug_deque = deque()
        while len(self._climb_debug_deque) > 1:
            self._climb_debug_deque.popleft()
        
        color = [random.random(), random.random(), random.random()]
        target_back_pos = None
        if sensor_event == anim_const.EnvSensorEventDefine.CROSS:
            back_pos = self.GetVariableV3("EnvSensor_BackPos", self.locomotion_graph_id)
            width = formula.Distance2D(predict_pos, back_pos)
            suffix_txt = "w:{:.2f}".format(width)
            target_back_pos = debug_draw_util.draw_sphere(None, back_pos, 0.1, color)

        start_sphere = debug_draw_util.draw_sphere(None, player_pos, 0.1, color)
        target_sphere = debug_draw_util.draw_sphere(None, predict_pos, 0.1, color)
        
        dist = formula.Distance2D(player_pos, predict_pos)
        text = "s:{}/{}\nh:{:.2f},d:{:.2f}".format(sensor_event, trigger_state, delta_height, dist) + suffix_txt
        text += f"\n{cross_type}"
        text_obj = cdestruct_debug.EQSItemObj(text, [predict_pos[0], predict_pos[1], predict_pos[2]], color)
        self._climb_debug_deque.append([start_sphere, target_sphere, text_obj, target_back_pos])
        # [DEBUG]
        pass

    def CalcEnvSensorClimbType(self, sensor_event, trigger_state):
        predict_pos = self.GetVariableV3("SYS_ENTITY_PREDICT_POS", self.locomotion_graph_id)
        player_pos = self.position
        delta_height = predict_pos[1] - player_pos[1]
        horizon_dis = formula.Distance2D(predict_pos, player_pos)
    
        return self.CalcClimbType(horizon_dis, delta_height, sensor_event, trigger_state)
        
    def CalcClimbType(self, horizon_dis, delta_height, sensor_event, trigger_state):
        # 根据策划文档写的判定规则，这部分可以考虑用条件表让策划自己配置
        owner = self.owner
        if delta_height < 0.01:
            # 高度差太小就直接不处理
            return anim_const.CLIMB_ILLEGAL_TYPE
        # 跨越
        if (owner.is_sprint or owner.is_super_sprint) and sensor_event == anim_const.EnvSensorEventDefine.CROSS and \
                trigger_state == anim_const.EnvSensorTriggerStateDefine.JUMP_START:
            if delta_height < anim_const.CLIMB_HEIGHT_0:
                if horizon_dis <= anim_const.CLIMB_MIN_SEARCH_DIS:
                    return anim_const.CLIMB_OVER_WEAPON_UP_CLOSE_LOW_B
                elif anim_const.CLIMB_MIN_SEARCH_DIS < horizon_dis <= anim_const.CLIMB_MAX_SEARCH_DIS:
                    return anim_const.CLIMB_OVER_WEAPON_UP_FAR_LOW_B
        # 攀爬(JumpUp)
        if trigger_state == anim_const.EnvSensorTriggerStateDefine.JUMP_START and (anim_const.CLIMB_MIN_SEARCH_DIS < horizon_dis <= anim_const.CLIMB_MAX_SEARCH_DIS):
            if anim_const.CLIMB_HEIGHT_3 <= delta_height <= anim_const.CLIMB_HEIGHT_4:
                return anim_const.CLIMB_WEAPON_DOWN_FAR
            elif anim_const.CLIMB_HEIGHT_1 <= delta_height < anim_const.CLIMB_HEIGHT_3:
                return anim_const.CLIMB_WEAPON_UP_FAR_HIGH
            elif delta_height < anim_const.CLIMB_HEIGHT_1:
                return anim_const.CLIMB_WEAPON_UP_FAR_LOW
        elif horizon_dis <= anim_const.CLIMB_MIN_SEARCH_DIS:
            if anim_const.CLIMB_HEIGHT_3 <= delta_height <= anim_const.CLIMB_HEIGHT_4:
                return anim_const.CLIMB_WEAPON_DOWN_CLOSE
            elif anim_const.CLIMB_HEIGHT_2 <= delta_height < anim_const.CLIMB_HEIGHT_3:
                return anim_const.CLIMB_WEAPON_UP_CLOSE_HIGH_D
            elif anim_const.CLIMB_HEIGHT_0 <= delta_height < anim_const.CLIMB_HEIGHT_2:
                return anim_const.CLIMB_WEAPON_UP_CLOSE_HIGH_B
            elif delta_height < anim_const.CLIMB_HEIGHT_0:
                return anim_const.CLIMB_WEAPON_UP_CLOSE_LOW
                
        return anim_const.CLIMB_ILLEGAL_TYPE

    # [DEBUG]
    def ProcessEnvSensorDebugDisplay(self):
        from gclient.util import debug_draw_util
        from gclient.framework.destruct import cdestruct_debug
        predict_pos = self.GetVariableV3("SYS_ENTITY_PREDICT_POS", self.locomotion_graph_id)
        player_pos = self.position
        if not hasattr(self, "_climb_debug_deque"):
            self._climb_debug_deque = deque()
        while len(self._climb_debug_deque) > 10:
            self._climb_debug_deque.popleft()
        
        color = [random.random(), random.random(), random.random()]
        start_sphere = debug_draw_util.draw_sphere(None, player_pos, 0.1, color)
        target_sphere = debug_draw_util.draw_sphere(None, predict_pos, 0.1, color)
        height = predict_pos[1] - player_pos[1]
        dist = formula.Distance2D(player_pos, predict_pos)
        text = "h:{:.2f},d:{:.2f}".format(height, dist)
        text_obj = cdestruct_debug.EQSItemObj(text, [predict_pos[0], predict_pos[1], predict_pos[2]], color)
        self._climb_debug_deque.append([start_sphere, target_sphere, text_obj])
    
    def ProcessEnvSensorDebugResult(self, sensor_event, trigger_state):
        # EnvSensor正常触发的结果
        from gclient.util import debug_draw_util
        from gclient.framework.destruct import cdestruct_debug
        predict_pos = self.GetVariableV3("SYS_ENTITY_PREDICT_POS", self.locomotion_graph_id)
        player_pos = self.position
        height = predict_pos[1] - player_pos[1]
        dist = formula.Distance2D(player_pos, predict_pos)
        climb_type = self.CalcClimbType(dist, height, sensor_event, trigger_state)
        text = "h:{:.2f},d:{:.2f}\nt:{}\ns:{}".format(height, dist, anim_const.CLIMB_TYPE_TO_NAME[climb_type], trigger_state)
        color = [1, 0, 0]
        text_obj = cdestruct_debug.EQSItemObj(text, [predict_pos[0], predict_pos[1], predict_pos[2]], color)
        self._climb_debug_list = []
        self._climb_debug_list.append(debug_draw_util.draw_sphere(None, predict_pos, 0.1, color))
        self._climb_debug_list.append(debug_draw_util.draw_sphere(None, self.position, 0.1, color))
        self._climb_debug_list.append(debug_draw_util.draw_arrow_line(self.position, predict_pos, color=color))
        self._climb_debug_list.append(text_obj)
    # [DEBUG]

    def GetFootfitHover(self, offset):
        center = MType.Vector3(*self.position) + offset
        pos = center + MType.Vector3(0, 1, 0)
        r = genv.space.ClosestRaycast(pos, pos + MType.Vector3(0, -100, 0), cconst.PHYSICS_BULLET, with_trigger=False)
        if r and r.IsHit:
            return center.y - r.Pos.y
        else:
            return 100.0

    def CheckDangling(self):
        owner = self.owner
        if owner.is_on_sky:
            return False

        # 离地太近的就不要jump了
        hover = self.GetFootfitHover(MType.Vector3(0, 0, 0))
        if hover < 0.4:
            return False

        # 离地不进不远的，就要求一下后方高度，看下是不是斜坡，解决下坡小跳步问题
        if hover < 1.0:
            direction = self.charctrl.LinearVel
            direction.y = 0
            # 水平速度大于某个值才开启小跳步检测
            if direction.length > 1.0:
                direction.normalize()
                back_hover = self.GetFootfitHover(direction * -0.4)
                if back_hover < 0.4:
                    return False
                # 如果是在做斜下运动的话back_hover的阈值加大一点点
                if hover - back_hover > 0.1 and back_hover < 0.5:
                    return False

        if owner.combat_state == consts.CombatState.DYING:
            self.CueJumpStartToFall()
            return False
        if owner.combat_state != consts.CombatState.ALIVE:
            return False
        return True

    def CueIsFalling(self):
        owner = self.owner
        if not owner:
            return
        if owner.is_on_sky:
            return
        motion_state = self.motion_state
        if motion_state == cconst.UNIT_STATE_STROP:
            return
        if motion_state == cconst.UNIT_STATE_SWIM:
            return
        if self.owner.is_transfigure:
            return
        if not self.CheckDangling():
            if self.pose_type == ModelPoseType.Jump:
                self.JumpEnd()
            return
        self.DoEnterFalling()
    
    def CueHideWeapon(self):
        for weapon_case in self.weapon_dict.values():
            weapon_case.AddHiddenReason(cconst.HIDDEN_REASON_WEAPON_BY_CUE)

    def CueShowWeapon(self):
        for weapon_case in self.weapon_dict.values():
            weapon_case.RemoveHiddenReason(cconst.HIDDEN_REASON_WEAPON_BY_CUE)

    def DoEnterFalling(self):
        owner = self.owner
        if not owner:
            return
        owner.is_in_air = True
        self.BeforeStartFallingForState()
        self.CueJumpStartToFall()
        self.JumpFall()
        owner.ChangePoseType(cconst.ModelPoseType.Jump)
        owner.hand_model and owner.hand_model.JumpFall()
        self.is_falling = True

    def BeforeStartFallingForState(self):
        owner = self.owner
        if self.motion_state == cconst.UNIT_STATE_PRONE:
            self.OnProne(False)
            owner.hand_model and owner.hand_model.OnProne(False)
        elif self.motion_state in (cconst.UNIT_STATE_ENTER_PRONE, cconst.UNIT_STATE_EXIT_PRONE):
            self._SetModelPoseType(ModelPoseType.Stand)
            self.SetCharCtrlProne(False)
        if self.pose_type == cconst.ModelPoseType.Crouch:
            self.OnCrouch(False)

    def CueFallingEnd(self):
        owner = self.owner
        owner.is_in_air = False
        if self.motion_state == cconst.UNIT_STATE_STROP:
            return
        if owner.force_no_fall_hurt:
            owner.force_no_fall_hurt = False
        elif self.fall_start_height is not None:
            if not owner.is_on_sky:
                owner.TryAttack(consts.FALL_HURT_SPELL_ID)
            self.fall_start_height = None
        if owner.helping_id or owner.getting_help_state != consts.HelpState.NONE:
            owner.OnShakeDir(0, 0)
        else:
            owner.RefreshKeyForRun()
        self.is_falling = False
        # if self.prone_before_jump:
        #   self.prone_before_jump = False
        #   self.owner.OnProne()

    @CueEndCallbackDef('PlayerAvatarModel', 'sliding_to_slidingEnd')
    def SlidingEnd(self):
        self.owner.OnSlideToCrouch()

    @CueEndCallbackDef('PlayerAvatarModel', 'swooping_to_swoopingEnd')
    def SwoopingEnd(self):
        self.owner.OnSwoopToProne()

    @CueEndCallbackDef('PlayerAvatarModel', 'sliding_to_Locomotion')
    def SlidingToLocomotion(self):
        if self.owner.pose_type == ModelPoseType.Slide:
            self.owner.OnSlide()

    @CueEndCallbackDef('PlayerAvatarModel', 'sliding_to_air')
    def SlidingToAir(self):
        self.is_sliding_to_air = True
        self.SlidingToLocomotion()
        self.CueIsFalling()
        self.is_sliding_to_air = False

    def CueSlidingToAir(self):
        self.is_sliding_to_air = True
        self.SlidingToLocomotion()
        self.CueIsFalling()
        self.is_sliding_to_air = False

    @CueEndCallbackDef('PlayerAvatarModel', 'StateMachine_to_UpperEnd')
    def CueUpperEnd(self):
        if self.upper_graph_id:
            self.PopUpperGraph()
        if self.action_graph_id:
            self.ActionEndToIdle()

    @CueEndCallbackDef('PlayerAvatarModel', 'Strop_to_StropEnd')
    def StropEnd(self):
        self.owner.CueStropToLocomotion()

    @CueEndCallbackDef('PlayerAvatarModel', 'Strop_to_Locomotion')
    def CueCueStropToLocomotion(self):
        self.owner.CueStropToLocomotion()

    def CueJumpStartToFall(self):
        self.fall_start_height = self.position[1]

    @Async(TAG_SPACE)
    def DelayRestoreCharctrl(self):
        # 防止翻墙的时候主角被其他玩家或者窗户卡住
        self.ResizeCharctrHeightForReason(cconst.CharctrlResizeHeightReason.Cross)
        yield 1
        if not self.isValid() or not self.charctrl:
            return
        self.RemoveCharctrlHeightReason(cconst.CharctrlResizeHeightReason.Cross)

    def DoJumpToCross(self, info=None):
        # 翻越
        self.owner.hand_model.JumpToState(cconst.UNIT_STATE_CROSS, info=info)
        self.JumpToState(cconst.UNIT_STATE_CROSS, info=info)
        self.fall_start_height = None
        self.owner.force_no_fall_hurt = True
        self.DelayRestoreCharctrl()
        self.ShowLowerModelForFps(False)

    @CueEndCallbackDef('PlayerAvatarModel', 'Jump_to_Cross')
    def JumpToCross(self):
        # 翻越
        self.DoJumpToCross()

    @CueEndCallbackDef('PlayerAvatarModel', 'Jump_to_CrossLeft')
    def JumpToCrossLeft(self):
        # 翻越
        self.DoJumpToCross({'is_left': True})

    @CueEndCallbackDef('PlayerAvatarModel', 'Jump_to_CrossRight')
    def JumpToCrossRight(self):
        # 翻越
        self.DoJumpToCross({'is_right': True})

    @CueEndCallbackDef('PlayerAvatarModel', 'Jump_to_Climb')
    def JumpToClimb(self):
        # 攀爬
        self.owner.hand_model.JumpToState(cconst.UNIT_STATE_CLIMB)
        self.JumpToState(cconst.UNIT_STATE_CLIMB)
        self.fall_start_height = None
        self.owner.force_no_fall_hurt = True
        self.DelayRestoreCharctrl()
    
    @CueEndCallbackDef('PlayerAvatarModel', 'Jump_to_ClimbV2')
    def JumpToClimbV2(self):
        # sjh@ 攀爬迭代后的信号触发函数，后续稳定了就直接把上面的干掉
        print("=========================  JumpToClimbV2 ")
        self.owner.hand_model.JumpToState(cconst.UNIT_STATE_CLIMB)
        self.JumpToState(cconst.UNIT_STATE_CLIMB)
        self.fall_start_height = None
        self.owner.force_no_fall_hurt = True
        self.DelayRestoreCharctrl()
    
    @CueEndCallbackDef('PlayerAvatarModel', 'Jump_to_CrossV2')
    def JumpToCrossV2(self):
        print("=========================  JumpToCrossV2 ")
        self.DoJumpToCross()

    def CueCanChangeProne(self):
        # 趴下结束，可以改状态了
        if self.motion_state != cconst.UNIT_STATE_ENTER_PRONE:
            return
        self.OnProne(True)
        self.RefreshMoveSpeed()

    def CueCanProneEnterAds(self):
        # 趴下过程中结束了，可以开镜了
        self.owner.prone_for_forbidden_ads = False
        if self.owner.prone_is_ads:
            self.owner.EnterAdsState(True)
            self.owner.prone_is_ads = False

    def CueCreepToCrouchOK(self):
        if not self.owner.prone_end_for_crouch:
            return
        self.owner.prone_end_for_crouch = False
        if self.motion_state != cconst.UNIT_STATE_EXIT_PRONE:
            return
        self.OnProne(False, to_stand=False)
        if self.owner.prone_is_ads:
            self.owner.EnterAdsState(True)
            self.owner.prone_is_ads = False
        self.owner.last_crouch_timestamp = 0  # 快速按，cd
        self.owner.OnCrouch(force_to_crouch=True)
        self.owner.last_creep_up_timestamp = time.time()

    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_CREEP_STAND_OK)
    def cue_signal_creep_stand_ok(self, trigger, signal_str):
        # 蹲下->站立成功
        if self.motion_state != cconst.UNIT_STATE_EXIT_PRONE:
            return
        if self.owner.prone_end_for_crouch:
            self.CueCreepToCrouchOK()
            return
        self.JumpToState(cconst.UNIT_STATE_STAND)
        self.RefreshMoveSpeed()
        self.OnProne(False)
        if self.owner.prone_is_ads:
            self.owner.EnterAdsState(True)
            self.owner.prone_is_ads = False
        self.owner.last_creep_up_timestamp = time.time()

    def CueHighFallEnd(self):
        genv.messenger.Broadcast(events.ON_LINK_STATE_RELATIONSHIP, cconst.StateRelationship.HighFall, False)
        genv.messenger.Broadcast(events.ON_CHECK_SUPER_SPRINT)
        self.owner.RefreshSpeedAndMotion()

    def CueActionMotionCtrlBegin(self):
        # TODO (gzwanglinggui) 后续合并成一个引擎的调用
        charctrl = self.charctrl
        charctrl.SetCollisionFilterInfo(cconst.PHYSICS_TREES)
        # filterData = charctrl.GetCollisionFilterData()
        # filterData.mData3 |= 0x20000000  # Regular Query
        # charctrl.SetCollisionFilterData(filterData)

    def CueActionMotionCtrlEnd(self):
        charctrl = self.charctrl
        charctrl.SetCollisionFilterInfo(cconst.PHYSICS_SELF)
        # filterData = charctrl.GetCollisionFilterData()
        # filterData.mData3 |= 0x20000000  # Regular Query
        # charctrl.SetCollisionFilterData(filterData)

    def CueParachuteEnd(self):
        # self.owner and self.owner.OnLandGround() # 不要landground阶段
        self.owner and self.owner.OnLandEnd()

    def CueLandEnd(self):
        self.owner and self.owner.OnLandEnd()

    @CueEndCallbackDef('PlayerAvatarModel', 'OpenParachute_to_Parachute')
    def CueOpenParachuteEnd(self):
        self.owner and self.owner.OpenParachuteEnd()
        self.has_closed_parachute = False

    @CueEndCallbackDef('PlayerAvatarModel', 'Parachute_to_FreeFall')
    def CueParachuteToFreeFall(self):
        self.owner and self.owner.CloseParachuteEnd(is_fps=False)
        self.has_closed_parachute = True

    @CueEndCallbackDef('PlayerAvatarModel', 'Parachute_to_FreeFall_new')
    def CueParachuteToFreeFallNew(self):
        self.owner and self.owner.CloseParachuteEnd(is_fps=False)
        self.has_closed_parachute = True

    def CueParachuteCameraChange(self):
        # cue信息来了改为fps模式
        camera = genv.camera
        if not camera or not genv.player:
            return
        camera.RestoreCamera()

    def CueHideParachute(self):
        if self.para_state not in (cconst.UNIT_STATE_PARACHUTE, cconst.UNIT_STATE_OPEN_PARACHUTE):
            self.SetParachuteModelVisible(False)

    def CueOnParachuteReadyLand(self):
        # 准备落地， 不能再开伞了
        # self.FireEvent('near_ground', self.parachute_graph_id)
        if self.owner:
            self.owner.OnParachuteReadyLand()
            self.owner.hand_model.SetParachuteNearGroundAction(True)
        self.SetVariableI('enableParachuteBoneTrans', 0, self.parachute_graph_id)

    def CueOnParachuteOffGround(self):
        self.owner.hand_model.SetParachuteNearGroundAction(False)
        self.SetVariableI('enableParachuteBoneTrans', 1, self.parachute_graph_id)

    def CueOnParachuteActionTo5(self):
        # 开伞状态近地，action
        self.SetForceParachuteAction(True)

    def CueJumpFallLowTouched(self):
        print('JumpFallLowTouched')
        genv.messenger.Broadcast(events.ON_HIGH_SKY_PARA, False)
        self.owner.can_high_sky_para = False

    def CueJumpFallHighFlying(self):
        game_logic = self.owner and self.owner.game_logic
        if not game_logic or not game_logic.CAN_HIGH_SKY_PARA:
            return
        if self.owner.is_on_sky:
            return
        if self.owner.settle_data:
            # 结算了不要了
            return
        if self.motion_state in (cconst.UNIT_STATE_SWIM, cconst.UNIT_STATE_DEAD, cconst.UNIT_STATE_KNOCK_DOWN):
            return
        space = genv.space
        if not space:
            return
        if not space.loader.IsPhysicsPosReady(MType.Vector3(*self.owner.position)):
            return
        self.owner.can_high_sky_para = True
        genv.messenger.Broadcast(events.ON_HIGH_SKY_PARA, True)
        print('CueJumpFallHighFlying')

    def CueJumpFallHighForceOpen(self):
        if self.owner.can_high_sky_para and config.LocalConfig.force_open_parachute == cconst.FORCE_OPEN_PARACHUTE_ALWAYS:
            self.owner.RequestHighSkyParachute()

    def CueExecutionEnd(self):
        # 处决结束
        owner = self.owner
        if owner:
            owner.FinishExecuteTarget()

    def CueSamuraiSprintCanParachute(self):
        # 疾风斩结束，可以开伞了
        self.owner.can_para_after_samurai_sprint = True
        genv.messenger.Broadcast(events.ON_SAMURAI_SPRINT_CAN_PARA)


    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_SWITCH_WEAPON)
    def cue_signal_switch_weapon(self, trigger, signal_str):
        # print('==========sjh@ avatar_model_cue cue_signal_switch_weapon ', signal_str)
        switch_status = float(signal_str) > 0
        if not self.weapon_case:
            return
        owner = self.owner
        if not owner:
            return
        cur_weapon_guid = owner.cur_weapon_guid
        cur_spec_weapon_guid = owner.cur_spec_weapon_guid
        # sjh@ raise 是从tps_enter_combat进来的，只需要等graph播完自动pop就好
        # drop 是从tps_leave_combat的cue触发的，需要触发下一把武器的raise
        # print_s(f'sjh@ avatar_model_cue cue_signal_switch_weapon {switch_status}', SomePreset.white_fg_green_bg)  # 这个要1.4ms谁顶得住啊
        if switch_status:
            # raise
            return
        else:
            # drop
            if not cur_spec_weapon_guid and cur_weapon_guid:
                hand_model = self.owner.hand_model
                if hand_model.mp_fps_action in (cconst.MpFpsAction.DropWeapon,):
                    self._pending_for_raise_weapon = True
                else:
                    self.RaiseCurWeapon()
    
    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_WALL_KICK_CHECK)
    def cue_signal_wall_kick_check(self, trigger, signal_str):
        # print('==========sjh@ cue_signal_wall_kick_check ', signal_str)
        is_collide, check_tag = signal_str.split(':')
        is_collide = int(is_collide) > 0
        if is_collide:
            # if getattr(self, '_delay_clear_wallkick_end_timer', None):
            #     self.cancel_timer(self._delay_clear_wallkick_end_timer)
            #     self._delay_clear_wallkick_end_timer = None
            self.SetWallKickCheckState(True, check_tag=check_tag)
        else:
            # self._delay_clear_wallkick_end_timer = self.add_timer(0.2, self.SetWallKickCheckState, False)
            self.SetWallKickCheckState(False, check_tag=check_tag)

    def SetWallKickCheckState(self, enable_kick_check, check_tag):
        self.enable_wall_kick_check[check_tag] = enable_kick_check
    
    def ClearWallKickCheckState(self):
        self.enable_wall_kick_check.clear()

    @CueCallbackDef('PlayerAvatarModel', cconst.CUE_TYPE_CLIMB_DOCKING_DETECT)
    def cue_signal_climb_docking_detect(self, trigger, signal_str):
        # print('==========sjh@ cue_signal_climb_docking_detect ', signal_str)
        trigger_state, is_detected = signal_str.split(':')
        is_detected = int(is_detected)
        if not is_detected:
            return
        if not self.model:
            return
        mark = self.model.Skeleton.GetTargetMark()
        res = self.CalcCrossType(mark.normal, mark.pos, trigger_state)
        _, y = self.owner.GetMoveKeysState()
        if y <= 0:
            res = anim_const.DockingCrossType.NONE

        if res != anim_const.DockingCrossType.NONE:
            self.SetVariableI('IsDock', 1, self.locomotion_graph_id)
            if res == anim_const.DockingCrossType.CROSS_ON:
                if self.owner.CheckCanEnterStateRelationship(cconst.StateRelationship.Climb):
                    delta_height = mark.pos[1] - self.position[1]
                    self.RefreshCrossOnSpeedScale(res, delta_height)
                    self.RefreshCrossOnType(res, delta_height)
                    self.FireEvent("ToCrossOn", self.locomotion_graph_id)
            elif res == anim_const.DockingCrossType.CROSS_OVER:
                if self.owner.CheckCanEnterStateRelationship(cconst.StateRelationship.Cross):
                    self.FireEvent("ToCrossOver", self.locomotion_graph_id)
        self.DebugDrawDockingCrossType(res, mark)

    def CueTransfigureAnimEnd(self):
        # 这里要先显示武器再切graph，不然会出现备用弹匣显示的问题
        self.weapon_case.RemoveHiddenReason(cconst.HIDDEN_REASON_COMMON)
        self.DeleteAllShellComponent()

        owner = self.owner
        if not owner:
            return
        is_exit_by_death = owner.combat_state >= consts.CombatState.DYING
        if not is_exit_by_death:
            self.PopToLocomotionGraph()
        owner.PlayTransfigureEndAnimEnd()
    
    def CueSwitchCamera(self):
        camera = genv.camera
        if not camera:
            return
        camera.RestoreCamera(need_blender=True, blend_time=1.36)
    
    def CueSwitchCameraWithStrParam(self, param_str):
        # param_str = "blend_time;fov_time"
        camera = genv.camera
        if not camera:
            return
        blend_time, fov_time = param_str.split(';') 
        blend_time = float(blend_time)
        fov_time = float(fov_time)
        owner = self.owner
        is_fps_mode = owner.is_fps_mode and owner.combat_state == consts.CombatState.ALIVE
        is_ads = owner.is_ads
        print_s("fps_placer -> 使用相对相机位置来Blend", SomePreset.white_fg_yellow_bg)
        # camera.RestoreCamera(need_blender=True, blend_time=blend_time, fov_blend_time=fov_time, use_relative_camera_blender=True)
        if is_fps_mode or is_ads:
            camera.ApplyCamera(                              # noqa
                cconst.CAMERA_ID_DEFAULT,                    # noqa
                {                                            # noqa
                'blend_time': blend_time,                    # noqa
                'fov_blend_time': fov_time,                  # noqa
                'need_blender': True,                        # noqa
                'use_manual_hide_tps_model': True,           # noqa
                'use_relative_camera_blender': True,         # noqa
            },                                               # noqa
            )                                                # noqa
        else:                                                # noqa
            camera.ApplyCamera(                              # noqa
                cconst.CAMERA_ID_ALS_TPS,                    # noqa
                {                                            # noqa
                'blend_time' : blend_time,                   # noqa
                'fov_blend_time' : fov_time,                 # noqa
                'need_blender' : True,                       # noqa
                'use_manual_hide_tps_model': True,           # noqa
                'use_relative_camera_blender': True,         # noqa
            },                                               # noqa
            )                                                # noqa

    def CueApplySharkCameraFromTPS(self):
        print_s("CueApplySharkCameraFromTPS", SomePreset.white_fg_purple_bg)
        owner = self.owner
        is_fps_mode = owner.is_fps_mode
        is_ads = owner.is_ads
        if is_fps_mode or is_ads:
            print_s("CueApplySharkCameraFromTPS, 当前是fps或ads模式，不适用", SomePreset.white_fg_purple_bg)
            return

        camera = genv.camera
        if not camera:
            return
        direction = camera.placer.placer.Direction if camera.placer and camera.placer.placer and camera.placer.placer.IsValid() else None
        print_s("CueApplySharkCameraFromTPS", SomePreset.white_fg_purple_bg)
        camera.ApplyCamera(
            cconst.CAMERA_ID_SHARK_MODE,
            {
                'target_direction': direction if direction else None,
                'blend_time': 0.64,
            },
            True,
        )
        #     pass
        camera.placer.placer.PitchMax = 0.49 * math.pi
        camera.placer.placer.Fov = 45
        pass
    
    def CueManualHideTPSModel(self):
        owner = self.owner
        if not owner:
            return
        is_exit_by_death = owner.combat_state >= consts.CombatState.DYING
        if is_exit_by_death:
            return
        camera = genv.camera
        if not camera:
            return
        if hasattr(camera.placer, 'ManualHideTPSModel'):
            camera.placer.ManualHideTPSModel()
    
    def CueManualRaiseHandModelWeapon(self):
        if hasattr(self.owner, 'hand_model'):
            # self.owner.hand_model.RaiseCurUseWeapon()
            self.owner.hand_model.RaiseCurWeapon()

    def CueAddHiddenReasonFromCue(self):
        print_s("CueAddHiddenReasonFromCue", SomePreset.white_fg_cyan_bg)
        owner = self.owner
        if not owner:
            return
        is_exit_by_death = owner.combat_state >= consts.CombatState.DYING
        if is_exit_by_death:
            return
        self.AddHiddenReason(cconst.HIDDEN_REASON_COMMON)

    def CalcCrossType(self, normal, pos, trigger_state):
        # 计算攀爬检测类型
        hand_model = self.owner.hand_model
        if not hand_model:
            return anim_const.DockingCrossType.NONE
        speed_level = hand_model.GetCacheMoveSpeedLevel()
        if speed_level in (cconst.MoveSpeedLevel.Jog, cconst.MoveSpeedLevel.Walk):
            return anim_const.DockingCrossType.CROSS_ON
        if self.is_falling:
            return anim_const.DockingCrossType.CROSS_ON
        
        if speed_level not in (cconst.MoveSpeedLevel.SuperSprint, cconst.MoveSpeedLevel.Sprint):
            return anim_const.DockingCrossType.NONE
        
        start_pos = pos
        to_dir = self.model.Transform.z_axis
        check_len = anim_const.DOCK_CHECK_CHECK_LEN
        box_shape = anim_const.DOCK_CHECK_BOX_SHAPE
        start_pos = MType.Vector3(start_pos[0], start_pos[1] + anim_const.DOCK_BOX_CHECK_Y_OFFSET, start_pos[2])
        # 先水平sweep一下看看有没有阻挡
        res = genv.space.ClosestSweepWithBox(box_shape, start_pos, check_len, to_dir, cconst.PHYSICS_SELF)
        # [DEBUG]
        if getattr(genv, "enable_climb_debug", None):
            self.DebugDrawDockingBlock(res, pos)
        # [DEBUG]
        if res and res.IsHit:
            return anim_const.DockingCrossType.CROSS_ON
        else:
            return anim_const.DockingCrossType.CROSS_OVER

    def DebugDrawDockingCrossType(self, res, mark):
        # [DEBUG]
        if not getattr(genv, "enable_climb_debug", None):
            return
        from gclient.framework.destruct import cdestruct_debug
        color = (0, 1, 0)
        pos = mark.pos
        text = f"DockType:{res},t:{mark.type}\npos:{pos}"
        text_obj = cdestruct_debug.EQSItemObj(text, [pos[0], pos[1], pos[2]], color)
        genv._docking_cross_debug = text_obj

        # [DEBUG]
        pass

    def DebugDrawDockingBlock(self, collision_res, start_pos):
        # [DEBUG]
        genv._cross_debug = []
        if not collision_res.IsHit:
            return
        from gclient.util import debug_draw_util
        genv._cross_debug = []
        start_pos = MType.Vector3(start_pos[0], start_pos[1] + anim_const.DOCK_BOX_CHECK_Y_OFFSET, start_pos[2])
        to_dir = self.model.Transform.z_axis
        check_len = anim_const.DOCK_CHECK_CHECK_LEN
        box_shape = anim_const.DOCK_CHECK_BOX_SHAPE
        genv._cross_debug.append(debug_draw_util.draw_box(position=start_pos, rotation=(0, 0, 0), scale=box_shape, color=(0, 0, 1)))
        
        genv._cross_debug.append(debug_draw_util.draw_box(position=start_pos + to_dir * check_len, rotation=(0, 0, 0), scale=box_shape, color=(0, 1, 0)))
        genv._cross_debug.append(debug_draw_util.draw_sphere(position=collision_res.Pos, radius=0.1, color=(1, 0, 0)))
        # [DEBUG]
        pass

    @CueEndCallbackDef('PlayerAvatarModel', 'SprintSpell_to_SprintEnd')
    def CueSamuraiSprintEnd(self):
        if self.upper_graph_id:
            if genv.use_tps_action_graph and self.weapon_case:
                weapon_id = self.weapon_case.weapon_id
                if self.CanUseTPSActionGraph(weapon_id):
                    self.ChangeUpperMotionState('')
                    return
                else:
                    self.PopActionGraph()
            if self.upper_graph_id:
                self.PopUpperGraph()
            self.ChangeUpperMotionState('')
        if self.action_graph_id:
            self.ActionEndToIdle()
    
    def CueIsSharkClimbing(self, param_str):
        is_climbing = param_str == '1'
        print_s(f'CueIsSharkClimbing {is_climbing}', SomePreset.white_fg_yellow_bg)
        owner = self.owner
        if not owner:
            print_s('avatar_model_cue CueIsSharkClimbing owner is None', SomePreset.white_fg_red_bg)
            return
        if not hasattr(owner, 'shark_model'):
            print_s('avatar_model_cue CueIsSharkClimbing 没有shark_model', SomePreset.white_fg_red_bg)
            return
        if not owner.shark_model:
            print_s('avatar_model_cue CueIsSharkClimbing shark_model is None', SomePreset.white_fg_red_bg)
            return

        owner.CallServer("OnSharkClimpStateChange", is_climbing)

        owner.shark_model.skeleton.SetVariableZ(-1, 'is_climbing', is_climbing)
        owner.shark_model.skeleton.FireEvent(-1, 'climp_state_changing')

        owner.shark_model.is_climbing = is_climbing
        tach_comp = owner.shark_model.model.Tach
        if not tach_comp:
            return
        if is_climbing:
            # owner.shark_model.model.Tach.RotationMode = 0
            owner.shark_model.model.Tach.RotationMode = 2
            pass
        else:
            owner.shark_model.model.Tach.RotationMode = 2
            pass

    def CueSamuraiSwordCollide(self):
        # 这里是造成伤害得
        weapon_case = self.owner.GetCurWeaponCase()
        if not weapon_case or not weapon_case.IsSamuraiSword:
            return
        # 造成伤害
        weapon_case.OnSamuraiSprintCollide()
    
    def CueEnableFootFit(self):
        print_s('CueEnableFootFit', SomePreset.white_fg_yellow_bg)
        self.SetVariableZ('FootFitEnable', True, self.locomotion_graph_id)

