# -*- coding: utf-8 -*-
# author: chenjie01

# 第三人称跳伞model相关comps

import math, copy
import MType
from gclient.framework.util.story_tick import StoryTick
from gclient import cconst
from gclient.data import unit_model_data, moving_speed_data
from gclient.framework.util import <PERSON>H<PERSON>per
from gclient.framework.util import events
from gshare import formula, consts
from gshare.async_util import Async, TAG_SPACE, TAG_UNIQUE
from gshare.game_logic.igame_logic_parachute_comp import ParaAvatarReason, PARA_ON_SKY_STAGE
from gshare.game_logic.igame_logic_parachute_comp import ParaAvatarStage
from gshare import ireplay


class ParachuteUnitModelComp(object):
    # 管降落伞的挂接、以及多人跳伞的挂接
    def __init_component__(self):
        self.parachute_model = None
        self.bind_follower_entity = None
        self.follower_model = None  # 跟随的model, # 现在只有自己跟随有，只做自己挂接别人
        self._follower_story_tick = False
        events.BatchAddListeners(self)

    def __fini_component__(self):
        events.BatchRemoveListeners(self)
        self.DestroyParachuteModel()
        self.DestroyFollowerEntity()
        if self._follower_story_tick:
            StoryTick().Remove(self._TickFollowerYawToSelf)
            self._follower_story_tick = False

    @events.ListenTo(events.ON_REPLAY_CAMERA_MODE_CHANGE)
    def OnReplayCameraModeChange(self, mode):
        if self.owner is not genv.replay_player:
            return
        self.AddParachuteModel()

    def AddParachuteModel(self):
        is_self_model = self.owner is genv.player
        if self.owner.para_stage not in (ParaAvatarStage.OpenParachute, ParaAvatarStage.Parachute):
            return
        if not self.parachute_model:
            model_data = unit_model_data.data[28]
            resources = model_data.get('models')
            skeleton = model_data.get('skeleton')
            graph = model_data.get('basic_graph')
            self.parachute_model = MHelper.CreateModel(done=None, skeleton_file=skeleton, resource_names=resources,
                                                                                                  graph_file=graph)
            MHelper.SetIsCastDynamicShadow(self.parachute_model, True)
            # 不用挂接了，用graph来做，防止tick时序手对不上伞拖的问题
            if self.isValid():
                self.parachute_model.Skeleton.AddTargetSlot('main', self.model.Skeleton)
                self.AddTargetSlot('parachute', self.parachute_model.Skeleton)
        if self.owner is genv.replay_player:  # 观战的人的降落伞
            self.SetParachuteModelVisible(True)
            self.SetParachuteModelRenderSet(cconst.RENDER_SET_DEFAULT if genv.camera and genv.camera.replay_mode !=
                                            ireplay.CameraMode.FPS else cconst.RENDER_SET_SHADOW_PROXY)
        else:
            if not is_self_model:  # 其他人的降落伞
                self.SetParachuteModelVisible(True)
                self.SetParachuteModelRenderSet(cconst.RENDER_SET_DEFAULT)
            else:  # 自己的
                self.SetParachuteModelVisible(True)
                self.SetParachuteModelRenderSet(cconst.RENDER_SET_DEFAULT if not self.owner.use_camera_fps_for_para else
                                                                               cconst.RENDER_SET_SHADOW_PROXY)

    def DestroyParachuteModel(self):
        if self.parachute_model:
            MHelper.DestroySimpleModel(self.parachute_model)
        self.parachute_model = None

    def SetParachuteModelVisible(self, visible):
        if self.parachute_model:
            self.parachute_model.IsVisible = visible

    def SetParachuteModelRenderSet(self, render_set):
        if not self.parachute_model:
            return
        for prim in self.parachute_model.Primitives:
            prim.CustomRenderSet = render_set

    def CreateFollowerEntity(self):
        if not self.bind_follower_entity:
            self.bind_follower_entity = MHelper.CreateDummy()
            MHelper.LoadSkeletonAndGraph(self.bind_follower_entity, 'Char/item/Skydive_Follow/base.skeleton')
            self.bind_follower_entity.Skeleton.UseDynamicVisibilityBox = True
            self.bind_follower_entity.Attach(self.model)
            self.bind_follower_entity.Tach.Hardpoint = 'HP_sit'
            self.bind_follower_entity.Tach.Basepoint = 'HP_sit001'
            self.bind_follower_entity.Tach.EnableTachVisible = False
            MHelper.StoryboardCancelFrameLimit(self.bind_follower_entity)
        skeleton = self.model.Skeleton
        if skeleton:
            skeleton.UseDynamicVisibilityBox = True

    def DestroyFollowerEntity(self):
        if self._is_destroyed:
            return
        if self.bind_follower_entity:
            self.bind_follower_entity.Detach()
            self.bind_follower_entity.LeaveArea()
        self.bind_follower_entity = None
        if self.model and self.model.Skeleton:
            self.model.Skeleton.UseDynamicVisibilityBox = False
            self.model.Skeleton.SetEnableVisibilityOptimize(True)
            # 禁止不在视野内限帧，保证能同步tick
            self.model.Storyboard.EnableSleep = True

    def ResetYawForFolloweeModel(self):
        self._TickFollowerYawToSelf()

    def AddFollower(self, avatar_model):
        if not avatar_model.isValid():
            return
        self.follower_model = avatar_model
        avatar_skeleton = avatar_model.model.Skeleton
        if avatar_skeleton:
            avatar_skeleton.UseDynamicVisibilityBox = True
            avatar_skeleton.SetEnableVisibilityOptimize(False)
        # 禁止不在视野内限帧，保证能同步tick
        avatar_model.model.Storyboard.EnableSleep = False
        if cconst.PARACHUTE_FOLLOWER_NO_ATTACH:
            if not self._follower_story_tick:
                self.SetGraphYawHalfLife(0)
                StoryTick().Add(self._TickFollowerYawToSelf)
                self._follower_story_tick = True
            self.ResetYawForFolloweeModel()
        else:
            self.model.Detach()
            self.model.Attach(avatar_model.model)
            self.model.Tach.Basepoint = 'Scene Root'
            self.model.Tach.EnableTachVisible = False
            # self.model.Tach.RotationMode = 2  # 挂接不跟着旋转，tick旋转
            self.model.Tach.Hardpoint = 'Scene Root'
            trans = self.model.Tach.Transform
            offset_map = {1: (8, 1, -0.5),
                      2: (-8, 1, -0.5),
                      3: (0, -8, -5)}
            if self.owner.trainee_index in offset_map:
                trans.translation = MType.Vector3(*offset_map[self.owner.trainee_index])
            else:
                trans.translation = MType.Vector3(*offset_map[1])
            self.model.Tach.Transform = trans
        if self.owner.IsPlayerCombatAvatar:
            self.RefreshTraineeUseFollower()

    def _TickFollowerYawToSelf(self, dt=None):
        if not cconst.PARACHUTE_FOLLOWER_NO_ATTACH:
            return
        if self._is_destroyed:
            return
        if not self.follower_model or not self.follower_model.model:
            return
        trans = self.follower_model.model.Transform
        offset_map = {1: (8, 1, -0.5),
                            2: (-8, 1, -0.5),
                            3: (0, -8, -5)}
        offset = offset_map.get(self.owner.trainee_index, offset_map[1])
        new_pos = trans.transform_p(MType.Vector3(*offset))
        # 设置follower2的目标位置
        self.SetVariableV3('TrainerPos', new_pos)

        target_yaw = self.follower_model.yaw
        if not self.model:
            return
        trans = self.model.Transform
        trans.set_pitch_yaw_roll(trans.pitch, target_yaw, trans.roll)
        # self.model.Transform = trans
        self.yaw = target_yaw

    def RemoveFollower(self):
        if not self.follower_model:
            return
        pre_position = self.position
        pre_transform = self.model.Transform
        if self is genv.player.model:
            self.CheckLocalVelocityZ()
        self.model.Detach()
        if self.model.Tach:
            self.model.Tach.RotationMode = 0  # 记得还原
            self.model.Tach.Transform = MType.Matrix4x3()
        pre_transform.pitch = 0
        pre_transform.roll = 0
        self.model.Transform = pre_transform
        self.owner.position = pre_position
        if self.follower_model:
            self.follower_model.DestroyFollowerEntity()
        self.follower_model = None
        if self._follower_story_tick:
            self.SetGraphYawHalfLife(0.02)
            StoryTick().Remove(self._TickFollowerYawToSelf)
            self._follower_story_tick = False


class ParachuteGraphModel(object):
    # 跳伞grpah相关
    def __init_component__(self):
        self.parachute_graph_id = None
        self.cur_graph_state = None
        self._leave_trainer_speed_refresh_timer = None
        self._para_graph_file = "TPS/Locomotion/tps_off_aircraft.graph"

    def __clear_para_state_component__(self):
        self.parachute_graph_id = None
        self.cur_graph_state = None

    def PopToIdleGraph(self):
        self.JumpToState(cconst.UNIT_STATE_IDLE)

    def PushParaGraph(self):
        if not self.parachute_graph_id:
            self.PopToIdleGraph()
            skeleton = self.GetSkeleton()
            if skeleton:
                skeleton.JumpToState(self.locomotion_graph_id, 'Locomotion', 0, True)
            if self.pose_type == consts.ModelPoseType.Jump:
                self.JumpLand()
            self.parachute_graph_id = self.PushGraph(self._para_graph_file)
            if cconst.USE_NEW_VZ_CALCULATE:
                self.SetVariableI('use_new_parachute', 1)
                self.InitParaGraphValue()
            else:
                self.SetVariableI('use_new_parachute', 0)
            self.cur_graph_state = None
            self._SetGraphYaw(self.owner.yaw)
            self.CheckTrainerForPassive()
            if self.owner.IsPlayerCombatAvatar:
                self.Towards(0, 0)
                self.SetEnableControlCamera(True)
            return False
        self.ReadjustParaGraphId()
        self.CheckTrainerForPassive()
        return True

    def InitParaGraphValue(self):
        # input
        self.SetVariableF('freefall_speedup_input_threshold_new', cconst.FREEFALL_SHAKE_SPEED_UP_THRESHOLD)
        #  y的速度
        self.SetVariableF('freefall_min_y_new', -cconst.FREEFALL_MIN_Y_NEW)
        self.SetVariableF('freefall_normal_y_new', -cconst.FREEFALL_NORMAL_Y_NEW)
        self.SetVariableF('freefall_max_y_new', -cconst.FREEFALL_MAX_Y_NEW)
        #  y的加速度
        self.SetVariableF('freefall_min_acc_y_new', cconst.FREEFALL_MIN_ACC_Y_NEW)
        self.SetVariableF('freefall_normal_acc_y_new', cconst.FREEFALL_NORMAL_ACC_Y_NEW)
        self.SetVariableF('freefall_max_acc_y_new', cconst.FREEFALL_MAX_ACC_Y_NEW)
        # z的加速度
        self.SetVariableF('vz_acc_new', cconst.FREEFALL_ACC_Z_NEW)
        # x的加速度
        self.SetVariableF('vx_acc_new', cconst.FREEFALL_ACC_X_NEW)
        #  俯仰角
        self.SetVariableF('min_pitch_factor_y_new', cconst.MIN_PITCH_FACTOR_Y)
        self.SetVariableF('normal_pitch_factor_y_new', cconst.NORMAL_PITCH_FACTOR_Y)
        self.SetVariableF('max_pitch_factor_y_new', cconst.MAX_PITCH_FACTOR_Y)
        self.SetVariableF('min_pitch_factor_z_new', cconst.MIN_PITCH_FACTOR_Z)
        self.SetVariableF('normal_pitch_factor_z_new', cconst.NORMAL_PITCH_FACTOR_Z)
        self.SetVariableF('max_pitch_factor_z_new', cconst.MAX_PITCH_FACTOR_Z)

    def ReadjustParaGraphId(self):
        # 矫正graph_id ， 机器人反复切master slave，graph并没有pop出去
        skeleton = self.GetSkeleton()
        self.parachute_graph_id = 0
        if not skeleton:
            return
        graph_stack_info = skeleton.GetGraphStackInfo()
        for stack_info in graph_stack_info:
            if stack_info.layerName == 'Graph/%s' % self._para_graph_file:
                self.parachute_graph_id = stack_info.handle

    def CheckTrainerForPassive(self, force_check=False):
        # 飞机上跳下来的判定下跳伞跟随
        owner = self.owner
        if not owner:
            return
        if not self.parachute_graph_id:
            return
        if owner.para_reason != ParaAvatarReason.Airplane:
            return
        if owner.trainer_info:
            self.SetVariableI('is_active', 0, self.parachute_graph_id)
        else:
            self.SetVariableI('is_active', 1, self.parachute_graph_id)
            if force_check:
                self.ForceLockSpeedForLeaveTrainer()
        self.RefreshTraineeUseFollower()

    def RefreshTraineeUseFollower(self):
        if not cconst.PARACHUTE_FOLLOWER_NO_ATTACH:
            return
        owner = self.owner
        if not owner:
            return
        if not owner.IsPlayerCombatAvatar:
            return
        trainer = owner.trainer_info
        if not trainer:
            self.SetVariableI('TrianeeUseFollower2', False, self.parachute_graph_id)
            return
        trainer_avatar = owner.space.GetEntityByID(trainer)
        if not trainer_avatar or not trainer_avatar.model or not trainer_avatar.model.is_loaded:
            self.SetVariableI('TrianeeUseFollower2', False, self.parachute_graph_id)
            return
        self.SetVariableI('TrianeeUseFollower2', True, self.parachute_graph_id)
        self.SetVariableV3('TrainerPos', MType.Vector3(*trainer_avatar.position))

    def ForceLockSpeedForLeaveTrainer(self):
        # 脱离跳伞之后，自己的SYS_ENTITY_LOCAL_VELOCITY y方向速度突变成了0导致卡0了一会。
        self.SetVariableI('lock_speed', 1, self.parachute_graph_id)
        speed = self.GetVariableV3('SYS_ENTITY_LOCAL_VELOCITY')
        self.SetVariableV3('lock_speed_value', speed, self.parachute_graph_id)
        if cconst.USE_NEW_VZ_CALCULATE:
            self.SetVariableF('dest_vy_new', -speed.y, self.parachute_graph_id)  # 目标速度
            self.SetVariableF('dest_vz_new', speed.z, self.parachute_graph_id)
        else:
            self.SetVariableF('dest_vy', -speed.y, self.parachute_graph_id)  # 目标速度
            self.SetVariableF('dest_vz', speed.z, self.parachute_graph_id)
        self._CancelUnlockTrainerSpeedTimer()
        if self.owner:
            self._leave_trainer_speed_refresh_timer = self.owner.add_timer(0.5, self.UnlockSpeedForTrainerPara)

    def _CancelUnlockTrainerSpeedTimer(self):
        owner = self.owner
        if not owner:
            return
        if self._leave_trainer_speed_refresh_timer:
            owner.cancel_timer(self._leave_trainer_speed_refresh_timer)
            self._leave_trainer_speed_refresh_timer = None

    def UnlockSpeedForTrainerPara(self):
        self._leave_trainer_speed_refresh_timer = None
        if not self.isValid():
            return
        self.SetVariableI('lock_speed', 0, self.parachute_graph_id)
        owner = self.owner
        owner and owner.IsPlayerCombatAvatar and owner.RefreshKeyForRun()

    def JumpToStateOnAircraft(self):
        if not self.parachute_graph_id:
            self.PushParaGraph()
        skeleton = self.GetSkeleton()
        if skeleton:
            skeleton.JumpToState(self.parachute_graph_id, 'OnAircraft', 0, True)
        if self.owner.IsPlayerCombatAvatar:
            self.owner.CheckTeammatePoseOnLoaded()

    def JumpToStateFreeFall(self):
        if not self.parachute_graph_id:
            self.PushParaGraph()
        self.ReadjustParaGraphId()
        if self.cur_graph_state == 'OnAircraft':
            self.FireEvent('@Leave', self.parachute_graph_id)
        elif self.cur_graph_state == 'CloseParachute' or self.cur_graph_state == 'FreeFall':
            pass
        else:
            skeleton = self.GetSkeleton()
            if skeleton:
                skeleton.JumpToState(self.parachute_graph_id, 'FreeFall', 0, True)
        self.cur_graph_state = 'FreeFall'

    def JumpToStateOpenParachute(self):
        if self.cur_graph_state != 'FreeFall':
            self.JumpToStateFreeFall()
        self.FireEvent('@Open', self.parachute_graph_id)
        self.cur_graph_state = 'OpenParachute'

    def JumpToStateParachute(self):
        if not self.PushParaGraph():
            skeleton = self.GetSkeleton()
            if skeleton:
                skeleton.JumpToState(self.parachute_graph_id, 'Parachute', 0, True)
        elif self.owner.IsRobotCombatAvatar:
            skeleton = self.GetSkeleton()
            if skeleton:
                skeleton.JumpToState(self.parachute_graph_id, 'Parachute', 0, True)
        self.cur_graph_state = 'Parachute'

    def JumpToStateCloseParachute(self):
        if self.cur_graph_state != 'Parachute':
            self.JumpToStateParachute()
        self.FireEvent('@Close', self.parachute_graph_id)
        self.cur_graph_state = 'CloseParachute'

    def JumpToStateLandGround(self):
        if self.cur_graph_state != 'Parachute':
            self.JumpToStateParachute()
        self.FireEvent('@Land', self.parachute_graph_id)
        self.cur_graph_state = 'LandGround'

    def JumpToStateParaFinish(self):
        if self.parachute_graph_id:
            self.PopGraph(self.parachute_graph_id)
        self.cur_graph_state = None
        self.parachute_graph_id = None
        self.PopToIdleGraph()

    def SetIsOpenFire(self, is_open):
        if not self.parachute_graph_id:
            return
        self.SetVariableF('isOpenfire', float(is_open), self.parachute_graph_id)
        if is_open:
            self.RaiseCurWeapon()
        else:
            self.DropCurWeapon()

    def SetOpenFireAction(self, open_fire_action):
        if not self.parachute_graph_id:
            return
        self.SetVariableF('OpenFireAction', open_fire_action, self.parachute_graph_id)

    def SetOnAircraftPose(self, pose):
        if not self.parachute_graph_id:
            return
        self.SetVariableF('aircraft_pose', pose, self.parachute_graph_id)


class ParachuteControllerComp(object):
    # 摇杆控制
    def __init_component__(self):
        self.fall_joyshake = None
        self.fall_pitch_show = 0  # pitch 界面展示
        self._cache_roll_rad = None
        self._cache_pitch_action = None
        self._cache_pitch_rad = None
        self.parachute_tick_start = False
        self.parachute_input_x = 0
        self.parachute_input_y = 0
        self.parachute_last_pitch = 0
        self.parachute_freefall_xz_speed_dict = {}
        self.parachute_glide_xz_speed_dict = {}
        self.parachute_glide_y_speed_dict = {}
        self.has_init_para_graph = False
        ### 调参用
        self.parachute_pfactor_z = 0
        self.parachute_pfactor_y = 0
        self.parachute_vz = 0
        self.parachute_vy = 0
        self.parachute_vx = 0
        ### 调参用

    def GetParachuteConfig(self, move_dir, key):
        # 策划表配置的是8向目标速率，返回z和x方向的速率，斜向需要根据比率换算
        if move_dir == 0:
            move_dir = 8
        _dict = None
        if key == "freefall_xz_speed":
            _dict = self.parachute_freefall_xz_speed_dict
        elif key == "glide_xz_speed":
            _dict = self.parachute_glide_xz_speed_dict
        # elif key == "glide_y_speed":
        #     _dict = self.parachute_glide_y_speed_dict
        if _dict is None:
            return (0, 0)
        if move_dir in _dict:
            return _dict[move_dir]
        move_speed_data = moving_speed_data.data.get(move_dir)
        move_speed_data = move_speed_data.get
        dir_speed = move_speed_data(key, cconst.FREEFALL_MAX_MOVE_Z)
        vx = dir_speed
        vz = dir_speed
        dir_map = {
            1: (2, 4),
            3: (2, 6),
            7: (8, 4),
            9: (8, 6)
        }
        if move_dir in (2, 8):
            vx = 0
        elif move_dir in (4, 6):
            vz = 0
        else:
            (z_index, x_index) = dir_map.get(move_dir)
            (z_speed, _) = self.GetParachuteConfig(z_index, key)
            (_, x_speed) = self.GetParachuteConfig(x_index, key)
            if x_speed == 0:
                vx = 0
                vz = 0
            else:
                tan_value = z_speed / x_speed
                tan_angle = math.atan(tan_value)
                vz = dir_speed * math.sin(tan_angle)
                vx = dir_speed * math.cos(tan_angle)
        _dict[move_dir] = (vz, vx)
        return (vz, vx)

    def JoyShakeOnSky(self, x, y):
        if not self.owner:
            return
        self.fall_joyshake = (x, y)
        if self.para_state in (cconst.UNIT_STATE_FREEFALL, cconst.UNIT_STATE_OPEN_FIRE_FREEFALL):
            self.EventOnFreeFall((x, y))
        elif self.para_state == cconst.UNIT_STATE_PARACHUTE:
            self.JoyShakeOnParachute(x, y)
        if cconst.USE_NEW_VZ_CALCULATE:
            self.SetVariableI('use_new_parachute', 1)
        else:
            self.SetVariableI('use_new_parachute', 0)

    def ClearShakeOnSky(self):
        if cconst.USE_NEW_VZ_CALCULATE:
            self.has_init_para_graph = False
        self.fall_joyshake = None
        self.parachute_freefall_xz_speed_dict.clear()
        self.parachute_glide_xz_speed_dict.clear()
        self.parachute_glide_y_speed_dict.clear()

    def EventOnFreeFall(self, joy_off=None, pitch=None):
        if self.fall_joyshake is None:
            return
        camera = genv.camera
        if not camera:
            return
        if joy_off:  # 摇杆变化
            if not camera.placer or not camera.placer.placer or not camera.placer.placer.IsValid():
                # 断线重连，cameraplacer还没好
                pitch = 0
            else:
                pitch = camera.placer.placer.Direction.pitch
        self.parachute_input_x = self.fall_joyshake[0]
        self.parachute_input_y = self.fall_joyshake[1]
        if cconst.USE_NEW_VZ_CALCULATE:
            self.UpdateFreeFallPoseNew(self.parachute_input_x, self.parachute_input_y, pitch)
        else:
            self.UpdateFreeFallPose(self.parachute_input_x, self.parachute_input_y, pitch)




    def UpdateFreeFallPoseNew(self, x, y, pitch):
        #  每次输入变化时需要传的值
        self.SetVariableF('parachute_input_x_new', x)
        self.SetVariableF('parachute_input_y_new', y)
        max_z = cconst.FREEFALL_MAX_MOVE_Z
        max_x = cconst.FREEFALL_MAX_MOVE_X
        owner = self.owner
        if owner:
            move_dir = owner.move_dir
            (max_z, max_x) = self.GetParachuteConfig(move_dir, "freefall_xz_speed")
        vz_max_new = 0 if (x == 0 and y == 0) else max_z
        vx_max_new = 0 if (x == 0 and y == 0) else max_x
        self.SetVariableF('vz_max_new', vz_max_new)
        self.SetVariableF('vx_max_new', vx_max_new)
        # 飞行姿态
        fall_action = cconst.GetFallActionByXYFreeFall(x, y)
        self.SetParachuteFallAction(fall_action)
        self.SetGraphJoyStickYaw(x, y)
        # 俯仰姿态
        # fall_speedup = y > cconst.FREEFALL_SHAKE_SPEED_UP_THRESHOLD
        # 相机角度影响加速方向
        # pfactor_z = 0.0  # 影响因子，pitch越大， 因子越大
        # pfactor_y = 0.0
        # acc_y = cconst.FREEFALL_MIN_ACC_Y_NEW
        # if fall_speedup:
        #     max_pitch_z = cconst.MAX_PITCH_FACTOR_Z
        #     normal_pitch_z = cconst.NORMAL_PITCH_FACTOR_Z
        #     min_pitch_z = cconst.MIN_PITCH_FACTOR_Z
        #     if pitch < normal_pitch_z:
        #         pfactor_z = formula.LinearMapNumber(pitch, (min_pitch_z, normal_pitch_z), (0, 0.5))
        #     else:
        #         pfactor_z = formula.LinearMapNumber(pitch, (normal_pitch_z, max_pitch_z), (0.5, 1))
        #     max_pitch_y = cconst.MAX_PITCH_FACTOR_Y
        #     normal_pitch_y = cconst.NORMAL_PITCH_FACTOR_Y
        #     min_pitch_y = cconst.MIN_PITCH_FACTOR_Y
        #     if pitch < normal_pitch_y:
        #         pfactor_y = formula.LinearMapNumber(pitch, (min_pitch_y, normal_pitch_y), (0, 0.5))
        #         acc_y = cconst.FREEFALL_NORMAL_ACC_Y_NEW
        #     else:
        #         pfactor_y = formula.LinearMapNumber(pitch, (normal_pitch_y, max_pitch_y), (0.5, 1))
        #         acc_y = cconst.FREEFALL_MAX_ACC_Y_NEW

        # z速度
        # acc_z = cconst.FREEFALL_ACC_Z_NEW
        # max_z = cconst.FREEFALL_MAX_MOVE_Z
        # max_x = cconst.FREEFALL_MAX_MOVE_X
        # owner = self.owner
        # if owner:
        #     move_dir = owner.move_dir
        #     (max_z, max_x) = self.GetParachuteConfig(move_dir, "freefall_xz_speed")
        # v_forward = 0 if (x == 0 and y == 0) else max_z * (1.0 - pfactor_z) * (0.4 + 0.6 * y)
        # self.SetVariableF('dest_vz_new', v_forward)
        # self.SetVariableF('vz_acc_new', acc_z)
        # y速度
        # min_y, normal_y, max_y = cconst.FREEFALL_MIN_Y_NEW, cconst.FREEFALL_NORMAL_Y_NEW, cconst.FREEFALL_MAX_Y_NEW
        # vy_limit = normal_y
        # if fall_speedup:
        #     vy_limit = max_y
        # dest_vy = formula.LerpNumber(min_y, vy_limit, pfactor_y)
        # self.SetVariableF('dest_vy_new', -dest_vy)
        # self.SetVariableF('vy_acc_new', acc_y)
        # x速度
        # acc_x = cconst.FREEFALL_ACC_X_NEW
        # v_left = -x * max_x
        # self.SetVariableF('dest_vx_new', v_left)
        # self.SetVariableF('vx_acc_new', acc_x)
        # handmodel
        if self.owner and self.owner.hand_model:
            self.owner.hand_model.SetFreefallFallAction(1 if y > 0.5 else 0)
        # self.parachute_last_pitch = pitch
        # 参数调试用
        speed = self.GetVariableV3('SYS_ENTITY_LOCAL_VELOCITY')
        self.parachute_pfactor_z = self.GetVariableF('pfactor_z_new')
        self.parachute_pfactor_y = self.GetVariableF('pfactor_y_new')
        self.parachute_vz = speed[2]
        self.parachute_vy = speed[1]
        self.parachute_vx = speed[0]

    def UpdateFreeFallPose(self, x, y, pitch):
        # 飞行姿态
        fall_action = cconst.GetFallActionByXYFreeFall(x, y)
        self.SetParachuteFallAction(fall_action)
        self.SetGraphJoyStickYaw(x, y)
        # 俯仰姿态
        fall_speedup = y > cconst.FREEFALL_SHAKE_SPEED_UP_THRESHOLD
        if fall_speedup:
            fall_pitch = formula.ClampNumber(pitch, *cconst.FREEFALL_PITCH_RANGE)  # 跳伞视角限制增大，加个clamp
            # 跳伞队长PITCH转动导致队员客户端抖动问题
            # self.SetVariableF('pitch_rad', fall_pitch)
        else:
            fall_pitch = formula.ClampNumber(y * 35 * math.pi / 180, *cconst.FREEFALL_PITCH_RANGE)
            # self.SetVariableF('pitch_rad', fall_pitch)
        self.fall_pitch_show = fall_pitch
        # 相机角度影响加速方向
        pfactor_z = 0.0  # 影响因子，pitch越大， 因子越大
        pfactor_y = 0.0
        if fall_speedup:
            max_pitch_z = 1.25
            normal_pitch_z = 0.4
            min_pitch_z = -0.6
            if pitch < normal_pitch_z:
                pfactor_z = formula.LinearMapNumber(pitch, (min_pitch_z, normal_pitch_z), (0, 0.5))
            else:
                pfactor_z = formula.LinearMapNumber(pitch, (normal_pitch_z, max_pitch_z), (0.5, 1))
            max_pitch_y = 1.25
            normal_pitch_y = 0.4
            min_pitch_y = -0.6
            if pitch < normal_pitch_y:
                pfactor_y = formula.LinearMapNumber(pitch, (min_pitch_y, normal_pitch_y), (0, 0.5))
            else:
                pfactor_y = formula.LinearMapNumber(pitch, (normal_pitch_y, max_pitch_y), (0.5, 1))

        # z速度
        max_z = cconst.FREEFALL_MAX_MOVE_Z
        v_forward = 0 if (x == 0 and y == 0) else max_z * (0.5 + 0.5 * y)  # 目标值
        self.SetVariableF('dest_vz', v_forward)
        self.SetVariableF('dest_vz_acc', 20)
        self.FireEvent('@CounterStart')  # 每次重新驱动counter，防止stopped
        # y速度
        min_y, normal_y, max_y = cconst.FREEFALL_MIN_Y, cconst.FREEFALL_NORMAL_Y, cconst.FREEFALL_MAX_Y
        dest_vy = min_y + (y + 1.0) * (normal_y - min_y)
        normal_max = normal_y * 2 - min_y
        if fall_speedup:
            # add_part = (max_y - normal_max) * (pfactor_y * 2 - 1.0)
            # print("cxt test origianl vy ", dest_vy, " add_part ", add_part, " max_y ", max_y, " normal_max ", normal_max)
            dest_vy += (max_y - normal_max) * (pfactor_y * 2 - 1.0)  # pitch越大，y速度越大
        self.SetVariableF('dest_vy', -dest_vy)
        self.SetVariableF('freefall_ratio', cconst.FREEFALL_GRAVITY_ACC_RATIO)
        # x速度
        v_left = -x * cconst.FREEFALL_MAX_MOVE_X
        self.SetVariableF('v_left', v_left)
        # handmodel
        if self.owner and self.owner.hand_model:
            self.owner.hand_model.SetFreefallFallAction(1 if y > 0.5 else 0)
        self.parachute_last_pitch = pitch

    def JoyShakeOnParachuteNew(self, x, y, force=False):
        self.SetVariableF('parachute_input_x_new', x)
        self.SetVariableF('parachute_input_y_new', y)
        # y速度
        min_y, normal_y, max_y = cconst.PARACHUTE_MIN_Y_NEW, cconst.PARACHUTE_NORMAL_Y_NEW, cconst.PARACHUTE_MAX_Y_NEW
        if y <= 0:
            dest_vy = min_y + (y + 1.0) * (normal_y - min_y)
        else:
            dest_vy = normal_y + y * (max_y - normal_y)
        self.SetVariableF('dest_vy_new', -dest_vy)  # 目标速度
        acc_y = cconst.PARACHUTE_ACC_Y_NEW
        self.SetVariableF('vy_acc_new', acc_y)
        # 转向速度
        v_yaw = -x * cconst.PARACHUTE_ROTATE_YAW_SPEED
        pitch = y * cconst.PARACHUTE_ROTATE_PITCH_SPEED
        roll = formula.ClampNumber(x * cconst.PARACHUTE_ROTATE_ROLL_SPEED, -0.6, 0.6)
        self.SetVariableF('v_yaw', v_yaw)
        self.SetParachutePitchRad(pitch)
        self.SetParachuteRollRad(roll)
        fall_action = cconst.GetFallActionByRollPitchParachute(pitch, roll) if not self.force_parachute_action else 5
        self.SetParachuteFallAction(fall_action)
        self.SetGraphJoyStickYaw(x, y)
        if self.owner and self.owner.hand_model:
            self.owner.hand_model.SetParachuteFallActionByJoystick(x, y)
        # z速度
        max_z = cconst.PARACHUTE_MAX_MOVE_Z
        max_x = cconst.PARACHUTE_SPEED_X_FACTOR_FOR_FPS
        acc_xz = cconst.PARACHUTE_ACC_XZ_NEW
        owner = self.owner
        if owner:
            move_dir = owner.move_dir
            (max_z, max_x) = self.GetParachuteConfig(move_dir, "glide_xz_speed")
        dest_vz = y * max_z
        self.SetVariableF('dest_vz_new', dest_vz)
        self.SetVariableF('vz_acc_new', acc_xz)
        # x速度
        dest_vx = -max_x * x
        self.SetVariableF('dest_vx_new', dest_vx)
        self.SetVariableF('vx_acc_new', acc_xz)
        if cconst.USE_NEW_VZ_CALCULATE:
            self.parachute_vz = dest_vz
            self.parachute_vy = -dest_vy
            self.parachute_vx = dest_vx

    def SyncParachuteEndSpeed(self):
        parachute_to_freefall_last_speed = self.GetVariableV3('SYS_ENTITY_LOCAL_VELOCITY')
        self.SetVariableF('vy_record_new', parachute_to_freefall_last_speed[1])

    def JoyShakeOnParachute(self, x, y, force=False):
        if cconst.USE_NEW_VZ_CALCULATE:
            self.JoyShakeOnParachuteNew(x, y, force)
            return
        # y速度
        min_y, normal_y, max_y = cconst.PARACHUTE_MIN_Y, cconst.PARACHUTE_NORMAL_Y, cconst.PARACHUTE_MAX_Y
        if y <= 0:
            dest_vy = min_y + (y + 1.0) * (normal_y - min_y)
        else:
            dest_vy = normal_y + y * (max_y - normal_y)
        self.SetVariableF('dest_vy', -dest_vy)  # 目标速度
        # 转向速度
        v_yaw = -x * cconst.PARACHUTE_ROTATE_YAW_SPEED
        pitch = y * cconst.PARACHUTE_ROTATE_PITCH_SPEED
        roll = formula.ClampNumber(x * cconst.PARACHUTE_ROTATE_ROLL_SPEED, -0.6, 0.6)
        self.SetVariableF('v_yaw', v_yaw)
        self.SetParachutePitchRad(pitch)
        self.SetParachuteRollRad(roll)
        fall_action = cconst.GetFallActionByRollPitchParachute(pitch, roll) if not self.force_parachute_action else 5
        self.SetParachuteFallAction(fall_action)
        self.SetGraphJoyStickYaw(x, y)
        if self.owner and self.owner.hand_model:
            self.owner.hand_model.SetParachuteFallActionByJoystick(x, y)
        # for other in self.trainee_models:
        #   other.SetVariableI('fall_action', fall_action)
        # z速度
        if y > 0.2:
            dest_vz = formula.ClampNumber(y, 0, 1) * cconst.PARACHUTE_MAX_MOVE_Z
        else:
            dest_vz = 0
        self.SetVariableF('dest_vz', dest_vz)
        self.SetVariableF('dest_vz_acc', cconst.PARACHUTE_FORWARD_ACCELERATION)
        # x速度
        if self.owner.IsRobotCombatAvatar or self.owner.use_camera_fps_for_para:
            dest_vx = -cconst.PARACHUTE_SPEED_X_FACTOR_FOR_FPS * x
        else:
            dest_vx = 0
        self.SetVariableF('dest_vx', dest_vx)
        self.FireEvent('@CounterStart')

    def SetGraphJoyStickYaw(self, x, y):
        if not x and not y:
            self.SetVariableI('IsJoyStickInput', 0)
            self.SetVariableF('JoyStickYaw', 0)
            owner = self.owner
            if owner and owner.IsPlayerCombatAvatar and owner.trainee_info:
                owner.CallServer('SyncTrainerParaAction', {'is_joystick_input': 0, 'joystick_yaw': 0})
            return
        fall_action = cconst.GetFallActionByXYFreeFall(x, y)
        is_joystick_input = 1 if fall_action > 0 else 0
        self.SetVariableI('IsJoyStickInput', is_joystick_input)
        angle = formula.Angle2D((0, 1), (x, y))
        value = angle if x > 0 else -angle
        self.SetVariableF('JoyStickYaw', value)
        owner = self.owner
        if owner and owner.IsPlayerCombatAvatar and owner.trainee_info:
            owner.CallServer('SyncTrainerParaAction', {'is_joystick_input': is_joystick_input, 'joystick_yaw': value})

    def SetParachuteFallAction(self, fall_action):
        if self._cache_pitch_action == fall_action:
            return
        self._cache_pitch_action = fall_action
        self.SetVariableI('fall_action', fall_action)
        owner = self.owner
        if owner and owner.IsPlayerCombatAvatar and owner.trainee_info:
            owner.CallServer('SyncTrainerParaAction', {'fall_action': fall_action})

    def SetParachuteRollRad(self, roll_rad):
        if self._cache_roll_rad == roll_rad:
            return 
        self._cache_roll_rad = roll_rad
        self.SetVariableF('roll_rad', roll_rad)
        owner = self.owner
        if owner and owner.IsPlayerCombatAvatar and owner.trainee_info:
            owner.CallServer('SyncTrainerParaAction', {'roll_rad': roll_rad})

    def SetParachutePitchRad(self, pitch):
        if self._cache_pitch_rad == pitch:
            return
        self._cache_pitch_rad = pitch
        self.SetVariableF('pitch_rad', pitch)
        owner = self.owner
        if owner and owner.IsPlayerCombatAvatar and owner.trainee_info:
            owner.CallServer('SyncTrainerParaAction', {'pitch_rad': pitch})


class ParachuteModelComp(object):
    # 跳伞的阶段，控制逻辑，playerAvatar, RobotCombatAvatar拥有
    def __init_component__(self):
        self.para_state = None
        self.enable_speedz_openpara = True
        self.force_parachute_action = False

    def __clear_para_state_component__(self):
        self.para_state = None
        self.force_parachute_action = False

    def EnableSpeedZOpenPara(self, value):
        self.enable_speedz_openpara = value

    def ChangeParachuteCameraMode(self):
        self.SetVariableI('is_fps_mode', int(self.owner.use_camera_fps_for_para))
        self.SetParachuteModelRenderSet(cconst.RENDER_SET_DEFAULT if not self.owner.use_camera_fps_for_para else
                                                                        cconst.RENDER_SET_SHADOW_PROXY)

    def _InitParachuteSpeedZ(self, local_z=True):
        if not local_z:
            local_speed_z = 0
        else:
            local_speed_z = self.GetVariableV3('SYS_ENTITY_LOCAL_VELOCITY').z
        if cconst.USE_NEW_VZ_CALCULATE:
            self.SetVariableF('dest_vz_new', local_speed_z)
            # self.SetVariableF('vz_record_new', local_speed_z)
        else:
            self.SetVariableF('dest_vz', local_speed_z)

    def SetForceParachuteAction(self, force):
        self.force_parachute_action = force
        if force:
            self.SetVariableI('fall_action', 5)
            self.owner.hand_model.SetParachuteFallAction(5)

    def _OnJumpTo_OnAircraft(self, info):
        self.SetParaModelParaState(cconst.UNIT_STATE_ON_AIRCRAFT)
        self.JumpToStateOnAircraft()

    def _OnJumpTo_FreeFall(self, info):
        self.SetParaModelParaState(cconst.UNIT_STATE_FREEFALL)
        if info.get('from_freefall_with_gun'):
            # 从拿枪进来不需要处理
            return
        self.DropCurWeapon()
        self.JumpToStateFreeFall()
        self.SetVariableF('max_vz', cconst.FREEFALL_MAX_MOVE_Z)
        # self.owner.IsPlayerCombatAvatar and self.CueJumpStartToFall()
        if self.fall_joyshake:
            self.EventOnFreeFall(self.fall_joyshake)
        self.CheckFreefallMaxSpeed()
        # self.CheckLocalVelocityZ()

    @Async(TAG_SPACE | TAG_UNIQUE)
    def CheckFreefallMaxSpeed(self):
        if self.para_state in (cconst.UNIT_STATE_FREEFALL, cconst.UNIT_STATE_OPEN_FIRE_FREEFALL):
            return
        # 从飞机上跳下，获取到的速度巨大，先停掉0.2s
        self.SetVariableI('lock_speed', 1, self.parachute_graph_id)
        yield 0.2
        if not self.isValid():
            return
        self.SetVariableI('lock_speed', 0, self.parachute_graph_id)

    @Async(TAG_SPACE | TAG_UNIQUE)
    def CheckLocalVelocityZ(self):
        if self.para_state in (cconst.UNIT_STATE_FREEFALL, cconst.UNIT_STATE_OPEN_FIRE_FREEFALL):
            return
        self.SetVariableI('init_speed_z', 1)
        yield 0.5
        if not self.isValid():
            return
        self.SetVariableI('init_speed_z', 0)

    def _OnJumpTo_FreeFallOpenFire(self, info):
        self.JumpToStateFreeFall()
        self.SetParaModelParaState(cconst.UNIT_STATE_OPEN_FIRE_FREEFALL)
        self.RaiseCurWeapon()

    def _OnJumpTo_PassiveFly(self, info):
        self.SetParaModelParaState(cconst.UNIT_STATE_PASSIVEFLY)
        self.DropCurWeapon()

    def _OnJumpTo_OpenParachute(self, info):
        self.SetParaModelParaState(cconst.UNIT_STATE_OPEN_PARACHUTE)
        self.DropCurWeapon()
        self.JumpToStateOpenParachute()
        is_high_sky = info.get('is_high_sky')
        speed_z = self.GetVariableV3('SYS_ENTITY_LOCAL_VELOCITY').z
        if is_high_sky and speed_z <= cconst.HIGH_SKY_PARA_MIN_SPEED_Z:
            # 高空跳伞给个至少给个前进速度
            speed_z = cconst.HIGH_SKY_PARA_MIN_SPEED_Z
        speed_z = speed_z if self.enable_speedz_openpara else 0
        # print '[OpenParachute] open parachute speed is ', speed_z, self.enable_speedz_openpara
        self.SetVariableF('open_para_speed_z', speed_z)
        self.SetVariableF('open_para_acc', cconst.OPEN_PARACHUTE_ACCELERATE)
        self.SetVariableI('is_from_common_jump', 1 if is_high_sky else 0)
        self._InitParachuteSpeedZ(local_z=True)

    def _OnJumpTo_Parachute(self, info):
        self.SetParaModelParaState(cconst.UNIT_STATE_PARACHUTE)
        self.DropCurWeapon()
        self.JumpToStateParachute()
        if self.owner.IsPlayerCombatAvatar:
            self.SetVariableI('is_fps_mode', int(self.owner.use_camera_fps_for_para))
        else:
            self.SetVariableI('is_fps_mode', 0)
        self.SetVariableF('max_vz', cconst.PARACHUTE_MAX_MOVE_Z)
        self.AddParachuteModel()
        self._InitParachuteSpeedZ(local_z=True)
        if self.owner.IsPlayerCombatAvatar:
            self.owner.RefreshKeyForRun()

    def _OnJumpTo_CloseParachute(self, info):
        self.SetParaModelParaState(cconst.UNIT_STATE_CLOSE_PARACHUTE)
        self.JumpToStateCloseParachute()
        self._InitParachuteSpeedZ(local_z=True)
        self.has_closed_parachute = False

    def _OnJumpTo_LandGround(self, info):
        self.SetParaModelParaState(cconst.UNIT_STATE_LANDGROUND)
        self.DestroyParachuteModel()
        self.DropCurWeapon()
        self.JumpToStateLandGround()

    def FinishParachuteGraph(self):
        self.SetParaModelParaState(None)
        self.JumpToStateParaFinish()
        self.owner.OnWeaponListChanged()
        self.RaiseCurWeapon()
        self.DestroyParachuteModel()
        self.force_parachute_action = False
        self.SetEnableControlCamera(False)

    def SetParaModelParaState(self, para_stage):
        if self.para_state == para_stage:
            return
        self.para_state = para_stage
        self.CheckEnableHandIKToWeapon()

    def CheckEnableHandIKToWeapon(self):
        if not self.owner.IsPlayerCombatAvatar:
            return
        if self.para_state == cconst.UNIT_STATE_OPEN_FIRE_FREEFALL:
            self.EnableHandIKToWeapon(True)
        else:
            self.EnableHandIKToWeapon(False)
