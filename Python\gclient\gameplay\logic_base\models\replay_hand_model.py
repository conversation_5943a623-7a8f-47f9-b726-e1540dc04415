# -*- coding:utf-8 -*
import functools
import time

import math
import <PERSON>ngine
import MObject
from common.classutils import Components
from gclient import cconst
from gclient.config import LocalConfig
from gclient.data import gun_attachments_data, gun_anim_data, gun_hand_anim_data, gun_fps_anim_data, equip_data, gun_anim_speed_data, \
    unit_model_data, lobby_item_data, hero_skin_data, effect_data
from gclient.framework.models.model_cache import ModelCache
from gclient.framework.models.simple_model import SimpleModel
from gclient.framework.util import MHelper, events
from gclient.framework.util.resource_loader import ResourceLoader
from gclient.gameplay.logic_base.comps.comp_spike_transfer import SpikeTransferHandModelComp
from gclient.gameplay.logic_base.equips.equip_case import EquipCaseFactory
from gclient.gameplay.logic_base.models.common_hand_model_cue import CommonHandModelCue
from gclient.gameplay.logic_base.models.hand_model_cue import ReplayHandModelCue
from gclient.gameplay.logic_base.models.model_attch_comp import Model<PERSON>ttach<PERSON>omp
from gclient.gameplay.logic_base.models.hand_model_extra_attach_comp import HandModelExtraAttachComp
from gclient.gameplay.logic_base.models.model_material_comp import HandModelMaterialChangeComp
from gclient.gameplay.uicomponents.hud_frontsight_comp import ReplayHudFrontsightComp
from gclient.util.decorators import ReplaySpectatorDecorator, ReplayMarkHandModelStatus
from gshare import consts, weapon_util, formula
from gclient.gameplay.logic_base.comps.comp_ladder import LadderHandModelComp
from gclient.gameplay.logic_base.models.model_stealth_comp import HandModelStealthComp
from gclient.gameplay.logic_base.models.para_hand_model_comp import HandParachuteStateCollectionComp,  ParachuteHandModelComp
from gclient.gameplay.logic_base.models import anim_const
from gclient.gameplay.logic_base.comps.comp_strop import StropHandModelComp

class InterruptInfo:
    def __init__(self, is_empty: bool, start_time: float):
        # 恢复时间，是否是空仓换弹
        self.is_empty = is_empty
        self.start_time = start_time

class HandModelAttachComp(ModelAttachComp):

    def PreloadAttachModel(self, model_id, hardpoint, basepoint):
        dict_key = "%d_%s" % (model_id, hardpoint)
        if dict_key in self.attach_normal_model_dict:
            return
        model_info = unit_model_data.data.get(model_id, {})
        if not model_info:
            return
        # check dlc
        if 'dlc_id' not in model_info or 'dlc_id' in model_info and genv.dlc_manager.CheckDlcExists(model_info['dlc_id']):
            model = MHelper.CreateModel(None, model_info['models'], model_info.get('skeleton'),
                                        model_info.get('basic_graph'))
            self.attach_normal_model_dict[dict_key] = model
            model.SetNoNearPlaneClip(True)
            for prim in model.Primitives:
                prim.CustomRenderSet = cconst.RENDER_SET_HUD_SIGHT
            model.IsVisible = False
            model.Attach(self.model)
            model.Tach.Hardpoint = hardpoint
            model.Tach.Basepoint = basepoint

    def CheckCreateAttachModel(self, model_id, is_attach, hardpoint, basepoint):
        dict_key = "%d_%s" % (model_id, hardpoint)
        if not is_attach:
            if dict_key in self.attach_normal_model_dict:
                model = self.attach_normal_model_dict[dict_key]
                model.IsVisible = False
                self.RemoveDynamicEntityForMaterialChange(model)
        else:
            if not self.isValid():
                return
            model = self.attach_normal_model_dict.get(dict_key)
            if not model:
                model_info = unit_model_data.data.get(model_id, {})
                if not model_info:
                    return
                # check dlc
                if 'dlc_id' not in model_info or 'dlc_id' in model_info and genv.dlc_manager.CheckDlcExists(model_info['dlc_id']):
                    model = MHelper.CreateModel(None, model_info['models'], model_info.get('skeleton'),
                                                model_info.get('basic_graph'))
                    self.attach_normal_model_dict[dict_key] = model
                    model.SetNoNearPlaneClip(True)
                    for prim in model.Primitives:
                        prim.CustomRenderSet = cconst.RENDER_SET_HUD_SIGHT
            if model:
                model.IsVisible = True
                model.Attach(self.model)
                model.Tach.Hardpoint = hardpoint
                model.Tach.Basepoint = basepoint
                self.AddDynamicEntityForMaterialChange(model)


@Components(ReplayHandModelCue, LadderHandModelComp, HandModelStealthComp,
            HandParachuteStateCollectionComp, ParachuteHandModelComp,
            HandModelMaterialChangeComp, HandModelExtraAttachComp,
            CommonHandModelCue, HandModelAttachComp, SpikeTransferHandModelComp, StropHandModelComp)
class ReplayHandModel(SimpleModel):

    def __init__(self, owner, position=None, yaw=0):
        super(ReplayHandModel, self).__init__(owner, position, yaw)
        self.weapon_dict = {}
        self.cur_client_weapon_guid = ''
        self.cur_client_lefthand_weapon_guid = ''

        self.pose_graph_id = None
        self.spell_graph_id = None

        self.overlay_action_graph_id = None
        self.overlay_action_state = cconst.UNIT_STATE_IDLE

        self.action_graph_id = None
        self.action_graph_path = ''
        self.action_state = cconst.UNIT_STATE_RAISEWEAPON  # 动作state 开枪/换弹等

        self.graph_move_state = cconst.UNIT_STATE_IDLE # graph正在跑的当前move节点
        self.move_state = cconst.UNIT_STATE_IDLE    # 逻辑上的移动state Walk/Jog/Sprint/SuperSprint
        self.pose_state = cconst.UNIT_STATE_STAND     # 姿态state 站/跳/蹲趴/滑铲

        self.camera_graph_id = None

        self.mp_fps_action = cconst.MpFpsAction.Idle
        self.texture_extend_distance = 0.0  # 战斗内1P物体贴图使用mip0

        self.is_ban_handik = False
        self.is_ban_move_handik = False
        self.is_ban_overlay_handik = False
        self.is_ban_right_handik = False
        self.is_ban_move_right_handik = False
        self.is_ban_overlay_right_handik = False
        self.is_slide_to_end = False

        self._callComponents('init')

        self.force_left_hand_ik = ''

        self.fire_start_time = 0

        self.middle_reload_map = {}

        self.replay_ads_optics_open_timer = None
        self.replay_ads_optics_close_timer = None

        # [DEBUG]
        self._start_raise_time = 0
        self._debug_reload_start_time_map = {}
        # [DEBUG]

        self._is_empty_reload = False

    @property
    def pose_type(self):
        if not self.owner:
            return consts.ModelPoseType.Stand
        return self.owner.pose_type

    def Refresh(self):
        # OnModelLoad 刷新最新状态
        self.RefreshWeaponState()
        self.RefreshCurStatus()
        self._callComponents('on_refresh_model')

    def RefreshAnimationDetail(self):
        value = int(bool(LocalConfig.fps_animation_detail))
        self.SetVariableI("IsLRPoseOn", value, self.pose_graph_id)
        self.SetVariableI("IsInertiaOn", value, self.pose_graph_id)

    def RefreshWeaponState(self):
        self.RefreshWeaponList()
        owner = self.owner
        if owner.IsCurTakeGunWeapon() or owner.IsCurTakeMeleeWeapon():
            self.RaiseCurWeapon()
            if owner.IsCurTakeLeftHandWeapon():
                self.RaiseCurLeftHandWeapon()
        else:
            self.RaiseCurSpecWeapon()
        if self.owner.is_in_swim:
            self.owner.SetCurWeaponCaseVisibility(False, cconst.HIDDEN_REASON_SWIM)

    def RefreshCurStatus(self):
        if not self.owner:
            return
        cur_status = self.owner.handmodel_status
        if not cur_status:
            return
        for key, value in cur_status.items():
            status, args = value
            refresh_func = getattr(self, '_on_refresh_status_%s' % key, None)
            if refresh_func:
                refresh_func(status, *args)
            else:
                func_name = ReplayMarkHandModelStatus.StatusToMethod.get(key, '')
                refresh_func = getattr(self, func_name, None)
                refresh_func and refresh_func(*args[0], **args[1])

    def RefreshStatusByKey(self, key):
        cur_status = self.owner.handmodel_status
        if not cur_status:
            return
        if key not in cur_status:
            return
        status, args = cur_status[key]
        refresh_func = getattr(self, '_on_refresh_status_%s' % key, None)
        if refresh_func:
            refresh_func(status, *args)
        else:
            func_name = ReplayMarkHandModelStatus.StatusToMethod.get(key, '')
            refresh_func = getattr(self, func_name, None)
            refresh_func and refresh_func(*args[0], **args[1])

    def _on_refresh_status_is_fire_start(self, status, args, kwargs):
        if status:
            self.JumpToFireStart(*args)
        else:
            self.JumpToFireEnd()

    def _on_refresh_status_move_state(self, status, args, kwargs):
        if status == 'SuperSprint':
            self._OnJumpTo_Sprint(*args, **kwargs)
            self._OnJumpTo_SuperSprint(*args, **kwargs)
        else:
            func = getattr(self, '_OnJumpTo_%s' % status, None)
            func and func(*args, **kwargs)

    def _on_refresh_status_is_ads(self, status, args, kwargs):
        self.JumpToAds(*args, **kwargs)
        is_ads = bool(args[0])
        self.owner.is_ads = is_ads
        if ReplayHudFrontsightComp.isInited():
            if status:
                ReplayHudFrontsightComp.instance().AimPointFadeOut(0)
            else:
                ReplayHudFrontsightComp.instance().AimPointFadeIn(0)
        self.RefreshWeaponCaseOptic()

    def RefreshWeaponCaseOptic(self):
        if self.owner.is_ads:
            cur_weapon_case = self.owner.GetCurWeaponCase(is_fps_weapon=True)
            if not cur_weapon_case or not cur_weapon_case.is_gun:
                return
            optic_part = cur_weapon_case.GetWeaponPart(consts.WeaponPartType_Optic)
            if not optic_part:
                return
            # if not weapon_util.IsWeaponOpticNeedReflector(cur_weapon_case.part_slots):
            #     return
            optic_crosshair_prim = cur_weapon_case.GetOpticCrosshairPrim()
            if not optic_crosshair_prim:
                return
            if not optic_crosshair_prim.IsReadyToAppear:
                return self.add_timer(0.2, self.RefreshWeaponCaseOptic)
            cur_weapon_case.ResetOpticModelForAds(True)
            cur_weapon_case.RefreshOpticMaterial(True)
            cur_weapon_case.RefreshOpticEffect(True)

    @property
    def weapon_model(self):
        weapon_case = self.weapon_case
        if not weapon_case:
            return None
        return weapon_case.weapon_model

    @property
    def dual_weapon_model(self):
        weapon_case = self.weapon_case
        if not weapon_case or not weapon_case.is_gun:
            return None
        return weapon_case.dual_weapon_model

    @property
    def weapon_case(self):
        return self.owner.GetCurWeaponCase(True)

    @property
    def lefthand_weapon_case(self):
        return self.owner.GetCurLeftHandWeaponCase(True)

    @property
    def hand_entity(self):
        return self.owner.hand_entity

    def PlaySkeletonEffect(self, effect_str, max_life=5.0, insure_play=False, effect_id=None):
        if effect_id in self.effect_dict and not insure_play:
            # 有就不重复播了  TODO
            return
        s = self.GetSkeleton()
        if not s:
            return
        insure_play and s.SetInsureEffectPlay(True)
        eid = s.PlayEffect(effect_str, max_life)
        effect_entities = s.GetEffectEntities(eid)
        for effect_entity in effect_entities:
            # 为啥要改False？ 因为不改挂相机面向不对，不要问为啥，撕逼过了，没结果。
            # (CameraAttaching后的特效Transform会错，解决方案：特效不勾选Local或者Local下改Facing为Z，这个方案不行，效果也会错)
            tach = effect_entity.Tach
            if tach:
                # 这里应该有个时序问题，特效跟随模式是0的时候，Tach大概率获取不到
                effect_entity.Tach.CameraAttaching = False
            effect_entity.SetNoNearPlaneClip(True)
            for p in effect_entity.Primitives:
                p.CustomRenderSet = cconst.RENDER_SET_HUD_SIGHT
        insure_play and s.SetInsureEffectPlay(False)
        if effect_id is not None:
            self.effect_dict[effect_id] = eid
        return eid

    def AddHiddenReason(self, reason):
        super(ReplayHandModel, self).AddHiddenReason(reason)
        for weapon in self.weapon_dict.values():
            weapon.weapon_model.AddHiddenReason(reason)

    def ClearHiddenReason(self):
        super(ReplayHandModel, self).ClearHiddenReason()
        for weapon in self.weapon_dict.values():
            weapon.weapon_model.ClearHiddenReason()

    def RemoveHiddenReason(self, reason):
        super(ReplayHandModel, self).RemoveHiddenReason(reason)
        for weapon in self.weapon_dict.values():
            weapon.weapon_model.RemoveHiddenReason(reason)

    def AddHiddenReasonForNonMainWeapons(self, reason):
        """只对非主手武器添加隐藏原因(包括副手武器)"""
        for guid, weapon in self.weapon_dict.items():
            if guid != self.cur_client_weapon_guid:
                weapon.weapon_model.AddHiddenReason(reason)

    def RemoveHiddenReasonForNonMainWeapons(self, reason):
        """只对非主手武器移除隐藏原因(包括副手武器)"""
        for guid, weapon in self.weapon_dict.items():
            if guid != self.cur_client_weapon_guid:
                weapon.weapon_model.RemoveHiddenReason(reason)

    def SetCustomRenderSet(self, renderSet):
        super(ReplayHandModel, self).SetCustomRenderSet(renderSet)
        for weapon in self.weapon_dict.values():
            weapon.SetCustomRenderSet(renderSet)
        
    def SetTachCameraSpaceTranslationOffset(self, offset):
        super(ReplayHandModel, self).SetTachCameraSpaceTranslationOffset(offset)
        for weapon in self.weapon_dict.values():
            weapon.SetTachCameraSpaceTranslationOffset(offset)
        for decoration in self.owner.decoration_model_list:
            decoration.SetTachCameraSpaceTranslationOffset(offset)

    def SetNoNearPlaneClip(self, enable):
        super(ReplayHandModel, self).SetNoNearPlaneClip(enable)
        for weapon in self.weapon_dict.values():
            weapon.SetNoNearPlaneClip(enable)

    def RefreshAllWeaponDecorates(self):
        for weapon in self.weapon_dict.values():
            weapon.RefreshWeaponDecorate()

    def SetPitchAndYaw(self, pitch, yaw):
        self.pitch = pitch
        self.yaw = yaw
        if self.owner:
            self.owner.model.yaw = yaw

    def _GetAvailableModelInfo(self, model_data):
        model_has_down = genv.dlc_manager.CheckDlcExists(model_data.get('dlc_id'))
        if not model_has_down or model_data['id'] == 78:
            model_id = model_data.get('dlc_default_id', 0)
            model_data = unit_model_data.data.get(model_id, {})
        else:
            model_id = model_data.get('id', 0)
        return model_id, model_data

    def Load(self, data, done=None):
        super(ReplayHandModel, self).Load(data, done if done else self.OnHandModelLoad)
        camera = MEngine.GetGameplay().Player.Camera
        camera_controller = genv.camera.controller
        if genv.use_fps_camera_control_v2:
            self.model.Attach(camera_controller)
        else:
            self.model.Attach(camera)
        self.model.Tach.Basepoint = 'tag_camera_scripted'
        # self.WarmUpWeaponAnimations(0)
        MHelper.StoryboardCancelFrameLimit(self.model)
        self.PlayHeroSkinHandEffect()
        # self.AttachToCamera()

    def OnHandModelLoad(self):
        self.loaded = True
        self.SetEnableControlCamera(True)
        self.SetEnableVisibilityOptimize(False)
        self.PushPoseGraph()
        self.SetCustomRenderSet(cconst.RENDER_SET_HUD_SIGHT)
        self.SetIsCastDynamicShadow(False)
        self.ReceiveDynamicShadow()
        self.RefreshAnimationDetail()
        self.OnHandModelLoadedForCamera()
        camera = genv.camera
        if self.owner.IsRobotCombatAvatar:
            # 观战机器人相机是客户端模拟
            camera.placer.EnableStoryTickForReplay()
        self._callComponents('on_loaded')

    def Reload(self, data, done=None, use_ready_to_appear=True):
        loaded = self.loaded
        super(SimpleModel, self).Reload(data, done)
        if not self.model.IsValid():
            return
        self.AddLoadTask("ReloadResource")

        # 换骨骼
        self._skeletonIsValid = False
        self.model.Skeleton.SetExpireCallback(None)
        self.ClearAllSkeletonEffect()
        self.CheckRecreateSkeletonOnReloaded(data)
        skeleton = self.model.Skeleton
        if skeleton:
            self._skeletonIsValid = True
            skeleton.SetExpireCallback(self.SkeletonExpireCallback)
            if skeleton.__class__ == MObject.ActorComponent and not self.force_no_graph:
                self.BindEventSignalNotify()  # 绑定model_cue事件

        self.model_id = data.get("id", 0)
        model_has_down = genv.dlc_manager.CheckDlcExists(data.get('dlc_id'))
        if not model_has_down:
            self.model_id = data.get('dlc_default_id', 0)
            data = unit_model_data.data.get(self.model_id, {})
        self._modelIsValid = False
        self.model.SetExpireCallback(None)
        models = data.get("models", ())
        self.DeleteAllShellComponent()
        ResourceLoader.ChangePrimitives(
            entity=self.model,
            resources=data.get("models", ()),
            use_ready_to_appear=use_ready_to_appear if loaded else False,
            done=lambda: self.RemoveLoadTask("ReloadResource")
        )
        # sjh@ setwarmingup是为了避免模型没加载好就显示，所以先隐藏，等ReadyToAppear再干掉warmup
        self.SetWarmingUp(True)
        self.resource_names = models
        self._modelIsValid = True
        self.model.SetExpireCallback(self.ModelExpireCallback)
        self.PlayHeroSkinHandEffect()

    def CheckRecreateSkeletonOnReloaded(self, data):
        # 是否需要重新创建骨骼
        skeleton_file = self.GetMotionSkeletonFile(data.get("skeleton", ""))
        graph_file = None if self.force_no_graph else data.get("basic_graph", "")
        self.base_graph_id = 0 if graph_file else None
        skeleton = self.model.Skeleton
        if not skeleton or skeleton.ScriptResourcePath != skeleton_file or skeleton.GraphFile != \
                ('Graph/%s' % graph_file if graph_file else ''):
            self.model.Skeleton.ClearAllEffects()  # 先清理一下上一个骨骼上的特效
            MHelper.LoadSkeletonAndGraph(
                entity=self.model,
                skeleton_file=skeleton_file,
                graph_file=graph_file,
            )
            self.MakeSureBones()
            # 重新push下graph
            self.pose_graph_id = None
            self.action_graph_id = None
            self.PushPoseGraph()
            self.PushActionGraph(self.action_graph_path)

    def OnHandModelReload(self):
        self.SetWarmingUp(False)
        self.SetEnableControlCamera(True)
        self.RefreshWeaponHiddenStateOnReload()
        self.RemoveXRayBlock()
        self.owner.model.model.Skeleton.AddChild(self.model.Skeleton)
        self._callComponents('on_reloaded')
    
    def RefreshWeaponHiddenStateOnReload(self):
        cur_weapon_case = self.owner.GetCurWeaponCase()
        if not cur_weapon_case:
            return
        for weapon in self.weapon_dict.values():
            if weapon.weapon_guid != cur_weapon_case.weapon_guid:
                weapon.model.AttachWeaponToHandBack()

    def OnHandModelLoadedForCamera(self):
        camera = genv.camera
        if not camera:
            return
        camera.RestoreCamera()
        camera.SetFocusTarget(self.owner.model)
        near = gpl.CAMERA_NEAR_DEFAULT if self.owner.pose_type != cconst.ModelPoseType.Swim else gpl.CAMERA_NEAR_SWIM
        camera.RefreshNearAndFar(near)

    def PlayHeroSkinHandEffect(self):
        suit_id = self.owner.arm_clothes.get(consts.ItemSubType.Suit, 0)
        lobby_item_proto = lobby_item_data.data.get(suit_id, {})
        hero_id = lobby_item_proto.get('hero_id')
        skin_id = lobby_item_proto.get('hero_skin_id')
        if not hero_id or skin_id is None:
            return
        hero_skin_proto = hero_skin_data.data.get(hero_id, {}).get(skin_id, {})
        if not hero_skin_proto:
            return
        hand1p_effect = hero_skin_proto.get('hand1p_effect', ())
        for effect_id in hand1p_effect:
            self.PlaySkeletonEffect(effect_data.data[effect_id]['effect_string'], -1, True)

    def OnDead(self):
        self.PopOverlayActionGraph()
        self.AddHiddenReason(cconst.HIDDEN_REASON_DEAD)

    def ResetStatus(self):
        self.RaiseCurWeapon()
        self.RemoveHiddenReason(cconst.HIDDEN_REASON_DEAD)
        self.RemoveHiddenReason(cconst.HIDDEN_REASON_GHOST)

    def OnReborn(self):
        self.ResetStatus()

    def OnGhost(self):
        self.AddHiddenReason(cconst.HIDDEN_REASON_GHOST)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToAds(self, is_ads):
        self.SetIsAds(is_ads)
        if is_ads:
            self.owner.StopSoundByTag(tag='AdsDown')
            self.FireEvent('@ads_up', self.action_graph_id)
            self.FireEvent('@ads_up', self.pose_graph_id)
        else:
            self.owner.StopSoundByTag(tag='AdsUp')
            self.FireEvent('@ads_down', self.action_graph_id)
            self.FireEvent('@ads_down', self.pose_graph_id)

        cur_weapon = self.owner.GetCurWeaponCase()
        cur_weapon and cur_weapon.is_gun and cur_weapon.JumpToAds(is_ads)

        if self.owner.IsRobotCombatAvatar and genv.camera:
            camera = genv.camera
            camera.placer.FSMAppend('Aim', True, bool(is_ads))
            aim_time = self.weapon_case.GetWeaponAttrValue('AdsUpAnimTimeConf', 0.2) if is_ads else self.weapon_case.GetWeaponAttrValue('AdsDownAnimTimeConf', 0.2)
            if ReplayHudFrontsightComp.isInited():
                if is_ads:
                    ReplayHudFrontsightComp.instance().AimPointFadeOut(aim_time)
                else:
                    ReplayHudFrontsightComp.instance().AimPointFadeIn(aim_time)

        if hasattr(cur_weapon, "GetWeaponPartModel"):
            cur_optic = cur_weapon.GetWeaponPartModel(consts.WeaponPartType_Optic)
            if cur_optic:
                if is_ads:
                    self.replay_ads_optics_close_timer and self.cancel_timer(self.replay_ads_optics_close_timer)
                    self.replay_ads_optics_close_timer = None
                    # self.SetAdsOpticPreStencil(cur_optic, False)   
                    # self.SetAdsOpticPreStencil(cur_optic, is_ads)   
                    self.replay_ads_optics_open_timer = self.add_timer(0.2, functools.partial(self.SetAdsOpticPreStencil, cur_optic, is_ads))
                else:
                    self.replay_ads_optics_open_timer and self.cancel_timer(self.replay_ads_optics_open_timer)
                    self.replay_ads_optics_open_timer = None
                    # self.SetAdsOpticPreStencil(cur_optic, is_ads)
                    self.replay_ads_optics_close_timer = self.add_timer(0.2, functools.partial(self.SetAdsOpticPreStencil, cur_optic, is_ads))

    def SetAdsOpticPreStencil(self, optic_model, value: bool):
        if value:
            optic_model.SetCustomRenderSet(cconst.RENDER_SET_HUD_PRE_STENCIL)
        else:
            optic_model.SetCustomRenderSet(cconst.RENDER_SET_HUD_SIGHT)

    def AttachToAvatar(self):
        self.model.Attach(self.owner.model.model)
        self.model.Tach.ScaleMode = 1
        self.model.Tach.RotationMode = 2
        self.model.Tach.EnableTachVisible = False
        self.model.Tach.Basepoint = 'Scene Root'
        self.model.Tach.Hardpoint = 'Scene Root'

    def BanHandIKToWeapon(self, is_ban, ban_type):
        # 这是左手
        if ban_type == 1:
            self.is_ban_handik = is_ban
        elif ban_type == 2:
            self.is_ban_move_handik = is_ban
        elif ban_type == 3:
            self.is_ban_overlay_handik = is_ban

    def BanRightHandIKToWeapon(self, is_ban, ban_type):
        # 这是右手
        if ban_type == 1:
            self.is_ban_right_handik = is_ban
        elif ban_type == 2:
            self.is_ban_move_right_handik = is_ban
        elif ban_type == 3:
            self.is_ban_overlay_right_handik = is_ban

    def EnableHandIKToWeapon(self, enable=True, hand_bone='WL', gun_bone='HP_Hand_Left_IK', ik_type=cconst.HAND_IK_TYPE_LEFT, left_ik_blend_time=0.1):
        if ik_type in (cconst.HAND_IK_TYPE_RIGHT, cconst.HAND_IK_TYPE_BOTH) and not self.IsForceUnableRightHandIK():
            # 只处理右手
            self.HandIkToWeapon(enable, 'WR', gun_bone, ik_type=cconst.HAND_IK_TYPE_RIGHT, left_ik_blend_time=left_ik_blend_time)

        # 只处理左手
        if enable and hand_bone == 'WL' and self.IsForceUnableLeftHandIK():
            return
        if hand_bone == 'WL' and self.force_left_hand_ik:
            gun_bone = self.force_left_hand_ik
        self.HandIkToWeapon(enable, hand_bone, gun_bone, ik_type=cconst.HAND_IK_TYPE_LEFT, left_ik_blend_time=left_ik_blend_time)

    def IsForceUnableLeftHandIK(self):
        if self.is_ban_handik or self.is_ban_move_handik or self.is_ban_overlay_handik:
            return True
        if self.action_state in (cconst.UNIT_STATE_RELOAD, ):
            return True
        # if self.pose_type in (consts.ModelPoseType.Slide, ) or self.is_slide_to_end:
        #     return True
        if self.overlay_action_graph_id:
            return True
        if self.move_state in (cconst.UNIT_STATE_SUPERSPRINT, ):
            return True

    def IsForceUnableRightHandIK(self):
        if self.is_ban_right_handik or self.is_ban_move_right_handik or self.is_ban_overlay_right_handik:
            return True
        if self.action_state in (cconst.UNIT_STATE_RELOAD, ):
            return True
        # if self.pose_type in (consts.ModelPoseType.Slide, ) or self.is_slide_to_end:
        #     return True
        if self.overlay_action_graph_id:
            return True
        if self.move_state in (cconst.UNIT_STATE_SUPERSPRINT, ):
            return True

    @ReplayMarkHandModelStatus('is_hand_ik', status_args_idx=0)
    @ReplaySpectatorDecorator('ReplayForHandModel')
    def HandIkToWeapon(self, enable=True, hand_bone='WL', gun_bone='HP_Hand_Left_IK', ik_type=cconst.HAND_IK_TYPE_LEFT, left_ik_blend_time=0.1):
        if ik_type in (cconst.HAND_IK_TYPE_LEFT, cconst.HAND_IK_TYPE_BOTH):
            self.SetVariableS('IKEndNode', hand_bone, 0)
            self.SetVariableS('IKTargetBone', gun_bone, 0)
            self.SetVariableI('IKHand', 0 if hand_bone in ('WL', 'tag_weapon_left') else 1, 0)
            self.SetVariableI('HandIkEnable', enable, 0)
            self.SetVariableF('LeftIKBlendTime', left_ik_blend_time, 0)
        if ik_type in (cconst.HAND_IK_TYPE_RIGHT, cconst.HAND_IK_TYPE_BOTH):
            self.SetVariableI('RightHandIkEnable', enable, 0)
        return True

    def RefreshWeaponList(self):
        if not self.isValid():
            # model不合法 跳过
            return
        weapon_list = self.owner.GetWeaponList()
        # 新增
        need_add = []
        for weapon in weapon_list:
            if weapon in self.weapon_dict:
                continue
            need_add.append(weapon)
        # 删除
        need_del = []
        for weapon in list(self.weapon_dict.keys()):
            if weapon in weapon_list:
                continue
            # 删除
            need_del.append(weapon)

        for cur_del in need_del:
            self.DestroyWeapon(cur_del, force_destroy=True)
        for cur_add in need_add:
            self.AddWeapon(cur_add)

    def GetCurClientWeaponCase(self):
        return self.GetWeaponCase(self.cur_client_weapon_guid)

    def GetCurClientLeftHandWeaponCase(self):
        return self.GetWeaponCase(self.cur_client_lefthand_weapon_guid)

    def GetWeaponCase(self, guid):
        return self.weapon_dict.get(guid)

    def GetWeaponCaseByWeaponId(self, weapon_id):
        for weapon in self.weapon_dict.values():
            if weapon.weapon_id == weapon_id:
                return weapon

    def AddWeapon(self, guid):
        if not self.isValid():
            return
        equip = self.owner.backpack.Get(guid)
        equip_id = equip.equip_id
        equip_guid = equip.guid
        skin_id = equip.skin_template_id
        guise_id = equip.guise_template_id
        hangings_id = equip.ornament_item_id
        sticker_dict = equip.skin_sticker
        part_guise_info = equip.part_guise_info
        from_airdrop = equip.owner_name == self.owner.name
        if not equip_id or not equip_guid:
            return
        if equip_guid in self.weapon_dict and self.weapon_dict[equip_guid].is_match_server:
            return
        weapon_case = EquipCaseFactory.Create(equip_id, self.owner, equip.guid, equip_id, skin_id, guise_id, extra={
            'hangings_id': hangings_id, 'sticker': sticker_dict, 'part_guise_info': part_guise_info, 'from_airdrop': from_airdrop
        })
        if not weapon_case:
            return
        weapon_case.InitWeapon()
        self.weapon_dict[guid] = weapon_case

    def ChangeWeapon(self):
        if not self.isValid():
            return
        self.DropCurWeapon()

    def ChangeLeftHandWeapon(self):
        if not self.isValid():
            return
        self.DropCurLeftHandWeapon()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def ForceDropWeapon(self, guid):
        # 强制把客户端当前的武器卸下 无动作 只清状态
        # 处理客户端和服务器不同步时候的异常情况
        if not self.isValid():
            return
        if guid == self.cur_client_lefthand_weapon_guid:
            self.cur_client_lefthand_weapon_guid = ''
        elif guid == self.cur_client_weapon_guid:
            self.cur_client_weapon_guid = ''
        cur_weapon = self.GetWeaponCase(guid)
        if cur_weapon:
            cur_weapon.ForceDropCurWeapon()
            self.UnpinWeaponAnimations(cur_weapon.weapon_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def DropCurWeapon(self):
        if not self.isValid():
            return
        cur_weapon = self.owner.GetCurWeaponCase()
        cur_weapon and cur_weapon.DropWeapon()
        self.owner.StopSoundByTag('Raise')

    def _CheckDropClientWeapon(self):
        if self.cur_client_weapon_guid and self.cur_client_weapon_guid != (self.owner.cur_spec_weapon_guid or self.owner.cur_weapon_guid):
            self.ForceDropWeapon(self.cur_client_weapon_guid)

    def _CheckDropClientLeftHandWeapon(self):
        if self.cur_client_lefthand_weapon_guid and self.cur_client_lefthand_weapon_guid != self.owner.cur_lefthand_weapon_guid:
            self.ForceDropWeapon(self.cur_client_lefthand_weapon_guid)

    def RaiseCurUseWeapon(self):
        if self.owner.cur_spec_weapon_guid:
            self.RaiseCurSpecWeapon()
        elif self.owner.cur_weapon_guid:
            self.RaiseCurWeapon()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def RaiseCurWeapon(self):
        if not self.isValid():
            return
        self._CheckDropClientWeapon()
        self.cur_client_weapon_guid = self.owner.cur_weapon_guid
        next_weapon = self.owner.GetWeaponCase(self.owner.cur_weapon_guid)
        next_weapon and next_weapon.RaiseWeapon()
        self.RefreshExtraAttachModelState()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def RaiseCurLeftHandWeapon(self):
        if not self.isValid():
            return
        self._CheckDropClientLeftHandWeapon()
        self.cur_client_lefthand_weapon_guid = self.owner.cur_lefthand_weapon_guid
        lefthand_weapon = self.owner.GetCurLeftHandWeaponCase()
        lefthand_weapon and lefthand_weapon.RaiseWeapon()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def DropCurLeftHandWeapon(self):
        if not self.isValid():
            return
        lefthand_weapon = self.owner.GetCurLeftHandWeaponCase()
        if lefthand_weapon:
            lefthand_weapon.DropWeapon()
            if self.cur_client_lefthand_weapon_guid == lefthand_weapon.weapon_guid:
                self.cur_client_lefthand_weapon_guid = ''

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def RaiseCurSpecWeapon(self, take_key_mode=cconst.UseItemMode.ITEM_DOWN):
        if not self.isValid():
            return
        self._CheckDropClientWeapon()
        self.cur_client_weapon_guid = self.owner.cur_spec_weapon_guid
        spec_weapon = self.owner.GetWeaponCase(self.owner.cur_spec_weapon_guid)
        if spec_weapon:
            if spec_weapon.IsSamuraiSword:
                spec_weapon.RaiseWeapon(take_key_mode)
            else:
                spec_weapon.RaiseWeapon()
        self.RefreshExtraAttachModelState()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def DropCurSpecWeapon(self):
        if not self.isValid():
            return
        spec_weapon = self.owner.GetCurSpecWeaponCase()
        spec_weapon and spec_weapon.DropWeapon()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def GetBackItem(self):
        if not self.isValid():
            return
        if self.overlay_action_graph_id:
            self.FireEvent('@GetBackItem', self.overlay_action_graph_id)
        elif self.spell_graph_id:
            self.FireEvent('@GetBackItem', self.spell_graph_id)
        elif self.action_graph_id:
            self.FireEvent('@GetBackItem', self.action_graph_id)

    def DestroyWeapon(self, guid, force_destroy=False):
        if guid not in self.weapon_dict:
            return
        weapon_case = self.weapon_dict[guid]
        # if not force_destroy and weapon_case.is_using:
        #     # 还在使用中 动画还在播
        #     weapon_case.is_delay_delete = True
        #     return
        del self.weapon_dict[guid]
        weapon_case.Destroy()

    # def DelayDestroyWeapon(self, guid):
    #     if guid not in self.weapon_dict:
    #         return
    #     weapon_case = self.weapon_dict[guid]
    #     del self.weapon_dict[guid]
    #     weapon_case.Destroy()

    def AddWeaponPart(self, weapon_guid, part_id):
        if not self.isValid():
            return
        cur_weapon = self.weapon_dict.get(weapon_guid)
        if not cur_weapon:
            return
        cur_weapon.AddWeaponPart(part_id)

    def RefreshUnderbarrelPose(self):
        # 换下挂后 刷新姿态
        if not self.owner.IsCurTakeGunWeapon():
            return
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        underbarrel_part_id = weapon_case.weapon_body.part_slots.get(consts.WeaponPartType_Underbarrel, {}).get('part_id')
        gun_attachments_data_getter = gun_attachments_data.data.get
        foregrip_pose_type = gun_attachments_data_getter(underbarrel_part_id, {}).get('foregrip_pose_type', 0)
        foregrip_pose_override = gun_attachments_data_getter(underbarrel_part_id, {}).get('foregrip_pose_override')
        if foregrip_pose_override:
            self.SetVariableS('GripIdlePoseAnimName', foregrip_pose_override, self.action_graph_id)
            self.SetVariableS('GripIdlePoseAnimName', foregrip_pose_override, self.pose_graph_id)
        else:
            self.SetGraphDynamicAnimByParameter(self.weapon_case.body_weapon_id, 'GripIdlePoseAnimName')
        self.SetVariableI('ForegripPoseType', foregrip_pose_type, self.action_graph_id)
        self.SetVariableI('ForegripPoseType', foregrip_pose_type, self.pose_graph_id)

    def RefreshBarrelPose(self):
        if not self.owner.IsCurTakeGunWeapon():
            return
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        barrel_part_id = weapon_case.weapon_body.part_slots.get(consts.WeaponPartType_Barrel, {}).get('part_id')
        gun_attachments_data_getter = gun_attachments_data.data.get
        barrel_pose_type = gun_attachments_data_getter(barrel_part_id, {}).get('barrel_pose_type', 0)
        barrel_pose_override = gun_attachments_data_getter(barrel_part_id, {}).get('barrel_pose_override')
        if barrel_pose_override:
            self.SetVariableS('AddIdlePoseAnimName', barrel_pose_override, self.action_graph_id)
            self.SetVariableS('AddIdlePoseAnimName', barrel_pose_override, self.pose_graph_id)
        else:
            self.SetGraphDynamicAnimByParameter(self.weapon_case.body_weapon_id, 'AddIdlePoseAnimName')
        self.SetVariableF('BarrelPoseType', barrel_pose_type, self.action_graph_id)
        self.SetVariableF('BarrelPoseType', barrel_pose_type, self.pose_graph_id)

    def RefreshStockPose(self):
        if not self.owner.IsCurTakeGunWeapon():
            self.SetVariableF('StockPoseType', 0, self.action_graph_id)
            self.SetVariableF('StockPoseType', 0, self.pose_graph_id)
            return
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        stock_part_id = weapon_case.weapon_body.part_slots.get(consts.WeaponPartType_Stock, {}).get('part_id')
        gun_attachments_data_getter = gun_attachments_data.data.get
        stock_pose_type = gun_attachments_data_getter(stock_part_id, {}).get('stock_pose_type', 0)
        self.SetVariableF('StockPoseType', stock_pose_type, self.action_graph_id)
        self.SetVariableF('StockPoseType', stock_pose_type, self.pose_graph_id)
        self.RefreshReplaceAnimName(consts.WeaponPartType_Stock)

    def RefreshDrumPose(self):
        if not self.owner.IsCurTakeGunWeapon():
            return
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        is_drum = weapon_util.IsWeaponAmmunitionIsDrum(weapon_case.weapon_body.part_slots)
        self.SetVariableI('IsDrum', is_drum, self.action_graph_id)
        self.SetVariableI('IsDrum', is_drum, self.pose_graph_id)
        self.RefreshReplaceAnimName(consts.WeaponPartType_Ammunition)

    def RefreshReplaceAnimName(self, weapon_part_type):
        # 根据配件修改动画名和动画时长
        if not self.owner.IsCurTakeGunWeapon():
            return
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        part_type_id = weapon_case.weapon_body.part_slots.get(weapon_part_type, {}).get('part_id')
        if not part_type_id:
            return
        gun_attachments_data_getter = gun_attachments_data.data.get
        part_data = gun_attachments_data_getter(part_type_id)
        if not part_data:
            return
        for arg in anim_const.WEAPON_PART_OVERRIDE_GRAPH_ARGS:
            value = part_data.get(arg, None)
            if arg.endswith('AnimName'):
                if value is not None:
                    self.SetVariableS(arg, value, self.action_graph_id)
                    self.SetVariableS(arg, value, self.pose_graph_id)
                else:
                    self.SetGraphDynamicAnimByParameter(self.weapon_case.body_weapon_id, arg)
            elif arg.endswith('AnimTime'):
                if value is not None:
                    self.SetVariableF(arg, value, self.action_graph_id)
                    self.SetVariableF(arg, value, self.pose_graph_id)
                else:
                    self.SetGraphDynamicAnimByParameter(self.weapon_case.body_weapon_id, arg)

    def RefreshOpticPose(self):
        # 换瞄具后 刷新姿态适应ads
        if not self.owner.IsCurTakeGunWeapon():
            return
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        cur_optic_id = weapon_case.part_slots[consts.WeaponPartType_Optic]['part_id']
        cur_optic_id = weapon_util.GetRealOpticPartIdByDlc(cur_optic_id)
        optic_guise_id = lobby_item_data.data.get(weapon_case.part_guise_info.get(cur_optic_id), {}).get('guise', {}).get('template_id')
        offset = weapon_util.GetGunOpticSightPoseOffset(weapon_case.gun_dlc_id, weapon_case.part_slots, weapon_case.guise_id, optic_guise_id)
        if offset:
            self.SetVariableV3('AdsTransOffset', offset, self.pose_graph_id)
            self.SetVariableV3('AdsTransOffset', offset, self.action_graph_id)

    def RefreshReloadMode(self):
        # 换瞄具后 某些枪需要刷新换弹的方式
        if not self.owner.IsCurTakeGunWeapon():
            return
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        need_special_reload = weapon_util.IsWeaponNeedSqueezeReload(weapon_case.weapon_body.part_slots)
        self.SetVariableF('IsNeedSqueeze', float(need_special_reload), self.action_graph_id)
        weapon_model = self.weapon_model
        if weapon_model and weapon_model.base_graph_id is not None:
            weapon_model.SetVariableF('IsNeedSqueeze', float(need_special_reload))

    def RefreshIsNeedRechamber(self, is_need):
        self.SetVariableI('IsNeedRechamber', is_need, self.action_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def RefreshIsReloadAllBullet(self):
        cur_weapon = self.owner.GetCurWeapon()
        cur_weapon_case = self.owner.GetCurWeaponCase()
        if not cur_weapon or not cur_weapon_case:
            return
        is_all = cur_weapon.backup_ammos <= 1 or cur_weapon.client_ammo >= cur_weapon_case.GetWeaponAttrValue('stock_capacity') - 1
        self.SetVariableI('IsReloadAllBullet', is_all, self.action_graph_id)
        if self.weapon_model and self.weapon_model.base_graph_id is not None:
            self.weapon_model.SetVariableI('IsReloadAllBullet', is_all)

    @ReplayMarkHandModelStatus('move_action_speed', status_args_idx=0)
    @ReplaySpectatorDecorator('ReplayForHandModel')
    def RefreshMoveActionSpeed(self, speed):
        if self.pose_graph_id is None or self.pose_graph_id < 0:
            return
        s = self.GetSkeleton()
        s and s.SetGraphActionPlaybackSpeed(self.pose_graph_id, 'Linear', speed, 0)
        return True

    @ReplayMarkHandModelStatus('gun_type', status_args_idx=0)
    @ReplaySpectatorDecorator('ReplayForHandModel')
    def RefreshHandGunType(self, gun_type):
        self.pose_graph_id and self.SetVariableI('GunType', gun_type, self.pose_graph_id)
        self.overlay_action_graph_id and self.SetVariableI('gun_type', gun_type, self.overlay_action_graph_id)
        return True

    def PinWeaponGraph(self, graph):
        self.PinGraphs('GraphForFpsWeapon', [graph, ])

    def UnpinWeaponGraph(self):
        self.UnpinResourcesByTag('GraphForFpsWeapon')

    def WarmUpWeaponAnimations(self, weapon_id, callback=None):
        tag = '%s_fps_anim' % weapon_id
        if not self.CheckNeedWarmUpAnimations(tag):
            return
        anim_id = equip_data.data.get(weapon_id, {}).get('anim_data_id')
        warmup_data = gun_fps_anim_data.data.get(anim_id)
        if not warmup_data:
            return
        anims = warmup_data['fps_anims']
        self.WarmUpAnimations(anims, callback)
        self.PinAnimations(tag, anims)  # 这里确定都是anim
    
    def WarmUpAndPinPermanentAnims(self):
        warmup_anims = self.GetPermanentWarmUpAnims()
        if not warmup_anims:
            return
        self.WarmUpAnimations(warmup_anims)
        tag = 'fps_permanent_anim'
        self.PinAnimations(tag, warmup_anims)
    
    def GetPermanentWarmUpAnims(self):
        # 获取常驻warmup动画
        # 1. raise动画
        collect_anim_keys = ('RaiseAnimName', 'FirstRaiseAnimName')
        permnent_warmup_anims = []
        for weapon_id, anim_data in gun_hand_anim_data.data.items():
            for key, anim_names in anim_data.items():
                for collect_key in collect_anim_keys:
                    anim_name = anim_names.get(collect_key)
                    anim_name and permnent_warmup_anims.append(anim_name)
        return permnent_warmup_anims

    def UnpinWeaponAnimations(self, weapon_id):
        anim_id = equip_data.data.get(weapon_id, {}).get('anim_data_id')
        warmup_data = gun_fps_anim_data.data.get(anim_id)
        if not warmup_data:
            return
        self.UnpinResourcesByTag('%s_fps_anim' % weapon_id)

    def PushPoseGraph(self):
        # 姿态graph  jump crouch slide
        if self.pose_graph_id:
            self.ReplaceGraph(self.pose_graph_id, 'FPS/Hand/fps_main_moveActions.graph')
        else:
            self.PopActionGraph() # pose graph必须在action graph下层 model refresh的时候时序问题可能会导致action graph在1层
            self.pose_graph_id = self.PushGraph('FPS/Hand/fps_main_moveActions.graph')
        return self.pose_graph_id

    def PushActionGraph(self, graph, blendTime=0):
        if not self.weapon_case:
            return
        self.action_graph_path = graph
        cur_weapon = self.owner.GetWeaponByGuid(self.weapon_case.weapon_guid)
        if cur_weapon:
            self.SetIsEmptyAmmo(cur_weapon.client_ammo <= 0)
            self.SetIsDualEmptyAmmo(cur_weapon.client_left_ammo <= 0)
        self.WarmUpWeaponAnimations(self.weapon_case.body_weapon_id)
        is_init = self.action_graph_id is None
        self.action_graph_id and self.PopGraph(self.action_graph_id)
        self.action_graph_id = self.PushGraph(graph, blendTime)
        # self.PinWeaponGraph(graph)
        self.RefreshAfterPushActionGraph()
        return self.action_graph_id

    def RefreshAfterPushActionGraph(self):
        # not is_init and self.SetIsFirstRaiseWeapon(self.weapon_case.is_gun and self.weapon_case.is_first_raise)
        self.SetIsDualWeapon(self.weapon_case.is_gun and self.weapon_case.is_dual_weapon)
        self.SetGraphMoveType(self.weapon_case.body_equip_proto.get('move_action_type', 0))
        self.SetGraphDynamicAnim(self.weapon_case.body_weapon_id)
        self.SetPoseAnimDynamicSpeed(self.weapon_case.body_weapon_id)
        self.weapon_case.RefreshGuiseOverlayIdlePose()
        self.weapon_case.RefreshGuiseSkinAnimPoseForHand()
        # not is_init and self.SetMpFpsAction(cconst.MpFpsAction.RaiseWeapon, 0)
        self.CheckLadderGraphOnActionGraph()
        return self.action_graph_id
    
    def RefreshGraphFireModeVal(self):
        if not self.weapon_case or not self.weapon_case.is_gun:
            self.SetVariableI("FireMode", 0, self.action_graph_id)
            return
        self.SetVariableI('FireMode', self.weapon_case.GetCurrentFireModeSelector(), self.action_graph_id)

    def PopActionGraph(self):
        if self.action_graph_id and self.action_graph_id > 0:
            self.PopGraph(self.action_graph_id)
            self.action_graph_id = None

    def PushOverlayActionGraph(self, graph, blendTime=0):
        if self.lefthand_weapon_case:
            self.WarmUpWeaponAnimations(self.lefthand_weapon_case.weapon_id)
        self.overlay_action_graph_id and self.PopGraph(self.overlay_action_graph_id)
        self.overlay_action_graph_id = self.PushGraph(graph, blendTime)
        self.RefreshHandGunType(self.owner.GetCurWeaponGunType())
        weapon_case = self.weapon_case
        weapon_case and self.SetGraphDynamicAnim(weapon_case.body_weapon_id)
        return self.overlay_action_graph_id

    def PopOverlayActionGraph(self, blendtime=0.0):
        if self.overlay_action_graph_id:
            self.PopGraph(self.overlay_action_graph_id, blendtime)
            self.overlay_action_graph_id = None

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def CommonOverlayActionEnd(self, pop_time=0.2):
        self.PopOverlayActionGraph(pop_time)

    def PushSpellGraph(self, graph, blendTime=0):
        self.spell_graph_id and self.PopGraph(self.spell_graph_id)
        self.spell_graph_id = self.PushGraph(graph, blendTime)
        return self.spell_graph_id

    def PopSpellGraph(self):
        if self.spell_graph_id and self.spell_graph_id > 0:
            self.PopGraph(self.spell_graph_id)
            self.spell_graph_id = None

    def OnLeftHandAdditive(self, is_add):
        if is_add:
            self.FireEvent('@tacticle_grenade_pull', self.action_graph_id)
        else:
            self.FireEvent('@tacticle_grenade_toss', self.action_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToMeleeStart(self, info=None, transtime=0.1, reset=False, spell_id=0):
        weapon_model = self.weapon_model
        if not weapon_model:
            return
        action_graph_id = self.action_graph_id
        if self.GetVariableF('Mp_Fps_Actions', action_graph_id) == cconst.MpFpsAction.Melee:
            self.FireEvent('@melee', action_graph_id)
        else:
            self.FireEvent('@melee', action_graph_id)
            self.SetMpFpsAction(cconst.MpFpsAction.Melee, 0)

        self.SetVariableI('isKeyUp', 0, action_graph_id)
        self.SetVariableI('isKeyDown', 1, action_graph_id)

        pose_graph_id = self.pose_graph_id
        self.SetVariableI('isKeyUp', 0, pose_graph_id)
        self.SetVariableI('isKeyDown', 1, pose_graph_id)
        
        # 450553 【US】使用武士刀、棒球棍滑铲接近战攻击，会有滑铲动作残留
        self.FireEvent("sliding_done", pose_graph_id)

        weapon_model.JumpToMeleeStart()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToMeleeEnd(self):
        self.SetVariableI('isKeyDown', 0, self.action_graph_id)
        self.SetVariableI('isKeyUp', 1, self.action_graph_id)
        self.SetVariableI('isKeyUp', 1, self.pose_graph_id)
        self.SetVariableI('isKeyDown', 0, self.pose_graph_id)
        self.FireEvent('@keyup', self.action_graph_id)
        weapon_model = self.weapon_model
        if weapon_model:
            weapon_model.JumpToMeleeEnd()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToFireStart(self, is_auto_fire=True):
        action_graph_id = self.action_graph_id
        if not action_graph_id:
            return
        if self.GetVariableF('Mp_Fps_Actions', action_graph_id) == cconst.MpFpsAction.Fire:
            self.FireEvent('@fire', action_graph_id)
        else:
            self.FireEvent('@fire', action_graph_id)
            self.SetMpFpsAction(cconst.MpFpsAction.Fire, 0)
            self.fire_start_time = time.time()
        skeleton = self.model.Skeleton
        if not skeleton:
            return
        SetVariableI = skeleton.SetVariableI
        SetVariableI(action_graph_id, 'isAutoFire', int(is_auto_fire))
        SetVariableI(action_graph_id, 'isKeyUp', 0)
        SetVariableI(action_graph_id, 'isKeyDown', 1)

        weapon_model = self.weapon_model
        if weapon_model:
            weapon_model.JumpToFireStart()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToFireEnd(self):
        if time.time() - self.fire_start_time <= 0.03 and self.weapon_case and weapon_util.IsWeaponCompoundBow(self.weapon_case.weapon_id):
            # inputselector的分支切换得在下一帧才执行
            # 开火的MpFpsAction分支和fire里的某个事件比如keyup如果是同一帧发送的话，分支会在下一帧才切，事件就会失效，就开不出火
            # [DEBUG]
            print('!!!!!!!!!!!!!!!!!!!!!!!!!! FireStart and FireEnd in same frame!!!!!! delay!!!!!!!!!!!!!!!!!!!!!!')
            # [DEBUG]
            return self.add_timer(0.1, self.JumpToFireEnd)
        self.SetVariableI('isKeyDown', 0, self.action_graph_id)
        self.SetVariableI('isKeyUp', 1, self.action_graph_id)
        self.FireEvent('@keyup', self.action_graph_id)
        weapon_model = self.weapon_model
        if weapon_model:
            weapon_model.JumpToFireEnd()

        owner = self.owner
        if owner.IsRobotCombatAvatar and genv.camera:
            # 观战机器人相机是客户端模拟
            camera = genv.camera
            if camera and camera.is_fps_placer and owner.shoot_idx > 1:
                camera.placer.EnablePersistRecoilRecover(True)
                owner.shoot_idx = 0

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToDualFireStart(self, is_auto_fire=True):
        action_graph_id = self.action_graph_id
        if self.GetVariableF('Dual_Mp_Fps_Actions', action_graph_id) == cconst.MpFpsAction.Fire:
            self.FireEvent('@Dual_fire', action_graph_id)
        else:
            self.FireEvent('@Dual_fire', action_graph_id)
            self.SetDualMpFpsAction(cconst.MpFpsAction.Fire, 0)
        skeleton = self.model.Skeleton
        if not skeleton:
            return
        SetVariableI = skeleton.SetVariableI
        SetVariableI(action_graph_id, 'Dual_isAutoFire', int(is_auto_fire))
        SetVariableI(action_graph_id, 'Dual_isKeyUp', 0)
        SetVariableI(action_graph_id, 'Dual_isKeyDown', 1)

        dual_weapon_model = self.dual_weapon_model
        if dual_weapon_model:
            dual_weapon_model.JumpToFireStart()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToDualFireEnd(self):
        action_graph_id = self.action_graph_id
        self.SetVariableI('Dual_isKeyDown', 0, action_graph_id)
        self.SetVariableI('Dual_isKeyUp', 1, action_graph_id)
        self.FireEvent('@Dual_keyup', action_graph_id)

        dual_weapon_model = self.dual_weapon_model
        if dual_weapon_model:
            dual_weapon_model.JumpToFireEnd()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToThrowBomb(self, spell_id):
        self.FireEvent('@grenade_toss', self.overlay_action_graph_id if self.overlay_action_graph_id else self.action_graph_id)
        self.OnLeftHandAdditive(False)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToSkillToss(self):
        self.FireEvent('@skill_toss', self.overlay_action_graph_id if self.overlay_action_graph_id else self.action_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToSkillHold(self):
        self.FireEvent('@skill_hold',
                       self.overlay_action_graph_id if self.overlay_action_graph_id else self.action_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToSkillToss2(self):
        self.FireEvent('@skill_toss2', self.overlay_action_graph_id if self.overlay_action_graph_id else self.action_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToSwitchToss(self):
        self.FireEvent('@switch_toss', self.overlay_action_graph_id if self.overlay_action_graph_id else self.action_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpToSkillTossSecondary(self):
        self.FireEvent('@skill_toss2',
                       self.overlay_action_graph_id if self.overlay_action_graph_id else self.action_graph_id)

    def ActionEndToIdle(self):
        mp_fps_blendtime = 0
        if self.action_state == cconst.UNIT_STATE_FIRE:
            self.can_break_fire = False
            mp_fps_blendtime = 0.2
        elif self.action_state == cconst.UNIT_STATE_RELOAD:
            mp_fps_blendtime = 0.2
        elif self.action_state == cconst.UNIT_STATE_RAISEWEAPON:
            mp_fps_blendtime = 0.2
        self.SetMpFpsAction(cconst.MpFpsAction.Idle, mp_fps_blendtime)

    def DualActionEndToIdle(self):
        mp_fps_blendtime = 0
        # if self.action_state == cconst.UNIT_STATE_FIRE:
        #     self.can_break_fire = False
        #     mp_fps_blendtime = 0.2
        # elif self.action_state == cconst.UNIT_STATE_RELOAD:
        #     mp_fps_blendtime = 0.06
        # elif self.action_state == cconst.UNIT_STATE_RAISEWEAPON:
        #     mp_fps_blendtime = 0.2
        self.SetDualMpFpsAction(cconst.MpFpsAction.Idle, mp_fps_blendtime)

    def ActionEndToRechamber(self):
        self.weapon_case.SetIsNeedRechamber(True)
        self.SetMpFpsAction(cconst.MpFpsAction.Rechamber, 0)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetIsEmptyReload(self, is_empty):
        self.SetVariableF('isEmptyReload', is_empty, self.action_graph_id)
        weapon_case = self.weapon_case
        if weapon_case and weapon_case.is_gun:
            weapon_case.SetIsEmptyReload(is_empty)
        self._is_empty_reload = is_empty

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetDualIsEmptyReload(self, is_empty):
        self.SetVariableF('Dual_isEmptyReload', is_empty, self.action_graph_id)
        weapon_case = self.weapon_case
        if weapon_case and weapon_case.is_dual_weapon:
            weapon_case.SetDualIsEmptyReload(is_empty)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetMpFpsAction(self, action, blendTime):
        if action in (cconst.MpFpsAction.RaiseWeapon, cconst.MpFpsAction.DropWeapon):
            # 收枪
            action == cconst.MpFpsAction.RaiseWeapon and self.SetVariableI('ACTOR_FPS_IsRaiseOver', 0)
        last_action = self.GetVariableF('Mp_Fps_Actions', self.action_graph_id)
        last_action and self.owner.StopSoundByTag(tag=cconst.MpFpsAction.to_string(last_action))
        self.SetVariableF('Mp_Fps_BlendTime', blendTime, self.action_graph_id)
        self.SetVariableF('Mp_Fps_Actions', action, self.action_graph_id)
        self.mp_fps_action = action
        weapon_case = self.weapon_case
        if weapon_case and weapon_case.is_gun:
            weapon_case.RefreshOpticShaderSightStabilize()
            weapon_case.SetMpFpsAction(action, blendTime)
            # 1p动作对镭射插值控制都由graph中cue统一管理，这里的设置暂时不需要
            # weapon_case.SetEnableCalibration(action not in (cconst.MpFpsAction.Reload,
            #                                               cconst.MpFpsAction.DropWeapon,
            #                                               cconst.MpFpsAction.RaiseWeapon,
            #                                               cconst.MpFpsAction.Inspection),
            #                                  reason=cconst.RACER_UNABLE_CALIBRATION_REASON_HAND)

            if action == cconst.MpFpsAction.Idle:
                # idle下隐藏 backup_charger_model
                weapon_case.backup_charger_model and weapon_case.backup_charger_model.AddHiddenReason(cconst.HIDDEN_REASON_COMMON)

        elif self.weapon_model:
            self.weapon_model.SetMpFpsAction(action, blendTime)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetDualMpFpsAction(self, action, blendTime):
        self.SetVariableF('Dual_Mp_Fps_BlendTime', blendTime, self.action_graph_id)
        self.SetVariableF('Dual_Mp_Fps_Actions', action, self.action_graph_id)
        if self.dual_weapon_model:
            self.dual_weapon_model.SetMpFpsAction(action, blendTime)

    def SetGraphMoveStartTime(self):
        moveActionTime = self.GetVariableF('%sActionTime' % self.graph_move_state.lower(), self.pose_graph_id)
        if not moveActionTime:
            return
        self.SetMoveStartTime(1.0 - moveActionTime)
        # print 'SetGraphMoveStartTime ======================= ', 1 - moveActionTime
        # print '=' * 50

    # @ReplaySpectatorDecorator('ReplayForHandModel')
    def OnSpeedLevelChange(self):
        speed_level = self.owner.speed_level
        self.SetVariableI('MoveGait', speed_level, self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetIsAds(self, is_ads):
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        weapon_case.SetIsAds(is_ads)

    def SetIsRealAds(self, is_real_ads):
        self.SetVariableF('isADS', is_real_ads, self.action_graph_id)
        weapon_case = self.weapon_case
        if not weapon_case:
            return
        weapon_case.SetIsRealAds(is_real_ads)

    @ReplayMarkHandModelStatus('is_empty_ammo', status_args_idx=0)
    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetIsEmptyAmmo(self, is_empty):
        self.SetVariableI('IsMagEmpty', int(is_empty), self.action_graph_id)
        # self.SetVariableI('IsMagEmpty', int(is_empty), self.pose_graph_id)
        weapon_case = self.weapon_case
        if weapon_case and weapon_case.is_gun:
            weapon_case.SetIsEmptyAmmo(is_empty)
        return True

    @ReplayMarkHandModelStatus('is_dual_empty_ammo', status_args_idx=0)
    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetIsDualEmptyAmmo(self, is_empty):
        self.SetVariableI('Dual_IsMagEmpty', int(is_empty), self.action_graph_id)
        # self.SetVariableI('IsMagEmpty', int(is_empty), self.pose_graph_id)
        # model = self.dual_weapon_model
        if model := self.dual_weapon_model:
            model.SetVariable('IsMagEmpty', is_empty)
        return True

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetJumpAirHeight(self, height):
        isHighFall = height >= 10 and not self.owner.HasTalent(consts.TalentCode.ReduceFallHurt)
        self.SetVariableI('isHighFall', 1 if isHighFall else 0, self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetIsHighFallAir(self, is_high_fall):
        isHighFall = is_high_fall and not self.owner.HasTalent(consts.TalentCode.ReduceFallHurt)
        self.SetVariableI('isHighFallAir', 1 if isHighFall else 0, self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetIsFirstRaiseWeapon(self, is_first_raise):
        self.SetVariableI('IsFirstRaise', is_first_raise, self.action_graph_id)
        case = self.weapon_case
        if case:
            if case.is_gun:
                case.SetIsFirstRaise(is_first_raise)
            else:
                model = case.weapon_model
                if model:
                    model.SetVariable('IsFirstRaise', is_first_raise)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetIsFastRaise(self, is_fast_raise):
        self.SetVariableI('ACTOR_FPS_IsFastRaise', is_fast_raise, self.action_graph_id)
        if case := self.weapon_case:
            if case.is_gun:
                case.SetIsFastRaise(is_fast_raise)
            else:
                if model := case.weapon_model:
                    model.SetVariable('ACTOR_FPS_IsFastRaise', is_fast_raise)
        if model := self.dual_weapon_model:
            model.SetVariable('ACTOR_FPS_IsFastRaise', is_fast_raise)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetGraphMoveType(self, move_type):
        self.SetVariableI('MoveType', move_type, self.pose_graph_id)

    def SetIsDualWeapon(self, is_dual):
        self.SetVariableI('ACTOR_FPS_IsDual', is_dual, self.action_graph_id)
        if owner := self.owner:
            owner.model.SetIsDualWeapon(is_dual)

        if model := self.weapon_model:
            model.SetIsDualWeapon(is_dual)

        if model := self.dual_weapon_model:
            model.SetIsDualWeapon(is_dual)
            model.SetIsDualLeftWeapon(True)

    # 特殊情况如需重新刷新一下指定graph animname参数用：如枪管/握把修正姿势取默认值
    def SetGraphDynamicAnimByParameter(self, equip_id, param):
        if not param.endswith('AnimName') and not param.endswith('AnimTime'):
            return
        anim_id = equip_data.data.get(equip_id, {}).get('anim_data_id', 15)
        anim_data = gun_anim_data.data.get(anim_id, {})
        if not anim_data:
            return

        skeleton = self.model.Skeleton
        if not skeleton:
            return
        SetVariableS = skeleton.SetVariableS
        pose_graph_id = self.pose_graph_id
        overlay_action_graph_id = self.overlay_action_graph_id

        is_dual = self.weapon_case.is_gun and self.weapon_case.is_dual_weapon
        anim_idx = 2 if is_dual else 1
        if param in anim_data[anim_idx]:
            value = anim_data[anim_idx][param]
            if pose_graph_id is not None:
                SetVariableS(pose_graph_id, param, value)
            if overlay_action_graph_id is not None:
                SetVariableS(overlay_action_graph_id, param, value)

        anim_hand_data = gun_hand_anim_data.data.get(anim_id, {})
        if not anim_hand_data:
            return
        action_graph_id = self.action_graph_id
        if not action_graph_id:
            return
        SetVariableF = skeleton.SetVariableF
        if param in anim_hand_data[anim_idx]:
            key = param
            value = anim_hand_data[anim_idx][param]
            if key.endswith('AnimName'):
                SetVariableS(action_graph_id, key, value)
            elif key.endswith('AnimTime'):
                SetVariableF(action_graph_id, key, value)

    def SetGraphDynamicAnim(self, equip_id):
        anim_id = equip_data.data.get(equip_id, {}).get('anim_data_id', 15)
        anim_data = gun_anim_data.data.get(anim_id, {})
        if not anim_data:
            return

        skeleton = self.model.Skeleton
        if not skeleton:
            return
        SetVariableS = skeleton.SetVariableS
        pose_graph_id = self.pose_graph_id
        overlay_action_graph_id = self.overlay_action_graph_id

        weapon_case = self.weapon_case
        is_dual = weapon_case.is_gun and weapon_case.is_dual_weapon
        anim_idx = 2 if is_dual else 1
        for key, value in anim_data[anim_idx].items():
            if key.endswith('AnimName'):
                if pose_graph_id is not None:
                    SetVariableS(pose_graph_id, key, value)
                if overlay_action_graph_id is not None:
                    SetVariableS(overlay_action_graph_id, key, value)
        
        anim_hand_data = gun_hand_anim_data.data.get(anim_id, {})
        if not anim_hand_data:
            return
        action_graph_id = self.action_graph_id
        if not action_graph_id:
            return
        SetVariableF = skeleton.SetVariableF
        for key, value in anim_hand_data[anim_idx].items():
            if key.endswith('AnimName'):
                SetVariableS(action_graph_id, key, value)
            elif key.endswith('AnimTime'):
                SetVariableF(action_graph_id, key, value)

    def SetPoseAnimDynamicSpeed(self, equip_id):
        skeleton = self.model.Skeleton
        if not skeleton:
            return
        anim_speed_id = equip_data.data.get(equip_id, {}).get('anim_speed_data_id', 1)
        anim_speed_data = gun_anim_speed_data.data.get(anim_speed_id, {})
        if not anim_speed_data:
            return
        weapon_case = self.weapon_case
        is_dual = weapon_case.is_gun and weapon_case.is_dual_weapon
        anim_idx = 2 if is_dual else 1
        SetVariableF = skeleton.SetVariableF  # 去掉中间商赚差价
        graph_id = self.pose_graph_id
        for key, value in anim_speed_data[anim_idx].items():
            if key.endswith('AnimSpeed'):
                SetVariableF(graph_id, key, value)

    def SetSkinActionAnim(self, anim_key, anim_name):
        self.SetVariableS(anim_key, anim_name, self.action_graph_id)

    def SetSkinPoseAnim(self, anim_key, anim_name):
        self.SetVariableS(anim_key, anim_name, self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetPoseType(self, pose_type):
        self.SetVariableF('poseType', pose_type, self.pose_graph_id)
        self.SetVariableF('poseType', pose_type, self.action_graph_id)
        weapon_case = self.GetCurClientWeaponCase()
        if weapon_case and weapon_case.weapon_id in cconst.HAND_MODEL_TO_WEAPON_CUE_IDS and weapon_case.model:
            # 暂时hardcode只给蝴蝶刀发
            weapon_case.model.SetVariableF('poseType', pose_type)

    # @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetSwitchWeaponDuration(self, raise_speed, drop_speed):
        self.SetVariableF('raiseSpeed', raise_speed, self.action_graph_id)
        self.SetVariableF('dropSpeed', drop_speed, self.action_graph_id)
        if self.weapon_model:
            self.weapon_model.SetSwitchWeaponDuration(raise_speed, drop_speed)
        if self.dual_weapon_model:
            self.dual_weapon_model.SetSwitchWeaponDuration(raise_speed, drop_speed)

    # @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetMoveStartTime(self, move_action_time):
        self.SetVariableF('startTime', move_action_time, self.pose_graph_id)

    # @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetRunShootDelayTime(self, delay_time):
        self.SetVariableF('RunShootDelayTime', delay_time, self.pose_graph_id)

    # @ReplaySpectatorDecorator('ReplayForHandModel')
    def ChangeMoveState(self, state):
        if self.move_state == state:
            return
        self.move_state = state

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def ChangeActionState(self, state):
        if self.action_state == state:
            return
        self.action_state = state

    def RefreshMoveInAnim(self):
        self.FireEvent('%sInAnim' % self.move_state, self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetGraphAnimIn(self, state, is_need_transition):
        self.SetVariableI('IsNeedTransition', is_need_transition, self.pose_graph_id)
        self.FireEvent('%sInAnim' % state, self.pose_graph_id)

    @ReplayMarkHandModelStatus('move_direction', status_args_idx=0)
    @ReplaySpectatorDecorator('ReplayForHandModel')
    def SetMoveDirection(self, move_direction):
        if self.owner.IsPlayerCombatAvatar and self.owner.pose_type == cconst.ModelPoseType.Prone and self.owner.is_shooting:
            move_direction = 0
        self.SetVariableI('MoveDirection', move_direction, self.pose_graph_id)
        self.SetVariableI('MoveDirection', move_direction, self.action_graph_id)
        return True

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Idle(self, info):
        move_dir = info.get('move_dir', 0)
        self.SetVariableI('JogOutDirection', move_dir, self.pose_graph_id)
        self.FireEvent('IdleInAnim', self.pose_graph_id)
        self.move_state = consts.UNIT_STATE_IDLE

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Walk(self, info):
        move_dir = info.get('move_dir', 0)
        self.SetVariableI('JogOutDirection', move_dir, self.pose_graph_id)
        self.FireEvent('WalkInAnim', self.pose_graph_id)
        self.move_state = consts.UNIT_STATE_WALK

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Jog(self, info):
        if self.move_state == cconst.UNIT_STATE_JOG:
            # 同状态内 改direction 改startTime
            stateActionTime = self.GetVariableF('JogActionTime', self.pose_graph_id)
            self.SetMoveStartTime(stateActionTime)
        self.SetGraphMoveStartTime()
        self.FireEvent('JogInAnim', self.pose_graph_id)
        self.move_state = consts.UNIT_STATE_JOG

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Sprint(self, info):
        if info.get('y') < 0:
            return
        self.FireEvent('@Sprinting', self.pose_graph_id)
        self.FireEvent('SprintInAnim', self.pose_graph_id)
        self.move_state = consts.UNIT_STATE_SPRINT

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_SuperSprint(self, info):
        if info.get('y') < 0:
            return
        self.FireEvent('@Sprinting', self.pose_graph_id)
        self.FireEvent('SuperSprintInAnim', self.pose_graph_id)
        self.move_state = consts.UNIT_STATE_SUPERSPRINT

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpFall(self):
        self.FireEvent('@jump_loop', self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Jump(self, info):
        self.FireEvent('@stand', self.pose_graph_id)
        self.SetVariableF('isHighJump', 1.0 if self.move_state in (cconst.UNIT_STATE_SPRINT, cconst.UNIT_STATE_SUPERSPRINT) else 0.0, self.pose_graph_id)
        self.FireEvent('jump_start', self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_PickUp(self, info):
        if self.overlay_action_graph_id:
            self.FireEvent('@pick', self.overlay_action_graph_id)
        else:
            self.PushOverlayActionGraph('FPS/Hand/fps_pickup.graph')
        if not self.weapon_case:
            return
        anim_id = equip_data.data.get(self.weapon_case.weapon_id, {}).get('anim_data_id', 15)
        anim_data = gun_anim_data.data.get(anim_id, {})
        is_dual = self.weapon_case.is_gun and self.weapon_case.is_dual_weapon
        if not anim_data:
            return
        anim_idx = 2 if is_dual else 1
        self.SetVariableS('PickUpAnimName', anim_data[anim_idx]['PickUpAnimName'], self.overlay_action_graph_id)
        self.SetVariableS('PickUpRhandAnimName', anim_data[anim_idx]['PickUpRhandAnimName'], self.overlay_action_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def JumpLand(self):
        self.FireEvent('jump_land', self.pose_graph_id)
        if self.weapon_model:
            self.weapon_model.SetIsLowClimb(False)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def OnSlideEnd(self, break_slide):
        self.FireEvent('sliding_done' if break_slide else 'sliding_end', self.pose_graph_id)
        self.owner.StopSoundByTag('Slide')

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Slide(self, info):
        self.FireEvent('@crouch', self.pose_graph_id)
        if not self.owner.IsCurTakeGunWeapon() or (self.weapon_case and not self.weapon_case.is_need_rechamber):
            # 拉栓/首次抬枪不能进滑铲动作
            self.SetVariableF('slidingIntype', 1.0 if self.move_state == cconst.UNIT_STATE_SUPERSPRINT else 0.0, self.pose_graph_id)
            self.FireEvent('@sliding_start', self.pose_graph_id)
            # self.BanHandIKToWeapon(True)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Crouch(self, info):
        self.FireEvent('@crouch', self.pose_graph_id)
        self.FireEvent('@crouch', self.action_graph_id)
        self.SetVariableF('startTime', 0.0, self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Prone(self, info):
        self.FireEvent('@lie', self.pose_graph_id)
        self.FireEvent('@lie', self.action_graph_id)
        self.SetVariableF('startTime', 0.0, self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Swoop(self, info):
        self.FireEvent('@lie', self.pose_graph_id)
        self.FireEvent('@lie', self.action_graph_id)
        self.SetVariableF('startTime', 0.0, self.pose_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def CancelExitProne(self):
        self.FireEvent('@lie', self.pose_graph_id)
        self.SetPoseType(cconst.ModelPoseType.Prone)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Stand(self, info):
        self.FireEvent('@stand', self.pose_graph_id)
        self.FireEvent('@stand', self.action_graph_id)
        if self.weapon_case.is_gun:
            self.weapon_case.SetEnableCalibration(True, reason=cconst.RACER_UNABLE_CALIBRATION_REASON_HAND)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Climb(self, info):
        self.SetVariableI('ClimbAction', 1, self.pose_graph_id)
        self.FireEvent('@Climb', self.pose_graph_id)
        if self.weapon_case.is_gun:
            self.weapon_case.SetEnableCalibration(False, reason=cconst.RACER_UNABLE_CALIBRATION_REASON_HAND)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Cross(self, info):
        self.SetVariableI('ClimbAction', 0, self.pose_graph_id)
        self.FireEvent('@Cross', self.pose_graph_id)
        if self.weapon_model:
            self.weapon_model.SetIsLowClimb(True)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Reload(self, info):
        weapon_case = self.weapon_case
        self.SetVariableI('IsReload', 1, self.pose_graph_id)
        weapon = self.owner.GetCurWeapon()
        reload_start_time = 0
        if weapon.client_ammo <= 0 and weapon_case.weapon_id in self.middle_reload_map:
            reload_type = self.middle_reload_map[weapon_case.weapon_id]
            if reload_type == 1:
                reload_start_time = weapon_case.GetWeaponAttrValue("EmptyReloadNoAmmunitionTimeConf", 0.4)
            else:
                reload_start_time = weapon_case.GetWeaponAttrValue("EmptyReloadOnlyBoltTimeConf", 0.8)
        self.SetVariableF('ReloadTime', reload_start_time, self.action_graph_id)
        self.SetMpFpsAction(cconst.MpFpsAction.Reload, 0)
        if weapon_case.is_gun:
            weapon_case.AttachWeaponChargerToLeftHandNew()
            weapon_case.RefreshChargerBulletVisible()

        # [DEBUG]
        weapon_id = self.weapon_case.weapon_id
        self._debug_reload_start_time_map[weapon_id] = time.time()
        if genv.smoke_robot:
            self._debug_reload_start_time_map[weapon_id] = genv.smoke_robot.Time()
        # [DEBUG]

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_FireModeChange(self, info):
        self.SetMpFpsAction(cconst.MpFpsAction.Fire_Mode_Select, 0)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def ReloadWeaponStop(self):
        if self.action_state != cconst.UNIT_STATE_RELOAD:
            return
        if not self.weapon_case:
            return
        self.SetVariableI('IsReload', 0, self.pose_graph_id)
        self.ActionEndToIdle()
        self.owner.StopSoundByTag(tag='Reload')
        cur_client_weapon_case = self.GetCurClientWeaponCase()
        cur_client_weapon_case and cur_client_weapon_case.is_gun and cur_client_weapon_case.ResetBackupCharger()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def ReloadDualWeaponStart(self):
        self.SetVariableI('Dual_IsReload', 1, self.pose_graph_id)
        self.SetVariableF('ReloadTime', 0, self.action_graph_id)
        self.SetDualMpFpsAction(cconst.MpFpsAction.Reload, 0)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def ReloadDualWeaponStop(self):
        if not self.weapon_case:
            return
        self.SetVariableI('Dual_IsReload', 0, self.pose_graph_id)
        self.DualActionEndToIdle()

    def OnHit(self, info):
        if not self.isValid():
            return
        if not info:
            hit_dir = hit_part = None
        else:
            hit_dir = info['hit_dir']
            hit_part = info['hit_part'].lower()
        hit_scope = 1 if self.owner.is_ads and weapon_util.GetWeaponPartOpticType(self.owner.GetCurWeaponPartSlots()) > consts.WeaponPartOpticType.Double else 0
        hit_damage = 1 if hit_part == consts.AvatarCollisionBone_Head else 0
        if not hit_dir:
            hit_direction = 1
        else:
            a = formula.YawToVector(self.yaw)[::2]
            b = hit_dir[::2]
            rad = formula.RotateAngle2D(a, b)
            hit_direction = 1 if -math.pi <= rad < 0 else 0

        if not self.camera_graph_id:
            self.camera_graph_id = self.PushGraph('FPS/Camera/camera_shake.graph')
        self.SetVariableI('gun_type', self.owner.GetCurWeaponGunType(), self.camera_graph_id)
        self.SetVariableF('hit_direction', hit_direction, self.camera_graph_id)
        self.SetVariableF('hit_scope', hit_scope, self.camera_graph_id)
        self.SetVariableF('hit_damage', hit_damage + self.owner.combat_attr.CalResult(hit_damage, 'HitWeakenShake'), self.camera_graph_id)
        self.FireEvent('@hit_shake', self.camera_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def PushPlates(self, is_end):
        if not is_end:
            self.FireEvent('@plates_next', self.action_graph_id)
        else:
            self.FireEvent('@plates_end', self.action_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def InjectEnd(self):
        self.FireEvent('@injection_end', self.action_graph_id)

    def Destroy(self):
        if self._is_destroyed:
            return
        if self.owner.IsRobotCombatAvatar and genv.camera:
            # 观战机器人相机是客户端模拟
            genv.camera.placer.DisableStoryTickForReplay()
        for guid in list(self.weapon_dict.keys()):
            self.DestroyWeapon(guid, force_destroy=True)
        if ModelCache.isInited() and ModelCache.instance().mode == ModelCache.MODE_PLANE:
            self.pose_graph_id = None
        # self.UnpinWeaponGraph()
        super(ReplayHandModel, self).Destroy()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def InspectionWeaponStart(self):
        if self.GetVariableF('Mp_Fps_Actions', self.action_graph_id) == cconst.MpFpsAction.Inspection:
            self.FireEvent('@Inspection', self.action_graph_id)
        else:
            self.SetMpFpsAction(cconst.MpFpsAction.Inspection, 0)

        if self.weapon_model:
            self.weapon_model.InspectionWeaponStart()
        if self.dual_weapon_model:
            self.dual_weapon_model.InspectionWeaponStart()
        if self.weapon_case:
            self.weapon_case.InspectionWeaponStart()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def InspectionWeaponEnd(self):
        self.ActionEndToIdle()
        if self.weapon_model:
            self.weapon_model.InspectionWeaponEnd()
        if self.dual_weapon_model:
            self.dual_weapon_model.InspectionWeaponEnd()
        cur_client_weapon_case = self.GetCurClientWeaponCase()
        cur_client_weapon_case and cur_client_weapon_case.is_gun and cur_client_weapon_case.ResetBackupCharger()

    @ReplayMarkHandModelStatus('pose_state', 'Swim')
    @ReplaySpectatorDecorator('ReplayForHandModel')
    def _OnJumpTo_Swim(self, info):
        genv.camera.RefreshNearAndFar(gpl.CAMERA_NEAR_SWIM)
        self.SetPoseType(cconst.ModelPoseType.Swim)
        melee_weapon = self.owner.GetMeleeWeaponCase()
        melee_weapon and melee_weapon.AddHiddenReason(cconst.HIDDEN_REASON_SWIM)
        return True

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def OnExitSwim(self):
        genv.camera.RefreshNearAndFar(gpl.CAMERA_NEAR_DEFAULT)
        self.JumpToState(cconst.UNIT_STATE_STAND)
        self.SetPoseType(cconst.ModelPoseType.Stand)
        melee_weapon = self.owner.GetMeleeWeaponCase()
        melee_weapon and melee_weapon.RemoveHiddenReason(cconst.HIDDEN_REASON_SWIM)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def OnStopHoldCurSpecWeapon(self):
        if not self.isValid():
            return
        spec_weapon = self.owner.GetCurSpecWeaponCase()
        spec_weapon and spec_weapon.OnStopHoldWeapon()

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def PlayMeleeSkill(self, skill_type=0, info={}):
        if not self.weapon_model:
            return
        if self.GetVariableF('Mp_Fps_Actions', self.action_graph_id) != cconst.MpFpsAction.Fire:
            self.SetMpFpsAction(cconst.MpFpsAction.Fire, 0)
            self.SetVariableI('skill_type', skill_type, self.action_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def EndMeleeSkill(self):
        if not self.weapon_model:
            return
        if self.GetVariableF('Mp_Fps_Actions', self.action_graph_id) != cconst.MpFpsAction.Idle:
            self.SetMpFpsAction(cconst.MpFpsAction.Idle, 0.2)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def OnUseSamuraiSword(self):
        if not self.isValid():
            return
        spec_weapon = self.owner.GetCurSpecWeaponCase()
        spec_weapon and spec_weapon.UseWeapon()

    def CueChangeTPSIsStub(self):
        owner = self.owner
        cur_attack_num = self.GetVariableI('MeleeAttackHitSfx', self.action_graph_id)
        if owner.HasTalentCode(consts.TalentCode.SpecialMeleeAttack):
            model = owner.model
            if model:
                model.SetVariableZ('ACTOR_TPS_isStab', cur_attack_num == 0, model.locomotion_graph_id)

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def OnPulseEndForNextSpell(self, use_next):
        weapon_case = self.GetCurClientWeaponCase()
        if use_next:
            if weapon_case:
                weapon_case.ShowAttachModel()
            self.FireEvent('@skill_ready')
        else:
            self.FireEvent('@skill_toss_end')
            if weapon_case:
                weapon_case.model.FireEvent("@GetBackItem")

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def OnReadyForNextPulseSpell(self):
        weapon_case = self.GetCurClientWeaponCase()
        if weapon_case:
            weapon_case.ShowAttachModel()
            weapon_case.model.FireEvent('@skill_start')

    @ReplaySpectatorDecorator('ReplayForHandModel')
    def OnScoutArrowSkillReadyForWeaponCase(self, is_pulse):
        weapon_case = self.GetCurClientWeaponCase()
        if weapon_case:
            weapon_case.model.SetVariableI('is_pulse', int(is_pulse) + 1)
            weapon_case.model.FireEvent('@skill_start')

    def DebugCheckTPos(self):
        if not self.skeleton:
            return False
        owner = self.owner
        if owner.para_stage not in (0, 8, 9):
            return False
        # 看一下graph层数，如果不是三层结构也return
        if len(self.skeleton.GetGraphStackInfo()) < 3:
            # 不是三层结构
            return False
        bone_name = 'Bip001 L Hand'  # 检测左手
        init_trans = self.skeleton.GetBoneInitLocalTransform(bone_name)
        cur_trans = self.GetBoneLocalTransform(bone_name)
        # 先看位置
        if (init_trans.translation - cur_trans.translation).length > 0.01:
            return False
        if abs(init_trans.yaw - cur_trans.yaw) > 0.01:
            return False
        if abs(init_trans.pitch - cur_trans.pitch) > 0.01:
            return False
        return True
