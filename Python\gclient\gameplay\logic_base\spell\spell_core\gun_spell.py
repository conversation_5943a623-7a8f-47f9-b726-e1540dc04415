# -*- coding: utf-8 -*-
import time
import MEngine
import MType
import math

from gclient.framework.util import events
import switches
from gclient.data import weapon_data
from gclient.gameplay.logic_base.spell.spell_core import spell_core_main
from gclient.gameplay.logic_base.spell.spell_core.spell_core_main import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Wrapper<PERSON>oundR<PERSON>ult
from gclient.gameplay.uicomponents.hud_frontsight_comp import HudFrontsightComp
from gshare import formula, effect_util, consts, weapon_util
from gshare.async_util import Async
from gclient import cconst
from gclient.gameplay.logic_base.spell import spell_util
from gclient.util.debug_log_util import print_s, SomePreset  # noqa

_reload_all = True


DegreesToRadians = math.pi / 180


class SpellWorker(SpikeSpellWorker):
    BallisticEffectInterval = cconst.TRAIL_BALLISTIC_EFFECT_PARAM["BallisticEffectInterval"]

    def __init__(self, caster):
        super(<PERSON>pel<PERSON><PERSON><PERSON><PERSON>, self).__init__(caster)
        self.last_ballistic_timestamp = 0

    def GetAimBonePos(self, weapon_case):
        if weapon_case:
            optic_part = weapon_case.GetWeaponPartModel(consts.WeaponPartType_Optic)
            if optic_part and optic_part.HasBone(cconst.TACH_POINT_GUN_SHOOTPOINT_ATTACHMENT):
                # 先尝试拿镜子的瞄点
                return optic_part.GetBoneWorldPosition(cconst.TACH_POINT_GUN_SHOOTPOINT_ATTACHMENT)

            barrel_part = weapon_case.GetWeaponPart(consts.WeaponPartType_Barrel)
            machine_sight_front_model = barrel_part.machine_sight_front_model
            if machine_sight_front_model and machine_sight_front_model.HasBone(cconst.TACH_POINT_GUN_SHOOTPOINT):
                # 机瞄的骨骼点
                return machine_sight_front_model.GetBoneWorldPosition(cconst.TACH_POINT_GUN_SHOOTPOINT)

            # 没拿到就拿枪主体的
            weapon_model = weapon_case.weapon_body.weapon_model
            if weapon_model and weapon_model.HasBone(cconst.TACH_POINT_GUN_SHOOTPOINT):
                return weapon_model.GetBoneWorldPosition(cconst.TACH_POINT_GUN_SHOOTPOINT)

    def GetShootDir(self, shoot_pos):
        camera = MEngine.GetGameplay().Player.Camera
        caster = self.caster
        is_real_ads = caster.is_real_ads
        weapon_case = caster.GetCurWeaponCase()
        shoot_dir = spell_core_main.GetPlayerShootDirWithoutScatter(shoot_pos)
        shoot_idx = caster.shoot_idx
        if is_real_ads:
            weapon_attr = weapon_case.GetWeaponAttrValue
            use_scatter_in_ads = weapon_attr('UseScatterInAdsMode', False)
            # 是否在ADS模式下使用腰部散射
            # print_s(f'use_scatter_in_ads: {use_scatter_in_ads}', SomePreset.black_fg_cyan_bg)
            if use_scatter_in_ads:
                scatterRadians = weapon_case.GetRealShootScatterRadian(shoot_idx)
            else:
                scatterRadians = weapon_case.GetRealShootScatterRadianForAds(shoot_idx)
        else:
            scatterRadians = weapon_case.GetRealShootScatterRadian(shoot_idx)
        # genv.space.DrawRay(start_pos_fps, start_pos_fps + shoot_dir_fps * 10, reset=True, color=(1, 0, 0))
        if scatterRadians > 0:
            shoot_dir = formula.RandomDirInConeByConeRadians(shoot_dir, scatterRadians)
        # genv.space.DrawRay(start_pos_fps, start_pos_fps + shoot_dir_fps * 10, reset=False, color=(0, 0, 1))

        # [DEBUG]
        if switches.DRAW_RAY:
            import MDebug
            genv.debug_cylinder = None
            start_pos_fps = spell_core_main.GetShootStartPos(self.caster)
            axis = MDebug.Cylinder()
            axis.endpoint0 = start_pos_fps
            axis.endpoint1 = start_pos_fps + camera.GetRayDirectionFromScreenPoint(int(shoot_pos[0]), int(shoot_pos[1]))
            axis.color = MType.Vector3(1.0, 0.0, 0.0)
            axis.radius = math.tan(scatterRadians)
            genv.debug_cylinder = axis
        # [DEBUG]
        return shoot_dir

    def _GetCostAmmoSpellResult(self):
        spell_result = self.NewSpellResult()
        return spell_result

    def StrikeBallisticEffect(self, caster, weapon_guid, gun_id, hit_pos, start_pos, shoot_dir, can_be_optimized, material_type, target_pos=None):
        # 弹道特效
        # 自己作为主控端的AI的弹道特效，can_be_optimized为True，此时主控端也可以不播弹道特效，少一次GetShootResult
        spell_result = self._GetCostAmmoSpellResult()
        spell_result.weapon_guid = weapon_guid
        spell_result.extra = {'gun_id': gun_id}
        if can_be_optimized:
            if time.time() - self.last_ballistic_timestamp < self.BallisticEffectInterval:
                # 如果间隔小于self.BallisticEffectInterval，说明弹道封包可以被优化，走fast path
                spell_result.extra.update({'need_respond': False})
                genv.spell_core.SendSpellResult(spell_result)
                return

        if hit_pos:
            end_pos = hit_pos
            distance = formula.Distance3D(self.caster.position, hit_pos.tuple())
        else:
            spell_result.is_hit = False
            # 先打一发超远距离射线 判断能不能打到东西 播特效
            far_hit_res = spell_core_main.GetShootResult(caster, 800, None, shoot_dir=shoot_dir, start_pos=start_pos)
            # 弹道特效
            if far_hit_res and far_hit_res.is_hit:
                end_pos = far_hit_res.physics_hit_pos
                distance = formula.Distance3D(self.caster.position, end_pos.tuple())
            else:
                distance = 500
                end_pos = start_pos + shoot_dir * distance
        # extra_data = {
        #     "create_pos": formula.Tuple(start_pos),
        #     "end_pos": formula.Tuple(target_pos) if target_pos else formula.Tuple(end_pos),
        #     "shoot_dir": formula.Tuple(shoot_dir),
        # }
        extra_data = {}
        if material_type:
            extra_data['material_type'] = material_type

        weapon_case = caster.GetCurWeaponCase()
        if weapon_case:
            bullet_speed = weapon_case.GetWeaponAttrValue("bullet_velocity", 100)
            extra_data["bullet_velocity"] = bullet_speed
            extra_data['BallisticEffectInterval'] = weapon_case.GetWeaponAttrValue('trail_sfx_cd', self.BallisticEffectInterval)
            extra_data['BallisticEffectIntervalProb'] = weapon_case.GetWeaponAttrValue('trail_sfx_cd_prob', 0.0)
        extra_data and spell_result.extra.update(extra_data)
        self.WrapperSendBallisticEffectResult(spell_result, end_pos, start_pos, distance, is_send=True)

    @Async
    def SpellStrike(self, code):
        caster = self.caster
        if not caster or caster.is_destroyed() or caster is not genv.player or not caster.model.isValid():
            return

        # 技能逻辑生效

        weapon = caster.GetCurWeaponCase()
        if not weapon:
            return
        weapon.OnCastSpell()

        weapon_id = weapon.weapon_id
        weapon_guid = weapon.weapon_guid
        gun_id = weapon.gun_id
        is_dual_weapon = weapon.is_dual_weapon
        weapon_attr_getter = weapon.GetWeaponAttrValue

        bullet_fly_speed = weapon_attr_getter('bullet_velocity', 800)
        bullet_fly_range = weapon_attr_getter('damage_range', 800)
        bullet_gravity = weapon_attr_getter('bullet_gravity', -20)

        has_gravity = True
        gravity = MType.Vector3(0, bullet_gravity, 0)  # 重力加速度

        yield_time = 0.01

        cur_bullet_dist = 0
        cur_time = time.time()

        shoot_screen_pos = spell_core_main.GetShootScreenPos(self.caster)
        shoot_dir_fps = self.GetShootDir(shoot_screen_pos)
        # 准星生效放在散步更新后，即GetShootDir后
        HudFrontsightComp.isInited() and HudFrontsightComp.instance().AimPointScatter(caster, caster.shoot_idx)

        if not caster.is_real_ads:
            camera = MEngine.GetGameplay().Player.Camera
            # 转换成相机本地坐标系的单位化向量
            vec = camera.Transform.inverse.transform_v(shoot_dir_fps).get_normalized()
            vec.z = 0
            caster.hand_model.UpdateStrafeSpreadDir(vec)

        start_pos_fps = spell_core_main.GetShootStartPos(self.caster)
        # [DEBUG]
        new_shoot = True
        if cconst.DEBUG_HP_SIGHT_DIR_RECORD:
            genv.messenger.Broadcast(events.ON_FIRE_DIR_RECORD, self.GMOnSpellStrike())
        # [DEBUG]
        # 把speed转换为v3
        bullet_fly_speed_v3 = MType.Vector3(shoot_dir_fps.x, shoot_dir_fps.y, shoot_dir_fps.z)
        bullet_fly_speed_v3.length = bullet_fly_speed
        first_frame_bullet_dist_v3 = bullet_fly_speed_v3 * yield_time + 0.5 * gravity * yield_time * yield_time
        bullet_fly_speed_v3 = bullet_fly_speed_v3 + gravity * yield_time
        cur_fly_dist_v3 = first_frame_bullet_dist_v3
        # 计算子弹500米落点，用于弹道表现
        total_t = bullet_fly_range * 1.0 / bullet_fly_speed
        total_dis = bullet_fly_speed_v3 * total_t + 0.5 * gravity * total_t * total_t
        target_pos = start_pos_fps + total_dis

        start_pos_tps = caster.model.GetBoneWorldPosition('HP_Camera')
        end_pos_tps = start_pos_fps + shoot_dir_fps * 5
        shoot_dir_tps = (end_pos_tps - start_pos_tps).get_normalized()

        use_tps_ballistic = not caster.is_fps_mode and not caster.is_ads

        # ClientSALog
        caster.RecordGunFireAmmo(weapon_id)

        # 一个角色只能受击一次 每次要排除掉已击中的角色
        bullet_exclude_actor = [caster.model.GetSkeleton()]
        shoot_result_getter = spell_core_main.GetShootResultWithPenetrate
        penetrate_handler = spell_core_main.PenetrateHandler(
            caster.game_logic, weapon_attr_getter('penerate_power_base', 100), weapon.hit_part_priority_config
        )
        start_pos, shoot_dir = start_pos_fps, shoot_dir_fps

        # 子弹射出的初始位置，用于服务端校验
        if use_tps_ballistic and cur_bullet_dist == 0:
            verify_start_pos = start_pos_tps
            verify_shoot_dir = shoot_dir_tps
        else:
            verify_start_pos = start_pos_fps
            verify_shoot_dir = shoot_dir_tps

        # 后坐力校验
        script_camera = genv.camera
        if script_camera and script_camera.is_fps_placer:
            recoil_pitch = script_camera.placer.RecoilPitchRange
        else:
            recoil_pitch = -1.0

        open_assist_aim = False
        if gui.is_pc:
            open_assist_aim = genv.player.IsAssistAimSettingOpen()
        time_diff = 0.01
        make_ballistic_effect = False
        hit_combat_avatar = False
        while cur_bullet_dist < bullet_fly_range:
            if use_tps_ballistic and cur_bullet_dist == 0:
                # tps下，角色向镜头正前方5m位置前探3m
                start_pos, shoot_dir = start_pos_tps, shoot_dir_tps
            else:
                if has_gravity:
                    end_pos = start_pos + cur_fly_dist_v3
                    shoot_dir = end_pos - start_pos
                    shoot_dir.normalize()
                else:
                    shoot_dir = shoot_dir_fps
            cur_fly_dist = bullet_fly_speed * time_diff
            shoot_results = shoot_result_getter(
                caster, cur_fly_dist, shoot_dir, start_pos,
                exclude_actor=bullet_exclude_actor, penetrate_handler=penetrate_handler
            )

            # [DEBUG]
            if switches.BULLET_DEBUG and switches.IS_DEBUG:
                spell_core_main.DrawBulletDebug(start_pos, shoot_dir, cur_fly_dist, shoot_results, new_shoot)
            new_shoot = False
            # [DEBUG]

            ballistic_hit_pos = None
            spell_result = None
            material_type = None
            if shoot_results:
                ballistic_hit_pos = shoot_results[0].physics_hit_pos
                # build spell result
                spell_result = self.NewSpellResult()
                spell_result.weapon_guid = weapon_guid
                spell_result.cost_ammo = False
                spell_result.extra = {'gun_id': gun_id}
                spell_result.verify_start_pos = verify_start_pos
                spell_result.verify_shoot_dir = verify_shoot_dir
                spell_result.verify_recoil_pitch = recoil_pitch
                spell_result.verify_assist_aim = open_assist_aim
                camera_transform = MEngine.GetGameplay().Player.Camera.Transform
                spell_result.verify_camera_pos = camera_transform.translation.tuple()
                # spell_result.verify_camera_yaw = camera_transform.yaw + math.pi
                penetrate_materials = []
                sound_results = []
                first_result_target = shoot_results[0].target
                for res in shoot_results:
                    raycast_bone_res = res.raycast_bone_res
                    target = res.target
                    material_type = res.materialTypeId
                    penetrate_power = res.penetrate_power
                    penetrate_materials.append(material_type)
                    if raycast_bone_res and target:
                        hit_name = raycast_bone_res.name
                        modify_hit_name = res.penetrate_hit_part if res.penetrate_hit_part else hit_name
                        if target.IsCombatAvatar:
                            # 是否背后击中
                            hit_back = target.model and target.model.CalcDeathDir(hit_dir=shoot_dir) == 1
                            # 是否穿透击中
                            hit_penetrate = res.penetrate_count > 0 and target != first_result_target
                            hit_combat_avatar = True
                        else:
                            hit_penetrate = hit_back = False
                        # 校验碰撞盒大小
                        verify_hit_radius, verify_hit_half, verify_bone_scale, verify_hit_offset = 0.0, None, None, None
                        if modify_hit_name and target.IsCombatAvatar and target.model:
                            # 校验碰撞盒尺寸是否被外挂修改
                            skeleton = target.model.skeleton
                            verify_hit_radius, verify_hit_half = spell_util.GetCollisionVerify(skeleton, modify_hit_name)
                            verify_hit_offset, verify_bone_scale = spell_util.GetCollisionBoneOffset(skeleton, hit_name, raycast_bone_res.Pos)

                        # 伤害result
                        caster.game_logic.DealWeaponDamageResult(self.spell_id, spell_result, caster, target, weapon_id, True,
                            hit_part=modify_hit_name, hit_dir=shoot_dir, hit_back=hit_back, hit_pos=raycast_bone_res.Pos,
                            hit_penetrate=hit_penetrate, penetrate_power=penetrate_power, penetrate_materials=penetrate_materials,
                            is_ads=not caster.is_real_ads, weapon_guid=weapon_guid, is_dual=is_dual_weapon, verify_hit_radius=verify_hit_radius,
                            verify_hit_half=verify_hit_half, verify_bone_scale=verify_bone_scale, verify_hit_offset=verify_hit_offset,
                        )
                    else:
                        if target:
                            if target.IsSimpleCombatUnit:
                                caster.game_logic.DealWeaponDamageResult(self.spell_id, spell_result, caster, target, weapon_id, True,
                                    hit_dir=shoot_dir, penetrate_power=penetrate_power, penetrate_materials=penetrate_materials,
                                    hit_pos=res.physics_hit_pos, weapon_guid=weapon_guid, is_dual=is_dual_weapon,
                                )
                            if target.IsDestructible:
                                self.OnHitDestructible(res, target, gun_id)
                            elif target.IsBreakItem:
                                self.OnBreakItemHitImmediate(res, target, gun_id)
                            elif target.IsCarriable:
                                spell_core_main.OnHitCarriable(res, target, gun_id, caster)
                            elif target.IsStrikeItem:
                                spell_core_main.OnHitStrikeItem(res.shoot_dir, res.physics_hit_pos, target, gun_id)
                            elif target.IsFragment:
                                target.OnHit(res.body.Entity)
                                continue
                            # 零件或者已经组装好的物体都可以直接启动
                            if target.IsUGCShip:
                                target.OnStart()
                                genv.ship = target
                            elif target.IsUGCShipComponent:
                                target.OnHit(res.shoot_dir, res.physics_hit_pos, 20)
                                genv.ship = target

                        # 音效result
                        not hit_combat_avatar and sound_results.append(WrapperSoundResult(target, res.physics_hit_pos, material_type))

                    # 击中特效result
                    # FIXME: 这里要改成枪械属性获取覆盖的根据材质等手段获取的hit_perform_id
                    hit_effect_dict = weapon.skin_hit_object_effect_dict
                    if not hit_effect_dict:
                        hit_effect_dict = weapon.ammunition_hit_object_effect_dict
                    hit_perform_id = hit_effect_dict.get(material_type, hit_effect_dict[0]) if hit_effect_dict else -1
                    self.WrapperSendHitEffectResult(spell_result, res.target, res.physics_hit_pos, res.hit_normal, material_type, shoot_dir, weapon_id, False, hit_perform_id, res.hit_pos_through, raycast_bone_res, weapon.gun_type)

                spell_result.sound_results = sound_results
            # 发送弹道特效封包
            if not make_ballistic_effect:
                self.StrikeBallisticEffect(caster, weapon_guid, gun_id, ballistic_hit_pos, start_pos_fps, shoot_dir_fps, False, material_type=material_type, target_pos=target_pos)
            make_ballistic_effect = True
            end_pos = start_pos + shoot_dir * cur_fly_dist
            self.SpellFriendOnAttackSound(cur_fly_dist, shoot_dir, start_pos, end_pos)
            # 发送真正的伤害封包
            if spell_result:
                self.WrapperSendSpellResult(spell_result, True)
                # ClientSALog
                for result in spell_result.damage_result.values():
                    caster.RecordGunFireAmmo(weapon_id, hit_part=result['hit_part'])
                    caster.RecordGunFireDamage(weapon_id, result['damage'], weapon.part_slots)
                    break

            if penetrate_handler.power <= 0:
                # 穿透结束 子弹销毁
                break

            if use_tps_ballistic and cur_bullet_dist == 0:
                end_pos = start_pos_fps
            else:
                end_pos = start_pos + shoot_dir * cur_fly_dist
                if has_gravity:
                    end_pos = start_pos + cur_fly_dist_v3
            if not has_gravity:
                cur_bullet_dist += cur_fly_dist
            else:
                cur_bullet_dist += cur_fly_dist_v3.length

            yield yield_time
            # 子弹下一次飞行
            caster = self.caster
            if not caster or caster.is_destroyed() or not caster.space:
                return
            if weapon.is_destroyed:
                return
            now_time = time.time()
            time_diff = now_time - cur_time
            start_pos = end_pos
            if has_gravity:
                cur_fly_dist_v3 = bullet_fly_speed_v3 * time_diff + 0.5 * gravity * time_diff * time_diff
                bullet_fly_speed_v3 = bullet_fly_speed_v3 + gravity * time_diff
                if cur_bullet_dist + cur_fly_dist_v3.length > bullet_fly_range:
                    cur_fly_dist_v3.length = bullet_fly_range - cur_bullet_dist + 2

            cur_time = now_time

    def SpellStop(self):
        caster = self.caster
        if caster is not genv.player:
            return
        caster.PlayFireReleaseAction()

        weapon_case = caster.GetCurWeaponCase()
        if not weapon_case or not weapon_case.is_own_gun:
            return
        # 膛烟特效
        weapon_data_current = weapon_data.data.get(weapon_case.gun_id, {})
        chamber_sfx_id = weapon_data_current.get('overheat_muzzle_smoke_vfx_id', 16)
        # if self.caster.shoot_idx >= weapon_data_current.get('tip_smoke_trigger_bullet_num', 20):
        #     chamber_sfx_id = weapon_data_current.get('overheat_muzzle_smoke_vfx_id', 16)
        # else:
        #     chamber_sfx_id = 919
        caster.SpellStop(weapon_case)
        if chamber_sfx_id:
            # 播枪口过热特效
            muzzle_model = weapon_case.GetWeaponPartModel(consts.WeaponPartType_Muzzle)
            if not muzzle_model:
                # 有些枪默认没有枪口 那挂点在枪管
                muzzle_model = weapon_case.GetWeaponPartModel(consts.WeaponPartType_Barrel)

            # 先用 HP_flash 跟开火特效挂点一起
            # effect_point = 'HP_flash'
            # muzzle_model and muzzle_model.PlayEffectById(chamber_sfx_id, -1, insure_play=True, bone=effect_point)
            caster.PlaySmokeEffect(muzzle_model, chamber_sfx_id, weapon_case, 'HP_flash')

        weapon_case.OnCastSpellStop()

    def SpellStart(self):
        # 按下按键
        cur_weapon = self.caster.GetCurWeapon()
        if not cur_weapon:
            return
        self.graph_info = {
            'hand_graph': cur_weapon.equip_proto.get('hand_graph'),
            'tps_graph': cur_weapon.equip_proto.get('tps_graph'),
        }
        self.caster.PlayFireAction(self.spell_id, self.graph_info)
        if self.caster is not genv.player:
            return
        # 停止枪口烟雾
        self.caster.CancelSmokeTimer()
        weapon_case = self.caster.GetCurWeaponCase()
        weapon_case and weapon_case.OnCastSpellStart()
        self.caster.SpellStart(weapon_case)

    def WrapperSendSpellResult(self, spell_result, is_send):
        is_send and genv.spell_core.SendSpellResult(spell_result)

    def WrapperSendHitEffectResult(self, spell_result, target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, is_send, hit_perform_id, hit_pos_through, raycast_bone_res, gun_type):
        hit_part = raycast_bone_res.name if raycast_bone_res else None
        hit_effect_data = effect_util.WrapperHitEffectResult(target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, gun_type, hit_perform_id, hit_pos_through=hit_pos_through, hit_part=hit_part)
        if not hit_effect_data:
            return
        if spell_result.hit_water:
            hit_effect_data['hit_perform_mode'] |= 1 << 1
        if hit_material_type == 11:  # 击中水了
            spell_result.hit_water = True
        spell_result.hit_effect.append(hit_effect_data)
        is_send and genv.spell_core.SendSpellResult(spell_result)

    def WrapperSendBallisticEffectResult(self, spell_result, physics_hit_pos, create_pos, distance, is_send):
        ballistic_effect_data = {
            'hit_pos': formula.Tuple(physics_hit_pos),
            'create_pos': formula.Tuple(create_pos),
            'hit_dis': distance,
        }
        spell_result.ballistic_effect.append(ballistic_effect_data)
        cur_time = time.time()
        if time.time() - self.last_ballistic_timestamp > self.BallisticEffectInterval:
            self.last_ballistic_timestamp = cur_time
        else:
            spell_result.extra.update({'need_respond': False})
        is_send and genv.spell_core.SendSpellResult(spell_result)
        if is_send and self.caster.IsPlayerCombatAvatar:
            # 连射的时候，不要每次都发这个事件
            if self.caster.backpack.slots.get(consts.BackpackSlot.WEAPON_3):
                self.caster.TellServerMotionState(consts.UNIT_STATE_SPELL, self.spell_id, force_deliver=True)

    def SpellStrikeForRobot(self, code):
        caster = self.caster
        if not caster or caster.is_destroyed() or not caster.IsRobotCombatAvatar:
            return
        position = caster.position
        start_pos = MType.Vector3(position[0], position[1] + 1.5, position[2])
        shoot_dir, force_miss = caster.GetShootDirection(start_pos)

        # 攻击目标是角色或者打不中走服务端构造封包
        if caster.use_server_spell or force_miss:
            caster.CallServer("SimulateGunSpellStrike", start_pos.tuple(), shoot_dir.tuple(), force_miss)
            return

        # 技能逻辑生效
        weapon = caster.GetCurWeaponCase(False)
        if not weapon:
            return
        weapon_id = weapon.weapon_id
        weapon_guid = weapon.weapon_guid
        gun_id = weapon.gun_id
        bullet_fly_range = weapon.GetWeaponAttrValue('damage_range', 800)

        # 服务端校验所用
        verify_start_pos = start_pos
        verify_shoot_dir = shoot_dir

        # 暂时只处理一个 不处理穿透后的特效
        has_send_hit_effect = False
        # 一个角色只能受击一次 每次要排除掉已击中的角色
        bullet_exclude_actor = [caster.model.GetSkeleton()]
        shoot_result_getter = spell_core_main.GetShootResultWithPenetrate
        penetrate_handler = spell_core_main.PenetrateHandler(caster.game_logic, weapon.GetWeaponAttrValue('penerate_power_base', 100), weapon.hit_part_priority_config)

        shoot_results = shoot_result_getter(
            caster, bullet_fly_range, shoot_dir, start_pos, force_miss=force_miss,
            exclude_actor=bullet_exclude_actor, penetrate_handler=penetrate_handler
        )

        material_type = None
        ballistic_hit_pos = None
        if shoot_results:
            ballistic_hit_pos = shoot_results[0].physics_hit_pos
            # build spell result
            spell_result = self.NewSpellResult()
            spell_result.weapon_guid = weapon_guid
            spell_result.cost_ammo = False
            spell_result.verify_start_pos = verify_start_pos
            spell_result.verify_shoot_dir = verify_shoot_dir
            penetrate_materials = []
            sound_results = []
            first_result_target = shoot_results[0].target
            hit_combat_avatar = False
            for res in shoot_results:
                raycast_bone_res = res.raycast_bone_res
                target = res.target
                material_type = res.materialTypeId
                penetrate_power = res.penetrate_power
                penetrate_materials.append(material_type)
                if raycast_bone_res:
                    hit_name = raycast_bone_res.name
                    # 是否背后击中
                    if target.IsCombatAvatar:
                        # 是否背后击中
                        hit_back = target.model and target.model.CalcDeathDir(hit_dir=shoot_dir) == 1
                        hit_penetrate = res.penetrate_count > 0 and target != first_result_target
                        hit_combat_avatar = True
                    else:
                        hit_penetrate = hit_back = False
                    # 伤害result
                    caster.game_logic.DealWeaponDamageResult(self.spell_id, spell_result, caster, target, weapon_id, False,
                        hit_part=hit_name, hit_dir=shoot_dir, hit_back=hit_back, hit_pos=raycast_bone_res.Pos, hit_penetrate=hit_penetrate,
                        penetrate_power=penetrate_power, penetrate_materials=penetrate_materials, is_ads=not caster.is_real_ads, weapon_guid=weapon_guid,
                    )
                else:
                    if target:
                        if target.IsSimpleCombatUnit:
                            caster.game_logic.DealWeaponDamageResult(
                                self.spell_id, spell_result, caster, target, weapon_id, False, hit_dir=shoot_dir,
                                penetrate_power=penetrate_power, penetrate_materials=penetrate_materials,
                                hit_pos=res.physics_hit_pos, weapon_guid=weapon_guid,
                            )
                        elif target.IsFragment:
                            continue
                    # 音效result
                    not hit_combat_avatar and sound_results.append(WrapperSoundResult(target, res.physics_hit_pos, material_type))

                # 击中特效result
                if not has_send_hit_effect:
                    self.WrapperSendHitEffectResult(spell_result, res.target, res.physics_hit_pos, res.hit_normal, material_type, shoot_dir, weapon_id, False, -1, res.hit_pos_through, raycast_bone_res, weapon.gun_type)
                    has_send_hit_effect = True

            # 打包spellresult发送
            spell_result.sound_results = sound_results
            self.WrapperSendSpellResult(spell_result, True)

        # 弹道特效
        self.StrikeBallisticEffect(caster, weapon_guid, gun_id, ballistic_hit_pos, start_pos, shoot_dir, True, material_type=material_type)
        if penetrate_handler.power <= 0:
            # 穿透结束 子弹销毁
            return

    def SpellOnClear(self):
        owner = self.caster
        if owner.IsPlayerCombatAvatar and owner.hand_model and owner.hand_model.is_in_stealth:
            owner.TellServerMotionState(cconst.UNIT_STATE_IDLE)

    def SpellFriendOnAttackSound(self, cur_fly_dist, shoot_dir, start_pos, end_pos):
        all_hits = genv.space.AllRaycast(start_pos, cur_fly_dist, to_pos=end_pos, filter_info=cconst.PHYSICS_SELF_TRIGGER)
        if not all_hits:
            return
        for r in sorted(all_hits, key=lambda rs: (rs.Pos - start_pos).length):
            body_owner = getattr(r.Body, 'owner', None)
            if body_owner and body_owner.IsCombatAvatar and body_owner.id != genv.player.id and not genv.space.game_logic.IsEnemy(genv.player, body_owner):
                genv.player.PlayGeneralSoundEventById(856, pos=r.Pos.tuple(), is_3d=True, noGameObject=True)

# region GM
    # [DEBUG]
    def GMOnSpellStrike(self):
        # 实际发射时的数据（HPsight骨骼朝向，屏幕朝向，相机到Hpsight点的方向）
        caster = self.caster
        # 相机方向
        camera = MEngine.GetGameplay().Player.Camera
        middle_point = gui.device_screen_center
        camera_dir = camera.GetRayDirectionFromScreenPoint(int(middle_point[0]), int(middle_point[1]))
        # 只抓取举枪瞄准的情况
        if not caster.is_real_ads:
            return (camera_dir, camera_dir, camera_dir, False)
        # 有瞄具才有区别，不然都一样
        weapon_case = caster.GetCurWeaponCase()
        if not weapon_util.IsReflectorSightEnable(weapon_case):
            return (camera_dir, camera_dir, camera_dir, False)
        
        # 相机到Hpsight点的方向
        optic_model = weapon_case.GetWeaponPartModel(consts.WeaponPartType_Optic)
        bone_transform = optic_model.GetBoneWorldTransform(cconst.TACH_POINT_GUN_SIGHT)
        # 实际射击方向
        optic_dir = bone_transform.z_axis
        # 相机到Hpsight连线方向
        camera_to_optic_dir = (bone_transform.translation - camera.GetOrigin()).get_normalized()
        return (optic_dir, camera_to_optic_dir, camera_dir, False)
    # [DEBUG]
# endregion GM