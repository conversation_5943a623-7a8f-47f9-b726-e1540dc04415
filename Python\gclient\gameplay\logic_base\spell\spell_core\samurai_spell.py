# -*- coding: utf-8 -*-
# author: l<PERSON><PERSON><PERSON><PERSON><PERSON>
import MType  # noqa
import MEngine

from gclient.gameplay.logic_base.spell.spell_core import spell_core_main
import switches
from gshare import formula
from gshare.italent import TalentCode
from gclient import cconst
from gclient.data import melee_weapon_data
from gclient.gameplay.logic_base.spell.spell_core.spell_core_main import SpikeSpellWorker

from gclient.framework.util import events
from gshare import consts
from gclient.framework.util import <PERSON><PERSON><PERSON>per

from gshare import effect_util

from gclient.data import effect_data
from gclient.data import material_type_data
from gclient.gameplay.logic_base.models import anim_const

_reload_all = True


class SpellWorker(SpikeSpellWorker):

    def __init__(self, caster):
        super(SpellWorker, self).__init__(caster)
        self._ensure_strike = True
        self.result_cache = None

    @property
    def ensure_strike(self):
        if self.extra.get('IsSamuraiSprint', False) or self.extra.get('<PERSON><PERSON><PERSON>uraiBlock', False):
            return False
        return self._ensure_strike

    def GetHitScreenPos(self):
        return gui.device_screen_center

    def GetHitDir(self, hit_screen_pos):
        camera = MEngine.GetGameplay().Player.Camera
        return camera.GetRayDirectionFromScreenPoint(int(hit_screen_pos[0]), int(hit_screen_pos[1]))

    def GetHitStartPos(self, caster_position, hit_dir):
        camera = MEngine.GetGameplay().Player.Camera
        diff_pos = camera.GetOrigin() - MType.Vector3(*caster_position)
        diff_pos.y = 0
        camera_length = diff_pos.length
        camera_move_dist = camera_length * 0.8
        start_pos = camera.GetOrigin() + hit_dir * camera_move_dist  # 修正一下
        return start_pos

    def SpellStart(self):
        if self.extra.get('IsSamuraiSprint', False):
            self.SpellStartForSamuraiSprint()
        elif self.extra.get('IsSamuraiBlock', False):
            self.SpellStartForSamuraiBlock()
        else:
            self.SpellStartForMelee()

    def SpellStrike(self, code):
        if self.extra.get('IsSamuraiSprint'):
            self.SpellStrikeForSamuraiSprint(code)
        elif self.extra.get('IsSamuraiBlock'):
            self.SpellStrikeForSamuraiBlock(code)
        else:
            self.SpellStrikeForMelee(code)

    def SpellStop(self):
        if self.extra.get('IsSamuraiSprint'):
            self.SpellStopForSamuraiSprint()
        elif self.extra.get('IsSamuraiBlock'):
            self.SpellStopSamuraiBlock()
        else:
            self.SpellStopForMelee()

    def SpellStrikeForMelee(self, code):
        _ = code
        # 近战辅助瞄准
        caster = self.caster
        caster.StopAssistAimMelee()
        space = caster.space
        if not space or caster is not genv.player:
            return
        cur_melee = caster.GetCurWeaponCase()
        if not cur_melee:
            return
        melee_weapon_id = cur_melee.equip_proto.get('melee_weapon_id')
        melee_data = melee_weapon_data.data.get(melee_weapon_id, {})
        weapon_id = cur_melee.weapon_id

        hit_screen_pos = self.GetHitScreenPos()
        hit_dir = self.GetHitDir(hit_screen_pos)
        hit_start_pos = self.GetHitStartPos(caster.position, hit_dir)
        hit_dist = self.proto.get('melee', {}).get('range', 1.0)
        hit_box = melee_data.get('melee_weapon_attack_box', (0.1, 0.2, 0.2))
        extra_data = {}
        hand_model = caster.hand_model
        cur_attack_num = 0
        if hand_model:
            cur_attack_num = hand_model.GetVariableI('MeleeAttackHitSfx', hand_model.action_graph_id)
        # model = caster.model
        # if model:
        #     model.SetVariableZ('ACTOR_TPS_isStab', cur_attack_num == 0, model.locomotion_graph_id)

        if caster.HasTalentCode(TalentCode.SpecialMeleeAttack):
            talent_proto = caster.GetProtoFromTalentCode(TalentCode.SpecialMeleeAttack)

            if talent_proto:
                talent_code_params = talent_proto.get('talent_code_params', {})

                melee_weapon_id = talent_code_params.get('melee_weapon_id')
                attack_num = talent_code_params.get('attack_num', 0)
                if cur_melee.equip_proto.get('melee_weapon_id') == melee_weapon_id and attack_num == cur_attack_num:
                    hit_dist = talent_code_params.get('range', hit_dist)
                    hit_box = talent_code_params.get('box', hit_box)
                    extra_data = {'samurai_special_hit': 1}

        spell_result = self.NewSpellResult()
        spell_result.verify_start_pos = hit_start_pos
        spell_result.cost_ammo = False

        # 宽高长
        hit_res = space.ClosestSweepWithBox(hit_box, hit_start_pos, hit_dist, hit_dir, cconst.PHYSICS_BULLET, local_trans=caster.model.model.Transform)
        if hit_res and hit_res.IsHit and hit_res.Body:
            target = getattr(hit_res.Body, 'owner', None)
            game_logic = space.game_logic
            if not game_logic:
                return
            if not target:
                # 用骨骼再打一次吧, 用高模拟radius算了
                bone_res = space.SphereSweepCollisionBone(hit_start_pos, hit_start_pos + hit_dir * hit_dist, hit_box[1], [caster.model.skeleton])
                if bone_res and bone_res.actor:
                    target = getattr(bone_res.Body.Parent, 'owner', None)
                    hit_res = bone_res

            if not self.DealMeleeSpellDamage(target, spell_result, cur_melee, hit_dir, hit_res, extra_data):
                if not (target and (target.IsCombatAvatar or target.IsMonster)):
                    self.HandleMeleeHitEffect(cur_melee, space, hit_start_pos, hit_dist, hit_dir, target, hit_res, weapon_id, melee_data)
                    res = spell_core_main.GetShootResult(caster, 800, None, shoot_dir=hit_dir, start_pos=hit_start_pos)
                    if target and target.IsDestructible:
                        self.OnHitDestructible(res, target, 4)  # 用Glock的顶一下
                    if target and target.IsBreakItem:
                        self.OnBreakItemHitImmediate(res, target, 1, 0.5)
        else:
            # 用骨骼再打一次吧, 用高模拟radius算了
            bone_res = space.SphereSweepCollisionBone(hit_start_pos, hit_start_pos + hit_dir * hit_dist, hit_box[1], [caster.model.skeleton])
            if bone_res and bone_res.actor:
                target = getattr(bone_res.Body.Parent, 'owner', None)
                hit_res = bone_res
                self.DealMeleeSpellDamage(target, spell_result, cur_melee, hit_dir, hit_res, extra_data)
        # [DEBUG]
        if switches.DRAW_RAY:
            m = MType.Matrix4x3()
            m.translation = hit_start_pos
            m.yaw = caster.model.model.Transform.yaw
            genv.space.DrawBoxOverlapDebugInfo(m, hit_box[0], hit_box[1], hit_box[2], life_time=5)

            m = MType.Matrix4x3()
            m.translation = hit_start_pos + hit_dir * hit_dist
            m.yaw = caster.model.model.Transform.yaw
            genv.space.DrawBoxOverlapDebugInfo(m, hit_box[0], hit_box[1], hit_box[2], life_time=5)
        # [DEBUG]

        if caster.IsPlayerCombatAvatar and caster.hand_model and caster.hand_model.is_in_stealth:
            genv.messenger.Broadcast(events.ON_PLAYER_SPELL_STRIKE, self.spell_id)

        self.strike_then_stop and self.SpellStop()

        if caster.IsPlayerCombatAvatar and caster.backpack.slots.get(consts.BackpackSlot.WEAPON_3):
            caster.TellServerMotionState(consts.UNIT_STATE_SPELL, self.spell_id, force_deliver=True)

    def DealMeleeSpellDamage(self, target, spell_result, cur_melee, hit_dir, hit_res, extra_data):
        game_logic = self.caster.space.game_logic
        if target and (target.IsCombatAvatar or target.IsMonster) and target.is_alive \
                and not target.is_invincible and game_logic.CanDamage(self.caster, target):
            weapon_id = cur_melee.weapon_id
            weapon_guid = cur_melee.weapon_guid
            hit_back = target.IsCombatAvatar and target.model and target.model.CalcDeathDir(hit_dir=hit_dir) == 1
            game_logic.DealMeleeWeaponDamageResult(self.spell_id, spell_result, self.caster, target, weapon_id,
                                                   hit_dir=hit_dir, hit_back=hit_back, hit_pos=hit_res.Pos,
                                                   weapon_guid=weapon_guid)
            if spell_result.extra:
                spell_result.extra.update(extra_data)
            else:
                spell_result.extra = extra_data
            hit_perform_id = -1
            if cur_melee.guise_id:
                fire_hit_effect_path_dict = cur_melee.guise_template_data.get('melee_skin_hit_object_effect_id')
                hit_effect_id = fire_hit_effect_path_dict.get(1001, fire_hit_effect_path_dict[0]) if \
                    fire_hit_effect_path_dict else 0
            self.WrapperSendHitEffectResultForMelee(spell_result, target, hit_res.Pos, hit_res.Normal, 1001, hit_dir, weapon_id, True, hit_perform_id)

            # 屏幕特效
            melee_weapon_id = cur_melee.equip_proto.get('melee_weapon_id')
            melee_data = melee_weapon_data.data.get(melee_weapon_id, {})
            melee_hit_camera_sfx_id = melee_data.get('melee_hit_camera_sfx_id')
            if cur_melee.guise_id:
                melee_hit_camera_sfx_id = cur_melee.guise_template_data.get('melee_hit_camera_sfx_id',
                                                                            melee_hit_camera_sfx_id)
            if melee_hit_camera_sfx_id:
                MHelper.PlayCameraEffectById(melee_hit_camera_sfx_id, 1.0)
            return True
        return False

    def SpellStrikeForRobot(self, code):
        space = self.caster.space
        if not space:
            return

        game_logic = space.game_logic
        if not game_logic:
            return

        caster = self.caster
        cur_melee = caster.GetCurWeaponCase(False)
        if not cur_melee:
            return
        melee_weapon_id = cur_melee.equip_proto.get('melee_weapon_id')
        melee_data = melee_weapon_data.data.get(melee_weapon_id, {})
        weapon_id = cur_melee.weapon_id
        weapon_guid = cur_melee.weapon_guid

        position = caster.position
        hit_start_pos = (position[0], position[1] + 1.5, position[2])
        hit_dir, _ = self.caster.GetShootDirection(hit_start_pos)
        hit_start_pos = MType.Vector3(*hit_start_pos)
        hit_dist = self.proto.get('samurai', {}).get('sprint_range', 1.0)
        hit_box = melee_data.get('melee_weapon_attack_box', (0.1, 0.2, 0.2))
        extra_data = {}

        spell_result = self.NewSpellResult()
        spell_result.verify_start_pos = hit_start_pos
        spell_result.cost_ammo = False

        # 宽高长
        hit_results = space.AllSweepWithBox(hit_box, hit_start_pos, hit_dist, hit_dir, cconst.PHYSICS_CHARCTRL,
                                            local_trans=caster.model.model.Transform, with_trigger=True)
        hits = []
        for hit in hit_results:
            target = getattr(hit.Body, 'owner', None)
            # 过滤掉自己和队友
            if target and target.IsSimpleCombatUnit and game_logic.IsFriend(caster, target):
                continue
            hits.append((hit.Distance, hit))

        hits.sort()
        if not hits:
            return
        hit_distance, hit_res = hits[0]
        if hit_res and hit_res.IsHit and hit_res.Body:
            target = getattr(hit_res.Body, 'owner', None)
            if target and (target.IsCombatAvatar or target.IsMonster) and target.is_alive \
                    and not target.is_invincible and game_logic.CanDamage(self.caster, target):
                hit_back = target.IsCombatAvatar and target.model and target.model.CalcDeathDir(hit_dir=hit_dir) == 1
                game_logic.DealMeleeWeaponDamageResult(self.spell_id, spell_result, self.caster, target, weapon_id,
                                                       hit_dir=hit_dir, hit_back=hit_back, hit_pos=hit_res.Pos,
                                                       weapon_guid=weapon_guid)
                if spell_result.extra:
                    spell_result.extra.update(extra_data)
                else:
                    spell_result.extra = extra_data
                self.WrapperSendHitEffectResultForMelee(spell_result, target, hit_res.Pos, hit_res.Normal, 1001, hit_dir, weapon_id, True, -1)

            else:
                self.HandleMeleeHitEffect(cur_melee, space, hit_start_pos, hit_dist, hit_dir, target, hit_res, weapon_id, melee_data)

        self.strike_then_stop and self.SpellStop()

    def HandleMeleeHitEffect(self, cur_melee, space, hit_start_pos, hit_dist, hit_dir, target, hit_res, weapon_id, melee_data):
        # 处理刀痕，要用射线校正具体位置
        hand_model = self.caster.hand_model
        if not hand_model:
            return
            # 第几下攻击
        hit_pose_id = hand_model.GetVariableI('MeleeAttackHitSfx', hand_model.action_graph_id)

        melee_attack_hit_fx_list = melee_data.get('melee_attack_hit_fx_list')
        if cur_melee.guise_id:
            melee_attack_hit_fx_list = cur_melee.guise_template_data.get('melee_attack_hit_fx_list', melee_attack_hit_fx_list)

        # 刀痕
        if not melee_attack_hit_fx_list:
            return

        effect_pos = hit_res.Pos
        effect_normal = hit_res.Normal
        max_life = space.game_logic.GetHitEffectMaxLifeTime(weapon_id)

        ownerid = None

        hit_effect_id = melee_attack_hit_fx_list[hit_pose_id]

        if not hit_effect_id:
            return

        melee_attack_hit_dir_list = melee_data.get('melee_attack_hit_dir_list')
        if cur_melee.guise_id:
            melee_attack_hit_dir_list = cur_melee.guise_template_data.get('melee_attack_hit_dir_list', melee_attack_hit_dir_list)

        melee_attack_hit_dir = melee_attack_hit_dir_list[hit_pose_id]

        hr = space.RawRaycast(hit_start_pos, hit_dist, cconst.PHYSICS_SHOOT_TEST, with_trigger=False, to_dir=hit_dir)

        if hit_res and hit_res.IsHit and hit_res.Body:
            owner = getattr(hit_res.Body, 'owner', None)
            if owner and owner.id == target.id:
                effect_pos = hr.Pos
                effect_normal = hr.Normal

        # [DEBUG]
        if switches.DRAW_RAY:
            space.DrawRay(effect_pos, effect_pos - hit_dir)
            m = MType.Matrix4x3()
            m.translation = effect_pos
            genv.space.DrawBoxOverlapDebugInfo(m, 0.05, 0.05, 0.05, life_time=5)
        # [DEBUG]

        camera = MEngine.GetGameplay().Player.Camera

        # 需要用相机up方向和normal确认贴画播放位置旋转
        m = MHelper.GetEffectTransformByNormalAndUp(effect_pos, effect_normal, camera.Transform.y_axis)
        m = formula.SetRotateTrans(m, (effect_normal.x, effect_normal.y, effect_normal.z), melee_attack_hit_dir, effect_pos)

        m.translation = m.translation + effect_normal * 0.01


        MHelper.PlayHitEffectInWorldByTransformWithId(hit_effect_id, effect_pos, m, max_life=max_life, insure_play=True)

        hit_material_type = hr.MaterialTypeId if hr else hit_res.MaterialTypeId

        hit_material_type = hit_material_type if hit_material_type else 1

        # [DEBUG]
        if switches.DRAW_RAY:
            print('当前击中的材质MaterialTypeId：', hr.MaterialTypeId)
        # [DEBUG]

        # 火星
        fire_hit_effect_path = material_type_data.data.get(hit_material_type, {}).get('melee_hit_effect_path')
        if cur_melee.guise_id:
            fire_hit_effect_path_dict = cur_melee.guise_template_data.get('melee_skin_hit_object_effect_id')
            if fire_hit_effect_path_dict:
                if hit_material_type in fire_hit_effect_path_dict:
                    fire_hit_effect_path = effect_data.data[fire_hit_effect_path_dict[hit_material_type]]['path']
                elif 0 in fire_hit_effect_path_dict:
                    fire_hit_effect_path = effect_data.data[fire_hit_effect_path_dict[0]]['path']
        if fire_hit_effect_path:
            melee_skin_hit_object_effect_dir = cur_melee.guise_template_data and cur_melee.guise_template_data.get('melee_skin_hit_object_effect_dir')
            if melee_skin_hit_object_effect_dir and len(melee_skin_hit_object_effect_dir) > hit_pose_id:
                effect_dir = melee_skin_hit_object_effect_dir[hit_pose_id]
                # 需要用相机up方向和normal确认贴画播放位置旋转
                m = MHelper.GetEffectTransformByNormalAndUp(effect_pos, effect_normal, camera.Transform.y_axis)
                m = formula.SetRotateTrans(m, (effect_normal.x, effect_normal.y, effect_normal.z), effect_dir,
                                           effect_pos)
                m.translation = m.translation + effect_normal * 0.01
                MHelper.PlayHitEffectInWorldByTransform(fire_hit_effect_path, effect_pos, m, max_life=max_life,
                                                        insure_play=True)
            else:
                MHelper.PlayHitEffectInWorld(fire_hit_effect_path, effect_pos, effect_normal, max_life=max_life,
                                             insure_play=True)

        # 击中声音
        self.caster.PlayHitSoundEvent(self.caster.id, sound_result={'target_id': ownerid,
                                                                    'killed': False,
                                                                    'pos': (effect_pos.x, effect_pos.y, effect_pos.z),
                                                                    'material_type': hit_material_type,
                                                                    'is_melee': True})

    def SpellStopForMelee(self):
        cur_weapon = self.caster.GetCurWeapon()
        if not cur_weapon:
            return
        self.caster.PlayMeleeEndAction()
        self.caster.spell_mgr.ClearCurrentSpell(self.spell_id)

    def SpellStartForMelee(self):
        # 这里根据extra的spell决定用哪个spell
        cur_weapon = self.caster.GetCurWeapon()
        if not cur_weapon:
            return
        caster = self.caster
        if not caster.IsRobotCombatAvatar:
            caster.ClearDelayShootReason()
            caster.ClearDelayShootReason(is_dual=True)
        self.graph_info = {
            'hand_graph': cur_weapon.equip_proto.get('hand_graph', ''),
            'tps_graph': cur_weapon.equip_proto.get('tps_graph', ''),
            'vice_tps_graph': cur_weapon.equip_proto.get('vice_tps_graph', ''),
            'sync_param': True
        }
        caster.PlayMeleeStartAction(self.spell_id, self.graph_info)
        if caster.IsRobotCombatAvatar:
            self.SpellStrikeForRobot(None)

    def WrapperSendHitEffectResultForMelee(self, spell_result, target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, is_send, hit_perform_id):
        hit_effect_data = effect_util.WrapperHitEffectResult(target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, 0, hit_perform_id)
        if not hit_effect_data:
            return
        hit_effect_data['hit_perform_mode'] |= 1 << 0  # 近战bit
        spell_result.hit_effect.append(hit_effect_data)
        is_send and genv.spell_core.SendSpellResult(spell_result)

    def SpellStartForSamuraiBlock(self):
        caster = self.caster
        cur_weapon = caster.GetCurWeapon()
        if not cur_weapon:
            return
        if caster and caster.IsCombatAvatar:
            cur_melee = caster.GetCurWeaponCase()
            if not cur_melee:
                return
            if not cur_melee.CheckSamuraiBlockCD():
                return
            caster.CallServer("CallSpellLogic", self.spell_id, "SpellStartForSamuraiBlock", [])
            caster.model.PushUpperGraph(cur_weapon.equip_proto.get('tps_graph', ''))

    def SpellStrikeForSamuraiBlock(self, code):
        pass

    def SpellStopSamuraiBlock(self):
        caster = self.caster
        self.caster.CallServer("OnSpellStrike", self.spell_id, self.level, self.item_guid, self.extra)
        if caster and caster.IsPlayerCombatAvatar:
            caster.EnterStateItemrecycle(self.item_guid)

    def SpellStartForSamuraiSprint(self):
        caster = self.caster
        cur_weapon = caster.GetCurWeapon()
        if not cur_weapon:
            return
        if caster and caster.IsCombatAvatar:
            cur_melee = caster.GetCurWeaponCase()
            if not cur_melee:
                return
            if not cur_melee.CheckSamuraiSprintCD():
                return
            caster.CallServer("CallSpellLogic", self.spell_id, "SpellStartForSamuraiSprint", [])
            caster.model.PushUpperGraph(cur_weapon.equip_proto.get('tps_graph', ''))
            caster.AddImpactBreakableReason(cconst.ImpactBreakableReason.SAMURAI_DASH_SPELL)
            caster.model.AddHijackMoveVec((0, 0, 1), 'samurai_dash')

    def PreSpellStrike(self, sprint_distance):
        if self.extra.get('IsSamuraiSprint'):
            self.PreSpellStrikeForSamuraiSprint(sprint_distance)

    def PreSpellStrikeForSamuraiSprint(self, sprint_distance):
        caster = self.caster
        caster.CallServer("OnSpellStrike", self.spell_id, self.level, self.item_guid, self.extra)

        space = caster.space
        if not space:
            return

        cur_melee = caster.GetCurWeaponCase()
        if not cur_melee:
            return

        weapon_id = cur_melee.weapon_id
        weapon_guid = cur_melee.weapon_guid

        hit_screen_pos = self.GetHitScreenPos()
        hit_dir = self.GetHitDir(hit_screen_pos)
        hit_start_pos = self.GetHitStartPos(caster.position, hit_dir)

        melee_proto = self.proto.get('melee', {})
        hit_dist = sprint_distance
        hit_box = melee_proto.get('box', (0.4, 0.8, 0.8))

        spell_result = self.NewSpellResult()
        spell_result.verify_start_pos = hit_start_pos
        spell_result.cost_ammo = False

        # 宽高长
        hit_res = space.ClosestSweepWithBox(hit_box, hit_start_pos,
                                            hit_dist, hit_dir,
                                            cconst.PHYSICS_SHOOT_TEST, local_trans=caster.model.model.Transform)

        if hit_res and hit_res.IsHit and hit_res.Body:
            ownerid = getattr(hit_res.Body, 'ownerid', None)
            if ownerid:
                target = caster.space.entities.get(ownerid)
            else:
                target = getattr(hit_res.Body.Parent, 'owner', None)
            game_logic = space.game_logic
            if not game_logic:
                return
            # [DEBUG]
            if switches.DRAW_RAY:
                m = MType.Matrix4x3()
                m.translation = hit_res.Pos
                genv.space.DrawBoxOverlapDebugInfo(m, hit_box[0], hit_box[1], hit_box[2], life_time=8)
            # [DEBUG]
            if target and (target.IsCombatAvatar or target.IsMonster) and target.is_alive \
                    and not target.is_invincible and game_logic.CanDamage(caster, target):
                hit_back = target.IsCombatAvatar and target.model and target.model.CalcDeathDir(hit_dir=hit_dir) == 1
                game_logic.DealMeleeWeaponDamageResult(self.spell_id, spell_result, caster, target, weapon_id,
                                                       hit_dir=hit_dir, hit_back=hit_back, hit_pos=hit_res.Pos,
                                                       weapon_guid=weapon_guid)
                self.result_cache = (spell_result, target, hit_res.Pos, hit_res.Normal, 1001, hit_dir, weapon_id, -1)

    def SpellStrikeForSamuraiSprint(self, code):
        _ = code
        caster = self.caster

        if caster and caster.IsPlayerCombatAvatar:
            # caster.ResetJumpFallSpeed()
            caster.model.SetJumpVerticalSpeedForbidReason(True, anim_const.JUMP_VERTICAL_SPEED_FORBID_REASON_DASH)
            caster.EnterStateItemrecycle(self.item_guid)

        caster.CancelSamuraiSprintTick()

        space = caster.space
        if not space:
            return

        if caster.IsPlayerCombatAvatar and caster.hand_model and caster.hand_model.is_in_stealth:
            genv.messenger.Broadcast(events.ON_PLAYER_SPELL_STRIKE, self.spell_id)

        self.WrapperSendHitEffectResultForSamuraiSprint()
        self.result_cache = None
    
    def SpellOnClear(self):
        if self.extra.get('IsSamuraiSprint'):
            self.SpellOnClearForSamuraiSprint()
        super(SpellWorker, self).SpellOnClear()

    def SpellOnClearForSamuraiSprint(self):
        super(SpellWorker, self).SpellOnClear()
        caster = self.caster
        if caster and caster.IsPlayerCombatAvatar:
            model = caster.model
            # model.SetJumpVerticalSpeedForbidReason(False, anim_const.JUMP_VERTICAL_SPEED_FORBID_REASON_DASH)
            # caster.ResetJumpFallSpeed()
            real_vel = model.GetVariableV3('ACTOR_TPS_RECORD_JUMP_VELOCITY', model.locomotion_graph_id)
            input_speed = formula.Length2D(real_vel)
            real_vel.y = 0
            model.SetVariableV3('jump_dir_expect_local', real_vel, model.locomotion_graph_id)
            model.SetVariableF('jump_max_speed', input_speed, model.locomotion_graph_id)
            # 强行进JumpFall
            model.SetVariableF('JumpFallBlendTime', 0, model.locomotion_graph_id)
            if not model.in_jump_fall:
                # 非JumpFall状态才需要这个事件，否则会重新进JumpUp
                model.FireEvent("JumpToTop", model.locomotion_graph_id)
            caster.RemoveImpactBreakableReason(cconst.ImpactBreakableReason.SAMURAI_DASH_SPELL)
            model.RemoveHijackMoveVec('samurai_dash')

    def SpellStopForSamuraiSprint(self):
        super(SpellWorker, self).SpellStop()
        caster = self.caster
        caster and caster.CancelSamuraiSprintTick()
        caster.CallServer("CallSpellLogic", self.spell_id, "SpellStopForSamuraiSprint", [])

    def WrapperSendHitEffectResultForSamuraiSprint(self):
        if not self.result_cache:
            return
        spell_result, target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, hit_perform_id = self.result_cache

        hit_effect_data = effect_util.WrapperHitEffectResult(target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, 0, hit_perform_id)
        if not hit_effect_data:
            return
        hit_effect_data['hit_perform_mode'] |= 1 << 0  # 近战bit
        spell_result.hit_effect.append(hit_effect_data)
        genv.spell_core.SendSpellResult(spell_result)