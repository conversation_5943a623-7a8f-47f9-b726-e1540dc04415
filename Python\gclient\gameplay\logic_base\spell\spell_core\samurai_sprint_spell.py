# -*- coding: utf-8 -*-
# 武士刀疾风斩 guojunwei
# 开始冲刺时判断前方击中目标，graph cue的时候结算伤害

import MType
import MEngine
import switches
from gclient.gameplay.logic_base.models import anim_const
from gshare import effect_util, formula
from gclient import cconst
from gclient.framework.util import events
from gclient.gameplay.logic_base.spell.spell_core.spell_core_main import SpikeSpellWorker


class SpellWorker(SpikeSpellWorker):

    def __init__(self, caster):
        super(SpellWorker, self).__init__(caster)
        self.result_cache = None
        self.damage_target_set = set()  # 已造成伤害得目标

    def GetHitScreenPos(self):
        return gui.device_screen_center

    def GetHitDir(self, hit_screen_pos):
        camera = MEngine.GetGameplay().Player.Camera
        return camera.GetRayDirectionFromScreenPoint(int(hit_screen_pos[0]), int(hit_screen_pos[1]))

    def GetHitStartPos(self, caster_position, hit_dir):
        camera = MEngine.GetGameplay().Player.Camera
        diff_pos = camera.GetOrigin() - MType.Vector3(*caster_position)
        diff_pos.y = 0
        camera_length = diff_pos.length
        camera_move_dist = camera_length * 0.8
        start_pos = camera.GetOrigin() + hit_dir * camera_move_dist  # 修正一下
        return start_pos

    def SpellStart(self):
        caster = self.caster
        self.damage_target_set = set()  # 已造成伤害得目标
        if caster and caster.IsCombatAvatar:
            caster.CallServer("CallSpellLogic", self.spell_id, "SpellStart", [])

    def PreSpellStrike(self, sprint_distance):
        caster = self.caster
        caster.CallServer("OnSpellStrike", self.spell_id, self.level, self.item_guid)

        return
        # space = caster.space
        # if not space:
        #     return
        #
        # cur_melee = caster.GetCurWeaponCase()
        # if not cur_melee:
        #     return
        #
        # weapon_id = cur_melee.weapon_id
        # weapon_guid = cur_melee.weapon_guid
        #
        # hit_screen_pos = self.GetHitScreenPos()
        # hit_dir = self.GetHitDir(hit_screen_pos)
        # hit_start_pos = self.GetHitStartPos(caster.position, hit_dir)
        #
        # melee_proto = self.proto.get('melee', {})
        # hit_dist = sprint_distance
        # hit_box = melee_proto.get('box', (0.4, 0.8, 0.8))
        #
        # spell_result = self.NewSpellResult()
        # spell_result.verify_start_pos = hit_start_pos
        # spell_result.cost_ammo = False
        #
        # # 宽高长
        # hit_res = space.ClosestSweepWithBox(hit_box, hit_start_pos,
        #                                     hit_dist, hit_dir,
        #                                     cconst.PHYSICS_SHOOT_TEST, local_trans=caster.model.model.Transform)
        #
        # if hit_res and hit_res.IsHit and hit_res.Body:
        #     ownerid = getattr(hit_res.Body, 'ownerid', None)
        #     if ownerid:
        #         target = caster.space.entities.get(ownerid)
        #     else:
        #         target = getattr(hit_res.Body.Parent, 'owner', None)
        #     game_logic = space.game_logic
        #     if not game_logic:
        #         return
        #     # [DEBUG]
        #     if switches.DRAW_RAY:
        #         m = MType.Matrix4x3()
        #         m.translation = hit_res.Pos
        #         genv.space.DrawBoxOverlapDebugInfo(m, hit_box[0], hit_box[1], hit_box[2], life_time=8)
        #     # [DEBUG]
        #     if target and (target.IsCombatAvatar or target.IsMonster) and target.is_alive \
        #             and not target.is_invincible and game_logic.CanDamage(caster, target):
        #         hit_back = target.IsCombatAvatar and target.model and target.model.CalcDeathDir(hit_dir=hit_dir) == 1
        #         game_logic.DealMeleeWeaponDamageResult(self.spell_id, spell_result, caster, target, weapon_id,
        #                                                hit_dir=hit_dir, hit_back=hit_back, hit_pos=hit_res.Pos,
        #                                                weapon_guid=weapon_guid)
        #         self.result_cache = (spell_result, target, hit_res.Pos, hit_res.Normal, 1001, hit_dir, weapon_id, 0)

    def SpellStrike(self, code):
        caster = self.caster

        if caster and caster.IsPlayerCombatAvatar:
            # caster.ResetJumpFallSpeed()
            caster.model.SetJumpVerticalSpeedForbidReason(True, anim_const.JUMP_VERTICAL_SPEED_FORBID_REASON_DASH)
            caster.EnterStateItemrecycle(self.item_guid)
            caster.model.SetForbidClimbReason(anim_const.ForbidClimbReason.SAMURAI_DASH, True)

        caster.CancelSamuraiSprintTick()

        space = caster.space
        if not space:
            return

        if caster.IsPlayerCombatAvatar and caster.hand_model and caster.hand_model.is_in_stealth:
            genv.messenger.Broadcast(events.ON_PLAYER_SPELL_STRIKE, self.spell_id)

    def SpellOnClear(self):
        super(SpellWorker, self).SpellOnClear()
        caster = self.caster
        if caster and caster.IsPlayerCombatAvatar:
            model = caster.model
            # model.SetJumpVerticalSpeedForbidReason(False, anim_const.JUMP_VERTICAL_SPEED_FORBID_REASON_DASH)
            # caster.ResetJumpFallSpeed()
            real_vel = model.GetVariableV3('ACTOR_TPS_RECORD_JUMP_VELOCITY', model.locomotion_graph_id)
            input_speed = formula.Length2D(real_vel)
            real_vel.y = 0
            model.SetVariableV3('jump_dir_expect_local', real_vel, model.locomotion_graph_id)
            model.SetVariableF('jump_max_speed', input_speed, model.locomotion_graph_id)
            # 强行进JumpFall
            model.SetVariableF('JumpFallBlendTime', 0, model.locomotion_graph_id)
            model.SetForbidClimbReason(anim_const.ForbidClimbReason.SAMURAI_DASH, False)
            if not model.in_jump_fall:
                # 非JumpFall状态才需要这个事件，否则会重新进JumpUp
                model.FireEvent("JumpToTop", model.locomotion_graph_id)

    def SpellStop(self):
        super(SpellWorker, self).SpellStop()
        caster = self.caster
        caster and caster.CancelSamuraiSprintTick()

    def WrapperSendHitEffectResult(self):
        if not self.result_cache:
            return
        spell_result, target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, hit_perform_id = self.result_cache

        hit_effect_data = effect_util.WrapperHitEffectResult(target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, 0, hit_perform_id)
        if not hit_effect_data:
            return
        hit_effect_data['hit_perform_mode'] |= 1 << 0  # 近战bit
        spell_result.hit_effect.append(hit_effect_data)
        genv.spell_core.SendSpellResult(spell_result)

    def OnSpellDamage(self, sprint_distance):
        caster = self.caster
        space = caster.space
        if not space:
            return

        cur_melee = caster.GetCurWeaponCase()
        if not cur_melee:
            return

        weapon_id = cur_melee.weapon_id
        weapon_guid = cur_melee.weapon_guid

        hit_screen_pos = self.GetHitScreenPos()
        hit_dir = self.GetHitDir(hit_screen_pos)
        hit_start_pos = self.GetHitStartPos(caster.position, hit_dir)

        melee_proto = self.proto.get('melee', {})
        hit_dist = 2  # 向前探测得距离小一点把，因为变成实时了
        hit_box = melee_proto.get('box', (0.4, 0.8, 0.8))

        spell_result = self.NewSpellResult()
        spell_result.verify_start_pos = hit_start_pos
        spell_result.cost_ammo = False

        # 宽高长
        hit_res = space.ClosestSweepWithBox(hit_box, hit_start_pos,
                                            hit_dist, hit_dir,
                                            cconst.PHYSICS_SHOOT_TEST, local_trans=caster.model.model.Transform)

        if hit_res and hit_res.IsHit and hit_res.Body:
            ownerid = getattr(hit_res.Body, 'ownerid', None)
            if ownerid:
                target = caster.space.entities.get(ownerid)
            else:
                target = getattr(hit_res.Body.Parent, 'owner', None)
            game_logic = space.game_logic
            if not game_logic:
                return
            # [DEBUG]
            if switches.DRAW_RAY:
                m = MType.Matrix4x3()
                m.translation = hit_res.Pos
                genv.space.DrawBoxOverlapDebugInfo(m, hit_box[0], hit_box[1], hit_box[2], life_time=8)
            # [DEBUG]
            if target and (target.IsCombatAvatar or target.IsMonster) and target.is_alive \
                    and not target.is_invincible and game_logic.CanDamage(caster, target) and target.id not in self.damage_target_set:
                hit_back = target.IsCombatAvatar and target.model and target.model.CalcDeathDir(hit_dir=hit_dir) == 1
                game_logic.DealMeleeWeaponDamageResult(self.spell_id, spell_result, caster, target, weapon_id,
                                                       hit_dir=hit_dir, hit_back=hit_back, hit_pos=hit_res.Pos,
                                                       weapon_guid=weapon_guid)
                self.damage_target_set.add(target.id)  # 每次技能对同一个目标只造成一次伤害
                self.result_cache = (spell_result, target, hit_res.Pos, hit_res.Normal, 1001, hit_dir, weapon_id, -1)
                self.WrapperSendHitEffectResult()
                self.result_cache = None
