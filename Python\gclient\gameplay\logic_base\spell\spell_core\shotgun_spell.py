# -*- coding: utf-8 -*-
import math
import time
import M<PERSON>, MEngine
import MType
import random

import switches
from gclient.data import weapon_data
from gclient.gameplay.logic_base.spell.spell_core import spell_core_main
from gclient.gameplay.logic_base.spell.spell_core.spell_core_main import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WrapperSoundResult
from gclient.gameplay.uicomponents.hud_frontsight_comp import HudFrontsightComp
from gshare import formula, effect_util, consts
from gshare.async_util import Async
from gclient.gameplay.logic_base.spell import spell_util

_reload_all = True


class SpellWorker(SpikeSpellWorker):

    def __init__(self, caster):
        super(<PERSON>pell<PERSON>orker, self).__init__(caster)
        self.has_damage_pellet_count = 0 # 造成伤害的弹丸数量
        self.max_damage_pellet_count = 0 # 可以造成伤害的最大弹丸数量

    def GetDirList(self):
        shoot_dir_list =[]
        is_real_ads = self.caster.is_real_ads
        shoot_idx = self.caster.shoot_idx
        weapon_case = self.caster.GetCurWeaponCase()
        weaponAttrData = weapon_case.GetWeaponAttrValue

        ammunition = weapon_case.GetWeaponPart(consts.WeaponPartType_Ammunition)
        pellet_num = weaponAttrData('shotgun_pellet_num', 1)
        weapon_attr = weapon_case.GetWeaponAttrValue
        use_scatter_in_ads = weapon_attr('UseScatterInAdsMode', False)
        for pn in range(int(pellet_num)):
            if is_real_ads:
                # 是否在ADS模式下使用腰部散射
                if use_scatter_in_ads:
                    scatterRadians = weapon_case.GetRealShootScatterRadian(shoot_idx)
                else:
                    scatterRadians = weapon_case.GetRealShootScatterRadianForAds(shoot_idx)
            else:
                scatterRadians = weapon_case.GetRealShootScatterRadian(shoot_idx)

            shoot_dir_list.append(self.GetShootDir(scatterRadians))

        return shoot_dir_list

    def GetShootDir(self, radians):
        middle_point = gui.device_screen_center
        camera = MEngine.GetGameplay().Player.Camera
        shoot_dir = camera.GetRayDirectionFromScreenPoint(int(middle_point[0]), int(middle_point[1]))
        if radians > 0:
            shoot_dir = formula.RandomDirInConeByConeRadians(shoot_dir, radians)
        return shoot_dir

    def GetShootStartPos(self, caster_position, shoot_dir):
        camera = MEngine.GetGameplay().Player.Camera
        diff_pos = camera.GetOrigin() - MType.Vector3(*caster_position)
        diff_pos.y = 0
        camera_length = diff_pos.length
        camera_move_dist = camera_length * 0.8
        start_pos = camera.GetOrigin()# + shoot_dir * camera_move_dist  # 修正一下
        return start_pos

    def SpellStrike(self, code):
        # 技能逻辑生效
        if not self.caster or self.caster.is_destroyed():
            return
        if self.caster is not genv.player:
            return
        weapon = self.caster.GetCurWeaponCase()
        if not weapon:
            return
        HudFrontsightComp.isInited() and HudFrontsightComp.instance().AimPointScatter(self.caster, self.caster.shoot_idx)
        self.has_damage_pellet_count = 0
        self.max_damage_pellet_count = weapon.GetWeaponAttrValue('shotgun_pellet_limit', 4)
        shoot_dir_list = self.GetDirList()
        self.BulletEffect(weapon.weapon_guid, weapon.gun_id, shoot_dir_list)
        for index, shoot_dir in enumerate(shoot_dir_list):
            self.BulletFly(shoot_dir, index)

    def BulletEffect(self, weapon_guid, gun_id, shoot_dir_list):
        spell_result = self.NewSpellResult()
        spell_result.weapon_guid = weapon_guid
        spell_result.extra = {'gun_id': gun_id, 'hit_list': []}
        spell_result.is_hit = False
        for i in range(len(shoot_dir_list)):
            shoot_dir = shoot_dir_list[i]
            start_pos = self.GetShootStartPos(self.caster.position, shoot_dir)
            # 先打一发超远距离射线 判断能不能打到东西 播特效
            far_hit_res = spell_core_main.GetShootResult(self.caster, 800, None, shoot_dir=shoot_dir, start_pos=start_pos)
            # 弹道特效
            if far_hit_res and far_hit_res.is_hit:
                spell_result.is_hit = True
                end_pos = far_hit_res.physics_hit_pos
                distance = formula.Distance3D(self.caster.position, formula.Tuple(end_pos))
            else:
                distance = 500
                end_pos = start_pos + shoot_dir * distance
            self.WrapperSendBallisticEffectResult(spell_result, end_pos, start_pos, distance, is_send=False)
            
            extra_data = {
                "create_pos": formula.Tuple(start_pos),
                "end_pos": formula.Tuple(end_pos),
            }
            weapon_case = self.caster.GetCurWeaponCase()
            if weapon_case:
                bullet_speed = weapon_case.GetWeaponAttrValue("bullet_velocity", 100)
                extra_data["bullet_velocity"] = bullet_speed
            
            hit_list = spell_result.extra.get('hit_list', [])
            hit_list.append(extra_data)
            spell_result.extra['hit_list'] = hit_list

        # 霰弹枪的子弹数量比较多
        # extra_data = {
        #     "create_pos": formula.Tuple(start_pos),
        #     "end_pos": formula.Tuple(end_pos),
        # }
        # spell_result.extra.update(extra_data)
        # 打包spellresult发送
        self.WrapperSendSpellResult(spell_result, True)

    def GetDirListForRobot(self):
        shoot_dir_list =[]
        robot_position = self.caster.position
        shoot_dir = MType.Vector3()
        shoot_dir.set_pitch_yaw(self.caster.camera_controller.pitch, self.caster.camera_controller.yaw)
        shoot_dir.normalize()
        start_pos = MType.Vector3(robot_position[0], robot_position[1] + 1.5, robot_position[2])
        end_pos = start_pos + shoot_dir

        pellet_num = 4
        for pn in range(int(pellet_num)):
            unit_random_point = formula.Random2DInCircleTest((0, 0), 0.1)
            pos = MType.Vector3(end_pos[0] + unit_random_point[0], end_pos[1] - unit_random_point[1], end_pos.z)
            shoot_dir_list.append(pos - start_pos)

        return shoot_dir_list

    def BulletEffectForRobot(self, weapon_guid, gun_id, shoot_dir_list):
        spell_result = self.NewSpellResult()
        spell_result.weapon_guid = weapon_guid
        spell_result.extra = {'gun_id': gun_id}
        for i in range(len(shoot_dir_list)):
            shoot_dir = shoot_dir_list[i]
            robot_position = self.caster.position
            start_pos = MType.Vector3(robot_position[0], robot_position[1] + 1.5, robot_position[2])
            # 先打一发超远距离射线 判断能不能打到东西 播特效
            far_hit_res = spell_core_main.GetShootResult(self.caster, 800, None, shoot_dir=shoot_dir, start_pos=start_pos)
            # 弹道特效
            if far_hit_res and far_hit_res.is_hit:
                end_pos = far_hit_res.physics_hit_pos
                distance = formula.Distance3D(self.caster.position, formula.Tuple(end_pos))
            else:
                distance = 500
                end_pos = start_pos + shoot_dir * distance
            self.WrapperSendBallisticEffectResult(spell_result, end_pos, start_pos, distance, is_send=False)

        # 打包spellresult发送
        self.WrapperSendSpellResult(spell_result, True)

    def SpellStrikeForRobot(self, code):
        if not self.caster or self.caster.is_destroyed():
            return
        if not self.caster.IsRobotCombatAvatar:
            return
        weapon = self.caster.GetCurWeaponCase(False)
        if not weapon:
            return
        self.has_damage_pellet_count = 0
        self.max_damage_pellet_count = 4
        shoot_dir_list = self.GetDirListForRobot()
        self.BulletEffectForRobot(weapon.weapon_guid, weapon.gun_id, shoot_dir_list)
        for shoot_dir in shoot_dir_list:
            self.BulletFlyForRobot(shoot_dir)

    @Async
    def BulletFly(self, shoot_dir_fps, shoot_index):
        caster = self.caster
        if not caster or caster.is_destroyed() or caster is not genv.player or not caster.model.isValid():
            return
        weapon = caster.GetCurWeaponCase()
        if not weapon:
            return
        weapon_id = weapon.weapon_id
        weapon_guid = weapon.weapon_guid
        gun_id = weapon.gun_id
        weapon_attr_getter = weapon.GetWeaponAttrValue

        bullet_fly_speed = weapon_attr_getter('bullet_velocity', 800)
        bullet_fly_range = weapon_attr_getter('damage_range', 800)

        yield_time = 0.1
        first_frame_bullet_dist = bullet_fly_speed * yield_time
        cur_bullet_dist = 0
        cur_fly_dist = first_frame_bullet_dist
        cur_time = time.time()

        start_pos_fps = self.GetShootStartPos(caster.position, shoot_dir_fps)
        start_pos_tps = caster.model.GetBoneWorldPosition('HP_Camera')
        end_pos_tps = start_pos_fps + shoot_dir_fps * 5
        shoot_dir_tps = (end_pos_tps - start_pos_tps).get_normalized()

        use_tps_ballistic = not caster.is_fps_mode and not caster.is_ads

        # 子弹射出的初始位置，用于服务端校验
        if use_tps_ballistic and cur_bullet_dist == 0:
            verify_start_pos = start_pos_tps
            verify_shoot_dir = shoot_dir_tps
        else:
            verify_start_pos = start_pos_fps
            verify_shoot_dir = shoot_dir_tps

        # ClientSALog
        caster.RecordGunFireAmmo(weapon.weapon_id)

        open_assist_aim = False
        if gui.is_pc:
            open_assist_aim = genv.player.IsAssistAimSettingOpen()

        # 一个角色只能受击一次 每次要排除掉已击中的角色
        bullet_exclude_actor = [caster.model.GetSkeleton()]
        shoot_result_getter = spell_core_main.GetShootResultWithPenetrate
        penetrate_handler = spell_core_main.PenetrateHandler(caster.game_logic, weapon_attr_getter('penerate_power_base', 100), weapon.hit_part_priority_config)
        start_pos, shoot_dir = start_pos_fps, shoot_dir_fps
        hit_combat_avatar = False
        while cur_bullet_dist < bullet_fly_range:
            if use_tps_ballistic and cur_bullet_dist == 0:
                # tps下，角色向镜头正前方5m位置前探3m
                start_pos, shoot_dir = start_pos_tps, shoot_dir_tps
                cur_fly_dist = 3
            else:
                shoot_dir = shoot_dir_fps

            shoot_results = shoot_result_getter(
                caster, cur_fly_dist, shoot_dir, start_pos,
                exclude_actor=bullet_exclude_actor, penetrate_handler=penetrate_handler
            )

            if switches.BULLET_DEBUG and switches.IS_DEBUG:
                spell_core_main.DrawBulletDebug(start_pos, shoot_dir, cur_fly_dist, shoot_results)

            if shoot_results:
                # build spell result
                spell_result = self.NewSpellResult()
                spell_result.weapon_guid = weapon_guid
                spell_result.cost_ammo = False
                spell_result.extra = {'gun_id': gun_id}
                spell_result.verify_start_pos = verify_start_pos
                spell_result.verify_shoot_dir = verify_shoot_dir
                spell_result.verify_assist_aim = open_assist_aim
                camera_transform = MEngine.GetGameplay().Player.Camera.Transform
                spell_result.verify_camera_pos = camera_transform.translation.tuple()
                # spell_result.verify_camera_yaw = camera_transform.yaw + math.pi
                penetrate_materials = []
                sound_results = []
                first_result_target = shoot_results[0].target
                for res in shoot_results:
                    raycast_bone_res = res.raycast_bone_res
                    target = res.target
                    material_type = res.materialTypeId
                    penetrate_power = res.penetrate_power
                    penetrate_materials.append(material_type)
                    if raycast_bone_res and target:
                        if self.has_damage_pellet_count < self.max_damage_pellet_count:
                            hit_name = raycast_bone_res.name
                            modify_hit_name = res.penetrate_hit_part if res.penetrate_hit_part else hit_name
                            if target.IsCombatAvatar:
                                # 是否背后击中
                                hit_back = target.model and target.model.CalcDeathDir(hit_dir=shoot_dir) == 1
                                # 是否穿透击中
                                hit_penetrate = res.penetrate_count > 0 and target != first_result_target
                                hit_combat_avatar = True
                            else:
                                hit_penetrate = hit_back = False
                            # 校验碰撞盒大小
                            verify_hit_radius = 0.0
                            verify_hit_half = None
                            verify_bone_scale = None
                            verify_hit_offset = None
                            if hit_name and target.IsCombatAvatar and target.model:
                                # 校验碰撞盒尺寸是否被外挂修改
                                skeleton = target.model.skeleton
                                verify_hit_radius, verify_hit_half = spell_util.GetCollisionVerify(skeleton, modify_hit_name)
                                verify_hit_offset, verify_bone_scale = spell_util.GetCollisionBoneOffset(skeleton, hit_name, raycast_bone_res.Pos)

                            # 伤害result
                            self.caster.game_logic.DealWeaponDamageResult(self.spell_id, spell_result, caster, target, weapon_id, True,
                                hit_part=modify_hit_name, hit_dir=shoot_dir, hit_back=hit_back, hit_pos=raycast_bone_res.Pos, hit_penetrate=hit_penetrate,
                                penetrate_power=penetrate_power, shoot_idx=caster.shoot_idx, penetrate_materials=penetrate_materials,
                                is_ads=not caster.is_real_ads, weapon_guid=weapon_guid,
                                verify_hit_radius=verify_hit_radius, verify_hit_half=verify_hit_half,
                                verify_hit_offset=verify_hit_offset, verify_bone_scale=verify_bone_scale,
                            )
                            self.has_damage_pellet_count += 1
                    else:
                        if target:
                            if target.IsSimpleCombatUnit:
                                if self.has_damage_pellet_count < self.max_damage_pellet_count:
                                    caster.game_logic.DealWeaponDamageResult(self.spell_id, spell_result, caster, target, weapon_id, True,
                                        hit_dir=shoot_dir, penetrate_power=penetrate_power, penetrate_materials=penetrate_materials, hit_pos=res.physics_hit_pos, weapon_guid=weapon_guid
                                    )
                                    if spell_result.damage_result:
                                        self.has_damage_pellet_count += 1
                            if target.IsDestructible:
                                self.OnHitDestructible(res, target, gun_id)
                            elif target.IsBreakItem:
                                self.OnBreakItemHitImmediate(res, target, gun_id)
                            elif target.IsCarriable:
                                spell_core_main.OnHitCarriable(res, target, gun_id, caster)
                            elif target.IsStrikeItem:
                                spell_core_main.OnHitStrikeItem(res.shoot_dir, res.physics_hit_pos, target, gun_id)
                            elif target.IsFragment:
                                target.OnHit(res.body.Entity)
                                continue
                        # 音效result
                        not hit_combat_avatar and sound_results.append(WrapperSoundResult(res.target, res.physics_hit_pos, res.materialTypeId))

                    # 击中特效result
                    hit_effect_dict = weapon.ammunition_hit_object_effect_dict
                    if not hit_effect_dict:
                        hit_effect_dict = weapon.skin_hit_object_effect_dict
                    hit_perform_id = hit_effect_dict.get(material_type, hit_effect_dict[0]) if hit_effect_dict else -1
                    self.WrapperSendHitEffectResult(spell_result, res.target, res.physics_hit_pos, res.hit_normal, material_type, shoot_dir, weapon_id, False, hit_perform_id)

                spell_result.sound_results = sound_results

                # 打包spellresult发送
                self.WrapperSendSpellResult(spell_result, True)
                # ClientSALog
                for result in spell_result.damage_result.values():
                    caster.RecordGunFireAmmo(weapon_id, hit_part=result['hit_part'])
                    caster.RecordGunFireDamage(weapon_id, result['damage'], weapon.part_slots)
                    break

            if penetrate_handler.power <= 0:
                # 穿透结束 子弹销毁
                break

            if use_tps_ballistic and cur_bullet_dist == 0:
                end_pos = start_pos_fps
            else:
                end_pos = start_pos + shoot_dir * cur_fly_dist
            cur_bullet_dist += cur_fly_dist

            yield yield_time - 0.001
            # 子弹下一次飞行
            caster = self.caster
            if not caster or caster.is_destroyed() or not caster.space:
                return
            if weapon.is_destroyed:
                return
            now_time = time.time()
            time_diff = now_time - cur_time
            start_pos = end_pos
            cur_fly_dist = time_diff * bullet_fly_speed
            if cur_bullet_dist + cur_fly_dist > bullet_fly_range:
                cur_fly_dist = bullet_fly_range - cur_bullet_dist + 2

            cur_time = now_time

    def BulletFlyForRobot(self, shoot_dir):
        caster = self.caster
        if not caster or caster.is_destroyed():
            return
        if not caster.IsRobotCombatAvatar:
            return
        position = caster.position
        start_pos = MType.Vector3(position[0], position[1] + 1.5, position[2])
        caster.CallServer("SimulateGunSpellStrike", start_pos.tuple(), shoot_dir.tuple(), False)
        return

    def SpellStop(self):
        self.caster.PlayFireReleaseAction()
        if self.caster is not genv.player:
            return
        weapon_case = self.caster.GetCurWeaponCase()
        if not weapon_case or not weapon_case.is_own_gun:
            return
        # 膛烟特效
        weapon_data_current = weapon_data.data.get(weapon_case.gun_id, {})
        chamber_sfx_id = weapon_data_current.get('overheat_muzzle_smoke_vfx_id', 16)
        # if self.caster.shoot_idx >= weapon_data_current.get('tip_smoke_trigger_bullet_num', 1):
        #     chamber_sfx_id = weapon_data_current.get('overheat_muzzle_smoke_vfx_id', 16)
        # else:
        #     chamber_sfx_id = 919
        self.caster.SpellStop(weapon_case)
        if chamber_sfx_id:
            # 播枪口过热特效
            muzzle_model = weapon_case.GetWeaponPartModel(consts.WeaponPartType_Muzzle)
            if not muzzle_model:
                # 有些枪默认没有枪口 那挂点在枪管
                muzzle_model = weapon_case.GetWeaponPartModel(consts.WeaponPartType_Barrel)

            # 先用 HP_flash 跟开火特效挂点一起
            effect_point = 'HP_flash'
            # muzzle_model and muzzle_model.PlayEffectById(chamber_sfx_id, -1, insure_play=True, bone=effect_point)
            self.caster.PlaySmokeEffect(muzzle_model, chamber_sfx_id, weapon_case, effect_point)

    def SpellStart(self):
        # 按下按键
        cur_weapon = self.caster.GetCurWeapon()
        if not cur_weapon:
            return
        self.graph_info = {
            'hand_graph': cur_weapon.equip_proto.get('hand_graph'),
            'tps_graph': cur_weapon.equip_proto.get('tps_graph'),
        }
        self.caster.PlayFireAction(self.spell_id, self.graph_info)
        if self.caster is not genv.player:
            return
        # 停止枪口烟雾
        self.caster.CancelSmokeTimer()
        weapon_case = self.caster.GetCurWeaponCase()
        self.caster.SpellStart(weapon_case)

    def WrapperSendSpellResult(self, spell_result, is_send):
        is_send and genv.spell_core.SendSpellResult(spell_result)

    def WrapperSendHitEffectResult(self, spell_result, target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, is_send, hit_perform_id):
        hit_effect_data = effect_util.WrapperHitEffectResult(target, physics_hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, 3, hit_perform_id)
        if not hit_effect_data:
            return
        if spell_result.hit_water:
            hit_effect_data['hit_perform_mode'] |= 1 << 1
        if hit_material_type == 11:  # 击中水了
            spell_result.hit_water = True
        spell_result.hit_effect.append(hit_effect_data)
        is_send and genv.spell_core.SendSpellResult(spell_result)

    def WrapperSendBallisticEffectResult(self, spell_result, physics_hit_pos, create_pos, distance, is_send):
        # 策划说喷子不需要弹线
        # ballistic_effect_data = {
        #     'hit_pos': formula.Tuple(physics_hit_pos),
        #     'create_pos': formula.Tuple(create_pos),
        #     'hit_dis': distance,
        # }
        # spell_result.ballistic_effect.append(ballistic_effect_data)
        # is_send and genv.spell_core.SendSpellResult(spell_result)
        return
