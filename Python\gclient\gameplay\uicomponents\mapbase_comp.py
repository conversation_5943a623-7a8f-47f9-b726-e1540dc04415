# -*- coding: utf-8 -*-
"""
小地图核心模块，管理位置、朝向、缩放、航线、毒圈等
https://km.netease.com/team/yinhe_studio/article/341834
"""
import time

import functools

import MEngine
import MUI
import MType
import cc
import ccui
import math

import switches
from gclient.config import LocalConfig
from gclient.gameplay.logic_base.spell import spell_util
from gclient.framework.ui.widgets.ui_splendor import UISplendorNode
from gclient.framework.util import events
from gclient.gameplay.uicomponents.map_tags import MapTagBase
from gclient.gameplay.uicomponents.map_tags import MapTextureTagScale
from gshare import formula, consts
from common.classutils import Components
from gclient.framework.ui.widgets.ui_texture import UITexture
from gclient.framework.ui.widgets import UIProgressBar
from gclient.framework.ui.widgets.ui_text import UIText
from gclient.framework.ui.widgets import UINode
from gclient import cconst
from gclient.gameplay.util import replay_util, gameplay_util
from gclient.data import minimap_data, map_tag_data, ui_icon_data
from gshare.utils import Swallower


class VehicleTeammateDotNode(UINode):
    def __init__(self, widget):
        super(VehicleTeammateDotNode, self).__init__(widget)
        self.init_height = self.getContentSize().height
        self.dot_node = self.seek('panel_1')
        self.dot_node_width = self.dot_node.getContentSize().width
        self.dot_node.img_dot = self.dot_node.seek('img_dot')
        self.dot_panels = [self.dot_node]

    def PushNode(self):
        dot_widget = self.dot_node.clone()
        self.pushBackCustomItem(dot_widget)
        dot_node = UINode(dot_widget)
        dot_node.img_dot = dot_node.seek('img_dot')
        self.dot_panels.append(dot_node)
        return dot_node

    def SetTeammateDots(self, teammate_ids):
        player = replay_util.GetPlayer()
        if not player:
            return
        length_teammate = len(teammate_ids)
        if length_teammate <= 0:
            self.visible = False
            return
        panel_length = len(self.dot_panels)
        self.visible = True
        for index, teammate_id in enumerate(teammate_ids):
            if index < panel_length:
                node = self.dot_panels[index]
            else:
                node = self.PushNode()
            node.visible = True
            color_index = player.GetTeammateColorIndex(teammate_id)
            node.img_dot.color = cconst.TEAMMATE_MARK_SLOT_2_COLOR[color_index]
        for index in list(range(length_teammate, len(self.dot_panels))):
            self.dot_panels[index].visible = False
        self.setContentSize(cc.Size(self.dot_node_width * length_teammate, self.init_height))


class TeamTag(MapTagBase):
    """
    队友标识Node
    """
    def __init__(self, node, info):
        super(TeamTag, self).__init__(node)
        self.owner = info['owner']
        self.num = info.get('num', 1)
        self.pid = info.get('pid', '')
        self.state = info.get('state')
        self.filename = info.get('filename')

        pos = info.get('pos')
        pos and node.setPosition(cc.Vec2(*pos))
        if info.get('image_child'):
            node.setScale(1.0 / self.owner.image.getScale())

        panel = node.getChildByName('panel_0')
        player_info_panel = panel.getChildByName('panel_player_info')
        self.player_info_panel = player_info_panel
        self.img_direction = player_info_panel.getChildByName('img_camera_direction')
        self.img_direction.setVisible(True if info.get('myself', False) else False)
        self.img_color = UITexture(player_info_panel.getChildByName('img_map_player_color'))
        self.img_state = UITexture(player_info_panel.getChildByName('img_down'))
        self.txt_num = UIText(player_info_panel.getChildByName('txt_num'))
        self.txt_num.visible = False
        self.SetNum(self.num)
        self.panel_scan = panel.getChildByName('panel_scan')
        self.panel_scan.setVisible(False)
        # 队友坐车上
        self.panel_car = panel.getChildByName('panel_car')
        self.panel_car.setVisible(False)
        self.img_player_on_vehicle = UITexture(self.panel_car.getChildByName('img_player'))
        self.img_direction_on_vehicle = self.panel_car.getChildByName('img_camera_direction')
        self.img_car = UITexture(self.panel_car.getChildByName('img_car'))
        self.listview_car_teammate_dot = VehicleTeammateDotNode(self.panel_car.getChildByName('list_dot'))
        # 技能指示范围
        self.panel_skill_range = self.img_color.getChildByName('panel_skill_range')
        self.panel_skill_range.setVisible(False)
        self.loading_skill_range = UIProgressBar(self.panel_skill_range.getChildByName('load_range'))

    def setRotation(self, rotation):
        # 设置方向image的旋转
        self.img_color.setRotation(rotation)
        self.img_direction.setRotation(rotation)

    def SetNum(self, num):
        self.img_color.texture = cconst.TEAMMATE_NUM_2_IMAGE.get(num, 10039)
        self.num = num

    def PlayUavAnimWithIndex(self, scale, start_index, end_index, callback=None):
        self.panel_scan.setScale(scale)
        self.GotoFrameAndPlay(start_index, end_index, callback=callback)

    def PlayUavAnim(self, scale, callback=None):
        self.panel_scan.setScale(scale)
        self.PlayAnim('loop', callback=callback)

    def ReCreateActionTimeline(self):
        if self.filename:
            self._cur_action = gui.LoadTimelineFromFile(self.filename)
            self.runAction(self._cur_action)

    def Refresh(self, info):
        self.img_direction.setVisible(True if info.get('myself', False) else False)
        self.SetNum(info.get('num', 1))
        self.pid = info.get('pid', '')
        self.setVisible(True)
        self.RefreshState()

    def GetImgColorPlayerTag(self):
        return self.img_color.widget

    def ShowSkillRange(self, loading_time=0):
        self.panel_skill_range.setVisible(True)
        if not loading_time:
            self.loading_skill_range.SetPercent(100)
        else:
            self.loading_skill_range.SetPercent(0)
            self.loading_skill_range.SetPercentGradually(100, loading_time)

    def HideSkillRange(self):
        self.panel_skill_range.setVisible(False)


@Components()
class MapBase(object):
    TEAM_TAG = TeamTag

    def __init__(self, ctrl, is_mini):
        self.owner = ctrl
        self.panel_cut = ctrl.panel_cut
        self.panel_cut.setClippingEnabled(True)
        self.panel_cut.setClippingType(ccui.LAYOUT_CLIPPINGTYPE_STENCIL)
        # self.panel_cut.setClippingType(ccui.LAYOUT_CLIPPINGTYPE_SCISSOR)
        self.is_mini = is_mini  # 小地图还是大地图
        cc.SpriteFrameCache.getInstance().addSpriteFramesWithFile('UIScript/lang_zh0_txt_ig_escape_map.plist')
        self.image = ctrl.image_map
        self.image.ignoreContentAdaptWithSize(True)
        # region ###########splendor node
        if switches.MINI_MAP_USE_SPLENDOR:
            if self.is_mini:
                self.splendor_image = UISplendorNode(parent_widget=self.panel_cut)
            else:
                self.splendor_image = UISplendorNode(parent_widget=self.image)
            self.splendor_image.CreateSplendorNodeNormal('UIDisturbImage', 0)
            self.splendor_image.UpdateFloatValue('cShineIntensity', 0)
        else:
            # for safe
            self.splendor_image = Swallower()
        # endregion ########splendor node
        if self.is_mini:
            self.panel_mask = ctrl.panel_mask
        self.InitData()

    def InitData(self):
        self.SetupMiniMapScaleGTick = None
        self.tick_teammates = []
        space = genv.space
        self.cur_spaceno = space.spaceno
        self.scale_nodes = []   # 缩放地图的时候，地图子节点不需要缩放的话放这里面(意思是放里面的话图标的像素会跟着地图变大而变大)
        panelcut_size = self.panel_cut.getContentSize()
        self.panelcut_size = panelcut_size
        self.panelcut_width = panelcut_size.width
        self.panelcut_height = panelcut_size.height
        # panel_cut的中心点位置，cocos坐标，待会要用来算相对位置，设置image的position
        self.center_pos_x = self.panelcut_width / 2.0
        self.center_pos_y = self.panelcut_height / 2.0
        self.panel_cut_radius = self.center_pos_x
        self.k = self.panelcut_height / self.panelcut_width  # 队友出了panel_cut之后，用来算位于panel_cut的边界位置
        self._cur_center_pix = None  # 缓存中心pix
        self.cur_center_rotate = None  # 缓存旋转角度
        self.center_pos = (self.center_pos_x, self.center_pos_y)

        proto = minimap_data.data.get(self.cur_spaceno, minimap_data.data.get(2))
        if not proto:
            return
        if switches.MINI_MAP_USE_SPLENDOR:
            self.SetBgTextureSplendor(proto)
        else:
            self.SetBgTexture(proto)
        if self.is_mini:
            self.image.setPosition(cc.Vec2(*self.center_pos))
            self._scale = 1.0

            minimap_scale_range = formula.Mul2D(cconst.ESetting_Slider_MiniMapScale_Range_Real, proto.get('init_scale', 1.0))
            s = formula.LinearMapNumber(LocalConfig.mini_map_scale, cconst.ESetting_Slider_MiniMapScale_Range, minimap_scale_range)
            self._min_scale = s
            self._max_scale = s
            self.scale_ratio = (self._max_scale / self._min_scale) ** (1.0 / (2 * cconst.BIG_MAP_MAX_ZOOM_COUNT - 1))  # 滑一下滚轮会响应2，所以*2
            self.change_contract_size = self.ZoomCountToScale(proto.get('change_icon_scale', 3) * 2) - 0.01
            self.scale = s
        else:
            self.image.setAnchorPoint(cc.Vec2(0.0, 0.0))
            self.image.setPosition(cc.Vec2(0.0, 0.0))
            self._min_scale = self.panelcut_width / self.map_width
            self._max_scale = proto.get('max_scale', 5.0)
            self._scale = 1.0
            self.scale_ratio = (self._max_scale / self._min_scale) ** (1.0 / (2 * cconst.BIG_MAP_MAX_ZOOM_COUNT - 1))  # 滑一下滚轮会响应2，所以*2
            self.change_contract_size = self.ZoomCountToScale(proto.get('change_icon_scale', 3) * 2) - 0.01
            self.scale = self._min_scale

        self.common_tags = {}  # {tag_type: tag} 注意：不包括center_tag和team_tags，其他的慢慢会全部整合进去
        self.common_tags_hidden_reasons = {}  # {tag_type: {hidden_reason, }}
        self.common_tags_in_region_hidden_reasons = {}  # {tag_type: {hidden_reason: region, }}
        self.SetBgText(proto)
        self.InitMatrixNum(proto)
        self.InitRatio()
        self._callComponents("init", None)
        self.center_tag = None
        self.team_tags = {}
        self.border_dict = {}
        self.mask_dict = {}
        # 队友标识
        self.InitTeamTag()
        # 自己标识
        self.InitSelfTag()
        # 主角标识两种模式，0：旋转地图 1：旋转指示标
        self.center_tickmode = cconst.MapCenterTickMode.ROTATE_TAG
        self.robot_tags = {}
        self.robot_teammates = None
        self.gm_supply_tags = {}

    def InitMatrixNum(self, proto):
        # 设置转换矩阵Pos--->Pix
        # [a b
        #  c d
        #  e f]
        self.matrix_a = proto['matrix_a']
        self.matrix_b = proto['matrix_b']
        self.matrix_c = proto['matrix_c']
        self.matrix_d = proto['matrix_d']
        self.matrix_e = proto['matrix_e']
        self.matrix_f = proto['matrix_f']
        # 逆转换 Pix--->Pos
        # [g h
        #  i j
        #  k l]
        self.matrix_g = proto['matrix_g']
        self.matrix_h = proto['matrix_h']
        self.matrix_j = proto['matrix_j']
        self.matrix_k = proto['matrix_k']
        self.matrix_i = proto['matrix_i']
        self.matrix_l = proto['matrix_l']

    def PreVisible(self, value):
        if value:
            self.StopTick()
            self.StartTick()
        else:
            self.StopTick()
        self._callComponents("show", value)

    def SetCenterNodeImgDir(self):
        if not self.is_mini:
            return
        player = replay_util.GetPlayer()
        if not player:
            return
        param = MUI.CocosWindowMiniMapCenterElementParam()
        param.imageNode = self.image
        param.splendorNode = self.splendor_image.widget if switches.MINI_MAP_USE_SPLENDOR else None
        param.centerNode = self.center_tag.widget
        param.matrixAce = MType.Vector3(self.matrix_a, self.matrix_c, self.matrix_e)
        param.matrixBdf = MType.Vector3(self.matrix_b, self.matrix_d, self.matrix_f)
        param.mapSize = MType.Vector2(self.map_width, self.map_height)
        param.cutSize = MType.Vector2(self.panelcut_width, self.panelcut_height)
        param.tickMode = cconst.MapCenterTickMode.ROTATE_TAG
        param.key = 'MiniMapCenterTick'
        is_car_visible = not not player.vehicle_id
        if not is_car_visible:
            param.centerNodeImgColor = self.center_tag.img_color.widget
            param.centerNodeImgDir = self.center_tag.img_direction
        else:
            param.centerNodeImgColor = self.center_tag.img_player_on_vehicle.widget
            param.centerNodeImgDir = self.center_tag.img_direction_on_vehicle
        MUI.AddCocosWindowMiniMapCenterElement(param)

    def StartTick(self):
        if self.is_mini:
            self.owner.AddStoryTick(self.CenterStoryTick)
            self.owner.AddStoryTick(self.TeamMemberStoryTick, 30)
        else:
            self.owner.AddStoryTick(self.CenterStoryTickBigMap)
            self.owner.AddStoryTick(self.TeamMemberStoryTickBigMap, 30)

    def StopTick(self):
        if self.is_mini:
            self.owner.RemoveStoryTick(self.CenterStoryTick)
            self.owner.RemoveStoryTick(self.TeamMemberStoryTick)
        else:
            self.owner.RemoveStoryTick(self.CenterStoryTickBigMap)
            self.owner.RemoveStoryTick(self.TeamMemberStoryTickBigMap)

    # region #################创建地图标识########################
    def CreateTagById(self, map_tag_id, wrapper=MapTagBase, wrapper_info=None, parent=None, scale=None, name=''):
        # map_tag_id: map_tag_data表的id
        # wrapper: map_tags.py里
        # wrapper_info: dict, 初始化MapTag额外参数
        # scale: 指定scale的话，就用
        if map_tag_id not in map_tag_data.data:
            return
        if not parent:
            parent = self.image
        parent_is_image = (parent is self.image)
        proto = map_tag_data.data[map_tag_id]
        tag_type = proto['type']
        icon_id = proto['icon']
        z_order = proto['zorder']
        if scale:
            _scale = scale
        else:
            _scale = proto.get('scale_mini', 1) if self.is_mini else proto.get('scale_big', 1)
        filename_default = proto.get('filename')
        filename_mini = proto.get('filename_mini')
        if self.is_mini:
            filename = filename_mini if filename_mini else filename_default
        else:
            filename = filename_default
        if not wrapper_info:
            wrapper_info = {}
        if filename:
            widget = gui.LoadWidgetFromFileCache(filename, parent_widget=parent, zorder=z_order)
            if parent_is_image:
                widget.setScale(_scale / self.scale)
        else:
            widget = self.CreateTag(gui.GetTextureById(icon_id), zorder=z_order, parent=parent, scale=_scale,
                                    name=name if name else proto.get('name', ''))
        wrapper_info['map_tag_proto'] = proto
        wrapper_info['parent_is_image'] = parent_is_image
        wrapper_info['filename'] = filename
        wrapper_info['map_base'] = self
        tag = wrapper(widget, wrapper_info)
        # map_tag_id不能作为key，因为同一个id，可能有很多个
        if tag_type not in self.common_tags:
            self.common_tags[tag_type] = []
        self.common_tags[tag_type].append(tag)
        if proto.get('scale_with_map', False) and parent is self.image:
            self.scale_nodes.append(widget)
        # 目前用得少，不知道要传什么参数  TODO
        self.PostCreateTagById(map_tag_id, tag_type, tag)
        return tag

    def PostCreateTagById(self, map_tag_id, tag_type, tag):
        pass

    def _PostTagHiddenReason(self, tag_type, tag):
        # 通用全图
        if tag_type in self.common_tags_hidden_reasons:
            for hidden_reason in self.common_tags_hidden_reasons[tag_type]:
                tag.AddHiddenReason(hidden_reason)
        # 一定范围
        if tag_type in self.common_tags_in_region_hidden_reasons:
            for hidden_reason, region in self.common_tags_in_region_hidden_reasons[tag_type].items():
                pass

    def RemoveTag(self, tag, remove_from_parent=True):
        # 对应上面的CreateTagById
        # remove_from_parent: 要不要removeFromParent，有些自带cache的就False
        if not tag:
            return
        tag_type = tag.map_tag_type
        if not tag_type:
            return
        if tag_type not in self.common_tags:
            return
        tags = self.common_tags[tag_type]
        if tag in tags:
            if tag.isvalid() and remove_from_parent:
                # for safe
                tag.setVisible(False)
                tag.removeFromParent()
            tags.remove(tag)
        if tag in self.scale_nodes:
            self.scale_nodes.remove(tag)

    def CreateTag(self, src_img, zorder=2, anchor=(0.5, 0.5), anchor_scale=(0.0, 0.0), parent=None, scale=1, name=''):
        itype = UINode.GetTextureType(src_img)
        ret = ccui.ImageView.create(src_img, itype)
        ret.ignoreContentAdaptWithSize(True)
        ret.setAnchorPoint(cc.Vec2(*anchor))
        ret.setPosition(cc.Vec2(0, 0))
        if parent is None:
            parent = self.image
        # 一定再包一层panel了，因为策划现在需要根据用户调节的小地图显示范围改变图标的大小。 21.9.29
        ret.setScale(scale)
        panel = ccui.Layout.create()
        panel.setAnchorPoint(cc.Vec2(*anchor_scale))
        panel.setCascadeOpacityEnabled(True)
        panel.addChild(ret)
        ret = panel

        parent.addChild(ret)
        ret.setLocalZOrder(zorder)  # z_order小于0时不可见，不知道为何!!!
        name and ret.setName(name)
        if parent is self.image:
            ret.setScale(1.0 / parent.getScale())   # 要保持标识大小不变，地图放大，标识就要对应缩小，所以要包一层panel（参数的scale用在标识图标的缩放，这里的setScale是用在Panel的缩放，不一样）
        return ret

    def CreateText(
            self, zorder=2, anchor=(0.5, 0.5), anchor_scale=(0.0, 0.0), parent=None, scale=1, fnt_size=15, name='',
            font_name='', outline_size=0, outline_color=(0, 0, 0, 255),
    ):
        ret = ccui.Text.create()
        ret.ignoreContentAdaptWithSize(True)
        ret.setAnchorPoint(cc.Vec2(*anchor))
        ret.setPosition(cc.Vec2(0, 0))
        if font_name:
            ret.setFontName('en_title')
        ret.setFontSize(fnt_size)
        if parent is None:
            parent = self.image
        if outline_size:
            ret.enableOutline(cc.Color4B(*outline_color), outline_size)
        # 一定再包一层panel了，因为策划现在需要根据用户调节的小地图显示范围改变图标的大小。 21.9.29
        ret.setScale(scale)
        panel = ccui.Layout.create()
        panel.setAnchorPoint(cc.Vec2(*anchor_scale))
        panel.setCascadeOpacityEnabled(True)
        panel.addChild(ret)
        ret = panel

        parent.addChild(ret)
        ret.setLocalZOrder(zorder)  # z_order小于0时不可见，不知道为何!!!
        name and ret.setName(name)
        if parent is self.image:
            ret.setScale(
                1.0 / parent.getScale())  # 要保持标识大小不变，地图放大，标识就要对应缩小，所以要包一层panel（参数的scale用在标识图标的缩放，这里的setScale是用在Panel的缩放，不一样）
        return ret

    def GetTagScaleByRadius(self, radius, ui_image_radius):
        # 用ui图片去铺满指定半径radius，计算ui图片应该要缩放的scale
        # 前提是用CreateTag/CreateTagById接口去创建
        scale = radius * self.ratio / ui_image_radius
        return scale * self.scale

    def AddTagHiddenReason(self, hidden_reason=cconst.HIDDEN_REASON_MAP_MARK):
        for tag_type in cconst.UI_HIDE_MAP_TYPES:
            self.AddTagHiddenReasonByTagType(tag_type, hidden_reason)

    def RemoveTagHiddenReason(self, hidden_reason=cconst.HIDDEN_REASON_MAP_MARK):
        for tag_type in cconst.UI_HIDE_MAP_TYPES:
            self.RemoveTagHiddenReasonByTagType(tag_type, hidden_reason)

    def AddTagHiddenReasonByTagType(self, tag_type, hidden_reason=cconst.HIDDEN_REASON_COMMON):
        self.common_tags_hidden_reasons.setdefault(tag_type, set()).add(hidden_reason)
        if tag_type not in self.common_tags:
            return
        for tag in self.common_tags[tag_type]:
            tag.AddHiddenReason(hidden_reason)

    def RemoveTagHiddenReasonByTagType(self, tag_type, hidden_reason=cconst.HIDDEN_REASON_COMMON):
        self.common_tags_hidden_reasons.setdefault(tag_type, set()).discard(hidden_reason)
        if tag_type not in self.common_tags:
            return
        for tag in self.common_tags[tag_type]:
            tag.RemoveHiddenReason(hidden_reason)

    def HaveTagHiddenReasonByTagType(self, tag_type, hidden_reason=cconst.HIDDEN_REASON_COMMON):
        if tag_type not in self.common_tags:
            return
        if tag_type not in self.common_tags_hidden_reasons:
            return
        return hidden_reason in self.common_tags_hidden_reasons[tag_type]

    def AddTagHiddenReasonByTagTypeInRegion(self, tag_type, region, hidden_reason=cconst.HIDDEN_REASON_COMMON):
        if not region:
            return
        self.common_tags_in_region_hidden_reasons.setdefault(tag_type, {})[hidden_reason] = region
        if tag_type not in self.common_tags:
            return
        region_center, region_radius = MType.Vector2(region[0], region[1]), region[-1]
        for tag in self.common_tags[tag_type]:
            tag_pos = tag.getPosition()
            tag_pos = MType.Vector2(tag_pos.x, tag_pos.y)
            if (tag_pos - region_center).length > region_radius:
                continue
            tag.AddHiddenReason(hidden_reason)

    def RemoveTagHiddenReasonByTagTypeInRegion(self, tag_type, region, hidden_reason=cconst.HIDDEN_REASON_COMMON):
        if tag_type in self.common_tags_in_region_hidden_reasons:
            in_region_hidden_reasons = self.common_tags_in_region_hidden_reasons[tag_type]
            if hidden_reason in in_region_hidden_reasons:
                in_region_hidden_reasons.pop(hidden_reason)

        if tag_type not in self.common_tags or not region:
            return
        region_center, region_radius = MType.Vector2(region[0], region[1]), region[-1]
        for tag in self.common_tags[tag_type]:
            tag_pos = tag.getPosition()
            tag_pos = MType.Vector2(tag_pos.x, tag_pos.y)
            if (tag_pos - region_center).length > region_radius:
                continue
            tag.RemoveHiddenReason(hidden_reason)
    # endregion #################创建地图标识########################

    # region 其他不知道怎么分类的 #################
    @property
    def game_logic(self):
        return genv.space.game_logic

    def MapTransfer(self, screen_pos):
        pix = self.image.convertToNodeSpace(screen_pos)
        return self.PixToPos(pix.x, pix.y)

    def SetupBigMapScaleAndPos(self, scale, pos):
        if scale:
            self.scale = scale
        if pos:
            self.SetImagePositionWithClamp(pos.x, pos.y)

    def SetupMiniMapScale(self, scale):
        if scale:
            self.scale = scale

    def SetupMiniMapScaleG(self, to_scale, duration):
        # duration时间内，scale渐变到to_scale
        if self.owner and to_scale:
            start_time = time.time()
            self.SetupMiniMapScaleGTick = functools.partial(self._SetupMiniMapScaleG, self.scale, to_scale, start_time, start_time + duration)
            self.owner.AddStoryTick(self.SetupMiniMapScaleGTick)

    def _SetupMiniMapScaleG(self, start_scale, to_scale, start_time, end_time, dtime):
        now = time.time()
        self.scale = formula.LinearMapNumber(now, [start_time, end_time], [start_scale, to_scale])
        if now >= end_time and self.SetupMiniMapScaleGTick:
            self.owner.RemoveStoryTick(self.SetupMiniMapScaleGTick)

    # endregion 其他不知道怎么分类的 #################

    # region 设置地图和地图地名 ###################
    def SetBgTextureSplendor(self, proto):
        width = proto['icon_width']
        height = proto['icon_height']
        map_size = cc.Size(width, height)
        if self.is_mini:
            self.splendor_image.SetupNodeParam(size=self.panelcut_size, anchor=cc.Vec2(0, 0), pos=cc.Vec2(0, 0))
        else:
            self.splendor_image.SetupNodeParam(size=map_size, anchor=cc.Vec2(0, 0), pos=cc.Vec2(0, 0))
            self.scale_nodes.append(self.splendor_image.widget)
        self.splendor_image.UpdateBoolValue('DiffuseTextureEnable', True)
        if 'map_vpath' in proto:
            map_vpath = proto['map_vpath']
        else:
            map_vpath = 'ui/%s' % ui_icon_data.data[proto['icon']]['ui_icon_path'].split('.')[0]
        self.splendor_image.UpdateTextureValue('DiffuseTexture', map_vpath)
        self.image.setContentSize(map_size)
        self.map_size = map_size
        self.map_width = float(width)
        self.map_height = float(height)

    def SetBgTexture(self, proto):
        image_obj = UITexture(self.image)
        icon_id = proto.get('icon')
        if icon_id:
            image_obj.texture = icon_id
        map_size = self.image.getContentSize()
        self.map_size = map_size
        self.map_width = map_size.width
        self.map_height = map_size.height

    def SetBgText(self, proto):
        # 小地图就不要创建地域名字了吧
        if self.is_mini and gpl.performance_level <= 1:
            return
        text_list = proto.get('map_text_list')
        if not text_list:
            return
        for map_tag_id in text_list:
            self.CreateTagById(map_tag_id)
# endregion 设置地图和地图地名 ###################

    # region ####### 缩放 ###########
    @property
    def scale(self):
        return self._scale

    @scale.setter
    def scale(self, val):
        old_scale = self._scale
        self._scale = val
        img = self.image
        img.setScale(val)
        for child in img.getChildren():
            if child in self.scale_nodes:
                continue
            child.setScale(1.0 / val)
        if not self.is_mini:
            # change_contract_size = gameplay_util.GetMapChangeContractSize()
            # change_contract_size = (self._max_scale - self._min_scale) * change_contract_size + self._min_scale
            change_contract_size = self.change_contract_size
            for target_scale in (change_contract_size, cconst.BIG_MAP_VEHICLE_SHOW_SCALE):
                if old_scale <= target_scale < val:
                    self.owner.OnMapScaleChange(False, target_scale, target_scale == change_contract_size)
                elif val < target_scale <= old_scale:
                    self.owner.OnMapScaleChange(True, target_scale, target_scale == change_contract_size)

    def PercentToScale(self, percent):
        return formula.ClampNumber(self._min_scale + (self._max_scale - self._min_scale) * percent, self._min_scale, self._max_scale)

    def ScaleToPercent(self, scale):
        scale = formula.ClampNumber(scale, self._min_scale, self._max_scale)
        return formula.ClampNumber((scale - self._min_scale) / (self._max_scale - self._min_scale), 0.0, 1.0)

    def ZoomCountToScale(self, count):
        return self._min_scale * self.scale_ratio ** count

    def SetSliderPercent(self, percent):
        self.SetScaleWithBase(self.PercentToScale(percent))

    def SetScaleWithBase(self, scale, base=None):
        # base: cocos坐标x y，如果base不为None，以base为中心放大地图
        # 如果base为None，暂定以center_player为中心缩放 TODO
        if base:
            pix = self.image.convertToNodeSpace(base)
            x = pix.x
            y = pix.y
        else:
            if self.IsCenterInPanelCut():
                x, y = self.cur_center_pix
            else:
                pix = self.image.convertToNodeSpace(self.panel_cut.convertToWorldSpaceAR(cc.Vec2(self.panelcut_width / 2.0, self.panelcut_height / 2.0)))
                x = pix.x
                y = pix.y
        scale = formula.ClampNumber(scale, self._min_scale, self._max_scale)
        pre_scale = self.scale
        self.scale = scale
        genv.messenger.Broadcast(events.ON_BIG_MAP_SCALE_CHANGE, scale)
        ratio = pre_scale - self.scale
        cur_pos = self.image.getPosition()
        self.SetImagePositionWithClamp(x * ratio + cur_pos.x, y * ratio + cur_pos.y)

    def IsCenterInPanelCut(self):
        if self.cur_center_pix:
            pix = self.panel_cut.convertToNodeSpace(self.image.convertToWorldSpaceAR(cc.Vec2(*self.cur_center_pix)))
            return 0 <= pix.x <= self.panelcut_width and 0 <= pix.y <= self.panelcut_height
        else:
            return False

    def GetAirDropShopBigMapInitScale(self, radius):
        # 1 pix------> 1 / ratio 米  (scale = 1.0)
        # 1 pix------> 1 / ratio * a 米  (scale = a)
        # width pix------> width / (ratio * a) 米
        # width / (ratio * a) = r
        # a = width / (r * ratio)
        # panel_cut显示多个20%米就是下面的公式了
        return self.panel_cut_radius / (1.2 * radius * self.ratio)
    # endregion ######## 缩放 ###########

    # region 平移 ###############
    def AdjustMapPositionWithDiffPos(self, diff_pos):
        cur_pos = self.image.getPosition()
        self.SetImagePositionWithClamp(cur_pos.x + diff_pos[0], cur_pos.y + diff_pos[-1])

    def SetImagePositionWithClamp(self, x, y):
        # 设置地图的位置，请用这个带clamp的函数！可以保证地图一定铺满panel_cut
        x = formula.ClampNumber(x, self.panelcut_width - self.map_width * self.scale, 0)
        y = formula.ClampNumber(y, self.panelcut_height - self.map_height * self.scale, 0)
        self.image.setPosition(cc.Vec2(x, y))
        genv.messenger.Broadcast(events.ON_BIG_MAP_POSITION_CHANGE, cc.Vec2(x, y))

    def SetSpecifyPosToCenterBigMap(self, x, z):
        # 设置指定位置到大地图中心
        # 这里好像写错了？  TODO
        # cur_center_pix = self.cur_center_pix
        # if cur_center_pix:
        #     begin = self.image.convertToWorldSpace(cc.Vec2(cur_center_pix[0], cur_center_pix[-1]))
        # else:
        #     begin = self.image.convertToWorldSpace(cc.Vec2(*self.PosToPix(x, z)))
        begin = self.image.convertToWorldSpace(cc.Vec2(*self.PosToPix(x, z)))
        end = self.panel_cut.convertToWorldSpace(cc.Vec2(self.center_pos_x, self.center_pos_y))
        end.subtract(begin)
        self.AdjustMapPositionWithDiffPos((end.x, end.y))
    # endregion 平移 ##############

    # region ########## 坐标转换 ################
    def PosToPix(self, posx, posz):
        return [posx * self.matrix_a + posz * self.matrix_c + self.matrix_e, posx * self.matrix_b + posz * self.matrix_d + self.matrix_f]

    def PixToPos(self, pixx, pixy):
        return [pixx * self.matrix_g + pixy * self.matrix_i + self.matrix_k, pixx * self.matrix_h + pixy * self.matrix_j + self.matrix_l]

    def YawToImageAngle(self, z_axis):
        posx = z_axis.x
        posy = z_axis.z
        pix = [posx * self.matrix_a + posy * self.matrix_c, posx * self.matrix_c + posy * self.matrix_d]
        dot = pix[-1]
        cross = pix[0]
        rad = math.atan2(cross, dot)
        self.cur_center_rotate = rad
        return rad * 180.0 / math.pi

    def YawToTagAngle(self, z_axis):
        posx = z_axis.x
        posy = z_axis.z
        # pix = [posx * self.matrix_a + posy * self.matrix_c, posx * self.matrix_c + posy * self.matrix_d]
        # dot = pix[-1]
        # cross = -pix[0]
        # rad = math.atan2(cross, dot)
        # 优化
        rad = math.atan2(-posx * self.matrix_a - posy * self.matrix_c, posx * self.matrix_c + posy * self.matrix_d)
        return rad * 180.0 / math.pi

    def YawToTagAngleTuple(self, vec):
        posx = vec[0]
        posy = vec[-1]
        # pix = [posx * self.matrix_a + posy * self.matrix_c, posx * self.matrix_c + posy * self.matrix_d]
        # dot = pix[-1]
        # cross = -pix[0]
        # 优化
        rad = math.atan2(-posx * self.matrix_a - posy * self.matrix_c, posx * self.matrix_c + posy * self.matrix_d)
        return rad * 180.0 / math.pi

    def RealYawToTagAngle(self, yaw):
        # 我们规定GUI出图是正方向指向北（正上方）的，这个函数用来算需要旋转多少度，cocos setRotation要加个-号
        vec = formula.YawToVector(yaw)
        posx = vec[0]
        posy = vec[-1]
        # pix = [posx * self.matrix_a + posy * self.matrix_c, posx * self.matrix_c + posy * self.matrix_d]
        # dot = pix[-1]
        # cross = -pix[0]
        # 优化
        rad = math.atan2(-posx * self.matrix_a - posy * self.matrix_c, posx * self.matrix_c + posy * self.matrix_d)
        return rad * 180.0 / math.pi

    def InitRatio(self):
        # 计算比例，在场景里面半径，当到pix里面，应该是多少倍；1米--->ratio个pix
        pix_0 = self.PosToPix(0, 0)
        pix_1 = self.PosToPix(0, 1)
        self.ratio = math.sqrt((pix_0[0] - pix_1[0]) ** 2 + (pix_0[1] - pix_1[1]) ** 2)

    def ConvertToPanelCutSpace(self, pix):
        # 把image坐标系下的pix转换成pancel_cut坐标系下的pix
        scale = self.scale
        cur_center_pix = self.cur_center_pix
        return (pix[0] - cur_center_pix[0]) * scale + self.center_pos_x, (pix[1] - cur_center_pix[1]) * scale + self.center_pos_y

    def ConvertToImageSpace(self, pix):
        # 上面的函数返回来解方程，没实际推导过。
        scale = self.scale
        cur_center_pix = self.cur_center_pix
        return (pix[0] - self.center_pos_x) / scale + cur_center_pix[0], (pix[1] - self.center_pos_y) / scale + cur_center_pix[1]

    def GetAvatarClientPositionAndYaw(self, avatar=None):
        # 取客户端Transform，如果是自己客户端取相机位置和朝向；如果是别人，取玩家位置和朝向
        if avatar:
            trans = avatar.model.model.Transform
            return trans.translation, trans.z_axis
        else:
            player = self.GetCenterTickEntity()
            if player:
                camera = MEngine.GetGameplay().Player.Camera
                trans = camera.Transform
                return player.model.model.Transform.translation, trans.z_axis * -1
            else:
                camera = MEngine.GetGameplay().Player.Camera
                trans = camera.Transform
                return trans.translation, trans.z_axis * -1

    # endregion ########### 坐标转换 ###############

    # region ########## 中心玩家的tick ##################
    def InitSelfTag(self):
        player = self.GetCenterTickEntity()
        if not player:
            self.owner and self.owner.add_timer(0.1, self.InitSelfTag)
            return

        if not player.combat_team:
            self.owner and self.owner.add_timer(0.1, self.InitSelfTag)
            return

        pid = player.id
        color_index = player.GetTeammateColorIndex(pid)
        if self.center_tag:
            self.center_tag.Refresh({'pid': pid, 'num': color_index, 'myself': True})
            return
        if self.is_mini:
            info = {'owner': self, 'image_child': False, 'pid': pid, 'num': color_index,
                    'myself': True}
            self.center_tag = self.CreateTagById(10000, wrapper=self.TEAM_TAG, wrapper_info=info,
                                                 parent=self.panel_mask.widget)
            self.center_tag.setAnchorPoint(cc.Vec2(0.5, 0.5))
            self.center_tag.setPosition(cc.Vec2(self.center_pos_x, self.center_pos_y))
        else:
            info = {'owner': self, 'image_child': True, 'pid': pid, 'num': color_index,
                    'myself': True}
            self.center_tag = self.CreateTagById(10000, wrapper=self.TEAM_TAG, wrapper_info=info, parent=self.image)

    def SetImageCenter(self, pix):
        self.image.setAnchorPoint(cc.Vec2(pix[0] / self.map_width, pix[-1] / self.map_height))

    def SetImageRotation(self, angle):
        self.image.setRotation(angle)

    @property
    def cur_center_pix(self):
        if switches.MINI_MAP_TAG_TICK and self.is_mini:
            image = self.image
            if image:
                points = image.getAnchorPointInPoints()
                return points.x, points.y
        else:
            return self._cur_center_pix

    @cur_center_pix.setter
    def cur_center_pix(self, value):
        self._cur_center_pix = value

    def CenterStoryTick(self, dtime):
        translation, z_axis = self.GetAvatarClientPositionAndYaw()
        pix = self.PosToPix(translation.x, translation.z)
        self.cur_center_pix = pix
        self.SetImageCenter(pix)
        if self.center_tickmode == cconst.MapCenterTickMode.ROTATE_MAP:
            angle = self.YawToImageAngle(z_axis)
            self.SetImageRotation(-angle)
            # self.center_tag.setPosition(cc.Vec2(*pix))
        else:
            angle = self.YawToTagAngle(z_axis)
            self.center_tag.setRotation(-angle)

            # scale = self.image.getScale()
            # size = self.image.getContentSize()
            # width = size.width
            # height = size.height
            # x_percent = self.center_pos_x / (width * scale)
            # y_percent = self.center_pos_y / (height * scale)
            # center_x = pix[0] / width
            # center_y = pix[1] / height
            # bl = cc.Vec2(center_x - x_percent, 1.0 - (center_y - y_percent))
            # br = cc.Vec2(center_x + x_percent, 1.0 - (center_y - y_percent))
            # tl = cc.Vec2(center_x - x_percent, 1.0 - (center_y + y_percent))
            # tr = cc.Vec2(center_x + x_percent, 1.0 - (center_y + y_percent))
            # self.splendor_image.UpdateTexCoord1(bl, br, tl, tr)

            bl = self.image.convertToNodeSpace(self.splendor_image.convertToWorldSpace(cc.Vec2(0, 0)))
            br = self.image.convertToNodeSpace(self.splendor_image.convertToWorldSpace(cc.Vec2(self.panelcut_width, 0)))
            tl = self.image.convertToNodeSpace(self.splendor_image.convertToWorldSpace(cc.Vec2(0, self.panelcut_height)))
            tr = self.image.convertToNodeSpace(self.splendor_image.convertToWorldSpace(cc.Vec2(self.panelcut_width, self.panelcut_height)))
            size = self.image.getContentSize()
            width = size.width
            height = size.height
            bl = cc.Vec2(bl.x / width, 1.0 - bl.y / height)
            br = cc.Vec2(br.x / width, 1.0 - br.y / height)
            tl = cc.Vec2(tl.x / width, 1.0 - tl.y / height)
            tr = cc.Vec2(tr.x / width, 1.0 - tr.y / height)
            self.splendor_image.UpdateTexCoord1(bl, br, tl, tr)

    def _GetCenterBigMapEntityYaw(self, entity):
        return entity.yaw

    def CenterStoryTickBigMap(self, dtime):
        entity = self.GetCenterTickEntity()
        if not entity:
            return
        # if not (entity and entity.model and entity.model.model):
        #   return
        player_position = entity.position
        player_yaw = self._GetCenterBigMapEntityYaw(entity)
        player_angle = self.RealYawToTagAngle(player_yaw)
        camera = MEngine.GetGameplay().Player.Camera
        camera_angle = self.YawToTagAngle(-1 * camera.Transform.z_axis)
        pix = self.PosToPix(player_position[0], player_position[-1])
        self.cur_center_pix = pix
        self.center_tag.setPosition(cc.Vec2(*pix))
        self.center_tag.img_color.setRotation(-player_angle)
        self.center_tag.img_direction.setRotation(-camera_angle)
        self.center_tag.img_direction_on_vehicle.setRotation(-camera_angle)
        self.center_tag.img_player_on_vehicle.setRotation(-player_angle)

    def GetCenterTickEntity(self):
        # 拿到中间标识的Entity, 观战的话要修改 Todo
        return replay_util.GetPlayer()

    # endregion ########## 中心玩家的tick ##################

    # region ########## 队友标识刷新 ##################
    def InitTeamTag(self):
        player = self.GetCenterTickEntity()
        if not player:
            self.owner and self.owner.add_timer(0.1, self.InitTeamTag)
            return
        # 把mask里面队伍的tag移回来
        # for slot in xrange(1, 5):
        #   tag = self.mask_dict.pop(slot, None)
        #   tag and self.MoveTagToImage(tag)
        # [TODO] 需要由引擎保证创建顺序
        if not player.combat_team:
            return player.add_timer(0.1, self.InitTeamTag)

        member_dict = player.teammate_info
        self.team_count = len(member_dict)
        # 多出来的team_tags要隐藏
        old_set = set(list(self.team_tags.keys()))
        real_player_id = replay_util.GetRealPlayerId()
        for pid, detail in member_dict.items():
            # player.id  观战时候------->被观战那个人的id(无论敌人观战和队友观战)----->不显示(上面InitSelfTag显示了)
            # player.id  正常情况下----->PlayerCombatAvatar的id---->不显示(上面InitSelfTag显示了)
            # 队友观战下，死之前的自己(genv.player)不显示（或者显示一个死亡的图标）
            if pid == player.id:
                continue
            if pid == real_player_id and real_player_id != player.id:
                continue
            if detail.is_leave:
                continue
            slot = detail['slot']
            color_index = player.GetTeammateColorIndex(pid)
            old_set.discard(slot)   # discard的是要显示的
            if slot in self.team_tags:
                tag = self.team_tags[slot]
                tag.Refresh({'num': color_index, 'pid': pid})
            else:
                if switches.MINI_MAP_TAG_TICK and self.is_mini:
                    parent = self.panel_mask.widget
                    image_child = False
                else:
                    parent = self.image
                    image_child = True
                info = {'owner': self, 'image_child': image_child, 'num': color_index, 'pid': pid}
                tag = self.CreateTagById(10001, self.TEAM_TAG, wrapper_info=info, parent=parent)
                self.team_tags[slot] = tag
                self.MoveTagToImage(tag)
            tag.setVisible(True)
        for slot in old_set:
            self.team_tags[slot].setVisible(False)

    def DebugCreateTeamTag(self, color_index, pid):
        parent = self.image
        image_child = True
        if switches.MINI_MAP_TAG_TICK and self.is_mini:
            parent = self.panel_mask.widget
            image_child = False
        info = {'owner': self, 'image_child': image_child, 'num': color_index, 'pid': pid}
        return self.CreateTagById(10001, self.TEAM_TAG, wrapper_info=info, parent=parent)
        
    def OnAvatarModelDestroy(self, avatar_id):
        if avatar_id in self.tick_teammates:
            self.tick_teammates.remove(avatar_id)
            MUI.RemoveCocosWindowElement('MiniMapTeammateTick%s' % avatar_id)

    def TeamMemberStoryTickNew(self, dtime):
        center_entity = self.GetCenterTickEntity()
        if not center_entity:
            return
        member_dict = center_entity.teammate_info
        combat_avatars = genv.space.combat_avatars
        for slot, tag in self.team_tags.items():
            if tag.pid in combat_avatars:
                # tick交给引擎
                avatar = combat_avatars[tag.pid]
                if not (avatar.model and avatar.model.model):
                    continue
                if tag.pid not in self.tick_teammates:
                    param = MUI.CocosWindowMiniMapTeammateElementParam()
                    param.imageNode = self.image
                    param.tagNode = tag.widget
                    param.tagNodeImgColor = tag.GetImgColorPlayerTag()
                    param.maskNode = self.panel_mask.widget
                    param.matrixAce = MType.Vector3(self.matrix_a, self.matrix_c, self.matrix_e)
                    param.matrixBdf = MType.Vector3(self.matrix_b, self.matrix_d, self.matrix_f)
                    param.maskNodeSize = MType.Vector2(self.panelcut_width, self.panelcut_height)
                    param.tickMode = cconst.MapCenterTickMode.ROTATE_TAG
                    param.key = 'MiniMapTeammateTick%s' % tag.pid
                    if MUI.AddCocosWindowMiniMapTeammateElement(param, avatar.model.model):
                        self.tick_teammates.append(tag.pid)
            else:
                if tag.pid in self.tick_teammates:
                    MUI.RemoveCocosWindowElement('MiniMapTeammateTick%s' % tag.pid)
                    self.tick_teammates.remove(tag.pid)
                if tag.pid not in member_dict:
                    continue
                member_info = member_dict[tag.pid]
                pos = member_info.position
                yaw = member_info['yaw']

                tag.setPosition(self.CalTeamMemberPosInPanelMask(self.PosToPix(pos[0], pos[-1])))
                tag.setRotation(-self.RealYawToTagAngle(yaw))

    def TeamMemberStoryTick(self, dtime):
        # 队友位置和朝向tick
        center_entity = self.GetCenterTickEntity()
        if not center_entity:
            return
        IsTeamMemberInside = self.IsTeamMemberInsideCircle
        CalTeamMemberOutsidePix = self.CalTeamMemberOutsidePixCircle
        member_dict = center_entity.teammate_info
        combat_avatars = genv.space.combat_avatars
        GetAvatarClientPositionAndYaw = self.GetAvatarClientPositionAndYaw
        for slot, tag in self.team_tags.items():
            yaw = None
            z_axis = None
            if tag.pid in combat_avatars:
                avatar = combat_avatars[tag.pid]
                translation, z_axis = GetAvatarClientPositionAndYaw(avatar)
                pos = (translation.x, translation.z)
            else:
                if tag.pid not in member_dict:
                    continue
                member_info = member_dict[tag.pid]
                pos = member_info.position
                yaw = member_info['yaw']

            pix = self.PosToPix(pos[0], pos[-1])
            if IsTeamMemberInside(pix):
                # if slot in self.mask_dict:
                #     self.MoveTagToImage(self.mask_dict[slot])
                #     self.mask_dict.pop(slot, None)
                angle = self.YawToTagAngle(z_axis) if z_axis else self.RealYawToTagAngle(yaw)
                tag.setPosition(cc.Vec2(*pix))
                tag.setRotation(-angle)
            else:
                # if slot not in self.mask_dict:
                #     self.mask_dict[slot] = tag
                tag.setPosition(CalTeamMemberOutsidePix(pix))
                angle = self.YawToTagAngle(z_axis) if z_axis else self.RealYawToTagAngle(yaw)
                tag.setRotation(-angle)

    def TeamMemberStoryTickBigMap(self, dtime):
        # 队友位置和朝向tick
        center_entity = self.GetCenterTickEntity()
        if not center_entity:
            return
        member_dict = center_entity.teammate_info
        combat_avatars = genv.space.combat_avatars
        GetAvatarClientPositionAndYaw = self.GetAvatarClientPositionAndYaw
        for tag in self.team_tags.values():
            yaw = None
            z_axis = None
            if tag.pid in combat_avatars:
                avatar = combat_avatars[tag.pid]
                translation, z_axis = GetAvatarClientPositionAndYaw(avatar)
                pos = (translation.x, translation.z)
            else:
                if tag.pid not in member_dict:
                    continue
                member_info = member_dict[tag.pid]
                pos = member_info.position
                yaw = member_info['yaw']

            pix = self.PosToPix(pos[0], pos[-1])
            angle = self.YawToTagAngle(z_axis) if z_axis else self.RealYawToTagAngle(yaw)
            tag.setPosition(cc.Vec2(*pix))
            tag.setRotation(-angle)

    def IsTeamMemberInside(self, pix, cur_center_pix=None):
        # 队友是否在panel_cut内
        if not cur_center_pix:
            cur_center_pix = self.cur_center_pix
        if not cur_center_pix:
            return True
        x, y = pix
        x -= cur_center_pix[0]
        y -= cur_center_pix[-1]
        cos_rad = math.cos(-self.cur_center_rotate)
        sin_rad = math.sin(-self.cur_center_rotate)
        new_x = x * cos_rad + y * sin_rad
        new_y = -x * sin_rad + y * cos_rad
        scale = self.scale
        center_pos_x = self.center_pos_x / scale
        center_pos_y = self.center_pos_y / scale
        return (-center_pos_x < new_x < center_pos_x) and (-center_pos_y < new_y < center_pos_y)

    def IsTeamMemberInsideWithoutRotate(self, pix, cur_center_pix=None):
        # 队友是否在panel_cut内，不需要管旋转
        if not cur_center_pix:
            cur_center_pix = self.cur_center_pix
        if not cur_center_pix:
            return True
        x, y = pix
        x -= cur_center_pix[0]
        y -= cur_center_pix[-1]
        scale = self.scale
        center_pos_x = self.center_pos_x / scale
        center_pos_y = self.center_pos_y / scale
        return (-center_pos_x < x < center_pos_x) and (-center_pos_y < y < center_pos_y)

    def CalTeamMemberOutsidePix(self, pix):
        # 如果队友在panel_cut外，要计算center_entity标识和队友标识连线与panel_cut的交点
        # 1. 拉回原点，旋转
        x, y = pix
        x -= self.cur_center_pix[0]
        y -= self.cur_center_pix[-1]
        cos_rad = math.cos(-self.cur_center_rotate)
        sin_rad = math.sin(-self.cur_center_rotate)
        new_x = x * cos_rad + y * sin_rad
        new_y = -x * sin_rad + y * cos_rad
        # 2. 计算连线斜率k, 连线的方程为y = kx
        k = new_y / new_x
        # 3. 根据斜率k判断应该与哪根线求交点
        positive_k = self.k
        negative_k = -positive_k
        scale = self.scale
        if negative_k <= k <= positive_k:
            center_pos_x = self.center_pos_x / scale
            res_x = center_pos_x if new_x > 0 else -center_pos_x
            res_y = k * res_x
        else:
            center_pos_y = self.center_pos_y / scale
            res_y = center_pos_y if new_y > 0 else -center_pos_y
            res_x = res_y / k
        # 4. 一次旋转逆变换，拉回去
        # cos_rad = math.cos(self.cur_center_rotate)
        # sin_rad = math.sin(self.cur_center_rotate)
        sin_rad = -sin_rad  # 优化，不用多算
        return (res_x * cos_rad + res_y * sin_rad + self.cur_center_pix[0], -res_x * sin_rad + res_y * cos_rad + self.cur_center_pix[-1])

    def CalTeamMemberOutsidePixWithoutRotate(self, pix):
        # 如果队友在panel_cut外，要计算center_entity标识和队友标识连线与panel_cut的交点，不需要管旋转
        # 1. 拉回原点，旋转
        cur_center_pix = self.cur_center_pix
        x, y = pix
        new_x = x - cur_center_pix[0]
        new_y = y - cur_center_pix[-1]
        scale = self.scale
        # 2. 计算连线斜率k, 连线的方程为y = kx
        if new_x == 0:
            center_pos_y = self.center_pos_y / scale
            res_y = center_pos_y if new_y > 0 else -center_pos_y
            res_x = 0
            return res_x + cur_center_pix[0], res_y + cur_center_pix[-1]
        k = new_y / new_x
        # 3. 根据斜率k判断应该与哪根线求交点
        positive_k = self.k
        negative_k = -positive_k
        if negative_k <= k <= positive_k:
            center_pos_x = self.center_pos_x / scale
            res_x = center_pos_x if new_x > 0 else -center_pos_x
            res_y = k * res_x
        else:
            center_pos_y = self.center_pos_y / scale
            res_y = center_pos_y if new_y > 0 else -center_pos_y
            res_x = res_y / k
        return res_x + cur_center_pix[0], res_y + cur_center_pix[-1]

    def IsTeamMemberInsideCircle(self, pix, cur_center_pix=None):
        # 队友是否在panel_cut内，蒙版是圆形，先不考虑地图的放大 Todo
        return formula.InRange2D(pix, self.cur_center_pix, (self.panel_cut_radius - 10) / self.scale)

    def CalTeamMemberOutsidePixCircle(self, pix):
        # 如果队友在panel_cut外，要计算center_entity标识和队友标识连线与panel_cut的交点
        x, y = pix
        # 1. 拉回原点
        x -= self.cur_center_pix[0]
        y -= self.cur_center_pix[1]
        # 2. 求直线y = kx 与 圆的交点
        k = y / x
        res_x = (self.panel_cut_radius - 10) / self.scale / math.sqrt(1 + k * k) if x >= 0 else\
            -(self.panel_cut_radius - 10) / self.scale / math.sqrt(1 + k * k)
        res_y = k * res_x
        # 3. 搬回去
        return cc.Vec2(res_x + self.cur_center_pix[0], res_y + self.cur_center_pix[1])

    def CalTeamMemberPosInPanelMask(self, pix):
        # pix: image上的像素位置;
        # 计算新位置，考虑卡边
        # return value: mask上的新位置
        # return type: cc.Vec2
        pix_mask = self.panel_mask.widget.convertToNodeSpace(self.image.convertToWorldSpace(cc.Vec2(*pix)))
        pix_mask_x = pix_mask.x
        pix_mask_y = pix_mask.y
        res_x = pix_mask_x - self.center_pos_x
        res_y = pix_mask_y - self.center_pos_y
        if -self.center_pos_x < res_x < self.center_pos_x and -self.center_pos_y < res_y < self.center_pos_y:
            return pix_mask
        if res_x == 0:
            return cc.Vec2(self.center_pos_x, 2 * self.center_pos_y if res_y > 0 else 0)
        else:
            k = res_y / res_x
            positive_k = self.k
            negative_k = -positive_k
            if negative_k <= k <= positive_k:
                res_x = self.center_pos_x if res_x > 0 else -self.center_pos_x
                res_y = k * res_x
            else:
                res_y = self.center_pos_y if res_y > 0 else -self.center_pos_y
                res_x = res_y / k
            return cc.Vec2(res_x + self.center_pos_x, res_y + self.center_pos_y)

    # endregion ########## 队友标识刷新 ##################

    # region ########### 标识卡边处理 ###################
    def AddBorderProcessForTag(self, mark_id, tag, origin_pix=None):
        # tag可以是img或者node，只要实现好对应用到的方法即可
        need_add_tick = False if self.border_dict else True
        if origin_pix:
            pix = origin_pix
        else:
            pos = tag.getPosition()
            pix = (pos.x, pos.y)
        self.border_dict[mark_id] = (tag, pix)
        if need_add_tick and self.owner:
            self.owner.RemoveStoryTick(self.BorderTagTick)
            frames = 5 if gpl.performance_level <= 1 else 10
            self.owner.AddStoryTick(self.BorderTagTick, frames)

    def RemoveBorderProcessForTag(self, mark_id, tag):
        info = self.border_dict.pop(mark_id, None)
        if not self.border_dict:
            self.owner and self.owner.RemoveStoryTick(self.BorderTagTick)
        # 删除的时候，如果在mask_dict里面的，就干掉
        mask_tag = self.mask_dict.pop(mark_id, None)
        if mask_tag:
            self.MoveTagToImage(mask_tag)
            if info:
                mask_tag.setPosition(cc.Vec2(*info[1]))

    def BorderTagTick(self, dtime):
        cur_center_pix = self.cur_center_pix
        IsTeamMemberInside = self.IsTeamMemberInsideCircle
        CalTeamMemberOutsidePix = self.CalTeamMemberOutsidePixCircle
        for tag_id, (tag, origin_pix) in self.border_dict.items():
            if tag.parent_is_image:
                # tag的parent是self.image的情况
                if IsTeamMemberInside(origin_pix, cur_center_pix):
                    if tag_id in self.mask_dict:
                        self.MoveTagToImage(self.mask_dict[tag_id])
                        self.mask_dict.pop(tag_id, None)
                    tag.setPosition(cc.Vec2(*origin_pix))
                else:
                    if tag_id not in self.mask_dict:
                        self.mask_dict[tag_id] = tag
                    tag.setPosition(CalTeamMemberOutsidePix(origin_pix))
                    if tag.border_dis and tag.pos_origin:
                        player = replay_util.GetPlayer()
                        if player and formula.InRange3D(tag.pos_origin, player.position, tag.border_dis):
                            tag.setVisible(True)
                        else:
                            tag.setVisible(False)
            else:
                # tag的parent是panel_mask
                tag.setPosition(CalTeamMemberOutsidePix(origin_pix))

    def MoveTagToPanelMask(self, tag):
        tag.retain()
        tag.removeFromParent()
        self.panel_mask.widget.addChild(tag.widget)
        # 因为panel_mask的scale是1，要保证包住tag image的那个parenat panel的scale * panel_mask_scale = 1.0
        tag.setScale(1.0)
        tag.release()
        # tag的动画一定会失效的，要重置自己逻辑绕一下，参考UAV
        if tag.show_only_in_border:
            tag.setVisible(True)

    def MoveTagToImage(self, tag):
        tag.retain()
        tag.removeFromParent()
        self.image.addChild(tag.widget)
        # 要保证包住tag image的那个parent panel的scale * self.image_scale = 1.0
        tag.setScale(1.0 / self.scale)
        tag.release()
        create_timeline = getattr(tag, 'ReCreateActionTimeline', None)
        create_timeline and create_timeline()
        if tag.show_only_in_border:
            tag.setVisible(False)
        elif tag.border_dis:
            # for safe
            tag.setVisible(True)

    # endregion ########### 标识卡边处理 ###################

    # region #############机器人
    def CreateOrRefreshRobotTag(self, robot_info):
        line_color = cc.Color4B(0, 255, 0, 128)
        city_list = robot_info.pop("city_list", None)
        city_list and self.DrawRobotCityList(city_list)
        for robot_id, info in robot_info.items():
            pos = info['position']
            pix_pos = cc.Vec2(*self.PosToPix(pos[0], pos[-1]))
            faction = info['faction']
            if robot_id in self.robot_tags:
                tag, line, text = self.robot_tags[robot_id]
            else:
                tag = self.CreateTagById(20, wrapper=MapTextureTagScale)
                line = cc.DrawNode.create()
                self.image.addChild(line)
                self.scale_nodes.append(line)
                text = self.DrawDebugString({'size': 10, 'position': self.PosToPix(pos[0], pos[-1]), 'color': cc.Color4B(255, 255, 255, 255), 'string': str(faction % 2000)})
                self.robot_tags[robot_id] = (tag, line, text)
            ai_source_type = info.get('ai_source_type')
            # t_task_pos = info.get('t_task_pos', None)
            tag.setPosition(pix_pos)
            text.setPosition(pix_pos)
            tag.setVisible(True)
            line.setVisible(False)
            # print(info)
            if self.robot_teammates and robot_id in self.robot_teammates:
                color = (0x8f, 0xef, 0xef)
            elif ai_source_type == consts.AISourceType.AISourceWander:
                color = (0x00, 0xff, 0x00)  # 绿
            elif ai_source_type == consts.AISourceType.AISourceBind:
                color = (0x00, 0x00, 0xff)  # 蓝
            else:
                color = (0xff, 0x00, 0x00)  # 红
            tag.color = color
            if 'jump_info' in info:
                line.setVisible(True)
                p = info['jump_info']
                if p:
                    line.clear()
                    area_center = cc.Vec2(*self.PosToPix(p['area_center'][0], p['area_center'][-1]))
                    target_pos = cc.Vec2(*self.PosToPix(p['target_pos'][0], p['target_pos'][-1]))
                    line.drawLine(pix_pos, area_center, 4.5, line_color)
                    line.drawLine(pix_pos, target_pos, 4.5, line_color)
            # elif t_task_pos:
            #     line.setVisible(True)
            #     line.clear()
            #     task_pos = cc.Vec2(*self.PosToPix(t_task_pos[0], t_task_pos[-1]))
            #     line.drawLine(pix_pos, task_pos, 4.5, line_color)
            else:
                line.setVisible(False)

        need_visible_set = set(list(robot_info.keys()))
        all_set = set(list(self.robot_tags.keys()))
        for robot_id in all_set - need_visible_set:
            x, y, z = self.robot_tags[robot_id]
            x.setVisible(False)
            y.setVisible(False)
            z.setVisible(False)
        # 如果没有画城区就把城区也画出来
        if not getattr(self, 'helen_city_debug_flag', False):
            self.helen_city_debug_flag = True
            from gclient.data.tactical_data import tactical_area_base
            space = genv.space
            city_list = []
            for city_id, city_info in tactical_area_base.data.get(
                    consts.HOST_SPACE_MAP.get(space.world_name, space.world_name), {}).items():
                city_list.append(city_info)
            genv.avatar.on_debug_indoor_city_2d(city_list, [])

    def DrawRobotCityList(self, city_list):
        if getattr(self, 'robot_city_list', None):
            return
        color = cc.Color4B(255, 0, 0, 255)
        # color = (0xff, 0x00, 0x00)  # 红
        r = {}
        for area_id, pos, radius in city_list:
            dn = cc.DrawNode.create()
            self.image.addChild(dn)
            self.scale_nodes.append(dn)
            dn.drawCircle(cc.Vec2(*self.PosToPix(pos[0], pos[-1])), radius * 2, 3.0, color, 0.0)
            r[area_id] = dn
        self.robot_city_list = r
    # endregion ###########机器人

    # region #############GM显示物资
    def GMShowSupplyTag(self, transforms):
        need_visible = dict()
        for color, data_dict in transforms.items():
            for eid, info in data_dict.items():
                need_visible[eid] = 1
                if eid in self.gm_supply_tags:
                    tag = self.gm_supply_tags[eid]
                else:
                    tag = self.CreateTagById(63, wrapper=MapTextureTagScale)
                    self.gm_supply_tags[eid] = tag
                pos = info['position']
                pix_pos = self.PosToPix(pos[0], pos[-1])
                tag.setPosition(cc.Vec2(*pix_pos))
                tag.setVisible(True)
                tag.color = color

        need_visible_set = set(list(need_visible.keys()))
        all_set = set(list(self.gm_supply_tags.keys()))
        for id in all_set - need_visible_set:
            self.gm_supply_tags[id].setVisible(False)

    def DrawDebugString(self, pos_dict, is_show=True):
        if not is_show:
            return
        t = ccui.Text.create()
        t.setAnchorPoint(cc.Vec2(0.5, 0.5))
        t.setContentSize(cc.Size(300, 50))
        t.setFontName('hkxzyw7.TTC')
        t.setFontSize(10)
        self.image.addChild(t)
        self.scale_nodes.append(t)
        # self.draw_debug_string_list.append(t)
        pos = pos_dict['position']
        color = pos_dict.get('color', cc.Color4B(255, 255, 255, 255))
        info = pos_dict.get('string', 'None')
        size = pos_dict.get('size', 50)
        t.setString(str(info))
        t.setFontSize(size)
        t.setPosition(cc.Vec2(pos[0], pos[-1]))
        t.setTextColor(color)
        return t

    # endregion ###########机器人

    # region 技能相关
    def OnInfoChaos(self, is_chaos):
        return
        # if is_chaos:
        #     self.splendor_image.UpdateFloatValue('cShineIntensity', 0.1)
        #     self.splendor_image.UpdateFloatValue('cBlockSizeLayer2', 6.0)
        #     self.splendor_image.UpdateFloatValue('cShineSpeed', 5.0)
        #     self.splendor_image.UpdateFloatValue('cBlockSizeLayer1', 20.0)
        #     self.AddTagHiddenReasonByTagType(cconst.MapTagType.MapText)
        # else:
        #     self.splendor_image.UpdateFloatValue('cShineIntensity', 0)
        #     self.RemoveTagHiddenReasonByTagType(cconst.MapTagType.MapText)

    def OnSpellShowingRange(self, spell_id, is_show):
        if is_show:
            if spell_id == 234:
                spell_proto = spell_util.GetSpellProto(spell_id)
                shock_proto = spell_proto.get('shock', {})
                loading_time = shock_proto.get('chargetime', [3, ])[0]
            else:
                loading_time = 0
            self.center_tag.ShowSkillRange(loading_time)
        else:
            self.center_tag.HideSkillRange()
    # endregion


class MapSkillRangeIndicatorComp(object):
    @events.ListenTo(events.ON_SKILL_RANGE_SHOW)
    def OnMapSkillRangeShow(self, spell_id, is_show):
        print('OnMapSkillRangeShow ', spell_id, is_show)
        if self.map_mgr:
            self.map_mgr.OnSpellShowingRange(spell_id, is_show)
