# -*- coding: utf-8 -*-
"""
    小地图uav组件
"""
import six2
import functools
import time, zlib
import six2.moves.cPickle as cPickle

import cc
from gclient.data import spell_data
from gclient.gameplay.uicomponents.map_tags import UavEnemyNode, UavFixPosNode
from gclient.gameplay.util import replay_util
from gclient import cconst


# 扫描动效的最大半宽
from gshare import formula, time_util
from gshare.icombat_attr import AttrType

UAV_PANEL_R = 120 * 1.7
DELAY_HIDE_TIME = 0.2


class UavComp(object):
    
    def __init_component__(self, _):
        self.uav_enemy_tags = {}
        self.full_uav_enemy_tags = {}
        self.hide_timer = None
        self.hide_full_uav_timer = None
    
    def __show_component__(self, value):
        # 根据proprety teamateinfo 恢复
        if self.is_mini or not value:
            return
        self.InitUavShow()
        self.InitUavShowFull()

    def InitUavShow(self):
        player = replay_util.GetPlayer()
        if not player:
            return
        uav_data = player.uav_data
        if not uav_data:
            return
        uav_data = cPickle.loads(zlib.decompress(uav_data))
        level = uav_data.get('level', 0)
        if level == 0:
            return
        avatar_infos = uav_data.get('avatars', {})
        caster_info = (player.id, player.position)
        spell_id = uav_data.get('spell_id')
        fix_pos = uav_data['fix_pos'] if len(uav_data.get('fix_pos', [])) == 3 else None
        radius = uav_data.get('radius', 125)
        start_time = player.uav_start_time
        if not start_time:
            return
        self.ShowUav(caster_info, avatar_infos, level, spell_id, fix_pos, radius, start_time=start_time)

    def InitUavShowFull(self):
        player = replay_util.GetPlayer()
        uav_data = player.uav_data_full
        if not uav_data:
            # 停止扫描
            self.HideUavEnemyTagFull()
            return
        uav_data = cPickle.loads(zlib.decompress(uav_data))
        level = uav_data.get('level', 0)
        if level == 0:
            # 停止扫描，直level == 0 代表停止。
            self.HideUavEnemyTagFull()
            return
        avatar_infos = uav_data.get('avatars', {})
        caster_info = (player.id, player.position)
        spell_id = uav_data.get('spell_id')
        fix_pos = uav_data.get('fix_pos')
        start_time = player.uav_start_time
        refresh_ui = uav_data.get('refresh_ui', False)
        if not start_time:
            return
        self.ShowUavFull(caster_info, avatar_infos, level, spell_id, fix_pos, refresh_ui, start_time)

    def HideUavTagByAvatarId(self, avatar_id):
        tag = self.uav_enemy_tags.pop(avatar_id, None)
        if not tag:
            return
        self.is_mini and self.RemoveBorderProcessForTag(avatar_id, tag)
        tag.setVisible(False)
        self.RemoveTag(tag)

    def HideFallUavTagByAvatarId(self, avatar_id):
        tag = self.full_uav_enemy_tags.pop(avatar_id, None)
        if not tag:
            return
        self.is_mini and self.RemoveBorderProcessForTag(avatar_id, tag)
        tag.setVisible(False)
        self.RemoveTag(tag)

    def HideAllUavTag(self):
        for avatar_id, tag in self.uav_enemy_tags.items():
            self.is_mini and self.RemoveBorderProcessForTag(avatar_id, tag)
            tag.setVisible(False)
            self.RemoveTag(tag)
        self.uav_enemy_tags = {}

    def HideUavEnemyTagFull(self):
        # 停止全图UAV，隐藏逻辑
        for tag in self.full_uav_enemy_tags.values():
            tag.setVisible(False)
            self.RemoveTag(tag)
        self.full_uav_enemy_tags = {}

    def ShowUavFull(self, caster_info, avatar_infos, level, spell_id, fix_pos, refresh_circle, start_time=None):
        # 全图UAV 不间断（1s）更新位置朝向；扫描圈间隔更新，保持原来逻辑
        print('==========ShowUavFull', caster_info, avatar_infos, level, spell_id, fix_pos, refresh_circle, start_time)
        owner = self.owner
        if not owner:
            return

        # region ########## 敌人位置
        # 现在有部分移出逻辑，所以要先删掉不显示的敌人位置图标
        remove_list = []
        for avatar_id in self.full_uav_enemy_tags.keys():
            if avatar_id not in avatar_infos:
                remove_list.append(avatar_id)
        for avatar_id in remove_list:
            tag = self.full_uav_enemy_tags.pop(avatar_id, None)
            tag.setVisible(False)
            self.RemoveTag(tag)

        # 全图UAV生效的时候，普通UAV直接失效，服务端延长全图UAV的时间
        self.HideAllUavTag()

        spell_proto = spell_data.data.get(spell_id, {}).get(level, {})
        fall_uav_proto = spell_proto.get('full_uav', {})
        # 全图uav不显示距离远的人（针对小地图，目的是不想太多卡边）
        limit_dis = fall_uav_proto.get('limit_dis')
        need_limit_dis = self.is_mini and limit_dis
        player_pos = replay_util.GetPlayer().position
        in_range_2d = formula.InRange2D

        for avatar_id, pos_yaw in avatar_infos.items():
            self.HideEnemyMarkTagsByAvatarId(avatar_id)
            pos = pos_yaw[:3]
            if need_limit_dis and not in_range_2d(pos, player_pos, need_limit_dis):
                continue
            yaw = pos_yaw[-1]
            if avatar_id in self.full_uav_enemy_tags:
                tag = self.full_uav_enemy_tags[avatar_id]
            else:
                parent = self.panel_mask.widget if self.is_mini else self.image
                tag = self.CreateTagById(46, wrapper=UavEnemyNode, parent=parent)
                self.full_uav_enemy_tags[avatar_id] = tag
            pix = self.PosToPix(pos[0], pos[-1])
            if self.is_mini:
                tag.SetAvatarPosGradually(self.CalTeamMemberPosInPanelMask(pix), fall_uav_proto.get('interval', 0.5))
            else:
                tag.SetAvatarPosGradually(cc.Vec2(*pix), fall_uav_proto.get('interval', 0.5))
            tag.SetAvatarYawGradually(yaw, self.RealYawToTagAngle, fall_uav_proto.get('interval', 0.5))
        # endregion ####### 敌人位置

        if not refresh_circle or not fix_pos:
            return

        # region ########## 扩散的圈
        uav_range = 4000  # 全图覆盖
        caster_id, caster_pos = caster_info
        teammate_info = replay_util.GetTeammateInfo()
        if caster_id not in teammate_info:
            print('ShowUav fail reason: caster_id not in teammate_info')
            return
        # parent是image
        tag = self.CreateTagById(29, wrapper=UavFixPosNode)
        tag.setPosition(cc.Vec2(*self.PosToPix(fix_pos[0], fix_pos[-1])))
        if not tag:
            print('ShowUav fail reason: not tag')
            return
        # panel_scan应该放大的倍数，self.scale = 1的情况下
        scale = uav_range * self.ratio / UAV_PANEL_R
        now = time.time()
        if start_time:
            total_frame = tag.GetTotalFrame('loop')
            if total_frame:
                anim_time = total_frame / 30.0
            else:
                anim_time = 55 / 30.0
            end_time = start_time + anim_time
            if start_time < now < end_time:
                _start_index = tag.GetStartFrame('loop') or 5
                _end_index = tag.GetEndFrame('loop') or 60
                start_index = formula.LinearMapNumber(now, (start_time, end_time), (_start_index, _end_index))
                tag.PlayUavAnimWithIndex(scale, int(start_index), _end_index)
        else:
            tag.PlayUavAnim(scale)
        # endregion ########## 扩散的圈

    def ShowUav(self, caster_info, avatar_infos, level, spell_id, fix_pos, uav_range, start_time=None):
        print('================ShowUav', avatar_infos, spell_id, fix_pos, uav_range, start_time)
        owner = self.owner
        if not owner:
            return

        if self.hide_timer:
            owner.cancel_timer(self.hide_timer)
            self.hide_timer = None
        self.HideAllUavTag()

        spell_proto = spell_data.data.get(19 if not spell_id else spell_id, {}).get(level, {})
        uav_proto = spell_proto.get('uav', {})
        if not uav_proto:
            print('ShowUav fail reason: not uav_proto')
            return
        uav_reveal_time = uav_proto.get('reveal_time', 2.2)
        enemy_fadeout_time = uav_proto.get('fadeout_time', 0.5)

        caster_id, caster_pos = caster_info
        teammate_info = replay_util.GetTeammateInfo()
        if caster_id not in teammate_info:
            print('ShowUav fail reason: caster_id not in teammate_info')
            return
        if not fix_pos:
            fix_pos = teammate_info[caster_id]['position']
        # parent是image
        tag = self.CreateTagById(29, wrapper=UavFixPosNode)
        tag.setPosition(cc.Vec2(*self.PosToPix(fix_pos[0], fix_pos[-1])))
        if not tag:
            print('ShowUav fail reason: not tag')
            return
        # panel_scan应该放大的倍数，self.scale = 1的情况下
        # scale = uav_range * self.ratio / UAV_PANEL_R
        scale = self.GetTagScaleByRadius(uav_range, UAV_PANEL_R)
        now = time_util.GetTimeNow()
        if start_time:
            total_frame = tag.GetTotalFrame('loop')
            if total_frame:
                anim_time = total_frame / 30.0
            else:
                anim_time = 55 / 30.0
            end_time = start_time + anim_time
            if start_time < now < end_time:
                _start_index = tag.GetStartFrame('loop') or 5
                _end_index = tag.GetEndFrame('loop') or 60
                start_index = formula.LinearMapNumber(now, (start_time, end_time), (_start_index, _end_index))
                tag.PlayUavAnimWithIndex(scale, int(start_index), _end_index)
        else:
            tag.PlayUavAnim(scale)
        is_mini = self.is_mini

        # uav不显示距离远的人（针对小地图，目的是不想太多卡边）
        limit_dis = uav_proto.get('limit_dis')
        need_limit_dis = is_mini and limit_dis
        in_range_2d = formula.InRange2D

        reveal_time = uav_reveal_time
        fadeout_time = enemy_fadeout_time
        if start_time:
            time_pass = now - start_time
            if time_pass < uav_reveal_time:
                reveal_time = uav_reveal_time - time_pass
                fadeout_time = enemy_fadeout_time
            elif time_pass < uav_reveal_time + enemy_fadeout_time:
                reveal_time = 0
                fadeout_time = uav_reveal_time + enemy_fadeout_time - time_pass
            else:
                self.HideAllUavTag()
                return

        for avatar_id, pos_yaw in avatar_infos.items():
            self.HideEnemyMarkTagsByAvatarId(avatar_id)
            pos = pos_yaw[:3]
            if need_limit_dis and not in_range_2d(pos, fix_pos, need_limit_dis):
                continue
            yaw = pos_yaw[-1]
            if avatar_id in self.uav_enemy_tags:
                tag = self.uav_enemy_tags[avatar_id]
            else:
                parent = self.image
                tag = self.CreateTagById(23, wrapper=UavEnemyNode, parent=parent)
                self.uav_enemy_tags[avatar_id] = tag
            pix_pos = self.PosToPix(pos[0], pos[-1])

            if is_mini:
                self.AddBorderProcessForTag(avatar_id, tag, pix_pos)
                tag.setPosition(self.CalTeamMemberOutsidePixCircle(pix_pos))
                tag.setRotation(-self.RealYawToTagAngle(yaw))
                tag.RunFadeAction(reveal_time, fadeout_time)
            else:
                tag.setPosition(cc.Vec2(*pix_pos))
                tag.setRotation(-self.RealYawToTagAngle(yaw))
                tag.RunFadeAction(reveal_time, fadeout_time)
            if is_mini:
                # 立刻刷新一次位置
                self.BorderTagTick(None)

        self.hide_timer = owner.add_timer(reveal_time + fadeout_time + DELAY_HIDE_TIME, self.HideAllUavTag)


class EnemyMarkComp(object):
    # 敌方位置/朝向显示组件  要与上面的UAVComp一起使用
    def __init_component__(self, _):
        self.enemy_mark_tags = {}

    def __show_component__(self, value):
        # 根据combat_team恢复
        if self.is_mini or not value:
            return
        self.InitEnemyMarkShow()

    def InitEnemyMarkShow(self):
        player = replay_util.GetPlayer()
        if not player:
            return
        combat_team = player.combat_team
        if not combat_team:
            return
        map_marks = combat_team.talent_data.map_marks
        if not map_marks:
            self.HideAllEnemyMarkTags()
        else:
            self.ShowEnemyMark(map_marks)

    def ShowEnemyMark(self, avatar_infos, extra=None):
        # avatar_infos: {avatar_id: (x,y,z,yaw)}
        # property on_set的间隔    TODO
        if extra is None:
            extra = {}
        is_gradually = extra.get('is_gradually', False)
        show_duration = extra.get('show_duration', -1)
        fadeout_time = extra.get('fadeout_time', 0.2)
        gun_sound = extra.get('gun_sound', False)
        interval = 0.2
        # region ########## 敌人位置
        # 现在有部分移出逻辑，所以要先删掉不显示的敌人位置图标
        if not gun_sound:
            remove_list = []
            for avatar_id in self.enemy_mark_tags.keys():
                if avatar_id not in avatar_infos:
                    remove_list.append(avatar_id)
            for avatar_id in remove_list:
                tag = self.enemy_mark_tags.pop(avatar_id, None)
                tag.setVisible(False)
                self.RemoveTag(tag)

        for avatar_id, pos_yaw in avatar_infos.items():
            # 这两玩意共存，所以得这样
            self.HideUavTagByAvatarId(avatar_id)
            self.HideFallUavTagByAvatarId(avatar_id)
            pos_yaw = list(pos_yaw)
            pos = pos_yaw[:3]
            yaw = pos_yaw[-1]
            pix = self.PosToPix(pos[0], pos[-1])
            if avatar_id in self.enemy_mark_tags:
                # 如果上一个开枪标记还没走完流程，直接return
                # 宏彬说没用了，只有侦查箭用，继续走
                tag = self.enemy_mark_tags[avatar_id]
            else:
                if gun_sound and self.is_mini:
                    is_inside = self.IsTeamMemberInsideWithoutRotate(pix)
                    parent = self.image if is_inside else self.panel_mask.widget
                else:
                    parent = self.panel_mask.widget if self.is_mini else self.image
                tag = self.CreateTagById(46, wrapper=UavEnemyNode, parent=parent)
                self.enemy_mark_tags[avatar_id] = tag
            if self.is_mini and tag.getParent() is self.panel_mask.widget:
                if is_gradually:
                    tag.SetAvatarPosGradually(self.CalTeamMemberPosInPanelMask(pix), interval)
                else:
                    tag.setPosition(self.CalTeamMemberPosInPanelMask(pix))
            else:
                if is_gradually:
                    tag.SetAvatarPosGradually(cc.Vec2(*pix), interval)
                else:
                    tag.setPosition(cc.Vec2(*pix))
            if is_gradually:
                tag.SetAvatarYawGradually(yaw, self.RealYawToTagAngle, interval)
            else:
                tag.setRotation(-self.RealYawToTagAngle(yaw))
            if show_duration != -1:
                self.owner.add_timer(
                    show_duration + fadeout_time + 0.05, functools.partial(self.HideEnemyMarkTagsByAvatarId, avatar_id))
                tag.RunFadeAction(show_duration, fadeout_time)
        # endregion ####### 敌人位置

    def HideAllEnemyMarkTags(self):
        for tag in self.enemy_mark_tags.values():
            tag.setVisible(False)
            self.RemoveTag(tag)
        self.enemy_mark_tags = {}

    def HideEnemyMarkTagsByAvatarId(self, avatar_id):
        tag = self.enemy_mark_tags.pop(avatar_id, None)
        if tag:
            tag.setVisible(False)
            self.RemoveTag(tag)
