# -*- coding: utf-8 -*-

import MObject
import Timer
import <PERSON>Type
import <PERSON><PERSON><PERSON>
from gclient.framework.models.simple_model import SimpleModel
import switches
from gclient.util.debug_log_util import print_s, SomePreset
from gclient.util.CinematicsImp import GetCastManager


class CharacterDisplayCineControlEntity(object):
    def __init__(self):
        self.template_graph_file = ""
        self.template_graph_file = "Graph/Cinematic/matchIntro_cine.graph"
        self.skeleton_file = ""
        self.cine_file = ""
        self.entity = None
        self.cineGroupReadyCallback_id = "Intro_CineGroupReady"
        self.owner_character_display_manager = None

    def OnCreate(self, owner_character_display_manager):
        self.owner_character_display_manager = owner_character_display_manager
        entity = SimpleModel('IEntity')
        entity.IsAnimated = True
        entity.IsMovable = True

        entity.model = MObject.CreateObject('IEntity')
        entity.model.Skeleton = MObject.CreateObject('ActorComponent')
        entity.model.Skeleton.SetEnableControlLight(True)  # default value is True
        entity.model.Skeleton.LoadSkeletonAndGraph(self.skeleton_file, self.template_graph_file)
        entity.BindEventSignalNotify()  # 绑定model_cue事件

        trans = MType.Matrix4x3()
        entity.Transform = trans
        self.entity = entity
        self.entity.model.SetName("CharacterDisplay_CineControl")
        self.enable_log = False

        # bind callback
        print_s(f"AddCineGroupReadyCallback: {self.cineGroupReadyCallback_id}", SomePreset.white_fg_yellow_bg)  # noqa
        GetCastManager().AddCineGroupReadyCallback(self.cineGroupReadyCallback_id, self.OnCineGroupReady)
    
    def print_s(self, *args, **kwargs):
        if self.enable_log:
            print_s(*args, **kwargs)
   
    def OnEnterArea(self, root_area, owner_character_display_manager):
        self.owner_character_display_manager = owner_character_display_manager
        # 极端情况下出现entity expired，导致trace, 先保护
        # TODO: 找到为啥会expired
        if not self.entity.model.IsValid():
            import traceback
            call_stack = traceback.format_stack()
            self.print_s("CharacterDisplayLightControlEntity OnEnterArea WARNING: entity expired, recreate")
            self.print_s(f"CharacterDisplayLightControlEntity OnEnterArea WARNING: call_stack: {call_stack}")
            self.OnCreate(owner_character_display_manager)

        if self.entity.model.Area != root_area:
            self.entity.model.EnterArea(root_area)
    
    def SetCineFileToPlay(self, cine_file, cam_cine_file=None):
        self.cine_file = cine_file
        self.entity.model.Skeleton.SetVariableS(0, "cine_file", cine_file)

        # Test
        # cam_cine_file = "Graph\\Cinematic\\Marok\\Marok_Camera.cine"
        can_play_camera_cine = cam_cine_file is not None
        if can_play_camera_cine:
            self.entity.model.Skeleton.SetVariableS(0, "cam_cine_file", cam_cine_file)
        else:
            self.entity.model.Skeleton.SetVariableS(0, "cam_cine_file", "")

        self.entity.model.Skeleton.SetVariableZ(0, "can_play_camera", can_play_camera_cine)

        if not switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
            Timer.addTimer(0.3, self.DelaySetCineOffset)
    
    def DelaySetCineOffset(self):
        pos_offset = switches.MATCHINTRO_LEVEL_POS
        rot_offset = MType.Vector3(0, 0, 0)
        file_name_with_ext = self.cine_file.split("\\")[-1]
        file_name = file_name_with_ext.split(".")[0]
        MCharacter.SetCineEpisodeOffset('main', 0, '', pos_offset, rot_offset)
        MCharacter.SetCineEpisodeOffset('camera', 0, '', pos_offset, rot_offset)
        self.print_s(f"MCharacter.SetCineEpisodeOffset: {file_name}, {pos_offset}, {rot_offset}", SomePreset.white_fg_purple_bg)

    def PlayCine(self):
        self.print_s(f"PlayCine: {self.cine_file}")
        # 不用了
        return
        self.entity.model.Skeleton.FireEvent(0, "OnAbandonCineOver")
        pass
    
    def PlayCineOver(self):
        if self.IsValid():
            self.entity.model.Skeleton.FireEvent(0, "OnCinePlayOver")

    def OnDestroy(self):
        pass

    def destroy(self):
        self.PlayCineOver()
        self.OnDestroy()
        # self.entity.Destroy()
        if self.IsValid():
            self.entity.model.LeaveArea()
        self.entity = None
        if hasattr(genv, 'cm') and genv.cm:
            if 'main' in genv.cm.groups:
                genv.cm.dismissPerformers('main')
            if 'camera' in genv.cm.groups:
                genv.cm.dismissPerformers('camera')

    def IsValid(self):
        if not self.entity:
            return False
        if not self.entity.model:
            return False
        if not self.entity.model.IsValid():
            return False
        return True

    def SetCinePlayTime(self, cur_play_time):
        prev_play_state = MCharacter.GetCineEpisodeState('main', 0)
        self.print_s(f"尝试设置Cine播放时间: {cur_play_time}, 之前: {prev_play_state}", SomePreset.white_fg_yellow_bg)
        MCharacter.SetCineEpisodeTime('main', 0, 0, cur_play_time, False)
        MCharacter.SetCineEpisodeTime('camera', 0, 0, cur_play_time, False)
        pass
    
    def OnCineGroupReady(self, group_name):
        if group_name not in ['camera']:
            return

        self.owner_character_display_manager.play_status_info.is_cine_ready = True
        self.owner_character_display_manager.play_status_info.CheckAllReadyAndExcuteReadyCallback()

