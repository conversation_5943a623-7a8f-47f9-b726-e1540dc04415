# -*- coding: utf-8 -*-
# author: chenhuaifei
import time
import functools
import Timer
import math
import MTypeEx
import MType
import MRender
import MShowRoom
from common.IdManager import IdManager
from gclient import cconst
from gclient.gamesystem.entities.character_display_cineControlEntity import CharacterDisplayCineControlEntity
# from gclient.gamesystem.models import charact_display_test_config
from gclient.util import CinematicsBase, CinematicsImp
from gclient.data import hero_data, hero_performance_data, unit_model_data
from gclient.framework.util import MHelper, events
from gclient.gamesystem.models.hall_doll_manager_base import HallDollManagerBase
from gclient.util.debug_log_util import SomePreset, print_s
from gshare import consts, formula  # noqa
from gshare.utils import SingletonMeta
import switches


default_hero_performance_data = {
    'Cinematic_file': 'Graph\Cinematic\Intro\Light_Intro_Idle.cine',
    'Camera_Cinematic_file': 'Graph\Cinematic\Intro\Camera_Intro_Idle.cine',
    'init_with_dissolve': True,
}


# TODO: 后续用这种方式，优化整个异步加载流程，避免现在回调套回调
class CharacterDisplayPlayStatusInfo(object):
    def __init__(self):
        self.is_main_npc_ready = False
        self.prop_ready_count = 0
        self.all_prop_count = 0
        self.is_cine_ready = False
        self.all_ready_callback = None
        self.all_ready_callbacks = []
    
    def SetAllReadyCallback(self, callback):
        self.all_ready_callback = callback
    
    def AppendAllReadyCallBack(self, callback):
        self.all_ready_callbacks.append(callback)
    
    def IsAllReady(self):
        return self.is_main_npc_ready and self.prop_ready_count >= self.all_prop_count and self.is_cine_ready
    
    def ResetStatus(self):
        self.is_main_npc_ready = False
        self.prop_ready_count = 0
        self.all_prop_count = 0
        self.is_cine_ready = False
        self.all_ready_callback = None
        self.all_ready_callbacks = []
    
    def CheckAllReadyAndExcuteReadyCallback(self):
        # import traceback
        # traceback.print_stack()
        print_s(f"CheckAllReadyAndExcuteReadyCallback: {self.IsAllReady()}", SomePreset.black_fg_cyan_bg)
        if self.IsAllReady():
            for callback in self.all_ready_callbacks:
                callback()
            self.all_ready_callbacks = []
            print_s(f"CheckAllReadyAndExcuteReadyCallback: 执行所有回调, self.all_ready_callback: {self.all_ready_callback}", SomePreset.white_fg_green_bg)
            if self.all_ready_callback:
                self.all_ready_callback()
                self.all_ready_callback = None


# TODO: 参考SplitScreenShowTeammateCtrl 优化重构整个加载流程
class CharacterDisplayDollModelManager(HallDollManagerBase, metaclass=SingletonMeta):
    def __init__(self):
        super(CharacterDisplayDollModelManager, self).__init__()
        self.main_npc = None  # 自己的模型
        print_s("CharacterDisplayDollModelManager.__init__", SomePreset.white_fg_green_bg)
        self.all_main_npc_dolls = {}
        self.use_ready_to_appear = True
        events.BatchAddListeners(self)
        self.hidden_reasons = []
        self.main_npc_guid = IdManager.genid()
        self.current_showroom_world = None
        self.current_showroom_name = "ChooseHeroShowRoomWithWorld"
        # NEW
        self.current_main_npc_other_models = []  # 其他npc的模型, 现在将存储IEntity实例

        self.cine_control_entity = None
        self.current_hero_id = None
        self.add_child_timer = None
        self.enable_log = False
        self.play_status_info = CharacterDisplayPlayStatusInfo()

        self.first_hero_show = True
        self.auto_disable_only_draw_ui = True
        self.last_time_when_Hero_start_show = -1
    
    def print_s(self, *args, **kwargs):
        if self.enable_log:
            print_s(*args, **kwargs)
    
    def OnShowRoomWramup(self, world):
        self.WarmUpAllMainNpcFromHeroPerformanceData(world)
        avatar = genv.avatar
        if not avatar:
            return
        if not avatar.hero_id:
            return
        self.OnEnter(avatar.hero_id)
        # self.RefreshSelectedHero(avatar.hero_id)
    
    def InitCineControl(self):
        valid = self.cine_control_entity and self.cine_control_entity.IsValid()
        print_s(f"CharacterDisplayDollModelManager.InitCineControl: is valid? -> {valid}", SomePreset.white_fg_yellow_bg)
        if not valid:
            print_s("CharacterDisplayDollModelManager.InitCineControl: 销毁cine_control_entity", SomePreset.white_fg_red_bg)
            # self.cine_control_entity.destroy()
            self.cine_control_entity = None

        if not self.cine_control_entity:
            print_s("CharacterDisplayDollModelManager.InitCineControl: 创建cine_control_entity", SomePreset.white_fg_green_bg)
            self.cine_control_entity = CharacterDisplayCineControlEntity()
            self.cine_control_entity.OnCreate(self)

    def InitMainNpc(self, cur_hero_id=None):
        print_s("CharacterDisplayDollModelManager.InitMainNpc", SomePreset.white_fg_green_bg)
        avatar = genv.avatar
        if not avatar:
            return
        
        if not cur_hero_id:
            cur_hero_id = avatar.hero_id

        cur_hero_version_id = switches.CHARACTER_DISPLAY_CONFIG_VERSION
        character_display_config = self.GetHeroCharactDisplayConfig(cur_hero_id, cur_hero_version_id)
        if not character_display_config or not character_display_config.get('hero_skeleton'):
            self.InitMainNpcWithHallConfig(cur_hero_id, cur_hero_version_id)
        else:
            self.InitMainNpcWithCharactDisplayConfig(character_display_config, cur_hero_id, cur_hero_version_id)

    # 没有出场资源的话, 用大厅局外展示初始化
    def InitMainNpcWithHallConfig(self, cur_hero_id, doll_version_id=0):
        self.main_npc = self.GetHeroDollFromHeroId(cur_hero_id, doll_version_id)

    # 有出场资源的话, 用出场资源初始化
    def InitMainNpcWithCharactDisplayConfig(self, character_display_config, cur_hero_id, doll_version_id=0):
        self.main_npc = self.GetHeroDollFromHeroId(cur_hero_id, doll_version_id)

    def DestroyDoll(self, doll):
        if doll is self.main_npc:
            self.main_npc = None
            return

    def OnEnter(self, cur_hero_id=None):
        self.last_time_when_Hero_start_show = -1
        # if hasattr(genv, 'cm') and genv.cm:
        #     groups = genv.cm.groups
        #     print_s(f"OnEnter: 当前cm groups: {groups}", SomePreset.white_fg_red_bg)
        #     if 'main' in genv.cm.groups:
        #         genv.cm.dismissPerformers('main')
        #     if 'camera' in genv.cm.groups:
        #         genv.cm.dismissPerformers('camera')
        print_s(">>> CharacterDisplayDollModelManager.OnEnter", SomePreset.white_fg_green_bg)
        genv.character_display_doll_model_manager = self
        self.InitCineControl()
        self.ValidateALLMainNpc()
        self.InitMainNpc(cur_hero_id)
    
    def OnAllReady(self):
        self.last_time_when_Hero_start_show = time.time()
        print_s("OnAllReady", SomePreset.white_fg_green_bg)
        self.main_npc.model.FireEvent("@Run")
        self.main_npc.DelayRemoveHiddenReason(self.AllPropDelayRemoveHiddenReason)
        # self.AllPropDelayRemoveHiddenReason()
        for prop in self.current_main_npc_other_models:
            prop.model.FireEvent("@Run")
            pass
        self.cine_control_entity.SetCinePlayTime(0)
        if self.first_hero_show:
            self.first_hero_show = False
            Timer.addTimer(0.1, functools.partial(gui.BackdropAllFadeOut, 0.2, None))
        pass

    def AllPropDelayRemoveHiddenReason(self):
        for prop in self.current_main_npc_other_models:
            prop.model.RemoveHiddenReason(cconst.HIDDEN_REASON_COMMON)

    def OnLeave(self):
        self.last_time_when_Hero_start_show = -1
        self.play_status_info.ResetStatus()
        self._DestroyAllCurrentMainNpcOtherModels()
        # print_s("<<< CharacterDisplayDollModelManager.OnLeave", SomePreset.white_fg_red_bg)
        if self.auto_disable_only_draw_ui:
            gpl.SetRenderOption("OnlyDrawUI", False)
        self.ReleaseNpcCinematics()
        for hero_id, version_id_doll_dict in self.all_main_npc_dolls.items():
            for version_id, doll in version_id_doll_dict.items():
                if doll:
                    if self.IsMainNpcValid(doll):
                        doll.model.SetEnableControlCamera(False)
                        doll.is_hall_model_loaded = False
                        doll.destroy()
                doll = None
        if self.cine_control_entity:
            self.cine_control_entity.destroy()
            self.cine_control_entity = None
        self.first_hero_show = True
        CinematicsBase.SetCustomCinematicsArea(None)
    
    def IsMainNpcValid(self, doll):
        if not doll:
            return False
        if not hasattr(doll, 'model') or not doll.model:
            return False
        if not doll.model.model or not doll.model.model.IsValid():
            return False
        return True
    
    def GetCurrentHeroPerformanceData(self, hero_id, version_id=0):
        hero_data = hero_performance_data.data.get(hero_id, None)
        if not hero_data:
            return default_hero_performance_data

        self.print_s(f"使用版本: <{version_id}> 的英雄表演数据: <{hero_id}>", SomePreset.white_fg_yellow_bg)
        data = hero_data.get(version_id, None)
        if not data:
            data = hero_data.get(0, None)
            self.print_s(f"GetCurrentHeroPerformanceData: 版本{version_id}不存在，使用默认版本0", SomePreset.white_fg_red_bg)
        return data
    
    def GetHeroDollFromHeroId(self, hero_id, version_id=0):
        if hero_id not in self.all_main_npc_dolls:
            return None
        the_hero_dolls = self.all_main_npc_dolls[hero_id]
        if version_id not in the_hero_dolls:
            self.print_s(f"GetHeroDollFromHeroId: {hero_id}, {version_id} 版本不存在，使用默认版本0", SomePreset.white_fg_red_bg)
            return the_hero_dolls[0]
        return the_hero_dolls[version_id]
    
    def CheckHeroDollVersion(self, hero_id, version_id=0):
        if hero_id not in self.all_main_npc_dolls:
            return False
        the_hero_dolls = self.all_main_npc_dolls[hero_id]
        if version_id not in the_hero_dolls:
            return False
        return True
    
    def SetHeroDollFromHeroId(self, doll, hero_id, version_id=0):
        if hero_id not in self.all_main_npc_dolls:
            self.all_main_npc_dolls[hero_id] = {}
        self.print_s(f"SetHeroDollFromHeroId: {hero_id}, {version_id}, {doll}", SomePreset.white_fg_green_bg)
        the_hero_dolls = self.all_main_npc_dolls[hero_id]
        the_hero_dolls[version_id] = doll
        self.all_main_npc_dolls[hero_id] = the_hero_dolls

    def GetArmClothesFromHeroId(self, hero_id):
        # 获取英雄默认套装ID
        hero_item_id = hero_data.data.get(hero_id, {}).get('hero_item_id', consts.DEFAULT_SUIT)
        return {consts.ItemSubType.Suit: hero_item_id}
    
    def ReleaseNpcCinematics(self):
        self.print_s("-----------------------------------------------------------------")
        self.print_s("[CharacterDisplayDollModelManager] ---> ReleaseNpcCinematics <---")
        self.print_s("-----------------------------------------------------------------")

    def RefreshSelectedHero(self, hero_id=0):
        # if self.first_hero_show:
        #     gui.BackdropAllFadeIn(0.1, (0, 0, 0), None)

        self.play_status_info.ResetStatus()
        self.play_status_info.SetAllReadyCallback(self.OnAllReady)
        if hero_id is None:
            hero_id = 101  # 默认英雄
        print_s(f"RefreshSelectedHero: {hero_id}", SomePreset.white_fg_green_bg)
        self.InitCineControl()

        if not self.current_hero_id:
            self.current_hero_id = hero_id

        version_id = switches.CHARACTER_DISPLAY_CONFIG_VERSION
        cine_file = self.GetCurrentHeroPerformanceData(hero_id, version_id).get('Cinematic_file', 'Graph\Cinematic\Idle_MatchIntro.cine')
        camera_cine_file = self.GetCurrentHeroPerformanceData(hero_id, version_id).get('Camera_Cinematic_file', None)
        self.cine_control_entity.SetCineFileToPlay(cine_file, camera_cine_file)

        for cur_hero_id, version_id_doll_dict in self.all_main_npc_dolls.items():
            for cur_version_id, doll in version_id_doll_dict.items():
                cur_hero_doll = doll
                if cur_hero_doll and cur_hero_doll.model:
                    cur_hero_doll.model.skeleton.ResetAllGraph()
                    cur_hero_doll.AddHiddenReason(cconst.HIDDEN_REASON_COMMON)

        self.cine_control_entity.PlayCineOver()

        # 更新选择的英雄的套装
        character_display_config = self.GetHeroCharactDisplayConfig(hero_id, version_id)
        init_with_dissolve = True
        if not character_display_config or not character_display_config.get('hero_skeleton'):  # 这个hero还没有出场资源，故用默认大厅局外展示顶一下
            self.main_npc = self.GetHeroDollFromHeroId(hero_id, version_id)
            self.main_npc.is_a_character_display_doll = True
            self.main_npc.init_with_dissolve = init_with_dissolve
            self.main_npc.ShowModelWhenInShowroom(auto_play=True, callback=self.OnHeroDollShowModelWhenInShowroomCallback)
        else:
            init_with_dissolve = character_display_config.get("init_with_dissolve", True)
            self.main_npc = self.GetHeroDollFromHeroId(hero_id, version_id)
            self.main_npc.is_a_character_display_doll = True
            self.main_npc.init_with_dissolve = init_with_dissolve
            self.UpdateMainNpcGraph(hero_id, version_id)
            self.main_npc.ShowModelWhenInShowroom(auto_play=True, callback=self.OnHeroDollShowModelWhenInShowroomCallback)
        self.current_hero_id = hero_id
    
    # 在showroom中展示Hero模型后的回调
    def OnHeroDollShowModelWhenInShowroomCallback(self, hero_id, doll):
        print_s(f"在showroom中展示Hero模型后的回调: {hero_id}, {doll}", SomePreset.white_fg_cyan_bg)
        version_id = switches.CHARACTER_DISPLAY_CONFIG_VERSION
        camera_cine_file = self.GetCurrentHeroPerformanceData(hero_id, version_id).get('Camera_Cinematic_file', None)

        init_with_dissolve = True
        character_display_config = self.GetHeroCharactDisplayConfig(hero_id, version_id)
        if not character_display_config or not character_display_config.get('hero_skeleton'):  # 这个hero还没有出场资源，故用默认大厅局外展示顶一下
            init_with_dissolve = True
        else:
            init_with_dissolve = character_display_config.get("init_with_dissolve", True)

        if self.current_showroom_world and self.current_showroom_world.IsValid():
            level = self.current_showroom_world.DefaultLevel
            if not switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
                level = self.current_showroom_world.Levels['MatchIntro@$root']
            area = level.RootArea
            if area.IsValid() and level.IsInWorld:
                doll.EnterArea(area)
                doll.EnterAttachWeaponCase(area)
        if other_model_anim_dict := self.GetCurrentHeroPerformanceData(hero_id, version_id).get('other_model_anim', {}):
            self.RefreshAllCurrentMainNpcOtherModels(other_model_anim_dict, doll, init_with_dissolve)
        else:
            print_s(f"没有其他模型动画, 使用默认动画: hero_id: {hero_id}, version_id: {version_id}", SomePreset.white_fg_red_bg)
            self.RefreshAllCurrentMainNpcOtherModels(None, doll, init_with_dissolve)
        pass

        if camera_cine_file:
            self.print_s(f"有相机剧情文件: {camera_cine_file}, 关闭Skeleton相机控制", SomePreset.white_fg_green_bg)
            self.main_npc.model.SetEnableControlCamera(False)
        else:
            self.print_s("没有相机剧情文件, 打开Skeleton相机控制", SomePreset.white_fg_red_bg)
            self.main_npc.model.SetEnableControlCamera(True)

        if switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
            # self.main_npc.model.skeleton.CameraName = "SR_%s" % str(self.current_showroom_name)
            self.main_npc.model.skeleton.CameraName = ""
        self.main_npc.model.skeleton.CameraName = ""
        self.main_npc.model.model.Skeleton.UseDynamicVisibilityBox = True

    def UpdateMainNpcGraph(self, hero_id, version_id=0):
        config = self.GetCurrentHeroPerformanceData(hero_id, version_id)
        self.main_npc.cur_anim_to_play = config['hero_sanim']
        self.main_npc.cur_camera_index_to_play = 0
    
    def GetHeroCharactDisplayConfig(self, hero_id, version_id=0):
        character_display_config = self.GetCurrentHeroPerformanceData(hero_id, version_id)
        if not character_display_config or not character_display_config.get('hero_skeleton'):
            return None
        return character_display_config
    
    def SyncCinePlayTime(self, cur_play_time):
        # self.cine_control_entity.SetCinePlayTime(cur_play_time)
        pass

    def OnCharacterDisplayEnterShowroomWorld(self, showroom_name, world):
        if not world:
            return
        self.SetShowroomRenderOptionAndPostProcess(showroom_name, world)
        for hero_id, version_id_doll_dict in self.all_main_npc_dolls.items():
            for version_id, doll in version_id_doll_dict.items():
                doll.SetShowroomWorld(showroom_name, world)
        self.current_showroom_world = world
        area = world.DefaultLevel.RootArea
        if not switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
            area = world.Levels['MatchIntro@$root'].RootArea

        self.InitCineControl()
        self.cine_control_entity.OnEnterArea(area, self)
        self.current_showroom_name = showroom_name

        for prop in self.current_main_npc_other_models:
            prop_model = prop.model.model
            if prop_model and prop_model.IsValid():
                if prop_model.Area != area:
                    prop_model.EnterArea(area)
    
    def SetShowroomRenderOptionAndPostProcess(self, showroom_name, world):
        if not switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
            return
        showroom_camera = MShowRoom.GetCamera(showroom_name)
        showroom_camera.Near = 0.01
        showroom_camera.Far = 1000
        MTypeEx.InitializeType('EAutoExposureMethod')
    
    def FindBaseEnvVolume(self, showroom_world):
        return showroom_world.EnvVolume

    def WarmUpAllMainNpcFromHeroPerformanceData(self, world):
        for hero_id in hero_performance_data.data:
            cur_hero_data = hero_performance_data.data[hero_id]
            for version_id in cur_hero_data:
                self.print_s(f"预热英雄: {hero_id}, 版本id: {version_id}", SomePreset.white_fg_purple_bg)
                self.ValidateMainNpcByHeroId(hero_id, world, version_id)
    
    # 预热角色模型
    def WarmUpMainNpcByHeroId(self, hero_id, world, doll_version_id=0):
        character_display_config = self.GetHeroCharactDisplayConfig(hero_id, doll_version_id)
        area = None
        if world:
            if switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
                area = world.DefaultLevel.RootArea
            else:
                area = world.Levels['MatchIntro@$root'].RootArea

        cur_hero_doll = None
        if not character_display_config or not character_display_config.get('hero_skeleton'):
            cur_hero_doll = self.WarmUpMainNpcWithHallConfig(hero_id, world, doll_version_id)
        else:
            cur_hero_doll = self.WarmUpMainNpcWithCharactDisplayConfig(character_display_config, hero_id, world, doll_version_id)
        self.SetHeroDollFromHeroId(cur_hero_doll, hero_id, doll_version_id)

    def OnHeroShowmodelWithWarmUp(self, hero_id, doll, world):
        area = None
        if world:
            if switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
                area = world.DefaultLevel.RootArea
            else:
                area = world.Levels['MatchIntro@$root'].RootArea
        if area and area.IsValid():
            doll.EnterArea(area)
            doll.EnterAttachWeaponCase(area)
            doll.EnableTeammateWeaponsVisible(False)
        pass

    # 没有出场资源的话, 用大厅局外展示初始化
    def WarmUpMainNpcWithHallConfig(self, hero_id, world, doll_version_id=0):
        hero_proto = hero_data.data.get(hero_id, {})
        hero_gender = hero_proto.get('hero_gender', 0)
        pos, yaw = self._GetTeammatePosYawByIndex(0, 1)
        pos = (0, 0, 0)
        arm_clothes = self.GetArmClothesFromHeroId(hero_id)
        temp_guid = IdManager.genid()
        yaw = math.pi
        main_npc = self._CreateDoll(temp_guid,
                                    0,
                                    arm_clothes,
                                    (pos, yaw),
                                    hero_gender,
                                    is_main_npc=True,
                                    extra={'doll_cls': 'CharacterDisplayDoll'})
        if not main_npc:
            return
        main_npc.SetCharacterDisplayConfig(None)
        main_npc.SetCurrentHeroId(hero_id)
        main_npc.ShowModel(True,
                           callback=lambda: self.OnHeroShowmodelWithWarmUp(hero_id, main_npc, world),
                           if_selected_callback=self.OnHeroDollShowModelWhenInShowroomCallback)
        main_npc.model.model.SetName(f"char_display_{hero_id}_ver_{doll_version_id}")
        return main_npc

    # 有出场资源的话, 用出场资源初始化
    def WarmUpMainNpcWithCharactDisplayConfig(self, character_display_config, hero_id, world, doll_version_id=0):
        hero_proto = hero_data.data.get(hero_id, {})
        hero_gender = hero_proto.get('hero_gender', 0)
        pos, yaw = self._GetTeammatePosYawByIndex(0, 1)
        pos = (0, 0, 0)
        arm_clothes = self.GetArmClothesFromHeroId(hero_id)
        temp_guid = IdManager.genid()
        yaw = math.pi
        # yaw = 0
        main_npc = self._CreateDoll(temp_guid,
                                    0,
                                    arm_clothes,
                                    (pos, yaw),
                                    hero_gender,
                                    is_main_npc=True,
                                    extra={'doll_cls': 'CharacterDisplayDoll'})
        if not main_npc:
            return
        main_npc_id = character_display_config.get('hero_skeleton', 400001)
        main_npc.SetCharacterDisplayConfig(character_display_config)
        main_npc.SetCharacterDisplayModelId(main_npc_id)
        main_npc.SetCurrentHeroId(hero_id)
        main_npc.ShowModel(True,
                           callback=lambda: self.OnHeroShowmodelWithWarmUp(hero_id, main_npc, world),
                           if_selected_callback=self.OnHeroDollShowModelWhenInShowroomCallback)
        main_npc.model.model.SetName(f"char_display_{hero_id}_版本_{doll_version_id}")
        return main_npc
    
    
    def ValidateALLMainNpc(self):
        for hero_id in hero_performance_data.data:
            cur_hero_data = hero_performance_data.data[hero_id]
            for version_id in cur_hero_data:
                self.ValidateMainNpcByHeroId(hero_id, doll_version_id=version_id)
    
    def ValidateMainNpcByHeroId(self, hero_id, world=None, doll_version_id=0):
        doll = self.GetHeroDollFromHeroId(hero_id, doll_version_id)
        check_version = self.CheckHeroDollVersion(hero_id, doll_version_id)
        is_valid = doll and hasattr(doll, 'model') and doll.model.model.IsValid() and check_version
        if not is_valid:
            self.print_s(f"ValidateMainNpcByHeroId: {hero_id} 模型, 版本id: {doll_version_id} 无效，重新预热", SomePreset.white_fg_red_bg)
            self.WarmUpMainNpcByHeroId(hero_id, world, doll_version_id)
        return True

    # 刷新当前主npc的其他搭配模型之类, 比如宠物苦无之类
    def RefreshAllCurrentMainNpcOtherModels(self, other_model_anims_dict, main_npc=None, init_with_dissolve=True):
        self._DestroyAllCurrentMainNpcOtherModels()
        if not other_model_anims_dict or not main_npc or not self.IsMainNpcValid(main_npc):
            print_s(f"cannot RefreshAllCurrentMainNpcOtherModels: other_model_anims_dict: {other_model_anims_dict}, main_npc: {main_npc}, self.IsMainNpcValid(main_npc): {self.IsMainNpcValid(main_npc)}", SomePreset.white_fg_red_bg)
            return
        
        # 确保 main_npc.model.model 是有效的父实体
        parent_entity = main_npc.model.model 
        if not parent_entity or not parent_entity.IsValid():
            self.print_s("RefreshAllCurrentMainNpcOtherModels: Main NPC model is not valid.", SomePreset.white_fg_red_bg)
            return

        all_prop_count = len(other_model_anims_dict)
        self.play_status_info.all_prop_count = all_prop_count
        for model_id, anim_name in other_model_anims_dict.items():
            print_s(f"model_id: {model_id}, anim_name: {anim_name}, area: {parent_entity.Area}", SomePreset.white_fg_red_bg)
            prop = self._CreateProp(model_id, parent_entity, on_load_done=functools.partial(self.OnPropModelLoaded, anim_name=anim_name))
            prop.init_with_dissolve = init_with_dissolve
            model_entity = prop.model.model
            model_entity.SetName(f"角色演出道具_{model_id}")
            self.current_main_npc_other_models.append(prop)
        self.print_s(f"Refreshed other NPCs. Count: {len(self.current_main_npc_other_models)}", SomePreset.white_fg_green_bg)
    
    def OnPropModelLoaded(self, prop, anim_name):
        model_entity = prop.model.model

        main_npc = self.main_npc
        main_npc.model.model.Skeleton.AddChild(model_entity.Skeleton)
        model_entity.Skeleton.SetVariableZ(-1,"is_main_npc", False)  # noqa
        model_entity.Skeleton.SetVariableI(-1,"cur_hero_id", main_npc.current_hero_id)  # noqa
        model_entity.Skeleton.SetVariableS(-1,"cur_anim", anim_name)  # noqa
        idle_anim_name = anim_name + "_idle"
        has_idle_anim = model_entity.Skeleton.HasAnimation(idle_anim_name)
        # print_s(f"prop: {model_id}, 有idle动画: {has_idle_anim}, idle_anim_name: {idle_anim_name}")
        model_entity.Skeleton.SetVariableS(-1,"cur_anim_idle", idle_anim_name)  # noqa
        model_entity.Skeleton.SetVariableZ(-1,"has_idle_anim", has_idle_anim)  # noqa
        model_entity.Skeleton.SetVariableZ(-1,"enter_play_over", False)  # noqa
        model_entity.Skeleton.FireEvent(-1, "EndPlay")  # noqa
        self.print_s(f"Set anim '{anim_name}' for model_id {prop.model.model_id}", SomePreset.white_fg_green_bg)

        prop.model.AddHiddenReason(cconst.HIDDEN_REASON_COMMON)
        self.play_status_info.prop_ready_count += 1
        self.play_status_info.CheckAllReadyAndExcuteReadyCallback()

        world = self.current_showroom_world 
        if not world or not world.IsValid():
            return
        area = world.DefaultLevel.RootArea
        if not switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
            area = world.Levels['MatchIntro@$root'].RootArea
        self.main_npc.model.model.Skeleton.AddChild(prop.model.model.Skeleton)
        if area and area.IsValid() and area.IsInWorld:
            prop.model.model.EnterArea(area)

    # 创建道具
    def _CreateProp(self, model_id, parent_entity, to_enter_area=None, on_load_done=None):
        doll_cls = 'CharacterDisplayProp'
        # doll_cls = 'Doll'
        prop_guid = IdManager.genid()
        prop = genv.space.create_entity(entitytype=doll_cls, entityid=prop_guid,
                                            bdict={'position': (0, 0, 0),  # noqa
                                                   'yaw': 0,
                                                   'model_id': model_id,
                                                   'parent_entity': parent_entity,
                                                   }
                                            )
        prop.ShowModel(on_load_done)
        return prop
   
    def _DestroyAllCurrentMainNpcOtherModels(self):
        """
        销毁所有 current_main_npc_other_models 中的模型实体并清空列表。
        """
        for prop in self.current_main_npc_other_models:
            self.print_s(f"Destroying NPC entity: {prop}", SomePreset.white_fg_red_bg)
            if hasattr(prop, 'model') and prop.model:
                prop_model = prop.model
                if hasattr(prop_model, 'model') and prop_model.model:
                    prop_model_entity = prop_model.model
                    main_npc_model_entity = self.main_npc.model.model
                    if main_npc_model_entity and main_npc_model_entity.IsValid() and prop_model_entity and prop_model_entity.IsValid():
                        main_npc_model_entity.Skeleton.DelChild(prop_model_entity.Skeleton)
            prop.destroy()
        self.current_main_npc_other_models = []
    