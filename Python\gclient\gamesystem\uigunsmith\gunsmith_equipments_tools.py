# -*- coding: utf-8 -*-
import functools

from gclient import lang
from gclient.framework.camera.cam_trans_utils import degree_to_radian
from gclient.framework.ui import ui_define
from gclient.framework.ui.commonnodes.common_key_node import KeyNode
from gclient.framework.ui.ui_helper import Helper<PERSON><PERSON>, HelperWindow
from gclient.framework.ui.widgets import UIButton, UITexture, UINode, UIText, UIProgressBar, UIListViewCycle
from gclient.framework.ui.widgets.ui_bake_texture import UIBakeTexture
from gclient.framework.ui.widgets.ui_button import UITabButton
from gclient.framework.ui.widgets.ui_render_node import UIRenderNode
from gclient.framework.ui.widgets.ui_textfield import UITextField
from gclient.gameplay.logic_base.ui import ui_util

from gclient.gamesystem.uigunsmith.gunsmith_model_ctrl import GunSmithModelCtrl
from gclient.ui.common.hoverable_button import HoverableButton_V2
from gclient.util import gun_ui_util, gun_skin_util
from gshare import weapon_util, consts
from gclient.data import gun_smith_show_weapon_data, gun_level_reward_data, task_data, gun_attachments_data, \
    hall_model_pos_data, gun_ui_attri_data, gun_mod_data
from gshare.utils import enum
import MObject
import MType
from gshare.utils import Singleton
import cc
import ccui


class PartNodeBuffType(enum):
    JIA = 0
    JIAN = 1
    KONG = 2


class GunSmithUnderButton(UIButton):
    """
    下方按键
    """
    def __init__(self, widget):
        super(GunSmithUnderButton, self).__init__(widget)
        self.img_dow = self.seek('img_dow', UITexture)
        self.img_slc = self.seek('img_slc', UITexture)
        self.img_on = self.seek('img_ts_nml')
        self.txt_des = self.seek('txt_des', UIText)
        self.img_slc.visible = False
        self.SetMouseMoveEventEnable(True)
        self.onMouseHover = self.OnMouseHover
        self.onClick = self.OnClick
        self.onClickBegin = self.OnClickBegin
        self.onClickCancel = self.OnClickCancel

    def OnMouseHover(self, widget, is_hover):
        self.img_slc.visible = is_hover

    def OnClickBegin(self, widget=None):
        # self.img_dow.visible = True
        pass
    def OnClickCancel(self, widget=None):
        # self.img_dow.visible = False
        pass

    def OnClick(self, widget=None):
        # self.img_dow.visible = False
        self.OnDoClick(widget)

    def OnDoClick(self, widget=None):
        pass

    def SetOnOff(self, is_on):
        self.img_on.visible = is_on
        self.txt_des.text_color = (0, 0, 0) if is_on else (255, 255, 255)


class GunSmithUnderSlcButton(GunSmithUnderButton):
    """
    下方绿色装配按钮
    """
    def __init__(self, widget):
        super(GunSmithUnderSlcButton, self).__init__(widget)
        self.img_dwn = self.seek('img_dwn', UITexture)
        self.img_hov = self.seek('img_hov')
        self.img_nml = self.seek('img_nml')
        self.txt_des = self.seek('txt_des', UIText)
        self.img_hov.visible = False
        self.img_dwn.visible = False
        self.img_dow = self.img_dwn
        self.is_gray = True
        self.enable = True
        self.txt_0 = "选择枪械"
        self.txt_1 = "已装备"

    def OnMouseHover(self, widget, is_hover):
        if self.is_gray:
            self.img_hov.visible = is_hover
            self.img_nml.visible = not is_hover
        self.img_slc.visible = is_hover

    def OnClickBegin(self, widget=None):
        if self.is_gray:
            self.img_nml.visible = False
            self.img_dow.visible = True

    def OnClickCancel(self, widget=None):
        if self.is_gray:
            self.img_dow.visible = False
            self.img_nml.visible = True

    def OnClick(self, widget=None):
        if self.is_gray:
            self.img_dow.visible = False
            self.img_nml.visible = True
        self.OnDoClick(widget)

    def SetGray(self, is_gray):
        self.is_gray = is_gray
        self.img_nml.visible = is_gray
        self.img_dwn.visible = is_gray
        self.img_hov.visible = is_gray
        self.txt_des.text = self.txt_0 if is_gray else self.txt_1
        self.txt_des.text_color = (0, 0, 0) if is_gray else (255, 255, 255)

    def SetEnabled(self, enable):
        super().SetEnabled(enable)
        self.SetGray(enable)
        self.enable = enable


class GunPropBarNode(UINode):
    def __init__(self, widget):
        super(GunPropBarNode, self).__init__(widget)
        self.txt_stat = self.seek('txt_stats', UIText)
        self.panel_loadingbar = self.seek('panel_loadingbar')
        self.loadingbar_1 = self.panel_loadingbar.seek('loadingbar_1', UIProgressBar)
        self.loadingbar_2 = self.panel_loadingbar.seek('loadingbar_2', UIProgressBar)  # 绿色进度条
        self.loadingbar_3 = self.panel_loadingbar.seek('loadingbar_3', UIProgressBar)  # 红色进度条
        self.img_line = self.panel_loadingbar.seek('img_line')
        self.loadingbar_2.visible = False
        self.loadingbar_3.visible = False
        self.img_line.visible = False  # 原配进度分割线
        self.txt_num = self.seek('txt_num', UIText)
        self.txt_num_up = self.seek('txt_num_up', UIText)
        self.value = None  # 白色进度条的值
        self.select_value = None  # 附加进度条的值
        self.default_value = None  # 默认配件的值

    def SetNodeInfo(self, info):
        self.txt_stat.text = info['name']
        self.value = info['value']
        self.loadingbar_1.SetPercent(self.value)
        self.txt_num.text = self.value
        if info and 'select_value' in info:
            self.select_value = info['select_value']
            min_value = min(self.value, self.select_value)
            self.loadingbar_1.SetPercent(min_value)
            self.txt_num.text = self.select_value
            if self.value < self.select_value:
                self.loadingbar_2.visible = True
                self.loadingbar_3.visible = False
                self.loadingbar_2.SetPercent(self.select_value)
            else:
                self.loadingbar_2.visible = False
                self.loadingbar_3.visible = True
                self.loadingbar_3.SetPercent(self.value)
            diff_value = self.select_value - self.value
            if diff_value > 0:
                self.txt_num_up.visible = True
                self.txt_num_up.text = '#3dd8ff+' + str(diff_value)
            elif diff_value < 0:
                self.txt_num_up.visible = True
                self.txt_num_up.text = '#e74169' + str(diff_value)
            elif diff_value == 0:
                self.txt_num_up.visible = False
        else:
            self.loadingbar_2.visible = False
            self.loadingbar_3.visible = False

        if info and 'default_value' in info:
            self.img_line.visible = True
            self.SetLinePos(info['default_value'])
        else:
            self.img_line.visible = False

    def SetLinePos(self, percent):
        loadingbar_pos = self.loadingbar_1.GetWorldPosition()
        loadingbar_width = self.loadingbar_1.GetWidth()
        final_x = loadingbar_pos.x + loadingbar_width * (percent / 100.0)
        self.img_line.SetWorldPosition(cc.Vec2(final_x, loadingbar_pos.y))


class GunPartFeatureListview(UIListViewCycle):
    FEATURE_NODE_COUNT = 6

    def __init__(self, widget):
        super(GunPartFeatureListview, self).__init__(widget)
        self.InitData()
        self.InitNode()

    def InitData(self):
        self.part_feature_list = ()

    def InitNode(self):
        self.create(
            self.FEATURE_NODE_COUNT, self.OnFeatureListRange,
            node_func=self.FeatureNodeFunc, auto_expand=True,
            hide_redundant=True
        )
        self.EnableTouch(False)

    def FeatureNodeFunc(self, children):
        node = GunPartFeatureListNode(children[0].clone())
        return node

    def OnFeatureListRange(self, irange):
        part_feature_list = self.part_feature_list
        for idx, node in irange:
            node.SetNodeInfo(part_feature_list[idx])

    def SetNodeInfo(self, part_feature_list):
        self.part_feature_list = part_feature_list
        self.total_item_num = len(part_feature_list)
        self.SetHeight(self.GetAllItemsSize().height)
        self.refreshView()

    def SetByPartId(self, part_id):
        self.SetNodeInfo(weapon_util.GetGunPartFeatureList(part_id))


class GunPartFeatureListNode(HelperNode):
    def __init__(self, widget):
        super(GunPartFeatureListNode, self).__init__(None, widget)
        self.InitData()
        self.InitNode()
        self.cur_listview = None

    def InitNode(self):
        self.txt_des = self.seek('txt_des', UIText)
        self.listview_jia = self.seek('listview_jia', UIListViewCycle)
        self.listview_jian = self.seek('listview_jian', UIListViewCycle)
        self.listview_kong = self.seek('listview_kong', UIListViewCycle)
        self.listview_jia.create(item_num=3, callback=self.ListviewRefreshJia, hide_redundant=True)
        self.listview_jian.create(item_num=3, callback=self.ListviewRefreshJian, hide_redundant=True)
        # self.listview_kong.create(item_num=3, hide_redundant=True)
        self.listview_kong.visible = False
        self.listview_jia.visible = False
        self.listview_jian.visible = False

    def RefreshData(self):
        pass

    def RefreshNode(self):
        pass

    def SetNodeInfo(self, feature):
        self.txt_des.text = feature.get('display_desc', '')
        self.value_level = value_level = weapon_util.GetGunPartFeatureDisplayLevel(feature)
        self.is_up = value_level >= 0
        self.is_normal = feature.get('display_type') == 2
        self.listview_jia.visible = self.is_up
        self.listview_jian.visible = not self.is_up and not self.is_normal
        if self.is_up:
            self.listview_jia.total_item_num = value_level
            self.cur_listview = self.listview_jia
        elif not self.is_normal:
            self.listview_jian.total_item_num = -value_level
            self.cur_listview = self.listview_jian

    def ListviewRefreshJia(self, irange):
        for idx, node in irange:
            if idx >= self.listview_jia.total_item_num:
                node.visible = False
            else:
                node.visible = True

    def ListviewRefreshJian(self, irange):
        for idx, node in irange:
            if idx >= self.listview_jian.total_item_num:
                node.visible = False
            else:
                node.visible = True


class GunSmithInjuryDistributionPanel(UINode):
    TXT_TO_CODE = {
        1: 'head_damage',
        2: 'uppertop_damage',
        3: 'upperbottom_damage',
        4: 'limbs_damage',
        5: 'lower_damage',
        6: 'limbs_damage',
        7: 'limbs_damage',
        8: 'limbs_damage',
    }

    def __init__(self, widget=None):
        super(GunSmithInjuryDistributionPanel, self).__init__(widget)
        self.LoadAnimFromFile('UIScript/node_og_gun_injury_distribution.csb')
        self.root = self.childex('node_injury_distribution.panel_injury_distribution')
        self.img_bg = self.root.seek('img_bg')
        self.listview_distribution = self.root.seek('listview_distribution', UIListViewCycle)
        self.listview_distribution.create(item_num=1, obj_type=GunSmithInjuryDistributionNode,
                                          callback=self.ListviewRefresh)
        self.img_bg_2 = self.root.seek('img_bg_2')

        # 模型图伤害
        self.listview_model_comparison = self.root.seek('listview_model_comparison')
        self.panel_model_distribution_1 = self.listview_model_comparison.seek('panel_distribution_1')
        self.panel_model_distribution_2 = self.listview_model_comparison.seek('panel_distribution_2')
        self.panel_model_distribution_3 = self.listview_model_comparison.seek('panel_distribution_3')
        self.panel_model = self.listview_model_comparison.seek('panel_model')
        self.txt_nodes = []
        self.InitPanelModel(self.panel_model)
        self.PlayAnim('show_arise')

    def InitPanelModel(self, panel):
        for i in range(8):
            damage_txt_node = panel.childex(f'img_line{i+1}.txt_num', UIText)
            self.txt_nodes.append(damage_txt_node)
            damage_txt_node.text = GunSmithModelCtrl.instance().cur_show_equip_case.GetWeaponAttrValue(self.TXT_TO_CODE[i + 1], '')

    def RefreshPanel(self):
        self.listview_distribution.refreshContent()

    def ListviewRefresh(self, irange):
        for idx, node in irange:
            node.SetNodeInfo(None)


class GunSmithInjuryDistributionNode(UINode):
    def __init__(self, widget=None):
        super(GunSmithInjuryDistributionNode, self).__init__(widget)
        self.panel_distribution = self.seek('panel_distribution')
        self.listview = self.seek('lv_item_xiao', UIListViewCycle)
        self.InitData()
        self.listview.create(item_num=7, obj_type=GunSmithInjuryDistributionItemNode, callback=self.ListviewRefresh)

    def InitData(self):
        self.data = gun_smith_show_weapon_data.data

    def SetNodeInfo(self, data=None):
        self.listview.total_item_num = len(self.data)

    def ListviewRefresh(self, irange):
        for idx, node in irange:
            if idx >= self.listview.total_item_num:
                node.visible = False
            else:
                node.visible = True
                node.RefreshNodeInfo(self.data[idx + 1])


class GunSmithInjuryDistributionItemNode(UINode):
    def __init__(self, widget=None):
        super(GunSmithInjuryDistributionItemNode, self).__init__(widget)
        self.img_bg = self.seek('img_bg')
        self.txt_des = self.seek('txt_des', UIText)
        self.txt_num = self.seek('txt_num', UIText)

    def RefreshNodeInfo(self, info):
        if info:
            self.txt_des.text = info.get('name')
            code = info.get('code')
            self.txt_num.text = GunSmithModelCtrl.instance().cur_show_equip_case.GetWeaponAttrValue(code, '')


class EquipRenamePopWindow(HelperWindow):
    """
    装备方案改名
    """
    CSB_NAME = 'UIScript/og_gun_anew_name_pop.csb'
    SCENE_IDS = (ui_define.UI_SCENEID_WINDOW,)
    ZORDER = ui_define.SHOW_LEVEL_WINDOW_TOP

    def InitData(self):
        self.equip = None
        self.ctrl = None

    def InitNode(self):
        self.LoadAnimFromFile(self.CSB_NAME)
        self.PlayAnim('in')
        # 按键
        self.panel_pop = self.seek('root').seek('panel_pop')
        pop_seek = self.panel_pop.seek
        self.btn_ok = pop_seek('btn_ok', UIButton)
        self.btn_ok.onClick = self.Rename
        self.btn_hov = self.btn_ok.seek('img_hov')
        self.btn_ok.SetMouseMoveEventEnable(True)
        self.btn_ok.onMouseHover = self.OnBtnMouseHover
        self.btn_cancle = pop_seek('btn_delete', UIButton)
        self.btn_cancle.onClick = self.ReSetName
        self.input_text = pop_seek('textfield', UITextField)
        self.input_text.place_holder = lang.GUN_SMITH_RENAME_TIPS_PLACE_HOLDER
        self.input_text.onNotify = self.OnCheckText
        self.input_text.SetMouseMoveEventEnable(True)
        self.input_text.onMouseHover = self.OnMouseHover
        self.input_text.onInputState = self.OnInputState
        self.txt_tips = pop_seek('txt_tips', UIText)
        self.txt_tips.visible = False
        self.is_legal = True
        self.esc_btn = self.childex('root.panel_btn.btn_back', GunSmithUnderButton)
        self.esc_btn.OnDoClick = lambda widget=None: self.Close()
        self.slc_img = pop_seek('img_slc')
        self.slc_img.visible = False
        self.slc_img.setCapInsets(cc.Rect(6, 1, 8, 18))
        self.is_hover = False
        self.is_input = False

    @property
    def old_name(self):
        return genv.avatar.backpacks[self.backpack_no].show_name

    def Rename(self, _=None):
        name = self.input_text.text
        if not name or len(name) == 0:
            gui.Prompt(1256)
            return
        if name == self.old_name:
            return
        if not self.is_legal:
            return
        genv.avatar.BackpackModifyName(self.backpack_no, name)
        gui.Prompt(357)
        self.Close()

    def ReSetName(self, widget=None):
        self.input_text.text = ''
        self.OnCheckText()

    def OnShow(self, info):
        self.backpack_no = info['backpack_no']

    def OnMouseHover(self, widget, is_hover):
        self.is_hover = is_hover
        self.slc_img.visible = self.is_input or self.is_hover

    def OnBtnMouseHover(self, widget, is_hover):
        self.btn_hov.visible = is_hover

    def OnInputState(self, is_input):
        self.is_input = is_input
        self.slc_img.visible = is_input or self.is_hover
        self.input_text.opacity = 255 if is_input else 80

    def RefreshNode(self):
        self.input_text.text = ''
        # self.OnCheckText()

    def IsLengthLegal(self):
        return 10 >= len(self.input_text.text)

    def OnCheckText(self, widget=None, char=None, is_down=None):

        def OnRenameSuccess(content, code):
            if self.IsLengthLegal():
                self.txt_tips.visible = False
                self.is_legal = True
            else:
                self.txt_tips.visible = True
                self.txt_tips.text = lang.GUN_SMITH_RENAME_TIPS_1
                self.is_legal = False

            if self.input_text.text == self.old_name:
                self.txt_tips.visible = True
                self.txt_tips.text = lang.GUN_SMITH_RENAME_TIPS_2
                self.is_legal = False
            self.btn_ok.SetEnabled(self.is_legal)

        def OnRenameFailed(content, code):
            self.txt_tips.visible = True
            self.txt_tips.text = lang.GUN_SMITH_RENAME_TIPS_0
            self.is_legal = False
            self.btn_ok.SetEnabled(self.is_legal)

        self.input_text.CheckFilterWord(success_callback=OnRenameSuccess, failed_callback=OnRenameFailed, failed_prompt=False)


class BluePrintRenamePopWindow(EquipRenamePopWindow):
    """
    蓝图方案改名
    """
    def InitData(self):
        self.blueprint_data = None
        self.callback = None

    @property
    def old_name(self):
        return '' if not self.blueprint_data else self.blueprint_data['name']

    def OnShow(self, info):
        self.blueprint_data = info.get('blueprint_data')
        self.idx = info.get('idx')
        self.callback = info.get('callback')

    def Rename(self, _=None):
        name = self.input_text.text
        if not name:
            return
        if name == self.old_name:
            return
        if not self.is_legal:
            return
        genv.avatar.BackpackModifyBlueprintName(self.blueprint_data['gun_id'], self.idx, name)
        gui.Prompt(357)
        if self.callback:
            self.callback(name)
        self.Close()


class RewardType(enum):
    ITEM = 1
    GUN = 2
    SYSTEM_UNLOCK = 3
    GUN_PART = 4
    GUN_SKIN = 5
    GUN_VIEW = 6


def GetGunLevelRewards(gun_id, level):
    rewards = []
    gun_level_data = gun_level_reward_data.data.get(gun_id)
    if not gun_level_data:
        return rewards
    for i in gun_level_data:
        if level != i[0]:
            continue
        reward_data = i[1]
        reward_type = reward_data.get('reward_type')
        if reward_type == 'skin':
            rewards.append({'type': RewardType.GUN_SKIN, 'item_id': reward_data.get('reward_info'), 'item_count': 1})
        elif reward_type == 'part':
            rewards.append({'type': RewardType.GUN_PART, 'item_id': reward_data.get('reward_info'), 'item_count': 1})
        elif reward_type == 'view':
            rewards.append({'type': RewardType.GUN_VIEW, 'item_id': 0, 'item_count': 1})
    return rewards


class GunSmithBlueprintCoverPop(EquipRenamePopWindow):
    """
    蓝图方案覆盖确认
    """
    CSB_NAME = 'UIScript/ig_beginner_guide_name_pop.csb'
    CLOSE_ON_ESC = True

    def InitNode(self):
        self.LoadAnimFromFile(self.CSB_NAME)
        self.PlayAnim('in')
        self.panel_pop = self.seek('root').seek('panel_pop')
        pop_seek = self.panel_pop.seek
        self.img_black = pop_seek('img_black', UITexture)
        self.img_black.visible = False
        self.txt_top = pop_seek('txt_top', UIText)
        self.txt_top.text = lang.GUN_SMITH_BLUEPRINT_POP_TITLE
        self.panel_name = pop_seek('panel_name')
        self.panel_name.visible = False
        self.panel_que = pop_seek('panel_que')
        self.panel_que.visible = True
        self.btn_ok = self.panel_que.seek('btn_ok', UIButton)
        self.btn_ok.onClick = self.OnBtnOk
        self.btn_no = self.panel_que.seek('btn_no', UIButton)
        self.btn_no.onClick = lambda widget=None: self.Close()
        self.txt_tips = self.panel_que.seek('txt_des', UIText)
        self.txt_tips.text = lang.GUN_SMITH_BLUEPRINT_POP_TIPS

    def OnBtnOk(self, widget=None):
        if self.ctrl:
            self.ctrl.OnSaveBlueprint()
        self.Close()

    def OnShow(self, info):
        self.ctrl = info['ctrl']

    def RefreshNode(self):
        pass

    def RefreshData(self):
        pass


class GunSkinMissionNode(UINode):
    TASK_DEFAULT_ICON = 563
    TASK_FINISH_ICON = 564
    TASK_LOCK_ICON = 565

    def __init__(self, widget=None):
        super(GunSkinMissionNode, self).__init__(widget)
        self.txt_name = self.seek('txt_name', UIText)
        self.txt_num = self.seek('txt_num', UIText)
        self.img_icon = self.seek('icon_lock', UITexture)

    def RefreshTask(self, task_id):
        avatar = genv.avatar
        if not avatar:
            return
        task = genv.avatar.GetSkinUnlockTaskById(task_id)
        if not task:
            task = avatar.common_item_unlock_tasks.get(task_id)
        tdata = task_data.data[task_id]
        total_progress = tdata['progress']
        has_progress = total_progress > 1
        task_unlock = avatar.CheckPreTask(task_id)
        self.txt_name.text = tdata['task_desc'].format('#ffffff', task.p if task else 0, total_progress)
        if task_unlock:
            self.opacity = 255
            if has_progress:
                self.txt_num.text = '%s/%s' % (task.p if task else 0, total_progress)
                self.txt_num.visible = True
                self.img_icon.visible = False
            else:
                self.txt_num.visible = False
                self.img_icon.visible = True
                self.img_icon.texture = self.TASK_FINISH_ICON if task and task.completed else self.TASK_DEFAULT_ICON
        else:
            self.opacity = 102
            self.txt_num.visible = False
            self.img_icon.visible = True
            self.img_icon.texture = self.TASK_LOCK_ICON

    def RefreshBp(self, unlock_bp_level):
        act_bp_info = genv.avatar.act_bp_info
        cur_level = act_bp_info.bp_level if act_bp_info else 0
        self.txt_name.text = lang.PART_UNLOCK_BP_LEVEL_CONDITION % (unlock_bp_level,)
        if cur_level > 0:
            self.txt_num.text = '%s/%s' % (min(unlock_bp_level, cur_level), unlock_bp_level)
            self.txt_num.visible = True
            self.img_icon.visible = False
        else:
            self.txt_num.visible = False
            self.img_icon.visible = True
            self.img_icon.texture = self.TASK_DEFAULT_ICON


class GunSmithSkinUnlockTaskNode(UINode):
    TASK_DEFAULT_ICON = 563
    TASK_FINISH_ICON = 564
    TASK_LOCK_ICON = 565

    def __init__(self, widget=None):
        super(GunSmithSkinUnlockTaskNode, self).__init__(widget)
        self.txt_task = self.seek('txt_des', UIText)
        self.panel_yuan = self.txt_task.seek('panel_yuan')
        self.img_num = self.panel_yuan.seek('img_num', UITexture)
        self.txt_num = self.img_num.seek('txt_num', UIText)
        self.img_gou = self.panel_yuan.seek('img_gou', UITexture)
        self.img_lock = self.panel_yuan.seek('img_lock', UITexture)
        self.task_id = None

    def SetNodeInfo(self, info):
        self.task_id = info.get('task_id')
        # task_info = genv.avatar.GetSkinUnlockTaskById(self.task_id)
        tdata = task_data.data[self.task_id]
        total_progress = tdata['progress']
        # has_progress = total_progress > 1
        task_unlock = genv.avatar.CheckPreTask(self.task_id)
        task = genv.avatar.GetSkinUnlockTaskById(self.task_id)
        self.txt_task.text = tdata['task_desc'].format('#ffffff', task.p if task else 0, total_progress)
        if task_unlock:
            self.opacity = 255
            self.img_gou.visible = task and task.completed
            self.img_lock.visible = not self.img_gou.visible
        else:
            self.opacity = 102


class GunSmithSlotLine(Singleton):
    IMG_PATH = 'ui/img_node_og_gun_weapon_modify_v2_item_line'

    def __init__(self):
        self.alpha = 177
        self.alpha_timer = None
        self._gun_model_ctrl = None
        self.scene_node_entity = None
        # [DEBUG]
        genv.slot_line = self
        # [DEBUG]

    @property
    def gun_model_ctrl(self):
        return GunSmithModelCtrl.instance()

    def InitLine(self):
        if self.gun_model_ctrl.model.model.UI:
            self.ClearAllLines()
        self._gun_model_ctrl = GunSmithModelCtrl.instance()

    def CreateLine(self, pos1, pos2, name):
        """
        pos1为2d坐标，Vector3(x, y, 0)
        pos2为3d坐标，Vector3(x, y, z)
        """
        pos2 = MType.Vector3(pos2[0], pos2[1], pos2[2])
        name = str(name)
        if not self.gun_model_ctrl.model.model.UI:
            self.gun_model_ctrl.model.model.UI = MObject.CreateObject("SceneNodeComponent")  # 创建SceneNodeComponent
        cc.FileUtils.getInstance().addTextureSize(self.IMG_PATH, cc.Size(2, 46))
        scene_node = self.scene_node_entity = self.gun_model_ctrl.model.model.UI
        scene_node.CreateConnectedNode(name, self.IMG_PATH, '', 2.0, False)  # 创建ImageComponent
        scene_node.SetSceneNodeInfo(1, 1, MType.Vector3(0, 0, 0), '', name)
        scene_node.SetEndpoint(False, 1, MType.Vector3(pos1.x, pos1.y, 0), name, None)  # 设置图片左边点属性
        scene_node.SetEndpoint(True, 0, pos2, name, None)  # 设置图片右边点属性
        scene_node.VisibleDistance = 10
        connectedNode = ccui.Helper.seekConnectedNodeByName(name, False)
        connectedNode.setOpacity(self.alpha)
        connectedNode.SetProgressType(1)

    def CreateLine3D(self, pos1, pos2, name):
        """
        pos1为3d坐标，Vector3(x, y, z)
        pos2为3d坐标，Vector3(x, y, z)
        """
        pos2 = MType.Vector3(pos2[0], pos2[1], pos2[2])
        name = str(name)
        if not self.gun_model_ctrl.model.model.UI:
            self.gun_model_ctrl.model.model.UI = MObject.CreateObject("SceneNodeComponent")  # 创建SceneNodeComponent
        cc.FileUtils.getInstance().addTextureSize(self.IMG_PATH, cc.Size(2, 46))
        scene_node = self.scene_node_entity = self.gun_model_ctrl.model.model.UI
        scene_node.CreateConnectedNode(name, self.IMG_PATH, '', 2.0, False)  # 创建ImageComponent
        scene_node.SetSceneNodeInfo(1, 1, MType.Vector3(0, 0, 0), '', self.IMG_PATH)
        scene_node.SetEndpoint(False, 0, MType.Vector3(*pos1), name, None)  # 设置图片左边点属性
        scene_node.SetEndpoint(True, 0, pos2, name, None)  # 设置图片右边点属性
        scene_node.VisibleDistance = 10


    def ClearAllLines(self):
        if self.scene_node_entity and self.scene_node_entity.IsValid():
            self.scene_node_entity.RemoveConnectedNode("")
        else:
            self.scene_node_entity = None

    def SetLineVisible(self, name, visible):
        name = str(name)
        if not self.gun_model_ctrl.model.model.UI:
            return
        else:
            scene_node = self.gun_model_ctrl.model.model.UI
            scene_node.SetVisible(visible, name)
            connectedNode = ccui.Helper.seekConnectedNodeByName(name, False)
            if connectedNode:
                connectedNode.runAction(cc.ProgressFromTo.create(0.1, 0, 1))


class GunSmithTabNode(UITabButton):
    CSB_NAME = 'UIScript/node_og_gun_tab_item.csb'

    def __init__(self, widget):
        super(GunSmithTabNode, self).__init__(widget)
        self.InitNode()

    def InitNode(self):
        self.root = self.childex('node_tab.btn_tab')
        self.panel_nml = self.root.seek('panel_nml')
        self.panel_slc = self.root.seek('panel_slc')
        self.SetMouseMoveEventEnable(True)
        self.onMouseHover = self.OnMouseHover
        self.img_panel_slc = self.panel_slc.seek('img_slc')
        self.img_panel_slc.visible = False
        self.txt_slc = self.panel_slc.seek('txt_des', UIText)
        self.txt_nml = self.panel_nml.seek('txt_des', UIText)
        self.img_slc = self.root.seek('img_slc')
        self.img_slc.visible = False
        self.LoadAnimFromFile(self.CSB_NAME)

    def SetNodeInfo(self, data):
        self.data = data
        self.txt_nml.text = data['name']
        self.txt_slc.text = data['name']


    def SetSelect(self, select):
        self.OnSelected(select)

    def OnSelected(self, is_selected):
        self.img_panel_slc.visible = is_selected
        self.txt_nml.visible = not is_selected
        self.txt_slc.visible = is_selected
        self.PlayAnim('slc')

    def OnMouseHover(self, widget, hover):
        self.img_slc.visible = hover
        self.PlayAnim('hold')


class GunSmithAttachmentsCountNode(UINode):
    def __init__(self, widget=None):
        super(GunSmithAttachmentsCountNode, self).__init__(widget)
        self.img_bg = self.seek('img_attachment_bg')
        self.img_attach = self.seek('img_attachment')
        self.img_attach.visible = False

    def SetModify(self, is_modify):
        self.img_attach.visible = is_modify


class GunSmithTagListNode(UINode):
    def __init__(self, widget):
        super(GunSmithTagListNode, self).__init__(widget)
        self.lv_tag_item = self.seek('lv_tag_item', UIListViewCycle)
        self.lv_tag_item.create(item_num=3, obj_type=GunSmithTagNode, callback=self.OnListviewTags, hide_redundant=True)
        self.tag_list = []

    def SetNodeInfo(self, data):
        self.tag_list = data.get('tag_list', [])
        self.lv_tag_item.total_item_num = len(self.tag_list)

    def OnListviewTags(self, irange):
        for i, node in irange:
            if i >= len(self.tag_list):
                node.visible = False
            else:
                node.SetNodeInfo(self.tag_list[i])


class GunSmithTagNode(UINode):
    def __init__(self, widget):
        super(GunSmithTagNode, self).__init__(widget)
        self.txt_des = self.seek('txt_des', UIText)

    def SetNodeInfo(self, data):
        self.txt_des.text = data


class GunSmithBluePrintPropSlotNode(UINode):
    def __init__(self, widget=None):
        super(GunSmithBluePrintPropSlotNode, self).__init__(widget)
        self.EnableTouch(True)
        self.SetMouseMoveEventEnable(True)
        self.root = self.childex('node_item.panel_attachment')
        self.img_bg = self.root.seek('img_bg')
        self.icon_attachment = self.root.seek('icon_attachment', UITexture)
        self.img_slc = self.root.seek('img_slc')
        self.img_hov = self.root.seek('img_hov')
        self.img_slc = self.root.seek('img_slc')
        self.img_slc.visible = False
        self.panel_lock = self.root.seek('panel_lock')
        self.img_lock = self.panel_lock.seek('img_lock')
        self.img_lock.visible = True
        self.SetMouseMoveEventEnable(True)
        self.part_id = None
        self.gun_id = None
        self.ctrl = None
        self.onMouseHover = self.OnHover
        self.is_selected = False
        self.data = None
        self.is_default = False
        self.part_type = None
        self.unlock = True
        # self.onClick = self.OnClick

    def OnClick(self, widget=None):
        self.OnSelect(not self.is_selected)

    def OnHover(self, widget, is_hover):
        self.img_hov.visible = is_hover

    def OnSelect(self, is_selected):
        self.is_selected = is_selected
        self.img_slc.visible = is_selected

    def SetNodeInfo(self, data):
        self.data = data
        if data and 'part_id' in data:
            self.part_id = data['part_id']
            self.icon_attachment.texture = gun_attachments_data.data.get(self.part_id, {}).get('attachment_ui_icon_id')
        if data and 'gun_id' in data:
            self.gun_id = data['gun_id']
        if self.part_id and self.gun_id:
            self.unlock = weapon_util.IsGunPartUnlock(genv.avatar, genv.avatar.gun_infos[self.gun_id].level, self.part_id)
            self.panel_lock.visible = not self.unlock


class GunSlotDetailsNode(UINode):
    def __init__(self, ctrl, widget=None):
        super(GunSlotDetailsNode, self).__init__(widget)
        self.txt_name = self.childex('panel_txt_name_caiqie.txt_des', UIText)
        self.img_icon = self.seek('icon_attachment', UITexture)
        self.listview_tips = self.seek('listview_tips', UIListViewCycle)
        self.panel_close = self.seek('btn_close')
        self.panel_close.EnableTouch(True)
        self.panel_close.onClick = self.OnClose
        self.listview_tips.create(item_num=3, obj_type=GunSlotDetailsBuffNode, callback=self.ListViewRefresh, hide_redundant=True)
        self.data = None
        self.part_type = None
        self.part_id = None
        self.feature = None
        self.can_close = False
        self.buff_num = 0
        self.ctrl = ctrl
        self.LoadAnimFromFile('UIScript/og_gun_modified_arms_v2_tips.csb')

    def SetNodeInfo(self, data):
        self.data = data
        self.part_type = data.get('part_type')
        self.part_id = data.get('part_id')
        self.feature = weapon_util.GetGunPartFeatureList(self.part_id)
        self.buff_num = len(self.feature)
        self.listview_tips.total_item_num = self.buff_num
        self.img_icon.texture = gun_attachments_data.data.get(self.part_id, {}).get('attachment_ui_icon_id')
        self.txt_name.text = gun_attachments_data.data.get(self.part_id, {}).get('attachment_ui_name', '')
        self.PlayAnim('in')

    def ListViewRefresh(self, irange):
        for idx, node in irange:
            if idx >= self.listview_tips.total_item_num:
                node.visible = False
            else:
                node.visible = True
                node.SetNodeInfo(self.feature[idx])

    def OnClose(self, widget=None):
        if self.can_close:
            self.visible = False
            if self.ctrl:
                self.ctrl.CloseTips()


class GunSlotDetailsBuffNode(UINode):
    def __init__(self, widget=None):
        super(GunSlotDetailsBuffNode, self).__init__(widget)
        self.txt_des = self.seek('txt_des', UIText)
        self.listview_fh = self.seek('listview_fh', UIListViewCycle)
        self.listview_fh.create(item_num=3, obj_type=GunSlotDetailsFhNode, callback=self.ListViewRefresh, hide_redundant=True)
        self.type = PartNodeBuffType.KONG
        self.buff_level = 0

    def SetNodeInfo(self, data):
        self.txt_des.text = data.get('display_desc', '')
        self.buff_level = weapon_util.GetGunPartFeatureDisplayLevel(data)
        self.is_up = self.buff_level >= 0
        self.is_normal = data.get('display_type') == 2
        if self.is_up:
            self.type = PartNodeBuffType.JIA
            self.listview_fh.total_item_num = self.buff_level
        elif not self.is_normal:
            self.type = PartNodeBuffType.JIAN
            self.listview_fh.total_item_num = -self.buff_level

    def ListViewRefresh(self, irange):
        for idx, node in irange:
            if idx >= self.listview_fh.total_item_num:
                node.visible = False
            else:
                node.visible = True
                node.SetNodeInfo({'type': self.type})


class GunSlotDetailsFhNode(UINode):
    def __init__(self, widget=None):
        super(GunSlotDetailsFhNode, self).__init__(widget)
        self.img_jia = self.seek('img_jia', UITexture)
        self.img_jian = self.seek('img_jian', UITexture)
        self.img_kong = self.seek('img_kong', UITexture)
        self.RefreshFh()
        self.data = None
        self.type = PartNodeBuffType.KONG
        self.switch_type = {PartNodeBuffType.KONG: self.img_kong, PartNodeBuffType.JIA: self.img_jia, PartNodeBuffType.JIAN: self.img_jian}

    def SetNodeInfo(self, data):
        self.data = data
        self.type = data.get('type')
        self.RefreshFh()
        self.switch_type[self.type].visible = True

    def RefreshFh(self):
        self.img_kong.visible = False
        self.img_jia.visible = False
        self.img_jian.visible = False


class GunSmith3DIndicatorComponent(object):
    def __init__(self):
        self.area_objs = None

    def CreateIndicator(self):
        self.area_objs = GunSmith3DIndicator_v1()

    def ClearIndicator(self):
        if self.area_objs:
            self.area_objs.ui_node.removeFromParent()
            self.area_objs = None


class GunSmith3DIndicator_v1(object):
    # node_og_gun_refit_peizhi_diedai_new_v2_item_gun.csb
    def __init__(self):
        pos = hall_model_pos_data.data[11].get('translation')
        rot = hall_model_pos_data.data[11].get('rotation')
        rot = (degree_to_radian(rot[0]), degree_to_radian(rot[1]), degree_to_radian(rot[2]))
        # pos = self.gun_model_ctrl.cur_show_equip_case.model.position
        # transform = self.gun_model_ctrl.cur_show_equip_case.model.model.Transform
        self.position = position = (pos[0], pos[1], pos[2])
        self.ui_node = GunSmith3DIndicator(gui.CreateWidgetFromFile('UIScript/node_og_gun_refit_peizhi_diedai_new_v2_item_gun.csb'))
        panel_gun = self.ui_node.panel_gun

        rt_node = UIRenderNode(gui.current_scene)
        content_size = panel_gun.getContentSize()
        rt_node.CreateNormal3DUINodeFixPositionGunSmith(self.ui_node, 'gun_indicate', position, rotator=rot, scale=(0.0008, 0.0008, 0.0008), content_size=(content_size.width, content_size.height))
        self.rt_node = rt_node

    def SetPosition(self, pos, scale=(0.01, 0.01, 0.01), rotator=(0, 0, 1)):
        self.position = (pos[0], pos[1], pos[2])
        _pos = cc.Vec3(*pos)
        _scale = cc.Vec3(*scale)
        _rotator = cc.Vec3(*rotator)
        self.rt_node.rt_node.transform(_pos, _scale, _rotator)


class GunSmith3DIndicator(UINode):
    # node_og_gun_refit_peizhi_diedai_new_v2_item_gun.csb
    def __init__(self, widget=None):
        super(GunSmith3DIndicator, self).__init__(widget)
        self.LoadAnimFromFile('UIScript/node_og_gun_refit_peizhi_diedai_new_v2_item_gun.csb')
        self.imgs = []
        self.panel_item = self.seek('panel_item')
        self.panel_gun = self.panel_item.seek('panel_gun', UIButton)
        self.panel_gun.EnableTouch(True)
        self.panel_gun.onMouseHover = self.OnMouseHover
        self.panel_gun.onClick = self.OnClickItem
        self.panel_gun.opacity = 128
        self.up_img_bg = self.panel_gun.seek('img_bg', UITexture)
        self.imgs.append(self.up_img_bg)
        self.txt_gun_name = self.panel_gun.seek('txt_name', UIText)
        self.txt_deco = self.panel_gun.seek('txt_deco', UIText)
        self.txt_lv = self.panel_gun.seek('txt_lve', UIText)
        self.txt_lve_bg = self.txt_lv.seek('img_bg', UITexture)
        self.txt_zf = self.panel_gun.seek('txt_zf', UIText)
        self.img_des = self.txt_zf.seek('img_des', UITexture)
        self.imgs.append(self.img_des)
        self.listview_parts = self.panel_gun.seek('listview_attachment', UIListViewCycle)
        self.listview_parts.create(1, self.OnRefreshGunParts, GunPartSlotImg, hide_redundant=True, auto_expand=True)
        self.imgs.append(self.txt_lve_bg)
        self.panel_go = self.panel_item.seek('panel_go', UIButton)
        self.panel_go.onClick = self.OnClickModify
        self.panel_go.onMouseHover = self.OnMouseHoverGo
        self.panel_go.setSwallowTouches(False)
        self.panel_go.visible = False
        self.down_img_bg = self.panel_go.seek('img_bg', UITexture)
        self.imgs.append(self.down_img_bg)
        self.txt_des = self.panel_go.childex('panel_caiqie.txt_des', UIText)
        self.node_pc = self.panel_go.childex('panel_caiqie.node_pc', functools.partial(KeyNode, 2012, None))
        # self.imgs.append(self.node_pc.img_key)
        self.img_slc = self.panel_gun.seek('img_slc', UITexture)
        self.img_hov = self.panel_gun.seek('img_hov', UITexture)
        self.imgs.append(self.img_slc)
        self.imgs.append(self.img_hov)
        self.img_line_1 = self.panel_gun.seek('panel_starting_point')
        self.img_line_1.visible = False
        self.info = None
        self.slot = consts.BackpackSlot.WEAPON_1
        self.AddAA()
        # self.PlayAnim('in')

    def AddAA(self):
        for img in self.imgs:
            img.EnableAA(cc.AAPARAM_AA_ALL)

    def OnMouseHover(self, widget, hover):
        self.panel_gun.runZoomAction(1.05 if hover else 1)

    def OnMouseHoverGo(self, widget, hover):
        self.panel_go.runZoomAction(1.05 if hover else 1)

    def OnClickItem(self, widget=None):
        if self.IsPlayingAnim():
            return
        from gclient.gamesystem.uigunsmith.gunsmith_select_weapon_window import GunSmithWeaponSelectWindow, GunSmithSubWeaponSelectWindow
        genv.avatar.RecordGunSmithClickLogToServer('2_1', info={'gun_id': self.info.gun_id})
        if self.slot == consts.BackpackSlot.WEAPON_1:
            self.PlayAnim('out_2', callback=lambda: GunSmithWeaponSelectWindow.instance().Show(info={'gun_id': self.info.gun_id, 'backpack_no': self.info.backpack_no, 'gun_slot': self.slot}))
        else:
            self.PlayAnim('out_2', callback=lambda: GunSmithSubWeaponSelectWindow.instance().Show(info={'gun_id': self.info.gun_id, 'backpack_no': self.info.backpack_no, 'gun_slot': consts.BackpackSlot.WEAPON_2}))

    def OnClickModify(self, widget=None):
        if self.IsPlayingAnim():
            return
        from gclient.gamesystem.uigunsmith.gunsmith_modify_weapon_window import GunSmithWeaponModifyWindow
        self.PlayAnim('out_1', callback=lambda: GunSmithWeaponModifyWindow.instance().Show(info={'gun_id': self.info.gun_id, 'backpack_no': self.info.backpack_no, 'gun_slot': self.slot}))

    def OnHoverShow(self, show):
        if show:
            self.StopAnim(reset_to_0=True)
            self.PlayAnim('in')
            self.panel_gun.opacity = 255
            self.panel_go.visible = True
        else:
            self.StopAnim(reset_to_0=True)
            self.PlayAnim('out')
            self.panel_gun.opacity = 128
            self.panel_go.visible = False

    def RefreshInfo(self, info=None):
        if not info:
            return
        self.info = info
        # self.listview_part_count.RefreshRefitCount(len(weapon_util.GetWeaponPartModifyTypeList(self.info.gun_id)))
        # self.listview_lien.create(item_num=self.info.part_slots, obj_type=GunRefitDotNode)
        self.txt_gun_name.text = self.info.gun_ui_proto.get('name')
        gun_level = genv.avatar.gun_infos[self.info.gun_id].level
        self.txt_lv.text = 'Lv%s' % gun_level
        gun_equipment = genv.avatar.backpacks[self.info.backpack_no].GetByGunId(self.info.gun_id)
        self.listview_parts.total_item_num = \
            weapon_util.GetWeaponPartModifyCount(self.info.gun_id, gun_equipment.part_slots)
        self.gun_id = self.info.gun_id
        self.slot = self.info.slot
        if self.slot == consts.BackpackSlot.WEAPON_1:
            self.txt_zf.text = lang.GUNSMITH_MAIN_GUN
        else:
            self.txt_zf.text = lang.GUNSMITH_SUB_GUN
        # self.RefreshReddot()

    def RefreshReddot(self):
        pass

    def OnRefreshGunParts(self, irange):
        pass


class GunSmith3DTouchPanel(UINode):
    def __init__(self, widget=None):
        super(GunSmith3DTouchPanel, self).__init__(widget)
        self.panel_touch = self.seek('panel_jc')
        self.panel_touch.EnableTouch(True)
        self.panel_touch.SetMouseMoveEventEnable(True)
        self.panel_touch.setSwallowTouches(False)
        self.img_bg = self.panel_touch.seek('img_mask')
        self.img_bg.visible = False


class GunPartSlotImg(UINode):
    def __init__(self, widget=None):
        super(GunPartSlotImg, self).__init__(widget)
        self.img = self.seek('img_attachment', UITexture)
        self.img.EnableAA(cc.AAPARAM_AA_ALL)


class GunSmithIndicatorLine(GunSmithSlotLine):
    IMG_PATH = 'ui/img_node_og_gun_weapon_modify_v2_item_line'

    def __init__(self):
        self.lines = []
        self.alpha = 1
        self.alpha_timer = None
        self._gun_model_ctrl = None
        # [DEBUG]
        genv.indicator_line = self
        # [DEBUG]


class GunSmithFireArmProperty(UINode):
    def __init__(self, widget=None):
        super(GunSmithFireArmProperty, self).__init__(widget)
        self.gun_id = None
        self.backpack_no = None
        self.part_slots = None
        self.can_fold = True
        # 展开后属性
        self.panel_prop_attachment = self.seek('panel_attachment', GunSmithAttachment)
        # 属性条
        self.panel_firearms_information = self.childex(
            'panel_firearms_information.panel_firearms_information_0')
        self.ammo_num_txt = self.panel_firearms_information.seek('txt_position_1_num', UIText)
        self.beilv_txt = self.panel_firearms_information.seek('txt_position_2_num', UIText)
        self.listview_firearm = self.panel_firearms_information.seek('listview_firearm', UIListViewCycle)
        self.listview_attachment_1 = self.panel_firearms_information.seek('listview_attachment_1')
        self.listview_attachment_1.visible = False
        self.listview_attachment_2 = self.panel_firearms_information.seek('listview_attachment_2')
        self.listview_attachment_2.visible = False
        self.listview_firearm.create(item_num=6, callback=self.ListviewProps, obj_type=GunPropBarNode)
        self.lisview_peijian = self.childex(
            'panel_firearms_information.panel_attachment.listview_attachment', UIListViewCycle)
        self.lisview_peijian.create(item_num=1, obj_type=GunSmithAttachmentsCountNode, callback=self.ListviewSlotsNum,
                                    hide_redundant=True, auto_expand=True)
        self.peijian_num = self.childex('panel_firearms_information.panel_attachment.txt_num', UIText)
        self.img_bg = self.seek('img_bg', UITexture)
        self.btn_xq = self.panel_prop_attachment.btn_xq
        if not self.img_bg.IsSwallower():
            ui_util.UseBlur(self.img_bg)

    def ListviewProps(self, irange):
        """刷新属性条"""
        self.DoListviewProps(irange)

    def DoListviewProps(self, irange):
        part_slots = genv.avatar.backpacks[self.backpack_no].GetByGunId2(self.gun_id).part_slots
        props_data = weapon_util.GetGunShowPropertyInfo(self.gun_id, part_slots)
        for index, node in irange:
            node.SetNodeInfo(info={'name': lang.GUN_PROPS_NAME_DICT[index],
                                   'value': props_data[index]})

    def ListviewSlotsNum(self, irange):
        """刷新改装槽位"""
        if not self.part_slots:
            part_slots = genv.avatar.backpacks[self.backpack_no].GetByGunId2(self.gun_id).part_slots
        else:
            part_slots = self.part_slots
        modify_num = weapon_util.GetWeaponPartModifyCount(self.gun_id, part_slots)
        for index, node in irange:
            if index >= modify_num:
                node.SetModify(False)
            else:
                node.SetModify(True)

    def JumpToExpCard(self, widget=None):
        from gclient.gamesystem.uigunsmith.gunsmith_exp_card_pop import GunSmithExpCardPopWindow
        GunSmithExpCardPopWindow.instance().Show(info={'ctrl': self, 'gun_id': self.gun_id})

    def RefreshInfo(self, info=None):
        if not info:
            return
        self.gun_id = info['gun_id']
        self.backpack_no = info['backpack_no']
        self.part_slots = info.get('part_slots', None)
        self.can_fold = info.get('can_fold', True)
        self.panel_prop_attachment.btn_xq.visible = self.can_fold

        # 改造和方案蓝图都改
        if not self.part_slots:
            self.part_slots = genv.avatar.backpacks[self.backpack_no].GetByGunId2(self.gun_id).part_slots
        part_slots = self.part_slots
        self.panel_prop_attachment.prop_txt_name.text = gun_name = gun_ui_util.GetGunNameByGunId(self.gun_id)
        self.panel_prop_attachment.prop_txt_name_caiqie.text = gun_name
        gun_level = genv.avatar.gun_infos[self.gun_id].level
        self.panel_prop_attachment.weapon_lv.text = gun_level
        all_num = len(weapon_util.GetWeaponPartModifyTypeList(self.gun_id))
        modify_num = weapon_util.GetWeaponPartModifyCount(self.gun_id, part_slots)
        self.lisview_peijian.total_item_num = all_num
        self.peijian_num.text = '%s/%s' % (modify_num, all_num)
        self.ammo_num_txt.text = weapon_util.GetGunStockCapacity(self.gun_id, part_slots)
        self.beilv_txt.text = lang.GUN_OPTIC_SHORT[weapon_util.GetWeaponPartOpticType(part_slots)]
        self.listview_firearm.refreshContent()


class GunSmithFireArmInformation(UINode):
    # 'node_og_gun_tab_main_modify_v3_information'
    def __init__(self, widget=None):
        super(GunSmithFireArmInformation, self).__init__(widget)
        self.gun_id = None
        self.backpack_no = None
        self.part_slots = None
        self.can_fold = True
        self.is_fold = False
        self.is_concat = False
        self.panel_property = self.seek('panel_property', GunSmithFireArmProperty)
        # 折叠后属性界面
        self.panel_attachment = self.seek('panel_attachment', GunSmithAttachment)
        self.panel_attachment.btn_xq.onClick = self.SwitchDetail
        self.panel_attachment.btn_xq.EnableClickSound(311)
        self.panel_property.panel_prop_attachment.btn_xq.onClick = self.SwitchDetail
        self.img_bg = self.panel_property.img_bg
        self.LoadAnimFromFile('UIScript/node_og_gun_tab_main_modify_v3_information.csb')

    def SwitchDetail(self, widget=None):
        if not self.is_fold:
            self.is_fold = True
            self.panel_attachment.btn_xq.SetEnabled(True)
            self.panel_property.btn_xq.SetEnabled(False)
            self.img_bg.visible = False
            self.PlayAnim('folding')
        else:
            self.is_fold = False
            self.panel_attachment.btn_xq.SetEnabled(False)
            self.panel_property.btn_xq.SetEnabled(True)
            self.img_bg.visible = True
            self.PlayAnim('unfold')

    def JumpToExpCard(self, widget=None):
        from gclient.gamesystem.uigunsmith.gunsmith_exp_card_pop import GunSmithExpCardPopWindow
        GunSmithExpCardPopWindow.instance().Show(info={'ctrl': self, 'gun_id': self.gun_id})

    def RefreshInfo(self, info=None):
        if not info:
            return
        self.gun_id = info['gun_id']
        self.backpack_no = info['backpack_no']
        self.part_slots = info.get('part_slots', None)
        self.can_fold = info.get('can_fold', True)
        if self.can_fold:
            self.is_fold = True
            self.panel_attachment.visible = True
            self.panel_property.btn_xq.SetEnabled(False)
            self.panel_attachment.btn_xq.SetEnabled(True)
            self.img_bg.visible = False
            self.PlayAnim('in_1')
        elif self.is_concat:
            self.img_bg.visible = False
        else:
            self.is_fold = False
            self.panel_attachment.visible = False
            self.panel_property.visible = True
            self.img_bg.visible = True
            self.PlayAnim('in')
        # 改造和方案蓝图都改
        self.panel_attachment.prop_txt_name.text = gun_name = gun_ui_util.GetGunNameByGunId(self.gun_id)
        self.panel_attachment.prop_txt_name_caiqie.text = gun_name
        gun_level = genv.avatar.gun_infos[self.gun_id].level
        self.panel_attachment.weapon_lv.text = gun_level
        self.panel_property.RefreshInfo(info)


class GunSmithFireArmInformationDetail(UINode):
    # node_og_gun_attachment_modify_v2_information
    def __init__(self, widget=None):
        super(GunSmithFireArmInformationDetail, self).__init__(widget)
        self.gun_id = None
        self.backpack_no = None
        self.part_slots = None
        self.part_id = None
        self.panel_advent = self.seek('panel_advent')
        self.panel_tips = self.panel_advent.seek('panel_tips', functools.partial(GunSlotDetailsNode, self))
        self.panel_property = self.panel_advent.seek('node_property', GunSmithFireArmInformation)
        self.panel_property.is_concat = True
        self.img_bg = self.panel_advent.seek('img_bg', UITexture)
        ui_util.UseBlur(self.img_bg)
        self.LoadAnimFromFile('UIScript/node_og_gun_attachment_modify_v2_information.csb')

    def RefreshInfo(self, info=None):
        if not info:
            return
        self.gun_id = info['gun_id']
        self.backpack_no = info['backpack_no']
        self.part_slots = info.get('part_slots', None)
        self.part_id = info.get('part_id', None)
        self.panel_property.RefreshInfo(info)
        self.panel_tips.SetNodeInfo(info)
        self.PlayAnim('in')


class GunSmithFireArmInformationInDetail(GunSmithFireArmInformation):
    # 刷新带增益效果的属性条
    def ListviewProps(self, irange):
        part_slots = genv.avatar.backpacks[self.backpack_no].GetByGunId2(self.gun_id).part_slots
        props_data = weapon_util.GetGunShowPropertyInfo(self.gun_id, part_slots)
        cur_part_slots = self.ctrl.gun_part_slots.copy()
        cur_part_slots[self.part_type]['part_id'] = self.cur_select_part_id
        cur_props_data = weapon_util.GetGunShowPropertyInfo(self.gun_id, cur_part_slots)
        default_parts = weapon_util.GetWeaponDefaultPartSlots(self.gun_id)
        default_props = weapon_util.GetGunShowPropertyInfo(self.gun_id, default_parts)
        for index, node in irange:
            node.SetNodeInfo(info={'name': lang.GUN_PROPS_NAME_DICT[index], 'value': props_data[index],
                                   'select_value': cur_props_data[index], 'default_value': default_props[index]})


class GunSmithFireArmInformationTips(UINode):
    # node_og_gun_firearms_information
    def __init__(self, widget=None):
        super(GunSmithFireArmInformationTips, self).__init__(widget)
        self.gun_id = None
        self.backpack_no = None
        self.part_slots = None
        self.panel_firearms_information = self.seek('panel_firearms_information')
        self.panel_property = self.panel_firearms_information.seek('panel_property', GunSmithFireArmProperty)
        self.panel_tips = self.panel_firearms_information.childex('node_tips.panel_tips', functools.partial(GunSlotDetailsNode, self))
        self.panel_tips.visible = False
        # 配件列表
        self.listview_attachment_1 = self.panel_firearms_information.seek('listview_attachment_1', UIListViewCycle)
        self.listview_attachment_2 = self.panel_firearms_information.seek('listview_attachment_2', UIListViewCycle)
        self.listview_attachment_1.create(5, obj_type=GunSmithBluePrintPropSlotNode, callback=self.ListviewAttach1)
        self.listview_attachment_2.create(5, obj_type=GunSmithBluePrintPropSlotNode, callback=self.ListviewAttach2)
        self.LoadAnimFromFile('UIScript/node_og_gun_firearms_information.csb')

    def RefreshInfo(self, info=None):
        if not info:
            return
        self.gun_id = info['gun_id']
        self.backpack_no = info['backpack_no']
        self.part_slots = info.get('part_slots', None)
        self.panel_tips.visible = False
        # 改造和方案蓝图都改
        self.panel_property.RefreshInfo(info)
        self.listview_attachment_1.refreshContent()
        self.listview_attachment_2.refreshContent()

    def ListviewAttach1(self, irange):
        """蓝图配件列表"""
        part_slots = self.part_slots
        # 可改装配件
        slots_num = len(part_slots)
        part_slots_key = list(part_slots.keys())
        for index, node in irange:
            if index >= slots_num:
                node.visible = False
            else:
                node.visible = True
                info = {'part_id': part_slots[part_slots_key[index]]['part_id'], 'gun_id': self.gun_id}
                node.SetNodeInfo(info)
                node.onClick = self.OnPartSlotSelect
                node.OnSelect(False)
                if node.part_id in weapon_util.GetWeaponDefaultParts(self.gun_id).values():
                    node.EnableTouch(False)
                else:
                    node.EnableTouch(True)

    def ListviewAttach2(self, irange):
        """蓝图配件列表2"""
        part_slots = self.part_slots
        # 可改装配件
        slots_num = len(part_slots)
        part_slots_key = list(part_slots.keys())
        for index, node in irange:
            if index + 5 >= slots_num:
                node.visible = False
            else:
                node.visible = True
                info = {'part_id': part_slots[part_slots_key[index + 5]]['part_id'], 'gun_id': self.gun_id}
                node.SetNodeInfo(info)
                node.onClick = self.OnPartSlotSelect
                node.OnSelect(False)
                if node.part_id in weapon_util.GetWeaponDefaultParts(self.gun_id).values():
                    node.EnableTouch(False)
                else:
                    node.EnableTouch(True)

    def OnPartSlotSelect(self, widget=None):
        for item in self.listview_attachment_1._items:
            if item != widget:
                item.OnSelect(False)
        if self.listview_attachment_2.visible:
            for item in self.listview_attachment_2._items:
                if item != widget:
                    item.OnSelect(False)
        if widget:
            widget.OnClick(widget)
            # 如果不是默认配件则显示详细属性面板
            if widget.is_selected and widget.part_id not in weapon_util.GetWeaponDefaultParts(self.gun_id).values():
                self.panel_tips.SetNodeInfo({'part_id': widget.part_id})
                self.panel_tips.visible = True
            else:
                self.panel_tips.visible = False


class GunSmithAttachment(UINode):
    def __init__(self, widget=None):
        super(GunSmithAttachment, self).__init__(widget)
        self.prop_txt_name = self.seek('txt_name', UIText)
        self.prop_txt_name_caiqie = self.childex('penel_txt_name_caiqie.txt_name', UIText)
        self.weapon_lv = self.seek('txt_lve', UIText)
        self.btn_xq = self.seek('btn_xq', HoverableButton_V2)
        self.btn_xq_hov = self.btn_xq.seek('img_hov', UITexture)
        self.btn_xq.SetMouseMoveEventEnable(True)
        self.btn_xq.setSwallowTouches(False)
        self.btn_xq.EnableClickSound(311)
        # self.btn_xq.onMouseHover = self.OnLvUpHover

    def OnLvUpHover(self, widget, is_hover):
        self.btn_xq_hov.visible = is_hover


class GunArmoryWeaponListItemNode(UINode):
    """
    一个武器Item
    在武器选择和方案选择中使用
    """

    def __init__(self, ctrl, widget):
        super(GunArmoryWeaponListItemNode, self).__init__(widget)
        self.ctrl = ctrl
        self.InitData()
        self.InitNode()
        self.LoadAnimFromFile('UIScript/node_og_gun_tab_main_arsenal_v3_lv_item.csb')

    def InitData(self):
        self.gun_id = 0
        self.gun_name = ''
        self.unlock_tips = ''
        self.is_locked = False

    def InitNode(self):
        self.panel_gun = self.childex('node_gun.panel_gun', HoverableButton_V2)
        self.panel_gun.EnableTouch(True)
        self.panel_gun.SetMouseMoveEventEnable(True)
        self.panel_gun.onClick = self.OnClickEnd
        self.panel_gun.EnableClickSound(334)
        self.panel_nml = self.panel_gun.seek('panel_nml')
        self.nml_bg = self.panel_nml.seek('img_bg')
        ui_util.UseBlur(self.nml_bg)
        self.panel_item = panel_item = self.panel_gun.seek('panel_nml')
        self.txt_des = panel_item.seek('txt_des', UIText)
        self.txt_des.visible = False
        self.panel_slc = self.panel_gun.seek('panel_slc')
        self.img_gou = self.panel_slc.seek('img_gou')
        self.img_gou.visible = False
        self.img_bg = self.panel_slc.seek('img_bg')
        self.img_bg.visible = False
        self.img_slc = self.panel_slc.seek('img_slc')
        self.img_slc.visible = False
        self.panel_weapon = self.panel_gun.seek('panel_weapon')
        self.panel_weapon.EnableTouch(True)
        self.panel_weapon.SetMouseMoveEventEnable(True)
        self.panel_weapon.setSwallowTouches(False)
        self.panel_weapon.onMouseHover = self.OnHover
        self.weapon_name = self.panel_weapon.seek('txt_name', UIText)
        self.weapon_name.text_color = (247, 247, 247)
        self.img_weapon = self.panel_weapon.seek('img_weapon', UIBakeTexture)
        self.lv_listview = self.panel_weapon.seek('listview_attachment', UIListViewCycle)
        self.color_selected = (0, 0, 0)
        self.color_unselected = (247, 247, 247)
        self.panel_lock = self.panel_gun.seek('panel_lock')
        self.lock_img_weapon = self.panel_lock.seek('img_weapon', UIBakeTexture)
        self.lock_tips = self.panel_lock.seek('txt_des', UIText)
        self.panel_lock.visible = False
        self.lv_listview.create(item_num=1, callback=self.ListviewRefresh, hide_redundant=True, auto_expand=True)

    def Refresh(self):
        self.RefreshData()
        self.RefreshNode()

    def SetNodeInfo(self, index):
        self.gun_list = self.ctrl.gun_type_to_list[self.ctrl.TAB_MAP[self.ctrl.cur_tab]]
        if index < len(self.gun_list):
            self.gun_id = self.gun_list[index]
            self.visible = True
        else:
            self.visible = False
        self.Refresh()

    def RefreshData(self):
        if not self.gun_id:
            return
        gun_id = self.gun_id
        avatar = genv.avatar
        gun_info = avatar.gun_infos.get(gun_id)
        gun_ui_data = gun_ui_attri_data.data.get(self.gun_id, {})
        if not gun_info or not gun_ui_data:
            self.gun_id = 0
            return
        self.equip_id = equip_id = gun_info.equip_id
        self.skin_template_id = skin_template_id = gun_info.skin_template_id
        self.guise_template_id = guise_template_id = gun_info.guise_template_id
        self.part_guise_info = gun_info.part_guise_info.dict()
        backpack_no = self.ctrl.backpack_no
        self.part_slots = avatar.backpacks[backpack_no].GetByGunId2(gun_id).part_slots
        bp_id = gun_mod_data.data.get(gun_id, {}).get('gun_blueprint_id', None)
        self.gun_icon = gun_skin_util.GetBlueprintGunIconName(bp_id)

        self.gun_level = gun_info.level
        self.weapon_name.text = gun_ui_util.GetGunNameByGunId(gun_id)

    def RefreshNode(self):
        gun_id = self.gun_id
        gun_info = genv.avatar.gun_infos.get(gun_id)
        self.is_locked = gun_info.lock
        if self.is_locked:
            self.panel_lock.visible = True
            self.panel_item.visible = False
            self.panel_weapon.visible = False
            self.lock_tips.text = gun_ui_util.GetGunUnlockConditionText(gun_id)
            self.lock_img_weapon.LoadGunRealtimeIcon(self.equip_id, self.skin_template_id, self.guise_template_id,
            self.part_slots, self.part_guise_info, is_custom_gun=True, bakeType=7, temp_texture=self.gun_icon)
        else:
            self.panel_lock.visible = False
            self.panel_item.visible = True
            self.panel_weapon.visible = True
        if not gun_id:
            return
        self.weapon_name.text = gun_ui_util.GetGunNameByGunId(gun_id)
        self.img_weapon.LoadGunRealtimeIcon(
            self.equip_id, self.skin_template_id, self.guise_template_id,
            self.part_slots, self.part_guise_info, is_custom_gun=True, bakeType=7, temp_texture=self.gun_icon)
        self.lv_listview.total_item_num = weapon_util.GetWeaponPartModifyCount(
            self.gun_id, self.part_slots)
        self.lv_listview.refreshContent()

    def ListviewRefresh(self, irange):
        for idx, node in irange:
            if idx >= self.lv_listview.total_item_num:
                node.visible = False
            else:
                node.visible = True

    def OnClickEnd(self, widget):
        if not self.is_locked:
            self.OnClickGun(widget)
            genv.avatar.RemoveNewUnlockGun(self.gun_id)
        else:
            self.OnClickLockGun(widget)

    def OnSelected(self, is_selected):
        self.img_bg.visible = is_selected
        self.weapon_name.text_color = self.color_selected if is_selected else self.color_unselected
        is_selected and self.PlayAnim('slc')

    def OnEquipped(self, is_equipped):
        self.img_gou.visible = is_equipped

    def OnHover(self, widget, is_hover):
        if is_hover:
            self.img_weapon.runZoomAction(1.1)
        else:
            self.img_weapon.runZoomAction(1)

    def OnClickGun(self, widget=None):
        if not self.gun_id:
            return
        if widget and not getattr(widget, 'can_click', True):
            return
        gun_id = self.gun_id
        genv.avatar.RemoveNewUnlockGun(gun_id)
        self._OnClickGun(gun_id)

    def OnClickLockGun(self, widget):
        if not self.gun_id:
            return
        self._OnClickGun(self.gun_id)

    def _OnClickGun(self, gun_id):
        if self.is_locked:
            self.ctrl.RefreshWithInfo(info={'gun_id_select': gun_id, 'gun_locked': True})
        else:
            self.ctrl.RefreshWithInfo(info={'gun_id_select': gun_id, 'gun_locked': False})
        slot = weapon_util.GetGunBackpackSlot(gun_id)
        genv.avatar.RecordGunSmithClickLogToServer(
            '16_%s' % (1 if slot == consts.BackpackSlot.WEAPON_1 else 2),
            info={'gun_id': gun_id})

