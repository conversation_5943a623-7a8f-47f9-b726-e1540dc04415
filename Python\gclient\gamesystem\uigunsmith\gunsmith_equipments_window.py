# -*- coding: utf-8 -*-
# author: z<PERSON>shuo19
import functools
import cc
from common.classutils import Components

from gclient.data import hall_model_pos_data
from gclient.framework.camera.cam_trans_utils import degree_to_radian
from gclient.framework.ui.commonnodes.ui_common_button import ButtonData, CommonBottomNode
from gclient.framework.ui.widgets.ui_render_node import UIRenderNode
from gclient.gamesystem.uigunsmith.gunsmith_equipments_tools import EquipRenamePopWindow, GunSmithSlotLine, \
    GunSmith3DIndicatorComponent, GunSmith3DIndicator, GunSmith3DTouchPanel
from gclient.gamesystem.uigunsmith.gunsmith_modify_weapon_window import Gun<PERSON>mithWeaponModifyWindow
from gclient.gamesystem.uiwarehouse.warehouse_reddot_ctrl import WarehouseReddotMgr
from gclient.ui.common.hoverable_button import Hoverable<PERSON>utton
from gclient.ui.common.reddot_node import CheckReddotForWidget
from gshare import consts
from gshare.decorators import Visible<PERSON>all

from gclient import cconst, lang

from gclient.framework.ui import ui_define
from gclient.framework.ui.ui_helper import HelperWindow
from gclient.framework.ui.commonnodes.common_key_node import KeyNode
from gclient.framework.ui.commonnodes.common_tab_list import CommonTabListComponent, BaseTabListItem
from gclient.framework.ui.widgets import UIButton, UITexture, UIText, UINode
from gclient.framework.ui.widgets.ui_bake_texture import UIBakeTexture
from gclient.framework.ui.widgets.ui_listview import UIListView
from gclient.framework.ui.widgets.ui_listview_cycle import UIListViewCycle
from gclient.framework.util import events
from gclient.framework.util.gameinput_controller import ListenPcKey
from gclient.framework.util.desktop_input import DesktopInput

from gclient.gamesystem.uigunsmith.gunsmith_model_ctrl import GunSmithModelCtrl
from gclient.util import gun_skin_util
from gshare import weapon_util
from gshare.consts import BackpackSlot
from gclient.gamesystem.uigunsmith.gunsmith_select_weapon_window import GunSmithWeaponSelectWindow, GunSmithSubWeaponSelectWindow

class GunSmithEquipsBackpackListComponent(CommonTabListComponent):
    def OnClickTabItem(self, tab_type, with_callback=True):
        if tab_type == -1:
            with_callback and self.tab_switch_callback and self.tab_switch_callback(self.cur_tab_type, tab_type)
        else:
            if genv.avatar:
                genv.avatar.RecordGunSmithClickLogToServer(f'1_{tab_type}')
                CommonTabListComponent.OnClickTabItem(self, tab_type, with_callback)


@Components(
    GunSmithEquipsBackpackListComponent,
    GunSmith3DIndicatorComponent
)
class GunSmithEquipsWindow(HelperWindow):
    HALL_LEVEL = cconst.LEVEL_WEAPON_DISPLAY_LIGHTING
    CSB_NAME = 'UIScript/og_gun_refit_peizhi_diedai_new_v2.csb'
    SCENE_IDS = (ui_define.UI_SCENEID_HALL,)
    ZORDER = ui_define.SHOW_LEVEL_WINDOW
    AUTO_OUT_ANIM = False
    AUTO_IN_ANIM = False

    def InitNode(self):
        # 下方按钮
        self.gun_model_ctrl = GunSmithModelCtrl.instance()
        self.line_mgr = GunSmithSlotLine.instance()
        self.panel_btn = self.root_widget.seek('panel_btn')
        self.panel_btn.visible = False
        self.btn_esc = self.panel_btn.seek('btn_back', GunSmithUnderButton)
        self.btn_esc.OnDoClick = self.OnBtnClose
        self.btn_intro = self.panel_btn.seek('btn_introduce', GunSmithUnderButton)
        self.btn_intro.OnDoClick = self.OnClickBtnIntro
        self.btn_rename = self.panel_btn.seek('btn_name', GunSmithUnderButton)
        self.btn_rename.OnDoClick = self.OnClickBtnRename
        self.btn_collect = self.panel_btn.seek('btn_collect', GunSmithUnderButton)
        self.btn_collect_txt = self.btn_collect.seek('txt_des', UIText)
        self.btn_collect.OnDoClick = self.OnClickBtnCollect
        self.node_intro = self.btn_intro.childex('txt_des.node_pc', functools.partial(KeyNode, 2011, None))  # 玩法介绍
        self.node_rename = self.btn_rename.childex('txt_des.node_pc', functools.partial(KeyNode, 2019, None))  # 重命名
        self.node_collect = self.btn_collect.childex('txt_des.node_pc', functools.partial(KeyNode, 2016, None))  # 收藏
        self.node_esc = self.btn_esc.childex('txt_des.node_pc', functools.partial(KeyNode, 2006, None))  # 返回

        # 新版按钮
        self.bottom_node = self.HelperSeek('node_c_btn_hint_bottom', CommonBottomNode)
        self.bottom_node.SetData(
            [
                ButtonData(lang.COMMON_TEXT_RETURN, self.OnBtnClose, cconst.PC_KEY_UI.KEY_ESC, None),
                ButtonData(lang.GAMEMODE_INTRODUCE, self.OnClickBtnIntro, cconst.PC_KEY_UI.KEY_1, None),
            ], [
                ButtonData(lang.GUN_SMITH_BUTTON_COLLECT, self.OnClickBtnCollect, cconst.PC_KEY_UI.KEY_C, None),
                ButtonData(lang.GUN_SMITH_BUTTON_RENAME, self.OnClickBtnRename, cconst.PC_KEY_UI.KEY_R, None),
                ButtonData(lang.GUN_SMITH_BUTTON_AIRDROP, self.OnClickBtnAirdrop, cconst.PC_KEY_UI.KEY_TAB, None),
            ]
        )
        _, right_buttons = self.bottom_node.GetButtons()
        self.btn_favorite = right_buttons[0]

        # 左侧列表
        self._callComponents(
            'init_tab_list', self.root_widget, 'listview_tab', GunSmithEquipsTreeItem,
            tab_switch_callback=self.OnSwitchBackpack
        )
        self._callComponents('init')
        self.LoadAnimFromFile(self.CSB_NAME)
        # 3DUI
        self.ui_node_main = None
        self.ui_node_sub = None
        self.ui_node_main_touch = None
        self.ui_node_sub_touch = None
        self.main_rt_node = None
        self.sub_rt_node = None
        # self.Refresh3DUI()



    def InitData(self):
        self.backpacks = genv.avatar.backpacks
        self.backpack_no = 0
        self.backpack_gun_data = []
        self.backpack_gunpart_data = []
        self.backpack_tactical_data = []
        self.backpack_list_data = ()
        self.is_trial_backpack = False
        self.equip = None
        self.cur_camera_type = cconst.GUN_SMITH_CAMERA_EQUIP
        self.ctrl = None
        self.gun_id_select = None
        self.gun_slots_select = None
        self.reddot_mgr = WarehouseReddotMgr.instance()
        self._callComponents('init_data')
        # [DEBUG]
        gui.gun_smith_eq = self
        # [DEBUG]

    def RefreshGunModelCtrl(self):
        self.gun_model_ctrl.ShowGunSmithEquips(
            self.backpack_no,
            self.backpack_gun_data[0].equip_id,
            self.backpack_gun_data[1].equip_id,
            camera_type=self.cur_camera_type
        )

    @events.ListenTo(events.ON_GUN_SMITH_MODEL_LOADED)
    def RefreshOnModelLoaded(self, is_main):
        if self.real_visible_include_scene:
            self.Refresh3DUILines()
        else:
            self.line_mgr.ClearAllLines()

    @events.ListenTo(events.ON_GUN_SMITH_CAMERA_CHANGED)
    def RefreshOnCameraMove(self):
        if self.real_visible_include_scene:
            self.Refresh3DUI()
            self.Refresh3DUILines()

    def Refresh3DUI(self, ver1=False):
        main_pos = hall_model_pos_data.data[11].get('translation')
        main_rot = hall_model_pos_data.data[11].get('rotation')
        sub_pos = hall_model_pos_data.data[12].get('translation')
        sub_rot = hall_model_pos_data.data[12].get('rotation')
        main_touch_pos = hall_model_pos_data.data[13].get('translation')
        # main_touch_rot = hall_model_pos_data.data[13].get('rotation')
        sub_touch_pos = hall_model_pos_data.data[14].get('translation')
        # sub_touch_rot = hall_model_pos_data.data[14].get('rotation')
        main_rot = (degree_to_radian(main_rot[0]), degree_to_radian(main_rot[1]), degree_to_radian(main_rot[2]))
        sub_rot = (degree_to_radian(sub_rot[0]), degree_to_radian(sub_rot[1]), degree_to_radian(sub_rot[2]))
        if ver1:
            self.panel_gun.visible = False
            if not self.ui_node_main:
                self.ui_node_main = GunSmith3DIndicator(gui.CreateWidgetFromFile('UIScript/node_og_gun_refit_peizhi_diedai_new_v2_item_gun.csb'))
                self.ui_node_main.retain()
                self.root_widget.addChild(self.ui_node_main)
            if not self.ui_node_sub:
                self.ui_node_sub = GunSmith3DIndicator(gui.CreateWidgetFromFile('UIScript/node_og_gun_refit_peizhi_diedai_new_v2_item_gun.csb'))
                self.ui_node_sub.retain()
                self.root_widget.addChild(self.ui_node_sub)
            # 主武器
            main_pos = gui.WorldToScreen(main_pos)
            # 指示器panel
            self.ui_node_main.widget.setRotation3D(cc.Vec3(-6, 2, -11))
            self.ui_node_main.widget.setPosition(main_pos[0], main_pos[1])
            # 副武器
            sub_pos = gui.WorldToScreen(sub_pos)
            # 指示器panel
            self.ui_node_sub.widget.setRotation3D(cc.Vec3(-6, 2, -11))
            self.ui_node_sub.widget.setPosition(sub_pos[0], sub_pos[1])
        else:
            if not self.ui_node_main:
                self.ui_node_main = GunSmith3DIndicator(gui.CreateWidgetFromFile('UIScript/node_og_gun_refit_peizhi_diedai_new_v2_item_gun.csb'))
                self.ui_node_main.retain()
                panel_item = self.ui_node_main.panel_item
                content_size = panel_item.getContentSize()
                self.main_rt_node = UIRenderNode(gui.current_scene)
                self.main_rt_node.CreateNormal3DUINodeFixPositionGunSmith(self.ui_node_main, 'gun_indicate_main', main_pos,
                                                                     rotator=main_rot,
                                                                     scale=(0.0006, 0.0006, 0.0006),
                                                                     content_size=(content_size.width, content_size.height))
                self.main_rt_node.rt_node.retain()
                gui.current_scene.removeChild(self.main_rt_node.rt_node)
                self.root_widget.addChild(self.main_rt_node.rt_node)
            if not self.ui_node_sub:
                self.ui_node_sub = GunSmith3DIndicator(gui.CreateWidgetFromFile('UIScript/node_og_gun_refit_peizhi_diedai_new_v2_item_gun.csb'))
                self.ui_node_sub.retain()
                self.ui_node_sub.slot = consts.BackpackSlot.WEAPON_2
                panel_item = self.ui_node_sub.panel_item
                content_size = panel_item.getContentSize()
                self.sub_rt_node = UIRenderNode(gui.current_scene)
                self.sub_rt_node.CreateNormal3DUINodeFixPositionGunSmith(self.ui_node_sub, 'gun_indicate_sub', sub_pos, rotator=sub_rot,
                                                               scale=(0.0006, 0.0006, 0.0006),
                                                               content_size=(content_size.width, content_size.height))
                self.sub_rt_node.rt_node.retain()
                gui.current_scene.removeChild(self.sub_rt_node.rt_node)
                self.root_widget.addChild(self.sub_rt_node.rt_node)
            # 触摸区域检测
            if not self.ui_node_main_touch:
                self.ui_node_main_touch = GunSmith3DTouchPanel(
                    gui.CreateWidgetFromFile('UIScript/node_og_gun_refit_peizhi_diedai_new_v2_item_gun_jc.csb'))
                self.ui_node_main_touch.retain()
                panel_jc = self.ui_node_main_touch.panel_touch
                panel_jc.onMouseHover = self.OnMouseHoverMain
                content_size = panel_jc.getContentSize()
                self.main_rt_node_touch = UIRenderNode(gui.current_scene)
                self.main_rt_node_touch.CreateNormal3DUINodeFixPositionGunSmith(self.ui_node_main_touch, 'gun_touch_main',
                                                                          main_touch_pos,
                                                                          rotator=main_rot,
                                                                          scale=(0.0006, 0.0006, 0.0006),
                                                                          content_size=(content_size.width, content_size.height))
                self.main_rt_node_touch.rt_node.retain()
                gui.current_scene.removeChild(self.main_rt_node_touch.rt_node)
                self.root_widget.addChild(self.main_rt_node_touch.rt_node)
            if not self.ui_node_sub_touch:
                self.ui_node_sub_touch = GunSmith3DTouchPanel(
                    gui.CreateWidgetFromFile('UIScript/node_og_gun_refit_peizhi_diedai_new_v2_item_gun_jc.csb'))
                self.ui_node_sub_touch.retain()
                panel_jc = self.ui_node_sub_touch.panel_touch
                panel_jc.SetMouseMoveEventEnable(True)
                panel_jc.onMouseHover = self.OnMouseHoverMain
                content_size = panel_jc.getContentSize()
                self.sub_rt_node_touch = UIRenderNode(gui.current_scene)
                self.sub_rt_node_touch.CreateNormal3DUINodeFixPositionGunSmith(self.ui_node_sub_touch, 'gun_touch_sub',
                                                                          sub_touch_pos,
                                                                          rotator=sub_rot,
                                                                          scale=(0.0005, 0.0005, 0.0005),
                                                                          content_size=(content_size.width, content_size.height))
                self.sub_rt_node_touch.rt_node.retain()
                gui.current_scene.removeChild(self.sub_rt_node_touch.rt_node)
                self.root_widget.addChild(self.sub_rt_node_touch.rt_node)


            self.sub_rt_node.ShowRtNode()
            self.main_rt_node.ShowRtNode()
        self.ui_node_main.RefreshInfo(self.backpack_gun_data[0])
        self.ui_node_sub.RefreshInfo(self.backpack_gun_data[1])
        if not self.ui_node_main.IsPlayingAnim():
            self.ui_node_main.PlayAnim('in_1')
        if not self.ui_node_sub.IsPlayingAnim():
            self.ui_node_sub.PlayAnim('in_1')

    def OnMouseHoverMain(self, widget, hover):
        if widget == self.ui_node_main_touch.panel_touch.widget:
            self.ui_node_main.OnHoverShow(hover)
            self.OnMouseHoverGun(self.ui_node_main.info) if hover else self.OnMouseHoverGun(None)
        else:
            self.ui_node_sub.OnHoverShow(hover)
            self.OnMouseHoverGun(self.ui_node_sub.info) if hover else self.OnMouseHoverGun(None)

    def Refresh3DUILines(self):
        # 指示线
        return
        main_gun_pos = self.gun_model_ctrl.cur_show_equip_case.model.position
        sub_gun_pos = self.gun_model_ctrl.cur_show_sub_equip_case.model.position
        if self.main_rt_node and self.main_rt_node.rt_node_translation:
            main_line_pos = self.main_rt_node.rt_node_translation
            self.line_mgr.CreateLine3D(main_line_pos, main_gun_pos, 'main_indicator')
        self.line_mgr.ClearAllLines()
        main_line_pos = self.ui_node_main.img_line_1.SetPosition()
        sub_line_pos = self.ui_node_sub.img_line_1.GetPosition()


        self.line_mgr.CreateLine3D(sub_line_pos, sub_gun_pos, 'sub_indicator')

    def RefreshNode(self):
        avatar = genv.avatar
        if not avatar:
            return
        self.RefreshBackpackList()
        # 枪
        backpack = avatar.backpacks[self.backpack_no]
        self.backpack_gun_data = [backpack.GetBySlot(BackpackSlot.WEAPON_1), backpack.GetBySlot(BackpackSlot.WEAPON_2)]
        self.backpack_gunpart_data = [[], []]
        if self.ui_node_main:
            self.ui_node_main.RefreshInfo(self.backpack_gun_data[0])
        if self.ui_node_sub:
            self.ui_node_sub.RefreshInfo(self.backpack_gun_data[1])
        self.RefreshCollectedBtnState()

    def RefreshBackpackList(self):
        cur_backpack_count = len(genv.avatar.backpacks)
        self.backpack_list_data = list(range(cur_backpack_count))
        if cur_backpack_count < consts.BACKPACK_MAX_COUNT:
            self.backpack_list_data += [-1, ]
        self._callComponents('refresh_tab_list', self.backpack_list_data, with_callback=False)

    def OnMouseHoverGun(self, info):
        if info:
            self.gun_id_select = info.gun_id
            self.gun_slots_select = info.part_slots
        else:
            self.gun_id_select = None
            self.gun_slots_select = None

    def OnSwitchBackpack(self, old, new):
        if new == -1:
            self.OnClickAddBackpack()
        else:
            self.backpack_no = new
            self.Refresh()
            self.RefreshGunModelCtrl()
            if self.ui_node_main:
                self.ui_node_main.StopAnim()
                self.ui_node_main.PlayAnim('in_1')
            if self.ui_node_sub:
                self.ui_node_sub.StopAnim()
                self.ui_node_sub.PlayAnim('in_1')

    def OnShow(self, info):
        genv.camera.UseCameraScriptPoseTick(True)
        self.show_info = info = info or {}
        self.ctrl = info.get('ctrl')
        self.gun_model_ctrl.visible = True
        self.PlayAnim('in')
        for backpack_no in (info.get('backpack_no'), genv.avatar.favorite_backpack_no):
            if backpack_no and 0 <= backpack_no < len(genv.avatar.backpacks):
                self.backpack_no = backpack_no
                break
        else:
            self.backpack_no = 0

    def OnClose(self):
        if genv.camera:
            genv.camera.UseCameraScriptPoseTick(False)
        self.gun_model_ctrl.pre_show_camera_type = None
        self.gun_model_ctrl.visible = False
        self.OnDestroy()

    def AfterShow(self):
        self.SelectBackpack(self.backpack_no)
        # self.RefreshGunModelCtrl()

    def SelectBackpack(self, backpack_no):
        self._callComponents('switch_tab', backpack_no, scroll=True, can_refresh=True)

    def OnWindowVisible(self, value, for_mutex=False, for_switch_scene=False):
        super(GunSmithEquipsWindow, self).OnWindowVisible(value, for_mutex=for_mutex, for_switch_scene=for_switch_scene)
        GunSmithSlotLine.instance().ClearAllLines()
        if value:
            self.Refresh()
            self.PlayAnim('in')
            if for_switch_scene:
                self.SelectBackpack(self.backpack_no)
        else:
            self.line_mgr.ClearAllLines()
            # self.ClearIndicator()

    def OnBtnClose(self, btn):
        self.OnKeyDownEsc()

    def OnClickBtnIntro(self, widget=None):
        genv.avatar.RecordGunSmithClickLogToServer('21')
        gui.Prompt(204)
        
    def OnClickBtnAirdrop(self, widget=None):
        gui.Prompt(204)
        # GunSmithAirdropSupportWindow.instance().Show()
        
    def IsCollectedBackpack(self):
        return genv.avatar.favorite_backpack_no == self.backpack_no

    def OnClickBtnCollect(self, widget=None):
        if self.backpack_no is None:
            return
        if self.IsCollectedBackpack():
            return False
        genv.avatar.RecordGunSmithClickLogToServer(
            f'17_{self.backpack_no}',)
        avatar = genv.avatar
        avatar.BackpackSetFavorite(self.backpack_no)
        gui.Prompt(1257)
        # self.btn_collect_txt.text = '已收藏'

    @events.ListenTo(events.ON_SET_FAVORITE_BACKPACK_NO)
    @VisibleCall
    def OnSetFavoriteBackpack(self):
        self.RefreshBackpackList()
        self.RefreshCollectedBtnState()

    def RefreshCollectedBtnState(self):
        self.btn_favorite.text = '已收藏' if self.IsCollectedBackpack() else '收藏'
        self.btn_collect.SetEnabled(not self.IsCollectedBackpack())

    @ListenPcKey(DesktopInput.KEY_C)
    def OnKeyC(self, is_down):
        if not is_down:
            return
        self.OnClickBtnCollect()

    @ListenPcKey(DesktopInput.KEY_1)
    def OnKey1(self, is_down):
        if not is_down:
            return
        self.OnClickBtnIntro()

    @ListenPcKey(DesktopInput.KEY_F)
    def OnKeyF(self, is_down):
        if not is_down and self.gun_id_select and self.gun_slots_select:
            GunSmithWeaponModifyWindow.instance().Show(
                info={'gun_id': self.gun_id_select, 'backpack_no': self.backpack_no, 'gun_slot': self.gun_slots_select})

    @ListenPcKey(DesktopInput.KEY_R)
    def OnKeyR(self, is_down):
        if not is_down:
            return
        self.OnClickBtnRename()

    def OnClickBtnRename(self, widget=None):
        # EquipRenamePopWindow.ShowInst({'backpack_no': self.backpack_no, 'ctrl': self})
        def OnNameInput(name):
            if name == genv.avatar.backpacks[self.backpack_no].show_name:
                return False, lang.GUN_SMITH_RENAME_TIPS_2
            if 0 < len(name) < 10:
                genv.avatar.BackpackModifyName(self.backpack_no, name)
                gui.Prompt(357)
                return True, ''
            else:
                return False, lang.GUN_SMITH_RENAME_TIPS_1

        gui.InputNotice(
            lang.GUN_SMITH_BUTTON_RENAME,
            genv.avatar.backpacks[self.backpack_no].show_name,
            OnNameInput,
            check_filter_word=True
        )

    def OnClickAddBackpack(self, widget=None):
        genv.avatar.AddNewHallBackpack()
    
    @ListenPcKey(DesktopInput.KEY_TAB)
    def OnKeyTab(self, is_down):
        if not is_down:
            return
        self.OnClickBtnAirdrop()
    
    @events.ListenTo(events.ON_HALL_BACKPACKS_UPDATE)
    @VisibleCall
    def OnHallBackpacksUpdate(self, *args):
        self.RefreshBackpackList()
        self.SelectBackpack(len(genv.avatar.backpacks) - 1)

    @events.ListenTo(events.ON_CUSTOM_BACKPACK_NAME_CHANGED)
    @VisibleCall
    def OnCustomBackpackNameChanged(self, *args):
        self.RefreshBackpackList()

    def OnKeyDownEsc(self):
        genv.messenger.Broadcast(events.ON_RETURN_HALL_MAIN)
        return True


class GunSmithEquipsTreeItem(BaseTabListItem):
    ANIM_FILE = 'UIScript/node_og_gun_refit_peizhi_diedai_new_v2_tab.csb'

    def InitData(self):
        self.is_add_item = False
        self.is_selected = False
        self.is_hovered = False
        self.is_touching = False

    def InitNode(self):
        self.root = self.childex('node_tab.child_lv_1', HoverableButton)
        self.root.setSwallowTouches(False)
        self.root.SetMouseMoveEventEnable(True)
        self.root.onClick = None
        # self.root.onMouseHover = self.OnMouseHover
        self.EnableTouch(True)

        # panel_nml
        self.panel_nml = self.root.seek('panel_nml')
        panel_txt_seek = self.panel_nml.seek
        self.img_love_nml = panel_txt_seek('img_icon')
        self.img_bg_nml = panel_txt_seek('img_bg')
        self.txt_backpack_name_nml = panel_txt_seek('txt_des', UIText)

        # panel_add
        self.panel_add = self.root.seek('panel_add')
        self.img_bg_add = self.panel_add.seek('img_bg')

        # panel_slc
        self.panel_select = self.root.seek('panel_slc')
        self.panel_caiqie = self.panel_select.seek('panel_caiqie')
        self.img_love_slc = self.panel_caiqie.seek('img_icon')
        self.img_bg = self.panel_caiqie.seek('img_bg')
        self.txt_backpack_name_slc = self.panel_caiqie.seek('txt_des', UIText)

        # 边框
        self.img_slc = self.root.seek('img_slc')

    def OnClickTab(self, widget=None):
        super().OnClickTab(widget)
        self.is_touching = False
        self.RefreshDisplayState()

    def SetSelected(self, is_selected):
        super().SetSelected(is_selected)
        self.is_selected = is_selected
        self.RefreshDisplayState()
        is_selected and self.PlayAnim('slc', callback=lambda: self.PlayAnim('slc_loop', is_loop=True))

    def OnMouseHover(self, widget, is_enter):
        self.is_hovered = is_enter
        self.RefreshDisplayState()

    def SetNodeInfo(self, cur_backpack_no, backpack_no, tab_data=None):
        self.is_add_item = is_add = backpack_no == -1
        super().SetNodeInfo(cur_backpack_no, backpack_no, tab_data)
        if is_add:
            pass
        else:
            avatar = genv.avatar
            backpack = avatar.backpacks[backpack_no]
            self.txt_backpack_name_nml.text = bp_name = backpack.show_name
            self.txt_backpack_name_slc.text = bp_name
            self.img_love_nml.visible = is_favorite = avatar.favorite_backpack_no == backpack_no
            self.img_love_slc.visible = is_favorite
        self.RefreshDisplayState()

    def RefreshDisplayState(self):
        self.panel_nml.visible = not self.is_add_item and not self.is_selected
        self.img_bg_nml.visible = not self.is_touching
        self.panel_select.visible = self.is_selected
        self.img_slc.visible = self.is_selected or self.is_hovered or self.is_touching
        self.panel_add.visible = self.is_add_item
        self.img_bg_add.visible = not self.is_touching


class GunSmithEquipsNode(UIButton):
    """
    用来描述所有物品的节点结构
    """
    def __init__(self, widget=None):
        super(GunSmithEquipsNode, self).__init__(widget)
        self.InitData()
        self.InitNode()

    def InitData(self):
        self.info = None
        self.is_trial_backpack = False
        self.is_hovered = False
        self.is_add = False
        self.is_touching = False

    def InitNode(self):
        # 总panel
        # self.EnableTouch(True, False)
        self.onClick = self.OnClickNode
        self.onClickBegin = self.OnClickBegin
        self.onClickCancel = self.OnClickCancel
        self.panel_nml = self.seek('panel_nml')
        self.panel_add = self.seek('panel_add')
        self.img_slc = self.seek('img_slc')
        self.img_click = self.seek('img_dow')
        self.img_slc.visible = False
        self.SetMouseMoveEventEnable(True)
        self.onMouseHover = self.OnMouseHover
        self.panel_add.visible = False
        panel_nml_seek = self.panel_nml.seek
        # panel_nml
        self.txt_lv = panel_nml_seek('txt_des', UIText)
        self.txt_gun_name = panel_nml_seek('txt_name', UIText)
        self.icon_gun = panel_nml_seek('img_weapon', UIBakeTexture)
        self.listview_lien = panel_nml_seek('listview_lien', UIListView)
        self.imgline1 = self.listview_lien.seek('imgline1', UITexture)
        # panel_add
        self.img_bg = self.panel_add.seek('img_bg', UITexture)
        self.txt_name = self.panel_add.seek('txt_name', UIText)
        self.img_add_0 = self.panel_add.seek('img_add_0', UITexture)

    def OnClickNode(self, widget=None):
        self.is_touching = False
        if self.is_add:
            self.OnClickAdd(widget)
        else:
            self.OnClickItem(widget)

    def OnClickAdd(self, widget=None):
        pass

    def OnClickItem(self, widget=None):
        pass

    def OnClickBegin(self, widget=None):
        self.is_touching = True
        self.RefreshDisplayState()

    def OnClickCancel(self, widget=None):
        self.is_touching = False
        self.RefreshDisplayState()

    def OnMouseHover(self, widget, is_enter):
        self.is_hovered = is_enter
        self.RefreshDisplayState()

    def RefreshDisplayState(self):
        self.img_slc.visible = self.is_hovered or self.is_touching
        # self.img_click.visible = self.is_touching

    def RefreshInfo(self, info=None):
        """
        刷新节点信息
        """
        self.RefreshDisplayState()

    def RefreshReddot(self):
        pass


class GunSmithEquipsGunNode(GunSmithEquipsNode):
    """
    枪械节点
    """
    def __init__(self, widget=None):
        super(GunSmithEquipsGunNode, self).__init__(widget)
        self.InitNode()
        self.slot = consts.BackpackSlot.WEAPON_1
        self.reddot_mgr = WarehouseReddotMgr.instance()

    def InitNode(self):
        super(GunSmithEquipsGunNode, self).InitNode()
        self.listview_parts = self.panel_nml.seek('listview_attachment', UIListViewCycle)
        self.listview_parts.create(1, self.OnRefreshGunParts, UINode, hide_redundant=True, auto_expand=True)
        self.listview_parts.setSwallowTouches(False)
        self.SetMouseMoveEventEnable(True)
        self.panel_key = self.seek('panel_tag')
        self.panel_key_hover = self.panel_key.seek('img_hov')
        self.panel_key.EnableTouch(True)
        self.panel_key.SetMouseMoveEventEnable(True)
        self.panel_key.widget.setSwallowMMove(False)
        self.panel_key.onClick = self.OnClickModify
        self.panel_key.onMouseHover = self.OnPanelTagHover
        self.panel_key.visible = False
        self.panel_key.childex('txt_des.node_pc', functools.partial(KeyNode, 2012, None))
        # self.onMouseHover = self.OnMouseHover
        self.is_hovered = False
        self.gun_id = None

    def OnRefreshGunParts(self, irange):
        pass

    def OnClickItem(self, widget=None):
        genv.avatar.RecordGunSmithClickLogToServer('2_1', info={'gun_id': self.info.gun_id})
        GunSmithWeaponSelectWindow.instance().Show(info={'gun_id': self.info.gun_id, 'backpack_no': self.info.backpack_no, 'gun_slot': 1001})

    def OnClickModify(self, widget=None):
        GunSmithWeaponModifyWindow.instance().Show(info={'gun_id': self.info.gun_id, 'backpack_no': self.info.backpack_no, 'gun_slot': self.info.part_slots})

    def RefreshInfo(self, info=None):
        super().RefreshInfo(info)
        if not info:
            return
        self.info = info
        # self.listview_part_count.RefreshRefitCount(len(weapon_util.GetWeaponPartModifyTypeList(self.info.gun_id)))
        # self.listview_lien.create(item_num=self.info.part_slots, obj_type=GunRefitDotNode)
        self.txt_gun_name.text = self.info.gun_ui_proto.get('name')
        gun_level = genv.avatar.gun_infos[self.info.gun_id].level
        self.txt_lv.text = 'Lv%s' % gun_level
        self.icon_gun.LoadGunRealtimeIcon(
            self.info.equip_id, self.info.skin_template_id, self.info.guise_template_id, self.info.part_slots,
            self.info.part_guise_info, is_custom_gun=True,
            temp_texture=gun_skin_util.GetRealGunSkinIcon(
                None, self.info.equip_id, self.info.skin_item_id, self.info.guise_item_id)
        )
        gun_equipment = genv.avatar.backpacks[self.info.backpack_no].GetByGunId(self.info.gun_id)
        self.listview_parts.total_item_num = weapon_util.GetWeaponPartModifyCount(
            self.info.gun_id, gun_equipment.part_slots)
        self.gun_id = self.info.gun_id
        self.RefreshReddot()

    def RefreshReddot(self):
        CheckReddotForWidget(self, self.reddot_mgr.GetWarehouseGunReddot(self.gun_id, cconst.GunSmithDiyType.Guise), show_number=False)

    def OnDoHover(self, info):
        pass

    def OnMouseHover(self, widget, is_enter):
        super().OnMouseHover(widget, is_enter)
        self.panel_key.visible = is_enter
        self.is_hovered = is_enter
        is_enter and self.OnDoHover(self.info)
        not is_enter and self.OnDoHover(None)

    def OnPanelTagHover(self, widget, hover):
        self.panel_key_hover.visible = hover


class GunSmithEquipsSubGunNode(GunSmithEquipsGunNode):
    """副武器节点"""
    def __init__(self, widget=None):
        super(GunSmithEquipsSubGunNode, self).__init__(widget)
        self.slot = consts.BackpackSlot.WEAPON_2

    def OnClickItem(self, _=None):
        genv.avatar.RecordGunSmithClickLogToServer('2_2', info={'gun_id': self.info.gun_id})
        GunSmithSubWeaponSelectWindow.instance().Show(info={'gun_id': self.info.gun_id, 'backpack_no': self.info.backpack_no, 'gun_slot': 1002})


class GunSmithEquipsBombNode(GunSmithEquipsNode):
    """
    投掷物节点
    """
    def RefreshInfo(self, info=None):
        if not info:
            return
        self.info = info
        # self.listview_part_count.RefreshRefitCount(len(weapon_util.GetWeaponPartModifyTypeList(self.info.gun_id)))
        # self.listview_lien.create(item_num=self.info.part_slots, obj_type=self.imgline1)
        self.txt_gun_name.text = self.info.gun_ui_proto.get('name')
        gun_level = genv.avatar.gun_infos[self.info.gun_id].level
        self.txt_lv.text = 'Lv%s' % gun_level
        self.icon_gun.LoadGunRealtimeIcon(
            self.info.equip_id, self.info.skin_template_id, self.info.guise_template_id, self.info.part_slots,
            self.info.part_guise_info, is_custom_gun=True,
            temp_texture=gun_skin_util.GetRealGunSkinIcon(
                None, self.info.equip_id, self.info.skin_item_id, self.info.guise_item_id)
        )

    def OnClickItem(self, widget=None):
        gui.Prompt(204)


class GunSmithEquipsSkillNode(GunSmithEquipsNode):
    """
    枪械节点
    """
    def RefreshInfo(self, info=None):
        if not info:
            return
        self.info = info
        # self.listview_part_count.RefreshRefitCount(len(weapon_util.GetWeaponPartModifyTypeList(self.info.gun_id)))
        # self.listview_lien.create(item_num=self.info.part_slots, obj_type=self.imgline1)
        self.txt_gun_name.text = self.info.gun_ui_proto.get('name')
        gun_level = genv.avatar.gun_infos[self.info.gun_id].level
        self.txt_lv.text = 'Lv%s' % gun_level
        self.icon_gun.LoadGunRealtimeIcon(
            self.info.equip_id, self.info.skin_template_id, self.info.guise_template_id, self.info.part_slots,
            self.info.part_guise_info, is_custom_gun=True,
            temp_texture=gun_skin_util.GetRealGunSkinIcon(
                None, self.info.equip_id, self.info.skin_item_id, self.info.guise_item_id)
        )

    def OnClickItem(self, widget=None):
        gui.Prompt(204)


class GunRefitDotNode(UINode):
    """
    用来描述枪械改装数值的点
    """
    def __init__(self, widget):
        super(GunRefitDotNode, self).__init__(widget)
        self.img_dot = self.seek('img_line1', UITexture)

    def RefreshInfo(self, info):
        if info is None:
            return
        # self.img_dot.color = cconst.WEAPON_PART_QUALITY_COLOR.get(info, (0xd5, 0xd5, 0xd5))
        self.visible = True


class GunSmithUnderButton(UIButton):
    """
    下方按键
    """
    def __init__(self, widget):
        super(GunSmithUnderButton, self).__init__(widget)
        self.img_dow = self.seek('img_dow', UITexture)
        self.img_slc = self.seek('img_slc', UITexture)
        self.SetMouseMoveEventEnable(True)
        self.onMouseHover = self.OnMouseHover
        self.onClick = self.OnClick
        self.onClickBegin = self.OnClickBegin
        self.onClickCancel = self.OnClickCancel

    def OnMouseHover(self, widget, is_hover):
        self.img_slc.visible = is_hover

    def OnClickBegin(self, widget=None):
        # self.img_dow.visible = True
        pass

    def OnClickCancel(self, widget=None):
        # self.img_dow.visible = False
        pass

    def OnClick(self, widget=None):
        # self.img_dow.visible = False
        self.OnDoClick(widget)

    def OnDoClick(self, widget=None):
        pass


