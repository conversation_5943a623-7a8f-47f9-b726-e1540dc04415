# -*- coding: utf-8 -*-
# author: <PERSON><PERSON><PERSON><PERSON><PERSON>
from functools import partial

from gclient import lang
from gclient.framework.ui import ui_define
from gclient.framework.ui.commonnodes.common_reward_list import \
    LevelRewardNode, RewardData, RewardLevelListData, RewardRankNode
from gclient.framework.ui.commonnodes.common_tab_list import <PERSON><PERSON>om<PERSON><PERSON>ab<PERSON>istNode, TabData
from gclient.framework.ui.commonnodes.long_text_cycle_node import CycleTextNode
from gclient.framework.ui.commonnodes.ui_common_button import ButtonData, CommonBottomNode
from gclient.framework.ui.ui_helper import HelperNode, HelperWindow
from gclient.framework.ui.widgets import UIButton, UIText, UINode, UIListViewCycle, UIProgressBar
from gclient.framework.ui.widgets.ui_bake_texture import UIBakeTexture
from gclient.framework.ui.commonnodes.common_key_node import KeyNode, KeyNodesManagerComp
from gclient.framework.ui.widgets.ui_listview_anycycle import UIListViewAnyCycle
from gclient.framework.ui.widgets.ui_texture import UITexture
from gclient.framework.ui.widgets.ui_tab_group_comp import UITabGroupComp
from gclient.framework.util.desktop_input import DesktopInput
from gclient.framework.util.gameinput_controller import ListenPcKey
from gclient.framework.util import events
from gclient.gamesystem.uigunsmith.gun_diy_ui_component import GunSmithDiyType
from gclient.gamesystem.uigunsmith.gun_touch_panel import GunTouchPanel
from gclient.gamesystem.uigunsmith.gunsmith_blueprints import GunSmithBlueprintsPanel, PartBlueprintNodeType
from gclient.gamesystem.uigunsmith.gunsmith_exp_card_pop import GunSmithExpCardPopWindow
import functools
from gclient.gamesystem.uigunsmith.gunsmith_model_ctrl import GunSmithModelCtrl
from gclient.gamesystem.uigunsmith.gunsmith_modify_detail_window import GunSmithModifyDetailWindow
from gclient.gamesystem.uigunsmith.gunsmith_part_blueprint_window import GunSmithPartBlueprintApplyPrompt
from gclient.gamesystem.uigunsmith.gunsmith_recommended_blueprint import GunSmithRecommendedWindow
from gclient.gamesystem.uigunsmith.gunsmith_show_weapon_window import GunSmithWeaponShowWindow
from gclient.gamesystem.uigunsmith.gunsmith_equipments_tools import GunSmithUnderButton, GunPropBarNode, \
    GunSmithUnderSlcButton, GunSmithInjuryDistributionPanel, BluePrintRenamePopWindow, GetGunLevelRewards, \
    GunPartFeatureListview, GunSmithBlueprintCoverPop, GunSmithSkinUnlockTaskNode, GunSmithSlotLine, \
    GunSmithAttachmentsCountNode, GunSmithTagListNode, GunSmithBluePrintPropSlotNode, GunSlotDetailsNode, \
    GunSmithFireArmInformation
from gclient.gamesystem.uigunsmith.gunsmith_guise_modify_panel import GunSmithDiyTabPanel, GunSmithDiyPanel
from gclient.gamesystem.uiwarehouse.warehouse_reddot_ctrl import WarehouseReddotMgr
from gclient.ui.common.reddot_node import CheckReddotForWidget
from gclient.util import gun_ui_util, cdata_util, gun_skin_util
from gshare import weapon_util, consts
from common.classutils import Components
from gclient.data import gun_mod_data, gun_attachments_data, gun_ui_attri_data, gun_tag_data, unlock_sys_data, \
    lobby_item_data, equip_data, gun_achievement_data, task_data
from gshare.decorators import LimitCall, SafeDelayCall
from gshare.utils import enum
from gclient import cconst
from gclient.data import match_data


GUN_MODIFY_NAME_DICT = {
    0: '改造',
    1: '方案',
    2: '外观',
    3: '成就',
}

GUN_MODIFY_NAME_DICT_EN = {
    0: 'MODIFY',
    1: 'SCHEME',
    2: 'GUISE',
    3: 'ACHIEVEMENT',
}

GUN_PROPS_NAME_DICT = {
    0: '伤害',
    1: '射程',
    2: '后坐力',
    3: '精度',
    4: '机动性',
    5: '射速',
}


class WeaponModifyPanel(HelperNode):
    pass


class WeaponBluePrintPanel(HelperNode):
    def InitNode(self):
        self.panel_default_scheme = self.seek('panel_default_scheme')
        self.down_txt_des = self.panel_default_scheme.seek('txt_des', UIText)
        self.down_txt_des.text = lang.GUN_SMITH_BLUEPRINT_DIY
        self.top_txt_des = self.panel_default_scheme.childex('panel_txt.txt_des', UIText)
        self.top_txt_des.text = lang.GUN_SMITH_BLUEPRINT_NOW
        self.panel_gun = self.panel_default_scheme.seek('panel_gun', partial(GunSmithBluePrintNode, self))

    def RefreshNode(self):
        self.panel_gun.RefreshInfo(info={'idx': 0, 'node_type': PartBlueprintNodeType.CurBlueprint, 'gun_id': self.ctrl.gun_id})


@Components(UITabGroupComp, KeyNodesManagerComp)
class GunSmithWeaponModifyWindow(HelperWindow):
    """枪械改装"""
    CSB_NAME = 'UIScript/og_gun_tab_main_modify.csb'
    SCENE_IDS = (ui_define.UI_SCENEID_WINDOW,)
    ZORDER = ui_define.SHOW_LEVEL_WINDOW

    SLOT_NUM = 10  # 改造配件槽数量
    HALL_LEVEL = cconst.LEVEL_WEAPON_DISPLAY_LIGHTING

    def InitData(self):
        self.ctrl = None
        self._close_on_esc = True
        self._gun_model_ctrl = None
        self.gun_id_list = []
        self.gun_id = 1
        self.backpack_no = 1
        self.offset = 50.0  # tab栏提示按钮偏移量
        self.gun_slot = 1001  # 主副武器插槽
        self.gun_part_type_list = []
        self.tab_nodes = []
        self.slot_nodes = []
        self.cur_blueprint_data = []
        self.gun_type_to_list = {}
        self.gun_part_slots = {}
        self.gun_record_data = {}
        self.cur_gun_slot = 1001
        self.temp_gun_part_slots = {}  # 用来临时保存方案
        self.cur_tab = 0
        self.cur_blueprint_node = None
        self.show_detail_info = False
        self.cur_camera_type = cconst.GUN_SMITH_CAMERA_MODIFY
        self.show_line_timer = None
        self.reddot_mgr = WarehouseReddotMgr.instance()
        self._callComponents("init")
        # [DEBUG]
        gui.gun_smith_modify = self
        # [DEBUG]

        self.extra_close_cb = None

    @property
    def gun_model_ctrl(self):
        return GunSmithModelCtrl.instance()

    def InitNode(self):
        root_seek = self.HelperSeek
        self.img_bg = root_seek('img_bg')
        self.img_bg.visible = False
        self.panel_top = root_seek('panel_top')

        # 改造主页
        self.panel_weapon_modify = root_seek('panel_weapon_modify', WeaponModifyPanel)
        self.panel_model = self.panel_weapon_modify.seek('panel_model')  # 滑动模型区域
        self.panel_property = self.panel_weapon_modify.seek('node_firearms_information', GunSmithFireArmInformation)
        self.panel_attachment = self.panel_weapon_modify.seek('panel_attachment')

        # 伤害详情
        self.panel_injury = root_seek('panel_injury_distribution', GunSmithInjuryDistributionPanel)
        self.panel_injury.visible = False

        # 改造配件栏
        self.InitWeaponSlot(self.panel_attachment)

        # 改造底部新按钮
        self.bottom_node = self.panel_weapon_modify.seek('node_c_btn_hint_bottom', partial(CommonBottomNode, self))
        self.bottom_node.SetData([
            ButtonData(lang.COMMON_TEXT_RETURN, self.OnBtnClose, cconst.PC_KEY_UI.KEY_ESC, None),
            ButtonData(lang.GUN_SMITH_BUTTON_INSPECT, self.OnClickBtnInspect, cconst.PC_KEY_UI.KEY_B, None),
            ButtonData(lang.GUN_SMITH_BUTTON_SHOOTING_RANGE, self.OnClickShootingRange, cconst.PC_KEY_UI.KEY_2, None),
        ], [
            ButtonData(lang.GUN_SMITH_BUTTON_SUGGESTION, self.OnClickBtnSuggest, cconst.PC_KEY_UI.KEY_TAB, None),
            ButtonData(lang.GUN_SMITH_BUTTON_DETAIL_DAMAGE, self.OnClickBtnDetails, cconst.PC_KEY_UI.KEY_1, None),
        ])
        _, rights = self.bottom_node.GetButtons()

        # 方案
        self.panel_blueprints = root_seek('panel_modified_arms', partial(GunSmithBlueprintsPanel, self))

        # 外观
        self.panel_guise_modify = root_seek('panel_stickery', GunSmithDiyPanel)
        sub_tab = self.panel_top.childex('panel_top_fu')
        sub_tab.visible = True
        self.panel_guise_tab = self.panel_top.childex('panel_top_fu.node_tab', partial(GunSmithDiyTabPanel, self))
        self.panel_guise_modify._parent = self
        self.panel_guise_tab.visible = False

        self.panel_guise_modify.SetGunId(self.gun_id)
        self.panel_guise_tab.SetGunId(self.gun_id)
        self.panel_guise_tab.onTabChangeCallback = partial(self.panel_guise_modify.SetGuiseType)
        self.SetGuisePanelVisible(False)

        # 成就
        self.panel_achievement = root_seek('panel_achievement', GunSmithAchievementPanel)
        self.panel_achievement.panel_btn.SetData(left=[
            ButtonData(lang.COMMON_TEXT_RETURN, lambda widget=None: self.Close(), cconst.PC_KEY_UI.KEY_ESC, None),
        ])
        self.panel_achievement.btn_back.OnDoClick = self.OnBtnClose
        self.panel_achievement.visible = False
        self.LoadAnimFromFile('UIScript/og_gun_tab_main_modify.csb')
        self.InitPanelModelTouch()
        # 顶部tab
        self.InitTopTab()

    def InitTopTab(self):
        self.panel_tab_root = self.panel_top.seek('node_tab', partial(HallCommonTabListNode, self))
        self.panel_tab_root.SetData(lang.WAREHOUSE_GUN_DIY_TITLE, [
            TabData(GUN_MODIFY_NAME_DICT[0], callback=partial(self.OnTabClicked, 0), lock_func=partial(self.CheckTabOpen, 0), text_en=GUN_MODIFY_NAME_DICT_EN[0]),
            TabData(GUN_MODIFY_NAME_DICT[1], callback=partial(self.OnTabClicked, 1), lock_func=partial(self.CheckTabOpen, 1), text_en=GUN_MODIFY_NAME_DICT_EN[1]),
            TabData(GUN_MODIFY_NAME_DICT[2], callback=partial(self.OnTabClicked, 2), lock_func=partial(self.CheckTabOpen, 2), red_func=self.CheckRed, text_en=GUN_MODIFY_NAME_DICT_EN[2]),
            TabData(GUN_MODIFY_NAME_DICT[3], callback=partial(self.OnTabClicked, 3), lock_func=partial(self.CheckTabOpen, 3), text_en=GUN_MODIFY_NAME_DICT_EN[3]),
        ])
        # self.panel_tab_root.SetKeyNodeEnable()
        # 初始化QEtab栏
        self.InitTabGroup(self.panel_tab_root.GetTabNodes(), 0)

    @events.ListenTo(events.ON_UPDATE_GUNSMITH_GUN_PART_SLOT)
    @events.ListenTo(events.ON_SET_GUNSMITH_EQUIP_SLOT)
    @events.ListenTo(events.ON_SET_GUNSMITH_GUN_PART_SLOT)
    def InitGunModel(self, *args):
        if self.real_visible_include_scene:
            # self.gun_model_ctrl.cur_show_camera_type = self.cur_camera_type
            self.gun_model_ctrl.visible = True
            self.gun_model_ctrl.SelectEquip(
                self.backpack_no, gun_mod_data.data[self.gun_id]['equip_id'], self.cur_camera_type,
                # equip_loaded_callback=self.OnGunEquipLoaded,
                camera_extra={'offset': (0, 0.02, 0.07)}, equip_loaded_callback=self.InitLines)
            # self.gun_model_ctrl.SelectCameraType(103)
            # self.gun_model_ctrl.ShowGunSmithModify(self.backpack_no, self.gun_id,
            #   camera_type=self.cur_camera_type, equip_loaded_callback=self.InitLines)

    def InitLines(self):
        if self.real_visible_include_scene:
            for node in self.slot_nodes:
                node.CreateLine()
                # 创建成功后也不能立即打开，需要等UI加载完
                node.SetLineVisible(False)

    def InitWeaponSlot(self, panel_attach):
        if not panel_attach:
            return
        for i in range(self.SLOT_NUM):
            slot = panel_attach.seek('panel_item%s' % str(i + 1), partial(WeaponModifySlotNode, self))
            slot.SetNodeNum(i)
            slot.SetNodeCtrl(self)
            self.slot_nodes.append(slot)

    def InitPanelModelTouch(self):
        self.panel_gun_touch = GunTouchPanel(self.gun_model_ctrl, self.panel_model)
        self.panel_gun_touch.EnableDoubleTouchZoom(True)
        self.panel_gun_touch.visible = True
        self.panel_gun_touch.on_move_callback = partial(self.ShowWeaponSlot, False)

    @events.ListenTo(events.ON_GUN_SMITH_ROTATE_END)
    def ShowWeaponSlot(self, visible=True):
        if self.real_visible_include_scene:
            if self.cur_tab != 0:
                return
            for slot in self.slot_nodes:
                if slot.valid:
                    slot.visible = visible
                    slot.SetLineVisible(visible)

    def OnShow(self, info):
        self.panel_tab_root.PlayAnim('in')
        self.panel_guise_tab.PlayAnim('in')
        if info and 'gun_id' in info:
            self.gun_id = info['gun_id']
        if info and 'backpack_no' in info:
            self.backpack_no = info['backpack_no']
        if info and 'gun_slot' in info:
            self.gun_slot = info['gun_slot']
        self.show_detail_info = False
        # 提前刷一下插槽以免加载武器后插槽内gun_id不对导致连线出错
        self.panel_tab_root.SetDefaultTab(0)
        self.RefreshWeaponSlot()
        self.InitGunModel()
        self.OnTabClicked(0)
        self.panel_blueprints.SetInfo(info={'gun_id': self.gun_id, 'backpack_no': self.backpack_no})
        avatar = genv.avatar
        avatar.CallServer('GetGunInfo', avatar.id, self.gun_id, 'OnGetGunCombatData')

    def CloseTips(self):
        self.OnPartSlotSelect()

    def RefreshNode(self):
        """
        更新所有UI
        """
        # 更新top栏
        # self.top_listview_tab.Refresh()
        # 更新插槽数据
        self.RefreshGunPartSlots()
        # 更新插槽UI
        self.RefreshWeaponSlot()
        # 更新属性面板
        self.RefreshGunProperty()
        # 默认进入枪械改装界面
        self.OnTabClicked(self.cur_tab)
        # 更新蓝图属性，在蓝图界面才刷新
        self.panel_blueprints.RefreshBlueprintProperty(self.cur_blueprint_node)
        # 更新成就
        self.RefreshRewards()
        # 更新蓝图方案栏
        # self.panel_blueprints.RefreshBluePrint()
        self.SetDetailInfoPanelVisible(self.show_detail_info)
        self.panel_guise_modify.SetGunId(self.gun_id)
        self.panel_guise_tab.SetGunId(self.gun_id)

    def RefreshTabModify(self):
        self.RefreshGunPartSlots()

    def RefreshSlotLines(self, visible):
        if not visible or self.cur_tab != 0:
            if self.show_line_timer:
                self.cancel_timer(self.show_line_timer)
                self.show_line_timer = None
        if self.cur_tab != 0:
            visible = False
        for node in self.slot_nodes:
            node.SetLineVisible(visible)

    def RemoveAllLines(self):
        GunSmithSlotLine.instance().ClearAllLines()

    def RefreshDetailInfo(self):
        pass

    def RefreshBluePrint(self):
        self.panel_gun.RefreshInfo(info={'idx': 0, 'node_type': PartBlueprintNodeType.CurBlueprint, 'gun_id': self.gun_id})
        self.panel_gun.onClick = self.OnSelectBlueprint

    @events.ListenTo(events.ON_UPDATE_GUNSMITH_GUN_PART_SLOT)
    def RefreshGunProperty(self, *args):
        """更新属性"""
        self.panel_property.RefreshInfo(info={'gun_id': self.gun_id, 'backpack_no': self.backpack_no})

    def RefreshWeaponSlot(self):
        """更新槽位节点"""
        final_type_list = consts.WeaponPartTypeShowSequenceInGunSmith.copy()
        part_modify_type_list = weapon_util.GetWeaponPartModifyTypeList(self.gun_id)

        def CheckTypeIsValid(part_type):
            # 检查当前可改槽位是否有可装配配件存在，如果没有照样不显示
            cur_part_list = weapon_util.GetWeaponPartIdListByType(self.gun_id, part_type)
            new_part_list = []
            for part_id in cur_part_list:
                if cdata_util.GetCanUnlockPart(part_id):
                    new_part_list.append(part_id)
            # 因为包含默认装配，需要大于1
            if len(new_part_list) > 1:
                return True
            else:
                return False
        for node in self.slot_nodes:
            idx = self.slot_nodes.index(node)
            part_type = final_type_list[idx]
            if part_type in part_modify_type_list and CheckTypeIsValid(part_type):
                node.visible = True
                node.valid = True
            else:
                node.visible = False
                node.valid = False
            node.RefreshInfo(
                info={'gun_id': self.gun_id, 'part_type': part_type,
                      'part_id': self.temp_gun_part_slots.get(part_type, {}).get('part_id', 0), 'backpack_no': self.backpack_no})
        self.RefreshWeaponSlotReddot()

    def RefreshWeaponSlotReddot(self):
        for node in self.slot_nodes:
            ret_type = genv.avatar.HasNewUnlockGunPartType(self.gun_id, node.part_type)
            if ret_type:
                CheckReddotForWidget(node.btn_item, True, show_number=False)
                continue
            part_list = weapon_util.GetWeaponPartIdListByType(self.gun_id, node.part_type)
            default_part_id = weapon_util.GetGunDefaultPartId(self.gun_id, node.part_type)
            for part_id in part_list:
                # 屏蔽默认配件的红点
                if part_id == default_part_id:
                    continue
                if genv.avatar.HasNewUnlockGunPart(self.gun_id, part_id):
                    CheckReddotForWidget(node.btn_item, True, show_number=False)
                    continue
            CheckReddotForWidget(node.btn_item, False, show_number=False)

    def RefreshGunModelCtrl(self):
        """刷新暂时选取的配件"""
        for part in self.temp_gun_part_slots.values():
            self.gun_model_ctrl.ChangeWeaponPart(part.get('part_id'))

    def RefreshGunPartSlots(self):
        """更新slot数据"""
        self.gun_part_slots.clear()
        self.temp_gun_part_slots.clear()
        part_slots = self.gun_model_ctrl.GetWeaponParts()
        for type, part in part_slots.items():
            self.gun_part_slots[type] = {'part_id': part.get('part_id')}
        self.temp_gun_part_slots = self.gun_part_slots.copy()

    def ResetGunPartSlots(self):
        """更新当前装配数据"""
        backpack = genv.avatar.backpacks[self.backpack_no]
        cur_gun = backpack.GetByGunId2(self.cur_gun_id)
        if not cur_gun:
            return
        part_slots = cur_gun.part_slots
        for type, part in part_slots.items():
            self.gun_part_slots.setdefault(type, {})['part_id'] = part['part_id']

    def RefreshRewards(self):
        """刷新奖励"""
        self.panel_achievement.gun_id = self.gun_id
        self.panel_achievement.SetData(self.BuildRewardListData())
        self.panel_achievement.ChangeGunPart = self.ChangeGunPart
        self.panel_achievement.ChangeGunSkin = self.ChangeGunSkin
        self.panel_achievement.JumpToLevel(1)
        selected_level = self.panel_achievement.GetListByLevel(2)
        if selected_level:
            selected_level.Click(0)

    def ChangeGunPart(self, part_id):
        self.gun_model_ctrl.ResetWeaponSkin()
        self.gun_model_ctrl.ResetWeaponPart()
        if part_id:
            self.gun_model_ctrl.ChangeWeaponPart(part_id)

    def CheckSlotsEqual(self, parts1, parts2):
        """
        检查parts是否相同
        """
        if not parts1 and not parts2:
            return False
        # 正向遍历，如果发现不一致，则返回False
        for k in parts1:
            if k not in parts2:
                return False
            if parts1[k].get('part_id') != parts2[k].get('part_id'):
                return False
        # 反向遍历，只要键全有就可以，排除子集
        for j in parts2:
            if j not in parts1:
                return False
        return True

    def ChangeGunSkin(self, skin_id):
        # self.gun_model_ctrl.ResetWeaponSkin()
        if skin_id:
            self.gun_model_ctrl.ChangeWeaponSkin(skin_id)

    def BuildRewardListData(self):
        """生成奖励列表数据"""
        res = []
        cur_gun_level = genv.avatar.gun_infos[self.gun_id].level
        for i in range(100):
            level = i + 1
            rewards = GetGunLevelRewards(self.gun_id, level)
            # rewards = career_utils.GetLevelRewards(level)
            if len(rewards) == 0:
                continue
            list_reward = []
            for reward in rewards:
                list_reward.append(RewardData(reward['item_id'], reward['item_count'], reward['type']))
            res.append(RewardLevelListData(
                list_reward,
                level,
                cur_gun_level >= level,
                False,
                level == cur_gun_level + 1,
            ))
        return res

    def ListviewTopCallback(self, irange):
        """top栏"""
        for index, node in irange:
            node.SetNodeInfo(data={'name': list(GUN_MODIFY_NAME_DICT.values())[index]})
            node.onClick = self.OnTabClicked
            node.onCheckCanSelect = partial(self.CheckTabOpen, index)
            # 检查外观红点
            if index == 2:
                ret_guise = self.reddot_mgr.GetWarehouseGunReddot(self.gun_id, cconst.GunSmithDiyType.Guise)
                ret_skin = self.reddot_mgr.GetWarehouseGunReddot(self.gun_id, cconst.GunSmithDiyType.Skin)
                ret_Hangings = self.reddot_mgr.GetWarehouseGunReddot(self.gun_id, cconst.GunSmithDiyType.Hangings)
                ret_Decal = self.reddot_mgr.GetWarehouseGunReddot(self.gun_id, cconst.GunSmithDiyType.Decal)
                CheckReddotForWidget(node, ret_guise or ret_skin or ret_Hangings or ret_Decal, show_number=False)

    def CheckRed(self):
        ret_guise = self.reddot_mgr.GetWarehouseGunReddot(self.gun_id, cconst.GunSmithDiyType.Guise)
        ret_skin = self.reddot_mgr.GetWarehouseGunReddot(self.gun_id, cconst.GunSmithDiyType.Skin)
        ret_Hangings = self.reddot_mgr.GetWarehouseGunReddot(self.gun_id, cconst.GunSmithDiyType.Hangings)
        ret_Decal = self.reddot_mgr.GetWarehouseGunReddot(self.gun_id, cconst.GunSmithDiyType.Decal)
        return ret_guise or ret_skin or ret_Hangings or ret_Decal

    def ListviewSlotsNum(self, irange):
        part_slots = genv.avatar.backpacks[self.backpack_no].GetByGunId2(self.gun_id).part_slots
        modify_num = weapon_util.GetWeaponPartModifyCount(self.gun_id, part_slots)
        for index, node in irange:
            if index >= modify_num:
                node.SetModify(False)
            else:
                node.SetModify(True)

    def OnWindowVisible(self, value, for_mutex=False, for_switch_scene=False):
        super(GunSmithWeaponModifyWindow, self).OnWindowVisible(value, for_mutex=for_mutex, for_switch_scene=for_switch_scene)
        if value:
            self.RefreshNode()
            # 等配件更新后调用
            self.InitGunModel()
            # self.gun_model_ctrl.SelectCameraType(self.cur_camera_type)
            self.OnTabClicked(self.cur_tab)
        else:
            self.RefreshSlotLines(False)
            self.RemoveAllLines()
            if self.show_line_timer:
                self.cancel_timer(self.show_line_timer)

    def OnTempGunPartSlotChange(self, part_type, part_id):
        self.temp_gun_part_slots[part_type] = {'part_id': part_id}
        self.RefreshWeaponSlot()
        self.TryEquipGunAndPart()

    def OnPartSlotSelect(self, widget=None):
        for item in self.prop_listview_attachment_1._items:
            if item != widget:
                item.OnSelect(False)
        if self.prop_listview_attachment_2.visible:
            for item in self.prop_listview_attachment_2._items:
                if item != widget:
                    item.OnSelect(False)
        if widget:
            widget.OnClick(widget)
            # 如果不是默认配件则显示详细属性面板
            if widget.is_selected and widget.part_id not in weapon_util.GetWeaponDefaultParts(self.gun_id).values():
                self.prop_slot_details.SetNodeInfo({'part_id': widget.part_id})
                self.prop_slot_details.visible = True
            else:
                self.prop_slot_details.visible = False

    @events.ListenTo(events.ON_SET_GUN_BLUEPRINT)
    def OnSetGunBlueprint(self):
        if self.real_visible_include_scene:
            gui.Prompt(357)
            self.panel_blueprints.cur_blueprint_data = genv.avatar.gun_blueprints[self.gun_id]
            self.panel_blueprints.blueprint_listview.refreshContent()
            self.panel_blueprints.RefreshBlueprintProperty(self.cur_blueprint_node)

    @events.ListenTo(events.ON_SET_GUNSMITH_EQUIP_SLOT)
    @events.ListenTo(events.ON_SET_GUNSMITH_GUN_PART_SLOT)
    @events.ListenTo(events.ON_UPDATE_GUNSMITH_GUN_PART_SLOT)
    @events.ListenTo(events.ON_UPDATE_GUNSMITH_EQUIP_SLOT)
    def OnSetGunSmithEquipSlot(self, *args):
        if self.real_visible_include_scene:
            self.TryRefresh()

    @events.ListenTo(events.ON_AVATAR_FETCH_LBS_GUN_INFO)
    def OnGetGunCombatData(self, data):
        if not self.visible:
            return
        if not data:
            return
        if data['id'] != genv.avatar.id:
            return
        if self.gun_id != data['gun_id']:
            return
        self.gun_record_data = data
        self.panel_achievement.RefreshGunRecord(data)

    @SafeDelayCall(delay=1)
    def OnApply(self, _=None):
        weapon_type = 1 if weapon_util.GetGunBackpackSlot(self.gun_id) == consts.BackpackSlot.WEAPON_1 else 2
        genv.avatar.RecordGunSmithClickLogToServer(f'8_{weapon_type}', info={'gun_id': self.gun_id})
        self.TryEquipGunAndPart()

    @events.ListenTo(events.ON_BACKPACK_HALL_EQUIPMENT_PROPERTY)
    def OnBackpackHallEquipmentProperty(self, key, new):
        if self.real_visible_include_scene:
            self.panel_guise_modify.OnBackpackHallEquipmentProperty(key, new)
            self.panel_guise_tab.OnBackpackHallEquipmentProperty(key, new)

    @events.ListenTo(events.ON_GUN_REDDOT_UPDATE)
    @events.ListenTo(events.ON_WAREHOUSE_REDDOT_ITEM_BTN)
    def OnRefreshAllReddot(self):
        # self.top_listview_tab.Refresh()
        self.panel_guise_tab.OnRefreshAllReddot()
        self.panel_guise_modify.OnRefreshAllReddot()
        self.RefreshWeaponSlotReddot()

    def CheckTabOpen(self, tab_type, is_mouse):
        # 检测不通过不会执行OnTabClicked
        if tab_type not in [0, 1, 2, 3]:
            gui.Prompt(204)
        return tab_type in [0, 1, 2, 3]

    def OnTabClicked(self, idx):
        if not self.CheckTabOpen(idx, True):
            gui.Prompt(204)
            return False
        self.cur_index = self.cur_tab = idx
        self.OnTabRefresh()
        return True

    def OnBtnClose(self, btn):
        self.Close()

    def OnClose(self):
        self.panel_gun_touch.visible = False
        self.BakeIconOnClose()
        # todo
        if self.extra_close_cb:
            self.extra_close_cb()
            self.extra_close_cb = None

    def OnClickBtnSuggest(self, btn=None):
        gui.Prompt(204)
        return
        weapon_type = 1 if weapon_util.GetGunBackpackSlot(self.gun_id) == consts.BackpackSlot.WEAPON_1 else 2
        genv.avatar.RecordGunSmithClickLogToServer(f'7_{weapon_type}', info={'gun_id': self.gun_id})
        GunSmithRecommendedWindow.instance().Show(info={'gun_id': self.gun_id, 'backpack_no': self.backpack_no})
        # gui.Prompt(204)

    def OnTabRefresh(self):
        # 关闭详细伤害面板
        self.panel_injury.visible = False
        self.show_detail_info = False
        if self.cur_tab == 0:
            self.panel_gun_touch.visible = True
            if self.gun_model_ctrl.CheckWeaponTransOnModify():
                self.gun_model_ctrl.SelectCameraType(cconst.GUN_SMITH_CAMERA_MODIFY)
            else:
                self.InitGunModel()
            self.panel_weapon_modify.visible = True
            self.panel_blueprints.visible = False
            self.SetGuisePanelVisible(False)
            self.panel_achievement.visible = False
            self.PlayAnim('in')
            self.RefreshGunPartSlots()
            self.RefreshWeaponSlot()
            self.show_line_timer = self.add_timer(0.3, partial(self.RefreshSlotLines, True))
        elif self.cur_tab == 1:
            self.panel_gun_touch.visible = False
            self.gun_model_ctrl.SelectCameraType(cconst.GUN_SMITH_CAMERA_BLUEPRINT)
            self.panel_weapon_modify.visible = False
            self.panel_blueprints.visible = True
            self.PlayAnim('in')
            self.SetGuisePanelVisible(False)
            self.panel_achievement.visible = False
            self.panel_blueprints.RefreshAll()
            self.RefreshSlotLines(False)
        elif self.cur_tab == 2:
            self.panel_gun_touch.visible = False
            self.panel_weapon_modify.visible = False
            self.panel_blueprints.visible = False
            self.panel_achievement.visible = False
            self.SetGuisePanelVisible(True)
            self.RefreshSlotLines(False)
        elif self.cur_tab == 3:
            self.panel_gun_touch.visible = False
            self.gun_model_ctrl.SelectCameraType(cconst.GUN_SMITH_CAMERA_ACHIEVEMENT)
            self.panel_weapon_modify.visible = False
            self.panel_blueprints.visible = False
            self.SetGuisePanelVisible(False)
            self.RefreshSlotLines(False)
            self.panel_achievement.visible = True
            self.panel_achievement.PlayAnim('in')

    def SetGuisePanelVisible(self, visible):
        self.panel_guise_modify.SetVisible(visible)
        self.panel_guise_tab.visible = visible

        if visible:
            self.panel_guise_modify.Refresh()
            self.panel_guise_tab.PlayAnim('in')

    @LimitCall(1)
    def TryEquipGunAndPart(self, callback=None, is_close_window=False, equip_to_backpack=False):
        avatar = genv.avatar
        if not avatar:
            return

        def apply_suc_cb(bp_no, gun_id):
            callback and callback()
            # gui.Prompt(696, extra_data=(bp_no + 1,))  # 提示应用成功
            self.gun_part_slots = self.temp_gun_part_slots.copy()
        avatar.BackpackSetWeaponParts(self.backpack_no, self.gun_id, self.temp_gun_part_slots, ignore_unlock=True)
        apply_suc_cb(self.backpack_no, self.gun_id)
        self.gun_model_ctrl.SetWeaponParts(self.temp_gun_part_slots)

    @SafeDelayCall(0)
    def TryRefresh(self):
        self.gun_part_slots = genv.avatar.backpacks[self.backpack_no].GetByGunId2(self.gun_id).part_slots.copy()
        self.RefreshGunPartSlots()
        self.RefreshWeaponSlot()

    def OnClickBtnInspect(self, widget=None):
        genv.avatar.RecordGunSmithClickLogToServer(
            f'34_{1 if weapon_util.GetGunBackpackSlot(self.gun_id) == consts.BackpackSlot.WEAPON_1 else 2}',
            info={'gun_id': self.gun_id})
        GunSmithWeaponShowWindow.ShowInst({'ctrl': self})

    def OnClickShootingRange(self, widget=None):
        gun_slot = weapon_util.GetGunBackpackSlot(self.gun_id)
        genv.avatar.RecordGunSmithClickLogToServer(
            f'9_{1 if gun_slot == consts.BackpackSlot.WEAPON_1 else 2}',
            info={'gun_id': self.gun_id})
        genv.avatar.ChangeMatchInfo(
            cconst.MatchType.ShootingRange, team_number=1,
            match_spacenos=match_data.data[cconst.MatchType.ShootingRange]['space'])
        genv.avatar.CallServer(
            "RequestMatch", cconst.MatchType.ShootingRange,
            {'initial_backpack_no': self.backpack_no,
             'initial_weapon_slot': gun_slot})

    def SetDetailInfoPanelVisible(self, visible):
        self.show_detail_info = visible
        self.panel_injury.visible = visible
        visible and self.panel_injury.PlayAnim('show_arise')
        visible and self.panel_injury.RefreshPanel()
        # self.bp_btn_details.SetLock(visible)
        if visible:
            self.RefreshDetailInfo()

    def OnClickBtnDetails(self, widget=None):
        self.SetDetailInfoPanelVisible(not self.show_detail_info)

    # region key listeners

    @ListenPcKey(DesktopInput.KEY_Q)
    def OnKeyQ(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        self.HandleButtonL(is_down)

    @ListenPcKey(DesktopInput.KEY_E)
    def OnKeyE(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        self.HandleButtonR(is_down)

    @ListenPcKey(DesktopInput.KEY_B)
    def OnKeyB(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        if not is_down:
            return
        self.OnClickBtnInspect()

    @ListenPcKey(DesktopInput.KEY_2)
    def OnKey2(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        if not is_down:
            return
        self.OnClickShootingRange()

    @ListenPcKey(DesktopInput.KEY_1)
    def OnKey1(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        if not is_down:
            return
        self.OnClickBtnDetails()

    @ListenPcKey(DesktopInput.KEY_V)
    def OnkeyV(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        if not is_down or self.cur_tab != 1:
            return
        self.OnClickBtnCover()

    @ListenPcKey(DesktopInput.KEY_TAB)
    def OnKeyTab(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        if not is_down:
            return
        self.OnClickBtnSuggest()

    @ListenPcKey(DesktopInput.KEY_SPACE)
    def OnKeySpace(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        if not is_down:
            return
        if self.panel_guise_modify.visible:
            self.panel_guise_modify.OnKeySpaceClicked(is_down)
            return

    def OnKeyDownEsc(self):
        # 优先关闭视频窗口
        if self.panel_achievement.panel_video.visible:
            self.panel_achievement.panel_video.visible = False
        else:
            self.Close()
        return True

    @ListenPcKey(DesktopInput.KEY_A)
    def OnKeyA(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        if self.real_visible and self.panel_guise_tab.visible:
            self.panel_guise_tab.HandleButtonL(is_down)

    @ListenPcKey(DesktopInput.KEY_D)
    def OnKeyD(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        if self.real_visible and self.panel_guise_tab.visible:
            self.panel_guise_tab.HandleButtonR(is_down)

    @ListenPcKey(DesktopInput.KEY_F)
    def OnKeyF(self, is_down=True):
        if not self.real_visible_include_scene:
            return
        if self.real_visible and self.panel_guise_modify.visible:
            self.panel_guise_modify.OnKeyFClicked(is_down)

    # endregion key listeners

    def ShowDiyPanel(self, backpack_no, diy_type, item_id, gun_id, extra_close_cb=None):
        gun_slot = weapon_util.GetGunBackpackSlot(gun_id)
        self.Show(info={'gun_id': gun_id, 'backpack_no': backpack_no, 'gun_slot': gun_slot})
        self.OnTabClicked(2)
        self.RefreshSlotLines(False)
        self.panel_guise_tab.DoSelectByIdx(diy_type)
        # self.panel_guise_modify.DoSelectByIdx(diy_type)
        self.extra_close_cb = extra_close_cb

    def BakeIconOnClose(self):
        avatar = genv.avatar
        if not avatar:
            return
        from gclient.util.iconbake import BakeIconHelper
        backpack = avatar.backpacks[self.backpack_no]
        equip = backpack.GetByGunId2(self.gun_id)
        slot_no = backpack.QueryEquipSlot(equip.guid)
        # BakeIconHelper.BakeBackpackEquipRealtimeWhiteIcon(self.backpack_no, slot_no=slot_no, bakeType=7)
        BakeIconHelper.BakeAllGunRealtimeWhiteIcons()


class GunSmithAchievementPanel(UINode):
    ITEM_COUNT = 16
    MAX_GUN_TAG_COUNT = 3
    RECORD_KEYS = (
        consts.GUN_ACHIEVEMENT_ID_KILL,
        consts.GUN_ACHIEVEMENT_ID_KILL_HEADSHOT,
        consts.GUN_ACHIEVEMENT_ID_MAX_DAMAGE,
        consts.GUN_ACHIEVEMENT_ID_WIN,
    )

    def __init__(self, widget):
        super(GunSmithAchievementPanel, self).__init__(widget)
        self.root = self.childex('node_achievement.root')
        self.img_bg = self.root.seek('img_bg')
        self.img_bg.visible = False
        self.panel_weapon_select = self.root.seek('panel_weapon_select')
        self.panel_property = self.panel_weapon_select.seek('panel_property')
        self.btn_back = self.panel_weapon_select.childex('panel_btn.node_btn.root.btn_back', GunSmithUnderButton)

        # rewards面板
        self.panel_rewards = self.panel_weapon_select.seek('panel_rewards')
        self.loadingbar = self.panel_rewards.seek('loadingbar', UIProgressBar)  # 进度条
        self.lv_rewards = self.panel_rewards.seek('lv_rewards', UIListViewAnyCycle)  # 奖励
        self.btn = self.panel_weapon_select.seek('node_bg')
        self.register_types = [
            ('l', partial(GunSmithRewardsNode, self)),
            ('s', partial(GunSmithRewardsNode, self)),
        ]
        self.lv_rewards.create(self.ITEM_COUNT, self.register_types, self.GenRewardLevelType, callback=self.OnListViewLevelReward, scroll_callback=self.SetLoadingBar)
        # 属性面板
        self.panel_attachment_1 = self.panel_property.seek('panel_attachment_1')
        self.lv_tag = self.panel_property.seek('lv_tag', UIListViewCycle)
        self.lv_tag.create(item_num=1, obj_type=GunSmithTagListNode, callback=self.OnListviewTags)
        self.lv_data = self.panel_property.seek('lv_data', UIListViewCycle)
        self.lv_data.create(item_num=4, obj_type=GunSmithRecordNode, callback=self.OnListviewRecord)
        self.txt_name = self.panel_attachment_1.seek('txt_name', UIText)
        self.txt_lve = self.panel_attachment_1.seek('txt_lve', UIText)
        self.btn_lve_up = self.panel_attachment_1.seek('btn_lve_up', UIButton)
        self.btn_lve_up.onClick = lambda w=None: GunSmithExpCardPopWindow.instance().Show(info={'ctrl': self, 'gun_id': self.gun_id})
        self.panel_attachment_2 = self.panel_property.seek('panel_attachment_2')
        self.panel_attachment_2.visible = False
        self.txt_des = self.panel_attachment_2.seek('txt_des', UIText)
        self.listview_attachment = self.panel_attachment_2.seek('listview_attachment', GunPartFeatureListview)
        # self.listview_attachment.create(item_num=3, obj_type=GunSlotDetailsBuffNode, callback=self.OnListviewProps)
        # 皮肤解锁任务
        self.panel_lock_panel = self.panel_weapon_select.seek('panel_lock')
        self.panel_lock = self.panel_weapon_select.childex('panel_lock.node_lock.panel_lock')
        self.panel_lock.visible = True
        self.txt_skin_name = self.panel_lock.seek('txt_lock', UIText)
        self.lv_lock = self.panel_lock.seek('lv_lock', UIListViewCycle)
        self.lv_lock.create(item_num=2, obj_type=GunSmithSkinUnlockTaskNode, callback=self.OnListviewSkinTask)
        self.tasks = []

        # 下方按钮
        self.panel_btn = self.panel_weapon_select.seek('node_c_btn_hint_bottom', partial(CommonBottomNode, self))

        # self.panel_btn.SetData([
        #     ButtonData(lang.COMMON_TEXT_RETURN, lambda widget=None: self.Close(), cconst.PC_KEY_UI.KEY_ESC, None),
        # ])
        # self.panel_btn_new.visible = True
        # 播放视频
        self.panel_video = self.panel_weapon_select.seek('panel_video')
        self.panel_video.visible = False
        self.panel_play = self.panel_video.seek('panel_play')
        self.img_video = self.panel_play.seek('img_video', UITexture)
        # self.OnSroll = None
        self.data = None
        self.gun_id = 1
        self.last_get_node = None
        self.next_reward_node = None
        self.node_offset = 0
        self.cur_gun_level = 1
        self.LoadAnimFromFile('UIScript/og_gun_achievement.csb')

    def SetData(self, data, reward_select_callback=None, scroll_callback=None):
        self.data = data
        self.OnRewardSelect = reward_select_callback
        self.OnScroll = scroll_callback
        gun_ui_data = gun_ui_attri_data.data[self.gun_id]
        gun_tag_ids = gun_ui_data.get('gun_tag_id', ())[:self.MAX_GUN_TAG_COUNT]
        gun_tag_getter = gun_tag_data.data.get
        self.gun_tag_texts = [gun_tag_getter(gun_tag_id, {}).get('name', '') for gun_tag_id in gun_tag_ids]
        self.cur_gun_level = genv.avatar.gun_infos[self.gun_id].level
        self.RefreshNode()

    def RefreshGunRecord(self, data):
        gun_combat_data = data.get('gun_combat_data', {})
        record_dict = {}
        for record in gun_combat_data.values():
            for record_key, record_value in record.items():
                if record_key not in record_dict:
                    record_dict[record_key] = 0
                record_dict[record_key] += record_value
        self.record_list = [(record_key, record_dict.get(record_key, 0)) for record_key in self.RECORD_KEYS]
        self.lv_data.refreshContent()

    def GenRewardLevelType(self, idx):
        level_data = self.data[idx]
        return 'l' if level_data.is_big_reward else 's'

    def OnListviewRecord(self, irange):
        for i, node in irange:
            node.SetNodeInfo(*self.record_list[i])

    def OnListviewTags(self, irange):
        for i, node in irange:
            node.SetNodeInfo(data={'tag_list': self.gun_tag_texts})

    def OnListviewProps(self, irange):
        pass

    def OnListviewSkinTask(self, irange):
        for i, node in irange:
            if i < self.lv_lock.total_item_num:
                node.visible = True
                node.SetNodeInfo(info={'task_id': self.tasks[i]})
            else:
                node.visible = False

    def SetLoadingBar(self):
        if not self.last_get_node:
            self.loadingbar.SetPercent(0)
        elif self.last_get_node.data.level <= self.cur_gun_level:
            node_pos_x = self.last_get_node.dian.GetWorldPosition().x
            loading_bar_pos_x = self.loadingbar.GetWorldPosition().x
            loading_bar_width = self.loadingbar.GetWidth()
            length = node_pos_x - loading_bar_pos_x
            percent = (length / loading_bar_width) * 100
            # 计算到下一阶段的进度
            offset_percent = (self.node_offset / loading_bar_width) * 100
            if offset_percent >= 0:
                percent = percent + offset_percent
            if 0 <= percent <= 100:
                self.loadingbar.SetPercent(percent)
            if percent <= 0:
                self.loadingbar.SetPercent(0)
            if percent >= 100:
                self.loadingbar.SetPercent(100)
        else:
            self.loadingbar.SetPercent(100)

    def OnListViewLevelReward(self, irange):
        self.list_levels = []
        self.last_get_node = None
        for i, node in irange:
            node.SetData(self.data[i])
            size = node.getContentSize()
            node.getParent().setContentSize(size)
            node.setContentSize(size)
            self.list_levels.append(node)
            # 进度条
            if self.data[i].has_got and node.visible:
                self.last_get_node = node
            if self.data[i].is_next_reward:
                self.next_reward_node = node
                self.CalculateOffset()
        self.SetLoadingBar()

    def CalculateOffset(self):
        if self.next_reward_node and self.last_get_node:
            node_pos_x = self.last_get_node.dian.GetWorldPosition().x
            next_reward_node = self.next_reward_node
            next_length = next_reward_node.dian.GetWorldPosition().x - node_pos_x
            # 计算枪械到下一级的经验
            gun_info = gun_ui_util.GetGunInfoByGunId(self.gun_id)
            level_progress = gun_info.GetExpProgressFromCurLevel(self.next_reward_node.data.level, self.last_get_node.data.level)
            self.node_offset = level_progress * next_length

    def RefreshNode(self):
        self.lv_tag.refreshContent()
        len_data = len(self.data)
        self.lv_rewards.total_item_num = len_data
        self.txt_name.text = gun_ui_util.GetGunNameByGunId(self.gun_id)
        gun_level = genv.avatar.gun_infos[self.gun_id].level
        self.txt_lve.text = 'Lv%s' % gun_level

    def OnChildClick(self, level, index, type, id, widget):
        for ls in self.list_levels:
            ls.SetSelect(level, index)
        self.ShowRewardContent(False, type, id, widget)

    def ShowRewardContent(self, is_stage_reward, type, id, widget, task_dats=None):
        if not self.visible:
            return
        self.panel_lock_panel.visible = False
        if type == RewardType.GUN_VIEW:
            self.panel_attachment_2.visible = False
            self.ChangeGunPart and self.ChangeGunPart(None)
        elif type == RewardType.GUN_PART:
            self.ChangeGunPart and self.ChangeGunPart(id)
            self.panel_attachment_2.visible = True
            part_data_getter = gun_attachments_data.data.get(id, {}).get
            self.txt_des.text = part_data_getter('attachment_ui_name', '')
            self.listview_attachment.SetByPartId(id)
        elif type == RewardType.GUN_SKIN:
            self.panel_attachment_2.visible = False
            self.ChangeGunPart and self.ChangeGunPart(None)

            self.tasks = list(genv.avatar.GetSkinUnlockTasks(id))
            skin_item_proto_get = lobby_item_data.data.get(id, {}).get
            self.txt_skin_name.text = skin_item_proto_get('name', '')
            skin_id = skin_item_proto_get('skin', {}).get('template_id', 0)
            self.lv_lock.total_item_num = len(self.tasks)
            if len(self.tasks) > 0:
                self.panel_lock_panel.visible = True
            else:
                self.panel_lock_panel.visible = False
            self.ChangeGunSkin and self.ChangeGunSkin(skin_id)

    def JumpToLevel(self, level):
        self.lv_rewards.JumpToIndexForVaryingLengthItem(level)

    def GetListByLevel(self, level):
        for ls in self.list_levels:
            if ls.data.level == level:
                return ls
        return None

    def GetCurIndex(self):
        return self.lv_rewards.GetCurIndex()

    def GetItemNum(self):
        return self.lv_rewards.item_num

    def AdjustSizeByContent(self):
        return self.lv_rewards.AdjustSizeByContent()


class GunSmithRecordNode(UINode):
    def __init__(self, widget):
        super(GunSmithRecordNode, self).__init__(widget)
        self.txt_num = self.seek('txt_num', UIText)
        self.txt_des = self.seek('txt_des', UIText)

    def SetNodeInfo(self, record_key, record_value):
        self.txt_num.text = record_value
        self.txt_des.text = gun_achievement_data.data[record_key]['desc']


class RewardType(enum):
    ITEM = 1
    GUN = 2
    SYSTEM_UNLOCK = 3
    GUN_PART = 4
    GUN_SKIN = 5
    GUN_VIEW = 6


class GunSmithRewardsNode(HelperNode):
    GUN_INSPECTION_ICON_ID = 11321

    def InitNode(self):
        self.img_bg = self.seek('img_lve_num_bg')
        self.listview_reward = self.seek('lve_item', UIListViewCycle)
        self.txt_num = self.seek('txt_num', UIText)
        self.txt_tips = self.seek('txt_tips')
        self.node_rank = self.seek('node_rank_lve', partial(RewardRankNode, self))
        self.listview_reward.create(item_num=4, callback=self.OnListViewReward, obj_type=partial(LevelRewardNode, self),
                                    hide_redundant=True)
        self.listview_reward.SetScrollEnabled(False)
        # 标记点
        self.dian_bg = self.childex('img_bg.panel_jdt_dian.panel_dian_bg')
        self.dian = self.childex('img_bg.panel_jdt_dian.panel_dian.img_deco', UITexture)
        self.dian.visible = False

        self.list_rewards = []

    def SetData(self, data: RewardLevelListData):
        self.data = data
        self.RefreshNode()

    def RefreshNode(self):
        # 一级解锁只保留迷彩
        if self.data.level == 1:
            new_rewards = []
            for r in self.data.rewards:
                if r.item_type == RewardType.GUN_SKIN:
                    new_rewards.append(r)
            self.data.rewards = new_rewards
        len_data = len(self.data.rewards)
        self.listview_reward.total_item_num = len_data
        self.txt_num.visible = not self.data.is_rank
        self.node_rank.visible = self.data.is_rank
        if self.data.is_rank:
            self.node_rank.SetData(self.data.match_type, self.data.level)
        else:
            self.txt_num.text = self.data.level

        self.img_bg.color = (0xca, 0xf2, 0x2e) if self.data.is_next_reward else (0x23, 0x24, 0x28)
        self.txt_num.text_color = (0x2a, 0x2d, 0x33) if self.data.is_next_reward else (0xff, 0xff, 0xff)
        self.txt_num.opacity = 255 if self.data.is_next_reward else int(255 * 0.7)
        self.opacity = 128 if self.data.has_got else 255
        self.dian.visible = self.data.has_got
        self.txt_tips.visible = self.data.is_next_reward

        self.listview_reward.AdjustSizeByContent()
        self.SetWidth(self.listview_reward.GetWidth())

    def OnListViewReward(self, irange):
        self.list_rewards = []
        for i, node in irange:
            node.SetData(self.BuildRewardData(self.data.rewards[i], i))
            self.list_rewards.append(node)

    def BuildRewardData(self, reward_data: RewardData, index):
        res = {
            'item_id': reward_data.item_id,
            'item_count': reward_data.item_cnt,
            'type': reward_data.item_type,
            'has_got': self.data.has_got,
            'index_in_parent': index
        }
        reward_type = reward_data.item_type
        if reward_type == RewardType.SYSTEM_UNLOCK:
            unlock_sys_proto = unlock_sys_data.data.get(res['item_id'])
            res['texture'] = unlock_sys_proto.get('small_icon_id', None)
            res['des'] = unlock_sys_proto.get('sys_name')
        elif reward_type == RewardType.ITEM:
            item_proto = lobby_item_data.data.get(res['item_id'])
            res['texture'] = item_proto.get('small_icon_id', None)
            res['quality'] = item_proto.get('quality', 1)
        elif reward_type == RewardType.GUN:
            equip_proto = equip_data.data.get(res['item_id'])
            res['texture'] = equip_proto.get('small_icon_id', None)
        elif reward_type == RewardType.GUN_PART:
            gun_attachments_proto = gun_attachments_data.data.get(res['item_id'])
            res['texture'] = gun_attachments_proto.get('attachment_ui_icon_id', None)
            res['des'] = gun_attachments_proto.get('attachment_ui_name', '')
        elif reward_type == RewardType.GUN_SKIN:
            item_proto = lobby_item_data.data[res['item_id']]
            is_locked = not gun_skin_util.CheckGunSkinOwned(res['item_id'])
            res['get'] = not is_locked
            res['des'] = lang.GUNSMITH_DIY_TAP_TITLE[GunSmithDiyType.Skin]
            res['texture'] = item_proto['icon']
        elif reward_type == RewardType.GUN_VIEW:
            res['texture'] = self.GUN_INSPECTION_ICON_ID
            res['des'] = 'view'
        return res

    def SetSelect(self, level, idx_in_parent):
        if level != self.data.level:
            idx_in_parent = -1

        for node in self.list_rewards:
            node.SetSelect(idx_in_parent == node.index)

    def OnChildClick(self, index, type, id, widget):
        self.ctrl.OnChildClick(self.data.level, index, type, id, widget)

    def Click(self, index):
        for item in self.list_rewards:
            if item.index == index:
                item.OnClick()


class WeaponModifySlotNode(HelperNode):
    def InitNode(self):
        root = self.seek('node_item')
        self.panel_attachment = root.seek('panel_attachment')
        self.btn_item = self.panel_attachment.seek('btn_item', UIButton)
        self.btn_item.SetMouseMoveEventEnable(True)
        self.btn_item.onMouseHover = self.OnHover
        self.btn_item.onClick = self.OnSelect
        self.panel_nml = self.btn_item.seek('panel_nml')
        self.panel_nml.visible = True
        self.panel_nml.EnableTouch(True)
        self.panel_nml.SetMouseMoveEventEnable(True)
        self.panel_nml.setSwallowTouches(False)
        self.panel_nml.onMouseHover = self.OnNmlHover
        self.img_nml_bg = self.panel_nml.seek('img_bg')
        self.img_nml_icon = self.panel_nml.seek('icon_attachment', UITexture)
        self.txt_nml = self.panel_nml.seek('txt_des', UIText)
        self.txt_nml.visible = False
        self.nml_hov = self.panel_nml.seek('img_hov')
        self.name = self.panel_nml.seek('txt_top_des', UIText)
        self.panel_mask = self.panel_nml.seek('panel_mask', CycleTextNode)
        self.panel_mask.visible = False
        self.panel_add = self.btn_item.seek('panel_add')
        self.panel_add.EnableTouch(True)
        self.panel_add.SetMouseMoveEventEnable(True)
        self.panel_add.setSwallowTouches(False)
        self.panel_add.onMouseHover = self.OnAddHover
        self.panel_add_name = self.panel_add.seek('txt_des', UIText)
        self.panel_add.visible = False
        self.img_add = self.panel_add.seek('img_add', UITexture)
        self.img_add_hov = self.panel_add.seek('img_hov', UITexture)
        self.panel_lock = self.btn_item.seek('panel_lock')
        self.panel_lock.visible = False
        self.panel_lock_name = self.panel_lock.seek('txt_des', UIText)
        self.img_slc = self.btn_item.seek('img_slc', UITexture)
        self.img_slc.visible = False
        self.num = 0  # 槽位编号
        self.part_type = None  # 槽位类型
        self.gun_id = 1
        self.part_id = 0
        self.backpack_no = 1
        self.info = None
        self.selected = False
        self.ctrl = None
        self.is_lock = False
        self.valid = True
        self.LoadAnimFromFile("UIScript/node_og_gun_weapon_modify_v2_item.csb")
        self.PlayAnim('in')

    def SetNodeNum(self, num):
        self.num = num

    def SetNodeCtrl(self, ctrl):
        self.ctrl = ctrl

    def RefreshInfo(self, info):
        if not info:
            return
        self.info = info
        self.gun_id = info.get('gun_id', 1)
        self.part_type = info.get('part_type', None)
        self.part_id = info.get('part_id', 0)
        self.backpack_no = info.get('backpack_no', 1)
        equip_id = gun_mod_data.data[self.gun_id]['equip_id']
        self.name.text = self.panel_lock_name.text = self.panel_add_name.text = lang.WeaponPartTypeNameArrowhead if weapon_util.IsWeaponCompoundBow(
            equip_id) and self.part_type == consts.WeaponPartType_Ammunition else lang.WeaponPartTypeName[
            self.part_type]
        # 槽位内
        # icon_id = gun_attachments_data.data.get(self.part_id, {}).get('attachment_ui_icon_id')
        icon_path = gun_skin_util.GetGunPartIconName(self.part_id, False)
        self.img_nml_icon.texture = icon_path
        part_name = gun_attachments_data.data.get(self.part_id, {}).get('attachment_ui_name')
        self.txt_nml.text = part_name
        self.panel_mask.text = part_name
        is_mutex = self.CheckMutex()
        is_add = self.CheckAdd()
        if is_mutex:
            self.panel_add.visible = False
            self.panel_lock.visible = True
            self.panel_nml.visible = False
            self.is_lock = True
        elif is_add:
            self.panel_add.visible = True
            self.panel_lock.visible = False
            self.panel_nml.visible = False
            self.is_lock = False
        else:
            self.panel_add.visible = False
            self.panel_lock.visible = False
            self.panel_nml.visible = True
            self.is_lock = False
        self.PlayAnim('in')

    def OnHover(self, widget, hover):
        self.img_slc.visible = hover

    def OnAddHover(self, widget, hover):
        self.img_add_hov.visible = hover
        self.panel_add.runZoomAction(1.05 if hover else 1)

    def OnNmlHover(self, widget, hover):
        self.nml_hov.visible = hover

    def OnSelect(self, btn):
        if self.is_lock:
            return
        self.selected = not self.selected
        genv.avatar.RecordGunSmithClickLogToServer(
            f'36_{1 if weapon_util.GetGunBackpackSlot(self.gun_id) == consts.BackpackSlot.WEAPON_1 else 2}',
            info={'gun_id': self.gun_id, 'part_type': self.part_type})
        GunSmithModifyDetailWindow.instance().Show(
            info={'name': self.name.text, 'gun_id': self.gun_id,
                  'backpack_no': self.backpack_no, 'part_type': self.part_type,
                  'ctrl': self.ctrl, 'gun_slot': self.ctrl.gun_slot})
        genv.avatar.RemoveNewUnlockPartType(self.gun_id, self.part_type)

    def CheckMutex(self):
        self.mutex_part_id = weapon_util.CheckPartTypeNeedMutex(self.ctrl.gun_part_slots, self.part_type)
        return self.mutex_part_id

    def CheckAdd(self):
        return self.part_id == weapon_util.GetGunDefaultPartId(gun_id=self.gun_id, part_type=self.part_type)

    def CreateLine(self):
        if not self.visible:
            self.SetLineVisible(False)
        else:
            origin_pos = self.GetPartPos()
            if origin_pos:
                GunSmithSlotLine.instance().CreateLine(pos1=self.btn_item.GetWorldPosition(), pos2=origin_pos, name=str(self.gun_id) + str(self.part_type))

    def SetLineVisible(self, visible):
        if not self.visible:
            GunSmithSlotLine.instance().SetLineVisible(str(self.gun_id) + str(self.part_type), False)
        else:
            GunSmithSlotLine.instance().SetLineVisible(str(self.gun_id) + str(self.part_type), visible)

    def GetPartPos(self):
        part_model = self.ctrl.gun_model_ctrl.cur_show_equip_case.GetWeaponPartModel(self.part_type)
        if part_model and part_model.model.Primitives:
            # bound_box = part_model.GetWorldBoundWithGenerateData()
            bound_box = part_model.GetPrimWorldBound()
            if bound_box and self.ctrl.gun_model_ctrl.CheckBoundBoxValid(bound_box.min, bound_box.max):
                origin_pos = (bound_box.min + bound_box.max) * 0.5
            else:
                return
        else:
            parent_part_model = self.ctrl.gun_model_ctrl.cur_show_equip_case.GetWeaponPartModel(consts.WeaponPartParentType[self.part_type])
            if parent_part_model:
                origin_pos = parent_part_model.GetBoneWorldPosition(consts.WeaponPartModelTachPoint[self.part_type])
            else:
                return
        return origin_pos



