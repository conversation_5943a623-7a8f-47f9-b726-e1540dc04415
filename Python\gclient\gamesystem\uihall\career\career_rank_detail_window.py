# -*- coding: utf-8 -*-
# author: lvxuhui
# date: 2025/6/20
from functools import partial
from gclient import cconst, lang
from gclient.cconst import PC_KEY_UI
from gclient.data import qualifying_season_data, lobby_item_data, rank_level_data

from gclient.framework.ui.commonnodes.common_reward_list import RewardData, RewardLevelListData, \
    RewardType
from gclient.framework.ui.commonnodes.common_reward_list_v2 import CommonRewardListV2, CommonRewardSwitchNode
from gclient.framework.ui.commonnodes.ui_common_button import CommonBottomNode, ButtonData
from gclient.framework.ui.ui_helper import HelperWindow
from gclient.framework.ui.widgets import UIText, UITexture, UIButton
from gclient.framework.util import events
from gclient.gamesystem.uihall.career import career_utils
from gclient.gamesystem.uihall.career.career_common_nodes import CareerModelShowCtrl, CareerModelTouchPanel
from gclient.gamesystem.uihall.career.career_data_mgr import CareerDataMgr
from gclient.gamesystem.uihall.career.common_item_tips_window import CommonItemTipsWindow
from gclient.util import cdata_util
from gshare import consts
import cc

class CareerRankDetailWindow(HelperWindow):
    # 排位赛段位奖励页面
    CSB_NAME = 'UIScript/og_ranking_gameplay_reward.csb'
    DESTROY_ON_CLOSE = True

    def InitData(self):
        self.cur_page = 0
        self.cur_select_widget = None
        self.gun_model_ctrl = CareerModelShowCtrl.instance()
        self.target_level = None

    def InitNode(self):
        self.panel_name = self.HelperSeek('panel_name')
        self.panel_content = self.HelperSeek('panel_content')
        self.panel_rewards = self.HelperSeek('panel_rewards')
        self.node_btn = self.HelperChildex('panel_btn.node_c_btn_hint_bottom', CommonBottomNode)
        self.HelperChildex('panel_bg.img_msak_1').visible = False

        self.InitPanelName(self.panel_name)
        self.InitPanelContent(self.panel_content)
        self.InitPanelRewards(self.panel_rewards)
        self.touch_panel = self.HelperSeek('panel_model', CareerModelTouchPanel)

        self.node_btn.SetData([
            ButtonData(lang.COMMON_TEXT_RETURN, self.Close, PC_KEY_UI.KEY_ESC, None)
        ], [])

    def InitPanelName(self, panel):
        self.txt_season_name = panel.seek('txt_des', UIText)
        self.txt_season_name_num = panel.seek('txt_time_des', UIText)
        self.txt_season_time = panel.seek('txt_time', UIText)

        self.txt_quality = panel.childex('panel_gun_name.txt_quality', UIText)
        self.img_quality = self.txt_quality.childex('panel_quality.img_quality')
        self.txt_reward_name = panel.childex('panel_gun_name.txt_name', UIText)

    def InitPanelContent(self, panel):
        self.img_reward = panel.seek('img_icon', UITexture)
        panel.seek('img_deco').visible = False
        self.img_reward.visible = False

    def InitPanelRewards(self, panel):
        self.rewards_node = panel.childex('panel_item.node_lve_item_0', partial(CommonRewardListV2, self))
        self.node_switch = panel.seek('node_switch', partial(CommonRewardSwitchNode, self))
        self.node_switch.visible = True
        panel.seek('panel_switch').visible = False

    def OnShow(self, info):
        self.cur_page = info.get('page', -1)
        self.match_type = info.get('match_type', cconst.MatchType.BattleRoyaleRank)
        self.gun_model_ctrl.InitCamera()

    def OnClose(self):
        self.gun_model_ctrl.visible = False
        self.gun_model_ctrl.AddHiddenReason(cconst.HIDDEN_REASON_LEVEL_TASK)
        if CommonItemTipsWindow.isInited() and CommonItemTipsWindow.instance().visible:
            CommonItemTipsWindow.instance().Close()

    def RefreshData(self):
        self.cur_select_widget = None

        self.season = season = genv.avatar.GetQualifyingSeason(self.match_type)
        proto = qualifying_season_data.data.get(self.match_type)
        if season not in proto:
            season = 1
        season_info = proto.get(season, {})
        self.season_name = season_info.get('name', '')
        self.season_time = season_info.get('time_display_division', '')
        self.qualifying_score = genv.avatar.qualifying_score_dict.get(self.match_type, 0)
        self.qualifying_level = cdata_util.CalcQualifyingLevel(self.qualifying_score, self.match_type)

        if self.cur_page == -1:
            self.cur_page = int(self.qualifying_level / 4)
        self.cur_page = max(min(self.cur_page, 6), 0)

        next_level = self.qualifying_level + 1
        self.is_max_level = False
        self.next_level_score = 0
        self.level_base_score = 0
        self.progress_percent = 0
        if next_level not in rank_level_data.data[self.match_type]:
            self.is_max_level = True
        else:
            self.next_level_score = rank_level_data.data[self.match_type][next_level]['rank_score']
            self.level_base_score = rank_level_data.data[self.match_type][self.qualifying_level]['rank_score']
            self.progress_percent = (self.qualifying_score - self.level_base_score) * 1.0 / (self.next_level_score - self.level_base_score) * 100

    def RefreshNode(self):
        self.txt_season_name.text = self.season_name
        self.txt_season_time.text = self.season_time
        self.txt_season_name_num.text = lang.CAREER_RANK_NUM % self.season

        target_level = CareerDataMgr.instance().GetLastSelectedLevel('rank')
        if not target_level:
            if not self.is_max_level:
                target_level = self.qualifying_level + 1
            else:
                target_level = self.qualifying_level
        self.target_level = target_level
        self.cur_page = int((target_level - 1) / 4)

        self.RefreshPage()

    def RefreshPage(self):
        self.node_switch.SetData(self.OnPageSwitch, 6, self.cur_page)
        self.rewards_node.SetData(self.BuildRewardListData(), self.OnRewardClick)
        self.rewards_node.AdjustSizeByContent()
        self.OnScreenSizeChanged()

        if ((self.target_level - 1) / 4) == self.cur_page:
            # 目标奖励处于当前页
            select_level = self.target_level
        elif self.cur_page == 0:
            select_level = 2
        else:
            select_level = self.cur_page * 4 + 1
        selected_level = self.rewards_node.GetListByLevel(select_level)
        if selected_level:
            selected_level.Click(0)


    def BuildRewardListData(self):
        res = []
        levels = []
        if self.cur_page >= 6:
            # 传奇段位
            levels.append(25)
        else:
            for i in range(4):
                levels.append(self.cur_page * 4 + i + 1)
        for level in levels:
            list_reward = []
            rewards = career_utils.GetRankRewards(self.match_type, level)
            if not rewards:
                continue

            for reward in rewards:
                list_reward.append(RewardData(reward['item_id'], reward['item_count'], reward['type']))

            is_next_level = level == self.qualifying_level + 1
            is_big_reward = (level - 1) % 4 == 0
            res.append(RewardLevelListData(
                list_reward,
                level,
                self.qualifying_level >= level,
                is_big_reward,
                is_next_level,
                True,
                self.match_type,
                progress_value=(self.qualifying_score, self.next_level_score) if is_next_level else None,
                progress_percent=self.progress_percent if is_next_level else None
            ))
        return res

    def OnRewardClick(self, reward_level, reward_index, reward_type, reward_id, widget):
        # if is_stage_reward:
        # self.rewards_node.SetSelect(-1, 0)
        # else:
        #     self.stage_rewards_node.SetSelect(-1, 0)

        if self.cur_select_widget == widget:
            return
        else:
            # self.jump_btn.visible = reward_level != self.qualifying_level + 1
            self.cur_select_widget = widget
            self.ShowRewardContent(reward_type, reward_id)
            CareerDataMgr.instance().SetLastSelectedLevel('rank', reward_level)

    def ShowRewardContent(self, reward_type, reward_id):
        if not self.visible:
            return

        self.img_reward.visible = False

        if reward_type == RewardType.ITEM:
            item_proto = lobby_item_data.data.get(reward_id, {})
            sub_type = item_proto.get('sub_type', 0)
            quality = item_proto.get('quality', 1)

            self.txt_reward_name.text = item_proto.get('name')
            self.txt_quality.text = lang.QUALITY_DESC.get(quality, '')
            self.img_quality.color = cconst.CAREER_QUALITY_LEVEL_COLOR[quality]

            if sub_type // 100 * 100 in (consts.WarehouseItemType.GunSkin, consts.WarehouseItemType.GunGuise,
                                         consts.WarehouseItemType.MeleeWeapon):
                self.gun_model_ctrl.InitModel(reward_id)
                self.gun_model_ctrl.visible = True
                self.gun_model_ctrl.RemoveHiddenReason(cconst.HIDDEN_REASON_LEVEL_TASK)
            else:
                self.gun_model_ctrl.visible = False
                self.gun_model_ctrl.AddHiddenReason(cconst.HIDDEN_REASON_LEVEL_TASK)

                self.img_reward.visible = True
                self.img_reward.texture = item_proto.get('small_icon_id')

    def OnPageSwitch(self, new_page):
        self.cur_select_widget = None

        self.cur_page = new_page
        self.RefreshPage()

    def OnDestroy(self):
        self.gun_model_ctrl.Destroy()
        super().OnDestroy()

    def AfterShow(self):
        super().AfterShow()
        self.node_switch.PlayInAnim()

    @events.ListenTo(events.ON_SCREEN_SIZE_CHANGED)
    def OnScreenSizeChanged(self):
        self.rewards_node.SetWorldPosition(
            cc.Vec2(gui.GetCocosScreenSize()[0] * 0.5 - self.rewards_node.GetContentWidth() * 0.5,
                    self.panel_rewards.GetWorldPosition().y)
        )
        self.node_switch.SetWorldPosition(
            cc.Vec2(gui.GetCocosScreenSize()[0] * 0.5, self.node_switch.GetWorldPosition().y)
        )
