# -*- coding: utf-8 -*-
# author: guangqisheng
import cc
import ccui
import math
import time
import pickle
from functools import partial
from common.classutils import Components

from gclient import lang
from gclient.framework.util import events
from gclient.framework.ui import ui_define
from gclient.framework.ui.ui_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>, HelperNode
from gclient.framework.ui.widgets import UIText, UITexture
from gclient.framework.ui.widgets.ui_listview_cycle import UIListViewCycle
from gclient.framework.ui.widgets.ui_listview import UIListView
from gclient.gameplay.logic_base.ui.ui_util import GetListviewCreateNodeNum
from gclient.framework.ui.widgets.ui_textfield import UITextField
from gclient.framework.util.desktop_input import DesktopInput
from gclient.framework.util.gameinput_controller import ListenPcKey
from gclient.gamesystem.util import unlock_sys_util
from gclient.gamesystem.util.unlock_sys_util import SystemId
from gclient.ui.common.common_personal_card import CommonPersonal<PERSON>ard<PERSON><PERSON>, SHOW_SOURCE_HALL_CHAT
from gclient.gameplay.logic_base.ui import ui_util
from gclient.framework.ui.commonnodes.ui_mouse_interactive_mask import MouseInteractiveComp
from gclient.ui.common.reddot_node import CheckReddotForWidget
from gclient.framework.util.mlive_util import MLiveVideoUtil
from gclient.gamesystem.uihall.friend.friend_common_node import BuildRankInfo
from gclient.framework.ui.widgets.ui_slider import CustomUISliderVertical

from gshare import ichat


TAB_ALL, TAB_WORLD, TAB_TEAM, TAB_PRIVATE = list(range(4))
CHANNEL_WORLD, CHANNEL_TEAM, CHANNEL_PRIVATE = list(range(3))
STATUS_STORAGE, STATUS_EXPAND = list(range(2))


TAB_TO_CHANNEL = {
    TAB_ALL: ichat.ChatChannel.ALL,
    TAB_WORLD: ichat.ChatChannel.HallWorld,
    TAB_TEAM: ichat.ChatChannel.HallTeam,
    TAB_PRIVATE: ichat.ChatChannel.HallFriend,
}


CHAT_TEXT_MAP = {
    CHANNEL_WORLD: lang.TEXT_CHAT_WORLD,
    CHANNEL_TEAM: lang.TEXT_CHAT_TEAM,
    CHANNEL_PRIVATE: lang.TEXT_CHAT_PRIVATE
}


class ChatTabButtonNode(HelperNode):

    def InitNode(self):
        self.slc_all = self.HelperSeek('slc_all')
        self.img_hover = self.HelperSeek('img_slc')
        self.img_hover_0 = self.HelperSeek('img_slc_0')
        self.img_hover.visible = False
        self.img_hover_0.visible = False
        self.slc_all.visible = False
        self.EnableTouch(True)
        self.SetMouseMoveEventEnable(True)
        self.onMouseHover = self.OnHover

    def SetSelect(self, is_select):
        self.slc_all.visible = is_select

    def OnHover(self, btn, visible):
        self.img_hover.visible = visible
        self.img_hover_0.visible = visible


# 感觉UIRichTextEx可以单独抽个class
class ChatTextNode(UIText):
    MAX_LIST_LENGTH = 367

    def __init__(self, widget=None):
        super(ChatTextNode, self).__init__(widget)
        self.msg = None
        self.player_guid = None
        self.player_name = None
        self.img_hover = self.seek('img_bg', UITexture)
        self.img_hover.opacity = 0
        self.img_hover.EnableTouch(True)
        self.img_hover.SetMouseMoveEventEnable(True)
        self.img_hover.onMouseHover = self.OnPlayerNameHover
        self.img_hover.onClick = self.ShowPlayerInfo

    def SetMsgInfo(self, msg):
        self.msg = msg
        if not msg:
            return
        avatar_id = genv.avatar.id
        frm = msg.get('frm', None)
        self.player_guid = frm
        self.player_name = msg['name']
        channel = msg['channel']
        msg_text = ''
        if channel == ichat.ChatChannel.HallWorld:
            msg_text = lang.TEXT_MESSAGE_WORLD % (msg['name'], msg['content'])
        elif channel == ichat.ChatChannel.HallFriend:
            if frm == avatar_id:
                msg_text = lang.TEXT_MESSAGE_SEND_PRIVATE % (msg['to_name'], msg['content'])
                self.player_guid = msg['to']
                self.player_name = msg['to_name']
            else:
                msg_text = lang.TEXT_MESSAGE_PRIVATE % (msg['name'], msg['content'])
        elif channel == ichat.ChatChannel.HallTeam:
            msg_text = lang.TEXT_MESSAGE_TEAM % (msg['name'], msg['content'])
        self.text = msg_text
        self.setRichModeEnabled(True)
        self.EnableTouch(True)
        self.SetMouseMoveEventEnable(True)
        self.onClick = self.OnClick
        self.onMouseHover = self.OnMouseHover
        # self.setLineInterval(20)

    def ShowPlayerInfo(self, _):
        avatar = genv.avatar
        if self.player_guid == avatar.id:
            return
        HallChatWindow.instance().ShowPersonalCardNode(self)

    def OnClick(self, btn):
        gPos = btn.getTouchEndPosition()
        localPos = self.convertToNodeSpace(gPos)  # 记得必须要convert成text的本地坐标
        evtName = self.getEventNameByLocalPos(localPos, 0, 8)  # 获取localPos所命中的segment的event
        if evtName == "e1":
            self.ShowPlayerInfo(gPos)

    def OnMouseHover(self, btn, visible):
        rect = self.getSegmentDefaultRectByID("i1")
        if not rect:
            self.img_hover.visible = False
            return
        self.img_hover.visible = True
        self.img_hover.setContentSize(cc.Size(rect.width, rect.height))
        self.img_hover.SetPosition(rect.x, rect.y + 15)  # 先加个15吧，看起来好像正常
        # gPos = btn.getTouchMovePosition()
        # localPos = self.convertToNodeSpace(gPos)  # 记得必须要convert成text的本地坐标
        # evtName = self.getEventNameByLocalPos(localPos, 0, 8)  # 获取localPos所命中的segment的event
        # if evtName == "e1":
        #     rect = self.getSegmentDefaultRectByID("i1")
        #     if not rect:
        #         self.img_hover.visible = False
        #         return
        #     self.img_hover.visible = True
        #     self.img_hover.setContentSize(cc.Size(rect.width, rect.height))
        #     self.img_hover.SetPosition(rect.x, rect.y + 15)
        #     return
        # self.img_hover.visible = False

    def OnPlayerNameHover(self, btn, visible):
        if not visible:
            self.img_hover.opacity = 0
        else:
            self.img_hover.opacity = 255 * 0.6

    def GetPlayerInfo(self):
        avatar = genv.avatar
        if self.player_guid == avatar.id:
            return {}
        segment_data = []
        info = {
            'id': self.player_guid,
            'show_name': self.player_name,
            'location_info': (self, 0),
            'segment_data': segment_data,
            'from_node': self,
            'level': self.msg.get('level', 0),
            'gender': self.msg.get('gender', 0)}
        return info


@Components(MouseInteractiveComp)
class HallChatWindow(HelperWindow):
    CSB_NAME = 'UIScript/og_text_chat.csb'
    SCENE_IDS = (ui_define.UI_SCENEID_WINDOW, ui_define.UI_SCENEID_HALL)
    ZORDER = ui_define.SHOW_LEVEL_WINDOW_TOP
    CLOSE_ON_ESC = False

    def InitData(self):
        # [debug]
        gui.chat = self
        # [debug]
        self.cur_tab = TAB_ALL  # 当前展示得信息类型
        self.cur_channel = CHANNEL_WORLD  # 当前信道
        self.chat_avatar_info_list = []  # value: (guid, name)
        self.chat_avatar_info_index = 0  # 当前的是和list得那个玩家聊

        self.cur_panel_channel = None
        self.cur_status = STATUS_STORAGE  # 收纳态

        self.enable_chat_input = True

        self.private_chat_cd = 0  # 记录下被禁言两分钟得开始时间
        self.private_chat_time_info = []  # value: (guid, name)

    def InitNode(self):
        self.panel_text = panel_text = self.HelperSeek('panel_text')
        self.panel_text.visible = False
        self.panel_bg = panel_text.seek('panel_bg')
        self.blur_node = ui_util.BuildBlurNode(self.panel_bg)
        panel_text.addChild(self.blur_node, -1)
        self.InitPanelInput(panel_text)
        self.InitPanelTop(panel_text)
        self.InitPanelTxt(panel_text)
        self.InitPanelStorage(panel_text)
        self.InitPanelMask(panel_text)
        self.node_personal_card = panel_text.seek('node_businesscard', CommonPersonalCardNode)
        self.node_personal_card.SetClick(self.ShowPersonalCardNode)
        self.node_personal_card.visible = False
        self.OnStorage()
        self.OnStorageCallBack()
        self._callComponents("init")
        self.BindLeftClick(self.OnMouseLeftClick)

    def InitPanelTop(self, panel):
        # 顶上得tab
        self.panel_top = panel_top = panel.seek('panel_top')
        self.panel_channel = panel_top.seek('panel_channel', partial(ChatTabButtonNode, self))
        self.panel_channel.onClick = partial(self.OnClickTabBtn, TAB_ALL)
        self.panel_channel_0 = panel_top.seek('panel_channel_0', partial(ChatTabButtonNode, self))
        self.panel_channel_0.onClick = partial(self.OnClickTabBtn, TAB_WORLD)
        self.panel_channel_1 = panel_top.seek('panel_channel_1', partial(ChatTabButtonNode, self))
        self.panel_channel_1.onClick = partial(self.OnClickTabBtn, TAB_TEAM)
        self.panel_channel_2 = panel_top.seek('panel_channel_2', partial(ChatTabButtonNode, self))
        self.panel_channel_2.onClick = partial(self.OnClickTabBtn, TAB_PRIVATE)
        self.panel_channels = {
            TAB_ALL: self.panel_channel,
            TAB_WORLD: self.panel_channel_0,
            TAB_TEAM: self.panel_channel_1,
            TAB_PRIVATE: self.panel_channel_2
        }

    def InitPanelTxt(self, panel):
        # 中间得文字
        self.panel_txt = panel_txt = panel.seek('panel_txt')
        self.lv_txt = panel_txt.seek('lv_txt', UIListViewCycle)
        self.lv_txt.setAlign(0)
        self.lv_txt.create(item_num=GetListviewCreateNodeNum(self.lv_txt) + 1, callback=self.OnRefreshListChat,
                           scroll_percent_listener=self.OnListChatScroll, obj_type=ChatTextNode, hide_redundant=True)
        self.lv_txt.setClippingType(0)

    def InitPanelInput(self, panel):
        # 底部得输入框
        self.panel_input = panel_typing = panel.seek('panel_typing')
        self.panel_input_bg = panel_typing.seek('panel_bg')
        panel_typing.EnableTouch(True)
        panel_typing.SetMouseMoveEventEnable(True)
        panel_typing.onMouseHover = self.OnInputMouseHover
        # 输入框选中效果
        self.img_slc = panel_typing.seek('img_slc')
        # 真正得input
        self.panel_world = panel_typing.seek('panel_world')
        self.txt_word = self.panel_world.seek('txt_word', UIText)
        self.panel_world.seek('icon_expression').visible = False
        self.txt_chat = self.txt_word.seek('txt_chat', UITextField)
        self.txt_chat.AddInputFilter(input_not_allowed='\t\n')
        self.txt_chat.onNotify = self.OnTxtChatNotify
        self.txt_chat.addEnterListener(lambda _: False)
        self.img_bai = self.txt_chat.seek('img_bai')
        self.img_bai.EnableTouch(True)
        self.img_bai.setLocalZOrder(100)
        self.img_bai.visible = False
        # 收纳态假的input
        self.panel_enter = panel_typing.seek('panel_enter')  # 这个之用于控制显示
        self.panel_enter.EnableTouch(True)
        self.panel_enter.onClick = self.OnClickPanelEnter

    def InitPanelStorage(self, panel):
        self.panel_storage = panel_storage = panel.seek('panel_storage')
        self.listview_storage_txt = panel_storage.seek('lv_storage', UIListView)
        self.listview_storage_txt.SetMaskPath(50006)
        self.listview_storage_txt.create(item_num=3, callback=self.OnRefreshListStorageChat, obj_type=ChatTextNode)

    def InitPanelMask(self, panel):
        self.panel_mask = panel.seek('panel_mask')
        self.panel_slider = self.panel_mask.seek('slider', CustomUISliderVertical)
        self.panel_slider.setPercent(0)
        self.panel_slider.Create(self.OnSliderScroll)
        self.panel_mask.visible = False

    def OnRefreshListChat(self, irange):
        chanel = TAB_TO_CHANNEL.get(self.cur_tab, ichat.ChatChannel.HallWorld)
        chat_info_list = genv.avatar.GetChatInfoByChannel(chanel)
        length = len(chat_info_list)
        for idx, node in irange:
            if idx < length:
                node.visible = True
                node.SetMsgInfo(genv.avatar.GetChatDetailInfoByGuid(chat_info_list[idx]))
            else:
                node.visible = False

    def OnRefreshListStorageChat(self, irange):
        chanel = TAB_TO_CHANNEL.get(self.cur_tab, ichat.ChatChannel.HallWorld)
        chat_info_list = genv.avatar.GetChatInfoByChannel(chanel)
        length = len(chat_info_list)
        for idx, node in irange:
            if idx < length:
                node.visible = True
                node.SetMsgInfo(genv.avatar.GetChatDetailInfoByGuid(chat_info_list[length - idx - 1]))
                node.EnableTouch(False)
            else:
                node.visible = False

    def OnClickTabBtn(self, tab, btn):
        tab_channel_map = {
            TAB_WORLD: CHANNEL_WORLD,
            TAB_TEAM: CHANNEL_TEAM,
            TAB_PRIVATE: CHANNEL_PRIVATE,
        }
        panel_channel = self.panel_channels[tab]
        self.cur_panel_channel and self.cur_panel_channel.SetSelect(False)
        panel_channel.SetSelect(True)
        self.cur_channel = tab_channel_map.get(tab, CHANNEL_WORLD)
        self.cur_tab = tab
        self.cur_panel_channel = panel_channel
        self.PlayAnim('slc')
        if self.cur_status == STATUS_EXPAND:
            if self.cur_tab == TAB_ALL:
                genv.avatar.ClearAllUnreadMsgs()  # 清空所有已读
            else:
                genv.avatar.ClearUnreadMsgs(TAB_TO_CHANNEL.get(self.cur_tab, ichat.ChatChannel.HallWorld))  # 清空所有已读
        self.RefreshAll()
        self.blur_node.visible = True

    def RefreshData(self):
        pass

    def RefreshNode(self):
        # 底下的输入框
        chat_label = CHAT_TEXT_MAP.get(self.cur_channel, lang.TEXT_CHAT_WORLD)
        if self.cur_channel == CHANNEL_PRIVATE:
            if self.chat_avatar_info_list:
                chat_label = chat_label % self.chat_avatar_info_list[self.chat_avatar_info_index][-1]
                self.txt_chat.place_holder = lang.TEXT_CHAT_TIPS_CHANGE_AVATAR
            else:
                chat_label = lang.TEXT_CHAT_PRIVATE_NO_AVATAR
                self.txt_chat.place_holder = lang.TEXT_CHAT_TIPS_NO_CHAT_AVATAR
        elif self.cur_channel == CHANNEL_TEAM and not genv.avatar.team.member_dict:
            self.txt_chat.place_holder = lang.TEXT_CHAT_TIPS_NOT_IN_TEAM
        else:
            self.txt_chat.place_holder = lang.TEXT_CHAT_TIPS_CHANGE_CHANNEL
        self.txt_word.text = chat_label
        # 信息列表
        if self.cur_status == STATUS_EXPAND:
            chanel = TAB_TO_CHANNEL.get(self.cur_tab, ichat.ChatChannel.HallWorld)
            self.lv_txt.total_item_num = len(genv.avatar.GetChatInfoByChannel(chanel))
            # 改一下slider大小
            item_num = self.lv_txt.item_num - 1
            total_item_num = max(1.0, self.lv_txt.total_item_num)
            _scale = min(1.0, item_num * 1.0 / total_item_num)
            self.lv_txt.jumpToBottom()
            self.panel_mask.visible = _scale < 1.0
            self.panel_slider.SetSliderScale(_scale)
        else:
            chanel = TAB_TO_CHANNEL.get(self.cur_tab, ichat.ChatChannel.HallWorld)
            from gclient.gamesystem.uihall.hall_main_window_v3 import HallMainWindow
            length = 1
            if HallMainWindow.isInited() and HallMainWindow.instance().real_visible_include_scene:
                length = 3
                
            if genv.avatar:
                self.listview_storage_txt.total_item_num = min(length, len(genv.avatar.GetChatInfoByChannel(chanel)))
        self.CheckMessagesRedDot()
        # 输入框
        enable_input = True
        if self.cur_tab == TAB_TEAM:
            enable_input = len(genv.avatar.team.member_dict) > 1
        elif self.cur_tab == TAB_PRIVATE:
            enable_input = len(self.chat_avatar_info_list) > 0

        self.SetEnableChatInput(enable_input)
        if self.enable_chat_input and self.cur_status == STATUS_EXPAND:
            self.txt_chat.EnterInputStatus()

    def BuildData(self):
        pass

    def Show(self, info=None):
        super(HallChatWindow, self).Show(info)
        self.cur_tab = TAB_ALL
        # self.cur_panel_channel = self.panel_channels[self.cur_tab]
        self.OnClickTabBtn(self.cur_tab, None)
        self.OnStorage()
        self.panel_text.visible = True

    def OnInputMouseHover(self, btn, visible):
        pass

    def OnClickPanelEnter(self, btn):
        if self.cur_status != STATUS_EXPAND:
            self.OnExpand()

    def OnExpand(self):
        self.cur_status = STATUS_EXPAND
        self.panel_top.visible = True
        self.panel_txt.visible = True
        self.panel_world.visible = True
        self.panel_enter.visible = False
        self.panel_storage.visible = False
        self.img_slc.visible = True
        self.panel_bg.visible = True
        self.panel_input.visible = True
        self.blur_node.visible = True
        self.panel_mask.visible = True
        if self.enable_chat_input:
            self.txt_chat.EnterInputStatus()
        if self.cur_status == STATUS_EXPAND:
            if self.cur_tab == TAB_ALL:
                genv.avatar.ClearAllUnreadMsgs()  # 清空所有已读
            else:
                genv.avatar.ClearUnreadMsgs(TAB_TO_CHANNEL.get(self.cur_tab, ichat.ChatChannel.HallWorld))  # 清空所有已读
        self.RefreshAll()
        self.PlayAnim('in')
        self.OnClickTabBtn(self.cur_tab, None)

    def OnStorage(self):
        self.node_personal_card.visible = False
        self.blur_node.visible = False
        self.panel_mask.visible = False
        self.PlayAnim('out', callback=self.OnStorageCallBack)

    def OnStorageCallBack(self):
        self.cur_status = STATUS_STORAGE
        self.panel_top.visible = False
        self.panel_txt.visible = False
        self.panel_world.visible = False
        self.panel_enter.visible = False
        self.panel_input.visible = False
        self.panel_storage.visible = True
        self.img_slc.visible = False
        self.panel_bg.visible = False
        self.txt_chat.CancelInputStatus()
        self.RefreshAll()

    @ListenPcKey(DesktopInput.KEY_RETURN)
    @ListenPcKey(DesktopInput.KEY_NUMPADENTER)
    def OnKeyEnter(self, is_down=True):
        if MLiveVideoUtil.instance().image_widget_sets:
            # 当前在播放视频
            # gui.prompt()
            return False
        if is_down:
            if self.cur_status != STATUS_EXPAND:
                self.OnExpand()
            else:
                self.OnSendMessage()
            return True
        return False

    @ListenPcKey(DesktopInput.KEY_ESCAPE)
    def OnKeyEsc(self, is_down=True):
        if is_down and self.cur_status != STATUS_STORAGE:
            self.OnStorage()
            return True
        return False

    @ListenPcKey(DesktopInput.KEY_TAB)
    def OnKeyTab(self, is_down=True):
        if not self.cur_status:
            return False
        if is_down:
            # 切信道
            if self.cur_tab == TAB_ALL:
                # 非私聊，切chanel
                next_channel = (self.cur_channel + 1) % 3
                if next_channel == CHANNEL_TEAM and not genv.avatar.team.member_dict:
                    # 没有小队
                    next_channel = (next_channel + 1) % 3

                if next_channel == CHANNEL_PRIVATE and not self.chat_avatar_info_list:
                    # 没有私聊
                    next_channel = (next_channel + 1) % 3

                self.cur_channel = next_channel
            elif self.cur_channel == CHANNEL_PRIVATE and self.chat_avatar_info_list:
                # 私聊，切玩家
                self.chat_avatar_info_index = (self.chat_avatar_info_index + 1) % len(self.chat_avatar_info_list)
            self.RefreshAll()
            return True
        return False

    def OnSendMessage(self):
        text = self.txt_chat.text
        text.strip()
        if not text:
            # 不能发送空消息
            if not self.txt_chat.isInInputState():
                self.OnStorage()
            else:
                gui.Prompt(1263)
            return
        if self.SendChat(text):
            self.txt_chat.text = ''
            self.txt_chat.EnterInputStatus()

    def SendChat(
            self, text, sound=None, share_info=None, at_player=None,
            msg_type=None, msg_id=None, extra=None):
        avatar = genv.avatar
        channel_id = None
        extra = extra or {}
        extra.update({'gender': avatar.gender, 'level': avatar.level})
        kwargs = {'content': text, 'sound': sound, 'extra': extra}
        if self.cur_channel == CHANNEL_WORLD:
            # left_time = int(math.ceil(avatar.GetWorldChatLeftCDTime()))
            # if left_time > 0:
            #     gui.Tips(321, extra_data=(left_time, ))
            #     return False
            if not unlock_sys_util.IsUnlock(SystemId.WorldChat):
                # 未解锁世界聊天
                unlock_sys_util.PromptSysLocked(SystemId.WorldChat)
                return False

            channel_id = ichat.ChatChannel.HallWorld
        elif self.cur_channel == CHANNEL_TEAM:
            if not genv.avatar.team.member_dict:
                # 没有小队，不允许发送
                return
            channel_id = ichat.ChatChannel.HallTeam
        elif self.cur_channel == CHANNEL_PRIVATE:
            if not self.chat_avatar_info_list:
                # 没有私聊对象，不允许发送
                return
            channel_id = ichat.ChatChannel.HallFriend
            friend_id = self.chat_avatar_info_list[self.chat_avatar_info_index][0]
            if friend_id not in avatar.friends:
                # 对陌生人发起私聊
                if not self.CheckPrivateChatCD(friend_id):
                    # 弹窗
                    gui.Prompt(1267)
                    return
            kwargs['to'] = friend_id
            extra['to_name'] = self.chat_avatar_info_list[self.chat_avatar_info_index][1]

        if share_info:
            kwargs['msg_type'] = share_info[0]
            extra['share_info'] = pickle.dumps(share_info[1])

        if at_player:
            extra['at_player_id'], extra['at_player_name'] = at_player

        if msg_type:
            kwargs['msg_type'] = msg_type

        if msg_id:
            extra['msg_id'] = msg_id

        channel_id and avatar.SendChat(channel_id, **kwargs)
        return True

    def CheckPrivateChatCD(self, friend_id):
        # 只有陌生人会进这里, 先判断cd
        time_now = time.time()
        for _id, chat_time in self.private_chat_time_info:
            if friend_id == _id:
                return True

        if len(self.private_chat_time_info) >= 4:
            # 超过四个
            time_old = self.private_chat_time_info[0][-1]
            if time_now - time_old <= 60:
                self.private_chat_cd = time_now
                return False
            self.private_chat_time_info.pop(0)
        chat_time_info = (friend_id, time_now)
        self.private_chat_time_info.append(chat_time_info)
        return True

    @events.ListenTo(events.ON_CHAT_EVENT)
    def OnChatInfo(self, channel, msg):
        self.RefreshAll()

    def AddPrivateChat(self, avatar_id, avatar_name):
        # 先看下是否已经在私聊里
        if self.cur_status != STATUS_EXPAND:
            self.OnExpand()
        for chat_info in self.chat_avatar_info_list:
            if chat_info[0] == avatar_id:
                # 切信道
                self.cur_channel = CHANNEL_PRIVATE
                self.chat_avatar_info_index = self.chat_avatar_info_list.index(chat_info)
                self.RefreshAll()
                return
        # 不在自己得私聊里
        if time.time() - self.private_chat_cd <= 120 and avatar_id not in genv.avatar.friends:
            # 私信过于频繁
            gui.Prompt(1267)
            return
        self.chat_avatar_info_list.append((avatar_id, avatar_name))
        if len(self.chat_avatar_info_list) > 3:
            self.chat_avatar_info_list.pop(0)
        self.cur_channel = CHANNEL_PRIVATE
        self.chat_avatar_info_index = len(self.chat_avatar_info_list) - 1
        self.RefreshAll()

    def OnTxtChatNotify(self, widget=None, char=None, is_down=None):
        if char == DesktopInput.KEY_TAB:
            self.OnKeyTab(is_down)
        elif char == DesktopInput.KEY_ESCAPE:
            self.OnKeyEsc(is_down)
        elif char in (DesktopInput.KEY_RETURN, DesktopInput.KEY_NUMPADENTER):
            self.OnKeyEnter(is_down)

    def ShowPersonalCardNode(self, show_node=None):
        detail_node = self.node_personal_card
        target_node = getattr(detail_node, 'target_node', None)
        if not show_node or target_node == show_node:
            detail_node.target_node = None
            detail_node.visible = False
        else:
            detail_node.target_node = show_node
            detail_node.visible = True
            # 看下是否是自己得好友
            detail_node.SetPlayerInfo(show_node.GetPlayerInfo(), SHOW_SOURCE_HALL_CHAT)
            genv.avatar.RequestPlayerCardInfo(show_node.player_guid)

    def OnMouseLeftClick(self, btn):
        # 点击画面以外的地方关闭，且不阻断本次点击事件
        pos = btn.getTouchEndPosition()
        if not self.panel_text.hitTest(pos) and not self.node_personal_card.hitTest(pos):
            if self.cur_status != STATUS_STORAGE:
                self.OnStorage()

    @events.ListenTo(events.ON_READ_CHAT)
    def CheckMessagesRedDot(self):
        avatar = genv.avatar
        if not avatar:
            return
        if not hasattr(avatar, 'unread_msgs'):
            return
        avatar = genv.avatar
        if not avatar:
            return
        if not hasattr(avatar, 'unread_msgs'):
            return
        if avatar.GetCurUnreadMsgs(ichat.ChatChannel.HallTeam):
            # 有小队或者其他得啥，直接加红点
            CheckReddotForWidget(self.panel_channel_1, 1, show_number=False)
        else:
            CheckReddotForWidget(self.panel_channel_1, 0, show_number=False)

        if avatar.GetCurUnreadMsgs(ichat.ChatChannel.HallFriend):
            # 有小队或者其他得啥，直接加红点
            CheckReddotForWidget(self.panel_channel_2, 1, show_number=False)
        else:
            CheckReddotForWidget(self.panel_channel_2, 0, show_number=False)

    @events.ListenTo(events.ON_RECV_PLAYER_CARD_INFO)
    def OnRecvPlayerCardInfo(self, avatar_id, card_info):
        if self.cur_status != STATUS_EXPAND:
            return
        detail_node = self.node_personal_card
        target_node = getattr(detail_node, 'target_node', None)
        if not detail_node.visible or not target_node:
            return
        card_info.update(target_node.GetPlayerInfo())
        card_info['guid'] = avatar_id
        # 段位信息处理一下
        show_qualifying_type = card_info.get('show_qualifying_type', None)
        segment_data = BuildRankInfo(card_info, show_qualifying_type)
        card_info['segment_data'] = [segment_data, ]
        detail_node.SetPlayerInfo(card_info, SHOW_SOURCE_HALL_CHAT)

    def OnSliderScroll(self, percent):
        self.lv_txt.SetTotalPercent(percent / 100.0)

    def OnListChatScroll(self, percent):
        self.panel_slider.SetPercent(self.lv_txt.GetSliderPercent() * 100)

    def SetEnableChatInput(self, enable):
        self.enable_chat_input = enable
        self.txt_chat.text = ''
        self.img_bai.visible = not enable  # 屏蔽底层输入
        self.txt_chat.opacity = 255 if enable else 200  # 变灰色，禁止输入