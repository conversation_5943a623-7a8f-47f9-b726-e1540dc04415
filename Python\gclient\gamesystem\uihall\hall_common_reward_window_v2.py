# -*- coding: utf-8 -*-
# author: lvx<PERSON>ui
# date: 2025/5/28
import math

from gclient import lang, cconst
from gclient.cconst import PC_KEY_UI
from gclient.data import lobby_item_data, hall_model_pos_data
from gclient.framework.ui import ui_define
from gclient.framework.ui.commonnodes.common_reward_list import RewardType
from gclient.framework.ui.commonnodes.ui_common_button import ButtonData, CreateBottomButtons
from gclient.framework.ui.ui_helper import HelperWindow, HelperNode
from gclient.framework.ui.widgets import UIText, UITexture
from gclient.framework.ui.widgets.ui_listview_grid import RowItemNode
from gclient.framework.util.desktop_input import DesktopInput
from gclient.framework.util.gameinput_controller import ListenPc<PERSON>ey
from gclient.gamesystem.uihall.career.career_common_nodes import CommonRewardNode, CareerModelTouchPanel, \
    CareerModelShowCtrl
from gclient.gamesystem.uihall.room.room_common_node import UIList<PERSON>iew<PERSON>ycleGrid
from gshare import consts
import cc
from gclient.framework.camera.cam_trans_utils import degree_to_radian
import MType


class RewardModelTouchPanel(CareerModelTouchPanel):
    def __init__(self, widget):
        super().__init__(widget)
        self.model_ctrl = RewardModelShowCtrl.instance()


class RewardModelShowCtrl(CareerModelShowCtrl):
    MODEL_Y_OFFSET = 0
    CAMERA_ID = cconst.HALL_CAMERA_VOID_INSPECT

    def UpdateModelTrans(self):
        pos = hall_model_pos_data.data[16].get('translation')
        rot = hall_model_pos_data.data[16].get('rotation')
        rot = (degree_to_radian(rot[0]), degree_to_radian(rot[1]), degree_to_radian(rot[2]))

        trans = MType.Matrix4x3()
        trans.translation = MType.Vector3(*pos)
        trans.set_pitch_yaw_roll(rot[0], rot[1], rot[2])
        self.show_model.model.Transform = trans

        raw_pos = self.show_model.model.Transform.translation
        # origin_pos = self.show_model.GetPrimWorldBoundCentreWithAttachments()
        weapon_box = self.show_model.GetPrimWorldBoundWithAttachments()
        weapon_center = (weapon_box.min + weapon_box.max) * 0.5
        offset = weapon_center - raw_pos
        new_pos = MType.Vector3(*pos) - offset

        trans = MType.Matrix4x3()
        trans.translation = new_pos
        trans.set_pitch_yaw_roll(rot[0], rot[1], rot[2])
        self.show_model.model.Transform = trans


class ModelNameNode(HelperNode):
    def InitNode(self):
        self.root = root = self.seek('panel_firearms')
        self.txt_name = root.seek('txt_1', UIText)
        self.img_quality = self.txt_name.seek('icon_quality')

    def SetData(self, name, quality):
        self.txt_name.text = name
        self.img_quality.color = cconst.CAREER_QUALITY_LEVEL_COLOR[quality]


class RewardNode(RowItemNode):
    def __init__(self, widget):
        super().__init__(widget)
        self.reward = self.seek('node_rewards', CommonRewardNode)

    def RefreshInfo(self, info):
        self.reward.SetData({
            'item_id': info.get('item_id', 0),
            'type': RewardType.ITEM,
            'item_count': info.get('item_count', 1)
        })
        self.reward.visible = False

    def PlayInAnim(self, anim_cb):
        self.reward.visible = True
        self.reward.PlayAnim('in', callback=anim_cb)

    def PrepareInAnim(self):
        self.reward.MakeSureActionByLoadFile()
        self.reward.PauseAnim(self.GetStartFrame('in'))


class HallCommonRewardWindowBase(HelperWindow):
    def InitData(self):
        self.ui_chain = None
        self.show_zorder = self.ZORDER

    def AfterShow(self):
        super().AfterShow()
        if self.ui_chain:
            self.ui_chain.AfterShow(self)

    def Close(self, _=None):
        if self.ui_chain:
            self.ui_chain.RunNext(self)
        else:
            super().Close(_)

    def SetShowZorder(self, zorder):
        self.show_zorder = zorder if zorder is not None else self.ZORDER
        self.setLocalZOrder(self.show_zorder)


class HallCommonRewardPanel(UIListViewCycleGrid):

    def __init__(self, widget):
        super(HallCommonRewardPanel, self).__init__(widget)
        self.anim_timer = None

    def PlayInAnim(self):
        self.StopInAnim()

        for item in self._items:
            item.PrepareInAnim()

        self.PlayNextItemInAnim(0)

    def PlayNextItemInAnim(self, idx):
        if idx < 0 or idx >= len(self._items):
            self.StopInAnim()
            return

        self._items[idx].PlayInAnim(None)
        self.anim_timer = self.add_timer(0.04, lambda _: self.PlayNextItemInAnim(idx + 1))

    def StopInAnim(self):
        self.anim_timer and self.cancel_timer(self.anim_timer)
        self.anim_timer = None


class HallCommonRewardWindow(HallCommonRewardWindowBase):
    CSB_NAME = 'UIScript/og_level_reward.csb'
    ZORDER = ui_define.SHOW_LEVEL_WINDOW
    CLOSE_ON_ESC = True
    DESTROY_ON_CLOSE = True
    AUTO_IN_ANIM = ''
    AUTO_OUT_ANIM = ''
    IS_POP_UP = True
    HALL_LEVEL = cconst.LEVEL_HALL_PROPERTY_LIGHTING

    REWARD_PER_ROW = 6

    def InitData(self):
        super().InitData()
        # 按稀有度排序后的奖励
        self.sorted_reward_list = []
        # 需要展示模型的奖励
        self.model_reward_list = []
        self.cur_show_index = 0
        self.cur_item_id = 0

        self.model_ctrl = RewardModelShowCtrl.instance()
        self.last_camera_id = 0
        self.last_hall_level_id = cconst.LEVEL_MAIN_HALL
        self.ui_chain = None
        self.shortcut_key = {}

        self.is_playing_anim = False

    def InitNode(self):
        super().InitNode()
        # self.HelperSeek('img_bg_0').visible = False
        self.HelperSeek('btn_right').visible = False
        self.img_bg = self.HelperSeek('img_bg_0')
        self.btn_node = CreateBottomButtons(self)

        self.txt_get_item = self.HelperSeek('txt_item', UIText)
        self.icon_text = self.HelperSeek('icon_text')

        self.node_get_gun = self.HelperSeek('node_fireearms', ModelNameNode)
        self.img_gun_0 = self.HelperSeek('img_gun_0', UITexture)
        self.img_gun_0.visible = False
        self.touch_panel = self.HelperSeek('panel_model', RewardModelTouchPanel)
        self.touch_panel.setSwallowTouches(True)

        self.panel_rewards = self.HelperSeek('panel_center')
        self.listview_rewards = self.panel_rewards.seek('lv_1', HallCommonRewardPanel)
        self.listview_rewards.create(None, self.REWARD_PER_ROW, obj_type=RewardNode)
        self.img_bg.visible = True
        self.img_bg.setTouchEnabled(True)
        self.img_bg.opacity = 0

    def OnShow(self, info):
        """
        {
            'reward_data': [
                {'item_id': 1100001730, 'item_count': 1}
            ]
        }
        """
        self.reward_list = info.get('reward_data', [])
        self.ui_chain = info.get('ui_chain', None)

    def OnClose(self):
        super().OnClose()
        self.StopAnim()

    def RefreshData(self):
        self.SortRewards()
        self.BuildModelRewardList()
        self.cur_show_index = 0
        self.cur_item_id = 0

    def RefreshNode(self):
        self.listview_rewards.RefreshItems(self.reward_list)
        if len(self.reward_list) < self.REWARD_PER_ROW:
            self.CenterRewards()

        self.OnClickNext()

    def CenterRewards(self):
        first_row_list = self.listview_rewards._rows[0].listview
        first_reward = self.listview_rewards._items[0]
        width = first_row_list.GetAllItemsSize(True).width
        content_width = self.panel_rewards.getContentSize().width
        content_height = self.panel_rewards.getContentSize().height
        anchor_x = width * 0.5 / content_width
        anchor_y = (content_height - first_reward.getPositionY()) * 1.0 / content_height
        self.panel_rewards.setAnchorPoint(cc.Vec2(anchor_x, anchor_y))

    def SortRewards(self):
        self.reward_list = sorted(
            self.reward_list,
            key=lambda x: lobby_item_data.data.get(x.get('item_id', 0)).get('quality', 1), reverse=True
        )

    def BuildModelRewardList(self):
        self.model_reward_list = []
        for reward in self.reward_list:
            item_proto = lobby_item_data.data.get(reward['item_id'], {})
            item_sub_type = item_proto.get('sub_type', 0)
            if item_sub_type // 100 * 100 in (consts.WarehouseItemType.GunSkin, consts.WarehouseItemType.GunGuise)\
                    or item_sub_type == consts.ItemSubType.OriginGun:
                self.model_reward_list.append(reward)

    def ShowSingleReward(self):
        self.is_playing_anim = False

        self.txt_get_item.visible = False
        self.icon_text.visible = False
        self.listview_rewards.visible = False

        self.node_get_gun.visible = True

        reward = self.model_reward_list[self.cur_show_index]
        reward_id = reward['item_id']
        item_proto = lobby_item_data.data.get(reward_id, {})
        item_sub_type = item_proto.get('sub_type', 0)

        left_data = []
        right_data = [
            ButtonData(lang.CALCULATION_NEXT, self.OnClickNext, PC_KEY_UI.KEY_SPACE, None, button_type=1),
        ]
        if item_sub_type != consts.ItemSubType.OriginGun:
            right_data.append(
                ButtonData(lang.REWARD_EQUIP_IMMEDIATELY, self.OnClickEquip, PC_KEY_UI.KEY_F, None)
            )
        self.btn_node.SetData(left_data, right_data)
        self.shortcut_key[PC_KEY_UI.KEY_F] = True

        self.cur_item_id = reward_id

        # self.model_ctrl.InitModel(reward_id)
        self.model_ctrl.RemoveHiddenReason(cconst.HIDDEN_REASON_COMMON_SHOW)

        self.node_get_gun.SetData(
            item_proto.get('name', ''),
            item_proto.get('quality', 1)
        )

        self.node_get_gun.PlayAnim('in')
        self.cur_show_index += 1

    def ShowAllRewards(self):
        self.txt_get_item.visible = True
        self.icon_text.visible = True
        self.listview_rewards.visible = True

        self.node_get_gun.visible = False

        self.listview_rewards.PlayInAnim()

        left_data = []
        right_data = [
            ButtonData(lang.CALCULATION_NEXT, self.OnClickNext, PC_KEY_UI.KEY_SPACE, None, button_type=1),
        ]
        self.btn_node.SetData(left_data, right_data)
        self.shortcut_key[PC_KEY_UI.KEY_F] = False

        self.model_ctrl.AddHiddenReason(cconst.HIDDEN_REASON_COMMON_SHOW)
        self.PlayAnim('in_1')
        self.cur_show_index += 1

    @ListenPcKey(DesktopInput.KEY_SPACE)
    def OnClickNext(self, is_down=True):
        if not is_down:
            return

        if self.is_playing_anim:
            return

        model_rewards_len = len(self.model_reward_list)
        all_rewards_len = len(self.reward_list)
        if self.cur_show_index < model_rewards_len:
            # 播放动画后再播放新的动画并显示
            self.PlayGunAnim('in', None)
        elif self.cur_show_index == len(self.model_reward_list) and all_rewards_len != model_rewards_len:
            # 有普通道具时，才显示列表
            self.ShowAllRewards()
        else:
            self.Close()
            return

    def PlayGunAnim(self, anim, callback):
        self.txt_get_item.visible = False
        self.icon_text.visible = False
        self.listview_rewards.visible = False

        self.node_get_gun.visible = False

        reward = self.model_reward_list[self.cur_show_index]
        reward_id = reward['item_id']
        self.model_ctrl.InitModel(reward_id)
        self.model_ctrl.AddHiddenReason(cconst.HIDDEN_REASON_COMMON_SHOW)

        self.is_playing_anim = True
        self.PlayAnim(anim, callback=callback)

    def FrameEventCallback(self, data):
        super().FrameEventCallback(data)
        event_str = data.getEvent()
        if event_str == 'gun_name_in':
            self.ShowSingleReward()
        elif event_str == 'gun_in':
            self.model_ctrl.RemoveHiddenReason(cconst.HIDDEN_REASON_COMMON_SHOW)

    @ListenPcKey(DesktopInput.KEY_F)
    def OnClickKeyF(self, is_down=True):
        if not is_down:
            return
        if not self.shortcut_key.get(PC_KEY_UI.KEY_F, False):
            return

        self.OnClickEquip(None)

    def OnClickEquip(self, _=None):
        # reward = self.model_reward_list[self.cur_show_index]
        # reward_id = reward['reward_id']
        # item_proto = lobby_item_data.data.get(reward_id, {})
        avatar = genv.avatar
        if not avatar:
            return

        item_proto = lobby_item_data.data.get(self.cur_item_id, {})
        item_sub_type = item_proto.get('sub_type', 0)
        if item_sub_type // 100 * 100 in (consts.WarehouseItemType.GunSkin, consts.WarehouseItemType.GunGuise):
            items = avatar.warehouse.GetItemByItemId(self.cur_item_id, single=False)
            if items:
                item = items[0]
                if item.skin_id:
                    avatar.CallServer('ArmGunSkin', item.gun_id, item.guid)
                elif item.guise_id:
                    avatar.CallServer('ArmGunGuise', item.gun_id, item.guid)

                left_data = []
                right_data = [
                    ButtonData(lang.CALCULATION_NEXT, self.OnClickNext, PC_KEY_UI.KEY_SPACE, None, button_type=1),
                    ButtonData(lang.COMMON_TEXT_EQUIPED, None, PC_KEY_UI.KEY_F, None)
                ]
                self.btn_node.SetData(left_data, right_data)

            return
        elif item_sub_type == consts.ItemSubType.OriginGun:
            pass

        gui.Prompt(204)

    def PreVisible(self, value):
        space = genv.space
        if value:
            self.last_hall_level_id = space.load_level_id
            self.last_camera_id = space.hall_space_special_camera_id
            space.ChangeSpecialCameraId(cconst.HALL_CAMERA_VOID_INSPECT, half_life=0)
        else:
            if self.last_hall_level_id:
                space.LoadHallLevel(self.last_hall_level_id)
            space.ChangeSpecialCameraId(self.last_camera_id, half_life=0)
            self.model_ctrl.AddHiddenReason(cconst.HIDDEN_REASON_COMMON_SHOW)

    def OnDestroy(self):
        self.model_ctrl.Destroy()
        super().OnDestroy()
