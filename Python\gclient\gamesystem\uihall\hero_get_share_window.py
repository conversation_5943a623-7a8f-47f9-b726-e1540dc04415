# -*- coding: utf-8 -*-
import cc
from functools import partial
from gclient.data import hero_data, hero_tag_data
from gclient.framework.ui import ui_define
from gclient.framework.ui.widgets import UINode
from gclient.framework.ui.widgets.ui_listview import UIListView
from gclient.framework.ui.widgets.ui_text import UIText
from gclient.framework.util import events
from gclient.gamesystem.models.character_display_doll_model_manager import CharacterDisplayDollModelManager
from gclient.gamesystem.uihall.uicombatsys.hall_combat_window import HallHeroTagNode
from gclient import lang
from gclient.gamesystem.uihall.hall_common_reward_window_v2 import HallCommonRewardWindowBase
from gclient.framework.ui.commonnodes.ui_common_button import ButtonData, CommonBottomNode
from gclient.cconst import PC_KEY_UI
from gclient.framework.util.gameinput_controller import ListenPc<PERSON>ey
from gclient.framework.util.desktop_input import DesktopInput
from gclient.util import CinematicsBase
from gclient.util.debug_log_util import SomePreset, print_s
from gclient.util.showroom_util import ChooseHeroShowRoomUtil, ShowRoomManager


class HeroGetAndShareWindow(HallCommonRewardWindowBase):
    CSB_NAME = 'UIScript/og_role_mall_obtain_pop.csb'
    ZORDER = ui_define.SHOW_LEVEL_WINDOW
    CLOSE_ON_ESC = True
    DESTROY_ON_CLOSE = True
    AUTO_IN_ANIM = 'in'
    AUTO_OUT_ANIM = 'out'
    IS_POP_UP = True
    # HALL_LEVEL = cconst.LEVEL_WEAPON_DISPLAY

    def InitData(self):
        super().InitData()
        self.hero_id = None
        self.close_callback = None
        self.hero_tags = None

    def InitNode(self):
        super().InitNode()
        panel_name = self.root_widget.seek('panel_name_skill_tag')
        self.txt_name = panel_name.seek('txt_name', UIText)
        self.listview_tags = panel_name.seek('lv_tag', UIListView)
        self.listview_tags.create(item_num=5, obj_type=partial(HallHeroTagNode, self), callback=self.OnListviewTags)

        self.panel_hover = UINode(gui.LoadWidgetFromFileCache('UIScript/node_og_role_mall_tips.csb', parent_widget=self.root_widget.widget))
        panel_tips = self.panel_hover.seek('panel_tips')
        panel_tips.setAnchorPoint(cc.Vec2(0.0, 1.0))
        panel_tips.SetPosition(cc.Vec2(35, -22))
        self.txt_hover_tag = self.panel_hover.childex('panel_tips.txt_tips', UIText)

        panel_btn_new = self.root_widget.seek('panel_btn_new')
        panel_btn_new.seek('node_btn_1').visible = False
        self.bottom_node = panel_btn_new.seek('node_c_btn_hint_bottom', partial(CommonBottomNode, self))
        self.bottom_node.SetData([], [ButtonData(lang.COMMON_TEXT_NEXT, self.OnClickNext, PC_KEY_UI.KEY_SPACE, None, button_type=1),
                                      ButtonData(lang.HALL_HERO_FAVORITE, self.OnClickDisplayInHall, PC_KEY_UI.KEY_F, None)])
        _, right_buttons = self.bottom_node.GetButtons()
        self.btn_favorite = right_buttons[1]
        self.img_db = self.root_widget.seek('img_db')
        self.img_db.opacity = 0
        self.img_db.setTouchEnabled(True)
        self.img_db.visible = True

    def OnListviewTags(self, irange):
        len_hero_tags = len(self.hero_tags)
        for idx, node in irange:
            if idx < len_hero_tags:
                node.visible = True
                node.SetTag(self.hero_tags[idx])
            else:
                node.visible = False
    
    def RefreshFavoriteBtn(self):
        is_favorite = self.hero_id == genv.avatar.hero_id
        btn_favorite = self.btn_favorite
        btn_favorite.SetKeyNodeVisible(not is_favorite)
        btn_favorite.SetEnabled(not is_favorite)
        btn_favorite.text = lang.HALL_HERO_ALREADY_FAVORITE if is_favorite else lang.HALL_HERO_FAVORITE
        self.bottom_node.Refresh()

    
    
    @events.ListenTo(events.ON_CAPTURE_SHARE_PICTURE)
    def OnHideSomeUIByCapture(self, is_capture):
        pass

    def OnShow(self, info=None):
        gpl.SetRenderOption("OnlyDrawUI", True)
        genv.messenger.Broadcast(events.ON_SHOW_HERO_GET_WINDOW, True)
        # [DEBUG]
        import GlobalData
        import switches
        if GlobalData.IsShaderCollectionRobot or switches.IsModelCheckRobot or GlobalData.IsShaderPrecompileListRobot:
            self.visible = False
            return
        # [DEBUG]
        super(HeroGetAndShareWindow, self).OnShow(info)
        self.hero_id = info.get('hero_id')
        if not self.hero_id:
            self.hero_id = 101
        self.PlayAnim('in')
        self.RefreshInfo()
        self.close_callback = info.get('close_cb')
        self.ui_chain = info.get('ui_chain', None)

        # CharacterDisplayDollModelManager().OnEnter()
        self.AttachShowroomImageView()

    def RefreshInfo(self):
        if not self.hero_id:
            return
        hero_info = hero_data.data[self.hero_id]
        self.txt_name.text = hero_info.get('hero_name')
        self.hero_tags = hero_info.get('hero_tag')
        if self.hero_tags:
            self.listview_tags.visible = True
            self.listview_tags.total_item_num = len(self.hero_tags)
        else:
            self.listview_tags.visible = False

    def AttachShowroomImageView(self):
        node = self.img_db
        genv.TestPPV2 = node
        node.setVisible(True)
        showroom_name = "ChooseHeroShowRoomWithWorld"
        world = ShowRoomManager().GetShowRoom(showroom_name)
        if world:
            area = world.DefaultLevel.RootArea
            CinematicsBase.SetCustomCinematicsArea(area, showroom_name)
        print_s(f'AttachShowroomImageView, world : {world}', SomePreset.white_fg_purple_bg)
        self.show_room_util = ChooseHeroShowRoomUtil()
        if world:
            CharacterDisplayDollModelManager().RefreshSelectedHero(self.hero_id)
            self.show_room_util.EnterHeroDoll(showroom_name)
            self.show_room_util.SetImgView(node, showroom_name)
            ShowRoomManager().SetResolutionScale(showroom_name, 1.0)
        else:
            self.show_room_util.CreateShowRoomWithWorld(showroom_name, node, on_create_showroom_callback=self.OnShowRoomWorldCreated)

    def OnShowRoomWorldCreated(self):
        print_s('OnShowRoomWorldCreated', SomePreset.white_fg_purple_bg)
        showroom_name = "ChooseHeroShowRoomWithWorld"
        world = ShowRoomManager().GetShowRoom(showroom_name)
        area = world.DefaultLevel.RootArea
        CinematicsBase.SetCustomCinematicsArea(area, showroom_name)
        if not self.hero_id:
            self.hero_id = 101
        CharacterDisplayDollModelManager().RefreshSelectedHero(self.hero_id)

    def ShowTagInfo(self, node, tag_id):
        self.panel_hover.visible = True
        self.txt_hover_tag.text = hero_tag_data.data.get(tag_id, {}).get('tag_desc')
        pos = node.GetWorldPosition()
        self.panel_hover.SetWorldPosition(pos)

    def HideTagInfo(self):
        self.panel_hover.visible = False

    def OnClose(self):
        gpl.SetRenderOption("OnlyDrawUI", False)
        genv.messenger.Broadcast(events.ON_SHOW_HERO_GET_WINDOW, False)
        # CharacterDisplayDollModelManager().OnLeave()
        CharacterDisplayDollModelManager().first_hero_show = True
        super(HeroGetAndShareWindow, self).OnClose()
        self.close_callback and self.close_callback()
        self.close_callback = None
        # genv.messenger.Broadcast(events.ON_SHOW_HERO_GET_WINDOW, False)

    def OnClickNext(self, btn):
        self.Close()

    def OnClickDisplayInHall(self, btn):
        if not self.hero_id:
            return
        avatar = genv.avatar
        if not avatar:
            return
        if self.hero_id == avatar.hero_id:
            return
        genv.avatar.CallServer('ChooseHero', self.hero_id)
    
    @events.ListenTo(events.ON_HERO_ID_CHANGE)
    def OnHeroIdChanged(self):
        self.RefreshFavoriteBtn()

    @ListenPcKey(DesktopInput.KEY_SPACE)
    def OnSpaceDown(self, is_down=True):
        if not is_down:
            return
        self.OnClickNext(None)

    @ListenPcKey(DesktopInput.KEY_F)
    def OnKeyFDown(self, is_down=True):
        if not is_down:
            return
        self.OnClickDisplayInHall(None)