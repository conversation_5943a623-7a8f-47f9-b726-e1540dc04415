# -*- coding: utf-8 -*-
# author: lv<PERSON><PERSON><PERSON>
import time
import <PERSON><PERSON>
from functools import partial

from gclient import lang
from gclient.cconst import PC_KEY_UI
from gclient.data import match_data, space_data, room_data
from gclient.framework.ui import ui_define
from gclient.framework.ui.commonnodes.ui_common_button import ButtonData, CreateBottomButtons
from gclient.framework.ui.ui_helper import HelperWindow, HelperNode, Size2String
from gclient.framework.ui.widgets import UIListViewCycle, UITexture, UIText, UIButton
from gclient.framework.ui.widgets.ui_slider import CustomUISliderVertical
from gclient.framework.ui.widgets.ui_textfield import UITextField
from gclient.framework.util import events
from gclient.framework.util.desktop_input import DesktopInput
from gclient.framework.util.gameinput_controller import ListenPc<PERSON>ey
from gclient.gamesystem.uihall.room import room_util, room_config
from gclient.gamesystem.uihall.room.room_mgr import RoomMgr
from gclient.gamesystem.uihall.room.room_setting_window_v2 import RoomSetting<PERSON>indow
from gclient.ui.common.hoverable_button import HoverableCutButton
from gshare import iroom
from gshare.iroom import RoomStatus
import cc
import MPlatform


class RoomNode(HelperNode):

    def InitData(self):
        self.room_id = 0

    def InitNode(self):
        root = self.root = self.seek('panel_1')
        self.img_hov = root.seek('img_hov')
        self.img_slc = root.seek('img_slc')
        self.img_map = root.childex('panel_pic.img_pic', UITexture)
        self.txt_map = root.childex('panel_pic.txt_play', UIText)
        self.txt_name = root.seek('txt_title', UIText)
        self.img_lock = self.txt_name.seek('ImageView_10467')
        self.txt_match_type = root.childex('panel_mode.txt_mode', UIText)
        self.txt_match_mode = root.childex('panel_mode.txt_mode_0', UIText)
        self.txt_room_mode = self.txt_match_mode.childex('panel_txt.txt_mode_1', UIText)
        self.txt_num = root.childex('panel_num.txt_1', UIText)
        self.txt_status = root.childex('panel_num.txt_status', UIText)

        self.root.SetMouseMoveEventEnable(True)
        self.root.EnableTouch(True, False)
        self.root.onClick = self.OnClick
        self.root.onDoubleClick = self.OnDoubleClick
        self.root.onMouseHover = self.OnHover
        self.img_slc.visible = False

        self.img_hov.visible = False
        self.root.BuildHighlight(self.img_hov)
        self.local_zorder = self.getLocalZOrder()

    def SetData(self, room_info):
        self.room_info = room_info
        self.room_id = room_info.get('rid')
        match_type = room_info.get('match_type')
        spaceno = room_info.get('spaceno')
        self.need_pwd = bool(room_info.get('password_hash'))
        self.match_proto_getter = match_data.data[match_type].get
        self.space_proto_getter = space_data.data[spaceno].get
        self.room_proto_getter = room_data.data[match_type].get

        self.option_type = room_info.get('option_type')
        self.RefreshNode()

    def RefreshData(self):
        pass

    def RefreshNode(self):
        match_type = self.room_info.get('match_type')
        self.img_map.texture = self.space_proto_getter('map_icon')
        self.txt_map.text = self.space_proto_getter('name')
        match_proto_getter = match_data.data[match_type].get

        # self.panel_lock.visible = room_info['password_hash']
        self.txt_name.text = self.room_info.get('room_name')
        self.txt_num.text = '%d/%d' % (self.room_info['battle_count'], self.room_info['max_people'])
        self.txt_match_type.text = match_proto_getter('prefix_match_name')
        self.txt_match_mode.text = lang.MATCH_MODE_HALL.get(self.room_info.get('match_mode', 1))
        self.txt_room_mode = room_util.GetRoomModeText(self.option_type)
        self.img_lock.visible = self.need_pwd

        if self.room_info['room_status'] == RoomStatus.Waiting:
            self.txt_status.text = '#61dd7e%s' % lang.ROOM_STATUS_WAITING
        else:
            self.txt_status.text = '#f07338%s' % lang.ROOM_STATUS_GAMING

    def SetSelect(self, select):
        self.img_slc.visible = select

    def OnClick(self, _=None):
        self.ctrl.OnClickRoomNode(self)

    def OnDoubleClick(self, _=None):
        self.ctrl.OnClickJoin()

    def OnHover(self, btn, visible):
        self.root.EnableHighlight(visible)
        self.root.setLocalZOrder(self.local_zorder + 10 if visible else self.local_zorder)
        if visible:
            self.root.runAction(cc.ScaleTo.create(0.1, 1.05), False)
        else:
            self.root.runAction(cc.ScaleTo.create(0.1, 1), False)


class RoomSearchPanel(HelperNode):
    def InitNode(self):
        self.img_hov = self.seek('img_hov')
        self.txt_enter = self.seek('txt_id', UITextField)
        # self.txt_enter.onChnagedText = self.OnChangeText
        self.btn_search = self.seek('btn_search', UIButton)
        self.btn_filter = self.seek('btn_cz')
        self.txt_filter = self.btn_filter.seek('txt_des', UIText)
        self.txt_filter.text = lang.ROOM_FILTER
        self.btn_search.onClick = self.OnBtnSearchClick
        self.btn_search.onMouseHover = self.OnBtnSearchHover
        self.SetMouseMoveEventEnable(True)
        self.btn_search.setSwallowMMove(False)
        self.txt_enter.setSwallowMMove(False)
        self.EnableTouch(True, False)
        self.onMouseHover = self.OnHover

        self.img_hov.visible = False
        self.BuildHighlight(self.img_hov)
        self.btn_filter.BuildHighlight(self.btn_filter.seek('img_bg'))
        self.btn_filter.EnableTouch(True)
        self.btn_filter.SetMouseMoveEventEnable(True)
        self.btn_filter.onClick = lambda _: gui.Prompt(204)
        self.btn_filter.onMouseHover = self.OnBtnFilterHover


    def OnHover(self, btn, visible):
        self.EnableHighlight(visible)

    def OnBtnSearchClick(self, btn):
        text = self.txt_enter.text
        if text and text.isdigit():
            rid = int(text)
            genv.avatar.SearchRoom(rid)

    def OnBtnSearchHover(self, btn, hover):
        self.btn_search.opacity = 255 if hover else 102

    def OnBtnFilterHover(self, btn, hover):
        self.btn_filter.EnableHighlight(hover)
        if hover:
            self.btn_filter.runAction(cc.ScaleTo.create(0.1, 1.05), False)
        else:
            self.btn_filter.runAction(cc.ScaleTo.create(0.1, 1), False)

    def HasInput(self):
        return self.txt_enter.text

    def IsInputting(self):
        return self.txt_enter.isInInputState()


class RoomInfoNode(HelperNode):
    def InitNode(self):
        self.txt_key = self.seek('txt_des_1', UIText)
        self.txt_value_str = self.seek('txt_des_2', UIText)
        self.txt_value_num = self.seek('txt_num', UIText)
        self.btn_copy = self.txt_value_num.seek('btn_fz', partial(HoverableCutButton, 'img_fz'))
        self.btn_copy.onClick = self.OnClickCopy

    def SetData(self, key, value, is_default=True, show_copy=False):
        self.key = key
        self.value = value
        self.is_num = isinstance(value, int)
        # self.color = color
        self.is_default = is_default
        self.show_copy = show_copy
        self.RefreshNode()

    def RefreshNode(self):
        self.txt_key.text = self.key
        self.txt_value_str.visible = not self.is_num
        self.txt_value_num.visible = self.is_num
        if self.is_num:
            self.txt_value_num.text = self.value
            self.btn_copy.visible = self.show_copy
        else:
            self.txt_value_str.text = self.value

        if self.is_default:
            self.txt_value_num.text_color = (0xff, 0xff, 0xff)
            self.txt_value_str.text_color = (0xff, 0xff, 0xff)
        else:
            self.txt_value_num.text_color = (0xeb, 0xf2, 0x1f)
            self.txt_value_str.text_color = (0xeb, 0xf2, 0x1f)
            self.txt_value_str.opacity = 255
            self.txt_value_num.opacity = 255

    def OnClickCopy(self, _=None):
        MPlatform.SetClipboardText(str(self.value))
        gui.Prompt(1271)


class RoomWindow(HelperWindow):
    CSB_NAME = 'UIScript/og_room_list_xd_new_edition_v3.csb'
    ZORDER = ui_define.SHOW_LEVEL_WINDOW
    SCENE_IDS = (ui_define.UI_SCENEID_WINDOW,)
    CLOSE_ON_ESC = False
    PERMANENT = False
    AUTO_IN_ANIM = False
    AUTO_OUT_ANIM = False
    AUTO_LOOP_ANIM = False
    AUTO_REFRESH = True
    HALL_WINDOW = False

    ROOM_REFRESH_CD = 3.0
    ROOM_AUTO_REFRESH_TIME = 5.0

    def InitData(self):
        # self.check_card_timer = None
        self.match_mode = 0
        self.match_type = 0
        self.click_btn_quick_time = 0
        self.select_room_node = None
        self.selected_room_id = 0
        self.match_type_list = [0] + list(room_data.data.keys())
        self.room_nodelist = {}

        self.basic_info_map = {}
        self.adv_info_map = []
        self.TextDict = RoomMgr.instance().GetRoomInfoText()

        self.refresh_timer = None

    def InitNode(self):
        self.panel_mid = self.HelperChildex('panel_room.panel_mid')
        self.panel_top = self.HelperChildex('panel_room.panel_top')
        self.panel_map_des = self.HelperChildex('panel_room.panel_map_des')
        self.panel_btn = self.HelperSeek('panel_btn')
        self.InitPanelMid(self.panel_mid)
        self.InitPanelTop(self.panel_top)
        self.InitPanelMapDes(self.panel_map_des)
        self.InitPanelBtns()

        self.panel_map_des.visible = False
        self.panel_empty.visible = True
        self.listview_room.visible = False

    def InitPanelMid(self, panel):
        self.panel_empty = panel.seek('panel_empty')
        self.txt_empty = self.panel_empty.seek('text_empty', UIText)
        self.txt_empty.text = lang.ROOM_EMPTY_TEXT

        self.listview_room = panel.seek('listview_room', UIListViewCycle)
        self.listview_room.setClippingType(0)
        self.listview_room.create(callback=self.OnListviewRoom, obj_type=partial(RoomNode, self), hide_redundant=True)

    def InitPanelTop(self, panel):
        self.panel_search = panel.seek('panel_l', partial(RoomSearchPanel, self))
        # panel_r = panel.seek('panel_r')
        # self.match_type_combox = panel_r.seek('panel_expand', UIComboBox)
        # self.match_type_combox.visible = True

        # self.panel_cd = panel_r.seek('panel_cd')
        # self.refresh_progress_node = self.panel_cd.seek('progress_time', UIProgressTimer)

    def InitPanelMapDes(self, panel):
        self.txt_room_name = panel.seek('txt_name', UIText)
        self.img_room = panel.childex('panel_pic.img_pic', UITexture)
        self.txt_map_name = panel.childex('panel_pic.txt_des', UIText)
        self.txt_ob_num = panel.childex('panel_pic.ListView_10230.panel_dw_num.txt_num', UIText)
        self.listview_basic_info = panel.childex('panel_data.list_information_1', UIListViewCycle)
        self.listview_adv_info = panel.childex('panel_data.list_information_2', UIListViewCycle)
        self.listview_basic_info.create(callback=self.OnListviewBasicInfo, obj_type=partial(RoomInfoNode, self), hide_redundant=True)
        self.listview_adv_info.create(callback=self.OnListviewAdvInfo, obj_type=partial(RoomInfoNode, self), hide_redundant=True, scroll_listener=self.OnListScroll)
        self.listview_adv_info.setClippingType(0)

        self.panel_slider = panel.childex('panel_data.panel_mask_0_0')
        self.slider_opts = self.panel_slider.seek('slider', CustomUISliderVertical)
        self.slider_opts.Create(self.OnSliderScroll)

    def OnSliderScroll(self, percent):
        self.listview_adv_info.SetTotalPercent(percent / 100.0)

    def OnListScroll(self, _):
        self.slider_opts.SetPercent(self.listview_adv_info.GetSliderPercent() * 100)

    def InitPanelBtns(self):
        self.HelperSeek('node_c_btn_hint_bottom').visible = False

        self.bottom_node = CreateBottomButtons(self)
        self.bottom_node.SetData([
            ButtonData(lang.COMMON_TEXT_RETURN, self.OnClickReturn, PC_KEY_UI.KEY_ESC, None),
        ], [
            ButtonData(lang.CUSTOM_ROOM_JOIN_ROOM, self.OnClickJoin, PC_KEY_UI.KEY_SPACE, None, button_type=1),
            ButtonData(lang.CUSTOM_ROOM_OBSERVE, self.onClickObserve, PC_KEY_UI.KEY_R, None),
            ButtonData(lang.ROOM_CREATE_ROOM, self.OnClickCreate, PC_KEY_UI.KEY_F, None),
            ButtonData(lang.ROOM_QUICK_JOIN, self.OnClickQuickJoin, PC_KEY_UI.KEY_Z, None),
            ButtonData(lang.COMMON_TEXT_REFRESH, self.OnClickRefresh, PC_KEY_UI.KEY_F5, None)
        ])
        _, right = self.bottom_node.GetButtons()
        self.btn_refresh = right[4]

    def OnListviewRoom(self, irange):
        self.room_nodelist = {}
        for idx, node in irange:
            node.SetData(self.room_list[idx])
            node.SetSelect(False)
            self.room_nodelist[self.room_list[idx]['rid']] = node

    def OnListviewBasicInfo(self, irange):
        info_list = list(self.basic_info_map.items())
        for i, node in irange:
            node.SetData(info_list[i][0], info_list[i][1], show_copy=True)

    def OnListviewAdvInfo(self, irange):
        info_list = self.adv_info_map
        for i, node in irange:
            node.SetData(info_list[i][0], info_list[i][1], info_list[i][2])

    def OnShow(self, info):
        MUI.UseUIMultiBlur(False)
        self.match_type = 0
        self.match_mode = 0
        self.selected_room_id = 0

        # self.RefreshTop()
        self.RefreshRoomList()

        self.CancelRefreshTimer()
        self.refresh_timer = self.add_repeat_timer(self.ROOM_AUTO_REFRESH_TIME, self.RefreshRoomList)

    def OnClose(self):
        self.CancelRefreshTimer()

    def CancelRefreshTimer(self):
        self.refresh_timer and self.cancel_timer(self.refresh_timer)
        self.refresh_timer = None

    def RefreshRoomList(self):
        if not self.real_visible:
            return

        if self.panel_search.HasInput() or self.panel_search.IsInputting():
            return

        avatar = genv.avatar
        filter_info = {}
        if self.match_type:
            filter_info['match_type'] = self.match_type
        if self.match_mode != 0:
            filter_info['match_mode'] = self.match_mode
        avatar.GetRoomList(filter_info)

    @events.ListenTo(events.ON_REFRESH_ROOM_LIST)
    def OnRefreshRoomList(self, filter_info, room_list):
        match_type = filter_info.get('match_type', 0)
        match_mode = filter_info.get('match_mode', 0)

        if match_type != self.match_type or match_mode != self.match_mode:
            return
        room_list.sort(key=lambda x: (
            bool(x['password_hash']),
            x['room_status'] != iroom.RoomStatus.Waiting,
            x["max_people"] == x["battle_count"],
            x["max_people"] - x["battle_count"],
            -x['create_time']
        ))
        self.room_list = room_list
        self.RefreshRoomListUI()


    @events.ListenTo(events.ON_SEARCH_ROOM_LIST)
    def OnSearchRoomList(self, room_list):
        self.room_list = room_list
        self.RefreshRoomListUI()

    def RefreshRoomListUI(self):
        self.listview_room.total_item_num = len(self.room_list)
        self.panel_empty.visible = not bool(len(self.room_list))
        self.listview_room.visible = bool(len(self.room_list))

        for n in self.room_nodelist.values():
            if self.selected_room_id == n.room_id:
                self.OnClickRoomNode(n)
                return

        if self.room_nodelist and self.selected_room_id == 0:
            self.OnClickRoomNode(list(self.room_nodelist.values())[0])
        else:
            self.OnClickRoomNode(None)

    # def RefreshTop(self):
    #     match_type_data = [self._GetMatchTypeName(match_type) for match_type in self.match_type_list]
    #     self.match_type_combox.SetData(match_type_data, choose_cb=self.OnChooseMatchTypeCombox)

    def _GetMatchTypeName(self, match_type):
        return match_data.data.get(match_type, {}).get('prefix_match_name') if match_type else lang.COMMON_TEXT_ALL

    def OnChooseMatchTypeCombox(self, idx):
        self.match_type = self.match_type_list[idx]
        self.RefreshRoomList()

    def OnClickReturn(self, btn):
        self._parent_window.Close()

    @ListenPcKey(DesktopInput.KEY_SPACE)
    def OnClickJoin(self, _=None):
        # 这时候点击加入房间
        if not self.select_room_node:
            return

        room_info = self.select_room_node.room_info
        need_pwd = bool(room_info.get('password_hash'))

        self.join_func = genv.avatar.JoinRoom

        def OnConfirm(value):
            if not value:
                return

            if need_pwd:
                self.ShowPasswordPanel()
            else:
                self.CheckRoomDlc(room_info["rid"], '', False)

        avatar = genv.avatar
        if not avatar.CheckCanPlayForYuanbao():
            gui.Prompt(540)
            return

        if len(avatar.team.member_dict) >= 2:
            gui.Notice('', lang.ROOM_LEAVE_TEAM_AND_JOIN_CONTENT, callback=OnConfirm)
        else:
            OnConfirm(True)

    def ShowPasswordPanel(self):
        gui.InputNotice(
            lang.ROOM_PASSWORD_TITLE,
            lang.ROOM_INPUT_PASSWORLD,
            self.OnPasswordInput
        )

    def OnPasswordInput(self, password):
        room_info = self.select_room_node.room_info
        if iroom.GetMd5(password) == room_info.get('password_hash'):
            self.CheckRoomDlc(room_info["rid"], password, False)
            return True, ''
        else:
            return False, lang.ROOM_PASSWORD_ERROR

    def CheckRoomDlc(self, room_id, pwd, with_team):
        room_info = self.select_room_node.room_info
        spaceno = room_info.get('spaceno')
        space_proto_getter = space_data.data.get(spaceno, {}).get
        dlc_id = space_proto_getter('dlc_id')
        space_name = space_proto_getter('name')
        dlc_manager = genv.dlc_manager
        if dlc_manager.CheckDlcExists(dlc_id):
            self.join_func(room_id, pwd, with_team)
        else:
            def OnConfirm(value):
                if value:
                    dlc_manager = genv.dlc_manager
                    dlc_manager.StartDownload(dlc_id, True)
                    self.join_func(room_id, pwd, with_team)
            total_size = dlc_manager.GetDlcTotalSize(dlc_id)
            context = lang.ROOM_DLC_MAP_DOWNLOAD_TIPS % (space_name, Size2String(total_size))
            gui.Notice(lang.ROOM_DOWNLOAD_TITLE, context, callback=OnConfirm)

    @ListenPcKey(DesktopInput.KEY_F5)
    def OnClickRefresh(self, is_down=True):
        if not is_down:
            return

        self.OnClickRoomNode(None)
        self.RefreshRoomList()
        self.btn_refresh.SetEnabled(False)

        # self.panel_cd.visible = True
        self.click_refresh_time = time.time()
        # self.refresh_progress_node.SetPercent(100)
        # self.refresh_progress_node.SetPercentGradually(0, self.ROOM_REFRESH_CD, self.LoadingCDEndCallback)

    def LoadingCDEndCallback(self):
        self.panel_cd.visible = False
        self.btn_refresh.SetEnabled(True)

    @ListenPcKey(DesktopInput.KEY_Z)
    def OnClickQuickJoin(self, is_down=True):
        if not is_down:
            return

        now = time.time()
        if now - self.click_btn_quick_time < 3.0:
            gui.Prompt(458)
            return
        self.click_btn_quick_time = now
        filter_info = {}
        if self.match_type:
            filter_info['match_type'] = self.match_type
        if self.match_mode != 0:
            filter_info['match_mode'] = self.match_mode
        genv.avatar.FastJoinRoom(filter_info)

    @ListenPcKey(DesktopInput.KEY_F)
    def OnClickCreate(self, btn):
        RoomSettingWindow.instance().Show()

    @ListenPcKey(DesktopInput.KEY_R)
    def onClickObserve(self, _):
        if not self.select_room_node:
            return

        room_info = self.select_room_node.room_info
        need_pwd = bool(room_info.get('password_hash'))

        self.join_func = genv.avatar.JoinRoomWatcher

        def OnConfirm(value):
            if not value:
                return

            if need_pwd:
                self.ShowPasswordPanel()
            else:
                self.CheckRoomDlc(room_info["rid"], '', False)

        avatar = genv.avatar
        if len(avatar.team.member_dict) >= 2:
            gui.Notice('', lang.ROOM_LEAVE_TEAM_AND_JOIN_CONTENT, callback=OnConfirm)
        else:
            OnConfirm(True)

    def OnClickRoomNode(self, node):
        for n in self.room_nodelist.values():
            n.SetSelect(n == node)

        self.select_room_node = node
        self.selected_room_id = node.room_id if node else 0
        self.RefreshRoomInfoDetail()

    def RefreshRoomInfoDetail(self):
        if not self.select_room_node:
            self.panel_map_des.visible = False
            return

        self.panel_map_des.visible = True
        room_info = self.select_room_node.room_info
        spaceno = room_info.get('spaceno')
        space_proto_getter = space_data.data[spaceno].get

        self.txt_room_name.text = room_info.get('room_name')
        self.img_room.texture = space_proto_getter('map_icon')
        self.txt_map_name = space_proto_getter('name')
        self.txt_ob_num.text = '%d/%d' % (room_info['watcher_count'], room_info['max_watcher_count'])
        match_proto_getter = match_data.data[room_info.get('match_type')].get


        self.basic_info_map = {
            lang.CUSTOM_ROOM_OWNER: room_info.get('owner_show_name'),
            lang.CUSTOM_ROOM_ID: room_info.get('rid'),
            # lang.CUSTOM_ROOM_MAP: space_proto_getter('name'),
            lang.CUSTOM_ROOM_MATCH_TYPE: match_proto_getter('prefix_match_name')
            # lang.CUSTOM_ROOM_MATCH_MODE: lang.MATCH_MODE_HALL.get(room_info.get('match_mode', 1)),
        }

        self.listview_basic_info.total_item_num = len(self.basic_info_map)
        self.RefreshRoomAdvInfo(room_info)

    def RefreshRoomAdvInfo(self, room_info):
        match_type = room_info.get('match_type')
        proto = room_data.data.get(match_type)
        default = proto.get('default').get('1')
        room_options = room_info.get('room_options', {})
        self.adv_info_map = []
        dict = self.TextDict

        for k, v in proto.items():
            info = dict.get(k)
            if info:
                default_value = default.get(k, v[0])
                value = room_options.get(k, default_value)
                option_title = info.get('title', '')

                if k in room_config.RoomComboxOptionList:
                    option_value = self.TextDict[k].get(value, value)
                else:
                    option_value = lang.ROOM_OPTION_OPEN if value else lang.ROOM_OPTION_CLOSE
                self.adv_info_map.append((option_title, option_value, value == default_value))

        self.listview_adv_info.total_item_num = len(self.adv_info_map)

        self.RefreshSliderVisible()

    def RefreshSliderVisible(self):
        self.listview_adv_info.refreshView()
        self.panel_slider.visible = self.listview_adv_info.getContentSize().height < self.listview_adv_info.getInnerContainer().getContentSize().height

    @events.ListenTo(events.ON_SHOW_ROOM)
    def OnShowRoom(self, is_in_room):
        if is_in_room and self.visible:
            self.Close()
        elif not is_in_room and not self.visible:
            self.Show()






