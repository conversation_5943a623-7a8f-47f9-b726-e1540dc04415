# -*- coding: utf-8 -*-

from . import imgui_widgets
from . import cmd_avatar  # noqa
from . import base  # noqa
from . import imgui_bomb_window  # noqa
from . import camera  # noqa
from . import common_cmd  # noqa
from . import effect  # noqa
from . import game_play  # noqa
from . import imgui_block  # noqa
from . import imgui_cmd  # noqa
from . import imgui_common  # noqa
from . import imgui_render  # noqa
from . import imgui_weapon_smith  # noqa
from . import imgui_place_item  # noqa
from . import imgui_logcat  # noqa
from . import physics  # noqa
from . import plot  # noqa
from . import imgui_sound  # noqa
from . import space # noqa
from . import imgui_assist_aim # noqa
from . import performance  # noqa
from . import imgui_dynamic_mipmap # noqa
from . import model    # noqa
from . import rpc  # noqa
from . import imgui_data   # noqa
from . import item  # noqa
from . import ui_style  # noqa
from . import talent # noqa
from . import imgui_robot # noqa
from . import robot  # noqa
from . import imgui_shader_graph
from . import imgui_csbhit
from . import imgui_panoramic
from . import imgui_model_viewer
from . import imgui_level_designer
from . import imgui_gyroscope
from . import hall_team
from . import vehicle_detail_info
from . import vehicle_test
from . import imgui_raycast_where
from . import contract
from . import imgui_avatar_inspector
from . import imgui_ballistic_effect_param
from . import imgui_bug_report
from . import mail
from . import imgui_guide_arrow
from . import imgui_profile
from . import imgui_bake_icon
from . import imgui_shader_cache_miss
from . import imgui_ig_voice
from . import imgui_face
from . import imgui_face_bone
from . import imgui_face_color
from . import imgui_i18n_translation
from . import activity
from . import drawcall_collection
from . import particle_collection
from . import debug_notice
from . import cinematics
from . import imgui_decal
from . import hall_segment_prompt
from . import player_count
from . import hotspot
from . import imgui_place_water_trigger
from . import imgui_moving_target_robot
from . import hotspot_calculation
from . import imgui_effect_viewer
from . import occlus
from . import imgui_physics_blast
from . import imgui_vehicle_system   # noqa
from . import slow
from . import character
from . import export_us_anim_tool
from . import imgui_room_watch
from . import imgui_helen
from . import imgui_execute
from . import imgui_bug
from . import imgui_1p_effect
from . import imgui_record
from . import imgui_camera_ani
from . import imgui_docking_mark
from . import imgui_ui_viewer
from . import imgui_data_inpector
from . import ui_profile
from . import imgui_house_sound  # noqa
from . import test_robot  # noqa
from . import test_hotfix  # noqa
from . import start_coverage  # noqa
from . import imgui_teleport  # noqa
from . import selecton  # noqa
from . import imgui_spawn_point  # noqa
from . import imgui_hadrpoint  # noqa
from . import imgui_hitmark  # noqa
from . import robot_any  # noqa
from . import imgui_display_runtime_effects  # noqa
from . import imgui_doll  # noqa
from . import imgui_splitScreenTeammates  # noqa
from . import imgui_showroom_manager
from . import imgui_ads_render  # noqa
from . import magazine_position # noqa
from . import imgui_debug_trace  # noqa
from . import check_performance_util  # noqa
from . import imgui_script_entity  # noqa
from . import imgui_display_loaded_level  # noqa
from . import imgui_fx_tune  # noqa
from . import qa_ui_autotest   # noqa
from . import qatestcases  # noqa
from . import qa_autotest  # noqa
from . import gm_base_hyxd  # noqa
from . import imgui_mini_server_tracy  # noqa
from . import render_mobile # noqa
from . import imgui_HPsight_debug # noqa
from . import imgui_smoke_robot  # noqa
from . import guntest  # noqa

def init():
    try:
        import MImGui
        MImGui.SetCustomFont('Fonts/zh_normal.ttf', 'Chinese')
    except Exception as e:  # noqa
        pass


init()
