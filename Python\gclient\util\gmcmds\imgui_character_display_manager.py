# -*- coding: utf-8 -*-
import json
import time
from gclient.gamesystem.models.character_display_doll_model_manager import CharacterDisplayDollModelManager
from gclient.util.debug_log_util import print_s, SomePreset  # noqa
from .base import imgui   # noqa
import M<PERSON>
import pyimgui
from gclient.util.gmcmds.base import gmcmd, GMBaseWindow
from gclient.data import hero_data, hero_performance_data
import switches


class CharacterDisplayManagerWindow(GMBaseWindow):
    def __init__(self):
        super(CharacterDisplayManagerWindow, self).__init__()
        self.window_name = "角色展示管理面板"
        self.selected_hero_id = None
        self.refresh_interval = 1.0  # 刷新间隔（秒）
        self.last_refresh_time = 0
        self.auto_refresh = True
        self.show_detailed_info = False
        self.test_hero_id = 101  # 测试用的英雄ID

    @property
    def manager(self):
        return CharacterDisplayDollModelManager()

    def draw(self):
        # 设置窗口大小和位置
        self.set_window_size()
        self.set_window_position()
        
        # 开始窗口
        expanded, opened = imgui.begin(self.window_name, True)
        if not opened:
            self.close()
            return
            
        if not expanded:
            imgui.end()
            return

        # 自动刷新逻辑
        current_time = time.time()
        if self.auto_refresh and (current_time - self.last_refresh_time) > self.refresh_interval:
            self.last_refresh_time = current_time

        # 控制按钮区域
        self.draw_control_buttons()
        imgui.separator()

        # 基本状态信息
        self.draw_basic_status()
        imgui.separator()

        # 当前英雄信息
        self.draw_current_hero_info()
        imgui.separator()

        # 操作接口区域
        self.draw_operation_interface()
        imgui.separator()

        # 详细信息（可折叠）
        if self.show_detailed_info:
            self.draw_detailed_info()

        imgui.end()

    def draw_control_buttons(self):
        imgui.text("控制操作")
        
        if imgui.button("手动刷新"):
            self.last_refresh_time = time.time()
        
        imgui.same_line()
        changed, self.auto_refresh = imgui.checkbox("自动刷新", self.auto_refresh)
        
        imgui.same_line()
        changed, self.show_detailed_info = imgui.checkbox("显示详细信息", self.show_detailed_info)

    def draw_basic_status(self):
        imgui.text("基本状态")
        
        manager = self.manager
        
        # 基本状态信息
        main_npc_status = "有效" if manager.main_npc and manager.IsMainNpcValid(manager.main_npc) else "无效"
        imgui.text(f"主要NPC状态: {main_npc_status}")
        
        current_hero_id = manager.current_hero_id if manager.current_hero_id else "无"
        imgui.text(f"当前英雄ID: {current_hero_id}")
        
        showroom_world_status = "有效" if manager.current_showroom_world and manager.current_showroom_world.IsValid() else "无效"
        imgui.text(f"ShowRoom世界状态: {showroom_world_status}")
        
        imgui.text(f"ShowRoom名称: {manager.current_showroom_name}")
        
        other_models_count = len(manager.current_main_npc_other_models)
        imgui.text(f"其他模型数量: {other_models_count}")
        
        all_dolls_count = sum(len(version_dict) for version_dict in manager.all_main_npc_dolls.values())
        imgui.text(f"所有人偶总数: {all_dolls_count}")

    def draw_current_hero_info(self):
        imgui.text("当前英雄信息")
        
        manager = self.manager
        if not manager.main_npc or not manager.current_hero_id:
            imgui.text("  当前无活跃英雄")
            return
        
        hero_id = manager.current_hero_id
        version_id = switches.CHARACTER_DISPLAY_CONFIG_VERSION
        
        # 英雄基本信息
        hero_info = hero_data.data.get(hero_id, {})
        hero_name = hero_info.get('name', f'英雄{hero_id}')
        imgui.text(f"英雄名称: {hero_name}")
        imgui.text(f"版本ID: {version_id}")
        
        # 性能数据信息
        performance_data = manager.GetCurrentHeroPerformanceData(hero_id, version_id)
        if performance_data:
            cine_file = performance_data.get('Cinematic_file', '无')
            camera_cine_file = performance_data.get('Camera_Cinematic_file', '无')
            imgui.text(f"剧情文件: {cine_file}")
            imgui.text(f"相机剧情文件: {camera_cine_file}")
        
        # 播放状态信息
        play_status = manager.play_status_info
        imgui.text(f"主NPC就绪: {play_status.is_main_npc_ready}")
        imgui.text(f"道具就绪: {play_status.prop_ready_count}/{play_status.all_prop_count}")
        imgui.text(f"剧情就绪: {play_status.is_cine_ready}")
        imgui.text(f"全部就绪: {play_status.IsAllReady()}")

    def draw_operation_interface(self):
        imgui.text("操作接口")
        
        # 英雄选择和切换
        imgui.text("英雄操作:")
        
        # 英雄ID输入
        imgui.set_next_item_width(100)
        changed, self.test_hero_id = imgui.input_int("测试英雄ID", self.test_hero_id)
        
        imgui.same_line()
        if imgui.button("刷新选中英雄"):
            self.refresh_selected_hero()
        
        imgui.same_line()
        if imgui.button("进入管理器"):
            self.enter_manager()
        
        imgui.same_line()
        if imgui.button("离开管理器"):
            self.leave_manager()
        
        # 模型操作
        imgui.text("模型操作:")
        if imgui.button("验证所有主NPC"):
            self.validate_all_main_npc()
        
        imgui.same_line()
        if imgui.button("初始化主NPC"):
            self.init_main_npc()
        
        imgui.same_line()
        if imgui.button("初始化剧情控制"):
            self.init_cine_control()
        
        # ShowRoom操作
        imgui.text("ShowRoom操作:")
        if imgui.button("进入ShowRoom世界"):
            self.enter_showroom_world()
        
        imgui.same_line()
        if imgui.button("预热所有角色模型"):
            self.warmup_all_characters()

    def draw_detailed_info(self):
        imgui.separator()
        imgui.text("详细信息")
        
        manager = self.manager
        
        # 所有人偶信息
        if imgui.collapsing_header("所有人偶信息"):
            for hero_id, version_dict in manager.all_main_npc_dolls.items():
                hero_name = hero_data.data.get(hero_id, {}).get('name', f'英雄{hero_id}')
                if imgui.tree_node(f"{hero_name} (ID: {hero_id})"):
                    for version_id, doll in version_dict.items():
                        doll_status = "有效" if manager.IsMainNpcValid(doll) else "无效"
                        imgui.text(f"  版本{version_id}: {doll_status}")
                        if doll and hasattr(doll, 'model') and doll.model:
                            model_name = getattr(doll.model.model, 'Name', '未知') if doll.model.model else '无模型'
                            imgui.text(f"    模型名称: {model_name}")
                    imgui.tree_pop()
        
        # 其他模型信息
        if imgui.collapsing_header("其他模型信息"):
            for i, prop in enumerate(manager.current_main_npc_other_models):
                if imgui.tree_node(f"道具模型 {i+1}"):
                    if hasattr(prop, 'model') and prop.model:
                        model_id = getattr(prop.model, 'model_id', '未知')
                        imgui.text(f"  模型ID: {model_id}")
                        if hasattr(prop.model, 'model') and prop.model.model:
                            model_name = getattr(prop.model.model, 'Name', '未知')
                            imgui.text(f"  模型名称: {model_name}")
                            is_valid = prop.model.model.IsValid() if prop.model.model else False
                            imgui.text(f"  是否有效: {is_valid}")
                    imgui.tree_pop()
        
        # 隐藏原因
        if imgui.collapsing_header("隐藏原因"):
            for i, reason in enumerate(manager.hidden_reasons):
                imgui.text(f"  {i+1}. {reason}")

    def refresh_selected_hero(self):
        """刷新选中的英雄"""
        try:
            self.manager.RefreshSelectedHero(self.test_hero_id)
            print_s(f"成功刷新英雄: {self.test_hero_id}", SomePreset.white_fg_green_bg)
        except Exception as e:
            print_s(f"刷新英雄失败: {self.test_hero_id}, 错误: {e}", SomePreset.white_fg_red_bg)

    def enter_manager(self):
        """进入管理器"""
        try:
            self.manager.OnEnter(self.test_hero_id)
            print_s(f"成功进入角色展示管理器，英雄ID: {self.test_hero_id}", SomePreset.white_fg_green_bg)
        except Exception as e:
            print_s(f"进入管理器失败: {e}", SomePreset.white_fg_red_bg)

    def leave_manager(self):
        """离开管理器"""
        try:
            self.manager.OnLeave()
            print_s("成功离开角色展示管理器", SomePreset.white_fg_green_bg)
        except Exception as e:
            print_s(f"离开管理器失败: {e}", SomePreset.white_fg_red_bg)

    def validate_all_main_npc(self):
        """验证所有主NPC"""
        try:
            self.manager.ValidateALLMainNpc()
            print_s("成功验证所有主NPC", SomePreset.white_fg_green_bg)
        except Exception as e:
            print_s(f"验证所有主NPC失败: {e}", SomePreset.white_fg_red_bg)

    def init_main_npc(self):
        """初始化主NPC"""
        try:
            self.manager.InitMainNpc(self.test_hero_id)
            print_s(f"成功初始化主NPC，英雄ID: {self.test_hero_id}", SomePreset.white_fg_green_bg)
        except Exception as e:
            print_s(f"初始化主NPC失败: {e}", SomePreset.white_fg_red_bg)

    def init_cine_control(self):
        """初始化剧情控制"""
        try:
            self.manager.InitCineControl()
            print_s("成功初始化剧情控制", SomePreset.white_fg_green_bg)
        except Exception as e:
            print_s(f"初始化剧情控制失败: {e}", SomePreset.white_fg_red_bg)

    def enter_showroom_world(self):
        """进入ShowRoom世界"""
        try:
            # 这里需要一个有效的world对象，通常从ShowRoomManager获取
            from gclient.util.showroom_util import ShowRoomManager
            showroom_name = self.manager.current_showroom_name
            world = ShowRoomManager().GetShowRoom(showroom_name)
            if world:
                self.manager.OnCharacterDisplayEnterShowroomWorld(showroom_name, world)
                print_s(f"成功进入ShowRoom世界: {showroom_name}", SomePreset.white_fg_green_bg)
            else:
                print_s(f"ShowRoom世界不存在: {showroom_name}", SomePreset.white_fg_red_bg)
        except Exception as e:
            print_s(f"进入ShowRoom世界失败: {e}", SomePreset.white_fg_red_bg)

    def warmup_all_characters(self):
        """预热所有角色模型"""
        try:
            world = self.manager.current_showroom_world
            if world:
                self.manager.WarmUpAllMainNpcFromHeroPerformanceData(world)
                print_s("成功预热所有角色模型", SomePreset.white_fg_green_bg)
            else:
                print_s("当前ShowRoom世界无效，无法预热", SomePreset.white_fg_red_bg)
        except Exception as e:
            print_s(f"预热所有角色模型失败: {e}", SomePreset.white_fg_red_bg)

    def set_window_size(self):
        pyimgui.set_next_window_size(MUI.GetScreenWidth() / 2.5, MUI.GetScreenHeight() / 1.5,
                                     condition=pyimgui.FIRST_USE_EVER)

    def set_window_position(self):
        pyimgui.set_next_window_position(MUI.GetScreenWidth() / 4.0, MUI.GetScreenHeight() / 8.0,
                                         condition=pyimgui.FIRST_USE_EVER)

    def close(self):
        super(CharacterDisplayManagerWindow, self).close()


@gmcmd("imgui_character_display_manager")
def toggle_imgui_character_display_manager():
    """切换角色展示管理面板的显示状态"""
    if CharacterDisplayManagerWindow.isInited():
        CharacterDisplayManagerWindow.instance().close()
        CharacterDisplayManagerWindow.Destroy()
    else:
        CharacterDisplayManagerWindow.instance().open()
