# -*- coding: utf-8 -*-

# pyimgui 文档： https://pyimgui.readthedocs.io/en/latest/reference/imgui.core.html
from functools import partial
import MConfig

from .base import *
import MImGui
import pyimgui as imgui
import Timer
import MEngine
import MPlatform

from gclient import cconst
from gclient.util import debugger
from gclient.data import hero_attributes_data
from gshare.utils import Functor
# _reload_all = True

SHOW_DEMO = False
class CmdOptionWindowData(object):
    DrawHandler = None
    IP = MPlatform.GetIpInfo().split(',')[0]


def show_test_window():
    global SHOW_DEMO
    SHOW_DEMO = not SHOW_DEMO

def run_command(cmd):
    if isinstance(cmd, list):
        for c in cmd:
            RunCommand(c)
    else:
        RunCommand(cmd)

renderpanelconfig = {
    'label': 'Render',
    'children':[
        {
            'label': 'Render Option',
            'onClick': Functor(run_command, 'render_options'),
        },
        {
            'label': 'Texture Statistics',
            'onClick': Functor(run_command, 'dynamic_mipmap'),
        },
    ],
}
MENU_TERR = {
    'label': 'Spike',
    'children': [
        {
            'label': '万物入口控制台',
            'children':[
                {
                    'label': '常用',
                    'onClick': Functor(run_command, 'imgui_common'),
                },
                {
                    'label': 'Logcat',
                    'onClick': Functor(run_command, 'imgui_logcat'),
                },
                {
                    'label': 'imgui sample',
                    'onClick': show_test_window,
                },
                {
                    'label': 'Profile',
                    'onClick': Functor(run_command, 'imgui_profile'),
                },
                {
                    'label': '冒烟机器人相关',
                    'onClick': Functor(run_command, 'imgui_smoke_robot'),
                },
                {
                    'label': 'Entity列表',
                    'onClick': Functor(run_command, 'imgui_hierarchy'),
                },
                {
                    'label': 'UI树(Python)',
                    'onClick': Functor(run_command, 'imgui_pyuitree'),
                },
                {
                    'label': 'UI树(Cocos)',
                    'onClick': Functor(run_command, 'imgui_cocosuitree'),
                },
{
                    'label': '属性窗口',
                    'onClick': Functor(run_command, 'imgui_property'),
                },
{
                    'label': '音效调试工具',
                    'onClick': Functor(run_command, 'imgui_sound'),
                },
{
                    'label': '游戏设置',
                    'onClick': Functor(run_command, 'imgui_game_setting'),
                },
                {
                    'label': '相机位置记录',
                    'onClick': Functor(run_command, 'imgui_scene_debug'),
                },
                # {
                #     'label': '组件窗口',
                #     'onClick': Functor(run_command, 'imgui_adjust_window'),
                # },
                {
                    'label': 'buff面板',
                    'onClick': Functor(run_command, 'imgui_buff'),
                },
                {
                    'label': '查看最近播放的特效/动作/Graph',
                    'onClick': Functor(run_command, 'imgui_display_runtime_effects'),
                },
                {
                    'label': '开火位置信息',
                    'onClick': Functor(run_command, 'imgui_HPsight_debug'),
                },
            ]
        },

        {
            'label': '玩法/系统',
            'children':[
                {
                    'label': '外服BUG',
                    'onClick': Functor(run_command, 'imgui_bug'),
                },
                {
                    'label': '渲染画面调试',
                    'onClick': Functor(run_command, 'render_debug'),
                },
                {
                    'label': '角色属性',
                    'onClick': Functor(run_command, 'imgui_avatar_inspector'),
                },
                # {
                #     'label': 'Data Sheet',
                #     'onClick': Functor(run_command, 'data_window'),
                # },
                {
                    'label': '物理破碎',
                    'onClick': Functor(run_command, 'imgui_physics_blast'),
                },
                {
                    'label': '车辆系统',
                    'onClick': Functor(run_command, 'imgui_vehicle_system'),
                },
                {
                    'label': '枪械',
                    'onClick': Functor(run_command, 'weapon_smith'),
                },
                {
                    'label': '模型挂接关系面板',
                    'onClick': Functor(run_command, 'imgui_tach_window'),
                },
                {
                    'label': 'Place System',
                    'onClick': Functor(run_command, 'imgui_place_item'),
                },
                {
                    'label': '场景BUG',
                    'onClick': Functor(run_command, 'imgui_panoramic'),
                },
                {
                    'label': '模型预览',
                    'onClick': Functor(run_command, 'imgui_model_viewer'),
                },
                {
                    'label': '特效预览',
                    'onClick': Functor(run_command, 'imgui_effect_viewer'),
                },
                {
                    'label': 'UI预览',
                    'onClick': Functor(run_command, 'imgui_ui_viewer'),
                },
                {
                    'label': 'i18n多语言',
                    'onClick': Functor(run_command, 'imgui_i18n_translation'),
                },
                {
                    'label': '实时改表(改data)',
                    'onClick': Functor(run_command, 'imgui_data_inpector'),
                },
                {
                    'label': '关卡白模',
                    'onClick': Functor(run_command, 'imgui_level_designer'),
                },
                {
                    'label': '辅助瞄准',
                    'onClick': Functor(run_command, 'aim_window'),
                },
                {
                    'label': '机器人相关',
                    'onClick': Functor(run_command, 'robot_window'),
                },
                {
                    'label': 'ShaderGraph',
                    'onClick': Functor(run_command, 'shader_graph_window'),
                },
                {
                    'label': '鼠标击中的ui',
                    'onClick': Functor(run_command, 'imgui_csbhit'),
                },
                {
                    'label': '陀螺仪',
                    'onClick': Functor(run_command, 'gyroscope_window'),
                },
                {
                    'label': '射线击中哪里？',
                    'onClick': Functor(run_command, 'raycast_where'),
                },
                {
                    'label': '弹道特效',
                    'onClick': Functor(run_command, 'ballistic_effect_window'),
                },
                {
                    'label': '手雷特效抛物线',
                    'onClick': Functor(run_command, 'bomb_window'),
                },
                {
                    'label': '放置物',
                    'onClick': Functor(run_command, 'block_window'),
                },
                {
                    'label': '受击指示箭头',
                    'onClick': Functor(run_command, 'guide_arrow_window'),
                },
                # {
                #     'label': '捏脸贴图',
                #     'onClick': Functor(run_command, 'face_window'),
                # },
                # {
                #     'label': '捏脸骨骼',
                #     'onClick': Functor(run_command, 'face_bone_window'),
                # },
                # {
                #     'label': '捏脸颜色',
                #     'onClick': Functor(run_command, 'face_color_window'),
                # },
                {
                    'label': '模型贴花',
                    'onClick': Functor(run_command, 'decal_window'),
                },
                {
                    'label': 'ShaderCacheMiss',
                    'onClick': Functor(run_command, 'shader_cache_miss_window'),
                },
                {
                    'label': '水域触发器',
                    'onClick': Functor(run_command, 'place_water_trigger'),
                },
                {
                    'label': '迷彩渲染配置',
                    'onClick': Functor(run_command, 'gun_skin_shader_graph_window'),
                },
                {
                    'label': '1p特效预览',
                    'onClick': Functor(run_command, 'imgui_1p_effect_viewer'),
                },
                {
                    'label': 'Hitmark调试工具',
                    'onClick': Functor(run_command, 'hitmark_window'),
                },
                {
                    'label': '3C调试面板',
                    'onClick': Functor(run_command, 'imgui_character_window'),
                },
                {
                    'label': 'dolls 相关面板',
                    'onClick': Functor(run_command, 'imgui_doll'),
                },
                {
                    'label': '入场组队展示调试与测试面板',
                    'onClick': Functor(run_command, 'imgui_splitScreenTeammates'),
                },
                {
                    'label': '追踪弹匣位置',
                    'onClick': Functor(run_command, 'mag_position'),
                },
                {
                    'label': '查看脚本创建的Entity',
                    'onClick': partial(run_command, 'imgui_script_entity'),
                },
                {
                    'label': '当前加载的Level列表',
                    'onClick': partial(run_command, 'imgui_display_loaded_level'),
                },
                {
                    'label': 'Ads Render Option',
                    'onClick': Functor(run_command, 'imgui_ads_render')
                },
                {
                    'label': '动画验收工具',
                    'onClick': Functor(run_command, 'imgui_anim_check')
                },
                {
                    'label': 'Cue监控',
                    'onClick': Functor(run_command, 'imgui_cue_window')
                },
            ],
        },
        {
            'label': 'AI/机器人',
            'children': [
                {
                    'label': 'HelenAI--server',
                    'onClick': Functor(run_command, 'imgui_helen_ai'),
                },
                {
                    'label': '仿真人调试',
                    'onClick': Functor(run_command, 'imgui_helen_ai_client'),
                },
                # {
                #     'label': 'HelenAI(外网)',
                #     'onClick': Functor(run_command, 'imgui_helen_ai_simple'),
                # },
                {
                    'label': '音效测试机器人',
                    'onClick': Functor(run_command, 'imgui_debug_robot'),
                },
                {
                    'label': '寻路面板',
                    'onClick': Functor(run_command, 'imgui_nav'),
                },
                {
                    'label': '3P测试机器人',
                    'onClick': Functor(run_command, 'imgui_3p_robot'),
                },
                {
                    'label': '3c动作测试机器人',
                    'onClick': Functor(run_command, 'imgui_3c_test_robot'),
                },

            ]
        },
        # {
        #   'label': 'Render',
        #   'children':[
        #     {
        #       'label': 'Render Option',
        #       'onClick': Functor(run_command, 'render_options'),
        #     },
        #     {
        #       'label': 'Texture Statistics',
        #       'onClick': Functor(run_command, 'dynamic_mipmap'),
        #     }
        #   ],
        # },
        # {
        #   'label': 'Help',
        #   'children':[
        #     {
        #       'label': 'imgui sample',
        #       'onClick': show_test_window,
        #     },
        #   ],
        # },
    ]
}

if MConfig.IsFinal:
    MENU_TERR['children'].append(renderpanelconfig)

def build_tree_item(children):

    for child in children:
        if 'children' in child:
            if imgui.begin_menu(child['label'], True):
                build_tree_item(child['children'])
                imgui.end_menu()
        else:
            clicked, state = imgui.menu_item(child['label'], None, False, True)  # menu_item(str label, str shortcut=None, bool selected=False, enabled=True)
            if clicked:
                child['onClick']()

def draw_imgui_cmd():
    io = imgui.get_io()
    # 非冒烟的情况才会隐藏 冒烟不隐藏
    if not genv.smoke_robot and not genv.is_cursor_visible:
        return
    if gui.is_mobile:
        io.font_global_scale = 2

    if imgui.begin_main_menu_bar():
        # first menu dropdown
        # if imgui.begin_menu('File', True):
        #     imgui.menu_item('New', 'Ctrl+N', False, True)
        #     imgui.menu_item('Open ...', 'Ctrl+O', False, True)
        #
        #     # submenu
        #     if imgui.begin_menu('Open Recent', True):
        #         imgui.menu_item('doc.txt', None, False, True)
        #         imgui.end_menu()
        #
        #     imgui.end_menu()

        build_tree_item(MENU_TERR['children'])
        if not MConfig.IsFinal and gui.is_pc:
            from .ImDebugger.IMTool import IMTool
            IMTool.instance().draw_imdebugger()

        if genv and genv.camera:
            imgui.begin_menu('Camera：%s' % genv.camera.position, False)

        imgui.begin_menu('IP: (%s)' % CmdOptionWindowData.IP, False)

        imgui.end_main_menu_bar()

        if SHOW_DEMO:
            # https://github.com/ocornut/imgui/blob/master/imgui_demo.cpp
            imgui.show_test_window()

def _toggle_imgui_cmd():
    from . import ImDebugger
    if CmdOptionWindowData.DrawHandler:
        if not MConfig.IsFinal:
            ImDebugger.CloseDebugger()
        MImGui.DelFromDraw(CmdOptionWindowData.DrawHandler)
        CmdOptionWindowData.DrawHandler = None
    else:
        CmdOptionWindowData.DrawHandler = MImGui.AddToDraw(draw_imgui_cmd)
        MImGui.StartNetImGuiListening(8890)
        if not MConfig.IsFinal:
            ImDebugger.OpenDebugger()

@gmcmd("imgui_cmd")
def toggle_imgui_cmd():
    _toggle_imgui_cmd()
    # if hasattr(MEngine, 'EnableIME'):
    #     MEngine.EnableIME(True)
