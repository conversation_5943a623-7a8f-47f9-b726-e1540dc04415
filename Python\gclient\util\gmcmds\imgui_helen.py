# -*- coding: utf-8 -*-

from .base import gmcmd
_reload_all = True


@gmcmd("imgui_hierarchy")
def toggle_imgui_hierarchy_cmd():
    from HelenUtils.debug_imgui.ImHierarchy import ImHierarchy
    if ImHierarchy.instance().opened:
        ImHierarchy.instance().close()
        ImHierarchy.Destroy()
    else:
        ImHierarchy.instance().open()


@gmcmd("imgui_pyuitree")
def toggle_imgui_pyuitree_cmd():
    from HelenUtils.debug_imgui.ImPyUITree import ImPyUITree
    if ImPyUITree.instance().opened:
        ImPyUITree.instance().close()
        ImPyUITree.Destroy()
    else:
        ImPyUITree.instance().open()


@gmcmd("imgui_cocosuitree")
def toggle_imgui_cocosuitree_cmd():
    from HelenUtils.debug_imgui.ImCocosUITree import ImCocosUITree
    if ImCocosUITree.instance().opened:
        ImCocosUITree.instance().close()
        ImCocosUITree.Destroy()
    else:
        ImCocosUITree.instance().open()


@gmcmd("imgui_property")
def toggle_imgui_property_cmd():
    from HelenUtils.debug_imgui.BaseImGui import ImPropertyWindow
    if ImPropertyWindow.instance().opened:
        ImPropertyWindow.instance().close()
    else:
        ImPropertyWindow.instance().open()


@gmcmd("imgui_helen_ai")
def toggle_imgui_helen_ai_cmd():
    from HelenUtils.debug_imgui.ImHelenAI import HelenAIDebugWindow
    if HelenAIDebugWindow.instance().opened:
        HelenAIDebugWindow.instance().close()
    else:
        HelenAIDebugWindow.instance().open()


@gmcmd("imgui_helen_ai_client")
def toggle_imgui_helen_ai_client_cmd():
    from HelenUtils.debug_imgui.ImHelenAI import ClientHelenAIDebugWindow
    if ClientHelenAIDebugWindow.instance().opened:
        ClientHelenAIDebugWindow.instance().close()
    else:
        ClientHelenAIDebugWindow.instance().open()


@gmcmd("imgui_debug_robot")
def toggle_imgui_debug_robot_cmd():
    from HelenUtils.debug_imgui.ImHelenRobot import DebugRobotWindow
    if DebugRobotWindow.instance().opened:
        DebugRobotWindow.instance().close()
    else:
        DebugRobotWindow.instance().open()


@gmcmd("imgui_helen_ai_simple")
def toggle_imgui_helen_ai_simple_cmd():
    from HelenUtils.debug_imgui.ImHelenAI import HelenAISimpleDebugWindow
    if HelenAISimpleDebugWindow.instance().opened:
        HelenAISimpleDebugWindow.instance().close()
    else:
        HelenAISimpleDebugWindow.instance().open()


@gmcmd("imgui_nav")
def toggle_imgui_hierarchy_cmd():
    from HelenUtils.debug_imgui.ImNav import ImNavWindow
    if ImNavWindow.instance().opened:
        ImNavWindow.instance().close()
        ImNavWindow.Destroy()
    else:
        ImNavWindow.instance().open()


@gmcmd("imgui_sound")
def toggle_imgui_sound_cmd():
    from HelenUtils.debug_imgui.ImSound import ImSoundWindow
    if ImSoundWindow.instance().opened:
        ImSoundWindow.instance().close()
        ImSoundWindow.Destroy()
    else:
        ImSoundWindow.instance().open()


@gmcmd("imgui_buff")
def toggle_imgui_buff_cmd():
    from HelenUtils.debug_imgui.ImSound import ImBuffWindow
    if ImBuffWindow.instance().opened:
        ImBuffWindow.instance().close()
        ImBuffWindow.Destroy()
    else:
        ImBuffWindow.instance().open()


@gmcmd("imgui_graph_sync_debug")
def toggle_imgui_graph_sync_debug_cmd():
    from HelenUtils.debug_imgui.ImSound import ImGraphSyncDebugWindow
    if ImGraphSyncDebugWindow.instance().opened:
        ImGraphSyncDebugWindow.instance().close()
    else:
        ImGraphSyncDebugWindow.instance().open()


@gmcmd("imgui_bound_box_generate_check")
def toggle_imgui_bound_box_generate_check_cmd():
    from HelenUtils.debug_imgui.ImModel import ImBoundBoxGenerateCheckWindow
    if ImBoundBoxGenerateCheckWindow.instance().opened:
        ImBoundBoxGenerateCheckWindow.instance().close()
    else:
        ImBoundBoxGenerateCheckWindow.instance().open()

@gmcmd("imgui_racer")
def toggle_imgui_racer_cmd():
    from HelenUtils.debug_imgui.ImSound import ImRacerWindow
    if ImRacerWindow.instance().opened:
        ImRacerWindow.instance().close()
        ImRacerWindow.Destroy()
    else:
        ImRacerWindow.instance().open()


@gmcmd("imgui_game_setting")
def toggle_imgui_game_setting_cmd():
    from HelenUtils.debug_imgui.ImGameSetting import ImGameSetting
    if ImGameSetting.instance().opened:
        ImGameSetting.instance().close()
    else:
        ImGameSetting.instance().open()


@gmcmd("imgui_scene_debug")
def toggle_imgui_scene_debug():
    from HelenUtils.debug_imgui.ImSceneDebug import ImSceneDebug
    if ImSceneDebug.instance().opened:
        ImSceneDebug.instance().close()
    else:
        ImSceneDebug.instance().open()


@gmcmd("imgui_adjust_window")
def toggle_imgui_adjust_window_debug():
    pass
    # from HelenUtils.debug_imgui.ugc_adjust_window import ImAdjustWindowGM
    # if ImAdjustWindowGM.instance().opened:
    #     ImAdjustWindowGM.instance().close()
    # else:
    #     ImAdjustWindowGM.instance().open()


@gmcmd("imgui_3p_robot")
def toggle_imgui_3p_robot_cmd():
    from HelenUtils.debug_imgui.Im3PRobot import Im3PRobot
    if Im3PRobot.instance().opened:
        Im3PRobot.instance().close()
    else:
        Im3PRobot.instance().open()


@gmcmd("imgui_anim_check")
def toggle_imgui_anim_check():
    from HelenUtils.debug_imgui.ImAnimCheckTool import ImAnimCheckTool
    if ImAnimCheckTool.instance().opened:
        ImAnimCheckTool.instance().close()
        ImAnimCheckTool.Destroy()
    else:
        ImAnimCheckTool.instance().open()


@gmcmd("imgui_3c_test_robot")
def toggle_imgui_3c_robot_cmd():
    from HelenUtils.debug_imgui.Im3CTestRobot import Im3CTestRobot
    if Im3CTestRobot.instance().opened:
        Im3CTestRobot.instance().close()
    else:
        Im3CTestRobot.instance().open()