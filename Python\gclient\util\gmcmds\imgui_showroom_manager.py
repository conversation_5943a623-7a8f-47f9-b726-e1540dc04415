# -*- coding: utf-8 -*-
import json
import time
from gclient.util.showroom_util import Show<PERSON>oomManager
from gclient.util.debug_log_util import print_s, SomePreset  # noqa
from .base import imgui   # noqa
import MUI
import pyimgui
from gclient.util.gmcmds.base import gmcmd, GMBaseWindow


class ShowRoomManagerWindow(GMBaseWindow):
    def __init__(self):
        super(ShowRoomManagerWindow, self).__init__()
        self.window_name = "ShowRoom管理面板"
        self.selected_showroom = None
        self.refresh_interval = 1.0  # 刷新间隔（秒）
        self.last_refresh_time = 0
        self.auto_refresh = True

    @property
    def showroom_manager(self):
        return ShowRoomManager()

    def draw(self):
        # 设置窗口大小和位置
        self.set_window_size()
        self.set_window_position()
        
        # 开始窗口
        expanded, opened = imgui.begin(self.window_name, True)
        if not opened:
            self.close()
            return
            
        if not expanded:
            imgui.end()
            return

        # 自动刷新逻辑
        current_time = time.time()
        if self.auto_refresh and (current_time - self.last_refresh_time) > self.refresh_interval:
            self.last_refresh_time = current_time

        # 控制按钮区域
        self.draw_control_buttons()
        imgui.separator()

        # 统计信息区域
        self.draw_statistics()
        imgui.separator()

        # 已创建的ShowRoom列表
        self.draw_created_showrooms()
        imgui.separator()

        # 正在创建的ShowRoom列表
        self.draw_creating_showrooms()
        imgui.separator()

        # 延迟删除的ShowRoom列表
        self.draw_delay_delete_showrooms()

        imgui.end()

    def draw_control_buttons(self):
        imgui.text("控制操作")
        
        if imgui.button("手动刷新"):
            self.last_refresh_time = time.time()
        
        imgui.same_line()
        changed, self.auto_refresh = imgui.checkbox("自动刷新", self.auto_refresh)
        
        imgui.same_line()
        if imgui.button("清理所有ShowRoom"):
            self.clear_all_showrooms()
        
        imgui.same_line()
        if imgui.button("强制清理创建中"):
            self.force_clear_creating()

    def draw_statistics(self):
        imgui.text("统计信息")
        
        manager = self.showroom_manager
        created_count = len(manager.showroom_dict)
        creating_count = len(manager.showroom_in_creating)
        delay_delete_count = len(manager.delay_del_showroom_list)
        timeout_timer_count = len(manager.showroom_creating_timeout_timer)
        
        imgui.text(f"已创建ShowRoom数量: {created_count}")
        imgui.text(f"正在创建ShowRoom数量: {creating_count}")
        imgui.text(f"延迟删除ShowRoom数量: {delay_delete_count}")
        imgui.text(f"创建超时定时器数量: {timeout_timer_count}")

    def draw_created_showrooms(self):
        imgui.text("已创建的ShowRoom")
        
        manager = self.showroom_manager
        if not manager.showroom_dict:
            imgui.text("  无已创建的ShowRoom")
            return
            
        imgui.columns(3, "created_showrooms")
        imgui.text("ShowRoom名称")
        imgui.next_column()
        imgui.text("World对象")
        imgui.next_column()
        imgui.text("操作")
        imgui.next_column()
        imgui.separator()
        
        for room_name, world in manager.showroom_dict.items():
            # ShowRoom名称
            if imgui.selectable(room_name, self.selected_showroom == room_name)[0]:
                self.selected_showroom = room_name
            imgui.next_column()
            
            # World对象信息
            world_info = str(world) if world else "None"
            if len(world_info) > 30:
                world_info = world_info[:30] + "..."
            imgui.text(world_info)
            imgui.next_column()
            
            # 操作按钮
            if imgui.button(f"删除##{room_name}"):
                self.delete_showroom(room_name)
            imgui.next_column()
        
        imgui.columns(1)

    def draw_creating_showrooms(self):
        imgui.text("正在创建的ShowRoom")
        
        manager = self.showroom_manager
        if not manager.showroom_in_creating:
            imgui.text("  无正在创建的ShowRoom")
            return
            
        imgui.columns(3, "creating_showrooms")
        imgui.text("ShowRoom名称")
        imgui.next_column()
        imgui.text("状态")
        imgui.next_column()
        imgui.text("操作")
        imgui.next_column()
        imgui.separator()
        
        for room_name, status in manager.showroom_in_creating.items():
            # ShowRoom名称
            imgui.text(room_name)
            imgui.next_column()
            
            # 状态信息
            imgui.text(f"创建中 (状态: {status})")
            imgui.next_column()
            
            # 操作按钮
            if imgui.button(f"取消创建##{room_name}"):
                self.cancel_creating_showroom(room_name)
            imgui.next_column()
        
        imgui.columns(1)

    def draw_delay_delete_showrooms(self):
        imgui.text("延迟删除的ShowRoom")
        
        manager = self.showroom_manager
        if not manager.delay_del_showroom_list:
            imgui.text("  无延迟删除的ShowRoom")
            return
            
        imgui.columns(3, "delay_delete_showrooms")
        imgui.text("ShowRoom名称")
        imgui.next_column()
        imgui.text("定时器ID")
        imgui.next_column()
        imgui.text("操作")
        imgui.next_column()
        imgui.separator()
        
        for room_name, timer_id in manager.delay_del_showroom_list.items():
            # ShowRoom名称
            imgui.text(room_name)
            imgui.next_column()
            
            # 定时器ID
            imgui.text(str(timer_id))
            imgui.next_column()
            
            # 操作按钮
            if imgui.button(f"立即删除##{room_name}"):
                self.immediate_delete_showroom(room_name)
            imgui.same_line()
            if imgui.button(f"取消删除##{room_name}"):
                self.cancel_delete_showroom(room_name)
            imgui.next_column()
        
        imgui.columns(1)

    def delete_showroom(self, room_name):
        """删除指定的ShowRoom"""
        try:
            self.showroom_manager.DelShowRoom(room_name)
            print_s(f"成功删除ShowRoom: {room_name}", SomePreset.white_fg_green_bg)
        except Exception as e:
            print_s(f"删除ShowRoom失败: {room_name}, 错误: {e}", SomePreset.white_fg_red_bg)

    def clear_all_showrooms(self):
        """清理所有ShowRoom"""
        try:
            manager = self.showroom_manager
            room_names = list(manager.showroom_dict.keys())
            for room_name in room_names:
                manager.DelShowRoom(room_name)
            print_s(f"成功清理所有ShowRoom，共清理 {len(room_names)} 个", SomePreset.white_fg_green_bg)
        except Exception as e:
            print_s(f"清理所有ShowRoom失败: {e}", SomePreset.white_fg_red_bg)

    def force_clear_creating(self):
        """强制清理正在创建中的ShowRoom"""
        try:
            manager = self.showroom_manager
            creating_names = list(manager.showroom_in_creating.keys())
            manager.showroom_in_creating.clear()
            
            # 清理超时定时器
            for timer in manager.showroom_creating_timeout_timer.values():
                if timer:
                    timer.cancel()
            manager.showroom_creating_timeout_timer.clear()
            
            print_s(f"强制清理正在创建的ShowRoom，共清理 {len(creating_names)} 个", SomePreset.white_fg_yellow_bg)
        except Exception as e:
            print_s(f"强制清理创建中ShowRoom失败: {e}", SomePreset.white_fg_red_bg)

    def cancel_creating_showroom(self, room_name):
        """取消创建指定的ShowRoom"""
        try:
            manager = self.showroom_manager
            if room_name in manager.showroom_in_creating:
                del manager.showroom_in_creating[room_name]
            
            if room_name in manager.showroom_creating_timeout_timer:
                timer = manager.showroom_creating_timeout_timer[room_name]
                if timer:
                    timer.cancel()
                del manager.showroom_creating_timeout_timer[room_name]
            
            print_s(f"取消创建ShowRoom: {room_name}", SomePreset.white_fg_yellow_bg)
        except Exception as e:
            print_s(f"取消创建ShowRoom失败: {room_name}, 错误: {e}", SomePreset.white_fg_red_bg)

    def immediate_delete_showroom(self, room_name):
        """立即删除延迟删除的ShowRoom"""
        try:
            self.showroom_manager.DelayDelShowRoomImpl(room_name)
            print_s(f"立即删除ShowRoom: {room_name}", SomePreset.white_fg_green_bg)
        except Exception as e:
            print_s(f"立即删除ShowRoom失败: {room_name}, 错误: {e}", SomePreset.white_fg_red_bg)

    def cancel_delete_showroom(self, room_name):
        """取消删除延迟删除的ShowRoom"""
        try:
            self.showroom_manager.CancelDelayDelShowRoom(room_name)
            print_s(f"取消删除ShowRoom: {room_name}", SomePreset.white_fg_yellow_bg)
        except Exception as e:
            print_s(f"取消删除ShowRoom失败: {room_name}, 错误: {e}", SomePreset.white_fg_red_bg)

    def set_window_size(self):
        pyimgui.set_next_window_size(MUI.GetScreenWidth() / 3.0, MUI.GetScreenHeight() / 2.0,
                                     condition=pyimgui.FIRST_USE_EVER)

    def set_window_position(self):
        pyimgui.set_next_window_position(MUI.GetScreenWidth() / 3.0, MUI.GetScreenHeight() / 6.0,
                                         condition=pyimgui.FIRST_USE_EVER)

    def close(self):
        super(ShowRoomManagerWindow, self).close()


@gmcmd("imgui_showroom_manager")
def toggle_imgui_showroom_manager():
    """切换ShowRoom管理面板的显示状态"""
    if ShowRoomManagerWindow.isInited():
        ShowRoomManagerWindow.instance().close()
        ShowRoomManagerWindow.Destroy()
    else:
        ShowRoomManagerWindow.instance().open()
