# -*- coding: utf-8 -*-
import json
import random
from gclient.framework.ui.ui_director import UIDirector
from gclient.gamesystem.models.split_screen_show_teammate_manager import SplitScreenShowTeammateManager
from gclient.util.debug_log_util import print_s, SomePreset  # noqa
from .base import imgui   # noqa
import MUI
import pyimgui
import MRender
from gclient.util.gmcmds.base import gmcmd, GMBaseWindow
import ccui
import cc
import MLive
from gclient.framework.ui import ui_define
from gclient.gamesystem.uihall.uicombatsys.hall_combat_window import HallCombatSysWindow
from gclient.util.cinematics.cinematics_mgr import AirplaneVideoCtrl

is_skeleton_editor = False
try:
    import SkeletonEditorEntry  # noqa
    is_skeleton_editor = True
except ImportError:
    # SkeletonEditorEntry模块不存在，不是骨骼编辑器，继续
    pass
if not is_skeleton_editor:
    # 这个在骨骼编辑器中不能用
    pass


class SplitScreenTeammatesTestUINode():
    def __init__(self):
        self.img_view = None
        # self.scene = cc.Director.getInstance().getRunningScene()
        self.parent_window = None
        self.index = 0

    def Init(self, parent_window, index):
        self.parent_window = parent_window
        self.index = index
        self.CreateImg()
    
    def CreateImg(self):
        if self.img_view:
            return

        self.img_view = ccui.ImageView.create()
        self.parent_window.addChild(self.img_view)
        self.img_view.setVisible(True)
        x = int(ui_define.ORIGINAL_DESIGN_WIDTH / 2)
        y = int(ui_define.ORIGINAL_DESIGN_HEIGHT / 1)
        self.img_view.setAnchorPoint(cc.Vec2(0, 0))
        self.img_view.setPosition(cc.Vec2(0, 0))
        self.img_view.setContentSize(cc.Size(x, y))
        self.img_view.setName("SplitScreenTeammatesTestUINode" + str(self.index))
        self.img_view.ignoreContentAdaptWithSize(False)
        self.ResetSizePosByIndex()
        self.img_view.EnableAA(True)
    
    def ResetSizePosByIndex(self):
        all_count = 4
        cur_index = self.index - 1
        x = int(ui_define.ORIGINAL_DESIGN_WIDTH / all_count) * cur_index
        y = 0
        size_x = int(ui_define.ORIGINAL_DESIGN_WIDTH / all_count)
        size_y = int(ui_define.ORIGINAL_DESIGN_HEIGHT)
        self.img_view.setPosition(cc.Vec2(x, y))
        self.img_view.resizeContentSize(cc.Size(size_x, size_y))
    
    def Destroy(self):
        if self.img_view and self.img_view.isvalid():
            self.parent_window.removeChild(self.img_view)
        self.img_view = None


class SplitScreenTeammatesWindow(GMBaseWindow):
    def __init__(self):
        super(SplitScreenTeammatesWindow, self).__init__()
        self.window_name = "入场组队展示调试与测试面板"

        self.teammate_1_ui_node = SplitScreenTeammatesTestUINode()
        self.teammate_2_ui_node = SplitScreenTeammatesTestUINode()
        self.teammate_3_ui_node = SplitScreenTeammatesTestUINode()
        self.teammate_4_ui_node = SplitScreenTeammatesTestUINode()
        self.all_hero_ids = ['101', '107', '116', '117', '118', '119', '120', '121', '122', '123', '124', '125', '126', '127', '128', '129']

        self.teammate_selected_hero_id = {
            0: None,
            1: None,
            2: None,
            3: None,
            4: None,
            5: None,
            6: None,
            7: None,
        }
        self.show_self_team = True
        self._videoImg = None

    
    @property
    def teammate_manager(self):
        return SplitScreenShowTeammateManager()
    
    def GetUINodeTestWindow(self):
        ui_ctrl = UIDirector.instance()
        return ui_ctrl.window_components[0]
        ins = HallCombatSysWindow.instance()
        return ins.root_widget
    
    def HaveUINodeTestWindow(self):
        ui_ctrl = UIDirector.instance()
        return len(ui_ctrl.window_components) > 0
    
    def CreateTestUINode(self):
        window_widget = self.GetUINodeTestWindow()
        self.teammate_1_ui_node.Init(window_widget, 1)
        self.teammate_2_ui_node.Init(window_widget, 2)
        self.teammate_3_ui_node.Init(window_widget, 3)
        self.teammate_4_ui_node.Init(window_widget, 4)
    
    def TestPreferCGVideo(self, path):
        if not self._videoImg:
            window_widget = self.GetUINodeTestWindow()
            img_view = ccui.ImageView.create()
            window_widget.addChild(img_view)
            img_view.setVisible(True)
            x = int(ui_define.ORIGINAL_DESIGN_WIDTH)
            y = int(ui_define.ORIGINAL_DESIGN_HEIGHT)
            img_view.setAnchorPoint(cc.Vec2(0, 0))
            img_view.setPosition(cc.Vec2(0, 0))
            img_view.setContentSize(cc.Size(x, y))
            img_view.setName("Test_Video_Img")
            img_view.ignoreContentAdaptWithSize(False)
            img_view.EnableAA(True)
            self._videoImg = img_view
            print_s(f"create video img: {self._videoImg}", SomePreset.white_fg_red_bg)

        self._videoImg.setLiveRoomName('Room_video')
        MLive.PreferLocalVideo(path)
        MLive.SetCCLiveNotifyCallback(self._onVideoNotify)

    def _onVideoNotify(self, msg):
        msg_json = json.loads(msg)
        type = msg_json.get('type', '')
        print_s(f"type: {type}", SomePreset.white_fg_red_bg)
        if type == 'onPlayProgress':
            pass
        elif type == 'onPlayCompleted':
            # print_s("视频播放结束", SomePreset.white_fg_red_bg)
            MLive.CloseCCLive()
            pass
        elif type == 'onPlayPrepare':
            pass

    def TestPlayVideo(self):
        MLive.SeekCCLive(0)
        MLive.StartCCLive()
    
    def TestDestroyVideoImg(self):
        if self._videoImg and self._videoImg.isvalid():
            self._videoImg.removeFromParent()
        self._videoImg = None

    def draw(self):
        if not self.HaveUINodeTestWindow():
            imgui.text("Waiting....")
            return
        # if imgui.button("手动创建测试UI节点"):
        #     self.CreateTestUINode()
        # imgui.same_line()
        # if imgui.button("UI节点Test Set"):
        #     self.teammate_1_ui_node.ResetSizePosByIndex()
        #     self.teammate_2_ui_node.ResetSizePosByIndex()
        #     self.teammate_3_ui_node.ResetSizePosByIndex()
        #     self.teammate_4_ui_node.ResetSizePosByIndex()
        #     pass
        # imgui.same_line()
        # if imgui.button("手动销毁测试UI节点"):
        #     self.teammate_1_ui_node.Destroy()
        #     self.teammate_2_ui_node.Destroy()
        #     self.teammate_3_ui_node.Destroy()
        #     self.teammate_4_ui_node.Destroy()
        # imgui.same_line()
        # if imgui.button("手动绑定测试UI节点"):
        #     MRender.SetRenderOption("OnlyDrawUI", True)
        #     self.teammate_manager.BindTeammateCtrlToUINode(self.teammate_1_ui_node.img_view, 0)
        #     self.teammate_manager.BindTeammateCtrlToUINode(self.teammate_2_ui_node.img_view, 1)
        #     self.teammate_manager.BindTeammateCtrlToUINode(self.teammate_3_ui_node.img_view, 2)
        #     self.teammate_manager.BindTeammateCtrlToUINode(self.teammate_4_ui_node.img_view, 3)       
        cur_window = self.GetUINodeTestWindow()
        if cur_window:
            if hasattr(cur_window, 'CSB_NAME'): 
                csb_name = cur_window.CSB_NAME
            else:
                csb_name = "None"
            imgui.text(f"cur_parent_window: {cur_window.__class__.__name__}")
            imgui.text(f"csb_name: {csb_name}")
        else:
            print_s("cur_parent_window is None", SomePreset.white_fg_red_bg)
        imgui.separator()

        imgui.text("飞机入场视频播放调试")
        imgui.separator()
        if imgui.button("1.测试CG视频预加载"):
            AirplaneVideoCtrl.instance().PreferCGVideo()
        imgui.same_line()
        if imgui.button("2.测试播放视频"):
            AirplaneVideoCtrl.instance().SeekAndPlayVideo()
        imgui.same_line()
        if imgui.button("3.测试CG视频结束"):
            AirplaneVideoCtrl.instance().DestroyVideoImg()

        if imgui.button("4.测试一键播放"):
            AirplaneVideoCtrl.instance().PlayVideo(play_over_callback=lambda: print_s("视频播放结束From Callback", SomePreset.white_fg_cyan_bg))
        cur_video_time = AirplaneVideoCtrl.instance().curTime
        cur_video_whole_time = AirplaneVideoCtrl.instance().wholeTime
        is_video_playing = AirplaneVideoCtrl.instance().is_playing
        is_video_hide_ui = AirplaneVideoCtrl.instance().is_hide_ui
        end_timer = AirplaneVideoCtrl.instance().end_timer
        end_timeout_timer = AirplaneVideoCtrl.instance().end_timeout_timer
        imgui.text(f"当前视频播放时间: {cur_video_time/1000.0}s,\n 总时间: {cur_video_whole_time/1000.0}s,\n 是否播放中: {is_video_playing},\t 是否隐藏UI: {is_video_hide_ui},\n 结束定时器: {end_timer},\t 结束超时定时器: {end_timeout_timer}")

        imgui.separator()
        imgui.text("无UI测试")
        imgui.separator()
        if imgui.button("1.测试初始化Teammate manager(自动创建并绑定临时UI节点)"):
            gpl.SetRenderOption("OnlyDrawUI", True)
            self.CreateTestUINode()
            self.teammate_manager.TestInit(4)
            self.teammate_manager.SetOnInitOverCallback(self.OnInitOver)
        if imgui.button("2.退出Teammate manager(销毁所有TeammateCtrl与临时UI节点)"):
            gpl.SetRenderOption("OnlyDrawUI", False)
            self.teammate_manager.ClearAllCtrl()
            self.teammate_manager.teammate_ctrl_list = {}
            self.teammate_1_ui_node.Destroy()
            self.teammate_2_ui_node.Destroy()
            self.teammate_3_ui_node.Destroy()
            self.teammate_4_ui_node.Destroy()
        # if imgui.button("只渲染UI"):
        #     MRender.SetRenderOption("OnlyDrawUI", True)
        # imgui.same_line()
        # if imgui.button("取消只渲染UI"):
        #     MRender.SetRenderOption("OnlyDrawUI", False)
       
        if imgui.button("3.随机替换所有Teammate"):
            random_hero_ids = random.sample(self.all_hero_ids, 4)
            for i, hero_id_str in enumerate(random_hero_ids):
                fade_out_duration = random.uniform(0.2, 0.2)
                ctrl = self.teammate_manager.teammate_ctrl_list[i]
                ctrl.SetBindImgBlack()
                self.teammate_manager.SetOnUpdateTeammateOverCallback(self.OnUpdateTeammateOver)
                self.teammate_manager.UpdateTeammate(i, int(hero_id_str), fade_out_duration=fade_out_duration)

        if imgui.button("4.动画全部重播 - 并切换阵营"):
            for index, teammate_ctrl in self.teammate_manager.teammate_ctrl_list.items():
                teammate_ctrl.SwitchTeamAndBg(not teammate_ctrl.is_blue_team, teammate_ctrl.bg_id)
                teammate_ctrl.TriggerPlay()
        imgui.text("小队UI测试")
        imgui.separator()
        # if imgui.button("Reset Manager"):
        #     self.teammate_manager.teammate_ctrl_list = {}
        if imgui.button("1.小队UI测试-预加载"):
            from ...gameplay.logic_base.ui.team_hero_show_comp_v2 import TeamHeroShowComp
            for i in range(4):
                self.teammate_manager.CreateTeammateCtrl(i)

        imgui.same_line()
        if imgui.button("2.小队UI测试-打开或关闭界面"):

            from ...gameplay.logic_base.ui.team_hero_show_comp_v2 import TeamHeroShowComp
            if TeamHeroShowComp.instance().visible:
                TeamHeroShowComp.instance().Close()
            else:
                self.show_self_team = True
                from common.IdManager import IdManager  # noqa
                entity_guid_self_1 = IdManager.genid()
                entity_guid_self_2 = IdManager.genid()
                entity_guid_self_3 = IdManager.genid()
                entity_guid_ace_1 = IdManager.genid()
                entity_guid_ace_2 = IdManager.genid()
                entity_guid_ace_3 = IdManager.genid()
                TeamHeroShowComp.instance().Show(
                    {
                        'self': {
                            genv.avatar.id:     {'hero_id': 101, 'show_name': 'test', 'slot': 0},  # noqa
                            entity_guid_self_1: {'hero_id': 118, 'show_name': 'test', 'slot': 1, 'is_test_hero': True},
                            entity_guid_self_2: {'hero_id': 107, 'show_name': 'test', 'slot': 2, 'is_test_hero': True},
                            entity_guid_self_3: {'hero_id': 119, 'show_name': 'test', 'slot': 3, 'is_test_hero': True},
                        },
                        'ace': {
                            genv.avatar.id:    {'hero_id': 119, 'show_name': 'test', 'slot': 0},  # noqa
                            entity_guid_ace_1: {'hero_id': 101, 'show_name': 'test', 'slot': 1, 'is_test_hero': True},
                            entity_guid_ace_2: {'hero_id': 119, 'show_name': 'test', 'slot': 2, 'is_test_hero': True},
                            entity_guid_ace_3: {'hero_id': 107, 'show_name': 'test', 'slot': 3, 'is_test_hero': True},
                        }
                    }
                )
        imgui.same_line()
        if imgui.button("3.小队UI测试-切换小队"):
            from ...gameplay.logic_base.ui.team_hero_show_comp_v2 import TeamHeroShowComp
            self.show_self_team = not self.show_self_team
            TeamHeroShowComp.instance().SwitchTeam()
        
        from ...gameplay.logic_base.ui.team_hero_show_comp_v2 import TeamHeroShowComp
        imgui.separator()
        # self.DrawTeamInfoPageInfo(TeamHeroShowComp.instance().self_team_panel)
        imgui.text("当前所有的TeammateCtrl")
        imgui.separator()
        # self.DrawTeamInfoPageInfo(TeamHeroShowComp.instance().ace_team_panel)
        imgui.separator()
        self.DrawTeammateManagerInfo()
    
    def DrawTeamInfoPageInfo(self, team_info_page):
        index = 0
        for id, player_card in team_info_page.player_cards.items():
            teammate_ctrl = player_card.teammate_ctrl
            self.DrawTestTeammateCtrlInfo(teammate_ctrl, index)
            imgui.separator()
            index += 1
    
    def OnUpdateTeammateOver(self, index):
        random_wait_time = random.sample(range(200, 800), 1)[0] / 1000.0
        random_wait_time = 0.2
        ctrl = self.teammate_manager.teammate_ctrl_list[index]
        ctrl.SetBindImgBlack()
        ctrl.TriggerPlay(random_wait_time)

    
    def OnInitOver(self, index):
        if index == 0:
            self.teammate_manager.BindTeammateCtrlToUINode(self.teammate_1_ui_node.img_view, 0)
        elif index == 1:
            self.teammate_manager.BindTeammateCtrlToUINode(self.teammate_2_ui_node.img_view, 1)
        elif index == 2:
            self.teammate_manager.BindTeammateCtrlToUINode(self.teammate_3_ui_node.img_view, 2)
        elif index == 3:
            self.teammate_manager.BindTeammateCtrlToUINode(self.teammate_4_ui_node.img_view, 3)

        random_wait_time = random.sample(range(100, 1000), 1)[0] / 1000.0
        random_wait_time = 0.2
        ctrl = self.teammate_manager.teammate_ctrl_list[index]
        ctrl.SetBindImgBlack()
        ctrl.TriggerPlay(random_wait_time)
    
    def DrawTeammateManagerInfo(self):
        for index, teammate_ctrl in self.teammate_manager.teammate_ctrl_list.items():
            self.DrawTestTeammateCtrlInfo(teammate_ctrl, index)
        pass
        
    def DrawTestTeammateCtrlInfo(self, teammate_ctrl, index):
        teammate_model = teammate_ctrl.teammate_model
        imgui.begin_group()
        imgui.next_column()
        imgui.begin_group()
        if teammate_model:
            info_str = f"Teammate Model: {index} - {teammate_model}"
            # teammate_area = teammate_model.model.model.Area  # noqa
            # showroom_world = teammate_ctrl.GetShowroomWorld()
            # if showroom_world:
            #     showroom_world_area = showroom_world.DefaultLevel.RootArea  # noqa
            # else:
            #     showroom_world_area = None  # noqa
            # area_str = f"SR Area: {showroom_world_area}, Teammate Area: {teammate_area}"
            show_info_expanded, visible = imgui.collapsing_header(f"角色-{index} 当前英雄ID{teammate_ctrl.teammate_info.hero_id}")

            imgui.indent()
            if show_info_expanded:
                state_str = teammate_ctrl.GetAllFlagStateInfoStr()
                imgui.text(info_str)
                teammate_hero_id_str = f"current Teammate Hero ID: {teammate_ctrl.teammate_info.hero_id}"
                imgui.text(teammate_hero_id_str)
                # imgui.text(area_str)
                imgui.text(state_str)
                world = teammate_ctrl.GetShowroomWorld()
                if world:
                    bg_level = world.Levels['L_MainHall_CharacterDebutDisplay']
                    self.DebugDrawEntitiesInLevel(bg_level, index)

            cur_selected_hero_id = self.teammate_selected_hero_id[index]
            if not cur_selected_hero_id:
                cur_selected_hero_id = teammate_ctrl.teammate_info.hero_id
            imgui.set_next_item_width(80)
            changed, combo_index = imgui.combo(f"替换英雄-{index}" , self.all_hero_ids.index(str(cur_selected_hero_id)), self.all_hero_ids)  # noqa
            if changed:
                self.teammate_selected_hero_id[index] = int(self.all_hero_ids[combo_index])

            imgui.same_line()
            if imgui.button(f"Set Hero {index} to {cur_selected_hero_id}"):
                self.teammate_manager.UpdateTeammate(index, cur_selected_hero_id)
                pass
            if imgui.button(f"触发演出-{index}"):
                teammate_ctrl.TriggerPlay()
            imgui.same_line()
            switch_light_str = f"切换<{index}号位>阵营为"
            if teammate_ctrl.is_blue_team:
                switch_light_str += "->红队"
            else:
                switch_light_str += "->蓝队"
            if imgui.button(f"{switch_light_str}"):
                teammate_ctrl.SwitchTeamAndBg(not teammate_ctrl.is_blue_team, teammate_ctrl.bg_id)
            # imgui.same_line()
            # if imgui.button(f"Override Level Material-{index}"):
            #     teammate_ctrl.SwitchLevelMaterial(teammate_ctrl.  is_blue_team, 6)
            # imgui.same_line()
            # if imgui.button(f"showmodel_{index}"):
            #     teammate_ctrl.teammate_model.ShowModel(True)

            imgui.unindent()
        else:
            info_str = f"Teammate Model: {index} - None"
            imgui.text(info_str)
        imgui.end_group()
        imgui.end_group()
        # if imgui.button(f"Test Set img-{index} color"):
        #     genv.TestPPV2 = self.teammate_1_ui_node.img_view
        #     self.teammate_1_ui_node.img_view.setColor(cc.Color3B(0, 0, 0))
        #     self.teammate_1_ui_node.img_view.setColor(cc.Color3B(255, 255, 255))

        imgui.separator()
        pass
    
    def DebugDrawEntitiesInLevel(self, level, index):
        show_info_expanded, visible = imgui.collapsing_header(f"Level: {level.GetName()} - {index}")
        imgui.indent()
        if show_info_expanded:
            index = 0
            for entity in level.RootArea.Entities:
                is_visible = entity.IsVisible
                show_hide_btn_str = "Hide" if is_visible else "Show"
                if imgui.button(f"{show_hide_btn_str}_{index}"):
                    entity.IsVisible = not is_visible

                imgui.same_line()
                if imgui.button(f"材质替换测试_{index}"):
                    test_path = 'Scenes/MainHall/Materials/MI_CharacterDisplay_Background_09_Blue'
                    prims = entity.Primitives
                    for prim in prims:
                        applied = prim.ApplyCustomMaterial(test_path)
                        print_s(f"applied: {applied}, path: {test_path}", SomePreset.white_fg_red_bg)
                        if applied:
                            test2_path = 'ShaderGraph/material/PBR_copy_Clip_IBL0'
                            prim.OverrideMaterial(test2_path)
                            prim.OverrideMaterial(test_path)

                imgui.same_line()
                imgui.text(f"\tEntity: {entity.GetName()}, \tIsVisible: {is_visible}")
                index += 1
        imgui.unindent()

    def set_window_size(self):
        pyimgui.set_next_window_size(MUI.GetScreenWidth() / 5.0, MUI.GetScreenHeight() / 4.0,
                                     condition=pyimgui.FIRST_USE_EVER)

    def set_window_position(self):
        pyimgui.set_next_window_position(0, MUI.GetScreenHeight() / 6.0,
                                         condition=pyimgui.FIRST_USE_EVER)

    def close(self):
        super(SplitScreenTeammatesWindow, self).close()


@gmcmd("imgui_splitScreenTeammates")
def toggle_imgui_splitScreenTeammates():
    if SplitScreenTeammatesWindow.isInited():
        SplitScreenTeammatesWindow.instance().close()
        SplitScreenTeammatesWindow.Destroy()
    else:
        SplitScreenTeammatesWindow.instance().open()
   