# -*- coding: utf-8 -*-
# import os

from gclient.util.gmcmds.smoke_test_robot.robot_util import ShowTitle
from gshare.async_util import Async
from ..aichat import g83_aigw
import Timer
from functools import partial
# from gclient.data import snare_data, 

# from gclient.util.gmcmds.smoke_test_robot import robot_util
# from gclient.util.gmcmds.smoke_test_robot.testcasemain import Free<PERSON>llMemory
# from gclient.data import weapon_data
# import MStatistics
# import MLauncher
# import MEngine
# import MRender
# import MProfile
# import MCharacter
# import subprocess
# import re

CHANNEL_WORLD, CHANNEL_TEAM, CHANNEL_PRIVATE = list(range(2, 5))
from gshare import ichat


class RobotChatTool():
    def __init__(self):
        self.last_reply_msg_guid = ""
        ShowTitle("[AI聊天]测试中")
        self.main()
        pass

    def GetAIReply(self, final_msg):
        msg_content = final_msg["content"]
        ai_service = g83_aigw.AIChatService.instance()
        cur_chat_callback = partial(self.chat_callback, player_msg=final_msg)
        user_id = final_msg["frm"]
        if not ai_service.use_cache or user_id not in ai_service.message_cache:
            ai_service.ChatCompletion(
                user_id=user_id,
                message=msg_content,
                callback=cur_chat_callback,
                system_prompt="你是一个女大学生，你叫小光。",
                # new_chat=True
            )
        else:
            ai_service.ChatCompletion(
                user_id=user_id,
                message=msg_content,
                callback=cur_chat_callback,
            )
        # return "hhh"

    def chat_callback(self, sucess, response, player_msg=None):
        # ai_service = g83_aigw.AIChatService.instance()
        if sucess:
            ShowTitle("[AI回复啦]")
            self.SendChat(response, player_msg)
        # msg_cache = ai_service._get_message_cache("assistant", g83_aigw.EmulatesMessageInfo)
        # print(sucess, response)
   
    def SendChat(self, reply_content, final_msg):
        robot_avatar = genv.avatar
        
        cur_msg_frmid = final_msg["frm"]
        self.cur_channel = final_msg["channel"]
        msg_type = final_msg.get("msg_type", None)
        # msg_id = final_msg.get("msg_type", None)

        channel_id = None
        extra = {}
        extra.update({'gender': robot_avatar.gender, 'level': robot_avatar.level})
        kwargs = {'content': reply_content, 'extra': extra}
        # 'sound': sound
        
        if self.cur_channel == CHANNEL_TEAM:
            if not robot_avatar.team.member_dict:
                # 没有小队，不允许发送
                return
            channel_id = ichat.ChatChannel.HallTeam
        elif self.cur_channel == CHANNEL_PRIVATE:
            ShowTitle("[聊天中]")
            # if not self.chat_avatar_info_list:
            #     # 没有私聊对象，不允许发送
            #     return
            channel_id = ichat.ChatChannel.HallFriend
            friend_id = cur_msg_frmid
            if friend_id not in robot_avatar.friends:
                # 对陌生人发起私聊
                # if not self.CheckPrivateChatCD(friend_id):
                #     # 弹窗
                #     gui.Prompt(1267)
                return
            kwargs['to'] = friend_id
            extra['to_name'] = robot_avatar.name
        
        # share_info = 
        # if share_info:
        #     kwargs['msg_type'] = share_info[0]
        #     extra['share_info'] = pickle.dumps(share_info[1])

        # if at_player:
        #     extra['at_player_id'], extra['at_player_name'] = at_player

        if msg_type:
            kwargs['msg_type'] = msg_type

        # if msg_id:
        #     extra['msg_id'] = msg_id

        channel_id and robot_avatar.SendChat(channel_id, **kwargs)
        return True

    def OnChating(self):
        robot_avatar = genv.avatar
        final_msg = robot_avatar.GetNewFriendMsg()
        if final_msg is None or final_msg['guid'] == self.last_reply_msg_guid:
            return 
        else:
            ShowTitle("[AI听到一个消息]")
            self.GetAIReply(final_msg)
            self.last_reply_msg_guid = final_msg['guid']
        pass

    def TryFriendApply(self):
        robot_avatar = genv.avatar
        for eid, apply_firend in robot_avatar.apply_list.items():
            # apply_time = apply_firend.apply_time
            apply_ignore = apply_firend.apply_ignore
            # if apply_time > _time and not apply_ignore:
            if eid and eid not in robot_avatar.friends:
                friend_apply_eid = eid
                ShowTitle(f"[聊天机器人] 收到好友{friend_apply_eid}申请，同意啦！")
                robot_avatar.ReplyFriendApply(friend_apply_eid, True)
                robot_avatar.IgnoreFriendApply(friend_apply_eid)

    def CancelTimer(self):
        self.apply_timer and self.apply_timer.cancel()
        self.chat_timer and self.chat_timer.cancel()
        self.normal_timer and self.normal_timer.cancel()

    def main(self):
        self.apply_timer = Timer.addRepeatTimer(1, self.TryFriendApply)
        self.chat_timer = Timer.addRepeatTimer(1, self.OnChating)
        self.normal_timer = Timer.addRepeatTimer(5, lambda: ShowTitle("[AI聊天]测试中"))

# class RobotJoinTeamTool():
    
#     def __init__(self):
        
#         pass

#     def CacelTimer(self):
#         pass

#     def JoinTeam(self):
#         robot_avatar = genv.avatar
#         recv_total_appliers = robot_avatar.recv_total_appliers
        
#         invite_applier = recv_total_appliers[0] if recv_total_appliers else None
#         invite_time = avatar.recv_total_appliers_info[invite_applier][-1] if invite_applier else 0.0
#         if invite_time > apply_time:
#             self.show_type = SHOW_TYPE_INVITE
#             if invite_applier and self.current_inviter_id != invite_applier:
#                 self.current_inviter_id = invite_applier
#                 self.RefreshCurrentInviterInfo()
#         else:
#             self.show_type = SHOW_TYPE_APLLY_FRIEND
#             if friend_apply_eid and self.current_friend_apply_id != friend_apply_eid:
#                 self.current_friend_apply_time = time.time()
#                 self.current_friend_apply_id = friend_apply_eid
#                 self.RefreshCurrentFriendApplyInfo()
#         pass
#     pass

def ChatDecorator(func):
    @Async
    def wrapper(*args, **kwargs):
        rct = RobotChatTool()
        yield func(*args, **kwargs)
        rct.CancelTimer()
    return wrapper

@ChatDecorator
@Async
def OnGM(cmd, logger=None):
    # rct = RobotChatTool()
    # t=100
    # while t>0:
    #     t-=1
    #     print("hh")
    #     yield 0.5
    yield 600
    # rct.CancelTimer()
    # yield 2
