# -*- coding: utf-8 -*-
# import functools
# import time
import GlobalData
# import json
import os
import sys

from gclient.util.gmcmds.smoke_test_robot.robot_util import ShowTitle
from gshare.async_util import Async
# from gclient.data import snare_data, 
from gclient.data import effect_data
from gshare import effect_util
from gclient.util.gmcmds.smoke_test_robot import robot_util
from gclient.util.gmcmds.smoke_test_robot.testcasemain import FreeAllMemory
from gclient.util.gmcmds.smoke_test_robot.gpm_tracy import GPM
import MType
import MUI
import MStatistics
import MLauncher
import Timer
import MEngine
import MRender
import MProfile
import <PERSON>haracter
import subprocess
import re
import pickle


PackagePath = MLauncher.PKGROOT
if "WinTrunkPatch\\" in PackagePath:
    PackagePath = PackagePath[:PackagePath.find("WinTrunkPatch\\")] + PackagePath[PackagePath.find("WinTrunkPatch\\") + len("WinTrunkPatch\\"):]

DIR_PATH = os.path.join(PackagePath, '../../docs/EffectCollect')
batlog = os.path.join(DIR_PATH, "effect_recording.txt")
effect_path = os.path.join(DIR_PATH, "all_effect_path")
gputime_path = os.path.join(DIR_PATH, "gpu.pkl")

if not os.path.exists(DIR_PATH):
    os.makedirs(DIR_PATH)
if not os.path.exists(batlog):
    open(batlog, 'w').close()


all_effect_id, all_effect_name, all_effect_path = None, None, None

TestLocal = False
wrong_ids = [93, 92, 100017, 65, 84]

def GetAllEffect():
    table_10 = "{}_10.pkl".format(effect_path)
    ### if not os.path.exists(table_10):
    all_effect_id = []
    all_effect_name = []
    all_effect_path = []
    for data in effect_data.data.values():
        if "path" in data.keys():
            all_effect_path.append(data["path"])
        elif "effect_string" in data.keys():
            all_effect_path.append(data["effect_string"])
        else:
            continue
        all_effect_id.append(int(data["id"]))
        all_effect_name.append(data["name"])
        # all_effect_path.append("")

    combined = list(zip(all_effect_id, all_effect_name, all_effect_path))
    sorted_combined = sorted(combined, key=lambda x: x[0])
    all_effect_id, all_effect_name, all_effect_path = zip(*sorted_combined)
    all_path_10 = all_effect_path
    with open(table_10, 'wb') as file:
        pickle.dump(sorted_combined, file)
    # else:
    #     with open(table_10, 'rb') as file:
    #         all_effect_id, all_effect_name, all_path_10 = zip(*pickle.load(file))

    table_158 = "{}_158.pkl".format(effect_path)
    ### if not os.path.exists(table_158):
    from gclient.data import glass_data 
    all_path_158 = set()
    for data in glass_data.data.values():
        if "effect_path" in data.keys():
            all_path_158.add(data["effect_path"])
        if "impact_effect_paths" in data.keys():
            for pp in data["impact_effect_paths"]:
                all_path_158.add(pp)
    all_path_158 = list(all_path_158)
    with open(table_158, 'wb') as file:
        pickle.dump(all_path_158, file)
    # else:
    #     with open(table_158, 'rb') as file:
    #         all_path_158= pickle.load(file)

    table_4 = "{}_4.pkl".format(effect_path)
    # if not os.path.exists(table_4):
    from gclient.data import material_type_data
    all_path_4 = set()
    for data in material_type_data.data.values():
        for key, value in data.items():
            if "effect_path" in key:  
                all_path_4.add(value)
    all_path_4 = list(all_path_4)
    with open(table_4, 'wb') as file:
        pickle.dump(all_path_4, file)
    # else:
    #     with open(table_4, 'rb') as file:
    #         all_path_4 = pickle.load(file)
    return all_path_4, all_path_10, all_path_158

def GetSVNRevision(path="src"):
    PKGROOT = MLauncher.PKGROOT
    if "WinTrunkPatch\\" in PKGROOT:
        PKGROOT = PKGROOT[:PKGROOT.find("WinTrunkPatch\\")] + PKGROOT[PKGROOT.find("WinTrunkPatch\\") + len("WinTrunkPatch\\"):]
    result = subprocess.run(['svn', 'info', os.path.join(PKGROOT, "..\\..", path)], capture_output=True, text=True, check=False)
    if result.returncode != 0:
        return 10086
    match = re.search(r'Rev: (\d+)', result.stdout)
    if match:
        return int(match.group(1))
    else:
        return 10086

def RevertEffectSVN(revision, path="src/Package/Repository"):
    PKGROOT = MLauncher.PKGROOT
    if "WinTrunkPatch\\" in PKGROOT:
        PKGROOT = PKGROOT[:PKGROOT.find("WinTrunkPatch\\")] + PKGROOT[PKGROOT.find("WinTrunkPatch\\") + len("WinTrunkPatch\\"):]
    result = subprocess.run(['svn', 'update', "-r", revision, os.path.join(PKGROOT, "..\\..", path), "--accept", "theirs-full"], capture_output=True, text=True, check=True)
    if result.returncode != 0:
        return False
    return True

def RevertUpdateEffect(func):
    @Async
    def wrapper(self, *args, **kwargs):
        cur_revision = GetSVNRevision("src/Package/Repository")
        target_revision = MEngine.Args.split(":")[-3] if "RevertEffect" in MEngine.Args else cur_revision
        ShowTitle("[Effect 回退检测] 开始")
        if target_revision != str(cur_revision):
            ShowTitle("[Effect 回退开始]")
            if RevertEffectSVN(target_revision):
                yield func(self, *args, **kwargs)
            ShowTitle("[Effect 更新svn] 开始")
            RevertEffectSVN(cur_revision) 
            ShowTitle("[Effect] 结束")
        else:
            yield func(self, *args, **kwargs)
            ShowTitle("[Effect] 结束")

    return wrapper

def append_number_to_file(number):
    global batlog
    # 以追加模式打开文件
    with open(batlog, 'a') as file:
        # 写入数字并换行
        file.write(f"{number}\n")

class EffectRunner():
    
    def __init__(self):
        GetAllEffect()
        table_10 = "{}_10.pkl".format(effect_path)
        with open(table_10, 'rb') as file:
            all_effect_id, all_effect_name, all_effect_path = zip(*pickle.load(file))
        cur_index = int(MEngine.Args.split(":")[-2])
        if "single" in MEngine.Args:
            # 单个模式，输入为真正的特效id，则将其转化为列表id
            for i, effect_id in enumerate(all_effect_id):
                if effect_id == cur_index:
                    cur_index = i
                    break
        lod = int(MEngine.Args.split(":")[-1])
        self.lod = lod
        self.cur_index = cur_index
        self.effect_id = all_effect_id[cur_index]
        self.effect_path = all_effect_path[cur_index]
        self.cur_effect_name = "id_{}|name_{}|lod_{}".format(self.effect_id, all_effect_name[cur_index], lod)
        
        self.report = {}
        effect_draw_call_id = MProfile.TracyRegisterPlot("EffectDrawCall")
        self.report["EffectDrawCall"] = effect_draw_call_id
        effect_primitive_id = MProfile.TracyRegisterPlot("EffectPrimitive")
        self.report["EffectPrimitive"] = effect_primitive_id
       
        draw_call_id = MProfile.TracyRegisterPlot("DrawCall")
        self.report["DrawCall"] = draw_call_id
        primitive_id = MProfile.TracyRegisterPlot("Primitive")
        self.report["Primitive"] = primitive_id
        gpu_time_id = MProfile.TracyRegisterPlot("GpuTime")
        self.report["GpuTime"] = gpu_time_id
        
        device_time_id = MProfile.TracyRegisterPlot("DeviceTime")
        self.report["DeviceTime"] = device_time_id
        particle_id = MProfile.TracyRegisterPlot("Particle")
        self.report["Particle"] = particle_id


        self.pre_primitive = 0
        self.pre_draw_call = 0
        
        self.is_wrong_effect = False
        self.new_agent = False
        self.svn_revision = "t{}.r{}".format(GetSVNRevision("src/Engine"), GetSVNRevision("src/Package/Repository"))
        self.SEND_TO = 7469358

        self.main()
        
    def GetPrePrimitive(self):
        render_statistics = MStatistics.GetRenderStatistics().splitlines()
        self.pre_draw_call = int(render_statistics[0][9:])
        self.pre_primitive = int(render_statistics[1][10:])
           
    def parse_time(self, time_str):
        # 拆分字符串
        parts = time_str.split(',')
        
        total_seconds = 0.0
        
        for part in parts:
            if 'ms' in part:
                value = float(part.replace('ms', '').strip())
                total_seconds += value  # 毫秒
            elif 'us' in part:
                value = float(part.replace('us', '').strip())
                total_seconds += value * 0.001  # 微秒 → 毫秒
        
        return total_seconds

    def TickRecord(self):
        render_statistics = MStatistics.GetRenderStatistics().splitlines()
        draw_call = int(render_statistics[0][9:]) 
        primitive = int(render_statistics[1][10:]) 
        device_time = self.parse_time(MStatistics.GetWatcherValue('Engine', 'DeviceTime'))
        # entity = MCharacter.GetEffectEntity(self.cur_effect)
        # MStatistics.GetUsingTextures()
        # MStatistics.GetUsingMesh()
        if primitive < self.pre_primitive:
            self.is_wrong_effect = True
        robot_util.ReportToTracy(self.report, "EffectDrawCall", draw_call - self.pre_draw_call, False, True)
        robot_util.ReportToTracy(self.report, "EffectPrimitive", primitive - self.pre_primitive, False, True)
        robot_util.ReportToTracy(self.report, "DrawCall", draw_call, False, True)
        robot_util.ReportToTracy(self.report, "Primitive", primitive, False, True)
        robot_util.ReportToTracy(self.report, "GpuTime", MStatistics.GetTotalGPUTime(), False, True)    
        robot_util.ReportToTracy(self.report, "DeviceTime", device_time, False, True)
        robot_util.ReportToTracy(self.report, "Particle", 0, False, True)

    def RefreshEffect(self):
        effect_entity = MCharacter.GetEffectEntity(self.cur_effect)
        if effect_entity is None:
            effect_util.ClearWorldEffectImmediately(self.cur_effect)
            self.cur_effect = effect_util.WrapperPlayEffectInWorld(self.effect_id, self.center_pos, 10, True)

    def StartVideo(self):
        print("[特效测试] 准备启动record")
        if GPM.getTraceStatus():
            print("[特效测试] 当前还有正在进行的tracy处理")
            Timer.addTimer(1, self.StartVideo)
            return
        if GPM.get_connected_callback_occured()['agent'] and not self.new_agent:
            print("[特效测试] 曾经连接过agent，先断开")
            GPM.agent_disconnect()
            Timer.addTimer(3, self.StartVideo)
            return
        if GPM.get_connected_status()['agent']:
            print("[特效测试] 正在连接agent...")
            Timer.addTimer(3, self.StartVideo)
            return
        if not GPM.get_server_is_connect():
            print("[特效测试] 视频服务器未连接 正在尝试连接...")
            self.new_agent = True
            GPM.link_to_agent(agentIp="*************", camera="0")
            Timer.addTimer(3, self.StartVideo)
            return
        print("[特效测试] 视频连接成功！", self.cur_effect_name)

    def ChangeSetting(self):
        # MStatistics.TracyEnableGPUQuery(1)
        MCharacter.SetWorldEffectLod(self.lod)
        MRender.SetRenderOption("EnableAMDFSR", False)
        MRender.SetRenderOption("EnableAMDFSR2", False)
        MRender.SetRenderOption("EnableAMDFSRSharpen", False)
        MRender.SetRenderOption("EnableAMDFSRHighPrecisionMatrix", False)
        MStatistics.EnableRenderStatistics(True)

    def ChangeSettingPre(self):
        MRender.SetDebugOption("HideUI", True)
        genv.player.hand_model.AddHiddenReason(1)
        genv.player.CheckAndTakeMeleeWeapon()

    def AvatarSetting(self):
        genv.avatar.server.Eval("self.c.pitch = 0; self.c.yaw=0")
        genv.camera.placer.TurnView(0, 0.5)
        genv.avatar.server.Eval("self.c.position = (-38, -1, 30)")

    def GetBaseInfo(self):
        render_statistics = MStatistics.GetRenderStatistics().splitlines()
        self.pre_draw_call = int(render_statistics[0][9:])
        self.pre_primitive = int(render_statistics[1][10:])
        self.pre_gputime = MStatistics.GetTotalGPUTime()
        print(self.pre_draw_call, self.pre_primitive)

    @Async
    def main(self):
        ShowTitle("[特效测试] 开始执行")
        robot_util.GoToSpace(76, 31, 1)
        for i in range(100):
            # 玩家创建出来就算成功
            if genv.player and genv.player.position:
                break
            yield 0.5
        yield 3
        self.AvatarSetting()
        self.center_pos = (-38, 0.5, 38)
        yield 2

        self.ChangeSettingPre()
        if not TestLocal:
            self.StartVideo()
        yield 2

        self.ChangeSetting()
        yield 2
        self.GetBaseInfo()
        yield 2

        ShowTitle("[特效测试Tracy] 开始")
        if TestLocal:
            robot_util.StartTracy("6888857d2e72b42d2b9a11c6", self.cur_effect_name, self.cur_effect_name,
                                enableVideo=False, autoStop=False, force=True, 
                                meta={"path": self.effect_path}, version=self.svn_revision)
        else:
            robot_util.StartTracy("687dd7c045aabd5256bca4ca", self.cur_effect_name, self.cur_effect_name,
                                enableVideo=True, autoStop=False, force=True, 
                                meta={"path": self.effect_path}, version=self.svn_revision)
        MStatistics.EnableGPUQuery(False)
        ShowTitle("[特效测试] 开始记录信息")

        t = Timer.addRepeatTimer(0.016, self.TickRecord)
        self.cur_effect = effect_util.WrapperPlayEffectInWorld(self.effect_id, self.center_pos, 10, True)
        t2 = Timer.addRepeatTimer(0.016, self.RefreshEffect)
        yield 10

        ShowTitle("[特效 {}] 执行结束正在返回".format(self.cur_effect_name))
        t.cancel()
        t2.cancel()
        robot_util.StopTracy(force=True)
        effect_util.ClearWorldEffectImmediately(self.cur_effect)
        yield 15

        MRender.SetDebugOption("HideUI", False)
        genv.player.hand_model.RemoveHiddenReason(1)
        # genv.smoke_robot.RecordPassInfos(True)

        if self.is_wrong_effect:
            robot_util.RobotSendPoPo("负数特效!!! ID:{}".format(self.effect_id), uid=self.SEND_TO, force=True)
        append_number_to_file("{}:id_{}/lod_{}".format(self.cur_index, self.effect_id, self.lod))
        
        genv.avatar.CallServer("RequestEnterHall")
        yield 1
        FreeAllMemory()
        robot_util._ExitGame()

class EffectRunnerName():
    
    def __init__(self, table_id=158):
        ShowTitle("[特效(路径)测试]")
        if table_id == 158:
            all_path = GetAllEffect()[2]
        elif table_id == 4:
            all_path = GetAllEffect()[0]
        if all_path == []:
            print("not support table id")
            return
        self.table_id = table_id
        all_path = sorted(list(all_path))

        cur_index = int(MEngine.Args.split(":")[-2])
        lod = int(MEngine.Args.split(":")[-1])
        self.lod = lod
        self.cur_index = cur_index
        self.effect_id = self.cur_index
        self.effect_path = all_path[cur_index].split(":")[0]
        if table_id == 158:
            self.cur_effect_name = "T158:id_{}|name_{}|lod_{}".format(self.effect_id, all_path[cur_index], lod)
        else:
            self.cur_effect_name = "Glass:id_{}|name_{}|lod_{}".format(self.effect_id, all_path[cur_index], lod)

        self.effect_str = self.effect_path

        self.report = {}
        effect_draw_call_id = MProfile.TracyRegisterPlot("EffectDrawCall")
        self.report["EffectDrawCall"] = effect_draw_call_id
        effect_primitive_id = MProfile.TracyRegisterPlot("EffectPrimitive")
        self.report["EffectPrimitive"] = effect_primitive_id
       
        draw_call_id = MProfile.TracyRegisterPlot("DrawCall")
        self.report["DrawCall"] = draw_call_id
        primitive_id = MProfile.TracyRegisterPlot("Primitive")
        self.report["Primitive"] = primitive_id
        
        gpu_time_id = MProfile.TracyRegisterPlot("GpuTime")
        self.report["GpuTime"] = gpu_time_id


        self.pre_primitive = 0
        self.pre_draw_call = 0
        
        self.is_wrong_effect = False
        self.new_agent = False
        self.svn_revision = "{}".format(GetSVNRevision())
        self.SEND_TO = 7469358

        self.GetEffectStr()

        self.RecordSingle()
       
    def GetEffectStr(self):
        suffix = 'root:-1:01000000'
        self.effect_str = '%s:%s' % (self.effect_str, suffix)
        
    def GetPrePrimitive(self):
        render_statistics = MStatistics.GetRenderStatistics().splitlines()
        self.pre_draw_call = int(render_statistics[0][9:])
        self.pre_primitive = int(render_statistics[1][10:])
    
    def TickRecord(self):
        render_statistics = MStatistics.GetRenderStatistics().splitlines()
        draw_call = int(render_statistics[0][9:]) 
        primitive = int(render_statistics[1][10:]) 
        if primitive < self.pre_primitive:
            self.is_wrong_effect = True
        robot_util.ReportToTracy(self.report, "EffectDrawCall", draw_call - self.pre_draw_call, False, True)
        robot_util.ReportToTracy(self.report, "EffectPrimitive", primitive - self.pre_primitive, False, True)
        robot_util.ReportToTracy(self.report, "DrawCall", draw_call, False, True)
        robot_util.ReportToTracy(self.report, "Primitive", primitive, False, True)
        robot_util.ReportToTracy(self.report, "GpuTime", MStatistics.GetTotalGPUTime(), False, True)
    
    def RefreshEffect(self):
        effect_entity = MCharacter.GetEffectEntity(self.cur_effect)
        if effect_entity is None:
            print("[特效太短啦]")
            effect_util.ClearWorldEffectImmediately(self.cur_effect)
            self.cur_effect = MCharacter.PlayEffectInWorld(self.effect_str, self.center_pos, -1)

    def StartVideo(self):
        print("[特效测试] 准备启动record")
        if GPM.getTraceStatus():
            print("[特效测试] 当前还有正在进行的tracy处理")
            Timer.addTimer(1, self.StartVideo)
            return
        if GPM.get_connected_callback_occured()['agent'] and not self.new_agent:
            print("[特效测试] 曾经连接过agent，先断开")
            GPM.agent_disconnect()
            Timer.addTimer(3, self.StartVideo)
            return
        if GPM.get_connected_status()['agent']:
            print("[特效测试] 正在连接agent...")
            Timer.addTimer(3, self.StartVideo)
            return
        if not GPM.get_server_is_connect():
            print("[特效测试] 视频服务器未连接 正在尝试连接...")
            self.new_agent = True
            GPM.link_to_agent(agentIp="*************", camera="0")
            Timer.addTimer(3, self.StartVideo)
            return
        print("[特效测试] 视频连接成功！", self.cur_effect_name)

    @Async
    def RecordSingle(self):
        ShowTitle("[特效测试] 开始执行")
        robot_util.GoToSpace(76, 31, 1)
        for i in range(100):
            # 玩家创建出来就算成功
            if genv.player and genv.player.position:
                break
            yield 0.5
        yield 5
        genv.avatar.server.Eval("self.c.pitch = 0;self.c.yaw=0")
        genv.avatar.server.Eval("self.c.position = (-38, -1, 30)")
        if self.table_id in [4]:
            genv.camera.placer.TurnView(0, 0.5)
        self.center_pos = (-38, 0.5, 38)
        self.center_pos = MType.Vector3(*self.center_pos)
        MCharacter.SetWorldEffectLod(self.lod)
        yield 2
        MRender.SetDebugOption("HideUI", True)
        genv.player.hand_model.AddHiddenReason(1)
        self.StartVideo()
        yield 12
        # region 开启特效统计
        MStatistics.EnableRenderStatistics(True)
        MUI.SetCursorVisibility(False)
        MRender.SetRenderOption("EnableAMDFSR", False)
        MRender.SetRenderOption("EnableAMDFSR2", False)
        MRender.SetRenderOption("EnableAMDFSRSharpen", False)
        MRender.SetRenderOption("EnableAMDFSRHighPrecisionMatrix", False)
        yield 4
        # endregion
        # region 记录初始信息
        render_statistics = MStatistics.GetRenderStatistics().splitlines()
        self.pre_draw_call = int(render_statistics[0][9:])
        self.pre_primitive = int(render_statistics[1][10:])
        print(self.pre_draw_call, self.pre_primitive)
        yield 4
        # endregion
        # region 开始tracy
        ShowTitle("[特效测试Tracy] 开始")
        robot_util.StartTracy("687dd7c045aabd5256bca4ca", self.cur_effect_name, self.cur_effect_name,
                              enableVideo=True, autoStop=False, force=True, 
                              meta={"path": self.effect_path}, version=self.svn_revision)
        t = Timer.addRepeatTimer(0.016, self.TickRecord)
        self.cur_effect = MCharacter.PlayEffectInWorld(self.effect_str, self.center_pos, -1)
        t2 = Timer.addRepeatTimer(0.016, self.RefreshEffect)
        yield 10
        t.cancel()
        t2.cancel()
        robot_util.StopTracy(force=True)
        effect_util.ClearWorldEffectImmediately(self.cur_effect)
        ShowTitle("[特效 {}] 执行结束正在返回".format(self.cur_effect_name))
        yield 15
        # endregion
        # region 关闭
        MRender.SetDebugOption("HideUI", False)
        genv.player.hand_model.RemoveHiddenReason(1)
        # genv.smoke_robot.RecordPassInfos(True)
        genv.avatar.CallServer("RequestEnterHall")
        if self.is_wrong_effect:
            robot_util.RobotSendPoPo("负数特效!!! ID:{}".format(self.effect_id), uid=self.SEND_TO, force=True)
        append_number_to_file("{}:id_{}/lod_{}".format(self.cur_index, self.effect_id, self.lod))
        yield 1
        # endregion
        FreeAllMemory()
        robot_util._ExitGame()

class UpdateEffectDate():
    def __init__(self):
        from editor.repres import Resource
        ShowTitle("[特效测试] 更新特效数据")
        self.guidMap = Resource.guidMap
        self.vpathMap = Resource.vpathMap
        self.dataMap = Resource.dataMap
        record_file = os.path.join(DIR_PATH, "change_recording.pkl")
        need_file_path = os.path.join(DIR_PATH, "test_path.pkl")
        is_first = False
        t4, t10, t158 = GetAllEffect()
        merge_list = lambda *lists: list(set().union(*lists)) 
        effect_paths = merge_list(t4, t10, t158)
        effect_paths = [path.split(':')[0] for path in effect_paths]
        if not os.path.exists(record_file):
            self.pre_recording = {}
            is_first = True
        else:
            self.pre_recording = self.LoadDict(record_file)
        self.cur_recording = {}
        need_update = []
        if is_first:
            print("第一次")
            for vpath, vRes in Resource.vpathMap.items():
                if vpath not in effect_paths:
                    continue
                self.SolveSelfAndDep(vRes)
        else: # 不是第一次
            print("开始检测")
            for vpath, vRes in Resource.vpathMap.items():
                if vpath not in effect_paths:
                    continue
                file_true_path = self.GetTruePath(vRes)
                if file_true_path == "":
                    continue
                if self.IsChanged(vRes):
                    print(vpath, file_true_path, "\n")
                    need_update.append(vpath)
        self.need_update_files = set(need_update)
        self.SaveFile(self.cur_recording, record_file)
        self.SaveFile(self.need_update_files, need_file_path)
    
    @Async    
    def GetBat(self):
        self.bat_ok = False
        self.GenBat()
        while not self.bat_ok:
            yield 1
        FreeAllMemory()
        robot_util._ExitGame()
        
    def GetTruePath(self, vRes):
        if vRes.type in self.dataMap:
            filename = self.dataMap[vRes.type]
            file_true_path = vRes.getDataPath(filename)
            if file_true_path is None:
                filename = filename + ".win64"
                file_true_path = vRes.getDataPath(filename)
            return file_true_path
        else:
            return ""
    
    def GetFileModiDate(self, file_path):
        # 获取文件的最后修改时间戳
        timestamp = os.path.getmtime(file_path)
        return timestamp

    def SaveFile(self, dictionary, file_path):
        """
        :param dictionary: 要保存的字典
        :param file_path: 保存的文件路径
        """
        with open(file_path, 'wb') as file:
            pickle.dump(dictionary, file)

    def IsChanged(self, tmpR):
        vpath = tmpR.vpath
        file_true_path = self.GetTruePath(tmpR)
        # 如果文件不存在，默认为不用更新。
        if file_true_path == "":
            return False
        self.cur_recording[vpath] = self.GetFileModiDate(file_true_path)
        if vpath not in self.pre_recording:
            res = True
        else:
            res = (self.pre_recording[vpath] != self.GetFileModiDate(file_true_path))
        for vguid in tmpR.deplist:
            res = self.IsChanged(self.guidMap[vguid]) or res
        return res
    
    def SolveSelfAndDep(self, tmpR):
        vpath = tmpR.vpath
        if vpath in self.cur_recording:
            return 
        file_true_path = self.GetTruePath(tmpR)
        if file_true_path == "":
            return 
        self.cur_recording[vpath] = self.GetFileModiDate(file_true_path)
        for vguid in tmpR.deplist:
            self.SolveSelfAndDep(self.guidMap[vguid])
        return 

    def LoadDict(self, file_path):
        """
        从文件加载字典
        
        :param file_path: 要加载的文件路径
        :return: 加载的字典
        """
        with open(file_path, 'rb') as file:
            return pickle.load(file)

    def GenBat(self):
        """
        目前只考虑支持一下900+的那个表里的全部特效捏
        """
        t10_data = []
        table_10 = "{}_10.pkl".format(effect_path)
        with open(table_10, 'rb') as file:
            all_effect_id, all_effect_name, all_effect_path = zip(*pickle.load(file))
        for i in range(len(all_effect_id)):
            eff_name = all_effect_name[i]
            if eff_name.split(':')[0] in self.need_update_files:
                t10_data.append(i)
        
        def GenT10Bat():
            t10_bat_path = os.path.join(DIR_PATH, "t10.bat")
            nonlocal t10_data
            t10_str = ' '.join(map(str, t10_data))
            script = "@echo off\n\n"
            script += "cd ../../src/Engine/Binaries/Win64/\n"
            script += f"set \"LOOP_RANGE={t10_str}\"\n"
            script += "for %%i in (%LOOP_RANGE%) do (\n"
            script += "    for /L %%l in (0,1,2) do (\n"
            script += "        Game_x64h.exe --dx11 --console --start=Python --python-args=innerdesktop;MainRobotTest;PlayEffect:%%i:%%l --sound-api=wwise --disable-shepherd --disable-streamline --enable-renderdoc\n"
            script += "    )\n"
            script += ")\n"
            script += "exit\n"
            with open(t10_bat_path, "w") as f:
                f.write(script)
            self.bat_ok = True
        GenT10Bat()
        
        
        print("Gen Table-10 ok!")

        
@Async
def OnGM(cmd, info=None):
    ShowTitle("[特效测试]")
    if "qfupdate" in MEngine.Args:
        update_tool = UpdateEffectDate()
        update_tool.GetBat()
    elif "t10" in MEngine.Args:
        EffectRunner()
    elif "t4" in MEngine.Args:
        EffectRunnerName(4)
    else:
        EffectRunnerName(158)
        
    
