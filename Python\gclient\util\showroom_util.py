# -*- coding: utf-8 -*-
# author: chenjie01
from gclient.framework.util.async_util import AsyncTick
import math
import copy
import MEngine
import MS<PERSON><PERSON>oom
import MUI
import MConfig
import MType
import Timer
import MObject
from gclient.util.debug_log_util import SomePreset, print_s
import switches

from functools import partial
from gclient import cconst
from gclient.data import unit_model_data, lobby_item_data
from gclient.framework.models.simple_model import SimpleModel
from gclient.framework.util import <PERSON>H<PERSON><PERSON>, events
from gclient.framework.util.resource_loader import ResourceLoader
from gshare import consts
from gshare.utils import SingletonMeta
from gclient.framework.util.story_tick import StoryTick
from gclient.data import performances_data

from gclient.framework.util.render_options.hall_render_options import hall_render_options
from gclient.framework.util.render_options.fixed_render_options import fixed_render_options
from gclient.framework.util.render_options.fixed_render_options import fixed_base_env_options
from gclient.framework.util.render_options.fixed_render_options import fixed_black_board_value
from gclient.framework.util.render_options.fixed_render_options import fixed_showroom_options

from gclient.framework.ui.widgets.ui_model_touch_panel import ModelTouchPanel
from gclient.gamesystem.models.character_display_doll_model_manager import CharacterDisplayDollModelManager
from gclient.gamesystem.uisettingnew.setting_graphics_audio_window import ConvertHFOVToVFov


SHOWROOM_UNSUPPORT_OPTIONS = [
    "HarmTextBeforeUI",
    "EnablePathTracer",
    "GTAOFalloffStartRatio",
    "EnablePathTracerVisibility",
    "GTAOFalloffEnd",
    "GTAOThicknessBlend",
    "ContactHardenRange",
    "ContactHardenIntensity",
    "ContactHardenBiasRate",
    "HudContactShadowLength",
    "ScreenSpaceShadowSurfaceThickness",
    "EnableHiZOcclusion",
    "HiZOcclusionReadBackNextFrame",
    "HiZOcclusionUseHighPrecise",
    "HiZOcclusionBoundScale",
    "HiZOcclusionBoundExpansion",
    "EnableVirtualTexture",
]

def SetShowroomSingleRenderOption(show_name, key, value):
    global SHOWROOM_UNSUPPORT_OPTIONS
    if key in SHOWROOM_UNSUPPORT_OPTIONS:
        return
    try:
        MShowRoom.SetRenderOption(show_name, key, value)
    except Exception as e:
        print(f"SetShowroomRenderOption Failed: opt:{key} value:{value} error:{e}")

def ApplyShowroomDefaultRenderOptions(show_name):
    print_s("OnApplyShowroomRenderOptions: 开始应用渲染选项", SomePreset.white_fg_yellow_bg)
    options = copy.copy(performances_data.data.get('L_MainHall', {}))

    options.update(hall_render_options)
    options.update(fixed_render_options)
    options.update(fixed_base_env_options)
    options.update(fixed_black_board_value)
    options.update(fixed_showroom_options)

    if MConfig.Driver == 'es3':     # ES3 只跑 forward
        options['EnableDeferredRendering'] = 0

    for key, value in options.items():
        SetShowroomSingleRenderOption(show_name, key, value)

class ShowRoomManager(object, metaclass=SingletonMeta):
    def __init__(self):
        super(ShowRoomManager, self).__init__()
        self.showroom_dict = {}
        self.showroom_in_creating = {}
        self.delay_del_showroom_list = {}
        self.showroom_creating_timeout_timer = {}

    def CreateShowRoom(self, room_name, box, cam_from, cam_to, fov, width, height, planar_shadow=False):
        '''
        创建不带背景的showroom
        :param room_name: 名字
        :param box: 包围框
        :param cam_from: 相机位置
        :param cam_to: 相机lookat目标位置
        :param fov: fov
        :param width: 窗口宽
        :param height: 窗口高
        :param planar_shadow: 是否开启平面阴影
        :return:
        '''
        if room_name in self.showroom_dict:
            return
        world = MEngine.GetGameplay().Scenario.MakeShowRoom(room_name, box, cam_from, cam_to, fov, width, height,
                                                            planar_shadow)
        if world:
            self.showroom_dict[room_name] = world
            return world

    def CreateShowRoomWithWorld(self, room_name, world_name, cam_from, cam_to, fov, planar_shadow=False,
                                background_enable=False, callback=None):
        '''
        创建带world的showroom
        :param world_name: world名字
        :param background_enable: 是否画天空盒
        :param callback:
        :return:
        '''
        self.CancelDelayDelShowRoom(room_name)
        print(f" try CreateShowRoomWithWorld: {room_name}")
        if room_name in self.showroom_in_creating:
            print_s(f"====> WARNING: {room_name} already in creating...", SomePreset.white_fg_red_bg)
            return

        if room_name in self.showroom_dict:
            print_s(f"====> WARNING: {room_name} already in showroom_dict...", SomePreset.white_fg_red_bg)
            # 补一个回调调用
            world = self.showroom_dict[room_name]
            if callback:
                callback(world)
            return
        world_name_with_suffix = 'Worlds/%s.iworld' % world_name
        self.showroom_in_creating[room_name] = 1
        if room_name in self.showroom_creating_timeout_timer and self.showroom_creating_timeout_timer[room_name]:
            self.showroom_creating_timeout_timer[room_name].cancel()
            self.showroom_creating_timeout_timer[room_name] = None
        self.showroom_creating_timeout_timer[room_name] = Timer.addTimer(5, lambda: self._OnCreatingTimeout(room_name))
        MEngine.GetGameplay().Scenario.MakeShowRoom2(room_name, world_name_with_suffix, cam_from, cam_to, fov,
                                                     planar_shadow, background_enable,
                                                     partial(self._OnLoadRoomWorld, room_name, callback))

    def _OnCreatingTimeout(self, room_name):
        if room_name not in self.showroom_in_creating:
            # 已经创建成功
            return
        if room_name in self.showroom_creating_timeout_timer and self.showroom_creating_timeout_timer[room_name]:
            self.showroom_creating_timeout_timer[room_name].cancel()
        self.showroom_creating_timeout_timer[room_name] = None
        del self.showroom_in_creating[room_name]
        print_s(f"====> WARNING: {room_name} creating timeout...", SomePreset.white_fg_red_bg)
        self.DelayDelShowRoom(room_name)

    def _OnLoadRoomWorld(self, room_name, callback, world):
        print(f"=====> _OnLoadRoomWorld, worldname:{room_name}, world:{world}")
        self.showroom_dict[room_name] = world
        if callback:
            callback(world)

        if room_name in self.showroom_in_creating:
            del self.showroom_in_creating[room_name]

        if room_name in self.showroom_creating_timeout_timer and self.showroom_creating_timeout_timer[room_name]:
            self.showroom_creating_timeout_timer[room_name].cancel()
            del self.showroom_creating_timeout_timer[room_name]

    def EnterShowRoom(self, room_name, entity):
        world = self.showroom_dict.get(room_name)
        if not world:
            return
        entity.EnterArea(world.DefaultLevel.RootArea)

    def DelShowRoom(self, room_name):
        if room_name not in self.showroom_dict:
            return
        MEngine.GetGameplay().Scenario.DeleteShowRoom(room_name)
        del self.showroom_dict[room_name]
        if room_name in self.delay_del_showroom_list:
            del self.delay_del_showroom_list[room_name]
        print(f"-----------> finally del showroom: {room_name}")

    def DelayDelShowRoomImpl(self, room_name):
        if room_name not in self.delay_del_showroom_list:
            print(f"----------->  showroom {room_name} not in showroom_dict, cancel delay del")
            return
        print(f"----------->  showroom {room_name} in showroom_dict, del showroom")
        self.DelShowRoom(room_name)
    
    def DelayDelShowRoom(self, room_name):
        timerId = Timer.addTimer(1, lambda: self.DelayDelShowRoomImpl(room_name))
        self.delay_del_showroom_list[room_name] = timerId
        print(f"-----------> add delay del showroom: {room_name}, timerId: {timerId}")
    
    def CancelDelayDelShowRoom(self, room_name):
        if room_name in self.delay_del_showroom_list:
            del self.delay_del_showroom_list[room_name]
            print(f"-----------> cancel delay del showroom: {room_name}")

    def GetShowRoom(self, room_name):
        return self.showroom_dict.get(room_name)

    def SetResolutionScale(self, room_name, scale):
        if room_name not in self.showroom_dict:
            return
        MShowRoom.UpdateResolutionScale(room_name, scale)

    def SetCameraPos(self, room_name, frm, to, fov):
        if room_name not in self.showroom_dict:
            return
        MShowRoom.UpdateView(room_name, frm, to, fov)



class RotateComp(object):
    def __init__(self, model):
        self.model = model
        self.rotate_recover_time = 0.0
        self.rotate_recover_timer = None
        self.rotate_recover_interval = 0.2
        self.rotate_recover_duration = 2.0

        self.rotate_x_axis = MType.Vector3(0, 0, 1)
        self.rotate_y_axis = MType.Vector3(0, 1, 0)

        self.zoom_recover_timer = None
        self.zoom_recover_interval = 1.0

        self.InitControlTrans()

    def InitControlTrans(self):
        # 旋转矩阵 世界
        self.transformRotate = MType.Matrix4x3()
        # 原点矩阵 世界
        self.transformMaster = MType.Matrix4x3()
        # 原点模型空间矩阵
        model_transform_inverse = self.model.Transform.inverse
        self.transformMasterModel = MType.Matrix4x3(
            model_transform_inverse.transform_v(self.transformMaster.x_axis),
            model_transform_inverse.transform_v(self.transformMaster.y_axis),
            model_transform_inverse.transform_v(self.transformMaster.z_axis),
            model_transform_inverse.transform_p(self.transformMaster.translation)
        )
        self.control_trans_inited = True

    def RotateModel(self, x, y):
        # 世界空间rotate 只有两个自由度 默认yaw和pitch
        # 始终以屏幕横纵轴为旋转轴
        if not self.control_trans_inited:
            return
        rotate_x_axis_sin = self.rotate_x_axis * math.sin(y)
        rotate_y_axis_sin = self.rotate_y_axis * math.sin(x)
        rotate_rotation = self.transformRotate.rotation
        rotation = MType.Quat(rotate_x_axis_sin.x, rotate_x_axis_sin.y, rotate_x_axis_sin.z, math.cos(y)) * \
                              MType.Quat(rotate_y_axis_sin.x, rotate_y_axis_sin.y, rotate_y_axis_sin.z, math.cos(x)) * \
                              MType.Quat(rotate_rotation.x, rotate_rotation.y, rotate_rotation.z, rotate_rotation.w)
        self.transformRotate.rotation = MType.Vector4(rotation.x, rotation.y, rotation.z, rotation.w)
        self.model.Transform = self.transformMasterModel.inverse * self.transformRotate * self.transformMaster

    def StartRotateRecoverTimer(self):
        if not self.control_trans_inited:
            return
        self.ClearRotateRecover()
        self.rotate_recover_timer = Timer.addTimer(self.rotate_recover_interval, self.StartRotateRecover)

    def ClearRotateRecover(self):
        self.EndRotateRecover()
        self.rotate_recover_time = 0
        self.rotate_recover_timer and self.rotate_recover_timer.cancel()

    def StartRotateRecover(self):
        if not self.control_trans_inited:
            return
        StoryTick().Add(self.RotateRecover)

    def EndRotateRecover(self):
        StoryTick().Remove(self.RotateRecover)

    def RotateRecover(self, dt):
        if not self.control_trans_inited:
            return
        current_rotation = self.transformRotate.rotation
        current_quat = MType.Quat(current_rotation.x, current_rotation.y, current_rotation.z, current_rotation.w)
        target_quat = MType.Quat()
        new_quat = current_quat.slerp(target_quat, self.rotate_recover_time / self.rotate_recover_duration)

        self.transformRotate.rotation = MType.Vector4(new_quat.x, new_quat.y, new_quat.z, new_quat.w)
        self.model.Transform = self.transformMasterModel.inverse * self.transformRotate * self.transformMaster

        self.rotate_recover_time += dt

        if abs(new_quat.x - target_quat.x) <= 0.001 and \
                abs(new_quat.y - target_quat.y) <= 0.001 and \
                abs(new_quat.z - target_quat.z) <= 0.001 and \
                abs(new_quat.w - target_quat.w) <= 0.001:
            self.EndRotateRecover()

    def RotateRecoverImmediately(self):
        self.ClearRotateRecover()
        model_base_trans = MType.Matrix4x3()
        self.model.Transform = model_base_trans

    def Clear(self):
        self.ClearRotateRecover()


class ShowRoomTouchPanel(ModelTouchPanel):
    def __init__(self, widget, owner):
        super(ShowRoomTouchPanel, self).__init__(widget)
        self.owner = owner
        self.mouse_last_pos = None

    def OnModelTouchBegin(self, touch, event):
        super(ShowRoomTouchPanel, self).OnModelTouchBegin(touch, event)
        if not self.owner.rotate_comp:
            return
        self.mouse_last_pos = None
        self.owner.rotate_comp.EndRotateRecover()

    def OnModelTouchMove(self, touch, event):
        super(ShowRoomTouchPanel, self).OnModelTouchMove(touch, event)
        x, y = self.cur_touch_pos.x, -self.cur_touch_pos.y
        y = MUI.GetScreenHeight() - y
        if not self.mouse_last_pos or not self.owner.rotate_comp:
            self.mouse_last_pos = (x, y)
            return
        diffx = x - self.mouse_last_pos[0]
        diffy = y - self.mouse_last_pos[1]
        self.mouse_last_pos = (x, y)
        self.owner.rotate_comp.RotateModel(diffx * 0.005, 0)

    def OnModelTouchEnd(self, touch, event):
        super(ShowRoomTouchPanel, self).OnModelTouchEnd(touch, event)
        # 每次旋转操作后 0.2s内如果无操作 恢复
        if self.owner.rotate_comp:
            self.owner.rotate_comp.StartRotateRecoverTimer()


class ShowRoomUtil(object):
    def __init__(self, can_rotate=False):
        self.can_rotate = can_rotate
        self.show_model = None
        self.showroom_name = None
        self.offset_transform = None
        self.rotate_comp = None
        self.rotate_panel = None
        self.show_model_list = []
        self.offset_transform_list = []

    def CreateShowRoomWithImageView(self, showroom_name, img, fov=60, offset_trans=None):
        box = MType.Box()
        box.union_p(MType.Vector3(1000, 1000, 1000))
        box.union_p(MType.Vector3(-1000, -1000, -1000))
        ShowRoomManager().CreateShowRoom(showroom_name, box, MType.Vector3(0, 1, 2), MType.Vector3(0, 1, 0),
                                         fov, 1080, 1920)
        ShowRoomManager().SetResolutionScale(showroom_name, 1.0)
        img.setShowRoomName(showroom_name)
        self.showroom_name = showroom_name
        self.offset_transform = offset_trans
        if self.can_rotate:
            self.rotate_panel = ShowRoomTouchPanel(img, self)

    def CreateShowRoom(self, showroom_name, parent_img, fov=60, offset_trans=None):
        box = MType.Box()
        box.union_p(MType.Vector3(1000, 1000, 1000))
        box.union_p(MType.Vector3(-1000, -1000, -1000))
        ShowRoomManager().CreateShowRoom(showroom_name, box, MType.Vector3(0, 1, 2), MType.Vector3(0, 1, 0),
                                         fov, 1080, 1920)
        ShowRoomManager().SetResolutionScale(showroom_name, 1.0)
        parent_img.widget.setShowRoomName(showroom_name)
        self.showroom_name = showroom_name
        self.offset_transform = offset_trans
        if self.can_rotate:
            self.rotate_panel = ShowRoomTouchPanel(parent_img.widget, self)

    def ResetCameraPos(self, frm, to, fov=60):
        ShowRoomManager().SetCameraPos(self.showroom_name, MType.Vector3(*frm), MType.Vector3(*to), fov)

    def SetTransformOffset(self, offset_trans):
        self.offset_transform = offset_trans

    def RefreshShowroomModel(self, unit_id=1):
        # unit_id = 1
        mdata = unit_model_data.data[unit_id]
        resources = mdata.get("models", ())
        if not self.show_model:
            self.show_model = MHelper.CreateModel(
                done=self.OnCreateModel,
                resource_names=resources,
                skeleton_file=SimpleModel.GetMotionSkeletonFile(mdata.get("skeleton", "")),
                disablemip=True,
                graph_file=None
            )
        else:
            ResourceLoader.ChangePrimitives(self.show_model, resources, use_ready_to_appear=True,
                                            done=self.OnReloadModel)

    def OnReloadModel(self):
        unit_id = 1
        mdata = unit_model_data.data[unit_id]
        MHelper.LoadSkeletonAndGraph(
            entity=self.show_model, skeleton_file=SimpleModel.GetMotionSkeletonFile(mdata.get("skeleton", "")),
            graph_file=None)
        self._PlayModelAnimation()

    def OnCreateModel(self, entity):
        if self.offset_transform is not None:
            new_trans = MType.Matrix4x3()
            new_trans.translation = self.offset_transform.translation
            entity.Transform = new_trans
        world = ShowRoomManager().GetShowRoom(self.showroom_name)
        if world:
            entity.EnterArea(world.DefaultLevel.RootArea)
        self._PlayModelAnimation()
        if self.can_rotate:
            self.rotate_comp = RotateComp(self.show_model)

    def AddShowroomModel(self, unit_id=1, offset_trans=None):
        mdata = unit_model_data.data[unit_id]
        resources = mdata.get("models", ())
        graph = mdata.get('basic_graph')
        show_model = MHelper.CreateModel(
            done=self.OnCreateModelInList,
            resource_names=resources,
            skeleton_file=SimpleModel.GetMotionSkeletonFile(mdata.get("skeleton", "")),
            disablemip=True,
            graph_file=graph
        )
        self.show_model_list.append(show_model)
        self.offset_transform_list.append(offset_trans)

    def GetModelComponentList(self, arm_clothes):
        model_id = self.GetModelUnitID(arm_clothes)
        return unit_model_data.data[model_id]['models']

    def GetBasicGraph(self):
        return 'Lobby/Lobby_Idle_Pose.graph'

    def _GetDefaultSuitItemId(self):
        # 默认hero套装，一定进包
        # hero_id = lobby_item_data.data.get(cconst.DEFAULT_HALL_SUIT_ID, {}).get('hero_id', 100)
        # 直接用小白人 for dlc
        return consts.DEFAULT_SUIT

    def GetModelUnitID(self, arm_clothes):
        suit_item_id = arm_clothes.get(consts.ItemSubType.Suit, cconst.DEFAULT_HALL_SUIT_ID) if arm_clothes else self._GetDefaultSuitItemId()
        return lobby_item_data.data[suit_item_id]['model_id']

    def GetModelSkeleton(self, arm_clothes):
        return unit_model_data.data[self.GetModelUnitID(arm_clothes)].get('skeleton')

    def AddShowroomModelTest(self, avatar, offset_trans=None):
        model_data = {'id': self.GetModelUnitID(avatar.arm_clothes)}
        graph_file = self.GetBasicGraph()
        if graph_file:
            model_data['basic_graph'] = graph_file
        model_data['models'] = self.GetModelComponentList(avatar.arm_clothes)
        model_data['skeleton'] = self.GetModelSkeleton(avatar.arm_clothes)
        show_model = MHelper.CreateModel(
            done=self.OnCreateModelInList,
            resource_names=model_data.get("models", ()),
            skeleton_file=SimpleModel.GetMotionSkeletonFile(model_data.get("skeleton", "")),
            disablemip=True,
            graph_file=model_data.get('basic_graph')
        )
        self.show_model_list.append(show_model)
        self.offset_transform_list.append(offset_trans)

    def OnCreateModelInList(self, entity):
        index = self.show_model_list.index(entity)
        if len(self.offset_transform_list) > index:
            new_trans = MType.Matrix4x3()
            new_trans.translation = self.offset_transform_list[index].translation
            entity.Transform = new_trans
        world = ShowRoomManager().GetShowRoom(self.showroom_name)
        if world:
            entity.EnterArea(world.DefaultLevel.RootArea)
        # self._PlayModelAnimation()
        # if self.can_rotate:
        #     self.rotate_comp = RotateComp(self.show_model)


    def _PlayModelAnimation(self):
        entity = self.show_model
        anim_name = '12094m_idle'
        s = entity.Skeleton
        if not s or not s.HasAnimation(anim_name):
            return
        s.StopAction(anim_name, True)
        s.ResetTPos()
        s.PlayAction(anim_name, False, True, 1.0)

    def Destroy(self):
        ShowRoomManager().DelShowRoom(self.showroom_name)
        if self.rotate_comp:
            self.rotate_comp.Clear()
            self.rotate_comp = None


class ChooseHeroShowRoomUtil(ShowRoomUtil):
    def __init__(self, can_rotate=False):
        self.can_rotate = can_rotate
        self.showroom_name = None
        self.rotate_panel = None
        self.rotate_comp = None
        self.parent_img = None
        self.on_create_showroom_callback = None

    def CreateShowRoom(self, showroom_name, parent_img, fov=60, offset_trans=None, on_create_showroom_callback=None):
        self.parent_img = parent_img
        self.showroom_name = showroom_name
        box = MType.Box()
        box.union_p(MType.Vector3(1000, 1000, 1000))
        box.union_p(MType.Vector3(-1000, -1000, -1000))

        self.on_create_showroom_callback = on_create_showroom_callback
        ShowRoomManager().CreateShowRoom(
            showroom_name,
            box,
            MType.Vector3(0, 0, 0),
            MType.Vector3(0, 0, 1),
            fov,
            1080,
            1920,
        )

        # ShowRoomManager().SetResolutionScale(self.showroom_name, 1.0)
        # self.parent_img.widget.setShowRoomName(self.showroom_name)
        world = ShowRoomManager().GetShowRoom(self.showroom_name)
        self.OnLoadRoomWorld(world)
        if self.can_rotate:
            self.rotate_panel = ShowRoomTouchPanel(parent_img.widget, self)

    def CreateShowRoomWithWorld(self, showroom_name, parent_img, fov=60, offset_trans=None, on_create_showroom_callback=None):
        print("----------------------------------------------------------------------------------------------------")
        print("--------------------------------> 创建shoowroom with world <----------------------------------------")
        print("----------------------------------------------------------------------------------------------------")
        self.parent_img = parent_img
        self.showroom_name = showroom_name
        box = MType.Box()
        box.union_p(MType.Vector3(1000, 1000, 1000))
        box.union_p(MType.Vector3(-1000, -1000, -1000))
        world_name = 'MatchIntro'
        # world_name = 'L_MainHall'
        self.on_create_showroom_callback = on_create_showroom_callback
        ShowRoomManager().CreateShowRoomWithWorld(
            showroom_name,
            world_name,
            MType.Vector3(0, 0, 0),
            MType.Vector3(0, 0, 1),
            fov,
            False,
            True,
            callback=self.OnLoadRoomWorld
        )
        if self.parent_img:
            if ShowRoomManager().GetShowRoom(self.showroom_name):
                self.parent_img.widget.setShowRoomName(self.showroom_name)
            if self.can_rotate:
                self.rotate_panel = ShowRoomTouchPanel(parent_img.widget, self)   

    def OnLoadRoomWorld(self, world):
        world.PhysicsSpace.EnablePhysicsLoading = False
        print(f"OnLoadRoomWorld: {world}")
        if self.parent_img:
            self.SetImgView(self.parent_img, self.showroom_name)
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableBloom', 1)  # 开启Bloom效果
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableTSAA', 1)  
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableMotionBlur', 1)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnabledHdrLighting', 1)  # 开启HDR效果

        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableBloom', 1)  # 开启Bloom效果
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableAdaption', 1)  # 开启自适应效果
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableDeferredRendering', 1)  # 开启DeferredRendering
        MShowRoom.SetRenderOption(self.showroom_name, "EnableVirtualTexture", 0)  # 关闭虚拟纹理
        self.OnApplyShowroomRenderOptions(self.showroom_name)   
        if self.parent_img:
            # 这时还没有space，没发创建entity
            self.EnterHeroDoll(self.showroom_name)

        if self.on_create_showroom_callback:
            self.on_create_showroom_callback()
        MShowRoom.SetAutoReleaseRenderTarget(self.showroom_name, True)
    
    def OnApplyShowroomRenderOptions(self, show_name):
        print_s("OnApplyShowroomRenderOptions: 开始应用渲染选项", SomePreset.white_fg_yellow_bg)
        options = copy.copy(performances_data.data.get('L_MainHall', {}))

        options.update(hall_render_options)
        options.update(fixed_render_options)
        options.update(fixed_base_env_options)
        options.update(fixed_black_board_value)

        if MConfig.Driver == 'es3':     # ES3 只跑 forward
            options['EnableDeferredRendering'] = 0

        for key, value in options.items():
            SetShowroomSingleRenderOption(show_name, key, value)

        camera = MShowRoom.GetCamera(self.showroom_name)
        if camera:
            camera.FieldOfView = ConvertHFOVToVFov(44)
            pass
 
        MShowRoom.SetRenderOption(self.showroom_name, 'EnableAmbientOcclusion', 1)  
        MShowRoom.SetRenderOption(self.showroom_name, 'CastShadowPointLights', 100)  
        MShowRoom.SetRenderOption(self.showroom_name, 'CastShadowSpotLights', 100)  

        MShowRoom.SetRenderOption(self.showroom_name, 'EnableSunLightShadow', 1)  
        MShowRoom.SetRenderOption(self.showroom_name, 'ShadowMapSize', 4096)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCSMDistMultiplier', 1.2)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCSMCascadeCountOffset', 0)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCsmNearestFilterMethod', 'StochasticDPCF')  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCsmMaxStochasticKernel', 30)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCsmMaxStochasticSample1D', 8)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnablePointLightShadow', 1)  
        MShowRoom.SetRenderOption(self.showroom_name, 'ShadowQuality', 'High')
        MShowRoom.SetRenderOption(self.showroom_name, 'AOMethod', 'GTAO')

        MShowRoom.SetRenderOption(self.showroom_name, 'PointLitShadowMapSize', 4096)             
        # MShowRoom.SetRenderOption(self.showroom_name, 'SpotLitShadowMapSize', 4096)  
    
    def SetShowroomRenderOption(self, show_name, key, value):
        global SHOWROOM_UNSUPPORT_OPTIONS
        if key in SHOWROOM_UNSUPPORT_OPTIONS:
            return
        try:
            MShowRoom.SetRenderOption(show_name, key, value)
        except Exception as e:
            print(f"SetShowroomRenderOption Failed: opt:{key} value:{value} error:{e}")
        
    
    def SetImgView(self, img, showroom_name):
        ShowRoomManager().SetResolutionScale(showroom_name, 1)
        if hasattr(img, 'widget'):
            img.widget.setShowRoomName(showroom_name)
        else:
            img.setShowRoomName(showroom_name)

    def EnterHeroDoll(self, showroom_name):
        main_npc = CharacterDisplayDollModelManager().main_npc
        if not main_npc:
            CharacterDisplayDollModelManager().OnEnter()
            main_npc = CharacterDisplayDollModelManager().main_npc

        world = ShowRoomManager().GetShowRoom(showroom_name)
        CharacterDisplayDollModelManager().OnCharacterDisplayEnterShowroomWorld(showroom_name, world)

    def Destroy(self):
        ShowRoomManager().DelShowRoom(self.showroom_name)
        if self.rotate_comp:
            self.rotate_comp.Clear()
            self.rotate_comp = None
        
    def CreateLightTestEntity(self, showroom_name):
        world = ShowRoomManager().GetShowRoom(showroom_name)
        if not world:
            pass
        entity = MObject.CreateObject('IEntity')
        entity.IsAnimated = True
        entity.IsMovable = True
        entity.Light = MObject.CreateObject("RectLightComponent")  # or "SpotLightComponent"
        entity.Light.IsBake = False
        entity.Skeleton = MObject.CreateObject('ActorComponent')
        entity.Skeleton.SetEnableControlLight(True)  # default value is True
        # Graph\Lobby\LightCtrl\LightCtrlTest.graph
        graph_file = 'Graph/Lobby/LightCtrl/LightCtrlTest.graph'
        entity.Skeleton.LoadSkeletonAndGraph('', graph_file)
        trans = MType.Matrix4x3()
        trans.translation = MType.Vector3(0, 1, 0)
        entity.Transform = trans
        entity.SetName("LightTestEntity")
        genv.TestLightEntity = entity
        if not world:
            world = MEngine.GetGameplay().Scenario.ActiveWorld
            level = world.DefaultLevel
            print(f"------------> CreateLightTestEntity: world:{world}")
            print(f"------------> CreateLightTestEntity: level:{level}")
            print(f"------------> CreateLightTestEntity: level.RootArea:{level.RootArea}")
            entity.EnterArea(level.RootArea)
        else:
            level = world.DefaultLevel
            print(f"------------> CreateLightTestEntity: world:{world}")
            print(f"------------> CreateLightTestEntity: level:{level}")
            print(f"------------> CreateLightTestEntity: level.RootArea:{level.RootArea}")
            entity.EnterArea(level.RootArea)

    def EnterHeroDollV2(self, world):
        main_npc = CharacterDisplayDollModelManager().main_npc
        if not main_npc:
            CharacterDisplayDollModelManager().OnEnter()
            main_npc = CharacterDisplayDollModelManager().main_npc

        CharacterDisplayDollModelManager().OnCharacterDisplayEnterShowroomWorld("", world)

    @AsyncTick
    def WarmupALLCharacterDisplayModel(self, warmup_callback=None):
        yield 1.5
        # 预加载角色展示模型
        print("--------------------------------------------------------------------")
        print("----------------------> 预加载所有角色展示模型 <----------------------")
        print("--------------------------------------------------------------------")
        for _ in range(1000):
            yield 0.1
            if not genv.space:
                continue
            if hasattr(genv.space, 'entity_creating_cache'):
                # print("space hasattr entity_creating_cache")
                continue
            break

        if switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
            showroom_name = "ChooseHeroShowRoomWithWorld"
            world = ShowRoomManager().GetShowRoom(showroom_name)
            CharacterDisplayDollModelManager().OnShowRoomWramup(world)
            self.EnterHeroDoll(showroom_name)
        else:
            world = genv.space.world
            CharacterDisplayDollModelManager().OnShowRoomWramup(world)
            self.EnterHeroDollV2(world)
        
        if warmup_callback:
            warmup_callback()

    @AsyncTick
    def WarmupCharacterDisplayScene(self, warmup_callback=None):
        yield 0.4
        print("-----------------------------------------------------------------")
        print("----------------------> 预加载角色展示场景 <----------------------")
        print("-----------------------------------------------------------------")
        if switches.USE_SHOWROOM_FOR_CHARACTER_DISPLAY:
            # 预加载展示场景 - showroom
            showroom_name = "ChooseHeroShowRoomWithWorld"
            ShowRoomManager().DelShowRoom(showroom_name)
            ChooseHeroShowRoomUtil().CreateShowRoomWithWorld(showroom_name, None, on_create_showroom_callback=lambda: self.WarmupALLCharacterDisplayModel(warmup_callback))
        else:
            # 预加载展示场景 - 场景level
            genv.space.TryLoadWorldLevels(on_load_level_callback=lambda: self.WarmupALLCharacterDisplayModel(warmup_callback))


class HallCareerShowRoomUtil(ShowRoomUtil):
    def __init__(self, can_rotate=False):
        self.can_rotate = can_rotate
        self.showroom_name = None
        self.rotate_panel = None
        self.rotate_comp = None
        self.parent_img = None
        self.on_create_showroom_callback = None

    def CreateShowRoom(self, showroom_name, parent_img, fov=60, offset_trans=None, on_create_showroom_callback=None):
        self.parent_img = parent_img
        self.showroom_name = showroom_name
        box = MType.Box()
        box.union_p(MType.Vector3(1000, 1000, 1000))
        box.union_p(MType.Vector3(-1000, -1000, -1000))

        self.on_create_showroom_callback = on_create_showroom_callback
        ShowRoomManager().CreateShowRoom(
            showroom_name,
            box,
            MType.Vector3(0, 0, 0),
            MType.Vector3(0, 0, -1),
            fov,
            1080,
            1920,
        )

        # ShowRoomManager().SetResolutionScale(self.showroom_name, 1.0)
        # self.parent_img.widget.setShowRoomName(self.showroom_name)
        world = ShowRoomManager().GetShowRoom(self.showroom_name)
        self.OnLoadRoomWorld(world)
        if self.can_rotate:
            self.rotate_panel = ShowRoomTouchPanel(parent_img.widget, self)

    def OnLoadRoomWorld(self, world):
        print(f"OnLoadRoomWorld: {world}")
        if self.parent_img:
            self.SetImgView(self.parent_img, self.showroom_name)
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableBloom', 1)  # 开启Bloom效果
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableTSAA', 1)
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableMotionBlur', 1)
        MShowRoom.SetRenderOption(self.showroom_name, 'EnabledHdrLighting', 1)  # 开启HDR效果
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableAdaption', 1)  # 开启自适应效果
        # MShowRoom.SetRenderOption(self.showroom_name, 'EnableDeferredRendering', 1)  # 开启DeferredRendering
        # self.OnApplyShowroomRenderOptions(self.showroom_name)
        MShowRoom.UpdateSunColor(self.showroom_name, MType.Vector3(30, 30, 30))
        if self.parent_img:
            # 这时还没有space，没发创建entity
            self.EnterItem(self.showroom_name)

        if self.on_create_showroom_callback:
            self.on_create_showroom_callback()
        MShowRoom.SetAutoReleaseRenderTarget(self.showroom_name, True)

    def SetImgView(self, img, showroom_name):
        ShowRoomManager().SetResolutionScale(showroom_name, 1.0)
        if hasattr(img, 'widget'):
            img.widget.setShowRoomName(showroom_name)
        else:
            img.setShowRoomName(showroom_name)

    def EnterItem(self, showroom_name):
        world = ShowRoomManager().GetShowRoom(showroom_name)
        genv.messenger.Broadcast(events.ON_CAREER_SHOWROOM_LOADED, showroom_name, world)

class SplitScreenShowRoomUtil(ShowRoomUtil):
    def __init__(self):
        self.showroom_name = None
        self.parent_img = None
        self.on_create_showroom_callback = None

    def CreateShowRoomWithWorld(self, showroom_name, world_name, fov=60, offset_trans=None, on_create_showroom_callback=None):
        print("----------------------------------------------------------------------------------------------------")
        print("--------------------------------> 创建SplitScreenShowRoom <----------------------------------------")
        print("----------------------------------------------------------------------------------------------------")
        self.showroom_name = showroom_name
        box = MType.Box()
        box.union_p(MType.Vector3(1000, 1000, 1000))
        box.union_p(MType.Vector3(-1000, -1000, -1000))
        self.on_create_showroom_callback = on_create_showroom_callback
        ShowRoomManager().CreateShowRoomWithWorld(
            showroom_name,
            world_name,
            MType.Vector3(0, 0, 0),
            MType.Vector3(0, 0, 1),
            fov,
            False,
            True,
            callback=self.OnLoadRoomWorld
        )

    def OnLoadRoomWorld(self, world):
        print(f"OnLoadRoomWorld: {world}")
        world.PhysicsSpace.EnablePhysicsLoading = False
        MShowRoom.SetRenderOption(self.showroom_name, 'EnabledHdrLighting', 1)  # 开启HDR效果
        MShowRoom.SetRenderOption(self.showroom_name, "EnableVirtualTexture", 0)  # 关闭虚拟纹理
        self.OnApplyShowroomRenderOptions(self.showroom_name)   
        if self.on_create_showroom_callback:
            self.on_create_showroom_callback()
        MShowRoom.SetAutoReleaseRenderTarget(self.showroom_name, True)
    
    def OnApplyShowroomRenderOptions(self, show_name):
        print_s("OnApplyShowroomRenderOptions: 开始应用渲染选项", SomePreset.white_fg_yellow_bg)
        options = copy.copy(performances_data.data.get('L_MainHall', {}))

        options.update(hall_render_options)
        options.update(fixed_render_options)
        options.update(fixed_base_env_options)
        options.update(fixed_black_board_value)

        if MConfig.Driver == 'es3':     # ES3 只跑 forward
            options['EnableDeferredRendering'] = 0

        for key, value in options.items():
            SetShowroomSingleRenderOption(show_name, key, value)


        camera = MShowRoom.GetCamera(self.showroom_name)
        if camera:
            camera.FieldOfView = ConvertHFOVToVFov(44)
            pass
 
        MShowRoom.SetRenderOption(self.showroom_name, 'EnableAmbientOcclusion', 1)  
        MShowRoom.SetRenderOption(self.showroom_name, 'CastShadowPointLights', 100)  
        MShowRoom.SetRenderOption(self.showroom_name, 'CastShadowSpotLights', 100)  

        MShowRoom.SetRenderOption(self.showroom_name, 'EnableSunLightShadow', 1)  
        MShowRoom.SetRenderOption(self.showroom_name, 'ShadowMapSize', 4096)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCSMDistMultiplier', 1.2)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCSMCascadeCountOffset', 0)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCsmNearestFilterMethod', 'StochasticDPCF')  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCsmMaxStochasticKernel', 30)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnhancedCsmMaxStochasticSample1D', 8)  
        MShowRoom.SetRenderOption(self.showroom_name, 'EnablePointLightShadow', 1)  
        MShowRoom.SetRenderOption(self.showroom_name, 'ShadowQuality', 'High')
        MShowRoom.SetRenderOption(self.showroom_name, 'AOMethod', 'GTAO')

        MShowRoom.SetRenderOption(self.showroom_name, 'PointLitShadowMapSize', 4096)             
    
    def SetShowroomRenderOption(self, show_name, key, value):
        global SHOWROOM_UNSUPPORT_OPTIONS
        if key in SHOWROOM_UNSUPPORT_OPTIONS:
            return
        try:
            MShowRoom.SetRenderOption(show_name, key, value)
        except Exception as e:
            print(f"SetShowroomRenderOption Failed: opt:{key} value:{value} error:{e}")
    
    def SetImgView(self, img):
        ShowRoomManager().SetResolutionScale(self.showroom_name, 1)
        self.parent_img = img
        self.parent_img.setShowRoomName(self.showroom_name)

    def Destroy(self):
        ShowRoomManager().DelShowRoom(self.showroom_name)
        if self.rotate_comp:
            self.rotate_comp.Clear()
            self.rotate_comp = None
        
    def DelayDelShowRoom(self, showroom_name):
        ShowRoomManager().DelayDelShowRoom(showroom_name)
