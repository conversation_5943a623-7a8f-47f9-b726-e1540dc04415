# -*- coding: utf-8 -*-
# author: gzwangxuanjing

import MType


from gclient.util.cglobal_op import GlobalOp
from gclient.data import special_param_rank_data
from gclient import cconst


class TechStateComponent(object):
    """数据在19号表里控制优先级"""

    def __init_component__(self):
        self.prioridlist = []
        self.techstateid2param = {}
        self.outlined_color = (0.0, 0.0, 0.0)
        self.outlined_thickness_base = cconst.OUTLINE_BASE
        self.outlined_alpha = None
        self.is_outline_dynamic = True
        self.limit_dis_range = [cconst.OUTLINE_NEAR, cconst.OUTLINE_FAR]
        self.disable_outlined_reason = 0

    def AddTechState(self, id, custom_param=None):
        # 优先显示优先级最高的techstate
        proto = special_param_rank_data.data.get(id)
        if not proto:
            return
        prior = proto.get('rank', 1)
        if (prior, id) in self.prioridlist:
            return
        if custom_param:
            self.techstateid2param[id] = custom_param
        self.prioridlist.append((prior, id))
        self.prioridlist.sort(key=lambda d: d[0], reverse=True)
        self.SetTechState()

    def SetTechStateParam(self, tech_id, custom_param):
        proto = special_param_rank_data.data.get(tech_id)
        if not proto:
            return
        prior = proto.get('rank', 1)
        if (prior, tech_id) not in self.prioridlist:
            return
        self.techstateid2param[tech_id] = custom_param
        if tech_id == self.prioridlist[0][1]:
            self.SetTechState()

    def RemoveTechState(self, proto_id):
        proto = special_param_rank_data.data.get(proto_id)
        if not proto:
            return
        prior = proto.get('rank', 1)
        if (prior, proto_id) not in self.prioridlist:
            return
        self.prioridlist.remove((prior, proto_id))
        if not self.prioridlist:
            if self.model:
                last_state = self.model.TechState
                if last_state == cconst.TechState.TechState_Xray:
                    GlobalOp().Pop("Xray", id(self))
                self.ClearTechState()
                self.ClearTechParam()
            return
        self.prioridlist.sort(key=lambda d: d[0], reverse=True)
        self.SetTechState()

    def SetTechState(self):
        if not self.prioridlist:
            self.ClearTechParam()
            return
        model = self.model
        if not model:
            return
        _, cur_id = self.prioridlist[0]
        cur_proto = special_param_rank_data.data.get(cur_id)
        techstate = cur_proto.get('state_id')
        param = cur_proto.get('tech_param1')
        param2 = cur_proto.get('tech_param2')
        is_outline = cur_proto.get('is_outline', False)
        is_force_exclude_occlusion = cur_proto.get('is_force_exclude_occlusion', False)
        # 如果有自定义属性的需求，优先用自定义的
        if cur_id in self.techstateid2param:
            param = self.techstateid2param[cur_id]
        if techstate == cconst.TechState.TechState_Highlight:
            if param and param2:
                self.UseTechHighlight(param, param2)
            elif param2:
                self.UseTechHighlight(param2[:3], param2[-1])
            elif param:
                self.UseTechHighlight(param)
        elif techstate == cconst.TechState.TechState_Edgeline:
            if param2:
                self.UseTechEdgeLine(param2[:3], param2[-1])
        elif techstate == cconst.TechState.TechState_Xray:
            self.UseTechXray(param)
        elif techstate == cconst.TechState.TechState_Frozen:
            self.UseTechFrozen()
        elif techstate == cconst.TechState.TechState_DistanceEnemy:
            self.UseDistancedEnemyTechState(param, param2)
        elif techstate == cconst.TechState.TechState_HighLightXray:
            self.UseTechHighLightXray(param, param2)
        elif techstate == cconst.TechState.TechState_Normal:
            self.UseTechNormal()
        if is_outline:
            self.outlined_color, self.outlined_thickness_base = param2[:3], param2[-1]
            self.EnableOutlined(param2[:3], param2[-1])
        else:
            self.DisableOutlined()
        self.SetModelForceExcludeOcclusion(is_force_exclude_occlusion)
        

    def UseTechHighlight(self, color, frequency=0.0):
        # 请尽量使用AddTechState
        model = self.model
        if not model:
            return
        if not isinstance(color, MType.Vector3):
            color = MType.Vector3(*color)

        if isinstance(frequency, (float, int)):
            frequency = MType.Vector4(0, 0, 0, frequency)
        elif isinstance(frequency, (tuple, list)):
            frequency = MType.Vector4(*frequency)
        frequency.y = 1000.0
        model.TechState = 0
        model.TechParam = color
        model.TechParam2 = frequency

    def UseTechEdgeLine(self, color, thickness):
        # 请尽量使用AddTechState
        model = self.model
        if not model:
            return
        if not isinstance(color, MType.Vector3):
            color = MType.Vector3(*color)
        if not isinstance(thickness, MType.Vector4):
            if isinstance(thickness, float):
                thickness = MType.Vector4(0, 0, 0, thickness)
            else:
                thickness = MType.Vector4(*thickness)

        model.TechState = 9
        model.TechParam = color
        model.TechParam2 = thickness
    
    def UseTechFrozen(self):
        # 请尽量使用AddTechState
        model = self.model
        if not model:
            return
        model.TechState = 3
        self.model.TechParam = MType.Vector3(0, 0, 0)
        self.model.TechParam2 = MType.Vector4(0, 0, 0, 0)

    def UseTechXray(self, param=None):
        # 请尽量使用AddTechState
        print('UseTechXray', param)
        if not self.model:
            return
        if param is None:
            param = (0.9, 0.45, 0)
        self.model.TechState = cconst.TechState.TechState_Xray
        self.model.TechParam = MType.Vector3(*param)
        self.model.TechParam2 = MType.Vector4(0, 0, 0, 0)
        GlobalOp().Push("Xray", id(self))

    def UseTechHighLightXray(self, param, param2):
        model = self.model
        if not model:
            return
        model.TechState = cconst.TechState.TechState_Xray
        model.TechParam = MType.Vector3(*param)
        model.TechParam2 = MType.Vector4(*param2)
        self.ApplyCustomStencil(cconst.RENDER_STENCIL_DIY3)
        GlobalOp().Push("Xray", id(self))

    def UseTechNormal(self):
        model = self.model
        if not model:
            return
        model.TechState = cconst.TechState.TechState_Normal
    
    def SetModelForceExcludeOcclusion(self, flag):
        model = self.model
        if not model:
            return
        for primitive in self.model.Primitives:
            primitive.SetForceExcludeOcclusion(flag)

    def ClearTechState(self):
        self.model.TechState = 0

    def ClearTechParam(self):
        if self.model:
            self.model.TechParam = MType.Vector3(0, 0, 0)
            self.model.TechParam2 = MType.Vector4(0, 0, 0, 0)
        self.DisableOutlined()

    def UseDistancedEnemyTechState(self, param, param2):
        # 10号敌人远距离
        if self.model:
            # [DEBUG]
            from gclient.util.gmcmds.imgui_common import FresnelTechParam
            if FresnelTechParam.Fresnel_Param1 and FresnelTechParam.Fresnel_Param2:
                self.model.TechParam = MType.Vector3(*FresnelTechParam.Fresnel_Param1)
                self.model.TechParam2 = MType.Vector4(*FresnelTechParam.Fresnel_Param2)
                self.model.TechState = 0
                return
            # [DEBUG]

            self.model.TechParam = MType.Vector3(*param)
            self.model.TechParam2 = MType.Vector4(*param2)
            self.model.TechState = 0

    # region outlined
    def AddDisableOutlinedReason(self, reason):
        self.disable_outlined_reason |= 1 << reason
        self.DisableOutlined()

    def ClearDisableOutlinedReason(self):
        if not hasattr(self, 'disable_outlined_reason'):
            return
        if self.disable_outlined_reason:
            self.EnableOutlined(self.outlined_color, self.outlined_thickness_base)
        self.disable_outlined_reason = 0
        return

    def HaveDisableOutlinedReason(self, reason):
        return self.disable_outlined_reason & (1 << reason)

    def RemoveDisableOutlinedReason(self, reason):
        self.disable_outlined_reason &= ~(1 << reason)
        if self.disable_outlined_reason == 0:
            self.EnableOutlined(self.outlined_color, self.outlined_thickness_base)

    def EnableOutlined(self, color, thickness):
        if self.model:
            self.model.SetIsOutlined(True)
            prev_tech_param = self.model.TechParam
            prev_tech_param.x = thickness
            if self.outlined_alpha:
                prev_tech_param.x = self.outlined_alpha
            self.model.TechParam = prev_tech_param  # 引擎老哥说这个也要的x也要设置
            self.model.TechParam2 = MType.Vector4(*(tuple(color) + (thickness, )))

    def DisableOutlined(self):
        if self.isValid():
            self.model.SetIsOutlined(False)

    def SetOutlinedParam(self, color, thickness):
        if self.model:
            self.model.TechParam2 = MType.Vector4(*(tuple(color) + (thickness, )))

    def IsEnableOutlined(self):
        return self.model and self.model.IsOutlined

    def SetOutlinedAlpha(self, alpha):
        if self.model:
            self.model.TechParam = MType.Vector3(alpha, 0.0, 0.0)
            self.outlined_alpha = alpha

    def UpdateOutlined(self, dis, color=None, alpha=None):
        if self.disable_outlined_reason != 0:
            return
        ientity = self.model
        if not ientity:
            return
        color = color if color else self.outlined_color
        if self.is_outline_dynamic:
            near, far = self.limit_dis_range
            if dis > far:
                ientity.SetIsOutlined(False)
            else:
                ientity.SetIsOutlined(True)
                if dis < near:
                    thick = self.outlined_thickness_base
                else:
                    ratio = (far - dis) / (far - near)
                    thick = self.outlined_thickness_base * ratio
                    alpha = alpha and alpha * ratio
                ientity.TechParam2 = MType.Vector4(*(color + (thick, )))
        else:
            ientity.TechParam2 = MType.Vector4(*(color + (self.outlined_thickness_base, )))
        if alpha is not None:
            ientity.TechParam = MType.Vector3(alpha, 0.0, 0.0)
            self.outlined_alpha = alpha

    # endregion outlined