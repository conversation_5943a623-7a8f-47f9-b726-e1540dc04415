# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 17-战斗物品表.xlsx:物品数据表, sheetname:
_reload_all = True

data = {
    1: {
        'id': 1,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 100001,
        'item_description': '原装M4A1步枪',
        'icon': 735,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    3: {
        'id': 3,
        'name': '破片手雷',
        'item_type': 101,
        'item_sub_type': 11,
        'quality': 1,
        'item_ground_model_id': 536,
        'equip_id': 3,
        'item_description': '爆炸后产生范围伤害',
        'short_tips': '投掷物',
        'icon': 389,
        'icon_outline': 11462,
        'bag_max_stack_limit': 2,
        'ground_max_stack_limit': 2,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
        'ig_voice_id': (1082, ),
        'mark_voice_id': (10071, ),
    }, 
    4: {
        'id': 4,
        'name': '烟雾弹',
        'item_type': 101,
        'item_sub_type': 12,
        'quality': 1,
        'item_ground_model_id': 538,
        'equip_id': 4,
        'item_description': '爆炸后产生烟雾，遮挡视野',
        'short_tips': '投掷物',
        'icon': 108,
        'icon_outline': 11463,
        'bag_max_stack_limit': 2,
        'ground_max_stack_limit': 2,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
        'ig_voice_id': (1083, ),
        'mark_voice_id': (10071, ),
    }, 
    5: {
        'id': 5,
        'name': '掩体生成器',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 3,
        'item_ground_model_id': 510,
        'equip_id': 5,
        'item_description': '使用后产生一个小型掩体',
        'icon': 109,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    7: {
        'id': 7,
        'name': '步枪子弹',
        'item_type': 102,
        'quality': 1,
        'item_ground_model_id': 541,
        'item_description': '步枪用弹药',
        'short_tips': '步枪弹药',
        'icon': 394,
        'bag_max_stack_limit': 100,
        'ground_max_stack_limit': 100,
        'mark_voice_id': (10079, ),
    }, 
    8: {
        'id': 8,
        'name': '冲锋枪子弹',
        'item_type': 102,
        'quality': 1,
        'item_ground_model_id': 543,
        'item_description': '冲锋枪用弹药',
        'short_tips': '冲锋枪弹药',
        'icon': 395,
        'bag_max_stack_limit': 100,
        'ground_max_stack_limit': 100,
        'mark_voice_id': (10078, ),
    }, 
    9: {
        'id': 9,
        'name': '闪光弹',
        'item_type': 101,
        'item_sub_type': 12,
        'quality': 1,
        'item_ground_model_id': 508,
        'equip_id': 7,
        'item_description': '爆炸后产生闪光，致盲敌人',
        'short_tips': '投掷物',
        'icon': 113,
        'icon_outline': 11461,
        'bag_max_stack_limit': 2,
        'ground_max_stack_limit': 2,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
            'cache': (2, ), 
        },
        'hold': {
            2: 'using', 
        },
        'ig_voice_id': (1084, ),
        'mark_voice_id': (10071, ),
    }, 
    10: {
        'id': 10,
        'name': '侦查手雷',
        'item_type': 101,
        'item_sub_type': 12,
        'quality': 1,
        'item_ground_model_id': 508,
        'equip_id': 8,
        'item_description': '使用后会扫描周围，暴露敌人所在位置',
        'short_tips': '投掷物',
        'icon': 107,
        'bag_max_stack_limit': 2,
        'ground_max_stack_limit': 2,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
            'cache': (2, ), 
        },
        'hold': {
            2: 'using', 
        },
    }, 
    13: {
        'id': 13,
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 100018,
        'item_description': '加装全息瞄准镜的M700',
        'icon': 744,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    14: {
        'id': 14,
        'name': '狙击枪子弹',
        'item_type': 102,
        'quality': 1,
        'item_ground_model_id': 542,
        'item_description': '狙击步枪用弹药',
        'short_tips': '狙击枪弹药',
        'icon': 396,
        'bag_max_stack_limit': 30,
        'ground_max_stack_limit': 30,
        'mark_voice_id': (10081, ),
    }, 
    15: {
        'id': 15,
        'name': 'UAV',
        'item_type': 101,
        'item_sub_type': 14,
        'quality': 4,
        'equip_id': 10,
        'item_description': '呼叫侦察机，在地图上标记敌对目标',
        'icon': 145,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'operate_mode': 1,
        'use_mode': 3,
        'empty': {
            1: 'hold', 
        },
    }, 
    16: {
        'id': 16,
        'name': '自救针',
        'item_type': 101,
        'item_sub_type': 16,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 507,
        'equip_id': 11,
        'item_description': '倒地后可复活自己',
        'short_tips': '倒地自救装置',
        'icon': 399,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'take_directly': True,
        'mark_voice_id': (10075, ),
    }, 
    17: {
        'id': 17,
        'name': '金币（小）',
        'item_type': 105,
        'quality': 1,
        'item_ground_model_id': 505,
        'item_description': '少量金币',
        'short_tips': '少量金币',
        'icon': 118,
        'add_br_money': 500,
        'mark_voice_id': (10077, ),
    }, 
    18: {
        'id': 18,
        'name': '金币（中）',
        'item_type': 105,
        'quality': 1,
        'item_ground_model_id': 511,
        'item_description': '一袋金币',
        'short_tips': '一袋金币',
        'icon': 119,
        'add_br_money': 1000,
        'mark_voice_id': (10077, ),
    }, 
    19: {
        'id': 19,
        'name': '金币（大）',
        'item_type': 105,
        'quality': 3,
        'item_ground_model_id': 512,
        'item_description': '大堆金币',
        'short_tips': '大堆金币',
        'icon': 120,
        'add_br_money': 2000,
        'mark_voice_id': (10077, ),
    }, 
    20: {
        'id': 20,
        'name': '燃烧瓶',
        'item_type': 101,
        'item_sub_type': 11,
        'quality': 1,
        'item_ground_model_id': 537,
        'equip_id': 12,
        'item_description': '爆炸后引燃周围，造成持续的烧伤',
        'short_tips': '燃烧瓶',
        'icon': 393,
        'icon_outline': 11460,
        'bag_max_stack_limit': 2,
        'ground_max_stack_limit': 2,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
        'mark_voice_id': (10071, ),
    }, 
    21: {
        'id': 21,
        'name': '战斗装备',
        'item_type': 104,
        'icon': 388,
    }, 
    22: {
        'id': 22,
        'name': '团队空投',
        'item_type': 104,
        'icon': 181,
        'mark_voice_id': (69, ),
    }, 
    23: {
        'id': 23,
        'name': '死亡掉落盒子',
        'item_type': 103,
        'item_ground_model_id': 577,
        'ground_max_stack_limit': 1,
    }, 
    24: {
        'id': 24,
        'name': '主动防御装置',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 3,
        'item_ground_model_id': 31,
        'equip_id': 13,
        'item_description': '主动防御装置，自动摧毁一定范围内的投掷物',
        'icon': 106,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'operate_mode': 1,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    27: {
        'id': 27,
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 8,
        'item_description': '加装2倍镜的M700',
        'icon': 351,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    28: {
        'id': 28,
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 9,
        'item_description': '加装全狙击镜的M700',
        'icon': 351,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    29: {
        'id': 29,
        'name': '随机天赋',
        'item_type': 110,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 513,
        'item_description': '随机获得一个普通天赋',
        'icon': 114,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'random_talent_type': (10, 11, 12, 13, ),
    }, 
    33: {
        'id': 33,
        'name': '任务代号-猎杀',
        'item_type': 107,
        'quality': 3,
        'item_ground_model_id': 561,
        'item_description': '拾取后接取猎杀任务',
        'icon': 400,
        'ground_max_stack_limit': 1,
        'br_task_id': 3,
        'mark_voice_id': (70, ),
    }, 
    34: {
        'id': 34,
        'name': '任务代号-占点',
        'item_type': 107,
        'quality': 3,
        'item_ground_model_id': 561,
        'item_description': '拾取后接取占点任务',
        'icon': 401,
        'ground_max_stack_limit': 1,
        'br_task_id': 1,
        'mark_voice_id': (70, ),
    }, 
    35: {
        'id': 35,
        'name': '任务代号-搜寻',
        'item_type': 107,
        'quality': 3,
        'item_ground_model_id': 561,
        'item_description': '拾取后接取搜寻任务',
        'icon': 402,
        'ground_max_stack_limit': 1,
        'br_task_id': 2,
        'mark_voice_id': (70, ),
    }, 
    36: {
        'id': 36,
        'name': '金条',
        'item_type': 105,
        'quality': 4,
        'item_ground_model_id': 512,
        'item_description': '大堆金币',
        'short_tips': '大堆金币',
        'icon': 116,
        'add_br_money': 4000,
        'mark_voice_id': (10077, ),
    }, 
    37: {
        'id': 37,
        'name': '心跳扫描器',
        'item_type': 101,
        'item_sub_type': 13,
        'quality': 3,
        'item_ground_model_id': 27,
        'equip_id': 14,
        'item_description': '使用后可扫描玩家前方50M内的敌人',
        'icon': 110,
        'use_mode': 5,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'using': {
            1: 'recycle', 
            3: 'recycle', 
        },
    }, 
    38: {
        'id': 38,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 100001,
        'item_description': '自定义枪械',
        'icon': 735,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    40: {
        'id': 40,
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 100018,
        'item_description': '自定义枪械',
        'icon': 351,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    41: {
        'id': 41,
        'name': 'Glock',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 566,
        'equip_id': 15,
        'gun_blueprint_id': 100004,
        'item_description': '自定义枪械',
        'icon': 736,
        'icon_outline': 1088,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    42: {
        'id': 42,
        'name': 'Glock',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 566,
        'equip_id': 15,
        'gun_blueprint_id': 100004,
        'item_description': '原装Glock手枪',
        'icon': 736,
        'icon_outline': 1088,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    43: {
        'id': 43,
        'name': '拳头',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'equip_id': 16,
        'item_description': '拳头',
        'icon': 149,
        'icon_outline': 149,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    44: {
        'id': 44,
        'name': '重生勋章',
        'item_type': 108,
        'item_sub_type': 1,
        'quality': 4,
        'item_description': '增加一次复活机会',
        'icon': 114,
        'bag_max_stack_limit': 5,
        'ground_max_stack_limit': 1,
        'rebirth_count': 1,
    }, 
    46: {
        'id': 46,
        'name': '绷带',
        'item_type': 101,
        'item_sub_type': 17,
        'item_ground_model_id': 523,
        'equip_id': 20,
        'item_description': '使用绷带，小幅度回复生命值',
        'short_tips': '医疗道具',
        'icon': 179,
        'bag_max_stack_limit': 8,
        'ground_max_stack_limit': 8,
        'operate_mode': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (1167, ),
        'mark_voice_id': (10069, ),
    }, 
    47: {
        'id': 47,
        'name': '医疗包',
        'item_type': 101,
        'item_sub_type': 17,
        'item_ground_model_id': 524,
        'ground_pitch': 90.0,
        'equip_id': 21,
        'item_description': '使用医疗包，大幅回复生命值',
        'short_tips': '医疗道具',
        'icon': 180,
        'bag_max_stack_limit': 2,
        'ground_max_stack_limit': 2,
        'operate_mode': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (1167, ),
        'mark_voice_id': (10069, ),
    }, 
    48: {
        'id': 48,
        'name': '战斗模组',
        'item_type': 109,
        'quality': 3,
        'item_ground_model_id': 513,
        'item_description': '解析后可提升战斗力',
        'icon': 10132,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'random_talent_type': (10, ),
    }, 
    49: {
        'id': 49,
        'name': '适应模组',
        'item_type': 109,
        'quality': 3,
        'item_ground_model_id': 513,
        'item_description': '可提升机动性和适应力',
        'icon': 10133,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'random_talent_type': (11, ),
    }, 
    50: {
        'id': 50,
        'name': '生存模组',
        'item_type': 109,
        'quality': 3,
        'item_ground_model_id': 513,
        'item_description': '可提升生存发育能力',
        'icon': 10134,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'random_talent_type': (12, ),
    }, 
    51: {
        'id': 51,
        'name': '侦查模组',
        'item_type': 109,
        'quality': 3,
        'item_ground_model_id': 513,
        'item_description': '可提升侦查与反侦察能力',
        'icon': 10135,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'random_talent_type': (13, ),
    }, 
    52: {
        'id': 52,
        'name': '空间裂隙',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 22,
        'item_description': '在目标点创造一个空间裂缝，造成大量爆炸伤害',
        'icon': 184,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    53: {
        'id': 53,
        'name': '卫星扫描',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 23,
        'item_description': '扫描周围200M范围内的敌人，并标记在小地图上',
        'icon': 183,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    54: {
        'id': 54,
        'name': '粒子护盾',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 24,
        'item_description': '在正前方展开一个阻挡伤害的粒子护盾',
        'icon': 10163,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    55: {
        'id': 55,
        'name': '全息诱饵',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 25,
        'item_description': '创造一个与自己一模一样的全息诱饵来干扰敌人',
        'icon': 187,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    56: {
        'id': 56,
        'name': '能量涌动',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 26,
        'icon': 186,
    }, 
    57: {
        'id': 57,
        'name': 'EMP手雷',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 27,
        'item_description': '爆炸产生电磁脉冲，对护盾和电子设备造成高额伤害',
        'icon': 10162,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
    }, 
    58: {
        'id': 58,
        'name': '导弹打击',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 28,
        'item_description': '锁定目标位置后，可呼叫远程导弹对目标点进行打击',
        'icon': 10161,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    59: {
        'id': 59,
        'name': '暴走',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 29,
        'item_description': '大幅提高移动速度，并降低冲刺时受到的伤害',
        'icon': 10160,
        'use_mode': 3,
        'empty': {
            1: 'hold', 
        },
    }, 
    60: {
        'id': 60,
        'name': '治疗无人机',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 30,
        'item_description': '为队友设置一个治疗无人机，可持续治疗所受伤害',
        'icon': 10164,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    61: {
        'id': 61,
        'name': '弹药箱',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 31,
        'item_description': '放置一个弹药箱，可为队友的当前武器填装高爆子弹',
        'icon': 189,
        'operate_mode': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    63: {
        'id': 63,
        'name': '移动屏障',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 32,
        'item_description': '可以阻挡子弹的屏障',
        'icon': 190,
        'has_cancel': True,
        'use_mode': 7,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'using': {
            5: 'recycle', 
        },
    }, 
    64: {
        'id': 64,
        'name': '空投-幽灵战甲',
        'item_type': 104,
        'item_sub_type': 1,
        'item_description': '开启后装备幽灵战甲',
        'icon': 181,
    }, 
    65: {
        'id': 65,
        'name': '匕首',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 33,
        'item_description': '匕首',
        'icon': 485,
        'icon_outline': 503,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    66: {
        'id': 66,
        'name': 'Origin-12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 100006,
        'item_description': '原装Origin12连发霰弹枪',
        'icon': 737,
        'icon_outline': 214,
        'mark_voice_id': (48, ),
    }, 
    67: {
        'id': 67,
        'name': 'Origin-12-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 100006,
        'item_description': '自定义枪械',
        'icon': 737,
        'icon_outline': 214,
        'mark_voice_id': (48, ),
    }, 
    68: {
        'id': 68,
        'name': '幽灵战甲-隐身',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 35,
        'icon': 212,
        'use_mode': 3,
        'empty': {
            1: 'hold', 
        },
    }, 
    70: {
        'id': 70,
        'name': '幽灵一闪',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 37,
        'icon': 211,
        'use_mode': 3,
        'empty': {
            1: 'hold', 
        },
        'take_directly': True,
    }, 
    71: {
        'id': 71,
        'name': '霰弹枪子弹',
        'item_type': 102,
        'item_ground_model_id': 544,
        'item_description': '霰弹枪弹药',
        'short_tips': '霰弹枪弹药',
        'icon': 397,
        'bag_max_stack_limit': 30,
        'ground_max_stack_limit': 30,
        'mark_voice_id': (10080, ),
    }, 
    72: {
        'id': 72,
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 100008,
        'item_description': '原装Vector冲锋枪',
        'icon': 738,
        'icon_outline': 220,
        'mark_voice_id': (48, ),
    }, 
    73: {
        'id': 73,
        'name': 'Vector-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 100008,
        'item_description': '自定义枪械',
        'icon': 738,
        'icon_outline': 220,
        'mark_voice_id': (48, ),
    }, 
    76: {
        'id': 76,
        'name': '测试载具',
        'item_type': 201,
        'item_sub_type': 1,
        'quality': 1,
        'item_description': '测试用载具',
    }, 
    77: {
        'id': 77,
        'name': 'AK47',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 100010,
        'item_description': '自定义枪械',
        'icon': 739,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    78: {
        'id': 78,
        'name': 'AK47',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 100010,
        'item_description': '原装AK47步枪',
        'icon': 739,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    79: {
        'id': 79,
        'name': '子弹补给',
        'item_type': 104,
        'item_sub_type': 2,
        'item_description': '子弹补给，包含各类型子弹',
        'item_collection': {
            7: 200, 
            8: 200, 
            14: 60, 
            71: 40, 
        },
    }, 
    80: {
        'id': 80,
        'name': '医疗补给',
        'item_type': 104,
        'item_sub_type': 2,
        'item_description': '医疗道具补给，包含各类型医疗道具',
        'item_collection': {
            46: 20, 
            47: 4, 
        },
    }, 
    81: {
        'id': 81,
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 17,
        'item_description': '加装全息瞄准镜的Vector',
        'icon': 738,
        'icon_outline': 220,
        'mark_voice_id': (48, ),
    }, 
    82: {
        'id': 82,
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 18,
        'item_description': '加装3倍镜的AUG',
        'icon': 741,
        'icon_outline': 217,
        'mark_voice_id': (48, ),
    }, 
    83: {
        'id': 83,
        'name': '技能-瞬爆烟雾弹',
        'item_type': 101,
        'item_sub_type': 12,
        'quality': 1,
        'item_ground_model_id': 508,
        'equip_id': 62,
        'item_description': '爆炸后产生烟雾，遮挡视野',
        'icon': 108,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
    }, 
    84: {
        'id': 84,
        'name': '粒子护盾',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 41,
        'item_description': '放置一个矩形粒子护盾，阻挡前方伤害',
        'icon': 10163,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    85: {
        'id': 85,
        'name': '粒子护盾-球型',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 42,
        'item_description': '放置一个半球形粒子护盾，阻挡伤害',
        'icon': 10163,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    86: {
        'id': 86,
        'name': '粒子护盾-充能',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 43,
        'item_description': '放置一个单向粒子护盾，子弹传过后会增加伤害',
        'icon': 10163,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    87: {
        'id': 87,
        'name': '卫星扫描',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 44,
        'item_description': '放置一个卫星扫描装置，进行定点扫描',
        'icon': 183,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    88: {
        'id': 88,
        'name': '卫星扫描-追踪',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 45,
        'item_description': '卫星扫描效果跟随玩家移动，但扫描范围变小，持续时间变短',
        'icon': 183,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    89: {
        'id': 89,
        'name': '卫星扫描-全局',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 46,
        'item_description': '卫星扫描范围变大，但扫描间隔和冷却时间变长',
        'icon': 183,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    90: {
        'id': 90,
        'name': '昆虫监视器',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 47,
        'item_description': '投掷一个昆虫探测器，对目标点周围进行小范围扫描，被扫描的敌人将进入暴露状态',
        'icon': 10160,
        'bag_max_stack_limit': 2,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    91: {
        'id': 91,
        'name': '昆虫监视器-陷阱',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 48,
        'item_description': '装置变为隐身陷阱，敌人靠近时触发爆炸，造成伤害并持续暴露敌人位置',
        'icon': 10160,
        'bag_max_stack_limit': 2,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    92: {
        'id': 92,
        'name': '昆虫监视器-突破',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 49,
        'item_description': '以自身为范围进行一次快速扫描，被扫描的敌人将进入暴露状态',
        'icon': 10160,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    93: {
        'id': 93,
        'name': 'EMP手雷',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 50,
        'item_description': '丢出一个EMP手雷，爆炸产生电磁脉冲，对护盾和装置类道具产生大量伤害，效果可穿墙',
        'icon': 10162,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    94: {
        'id': 94,
        'name': 'EMP手雷-大号',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 51,
        'item_description': '增加EMP手雷爆炸范围，但伤害变低',
        'icon': 10162,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    95: {
        'id': 95,
        'name': 'EMP手雷-震荡',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 52,
        'item_description': '爆炸还会产生减速效果，但伤害变低',
        'icon': 10162,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    96: {
        'id': 96,
        'name': '导弹打击',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 53,
        'item_description': '呼叫远程导弹，随机多次打击目标范围',
        'icon': 10161,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    97: {
        'id': 97,
        'name': '导弹打击-燃烧',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 54,
        'item_description': '导弹爆炸伤害降低，爆炸后产生范围燃烧效果',
        'icon': 10161,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    98: {
        'id': 98,
        'name': '导弹打击-烟雾',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 55,
        'item_description': '导弹爆炸伤害降低，爆炸后产生大量烟雾',
        'icon': 10161,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    99: {
        'id': 99,
        'name': '毒气陷阱',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 56,
        'item_description': '布置一个毒气陷阱，敌人靠近时爆炸，可存储2个',
        'icon': 10164,
        'bag_max_stack_limit': 2,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
    }, 
    100: {
        'id': 100,
        'name': '毒气陷阱-剧毒',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 57,
        'item_description': '增加毒气陷阱爆炸范围，并产生干扰效果，只能存储1个',
        'icon': 10164,
        'bag_max_stack_limit': 1,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
    }, 
    101: {
        'id': 101,
        'name': '毒气陷阱-防御',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 58,
        'item_description': '毒气陷阱隐身，但爆炸伤害降低,可存储2个',
        'icon': 10164,
        'bag_max_stack_limit': 2,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
    }, 
    102: {
        'id': 102,
        'name': '治疗无人机',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 59,
        'item_description': '设置一个范围治疗无人机，可持续治疗所受伤害',
        'icon': 10160,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    103: {
        'id': 103,
        'name': '治疗无人机-速度',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 60,
        'item_description': '设置一个无人机跟随队友，可持续治疗并提供加速效果',
        'icon': 10160,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    104: {
        'id': 104,
        'name': '治疗无人机-火力',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 61,
        'item_description': '设置一个无人机跟随队友，可持续治疗并提供使目标枪械伤害增加',
        'icon': 10160,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    109: {
        'id': 109,
        'name': '任务代号-护送',
        'item_type': 107,
        'quality': 3,
        'item_ground_model_id': 513,
        'item_description': '护送卡车到目标地点',
        'icon': 114,
        'ground_max_stack_limit': 1,
        'br_task_id': 6,
    }, 
    110: {
        'id': 110,
        'name': '深海领域',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 65,
        'item_description': '使用深海的力量，召唤一个烟雾立场',
        'icon': 10160,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    111: {
        'id': 111,
        'name': '深海领域-极寒',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 66,
        'item_description': '降低立场内的温度，立场内的玩家受到减速效果',
        'icon': 10160,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    112: {
        'id': 112,
        'name': '深海领域-腐蚀',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 67,
        'item_description': '立场具有腐蚀性，立场内的玩家受到持续伤害',
        'icon': 10160,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    113: {
        'id': 113,
        'name': '赛博忍术-瞬身',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 68,
        'item_description': '丢出一支苦无，并在短暂的延迟后飞到苦无所在位置',
        'icon': 10160,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    114: {
        'id': 114,
        'name': '赛博忍术-回溯1阶段',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 69,
        'item_description': '设置一个图腾，一定时间内再次使用可回到图腾位置',
        'icon': 10160,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    115: {
        'id': 115,
        'name': '赛博忍术-回溯2阶段',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 70,
        'item_description': '设置一个图腾，一定时间内再次使用可回到图腾位置',
        'icon': 10160,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
    116: {
        'id': 116,
        'name': '天赋核心三选一',
        'item_type': 111,
        'item_ground_model_id': 513,
        'item_description': '使用后能 从三个天赋中选一个',
        'icon': 10160,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'use_code': 'TACTICAL_CORE',
        'operate_mode': 1,
        'use_mode': 3,
        'empty': {
            1: 'hold', 
        },
    }, 
    117: {
        'id': 117,
        'name': 'Kala',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 100013,
        'item_description': 'Kala狙击枪',
        'icon': 740,
        'icon_outline': 266,
        'mark_voice_id': (48, ),
    }, 
    118: {
        'id': 118,
        'name': 'Kala-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 100013,
        'item_description': '自定义枪械',
        'icon': 740,
        'icon_outline': 266,
        'mark_voice_id': (48, ),
    }, 
    119: {
        'id': 119,
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 100014,
        'item_description': '原装KAG-6步枪',
        'icon': 741,
        'icon_outline': 217,
        'mark_voice_id': (48, ),
    }, 
    120: {
        'id': 120,
        'name': 'KAG-6-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 100014,
        'item_description': '自定义枪械',
        'icon': 741,
        'icon_outline': 217,
        'mark_voice_id': (48, ),
    }, 
    142: {
        'id': 142,
        'name': 'DesertEagle',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 572,
        'equip_id': 74,
        'gun_blueprint_id': 100015,
        'item_description': '原装沙鹰',
        'icon': 742,
        'icon_outline': 326,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    143: {
        'id': 143,
        'name': 'DesertEagle-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 572,
        'equip_id': 74,
        'gun_blueprint_id': 100015,
        'item_description': '自定义枪械',
        'icon': 742,
        'icon_outline': 326,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    144: {
        'id': 144,
        'name': '枪匕首',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 516,
        'equip_id': 73,
        'gun_blueprint_id': 100019,
        'icon': 193,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    145: {
        'id': 145,
        'name': '敌人的名牌',
        'item_type': 112,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '拾取后获得进化能量',
        'short_tips': '拾取后获得进化能量',
        'icon': 10683,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (44, ),
    }, 
    146: {
        'id': 146,
        'name': '技能模组',
        'item_type': 111,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 579,
        'item_description': '技能获得大幅强化',
        'short_tips': '大幅强化技能效果',
        'icon': 391,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    147: {
        'id': 147,
        'name': 'M82',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 100016,
        'item_description': '原装URB冲锋枪',
        'icon': 743,
        'icon_outline': 322,
        'mark_voice_id': (48, ),
    }, 
    148: {
        'id': 148,
        'name': 'M82-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 100016,
        'item_description': '自定义枪械',
        'icon': 743,
        'icon_outline': 322,
        'mark_voice_id': (48, ),
    }, 
    149: {
        'id': 149,
        'name': 'INP9',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 100017,
        'item_description': '原装INP9冲锋枪',
        'icon': 748,
        'icon_outline': 323,
        'mark_voice_id': (48, ),
    }, 
    150: {
        'id': 150,
        'name': 'INP9-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 100017,
        'item_description': '自定义枪械',
        'icon': 748,
        'icon_outline': 323,
        'mark_voice_id': (48, ),
    }, 
    151: {
        'id': 151,
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 100018,
        'item_description': '原装M700狙击枪',
        'icon': 351,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    152: {
        'id': 152,
        'name': 'M700-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 100018,
        'item_description': '自定义枪械',
        'icon': 744,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    153: {
        'id': 153,
        'name': 'M9A3',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 566,
        'equip_id': 78,
        'gun_blueprint_id': 100019,
        'item_description': '原装M9A3手枪',
        'icon': 178,
        'icon_outline': 327,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    154: {
        'id': 154,
        'name': 'M9A3-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 566,
        'equip_id': 78,
        'gun_blueprint_id': 100019,
        'item_description': '自定义枪械',
        'icon': 178,
        'icon_outline': 327,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    155: {
        'id': 155,
        'name': 'M870',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 100020,
        'item_description': '原装M870单发霰弹枪',
        'icon': 745,
        'icon_outline': 321,
        'mark_voice_id': (48, ),
    }, 
    156: {
        'id': 156,
        'name': 'M870-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 100020,
        'item_description': '自定义枪械',
        'icon': 745,
        'icon_outline': 321,
        'mark_voice_id': (48, ),
    }, 
    157: {
        'id': 157,
        'name': '测试载具-roar',
        'item_type': 201,
        'item_sub_type': 1,
        'quality': 1,
        'item_description': '测试用载具',
    }, 
    158: {
        'id': 158,
        'name': '子弹补满',
        'item_type': 113,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 513,
        'item_description': '能把背包子弹补满',
        'icon': 398,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    159: {
        'id': 159,
        'name': 'xdroid',
        'item_type': 101,
        'item_sub_type': 19,
        'quality': 3,
        'equip_id': 80,
        'item_description': '帮助专家适应各种作战环境',
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'use_mode': 9,
    }, 
    160: {
        'id': 160,
        'name': '高价值空投',
        'item_type': 103,
        'item_ground_model_id': 552,
        'item_description': '高价值空投',
        'icon': 403,
        'ground_max_stack_limit': 1,
    }, 
    161: {
        'id': 161,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1001,
        'item_description': '原装M4A1步枪',
        'icon': 735,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    162: {
        'id': 162,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1002,
        'item_description': '装备全息瞄具的M4A1步枪',
        'icon': 645,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    163: {
        'id': 163,
        'name': 'M4A1 - 中距离',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1003,
        'item_description': '装备全息镜，强化中距离作战能力',
        'icon': 646,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    164: {
        'id': 164,
        'name': 'M4A1 - 腰射强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1004,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 647,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    165: {
        'id': 165,
        'name': 'Origin12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6001,
        'item_description': '原装Origin12连发霰弹枪',
        'icon': 737,
        'icon_outline': 214,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    166: {
        'id': 166,
        'name': 'Origin12 - 扩容弹匣',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6002,
        'item_description': '装备扩容弹匣的Origin12霰弹枪',
        'icon': 655,
        'icon_outline': 214,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    167: {
        'id': 167,
        'name': 'Origin12 - CQC',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6003,
        'item_description': '强化近距离作战能力的Origin12霰弹枪',
        'icon': 656,
        'icon_outline': 214,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    168: {
        'id': 168,
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8001,
        'item_description': '原装Vector冲锋枪',
        'icon': 738,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    169: {
        'id': 169,
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8002,
        'item_description': '装备了全息镜的Vector冲锋枪',
        'icon': 661,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    170: {
        'id': 170,
        'name': 'Vector - 扩容弹匣',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8003,
        'item_description': '装备了扩容弹匣的Vector冲锋枪',
        'icon': 662,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    171: {
        'id': 171,
        'name': 'Vector - CQC腰射',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8004,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 663,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    172: {
        'id': 172,
        'name': 'AK47',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10001,
        'item_description': '原装AK47步枪',
        'icon': 739,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    173: {
        'id': 173,
        'name': 'AK47 - 近程强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10002,
        'item_description': '强化近距离作战能力的AK47步枪',
        'icon': 668,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    174: {
        'id': 174,
        'name': 'AK47 - 扩容弹匣',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10003,
        'item_description': '装备了扩容弹匣的AK47步枪',
        'icon': 669,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    175: {
        'id': 175,
        'name': 'AK47 - 远程强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10004,
        'item_description': '装备2倍镜，强化中远距离作战能力',
        'icon': 670,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    176: {
        'id': 176,
        'name': 'Kala - 中程强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 13001,
        'item_description': '装备6倍镜，强化中距离作战能力',
        'icon': 674,
        'icon_outline': 266,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    177: {
        'id': 177,
        'name': 'Kala - 狙击手',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 13002,
        'item_description': '装备狙击镜，强化远距离作战能力',
        'icon': 675,
        'icon_outline': 266,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    178: {
        'id': 178,
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14001,
        'item_description': '原装KAG-6步枪',
        'icon': 741,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    179: {
        'id': 179,
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14002,
        'item_description': '装备的KAG-6步枪',
        'icon': 680,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    180: {
        'id': 180,
        'name': 'KAG-6 - 扩容弹匣',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14003,
        'item_description': '装备全息镜和扩容弹匣，强化中距离作战能力',
        'icon': 681,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    181: {
        'id': 181,
        'name': 'KAG-6 - 远程强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14004,
        'item_description': '装备2倍镜，强化中远距离作战能力',
        'icon': 682,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    182: {
        'id': 182,
        'name': 'Desert Eagle - 狙击手',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 572,
        'equip_id': 74,
        'gun_blueprint_id': 15001,
        'item_description': '装备3倍镜，有成为狙击枪的潜力',
        'icon': 686,
        'icon_outline': 326,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    183: {
        'id': 183,
        'name': 'URB',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16001,
        'item_description': '原装URB冲锋枪',
        'icon': 743,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    184: {
        'id': 184,
        'name': 'URB',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16002,
        'item_description': '装备全息镜的URB冲锋枪',
        'icon': 691,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    185: {
        'id': 185,
        'name': 'URB - 扩容弹匣',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16003,
        'item_description': '装备全息镜和扩容弹匣，强化中距离作战能力',
        'icon': 692,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    186: {
        'id': 186,
        'name': 'URB - 狙击副手',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16003,
        'item_description': '装备长枪管，适合作为狙击副手武器',
        'icon': 693,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    187: {
        'id': 187,
        'name': 'INP-9',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17001,
        'item_description': '原装INP-9冲锋枪',
        'icon': 748,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    188: {
        'id': 188,
        'name': 'INP-9',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17002,
        'item_description': '装备全息镜的INP-9冲锋枪',
        'icon': 698,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    189: {
        'id': 189,
        'name': 'INP-9 - 近程强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17003,
        'item_description': '装备全息镜和扩容弹匣，强化近距离作战能力',
        'icon': 699,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    190: {
        'id': 190,
        'name': 'INP-9 - 狙击副手',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17003,
        'item_description': '装备中距离枪管，适合作为狙击副手武器',
        'icon': 700,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    191: {
        'id': 191,
        'name': 'M700 - CQC',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18001,
        'item_description': '装备全息瞄具，强化近距离作战能力',
        'icon': 704,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    192: {
        'id': 192,
        'name': 'M700 - 狙击手',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18002,
        'item_description': '装备狙击镜，强化远距离作战能力',
        'icon': 705,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    193: {
        'id': 193,
        'name': 'M870 - 扩容弹匣',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 20001,
        'item_description': '装备扩容弹匣的M870霰弹枪',
        'icon': 709,
        'icon_outline': 321,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    194: {
        'id': 194,
        'name': 'M870 - 开镜强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 20002,
        'item_description': '强化开镜命中率的M870霰弹枪',
        'icon': 710,
        'icon_outline': 321,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    195: {
        'id': 195,
        'name': 'RPG-7',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 581,
        'equip_id': 82,
        'gun_blueprint_id': 100021,
        'item_description': '原装RPG-7发射器',
        'icon': 746,
        'icon_outline': 430,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    196: {
        'id': 196,
        'name': 'RPG-7-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 581,
        'equip_id': 82,
        'gun_blueprint_id': 100021,
        'item_description': '自定义枪械',
        'icon': 746,
        'icon_outline': 430,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    197: {
        'id': 197,
        'name': '发射器子弹',
        'item_type': 102,
        'quality': 1,
        'item_ground_model_id': 586,
        'item_description': '发射器专用弹药',
        'short_tips': '发射器弹药',
        'icon': 425,
        'bag_max_stack_limit': 10,
        'ground_max_stack_limit': 10,
        'mark_voice_id': (10082, ),
    }, 
    198: {
        'id': 198,
        'name': '复合弓',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'auto_mark': 1,
        'item_ground_model_id': 582,
        'equip_id': 83,
        'gun_blueprint_id': 100022,
        'item_description': '复合弓',
        'icon': 747,
        'icon_outline': 524,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    199: {
        'id': 199,
        'name': '复合弓-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 582,
        'equip_id': 83,
        'gun_blueprint_id': 100022,
        'item_description': '自定义枪械',
        'icon': 747,
        'icon_outline': 524,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    200: {
        'id': 200,
        'name': '防护屏障',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 200,
        'item_description': '设置一个可阻挡子弹的屏障',
        'skill_full_description': '放置一个可阻挡子弹的屏障(无法阻挡投掷物等道具类装置)，持续时间#eed42b 30s #E；当护盾生成器被破坏时，屏障也会消失',
        'icon': 10279,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1143, ),
        'ig_voice_CD_id': (1144, ),
    }, 
    201: {
        'id': 201,
        'name': '第四面墙',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 201,
        'item_description': '设置一个单向屏障，可阻挡外侧的子弹。',
        'skill_full_description': '设置一个单向屏障，可阻挡外侧的子弹，但无法阻挡投掷物等道具。当护盾生成器被破坏时，屏障也会消失。',
        'icon': 1091,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1143, ),
        'ig_voice_CD_id': (1144, ),
    }, 
    202: {
        'id': 202,
        'name': '导弹轰炸',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 202,
        'item_description': '呼叫远程导弹打击目标地点',
        'skill_full_description': '呼叫远程导弹打击目标地点，一共轰炸#eed42b 7 #E次，前#eed42b 3 #E发导弹会精准的攻击目标点，随后轰炸范围会逐步扩大到#eed42b 25m #E；每发导弹中心区域的伤害为#eed42b 150 #E点',
        'icon': 10276,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (54, ),
    }, 
    203: {
        'id': 203,
        'name': '光线打击',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 203,
        'item_description': '呼叫卫星发射聚能射线，持续打击目标地点',
        'icon': 10275,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'ig_voice_id': (73, ),
    }, 
    204: {
        'id': 204,
        'name': '毒气手雷',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 204,
        'item_description': '投掷一个毒气手雷，手雷撞击后引爆，持续不断的释放有毒气体;NOVA还可以主动吸收毒气来强化自己的战斗力',
        'skill_full_description': '投掷触爆式香水瓶，碎裂产生致幻毒气，遮蔽范围内玩家视野并造成伤害。',
        'icon': 10272,
        'bag_max_stack_limit': 2,
        'has_cancel': True,
        'use_mode': 10,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
            8: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (53, ),
    }, 
    205: {
        'id': 205,
        'name': '剧毒领域',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 205,
        'item_description': '设置一个毒气炸弹，释放大范围有毒气体，造成视线模糊和咳嗽',
        'icon': 10271,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            8: 'hold', 
            'cache': (8, ), 
        },
        'hold': {
            1: 'using', 
        },
        'ig_voice_id': (74, ),
    }, 
    206: {
        'id': 206,
        'name': '卫星扫描',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 206,
        'item_description': '设置一个扫描装置，将附近敌人的位置标记在小地图上',
        'skill_full_description': '设置一个扫描装置，每隔#eed42b 6s #E扫描范围#eed42b 150m #E内的敌人，将并将其位置标记在小地图上，扫描持续#eed42b 20s #E',
        'icon': 10274,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (51, ),
    }, 
    207: {
        'id': 207,
        'name': '进阶卫星扫描',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 207,
        'item_description': '升级扫描装置，将全图敌人的位置实时显示在小地图上',
        'icon': 10273,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'ig_voice_id': (75, ),
    }, 
    208: {
        'id': 208,
        'name': '防御炮塔',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 208,
        'item_description': '部署一个自动攻击的炮台',
        'skill_full_description': '部署一个自动攻击的炮台，炮台单次攻击伤害#eed42b 15 #E,攻击半径#eed42b 20m #E,攻击间隔#eed42b 1s #E,并拥有#eed42b 100 #E点生命值',
        'icon': 328,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (52, ),
    }, 
    209: {
        'id': 209,
        'name': '重火力炮塔',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 209,
        'item_description': '炮台升级，性能更加强大',
        'icon': 327,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'ig_voice_id': (52, ),
    }, 
    210: {
        'id': 210,
        'name': '刺杀形态',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 210,
        'item_description': '进入潜行状态，移动时不发出声音',
        'skill_full_description': '进入潜行状态，移动时不发出声音；进入状态后持续消耗能量，动作幅度越大，能量消耗越多；开火，跳跃等动作会额外消耗能量；技能再次开启间隔为 #eed42b 10s #E',
        'icon': 10269,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'ignore_quick_mode': True,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (57, ),
    }, 
    211: {
        'id': 211,
        'name': '幽灵形态',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 211,
        'item_description': '激活隐形迷彩，隐身状态下动作幅度越小，可见度越低',
        'icon': 10270,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'ignore_quick_mode': True,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'ig_voice_id': (57, ),
    }, 
    212: {
        'id': 212,
        'name': '治疗信标',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 212,
        'item_description': '放置一个医疗信标，治疗周围的队友',
        'skill_full_description': '放置一个医疗信标，治疗半径 #eed42b 5m #E内的队友，每秒恢复#eed42b 10 #E点生命值，持续 #eed42b 20s#E',
        'icon': 10277,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (55, ),
    }, 
    213: {
        'id': 213,
        'name': '治疗无人机',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 213,
        'item_description': '设置一个跟随队友的无人机，无人机可持续治疗队友',
        'icon': 10278,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (78, ),
    }, 
    214: {
        'id': 214,
        'name': '旋涡',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 214,
        'item_description': '召唤群鸦干扰敌人视野，其中一只乌鸦会追踪附近的敌人',
        'skill_full_description': '召唤群鸦干扰敌人视野，最远可命中#eed42b 33m #E内的目标；其中一只乌鸦会自动追踪半径#eed42b 8m #E内的敌人，被命中的敌人将进入#eed42b 2s #E的视野受阻状态',
        'icon': 10268,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (58, ),
    }, 
    215: {
        'id': 215,
        'name': '深渊',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 215,
        'item_description': '以自己为圆心创造一个领域，削弱敌人的视野',
        'icon': 10267,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'ig_voice_id': (79, ),
    }, 
    216: {
        'id': 216,
        'name': 'Glock',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 566,
        'equip_id': 15,
        'gun_blueprint_id': 55,
        'item_description': '装备扩容弹匣的Glock手枪',
        'icon': 736,
        'icon_outline': 177,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    217: {
        'id': 217,
        'name': 'DesertEagle',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 572,
        'equip_id': 74,
        'gun_blueprint_id': 56,
        'item_description': '装备扩容弹匣的沙鹰',
        'icon': 348,
        'icon_outline': 326,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    218: {
        'id': 218,
        'name': '粘雷',
        'item_type': 101,
        'item_sub_type': 11,
        'quality': 1,
        'item_ground_model_id': 539,
        'equip_id': 81,
        'item_description': '爆炸后产生范围伤害',
        'short_tips': '投掷物',
        'icon': 539,
        'icon_outline': 11459,
        'bag_max_stack_limit': 2,
        'ground_max_stack_limit': 2,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
        'mark_voice_id': (1073, ),
    }, 
    219: {
        'id': 219,
        'name': '斧子',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 84,
        'item_description': '斧子',
        'icon': 486,
        'icon_outline': 504,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    220: {
        'id': 220,
        'name': '吸入器',
        'item_type': 101,
        'item_sub_type': 17,
        'item_ground_model_id': 583,
        'equip_id': 85,
        'item_description': '使用吸入器，持续恢复生命',
        'short_tips': '医疗道具',
        'icon': 428,
        'bag_max_stack_limit': 2,
        'ground_max_stack_limit': 2,
        'operate_mode': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'mark_voice_id': (1074, ),
    }, 
    221: {
        'id': 221,
        'name': '武士刀',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 86,
        'item_description': '武士刀',
        'icon': 549,
        'icon_outline': 557,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    222: {
        'id': 222,
        'skip': 'trunk_only',
        'name': '棒球棍',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 87,
        'item_description': '棒球棍',
        'icon': 486,
        'icon_outline': 504,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    223: {
        'id': 223,
        'name': 'SCAR',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 100023,
        'item_description': 'Scar',
        'icon': 749,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    224: {
        'id': 224,
        'name': 'SCAR-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 100023,
        'item_description': '自定义枪械',
        'icon': 749,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    225: {
        'id': 225,
        'name': '寒冰屏障',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 225,
        'item_description': '召唤出一道无法通过的冰墙，来阻挡敌人的进攻',
        'skill_full_description': '召唤出一道无法通过的冰墙，来阻挡敌人的进攻，冰墙生命值 #eed42b 1000 #E，持续时间#eed42b 25 #E秒',
        'icon': 755,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1054, 1055, ),
    }, 
    226: {
        'id': 226,
        'name': 'M4A1-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1001,
        'item_description': '自定义枪械',
        'icon': 341,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    227: {
        'id': 227,
        'name': 'M4A1-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1002,
        'item_description': '自定义枪械',
        'icon': 341,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    228: {
        'id': 228,
        'name': 'M4A1-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1003,
        'item_description': '自定义枪械',
        'icon': 341,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    229: {
        'id': 229,
        'name': 'M4A1-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1004,
        'item_description': '自定义枪械',
        'icon': 341,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    230: {
        'id': 230,
        'name': 'Vector-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8001,
        'item_description': '自定义枪械',
        'icon': 344,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    231: {
        'id': 231,
        'name': 'Vector-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8002,
        'item_description': '自定义枪械',
        'icon': 344,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    232: {
        'id': 232,
        'name': 'Vector-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8003,
        'item_description': '自定义枪械',
        'icon': 344,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    233: {
        'id': 233,
        'name': 'Vector-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8004,
        'item_description': '自定义枪械',
        'icon': 344,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    234: {
        'id': 234,
        'name': 'AK47-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10001,
        'item_description': '自定义枪械',
        'icon': 345,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    235: {
        'id': 235,
        'name': 'AK47-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10002,
        'item_description': '自定义枪械',
        'icon': 345,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    236: {
        'id': 236,
        'name': 'AK47-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10003,
        'item_description': '自定义枪械',
        'icon': 345,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    237: {
        'id': 237,
        'name': 'AK47-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10004,
        'item_description': '自定义枪械',
        'icon': 345,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    238: {
        'id': 238,
        'name': 'KAG-6-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14001,
        'item_description': '自定义枪械',
        'icon': 347,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    239: {
        'id': 239,
        'name': 'KAG-6-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14002,
        'item_description': '自定义枪械',
        'icon': 347,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    240: {
        'id': 240,
        'name': 'KAG-6-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14003,
        'item_description': '自定义枪械',
        'icon': 347,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    241: {
        'id': 241,
        'name': 'KAG-6-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14004,
        'item_description': '自定义枪械',
        'icon': 347,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    242: {
        'id': 242,
        'name': 'URB-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16001,
        'item_description': '自定义枪械',
        'icon': 349,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    243: {
        'id': 243,
        'name': 'URB-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16002,
        'item_description': '自定义枪械',
        'icon': 349,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    244: {
        'id': 244,
        'name': 'URB-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16003,
        'item_description': '自定义枪械',
        'icon': 349,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    245: {
        'id': 245,
        'name': 'URB-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16003,
        'item_description': '自定义枪械',
        'icon': 349,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    246: {
        'id': 246,
        'name': 'INP-9-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17001,
        'item_description': '自定义枪械',
        'icon': 350,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    247: {
        'id': 247,
        'name': 'INP-9-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17002,
        'item_description': '自定义枪械',
        'icon': 350,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    248: {
        'id': 248,
        'name': 'INP-9-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17003,
        'item_description': '自定义枪械',
        'icon': 350,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    249: {
        'id': 249,
        'name': 'INP-9-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17003,
        'item_description': '自定义枪械',
        'icon': 350,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    250: {
        'id': 250,
        'name': '地狱之火',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 226,
        'item_description': '召唤来自地狱的火焰，形成一堵阻挡敌人视野的火墙，经过火墙的敌人会受到短暂的致盲效果',
        'skill_full_description': '召唤来自地狱的火焰，形成一堵阻挡敌人视野的火墙，火墙长#eed42b 30 #Em,高#eed42b 4 #Em，创建方向可以自由切换，持续#eed42b 20 #E秒，经过火墙的敌人会受到#eed42b 1.5s#E 的致盲效果',
        'icon': 788,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1125, ),
    }, 
    251: {
        'id': 251,
        'name': '棒球棍',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 89,
        'item_description': '棒球棍',
        'icon': 627,
        'icon_outline': 630,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    252: {
        'id': 252,
        'name': 'P90',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 591,
        'equip_id': 90,
        'gun_blueprint_id': 100024,
        'item_description': '原装P90冲锋枪',
        'icon': 770,
        'icon_outline': 763,
        'mark_voice_id': (48, ),
    }, 
    253: {
        'id': 253,
        'name': 'P90-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 591,
        'equip_id': 90,
        'gun_blueprint_id': 100024,
        'item_description': '自定义枪械',
        'icon': 770,
        'icon_outline': 763,
        'mark_voice_id': (48, ),
    }, 
    254: {
        'id': 254,
        'name': '钻墙炸弹',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 227,
        'item_description': 'blast可以用改装手臂发射炸弹，并且可以切换2种炸弹的形态；一种可以穿透掩体对敌人造成伤害，另一种可以引发持续爆炸',
        'skill_full_description': 'blast可以用改装手臂发射炸弹，并且可以切换2种炸弹的形态；一种可以穿透掩体对敌人造成伤害，可钻透#eed42b 5.5M #E厚度的掩体；钻墙成功时会在另一侧发射一枚涂鸦炸弹，对半径#eed42b 8m #E的敌人造成最高#eed42b 125 #E点爆炸伤害；另一种可以引发持续 #eed42b 6s #E的爆炸，爆炸半径为#eed42b 4m#E，每秒伤害为#eed42b 30#E点',
        'icon': 642,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 10,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
            8: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (1052, 1053, ),
    }, 
    255: {
        'id': 255,
        'name': '钻墙炸弹-穿透',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 228,
        'icon': 10268,
        'bag_max_stack_limit': 1,
        'ig_voice_id': (1052, 1053, ),
    }, 
    256: {
        'id': 256,
        'name': 'SCAR',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23005,
        'item_description': '原装SCAR步枪',
        'icon': 749,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    257: {
        'id': 257,
        'name': 'SCAR',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23006,
        'item_description': '装备全息瞄具的SCAR步枪',
        'icon': 719,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    258: {
        'id': 258,
        'name': 'SCAR-扩容弹匣',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23007,
        'item_description': '装备扩容弹匣的SCAR步枪',
        'icon': 720,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    259: {
        'id': 259,
        'name': 'SCAR-中程强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23008,
        'item_description': '装备延长枪管，强化中距离作战能力的SCAR步枪',
        'icon': 721,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    260: {
        'id': 260,
        'skip': 'trunk_only',
        'name': '测试用枪',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 575,
        'equip_id': 92,
        'gun_blueprint_id': 100026,
        'item_description': '原装VSS半自动狙击枪',
        'icon': 351,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    261: {
        'id': 261,
        'skip': 'trunk_only',
        'name': '测试用枪-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 575,
        'equip_id': 92,
        'gun_blueprint_id': 100026,
        'item_description': '自定义枪械',
        'icon': 351,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    262: {
        'id': 262,
        'name': '蝴蝶刀',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 95,
        'item_description': '蝴蝶刀',
        'icon': 20700000200,
        'icon_outline': 11734,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    263: {
        'id': 263,
        'name': '双手斧',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 96,
        'item_description': '双手斧',
        'icon': 1044,
        'icon_outline': 1045,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    264: {
        'id': 264,
        'name': '双刀',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 97,
        'item_description': '双刀',
        'icon': 893,
        'icon_outline': 899,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    265: {
        'id': 265,
        'name': '爪刀',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 99,
        'item_description': '爪刀',
        'icon': 1044,
        'icon_outline': 1070,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    266: {
        'id': 266,
        'name': '蝴蝶刀v6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 104,
        'item_description': '蝴蝶刀',
        'icon': 20700000200,
        'icon_outline': 11734,
        'bag_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    269: {
        'id': 269,
        'name': '追踪导弹',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 244,
        'item_description': '释放一枚可自动追踪敌人的小型导弹，导弹爆炸后会标记敌人',
        'skill_full_description': 'blast发射一枚可自动追踪敌人的小型导弹，导弹在飞行#eed42b 0.2s #E后会追踪附近的敌人，发现目标后会飞向目标的脚下；导弹在碰撞#eed42b 1s #E后爆炸，导弹爆炸会造成 #eed42b 60 #E点伤害并标记敌人#eed42b 4s#E',
        'icon': 642,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 10,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
            8: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (1052, 1053, ),
    }, 
    270: {
        'id': 270,
        'name': '击退炸弹',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 245,
        'item_description': '占位blast击退炸弹描述 物品表270',
        'skill_full_description': '占位blast击退炸弹描述 物品表270',
        'icon': 642,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 14,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
            8: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (1052, 1053, ),
    }, 
    271: {
        'id': 271,
        'name': '烟火秀',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 246,
        'item_description': '快速丢出一枚延迟爆炸的手雷，手雷爆炸时的火花会减速附近的敌人',
        'skill_full_description': '快速丢出一枚延迟爆炸的手雷，手雷爆炸时的火花会减速附近的敌人',
        'icon': 642,
        'bag_max_stack_limit': 1,
        'use_mode': 17,
        'empty': {
            8: 'hold', 
        },
        'hold': {
            9: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (1052, 1053, ),
    }, 
    292: {
        'id': 292,
        'name': '古典花瓶',
        'item_type': 105,
        'item_sub_type': 21,
        'quality': 4,
        'high_value': 1,
        'item_ground_model_id': 649,
        'item_description': '60000',
        'icon': 13402,
        'add_br_money': 60000,
        'mark_voice_id': (49, ),
    }, 
    293: {
        'id': 293,
        'name': '纯金奖牌',
        'item_type': 105,
        'item_sub_type': 21,
        'quality': 4,
        'high_value': 1,
        'item_ground_model_id': 648,
        'item_description': '80000',
        'icon': 13403,
        'add_br_money': 80000,
        'mark_voice_id': (49, ),
    }, 
    294: {
        'id': 294,
        'name': '纯金圣杯',
        'item_type': 105,
        'item_sub_type': 21,
        'quality': 4,
        'high_value': 1,
        'item_ground_model_id': 651,
        'item_description': '100000',
        'icon': 13400,
        'add_br_money': 100000,
        'mark_voice_id': (49, ),
    }, 
    295: {
        'id': 295,
        'name': '纯金王冠',
        'item_type': 105,
        'item_sub_type': 21,
        'quality': 4,
        'high_value': 1,
        'item_ground_model_id': 647,
        'item_description': '150000',
        'icon': 13401,
        'add_br_money': 150000,
        'mark_voice_id': (49, ),
    }, 
    296: {
        'id': 296,
        'name': '金币',
        'item_type': 105,
        'quality': 2,
        'item_ground_model_id': 511,
        'item_description': '一堆金条',
        'short_tips': '少量金条',
        'icon': 116,
        'add_br_money': 2000,
        'mark_voice_id': (49, ),
    }, 
    297: {
        'id': 297,
        'name': '金条',
        'item_type': 105,
        'quality': 2,
        'item_ground_model_id': 630,
        'item_description': '一堆金条',
        'short_tips': '少量金条',
        'icon': 13406,
        'add_br_money': 5000,
        'mark_voice_id': (49, ),
    }, 
    298: {
        'id': 298,
        'name': '金条堆（小）',
        'item_type': 105,
        'quality': 4,
        'item_ground_model_id': 631,
        'item_description': '一堆金条',
        'short_tips': '少量金条',
        'icon': 13405,
        'add_br_money': 10000,
        'mark_voice_id': (49, ),
    }, 
    299: {
        'id': 299,
        'name': '金条堆（大）',
        'item_type': 105,
        'quality': 4,
        'item_ground_model_id': 632,
        'item_description': '一袋金条',
        'short_tips': '一袋金条',
        'icon': 13404,
        'add_br_money': 30000,
        'mark_voice_id': (49, ),
    }, 
    300: {
        'id': 300,
        'name': '大袋金条',
        'item_type': 105,
        'quality': 4,
        'high_value': 1,
        'item_ground_model_id': 633,
        'item_description': '大堆金条',
        'short_tips': '大堆金条',
        'icon': 13407,
        'add_br_money': 50000,
        'mark_voice_id': (49, ),
    }, 
    301: {
        'id': 301,
        'name': '队友的名牌',
        'item_type': 112,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '拾取后减少队友30%复活时间',
        'short_tips': '拾取减少复活时间',
        'icon': 10682,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (44, ),
    }, 
    302: {
        'id': 302,
        'name': '战利品',
        'item_type': 114,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '由战利品天赋击杀获得',
        'icon': 10132,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'trophies_group': 1053,
    }, 
    303: {
        'id': 303,
        'name': '进化能量（小）',
        'item_type': 116,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '获得100点进化能量',
        'short_tips': '获得100点进化能量',
        'icon': 598,
        'mark_voice_id': (10076, ),
        'add_core_points': 100,
    }, 
    304: {
        'id': 304,
        'name': '进化能量（中）',
        'item_type': 116,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '获得200点进化能量',
        'short_tips': '获得200点进化能量',
        'icon': 599,
        'mark_voice_id': (10076, ),
        'add_core_points': 200,
    }, 
    305: {
        'id': 305,
        'name': '进化能量（大）',
        'item_type': 116,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 579,
        'item_description': '获得400点进化能量',
        'short_tips': '获得400点进化能量',
        'icon': 600,
        'mark_voice_id': (10076, ),
        'add_core_points': 400,
    }, 
    306: {
        'id': 306,
        'name': '进化能量（特）',
        'item_type': 116,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 579,
        'item_description': '获得600点进化能量',
        'short_tips': '获得600点进化能量',
        'icon': 601,
        'mark_voice_id': (10076, ),
        'add_core_points': 600,
    }, 
    307: {
        'id': 307,
        'name': '扫描器',
        'item_type': 101,
        'item_sub_type': 20,
        'quality': 1,
        'equip_id': 229,
        'item_description': '点击后可以扫描裂隙位置',
        'short_tips': '扫描裂隙位置',
        'icon': 11181,
        'bag_max_stack_limit': 1,
        'can_drop': False,
        'use_mode': 11,
    }, 
    308: {
        'id': 308,
        'name': '血晶石',
        'item_type': 117,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 595,
        'item_description': '可用于局外进行奖励兑换',
        'short_tips': '兑换活动奖励',
        'icon': 11230,
        'ground_max_stack_limit': 6,
        'can_drop': False,
    }, 
    309: {
        'id': 309,
        'name': '地狱之火',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 230,
        'item_description': '召唤来自地狱的火焰，形成一堵阻挡敌人视野的火墙，经过火墙的敌人会受到短暂的致盲效果',
        'skill_full_description': '召唤来自地狱的火焰，形成一堵阻挡敌人视野的火墙，火墙长#eed42b 30 #Em,高#eed42b 4 #Em，创建方向可以自由切换，持续#eed42b 20 #E秒，经过火墙的敌人会受到#eed42b 1.5s#E 的致盲效果',
        'icon': 788,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1125, ),
    }, 
    310: {
        'id': 310,
        'name': '疾风斩',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 231,
        'item_description': '蓄力后向前斩出，获得一段位移',
        'skill_full_description': '蓄力后向前斩出，蓄力时间越长，斩击速度越快；三段蓄力所需时间分别为 #eed42b 1.2/1.8/2.4 s#E ，蓄力后斩击速度分别为 #eed42b 8/16/30 m/s #E',
        'icon': 827,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'take_directly': True,
        'ig_voice_id': (1127, ),
    }, 
    311: {
        'id': 311,
        'name': '任务代号-神秘商店',
        'item_type': 107,
        'quality': 3,
        'item_ground_model_id': 561,
        'item_description': '拾取后接取神秘商店任务',
        'icon': 11302,
        'ground_max_stack_limit': 1,
        'br_task_id': 7,
        'mark_voice_id': (70, ),
    }, 
    312: {
        'id': 312,
        'name': 'VSS',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 100025,
        'item_description': '原装VSS半自动狙击枪',
        'icon': 813,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    313: {
        'id': 313,
        'name': 'VSS-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 100025,
        'item_description': '自定义枪械',
        'icon': 813,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    314: {
        'id': 314,
        'name': '格挡',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 232,
        'item_description': '格挡来自前方的子弹',
        'icon': 788,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 13,
        'empty': {
            8: 'hold', 
            'cache': (8, ), 
        },
        'hold': {
            8: 'using', 
        },
        'using': {
            10: 'recycle', 
        },
        'take_directly': True,
    }, 
    315: {
        'id': 315,
        'name': '武士刀-大厅挂接',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 233,
        'icon': 549,
        'icon_outline': 557,
        'bag_max_stack_limit': 1,
    }, 
    316: {
        'id': 316,
        'name': '武士刀鞘-大厅挂接',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_description': 'ZERO-武士刀',
        'icon': 549,
        'icon_outline': 557,
        'bag_max_stack_limit': 1,
    }, 
    317: {
        'id': 317,
        'name': '电磁爆破',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 234,
        'item_description': '蓄力后向前发射电磁脉冲，对护甲和设备造成伤害，同时使敌人进入干扰状态，看不清队友名牌并且无法使用技能',
        'skill_full_description': '蓄力后向前发射电磁脉冲（脉冲宽度为 #eed42b 20m #E）,蓄力最远可对 #eed42b 40m #E内敌人的护甲和设备造成 #eed42b 40 #E伤害，同时使敌人进入干扰状态，看不清队友名牌并且无法使用技能，持续 #eed42b 6s#E',
        'icon': 853,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1129, ),
    }, 
    318: {
        'id': 318,
        'name': '护甲升级Lv.4',
        'item_type': 118,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '获得护甲升级，125点护甲',
        'icon': 11406,
        'bag_max_stack_limit': 1,
    }, 
    319: {
        'id': 319,
        'name': '技能升级',
        'item_type': 119,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '获得技能升级',
        'icon': 11407,
        'bag_max_stack_limit': 1,
    }, 
    320: {
        'id': 320,
        'name': '护甲Lv.2',
        'item_type': 118,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '获得护甲升级，75点护甲',
        'icon': 11404,
        'bag_max_stack_limit': 1,
    }, 
    321: {
        'id': 321,
        'name': '护甲Lv.3',
        'item_type': 118,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '获得护甲升级，100点护甲',
        'icon': 11405,
        'bag_max_stack_limit': 1,
    }, 
    322: {
        'id': 322,
        'name': 'AR97',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 608,
        'equip_id': 93,
        'gun_blueprint_id': 100027,
        'item_description': '原装AR97',
        'icon': 110000,
        'icon_outline': 110002,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    323: {
        'id': 323,
        'skip': 'trunk_only',
        'name': '占位-直升机',
        'item_type': 201,
        'item_sub_type': 1,
        'quality': 1,
    }, 
    324: {
        'id': 324,
        'name': 'AR97-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 608,
        'equip_id': 93,
        'gun_blueprint_id': 100027,
        'item_description': '自定义枪械',
        'icon': 110000,
        'icon_outline': 110002,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    325: {
        'id': 325,
        'name': 'Minigun',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 616,
        'equip_id': 94,
        'gun_blueprint_id': 100028,
        'item_description': '原装Minigun',
        'icon': 1011,
        'icon_outline': 1010,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    326: {
        'id': 326,
        'name': 'Minigun-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 616,
        'equip_id': 94,
        'gun_blueprint_id': 100028,
        'item_description': '自定义枪械',
        'icon': 1011,
        'icon_outline': 1010,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    327: {
        'id': 327,
        'name': '嗨翻全场',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 235,
        'item_description': '激活高能状态，持续期间滑铲速度提升。',
        'skill_full_description': '激活高能状态，持续期间滑铲速度提升。',
        'icon': 1092,
        'bag_max_stack_limit': 1,
        'ig_voice_id': (1140, ),
        'ig_voice_CD_id': (1141, ),
    }, 
    328: {
        'id': 328,
        'name': 'MP5',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 618,
        'equip_id': 2,
        'gun_blueprint_id': 100002,
        'item_description': '原装MP5',
        'icon': 1031,
        'icon_outline': 12074,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    329: {
        'id': 329,
        'name': 'MP5',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 618,
        'equip_id': 2,
        'gun_blueprint_id': 100002,
        'item_description': '自定义枪械',
        'icon': 1031,
        'icon_outline': 12074,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    330: {
        'id': 330,
        'name': 'M4A1-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1015,
        'item_description': '自定义枪械',
        'icon': 341,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    331: {
        'id': 331,
        'name': 'M4A1-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1016,
        'item_description': '自定义枪械',
        'icon': 341,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    332: {
        'id': 332,
        'name': 'Vector-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8016,
        'item_description': '自定义枪械',
        'icon': 344,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    333: {
        'id': 333,
        'name': 'Vector-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8017,
        'item_description': '自定义枪械',
        'icon': 344,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    334: {
        'id': 334,
        'name': 'AK47-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10016,
        'item_description': '自定义枪械',
        'icon': 345,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    335: {
        'id': 335,
        'name': 'AK47-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10017,
        'item_description': '自定义枪械',
        'icon': 345,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    336: {
        'id': 336,
        'name': 'KAG-6-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14016,
        'item_description': '自定义枪械',
        'icon': 347,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    337: {
        'id': 337,
        'name': 'KAG-6-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14017,
        'item_description': '自定义枪械',
        'icon': 347,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    338: {
        'id': 338,
        'name': 'URB-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16019,
        'item_description': '自定义枪械',
        'icon': 349,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    339: {
        'id': 339,
        'name': 'URB-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16020,
        'item_description': '自定义枪械',
        'icon': 349,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    340: {
        'id': 340,
        'name': 'INP-9-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17017,
        'item_description': '自定义枪械',
        'icon': 350,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    341: {
        'id': 341,
        'name': 'INP-9-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17018,
        'item_description': '自定义枪械',
        'icon': 350,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    342: {
        'id': 342,
        'name': 'SCAR-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23017,
        'item_description': '自定义枪械',
        'icon': 749,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    343: {
        'id': 343,
        'name': 'SCAR-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23018,
        'item_description': '自定义枪械',
        'icon': 749,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    344: {
        'id': 344,
        'name': 'P90-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 591,
        'equip_id': 90,
        'gun_blueprint_id': 24014,
        'item_description': '自定义枪械',
        'icon': 770,
        'icon_outline': 763,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    345: {
        'id': 345,
        'name': 'P90-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 591,
        'equip_id': 90,
        'gun_blueprint_id': 24015,
        'item_description': '自定义枪械',
        'icon': 770,
        'icon_outline': 763,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    346: {
        'id': 346,
        'name': 'VSS-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25006,
        'item_description': '自定义枪械',
        'icon': 813,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    347: {
        'id': 347,
        'name': 'VSS-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25007,
        'item_description': '自定义枪械',
        'icon': 813,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    348: {
        'id': 348,
        'name': '线索',
        'item_type': 120,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 614,
        'item_description': '可用于局外进行奖励兑换',
        'short_tips': '兑换活动奖励',
        'icon': 11860,
        'ground_max_stack_limit': 5,
        'can_drop': False,
    }, 
    349: {
        'id': 349,
        'name': '转移无人机',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'icon': 189,
    }, 
    350: {
        'id': 350,
        'name': '干扰UAV',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'icon': 189,
    }, 
    351: {
        'id': 351,
        'name': '快捷商店',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'icon': 189,
    }, 
    352: {
        'id': 352,
        'name': '应急治疗',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 236,
        'item_description': '短时间内大幅度强化甲虫无人机的治疗效果',
        'skill_full_description': '短时间内增加  #eed42b 100% #E甲虫无人机的治疗效果',
        'icon': 1008,
        'bag_max_stack_limit': 1,
    }, 
    353: {
        'id': 353,
        'name': '爱愈降临',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 237,
        'item_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友',
        'skill_full_description': '对友方或自身释放厄洛斯，厄洛斯会跟随并持续治疗目标玩家。',
        'icon': 1098,
        'bag_max_stack_limit': 2,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (78, ),
    }, 
    354: {
        'id': 354,
        'name': '甲虫无人机',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 238,
        'item_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友',
        'skill_full_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友，每1.5秒恢复 #eed42b 8 #E生命值（护盾效率减半）；无人机可对 #eed42b 40m#E内的队友释放（不会受到阻挡），无人机可以被子弹或技能破坏',
        'icon': 1017,
        'bag_max_stack_limit': 2,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'ig_voice_id': (78, ),
    }, 
    355: {
        'id': 355,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1017,
        'item_description': '装备扩容弹匣的M4A1步枪',
        'icon': 856,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    356: {
        'id': 356,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1018,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 857,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    357: {
        'id': 357,
        'name': 'Origin12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6014,
        'item_description': '装备延长枪管的Origin12霰弹枪',
        'icon': 858,
        'icon_outline': 214,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    358: {
        'id': 358,
        'name': 'Origin12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6015,
        'item_description': '装备弹鼓的Origin12霰弹枪',
        'icon': 859,
        'icon_outline': 214,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    359: {
        'id': 359,
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8018,
        'item_description': '综合性能优秀的Vector冲锋枪',
        'icon': 860,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    360: {
        'id': 360,
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8019,
        'item_description': '装备弹鼓的Vector冲锋枪',
        'icon': 861,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    361: {
        'id': 361,
        'name': 'AK47',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10018,
        'item_description': '装备4倍镜，强化中距离作战能力',
        'icon': 862,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    362: {
        'id': 362,
        'name': 'AK47',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10019,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 863,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    363: {
        'id': 363,
        'name': 'Kala',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 13013,
        'item_description': '装备延长重型枪管，强化远距离作战能力',
        'icon': 864,
        'icon_outline': 266,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    364: {
        'id': 364,
        'name': 'Kala',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 13014,
        'item_description': '装备2倍镜，强化近距离作战能力',
        'icon': 865,
        'icon_outline': 266,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    365: {
        'id': 365,
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14018,
        'item_description': '装备扩容弹匣的KAG-6步枪',
        'icon': 866,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    366: {
        'id': 366,
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14019,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 867,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    367: {
        'id': 367,
        'name': 'URB',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16021,
        'item_description': '装备2倍镜，强化中距离作战能力',
        'icon': 868,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    368: {
        'id': 368,
        'name': 'URB',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16022,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 869,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    369: {
        'id': 369,
        'name': 'INP-9',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17019,
        'item_description': '综合性能优秀的INP-9冲锋枪',
        'icon': 870,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    370: {
        'id': 370,
        'name': 'INP-9',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17020,
        'item_description': '装备2倍镜和枪口制退器，强化中距离作战能力',
        'icon': 871,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    371: {
        'id': 371,
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18014,
        'item_description': '装备扩容弹匣的M700狙击步枪',
        'icon': 872,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    372: {
        'id': 372,
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18015,
        'item_description': '装备2倍镜，强化近距离作战能力',
        'icon': 873,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    373: {
        'id': 373,
        'name': 'M870',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 20012,
        'item_description': '综合性能优秀的M870霰弹枪',
        'icon': 874,
        'icon_outline': 321,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    374: {
        'id': 374,
        'name': 'M870',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 20013,
        'item_description': '装备长枪管，强化中距离作战能力',
        'icon': 875,
        'icon_outline': 321,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    375: {
        'id': 375,
        'name': 'Scar',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23019,
        'item_description': '综合性能优秀的SCAR步枪',
        'icon': 876,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    376: {
        'id': 376,
        'name': 'Scar',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23020,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 877,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    377: {
        'id': 377,
        'name': 'P90',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 591,
        'equip_id': 90,
        'gun_blueprint_id': 24016,
        'item_description': '综合性能优秀的P90冲锋枪',
        'icon': 878,
        'icon_outline': 763,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    378: {
        'id': 378,
        'name': 'P90',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 591,
        'equip_id': 90,
        'gun_blueprint_id': 24017,
        'item_description': '装备2倍镜，强化近距离作战能力',
        'icon': 879,
        'icon_outline': 763,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    379: {
        'id': 379,
        'name': 'VSS',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25008,
        'item_description': '原装VSS半自动狙击枪',
        'icon': 880,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    380: {
        'id': 380,
        'name': 'VSS',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25009,
        'item_description': '装备2倍镜的VSS半自动狙击枪',
        'icon': 881,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    381: {
        'id': 381,
        'name': 'VSS',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25010,
        'item_description': '装备扩容弹匣的VSS半自动狙击枪',
        'icon': 882,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    382: {
        'id': 382,
        'name': 'VSS',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25011,
        'item_description': '装备狙击枪托和4倍镜，强化远距离作战能力',
        'icon': 883,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    383: {
        'id': 383,
        'name': 'VSS',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25012,
        'item_description': '装备20发尖头弹匣，强化伤害',
        'icon': 884,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    384: {
        'id': 384,
        'name': 'VSS',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25013,
        'item_description': '综合性能优秀的VSS半自动狙击枪',
        'icon': 885,
        'icon_outline': 815,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    385: {
        'id': 385,
        'name': 'AR97',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 608,
        'equip_id': 93,
        'gun_blueprint_id': 27006,
        'item_description': '原装AR97步枪',
        'icon': 886,
        'icon_outline': 110002,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    386: {
        'id': 386,
        'name': 'AR97',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 608,
        'equip_id': 93,
        'gun_blueprint_id': 27007,
        'item_description': '装备了枪口制退器的AR97步枪',
        'icon': 887,
        'icon_outline': 110002,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    387: {
        'id': 387,
        'name': 'AR97',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 608,
        'equip_id': 93,
        'gun_blueprint_id': 27008,
        'item_description': '装备扩容弹匣的原装AR97步枪',
        'icon': 888,
        'icon_outline': 110002,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    388: {
        'id': 388,
        'name': 'AR97',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 608,
        'equip_id': 93,
        'gun_blueprint_id': 27009,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 889,
        'icon_outline': 110002,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    389: {
        'id': 389,
        'name': 'AR97',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 608,
        'equip_id': 93,
        'gun_blueprint_id': 27010,
        'item_description': '综合性能优秀的AR97步枪',
        'icon': 890,
        'icon_outline': 110002,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    390: {
        'id': 390,
        'name': 'AR97',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 608,
        'equip_id': 93,
        'gun_blueprint_id': 27011,
        'item_description': '装备长枪管和4倍镜，强化远距离作战能力',
        'icon': 891,
        'icon_outline': 110002,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    391: {
        'id': 391,
        'skip': 'trunk_only',
        'name': '破解任务-U盘',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 239,
        'item_description': '可用于破解任务打开保险箱使用',
        'use_mode': 6,
        'take_directly': True,
    }, 
    392: {
        'id': 392,
        'name': '钻墙炸弹-改',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 240,
        'item_description': 'blast可以用改装手臂发射热能炸弹，可以穿透障碍伤害敌人',
        'skill_full_description': 'blast可以用改装手臂发射热能炸弹，可以穿透障碍伤害敌人',
        'icon': 642,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 10,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
            8: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (1052, 1053, ),
    }, 
    393: {
        'id': 393,
        'name': '分裂手雷',
        'item_type': 101,
        'item_sub_type': 11,
        'quality': 1,
        'equip_id': 241,
        'item_description': '爆炸后产生范围伤害',
        'short_tips': '投掷物',
        'icon': 389,
        'icon_outline': 11462,
        'bag_max_stack_limit': 2,
        'ground_max_stack_limit': 2,
        'use_mode': 14,
        'empty': {
            8: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'ig_voice_id': (1082, ),
        'mark_voice_id': (61, ),
    }, 
    394: {
        'id': 394,
        'name': 'AUG',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 646,
        'equip_id': 98,
        'gun_blueprint_id': 100029,
        'item_description': '原装AUG',
        'icon': 110021,
        'icon_outline': 110023,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    395: {
        'id': 395,
        'name': 'AUG-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 646,
        'equip_id': 98,
        'gun_blueprint_id': 100029,
        'item_description': '自定义枪械',
        'icon': 110021,
        'icon_outline': 110023,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    396: {
        'id': 396,
        'skip': 'trunk_only',
        'name': 'Magnum',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 572,
        'equip_id': 101,
        'gun_blueprint_id': 100030,
        'item_description': '原装Magnum',
        'icon': 110021,
        'icon_outline': 110023,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    397: {
        'id': 397,
        'skip': 'trunk_only',
        'name': 'Magnum-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 572,
        'equip_id': 101,
        'gun_blueprint_id': 100030,
        'item_description': '自定义枪械',
        'icon': 110021,
        'icon_outline': 110023,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    398: {
        'id': 398,
        'name': 'MCX',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 656,
        'equip_id': 102,
        'gun_blueprint_id': 100031,
        'item_description': '原装MCX',
        'icon': 110029,
        'icon_outline': 110031,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    399: {
        'id': 399,
        'name': 'MCX-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 656,
        'equip_id': 102,
        'gun_blueprint_id': 100031,
        'item_description': '自定义枪械',
        'icon': 110029,
        'icon_outline': 110031,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    400: {
        'id': 400,
        'name': '侦察箭',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 242,
        'item_description': '命中后可短暂的显示附近敌人的位置',
        'skill_full_description': '命中后可短暂的显示附近  #eed42b 10m #E敌人的位置,持续  #eed42b 15s #E',
        'icon': 1086,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            8: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
            10: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1129, ),
    }, 
    401: {
        'id': 401,
        'name': '脉冲箭',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 243,
        'item_description': '发射可穿墙的脉冲爆炸，并减速命中的敌人',
        'skill_full_description': '发射一道宽  #eed42b 8m#E ,长  #eed42b 40m#E 的可穿墙的脉冲爆炸，被命中的敌人减速并且无法进入冲刺，持续 #eed42b 5s #E',
        'icon': 1083,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1129, ),
    }, 
    402: {
        'id': 402,
        'name': '弓-大厅挂接',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'equip_id': 301,
        'icon': 549,
        'icon_outline': 557,
        'bag_max_stack_limit': 1,
    }, 
    403: {
        'id': 403,
        'skip': 'trunk_only',
        'name': 'FAL',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 656,
        'equip_id': 103,
        'gun_blueprint_id': 100032,
        'item_description': '原装FAL',
        'icon': 110029,
        'icon_outline': 110031,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    404: {
        'id': 404,
        'skip': 'trunk_only',
        'name': 'FAL-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 656,
        'equip_id': 103,
        'gun_blueprint_id': 100032,
        'item_description': '自定义枪械',
        'icon': 110029,
        'icon_outline': 110031,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    405: {
        'id': 405,
        'skip': 'trunk_only',
        'name': 'Kar98k',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 575,
        'equip_id': 103,
        'gun_blueprint_id': 100033,
        'item_description': '原装Kar98k栓动步枪',
        'icon': 351,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    406: {
        'id': 406,
        'skip': 'trunk_only',
        'name': 'Kar98k-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 575,
        'equip_id': 103,
        'gun_blueprint_id': 100033,
        'item_description': '自定义枪械',
        'icon': 744,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    407: {
        'id': 407,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 565,
        'equip_id': 100,
        'gun_blueprint_id': 100035,
        'item_description': '原装M4A1步枪',
        'icon': 735,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    408: {
        'id': 408,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 565,
        'equip_id': 100,
        'gun_blueprint_id': 100035,
        'item_description': '原装M4A1步枪',
        'icon': 735,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    409: {
        'id': 409,
        'name': 'PKM',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 3863,
        'equip_id': 108,
        'gun_blueprint_id': 100034,
        'item_description': '原装PKM',
        'icon': 110072,
        'icon_outline': 110074,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    410: {
        'id': 410,
        'name': 'PKM-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 3863,
        'equip_id': 108,
        'gun_blueprint_id': 100034,
        'item_description': '自定义枪械',
        'icon': 110072,
        'icon_outline': 110074,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    429: {
        'id': 429,
        'name': 'Uzi',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 3868,
        'equip_id': 110,
        'gun_blueprint_id': 100040,
        'item_description': '原装Uzi',
        'icon': 110097,
        'icon_outline': 110099,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    430: {
        'id': 430,
        'name': 'Uzi-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 3868,
        'equip_id': 110,
        'gun_blueprint_id': 100040,
        'item_description': '自定义枪械',
        'icon': 110097,
        'icon_outline': 110099,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    437: {
        'id': 437,
        'skip': 'trunk_only',
        'name': '占位-HANK遥控战车',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 437,
        'item_description': '占位遥控战车：部署一个可遥控的小车攻击敌人。持续30秒，CD：90秒',
        'short_tips': '占位遥控战车：部署一个可遥控的小车攻击敌人。持续30秒，CD：90秒',
        'skill_full_description': '部署一台机械狗，再次使用技能可指令其移动。机械狗会自动锁定攻击范围内可见敌人。',
        'icon': 328,
        'ground_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (52, ),
    }, 
    467: {
        'id': 467,
        'name': '游猎哨卫',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 467,
        'item_description': '部署一个可指挥移动的全自动战车攻击敌人',
        'short_tips': '部署一个可指挥移动的全自动战车攻击敌人',
        'skill_full_description': '部署一台机械狗，再次使用技能可指令其移动。机械狗会自动锁定攻击范围内可见敌人。',
        'icon': 1089,
        'ground_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (52, ),
    }, 
    469: {
        'id': 469,
        'name': '巡蜂索敌',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 469,
        'item_description': 'HANK的巡逻无人机',
        'skill_full_description': '投掷一个巡飞弹，在指定范围内盘旋并索敌，锁定目标后俯冲引爆，对范围内敌人造成伤害和短暂减速，并暴露其当前位置。',
        'icon': 1090,
        'has_cancel': True,
        'use_mode': 1,
        'empty': {
            8: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
    }, 
    473: {
        'id': 473,
        'name': 'FN2000',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 3951,
        'equip_id': 114,
        'gun_blueprint_id': 100037,
        'item_description': '原装FN2000',
        'icon': 110111,
        'icon_outline': 110113,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    474: {
        'id': 474,
        'name': 'FN2000-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 3951,
        'equip_id': 114,
        'gun_blueprint_id': 100037,
        'item_description': '自定义枪械',
        'icon': 110111,
        'icon_outline': 110113,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    475: {
        'id': 475,
        'name': 'Galil',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 3974,
        'equip_id': 115,
        'gun_blueprint_id': 100038,
        'item_description': '原装Galil',
        'icon': 100038,
        'icon_outline': 110126,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    476: {
        'id': 476,
        'name': 'Galil-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 3974,
        'equip_id': 115,
        'gun_blueprint_id': 100038,
        'item_description': '自定义枪械',
        'icon': 100038,
        'icon_outline': 110126,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    513: {
        'id': 513,
        'skip': 'trunk_only',
        'name': 'QBZ95',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 3974,
        'equip_id': 121,
        'gun_blueprint_id': 100039,
        'item_description': '原装QBZ95',
        'icon': 110166,
        'icon_outline': 110168,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    514: {
        'id': 514,
        'skip': 'trunk_only',
        'name': 'QBZ95-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 3974,
        'equip_id': 121,
        'gun_blueprint_id': 100039,
        'item_description': '自定义枪械',
        'icon': 110166,
        'icon_outline': 110168,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    515: {
        'id': 515,
        'name': 'FN2000-New',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 3951,
        'equip_id': 122,
        'gun_blueprint_id': 100043,
        'item_description': '原装FN2000',
        'icon': 110111,
        'icon_outline': 110113,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    516: {
        'id': 516,
        'name': 'FN2000-New-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 3951,
        'equip_id': 122,
        'gun_blueprint_id': 100043,
        'item_description': '自定义枪械',
        'icon': 110111,
        'icon_outline': 110113,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    517: {
        'id': 517,
        'name': 'SKS',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 3951,
        'equip_id': 123,
        'gun_blueprint_id': 100044,
        'item_description': '原装SKS',
        'icon': 110111,
        'icon_outline': 110113,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    518: {
        'id': 518,
        'name': 'SKS-自定义',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'auto_mark': 1,
        'item_ground_model_id': 3951,
        'equip_id': 123,
        'gun_blueprint_id': 100044,
        'item_description': '自定义枪械',
        'icon': 110111,
        'icon_outline': 110113,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    701: {
        'id': 701,
        'name': '通用可投掷物测试',
        'item_type': 101,
        'item_sub_type': 22,
        'quality': 4,
        'equip_id': 401,
        'item_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友',
        'skill_full_description': '设置一个跟随队友或自己的甲虫无人机，无人机可持续治疗队友，每1.5秒恢复 #eed42b 8 #E生命值（护盾效率减半）；无人机可对 #eed42b 40m#E内的队友释放（不会受到阻挡），无人机可以被子弹或技能破坏',
        'icon': 1017,
        'bag_max_stack_limit': 2,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
    }, 
    1001: {
        'id': 1001,
        'name': '元气音域',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 300,
        'item_description': '音乐伙伴波波飞出，在目标位置生成混音台，音浪范围内的友方获得治疗。',
        'skill_full_description': '音乐伙伴波波飞出，在目标位置生成混音台，音浪范围内的友方获得治疗。',
        'icon': 1096,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (1147, ),
        'ig_voice_CD_id': (1148, ),
    }, 
    1002: {
        'id': 1002,
        'name': '律动加速环',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 301,
        'item_description': '创造一个具有加速效果的圆环，穿过友方获得持续一段时间的移速加成。',
        'skill_full_description': '创造一个具有加速效果的圆环，友方穿过时可获得持续一段时间的移速加成。',
        'icon': 1097,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 15,
        'empty': {
            8: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
            10: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1150, ),
        'ig_voice_CD_id': (1151, ),
    }, 
    1003: {
        'id': 1003,
        'name': '机动突跃',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 303,
        'item_description': '朝输入方向短暂冲刺，冲刺期间无视重力',
        'skill_full_description': '进行一段高速短冲刺。面对墙壁跳跃时，再次使用跳跃键可触发二段跳。',
        'icon': 1093,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1129, ),
    }, 
    1004: {
        'id': 1004,
        'name': '落雷刃',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 304,
        'item_description': '投掷电弧苦无，可命中敌方造成减速和伤害。X秒后苦无爆炸，造成范围伤害和减速。',
        'skill_full_description': '投掷电弧苦无，可命中敌方造成减速和伤害。3秒后苦无爆炸，造成范围伤害和减速。',
        'icon': 1094,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'ignore_quick_mode': True,
        'empty': {
            8: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
            9: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (1129, ),
    }, 
    1005: {
        'id': 1005,
        'name': '鲨鱼溜溜',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 305,
        'item_description': '使用后变身成数码鲨鱼，以鱼形态移动',
        'skill_full_description': '鲨琪把自己变成一只数码鲨鱼，提升移动能力。',
        'icon': 1102,
        'bag_max_stack_limit': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1192, ),
        'mark_voice_id': (1194, ),
        'ig_voice_CD_id': (1193, ),
    }, 
    1006: {
        'id': 1006,
        'name': '数字水族馆',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 306,
        'item_description': '以自身为中心开启数码矩阵，矩阵范围内敌人位置暴露，穿越边界会减速',
        'skill_full_description': '创造一个数码领域，对进入领域的敌人揭示其位置并造成短暂减速，领域内鲨琪移速提升。',
        'icon': 1103,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (1195, ),
        'mark_voice_id': (1198, ),
        'ig_voice_CD_id': (1197, ),
    }, 
    1007: {
        'id': 1007,
        'name': '为我心跳',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 307,
        'item_description': '强化受无人机影响的队友，提升多种能力',
        'skill_full_description': '厄洛斯跟随玩家时，按下技能可强化目标玩家，使其获得临时生命值、移速提升、射速提升。',
        'icon': 1099,
        'bag_max_stack_limit': 2,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (78, ),
    }, 
    1010: {
        'id': 1010,
        'name': '命运替演',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 421,
        'item_description': '释放一个幻象并隐身，再次释放技能可传送到幻象当前所处位置。',
        'short_tips': '释放一个幻象并隐身，再次释放技能可传送到幻象当前所处位置。',
        'skill_full_description': '多里安向面朝方向制造一个分身幻象，再次使用技能可传送至分身位置。',
        'icon': 1104,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
        'take_directly': True,
        'ig_voice_id': (57, ),
    }, 
    1011: {
        'id': 1011,
        'name': '幻象传送占位',
        'item_type': 101,
        'item_sub_type': 18,
        'equip_id': 422,
        'item_description': '释放一个自身的幻象，且在此期间内自己进入隐身状态。开火等进攻行为会解除隐身。',
        'short_tips': '释放一个自身的幻象，且在此期间内自己进入隐身状态。开火等进攻行为会解除隐身。幻象持续10秒，CD60秒。',
        'skill_full_description': '投掷脱身道具，触爆产生视障烟幕，范围内友方获得隐身效果。',
        'icon': 10270,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'ig_voice_id': (57, ),
    }, 
    1012: {
        'id': 1012,
        'name': '谎言之幕',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 426,
        'skill_full_description': '投掷脱身道具，触爆产生视障烟幕，范围内友方获得隐身效果。',
        'icon': 1105,
        'icon_outline': 11462,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 14,
        'empty': {
            8: 'hold', 
        },
        'hold': {
            3: 'using', 
            5: 'recycle', 
        },
        'take_directly': True,
        'mark_voice_id': (61, ),
    }, 
    1013: {
        'id': 1013,
        'name': '弹跳恶魔',
        'item_type': 101,
        'item_sub_type': 11,
        'equip_id': 308,
        'skill_full_description': '投掷恶魔粘弹，第一次碰撞后持续移动、反弹，对敌方造成伤害和击退效果。',
        'icon': 1100,
        'icon_outline': 11462,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 1,
        'empty': {
            1: 'hold', 
        },
        'hold': {
            4: 'using', 
        },
        'take_directly': True,
    }, 
    1014: {
        'id': 1014,
        'name': '震荡粘雷',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 309,
        'skill_full_description': '投掷粘雷，再次按下技能引爆。对敌人造成伤害和击退，对自身只有击退效果。',
        'icon': 1101,
        'icon_outline': 11462,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 1,
        'multi_using_code': 'ManualTriggerBomb',
        'empty': {
            8: 'hold', 
            'cache': (8, ), 
        },
        'hold': {
            8: 'using', 
        },
        'using': {
            8: 'using', 
        },
        'take_directly': True,
    }, 
    1015: {
        'id': 1015,
        'name': '踏风斩',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 310,
        'item_description': '蓄力后向前突刺，快速移动并对途径敌人造成伤害',
        'skill_full_description': '按下Q消耗能量进入蓄力，松开时释放技能向前冲刺，并对沿途的敌人造成伤害。蓄力越久冲刺距离越远。白止的技能都需要消耗能量来使用，能量会随时间自然恢复，也可以通过对敌人造成伤害恢复。',
        'icon': 1106,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 6,
        'take_directly': True,
        'ig_voice_id': (1127, ),
    }, 
    1016: {
        'id': 1016,
        'name': '未济势',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 311,
        'item_description': '格挡来自前方的子弹',
        'skill_full_description': '白止横刀进入格挡状态，持续期间，可消耗能量格挡来自正面的攻击。',
        'icon': 1107,
        'bag_max_stack_limit': 1,
        'has_cancel': True,
        'use_mode': 13,
        'empty': {
            8: 'hold', 
            'cache': (8, ), 
        },
        'hold': {
            8: 'using', 
        },
        'using': {
            10: 'recycle', 
        },
        'take_directly': True,
    }, 
    1017: {
        'id': 1017,
        'name': '完美萃取',
        'item_type': 101,
        'item_sub_type': 18,
        'quality': 4,
        'equip_id': 312,
        'item_description': '强化自身能力',
        'skill_full_description': '伊迪丝摄入特制试剂强化自身，持续期间，移速提升。',
        'icon': 1109,
        'bag_max_stack_limit': 2,
        'has_cancel': True,
        'use_mode': 3,
        'take_directly': True,
    }, 
    1380: {
        'id': 1380,
        'name': '正面冲突',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '冲锋枪和霰弹枪30M内交战时的伤害提升5%',
        'short_tips': '提高近战伤害',
        'icon': 10329,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2002,
    }, 
    1381: {
        'id': 1381,
        'name': '手雷改良',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '投掷物的爆炸、燃烧伤害增加30%',
        'short_tips': '增加手雷爆炸伤害',
        'icon': 10374,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2003,
    }, 
    1382: {
        'id': 1382,
        'name': '关节破坏',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '提高步枪和冲锋枪命中四肢时的伤害',
        'short_tips': '提升四肢伤害',
        'icon': 10348,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2004,
    }, 
    1383: {
        'id': 1383,
        'name': '贯穿',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '狙击枪致命伤害范围增加',
        'short_tips': '扩大狙击枪暴击范围',
        'icon': 10347,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2005,
    }, 
    1384: {
        'id': 1384,
        'name': '危机意识',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '敌人发现你时会有明显的屏幕提示',
        'short_tips': '预警敌人',
        'icon': 10323,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2006,
    }, 
    1385: {
        'id': 1385,
        'name': '脚步查看',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '可以查看敌人脚印',
        'short_tips': '查看敌人脚印',
        'icon': 10358,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2007,
    }, 
    1386: {
        'id': 1386,
        'name': '全面防护',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '减少闪光、减速、揭露等负面状态50%持续时间',
        'short_tips': '提升异常抗性',
        'icon': 10370,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2008,
    }, 
    1387: {
        'id': 1387,
        'name': '额外奖赏',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '全队完成任务的奖励提高',
        'short_tips': '任务奖励提升',
        'icon': 10344,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2009,
    }, 
    1389: {
        'id': 1389,
        'name': '充能',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '技能冷却时间减少15%',
        'short_tips': '技能冷却减少',
        'icon': 10342,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2011,
    }, 
    1390: {
        'id': 1390,
        'name': '轻装上阵',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '减少冲刺开火时间，翻越速度加快',
        'short_tips': '提高机动性',
        'icon': 10368,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2012,
    }, 
    1391: {
        'id': 1391,
        'name': '钻石手',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '初始开火后坐力降低',
        'short_tips': '降低枪械后坐力',
        'icon': 10343,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2148,
    }, 
    1393: {
        'id': 1393,
        'name': '能量反馈',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '攻击时有概率恢复护盾',
        'short_tips': '攻击恢复护盾',
        'icon': 10353,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2150,
    }, 
    1394: {
        'id': 1394,
        'name': '嗜血',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '感知一定范围内血量低于50%的敌人',
        'short_tips': '感知残血敌人',
        'icon': 10571,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2151,
    }, 
    1395: {
        'id': 1395,
        'name': '弹道分析',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '短暂显示对你发起攻击的玩家名牌',
        'short_tips': '标记攻击你的玩家',
        'icon': 10572,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2152,
    }, 
    1396: {
        'id': 1396,
        'name': '拾荒者',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '助攻或击杀敌人后，可额外获得一份战利品',
        'short_tips': '额外战利品',
        'icon': 10573,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2153,
    }, 
    1397: {
        'id': 1397,
        'name': '护盾阻断',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '使用枪械攻击命中敌人时，延缓其护盾恢复速度',
        'short_tips': '破坏护盾恢复',
        'icon': 10372,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2013,
    }, 
    1398: {
        'id': 1398,
        'name': '手雷狂人',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '可携带更多手雷',
        'short_tips': '额外投掷物',
        'icon': 10715,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2154,
    }, 
    1401: {
        'id': 1401,
        'name': '快手',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '换弹速度增加25%',
        'short_tips': '增加换弹速度',
        'icon': 10364,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2101,
    }, 
    1402: {
        'id': 1402,
        'name': '突击兵',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '使用冲锋枪增加腰射准确度和移动速度',
        'short_tips': '提高冲锋枪能力',
        'icon': 10373,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2102,
    }, 
    1403: {
        'id': 1403,
        'name': '沉稳',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '降低10%步枪、冲锋枪的后坐力',
        'short_tips': '降低后坐力',
        'icon': 10340,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2103,
    }, 
    1404: {
        'id': 1404,
        'name': '肌肉记忆',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '增加20%开镜速度',
        'short_tips': '加快开镜速度',
        'icon': 10362,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2104,
    }, 
    1405: {
        'id': 1405,
        'name': '爆炸防护',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '受到爆炸伤害减少30%',
        'short_tips': '增加爆炸抵抗',
        'icon': 10333,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2105,
    }, 
    1406: {
        'id': 1406,
        'name': '精准射击',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '腰射精度提升',
        'short_tips': '提升腰射准度',
        'icon': 10359,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2106,
    }, 
    1407: {
        'id': 1407,
        'name': '花式换弹',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '滑铲增加1发弹药',
        'short_tips': '滑铲增加弹药',
        'icon': 10352,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2107,
    }, 
    1408: {
        'id': 1408,
        'name': '惩戒',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '击倒敌人时，立刻开始护盾充能',
        'short_tips': '击杀加快恢复',
        'icon': 10338,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2108,
    }, 
    1409: {
        'id': 1409,
        'name': '乘胜追击',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '击碎敌人护甲时，提高移动速度',
        'short_tips': '破甲加快移速',
        'icon': 10339,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2109,
    }, 
    1410: {
        'id': 1410,
        'name': '保命要紧',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '自身护盾破裂时，减免20%伤害',
        'short_tips': '护盾耗尽时减伤',
        'icon': 10332,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2110,
    }, 
    1411: {
        'id': 1411,
        'name': '火力至上',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '提升腰射开火时的移动速度',
        'short_tips': '增加开火移速',
        'icon': 10357,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2111,
    }, 
    1412: {
        'id': 1412,
        'name': '步枪专家',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '步枪有效射程增加20%',
        'short_tips': '提高步枪射程',
        'icon': 10336,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2112,
    }, 
    1413: {
        'id': 1413,
        'name': '持久战斗',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '持续开火时的后座稳定性提升',
        'short_tips': '提升后坐力控制',
        'icon': 10341,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2113,
    }, 
    1414: {
        'id': 1414,
        'name': '护盾克星',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '步枪对护盾的伤害增加10%',
        'short_tips': '步枪更快破盾',
        'icon': 10355,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2114,
    }, 
    1415: {
        'id': 1415,
        'name': '致命一击',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '爆头伤害增加15%',
        'short_tips': '增加爆头伤害',
        'icon': 10330,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2115,
    }, 
    1416: {
        'id': 1416,
        'name': '百步穿杨',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '狙击枪伤害不随距离衰减',
        'short_tips': '狙击枪伤害不衰减',
        'icon': 10331,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2116,
    }, 
    1420: {
        'id': 1420,
        'name': '求生意志',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '倒地后获得50%生命值的护盾',
        'short_tips': '倒地获得护盾',
        'icon': 10369,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2120,
    }, 
    1421: {
        'id': 1421,
        'name': '强壮',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '提升10点生命值',
        'short_tips': '增加生命值',
        'icon': 10346,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2121,
    }, 
    1422: {
        'id': 1422,
        'name': '极限充能',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '护盾的回复间隔变短20%，回复速度变快33%',
        'short_tips': '护盾充能加快',
        'icon': 10363,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2122,
    }, 
    1423: {
        'id': 1423,
        'name': '跑酷达人',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '受攻击时，短暂的增加移动速度',
        'short_tips': '受伤增加移速',
        'icon': 10368,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2123,
    }, 
    1424: {
        'id': 1424,
        'name': '迷你护盾I',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '开镜时获得一个护盾',
        'short_tips': '开镜获得护盾',
        'icon': 10366,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2124,
    }, 
    1425: {
        'id': 1425,
        'name': '鬼魅',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '屏蔽信号，不会被雷达和UAV侦测',
        'short_tips': '屏蔽敌人侦察',
        'icon': 10349,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2125,
    }, 
    1426: {
        'id': 1426,
        'name': '恢复',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '生命值不满时会缓慢恢复',
        'short_tips': '缓慢恢复生命',
        'icon': 10356,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2126,
    }, 
    1427: {
        'id': 1427,
        'name': '烟雾护体',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '破盾时，脚下会产生一个烟雾弹',
        'short_tips': '受伤触发烟雾弹',
        'icon': 10328,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2127,
    }, 
    1428: {
        'id': 1428,
        'name': '声音侦查',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '声音提示范围增加 30%',
        'short_tips': '声音提示范围加大',
        'icon': 10322,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2128,
    }, 
    1429: {
        'id': 1429,
        'name': '染色标记',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '攻击敌人时，会标记敌人一段时间（队友可见）',
        'short_tips': '攻击标记敌人',
        'icon': 643,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2129,
    }, 
    1430: {
        'id': 1430,
        'name': '追踪',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '击杀敌人时，在小地图标记敌人队友的位置',
        'short_tips': '击杀标记敌人位置',
        'icon': 10365,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2130,
    }, 
    1432: {
        'id': 1432,
        'name': '超频',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '击杀/助攻获得20%技能冷却（包括被动技能）',
        'short_tips': '减少技能冷却',
        'icon': 10337,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2132,
    }, 
    1433: {
        'id': 1433,
        'name': '一换一',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '击杀你的玩家的位置将会暴露20s',
        'short_tips': '暴露敌人位置',
        'icon': 10345,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2133,
    }, 
    1434: {
        'id': 1434,
        'name': '吸收',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '击杀越多，护甲上限越高，最多25点',
        'short_tips': '杀敌增加护盾',
        'icon': 10371,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2134,
    }, 
    1435: {
        'id': 1435,
        'name': '玻璃人',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '护盾承受伤害增加50%，输出增加20%',
        'short_tips': '伤害增加，护盾减弱',
        'icon': 10335,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2135,
    }, 
    1436: {
        'id': 1436,
        'name': '后门协议',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '野外商店购买道具打85折',
        'short_tips': '道具打折',
        'icon': 10350,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2136,
    }, 
    1437: {
        'id': 1437,
        'name': '救援加速',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '救援队友的速度增加50%',
        'short_tips': '救援速度加快',
        'icon': 10367,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2137,
    }, 
    1438: {
        'id': 1438,
        'name': '细胞再生',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '治疗类道具或技能的效果增加20%',
        'short_tips': '治疗效果加强',
        'icon': 10325,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2138,
    }, 
    1439: {
        'id': 1439,
        'name': '护盾转化',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '50%护盾变成生命值',
        'short_tips': '50%护盾变成生命值',
        'icon': 10354,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2139,
    }, 
    1442: {
        'id': 1442,
        'name': '熟能生巧',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '切枪速度变快，使用道具速度加快',
        'short_tips': '加快动作速度',
        'icon': 10375,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2142,
    }, 
    1443: {
        'id': 1443,
        'name': '下方突击',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '滑铲速度变快，滑铲时腰射准确度提升',
        'short_tips': '提高滑铲速度',
        'icon': 10324,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2143,
    }, 
    1445: {
        'id': 1445,
        'name': '下落缓冲',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 2,
        'item_ground_model_id': 579,
        'item_description': '掉落伤害减少50%，不触发落地缓冲动作',
        'short_tips': '减少掉落伤害',
        'icon': 10351,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2145,
    }, 
    1446: {
        'id': 1446,
        'name': '紧急回避',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '奔跑和冲刺时获得15%伤害减免',
        'short_tips': '冲刺减少伤害',
        'icon': 10360,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2146,
    }, 
    1500: {
        'id': 1500,
        'name': '能量充沛',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '护盾持续时间增加',
        'short_tips': '增加护盾时间',
        'icon': 10406,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3000,
    }, 
    1501: {
        'id': 1501,
        'name': '护盾强化',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '护盾阻挡范围变大',
        'short_tips': '提升护盾大小',
        'icon': 10391,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3001,
    }, 
    1502: {
        'id': 1502,
        'name': '矩阵运算',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '护盾变为单向穿透，阻挡外侧子弹',
        'short_tips': '护盾可单向穿透',
        'icon': 10280,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3002,
    }, 
    1503: {
        'id': 1503,
        'name': '多重打击',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '增加导弹打击次数（提升光线持续时间和半径）',
        'short_tips': '增加打击次数',
        'icon': 10395,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3003,
    }, 
    1504: {
        'id': 1504,
        'name': '爆炸强化',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '增加导弹打击伤害（光线伤害）',
        'short_tips': '提升爆炸伤害',
        'icon': 10398,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3004,
    }, 
    1505: {
        'id': 1505,
        'name': '聚能风暴',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '导弹打击升级为聚能风暴，可以穿透建筑攻击敌人',
        'short_tips': '升级为聚能风暴',
        'icon': 10275,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3005,
    }, 
    1510: {
        'id': 1510,
        'name': '扫描充能',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '扫描时间增加',
        'short_tips': '延长扫描时间',
        'icon': 10402,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3010,
    }, 
    1511: {
        'id': 1511,
        'name': '范围强化',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '扫描范围增加',
        'short_tips': '扩大扫描范围',
        'icon': 10392,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3011,
    }, 
    1512: {
        'id': 1512,
        'name': '进阶扫描',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '扫描升级，实时显示所有玩家位置',
        'short_tips': '实时扫描全地图玩家',
        'icon': 10273,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3012,
    }, 
    1513: {
        'id': 1513,
        'name': '弹药升级',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '炮塔攻击力上升',
        'short_tips': '提升炮塔攻击力',
        'icon': 10396,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3013,
    }, 
    1514: {
        'id': 1514,
        'name': '穿透子弹',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '炮塔攻击有概率造成减速',
        'short_tips': '炮塔攻击减速',
        'icon': 10397,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3014,
    }, 
    1515: {
        'id': 1515,
        'name': '重火力炮塔',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '炮台升级为机枪炮台，拥有更快的攻击频率',
        'short_tips': '炮塔全方位强化',
        'icon': 327,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3015,
    }, 
    1516: {
        'id': 1516,
        'name': '速度强化',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '提升技能激活时的移动速度',
        'short_tips': '提升技能移动速度',
        'icon': 10401,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3016,
    }, 
    1517: {
        'id': 1517,
        'name': '临界反应',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '技能激活时减免伤害',
        'short_tips': '技能激活时减免伤害',
        'icon': 10404,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3017,
    }, 
    1518: {
        'id': 1518,
        'name': '幽灵形态',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '刺杀形态会激活隐形迷彩',
        'short_tips': '极大的增强隐蔽能力',
        'icon': 10270,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3018,
    }, 
    1519: {
        'id': 1519,
        'name': '治疗强化',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '提升治疗效率',
        'short_tips': '提高治疗效果',
        'icon': 10400,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3019,
    }, 
    1520: {
        'id': 1520,
        'name': '能量脉冲',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '无人机间隔性释放恢复20点护盾的脉冲',
        'short_tips': '激活能量脉冲',
        'icon': 10403,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3020,
    }, 
    1521: {
        'id': 1521,
        'name': '进阶无人机',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '无人机可以跟随玩家移动，持续治疗目标玩家',
        'short_tips': '无人机可跟随玩家',
        'icon': 10278,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3021,
    }, 
    1522: {
        'id': 1522,
        'name': '干扰',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '增加旋涡负面状态的持续时间',
        'short_tips': '增加致盲时间',
        'icon': 10405,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3022,
    }, 
    1523: {
        'id': 1523,
        'name': '波动',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '增加旋涡的判定范围',
        'short_tips': '提升判定范围',
        'icon': 10399,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3023,
    }, 
    1524: {
        'id': 1524,
        'name': '深渊',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '旋涡升级为深渊，在当前位置生成一个暗影区域，所有进入区域的角色视野被阻挡',
        'short_tips': '技能升级为深渊',
        'icon': 10267,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3024,
    }, 
    1525: {
        'id': 1525,
        'name': '毒性升级',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '增加毒气每秒伤害',
        'short_tips': '增加毒气伤害',
        'icon': 10393,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3025,
    }, 
    1526: {
        'id': 1526,
        'name': '毒性扩散',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '增加毒气扩散范围',
        'short_tips': '扩大毒气范围',
        'icon': 10394,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3026,
    }, 
    1527: {
        'id': 1527,
        'name': '剧毒领域',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '毒气手雷升级为毒气领域，大范围释放毒气',
        'short_tips': '技能升级为毒气领域',
        'icon': 10271,
        'icon_outline': 1,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3027,
    }, 
    1528: {
        'id': 1528,
        'name': '子弹包',
        'item_type': 115,
        'item_ground_model_id': 541,
        'item_description': '一弹匣子弹',
        'short_tips': '一弹匣子弹',
        'icon': 394,
    }, 
    1529: {
        'id': 1529,
        'name': '进化核心等级+1',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 4,
        'item_ground_model_id': 579,
        'item_description': '提升1级进化核心等级',
        'short_tips': '进化能量',
        'icon': 10482,
        'bag_max_stack_limit': 1,
    }, 
    1530: {
        'id': 1530,
        'name': '猎魂',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '击杀或助攻会永久减少技能冷却时间，每次5%，上限50%',
        'short_tips': '减少技能冷却',
        'icon': 10337,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2200,
    }, 
    1531: {
        'id': 1531,
        'name': '猎魂',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '每4次击杀可以获得一次技能充能',
        'short_tips': '减少技能冷却',
        'icon': 10337,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 2201,
    }, 
    1532: {
        'id': 1532,
        'name': '爆炸艺术',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '增加钻墙手雷的爆炸伤害',
        'short_tips': '增加爆炸伤害',
        'icon': 640,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3028,
    }, 
    1533: {
        'id': 1533,
        'name': '安可',
        'item_type': 106,
        'item_sub_type': 2,
        'quality': 3,
        'item_ground_model_id': 579,
        'item_description': '可额外存储一发钻墙手雷',
        'short_tips': '存储上限加1',
        'icon': 641,
        'bag_max_stack_limit': 1,
        'learn_talent_id': 3029,
    }, 
    10001: {
        'id': 10001,
        'skip': 'skip',
        'name': 'Glock',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 566,
        'equip_id': 15,
        'gun_blueprint_id': 55,
        'item_description': '半自动手枪，可靠的副武器',
        'icon': 736,
        'icon_outline': 177,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    10002: {
        'id': 10002,
        'skip': 'skip',
        'name': 'Glock',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 566,
        'equip_id': 15,
        'gun_blueprint_id': 4002,
        'item_description': '半自动手枪，可靠的副武器',
        'icon': 651,
        'icon_outline': 177,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    10003: {
        'id': 10003,
        'skip': 'skip',
        'name': 'Glock',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 566,
        'equip_id': 15,
        'gun_blueprint_id': 4003,
        'item_description': '半自动手枪，可靠的副武器',
        'icon': 652,
        'icon_outline': 177,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    10004: {
        'id': 10004,
        'skip': 'skip',
        'name': 'Glock',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 566,
        'equip_id': 15,
        'gun_blueprint_id': 4004,
        'item_description': '半自动手枪，可靠的副武器',
        'icon': 653,
        'icon_outline': 177,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    10005: {
        'id': 10005,
        'skip': 'skip',
        'name': 'Desert Eagle',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 572,
        'equip_id': 74,
        'gun_blueprint_id': 100015,
        'item_description': '发射.50子弹的重型手枪，子弹威力大',
        'icon': 742,
        'icon_outline': 326,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    10006: {
        'id': 10006,
        'skip': 'skip',
        'name': 'Desert Eagle',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 572,
        'equip_id': 74,
        'gun_blueprint_id': 15003,
        'item_description': '发射.50子弹的重型手枪，子弹威力大',
        'icon': 687,
        'icon_outline': 326,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    10007: {
        'id': 10007,
        'skip': 'skip',
        'name': 'Desert Eagle',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 572,
        'equip_id': 74,
        'gun_blueprint_id': 15004,
        'item_description': '发射.50子弹的重型手枪，子弹威力大',
        'icon': 688,
        'icon_outline': 326,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    10008: {
        'id': 10008,
        'skip': 'skip',
        'name': 'Desert Eagle',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 572,
        'equip_id': 74,
        'gun_blueprint_id': 15005,
        'item_description': '发射.50子弹的重型手枪，子弹威力大',
        'icon': 689,
        'icon_outline': 326,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    11001: {
        'id': 11001,
        'skip': 'skip',
        'name': 'Origin12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6001,
        'item_description': '高射速连发霰弹枪，近距离杀伤力极强',
        'icon': 737,
        'icon_outline': 214,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    11002: {
        'id': 11002,
        'skip': 'skip',
        'name': 'Origin12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6005,
        'item_description': '高射速连发霰弹枪，近距离杀伤力极强',
        'icon': 657,
        'icon_outline': 214,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    11003: {
        'id': 11003,
        'skip': 'skip',
        'name': 'Origin12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6006,
        'item_description': '高射速连发霰弹枪，近距离杀伤力极强',
        'icon': 658,
        'icon_outline': 214,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    11004: {
        'id': 11004,
        'skip': 'skip',
        'name': 'Origin12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6007,
        'item_description': '高射速连发霰弹枪，近距离杀伤力极强',
        'icon': 659,
        'icon_outline': 214,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    11005: {
        'id': 11005,
        'skip': 'skip',
        'name': 'M870',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 100020,
        'item_description': '半自动智能霰弹枪，经过合适的改装也可胜任中近距离的作战情景',
        'icon': 745,
        'icon_outline': 321,
    }, 
    11006: {
        'id': 11006,
        'skip': 'skip',
        'name': 'M870',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 20004,
        'item_description': '半自动智能霰弹枪，经过合适的改装也可胜任中近距离的作战情景',
        'icon': 711,
        'icon_outline': 321,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    11007: {
        'id': 11007,
        'skip': 'skip',
        'name': 'M870',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 20005,
        'item_description': '半自动智能霰弹枪，经过合适的改装也可胜任中近距离的作战情景',
        'icon': 712,
        'icon_outline': 321,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    11008: {
        'id': 11008,
        'skip': 'skip',
        'name': 'M870',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 20006,
        'item_description': '半自动智能霰弹枪，经过合适的改装也可胜任中近距离的作战情景',
        'icon': 713,
        'icon_outline': 321,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12001: {
        'id': 12001,
        'skip': 'skip',
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8001,
        'item_description': '射速极快的冲锋枪，在枪手的精准操控下非常致命',
        'icon': 738,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12002: {
        'id': 12002,
        'skip': 'skip',
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8007,
        'item_description': '射速极快的冲锋枪，在枪手的精准操控下非常致命',
        'icon': 664,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12003: {
        'id': 12003,
        'skip': 'skip',
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8008,
        'item_description': '射速极快的冲锋枪，在枪手的精准操控下非常致命',
        'icon': 665,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12004: {
        'id': 12004,
        'skip': 'skip',
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 568,
        'equip_id': 38,
        'gun_blueprint_id': 8009,
        'item_description': '射速极快的冲锋枪，在枪手的精准操控下非常致命',
        'icon': 666,
        'icon_outline': 220,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12005: {
        'id': 12005,
        'skip': 'skip',
        'name': 'URB',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16001,
        'item_description': '射速较慢的冲锋枪，但子弹威力衰减弱，适用于多种交战场合',
        'icon': 743,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12006: {
        'id': 12006,
        'skip': 'skip',
        'name': 'URB',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16007,
        'item_description': '射速较慢的冲锋枪，但子弹威力衰减弱，适用于多种交战场合',
        'icon': 694,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12007: {
        'id': 12007,
        'skip': 'skip',
        'name': 'URB',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16008,
        'item_description': '射速较慢的冲锋枪，但子弹威力衰减弱，适用于多种交战场合',
        'icon': 695,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12008: {
        'id': 12008,
        'skip': 'skip',
        'name': 'URB',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 573,
        'equip_id': 75,
        'gun_blueprint_id': 16009,
        'item_description': '射速较慢的冲锋枪，但子弹威力衰减弱，适用于多种交战场合',
        'icon': 696,
        'icon_outline': 322,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12009: {
        'id': 12009,
        'skip': 'skip',
        'name': 'INP-9',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17001,
        'item_description': '射速中等的冲锋枪，性能可靠，适用于中近距离作战',
        'icon': 748,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12010: {
        'id': 12010,
        'skip': 'skip',
        'name': 'INP-9',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17007,
        'item_description': '射速中等的冲锋枪，性能可靠，适用于中近距离作战',
        'icon': 701,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12011: {
        'id': 12011,
        'skip': 'skip',
        'name': 'INP-9',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17008,
        'item_description': '射速中等的冲锋枪，性能可靠，适用于中近距离作战',
        'icon': 702,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    12012: {
        'id': 12012,
        'skip': 'skip',
        'name': 'INP-9',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 574,
        'equip_id': 76,
        'gun_blueprint_id': 17009,
        'item_description': '射速中等的冲锋枪，性能可靠，适用于中近距离作战',
        'icon': 703,
        'icon_outline': 323,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13001: {
        'id': 13001,
        'skip': 'skip',
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1001,
        'item_description': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力',
        'icon': 735,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13002: {
        'id': 13002,
        'skip': 'skip',
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1008,
        'item_description': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力',
        'icon': 648,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13003: {
        'id': 13003,
        'skip': 'skip',
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1009,
        'item_description': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力',
        'icon': 649,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13004: {
        'id': 13004,
        'skip': 'skip',
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1010,
        'item_description': '全自动步枪，适用场景全面、改装自由度高，在中距离拥有极高的作战能力',
        'icon': 650,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13005: {
        'id': 13005,
        'skip': 'skip',
        'name': 'AK47',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10001,
        'item_description': '耐用性极强的全自动步枪，射速慢但子弹威力大，适合中远距离',
        'icon': 739,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13006: {
        'id': 13006,
        'skip': 'skip',
        'name': 'AK47',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10008,
        'item_description': '耐用性极强的全自动步枪，射速慢但子弹威力大，适合中远距离',
        'icon': 671,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13007: {
        'id': 13007,
        'skip': 'skip',
        'name': 'AK47',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10009,
        'item_description': '耐用性极强的全自动步枪，射速慢但子弹威力大，适合中远距离',
        'icon': 672,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13008: {
        'id': 13008,
        'skip': 'skip',
        'name': 'AK47',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10010,
        'item_description': '耐用性极强的全自动步枪，射速慢但子弹威力大，适合中远距离',
        'icon': 673,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13009: {
        'id': 13009,
        'skip': 'skip',
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14001,
        'item_description': '全自动步枪，射速快、易控制，非常适合远距离击杀敌人',
        'icon': 741,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13010: {
        'id': 13010,
        'skip': 'skip',
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14008,
        'item_description': '全自动步枪，射速快、易控制，非常适合远距离击杀敌人',
        'icon': 683,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13011: {
        'id': 13011,
        'skip': 'skip',
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14009,
        'item_description': '全自动步枪，射速快、易控制，非常适合远距离击杀敌人',
        'icon': 684,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13012: {
        'id': 13012,
        'skip': 'skip',
        'name': 'KAG-6',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 571,
        'equip_id': 72,
        'gun_blueprint_id': 14010,
        'item_description': '全自动步枪，射速快、易控制，非常适合远距离击杀敌人',
        'icon': 685,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    13013: {
        'id': 13013,
        'skip': 'skip',
        'name': 'SCAR',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23001,
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境',
        'icon': 749,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
    }, 
    13014: {
        'id': 13014,
        'skip': 'skip',
        'name': 'SCAR',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23002,
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境',
        'icon': 715,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
    }, 
    13015: {
        'id': 13015,
        'skip': 'skip',
        'name': 'SCAR',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23003,
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境',
        'icon': 716,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
    }, 
    13016: {
        'id': 13016,
        'skip': 'skip',
        'name': 'SCAR',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23004,
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境',
        'icon': 717,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
    }, 
    14001: {
        'id': 14001,
        'skip': 'skip',
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'auto_mark': 1,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 100018,
        'item_description': '轻型狙击步枪，机动性强，命中头部一发致命',
        'icon': 744,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14002: {
        'id': 14002,
        'skip': 'skip',
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18005,
        'item_description': '轻型狙击步枪，机动性强，命中头部一发致命',
        'icon': 706,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14003: {
        'id': 14003,
        'skip': 'skip',
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18006,
        'item_description': '轻型狙击步枪，机动性强，命中头部一发致命',
        'icon': 707,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14004: {
        'id': 14004,
        'skip': 'skip',
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18007,
        'item_description': '轻型狙击步枪，机动性强，命中头部一发致命',
        'icon': 708,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14005: {
        'id': 14005,
        'skip': 'skip',
        'name': 'Kala',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 100013,
        'item_description': '重型狙击步枪，机动性差，但子弹威力大，命中头部一发致命',
        'icon': 740,
        'icon_outline': 266,
    }, 
    14006: {
        'id': 14006,
        'skip': 'skip',
        'name': 'Kala',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 13004,
        'item_description': '重型狙击步枪，机动性差，但子弹威力大，命中头部一发致命',
        'icon': 676,
        'icon_outline': 266,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14007: {
        'id': 14007,
        'skip': 'skip',
        'name': 'Kala',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 13005,
        'item_description': '重型狙击步枪，机动性差，但子弹威力大，命中头部一发致命',
        'icon': 677,
        'icon_outline': 266,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14008: {
        'id': 14008,
        'skip': 'skip',
        'name': 'Kala',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 570,
        'equip_id': 71,
        'gun_blueprint_id': 13006,
        'item_description': '重型狙击步枪，机动性差，但子弹威力大，命中头部一发致命',
        'icon': 678,
        'icon_outline': 266,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14009: {
        'id': 14009,
        'skip': 'skip',
        'name': '复合弓',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'auto_mark': 1,
        'item_ground_model_id': 582,
        'equip_id': 83,
        'gun_blueprint_id': 100022,
        'item_description': '复合弓',
        'icon': 747,
        'icon_outline': 524,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14010: {
        'id': 14010,
        'skip': 'skip',
        'name': '复合弓',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'auto_mark': 1,
        'item_ground_model_id': 582,
        'equip_id': 83,
        'gun_blueprint_id': 100022,
        'item_description': '复合弓',
        'icon': 747,
        'icon_outline': 524,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14011: {
        'id': 14011,
        'skip': 'skip',
        'name': '复合弓',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 582,
        'equip_id': 83,
        'gun_blueprint_id': 100022,
        'item_description': '复合弓',
        'icon': 747,
        'icon_outline': 524,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14012: {
        'id': 14012,
        'skip': 'skip',
        'name': '复合弓',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 582,
        'equip_id': 83,
        'gun_blueprint_id': 100022,
        'item_description': '复合弓',
        'icon': 747,
        'icon_outline': 524,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    14013: {
        'id': 14013,
        'skip': 'skip',
        'name': 'STI2011',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 6,
        'item_ground_model_id': 566,
        'equip_id': 402,
        'gun_blueprint_id': 100042,
        'item_description': '自定义枪械',
        'icon': 736,
        'icon_outline': 1088,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    14014: {
        'id': 14014,
        'skip': 'skip',
        'name': 'STI2011',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 566,
        'equip_id': 402,
        'gun_blueprint_id': 100042,
        'item_description': '原装STI2011手枪',
        'icon': 736,
        'icon_outline': 1088,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    100101: {
        'id': 100101,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1001,
        'item_description': '原装M4A1步枪',
        'icon': 735,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    100102: {
        'id': 100102,
        'name': 'M4A1',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1002,
        'item_description': '装备全息瞄具的M4A1步枪',
        'icon': 645,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    100103: {
        'id': 100103,
        'name': 'M4A1 - 中距离',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1003,
        'item_description': '装备全息镜，强化中距离作战能力',
        'icon': 646,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    100104: {
        'id': 100104,
        'name': 'M4A1 - 近距离强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 565,
        'equip_id': 1,
        'gun_blueprint_id': 1004,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 647,
        'icon_outline': 325,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    100201: {
        'id': 100201,
        'name': 'MP5',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 618,
        'equip_id': 2,
        'gun_blueprint_id': 100002,
        'item_description': '原装MP5',
        'icon': 1031,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    100202: {
        'id': 100202,
        'name': 'MP5 - 全息',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 618,
        'equip_id': 2,
        'gun_blueprint_id': 200201,
        'item_description': '装备全息镜，强化中距离作战能力',
        'icon': 1031,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    100203: {
        'id': 100203,
        'name': 'MP5 - 轻量级',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 618,
        'equip_id': 2,
        'gun_blueprint_id': 200202,
        'item_description': '腰射精准，强化近距离作战能力',
        'icon': 1031,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    100204: {
        'id': 100204,
        'name': 'MP5 - 腰射满改',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 618,
        'equip_id': 2,
        'gun_blueprint_id': 200203,
        'item_description': '加装镭射和50发弹鼓，最大化近距离作战能力',
        'icon': 1031,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    100601: {
        'id': 100601,
        'name': 'Origin12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 100006,
        'item_description': '原装Origin12',
        'icon': 343,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    100602: {
        'id': 100602,
        'name': 'Origin12 - 扩容',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6002,
        'item_description': '加装扩容弹匣，提升火力持续性',
        'icon': 343,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    100603: {
        'id': 100603,
        'name': 'Origin12 - 中近距离作战',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6014,
        'item_description': '加装延长枪管，提升近距离作战可靠性',
        'icon': 343,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    100604: {
        'id': 100604,
        'name': 'Origin12 - 腰射满改',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 567,
        'equip_id': 34,
        'gun_blueprint_id': 6003,
        'item_description': '满改Origin12，最大化近距离作战能力',
        'icon': 343,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    100801: {
        'id': 100801,
        'name': 'Vector',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 646,
        'equip_id': 38,
        'gun_blueprint_id': 100008,
        'item_description': '原装Vector',
        'icon': 344,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    100802: {
        'id': 100802,
        'name': 'Vector - 全息',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 646,
        'equip_id': 38,
        'gun_blueprint_id': 8002,
        'item_description': '安装了全息瞄具',
        'icon': 344,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    100803: {
        'id': 100803,
        'name': 'Vector - 轻量级',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 646,
        'equip_id': 38,
        'gun_blueprint_id': 8003,
        'item_description': '弹容量提升',
        'icon': 344,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    100804: {
        'id': 100804,
        'name': 'Vector - 腰射满改',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 646,
        'equip_id': 38,
        'gun_blueprint_id': 8004,
        'item_description': '最大化近距离作战能力',
        'icon': 344,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    101001: {
        'id': 101001,
        'name': 'AK12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10001,
        'item_description': '原装AK12步枪',
        'icon': 861,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    101002: {
        'id': 101002,
        'name': 'AK12',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10002,
        'item_description': '强化了近距离作战能力的AK12步枪',
        'icon': 861,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    101003: {
        'id': 101003,
        'name': 'AK12-中距离',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10003,
        'item_description': '装备了扩容弹匣的AK12步枪',
        'icon': 861,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    101004: {
        'id': 101004,
        'name': 'AK12-远距离强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 569,
        'equip_id': 40,
        'gun_blueprint_id': 10004,
        'item_description': '强化了远距离作战能力的AK12步枪',
        'icon': 861,
        'icon_outline': 174,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    101501: {
        'id': 101501,
        'name': 'Desert Eagle',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 572,
        'equip_id': 74,
        'gun_blueprint_id': 100015,
        'item_description': '发射.50子弹的重型手枪，子弹威力大',
        'icon': 742,
        'icon_outline': 326,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    101801: {
        'id': 101801,
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 100018,
        'item_description': '原装M700狙击枪',
        'icon': 704,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    101802: {
        'id': 101802,
        'name': 'M700 - 轻量化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18001,
        'item_description': '适合中距离作战的M700狙击枪',
        'icon': 704,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    101803: {
        'id': 101803,
        'name': 'M700 - 远距离',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18002,
        'item_description': '最大化远距离作战能力的M700狙击枪',
        'icon': 705,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    101804: {
        'id': 101804,
        'name': 'M700',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 575,
        'equip_id': 77,
        'gun_blueprint_id': 18003,
        'item_description': '安装了消音器的M700狙击枪',
        'icon': 705,
        'icon_outline': 324,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    102001: {
        'id': 102001,
        'name': 'M870 - 扩容弹匣',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 20001,
        'item_description': '装备扩容弹匣的M870霰弹枪',
        'icon': 709,
        'icon_outline': 321,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    102002: {
        'id': 102002,
        'name': 'M870 - 开镜强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 576,
        'equip_id': 79,
        'gun_blueprint_id': 20002,
        'item_description': '强化开镜命中率的M870霰弹枪',
        'icon': 710,
        'icon_outline': 321,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    102301: {
        'id': 102301,
        'name': 'SCAR',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23005,
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境',
        'icon': 749,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    102302: {
        'id': 102302,
        'name': 'SCAR - 2倍镜',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23006,
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境',
        'icon': 715,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    102303: {
        'id': 102303,
        'name': 'SCAR - 近战强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 588,
        'equip_id': 88,
        'gun_blueprint_id': 23007,
        'item_description': '装备大口径子弹、射速较慢的全自动步枪，单发子弹威力强劲，适用中距离作战环境',
        'icon': 716,
        'icon_outline': 611,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    102401: {
        'id': 102401,
        'name': 'P90',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 591,
        'equip_id': 90,
        'gun_blueprint_id': 24010,
        'item_description': '原装P90冲锋枪',
        'icon': 770,
        'icon_outline': 763,
        'mark_voice_id': (48, ),
    }, 
    102402: {
        'id': 102402,
        'name': 'P90 - 反射瞄具',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 591,
        'equip_id': 90,
        'gun_blueprint_id': 24011,
        'item_description': 'P90',
        'icon': 770,
        'icon_outline': 763,
        'mark_voice_id': (49, ),
    }, 
    102403: {
        'id': 102403,
        'name': 'P90 - 短枪管',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 591,
        'equip_id': 90,
        'gun_blueprint_id': 24012,
        'item_description': 'P90',
        'icon': 770,
        'icon_outline': 763,
        'mark_voice_id': (50, ),
    }, 
    102501: {
        'id': 102501,
        'name': 'VSS',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 100025,
        'item_description': '原装VSS',
        'icon': 813,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    102502: {
        'id': 102502,
        'name': 'VSS - 中程强化',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25009,
        'item_description': '加装2.0x Hamr瞄准镜，提升中距离作战能力',
        'icon': 813,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    102503: {
        'id': 102503,
        'name': 'VSS - 扩容',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25010,
        'item_description': '20发扩容弹匣，提升火力持续性',
        'icon': 813,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    102504: {
        'id': 102504,
        'name': 'VSS - 远距离满改',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'auto_mark': 1,
        'item_ground_model_id': 602,
        'equip_id': 91,
        'gun_blueprint_id': 25011,
        'item_description': '加装狙击枪托和特种战术枪管，最大限度提高远距离作战能力',
        'icon': 813,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
    }, 
    102901: {
        'id': 102901,
        'name': 'AUG',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 646,
        'equip_id': 98,
        'gun_blueprint_id': 100029,
        'item_description': '原装AUG',
        'icon': 216,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    102902: {
        'id': 102902,
        'name': 'AUG - 扩容弹匣',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 646,
        'equip_id': 98,
        'gun_blueprint_id': 202901,
        'item_description': '安装了扩容弹匣的AUG',
        'icon': 216,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    102903: {
        'id': 102903,
        'name': 'AUG',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 646,
        'equip_id': 98,
        'gun_blueprint_id': 202902,
        'item_description': '强化了近距离作战能力的AUG',
        'icon': 216,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    102904: {
        'id': 102904,
        'name': 'AUG',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 4,
        'item_ground_model_id': 646,
        'equip_id': 98,
        'gun_blueprint_id': 202903,
        'item_description': '均衡的AUG',
        'icon': 216,
        'icon_outline': 217,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    103901: {
        'id': 103901,
        'name': 'QBZ95',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 3991,
        'equip_id': 121,
        'gun_blueprint_id': 100039,
        'item_description': '原装QBZ95步枪',
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    103902: {
        'id': 103902,
        'name': 'QBZ95',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'item_ground_model_id': 3991,
        'equip_id': 121,
        'gun_blueprint_id': 203901,
        'item_description': '优化了后坐力控制的QBZ95步枪',
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    104001: {
        'id': 104001,
        'name': 'Uzi',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 3868,
        'equip_id': 110,
        'gun_blueprint_id': 40010,
        'item_description': 'Uzi',
        'icon': 110097,
        'icon_outline': 110099,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    104002: {
        'id': 104002,
        'name': 'Uzi-远程',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 2,
        'item_ground_model_id': 3868,
        'equip_id': 110,
        'gun_blueprint_id': 40011,
        'item_description': 'Uzi',
        'icon': 110097,
        'icon_outline': 110099,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    104003: {
        'id': 104003,
        'name': 'Uzi-特种作战',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 3868,
        'equip_id': 110,
        'gun_blueprint_id': 40012,
        'item_description': 'Uzi',
        'icon': 110097,
        'icon_outline': 110099,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    104004: {
        'id': 104004,
        'name': 'Uzi-战术',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 3,
        'auto_mark': 1,
        'item_ground_model_id': 3868,
        'equip_id': 110,
        'gun_blueprint_id': 40013,
        'item_description': 'Uzi',
        'icon': 110097,
        'icon_outline': 110099,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    104101: {
        'id': 104101,
        'name': 'STI2011',
        'item_type': 101,
        'item_sub_type': 1,
        'quality': 1,
        'item_ground_model_id': 566,
        'equip_id': 402,
        'gun_blueprint_id': 100042,
        'item_description': '原装STI2011手枪',
        'icon': 736,
        'icon_outline': 1088,
        'icon_outline_2': 799,
        'bag_max_stack_limit': 1,
        'ground_max_stack_limit': 1,
        'mark_voice_id': (48, ),
    }, 
    400004: {
        'id': 400004,
        'skip': 'skip',
        'name': 'x化合物',
        'item_type': 101,
        'item_sub_type': 17,
        'quality': 3,
        'item_ground_model_id': 524,
        'equip_id': 300,
        'item_description': '准备阶段使用，使用后随机获得一个天赋',
        'short_tips': '使用后随机获得天赋',
        'icon': 10482,
        'bag_max_stack_limit': 10,
        'can_drop': False,
        'operate_mode': 1,
        'use_mode': 4,
        'empty': {
            1: 'hold', 
            'cache': (1, ), 
        },
        'hold': {
            1: 'using', 
        },
    }, 
}
