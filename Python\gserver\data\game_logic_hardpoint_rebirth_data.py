# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 80-热点战玩法表.xlsx, sheetname:热点_复活点配置
_reload_all = True

data = {
    2: {
        1: {
            'id': '2.1',
            'space_id': 2,
            'local_id': 1,
            'pos': (161.0, 6.5, 265.0, -3.1, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '2.2',
            'space_id': 2,
            'local_id': 2,
            'pos': (162.0, 6.5, 265.0, -3.1, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '2.3',
            'space_id': 2,
            'local_id': 3,
            'pos': (163.0, 6.5, 265.0, -3.1, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '2.4',
            'space_id': 2,
            'local_id': 4,
            'pos': (164.0, 6.5, 265.0, -3.1, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '2.5',
            'space_id': 2,
            'local_id': 5,
            'pos': (165.0, 6.5, 265.0, -3.1, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '2.6',
            'space_id': 2,
            'local_id': 6,
            'pos': (187.0, 0.1, 184.0, 0.0, ),
            'team_first_birth': 2,
        }, 
        7: {
            'id': '2.7',
            'space_id': 2,
            'local_id': 7,
            'pos': (188.0, 0.1, 184.0, 0.0, ),
            'team_first_birth': 2,
        }, 
        8: {
            'id': '2.8',
            'space_id': 2,
            'local_id': 8,
            'pos': (191.0, 0.1, 184.0, 0.0, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '2.9',
            'space_id': 2,
            'local_id': 9,
            'pos': (189.0, 0.1, 184.0, 0.0, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '2.10',
            'space_id': 2,
            'local_id': 10,
            'pos': (190.0, 0.1, 184.0, 0.0, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '2.11',
            'space_id': 2,
            'local_id': 11,
            'pos': (129.0, 4.0, 214.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        12: {
            'id': '2.12',
            'space_id': 2,
            'local_id': 12,
            'pos': (193.0, 3.5, 200.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        13: {
            'id': '2.13',
            'space_id': 2,
            'local_id': 13,
            'pos': (127.5, 6.5, 257.5, 1.6, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '2.14',
            'space_id': 2,
            'local_id': 14,
            'pos': (131.5, 4.0, 228.4, 1.6, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '2.15',
            'space_id': 2,
            'local_id': 15,
            'pos': (139.2, 3.5, 201.5, -3.1, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '2.16',
            'space_id': 2,
            'local_id': 16,
            'pos': (132.0, 7.2, 166.9, 0.0, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '2.17',
            'space_id': 2,
            'local_id': 17,
            'pos': (146.0, 3.5, 174.3, -0.8, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '2.18',
            'space_id': 2,
            'local_id': 18,
            'pos': (151.8, 3.5, 176.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '2.19',
            'space_id': 2,
            'local_id': 19,
            'pos': (208.2, 6.5, 170.5, 0.0, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '2.20',
            'space_id': 2,
            'local_id': 20,
            'pos': (224.7, 3.5, 191.6, 0.0, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '2.21',
            'space_id': 2,
            'local_id': 21,
            'pos': (224.9, 5.0, 232.6, -1.6, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '2.22',
            'space_id': 2,
            'local_id': 22,
            'pos': (223.6, 5.0, 218.3, -1.6, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '2.23',
            'space_id': 2,
            'local_id': 23,
            'pos': (190.3, 3.5, 232.9, -1.6, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '2.24',
            'space_id': 2,
            'local_id': 24,
            'pos': (163.7, 3.5, 230.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '2.25',
            'space_id': 2,
            'local_id': 25,
            'pos': (178.2, 3.5, 256.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '2.26',
            'space_id': 2,
            'local_id': 26,
            'pos': (186.0, 3.5, 251.7, 1.6, ),
            'team_first_birth': 0,
        }, 
        27: {
            'id': '2.27',
            'space_id': 2,
            'local_id': 27,
            'pos': (188.0, 3.5, 264.7, -3.1, ),
            'team_first_birth': 0,
        }, 
        28: {
            'id': '2.28',
            'space_id': 2,
            'local_id': 28,
            'pos': (199.7, 3.5, 262.7, -3.1, ),
            'team_first_birth': 0,
        }, 
        29: {
            'id': '2.29',
            'space_id': 2,
            'local_id': 29,
            'pos': (166.0, 0.1, 180.1, 0.0, ),
            'team_first_birth': 0,
        }, 
        30: {
            'id': '2.30',
            'space_id': 2,
            'local_id': 30,
            'pos': (197.7, 3.5, 213.9, 0.0, ),
            'team_first_birth': 0,
        }, 
        31: {
            'id': '2.31',
            'space_id': 2,
            'local_id': 31,
            'pos': (208.1, 3.5, 235.7, -3.1, ),
            'team_first_birth': 0,
        }, 
        32: {
            'id': '2.32',
            'space_id': 2,
            'local_id': 32,
            'pos': (144.9, 3.5, 244.8, -3.1, ),
            'team_first_birth': 0,
        }, 
        33: {
            'id': '2.33',
            'space_id': 2,
            'local_id': 33,
            'pos': (152.2, 0.5, 207.9, 1.6, ),
            'team_first_birth': 0,
        }, 
        34: {
            'id': '2.34',
            'space_id': 2,
            'local_id': 34,
            'pos': (174.3, 0.1, 206.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        35: {
            'id': '2.35',
            'space_id': 2,
            'local_id': 35,
            'pos': (174.1, 0.1, 199.3, -1.6, ),
            'team_first_birth': 0,
        }, 
    }, 
    17: {
        1: {
            'id': '17.1',
            'space_id': 17,
            'local_id': 1,
            'pos': (43.2, 0.0, 0.0, -1.5, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '17.2',
            'space_id': 17,
            'local_id': 2,
            'pos': (43.2, 0.0, 4.0, -1.1, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '17.3',
            'space_id': 17,
            'local_id': 3,
            'pos': (39.2, 0.0, -4.0, -1.5, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '17.4',
            'space_id': 17,
            'local_id': 4,
            'pos': (39.2, 0.0, 0.0, -1.5, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '17.5',
            'space_id': 17,
            'local_id': 5,
            'pos': (39.2, 0.0, 4.0, -0.8, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '17.6',
            'space_id': 17,
            'local_id': 6,
            'pos': (43.3, 0.0, -4.0, -1.5, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '17.7',
            'space_id': 17,
            'local_id': 7,
            'pos': (-37.2, 0.0, 4.0, 1.8, ),
            'team_first_birth': 2,
        }, 
        8: {
            'id': '17.8',
            'space_id': 17,
            'local_id': 8,
            'pos': (-41.2, 0.0, 4.0, 1.8, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '17.9',
            'space_id': 17,
            'local_id': 9,
            'pos': (-41.2, 0.0, 0.0, 1.7, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '17.10',
            'space_id': 17,
            'local_id': 10,
            'pos': (-37.2, 0.0, 0.0, 1.7, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '17.11',
            'space_id': 17,
            'local_id': 11,
            'pos': (-37.2, 0.0, -4.0, 2.4, ),
            'team_first_birth': 2,
        }, 
        12: {
            'id': '17.12',
            'space_id': 17,
            'local_id': 12,
            'pos': (-41.2, 0.0, -4.0, 2.1, ),
            'team_first_birth': 2,
        }, 
        13: {
            'id': '17.13',
            'space_id': 17,
            'local_id': 13,
            'pos': (45.0, 0.0, -11.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '17.14',
            'space_id': 17,
            'local_id': 14,
            'pos': (37.0, 0.0, -18.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '17.15',
            'space_id': 17,
            'local_id': 15,
            'pos': (45.0, 0.0, -9.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '17.16',
            'space_id': 17,
            'local_id': 16,
            'pos': (45.0, 0.0, -13.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '17.17',
            'space_id': 17,
            'local_id': 17,
            'pos': (39.0, 0.0, 17.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '17.18',
            'space_id': 17,
            'local_id': 18,
            'pos': (42.2, 0.0, 17.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '17.19',
            'space_id': 17,
            'local_id': 19,
            'pos': (45.0, 0.0, 17.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '17.20',
            'space_id': 17,
            'local_id': 20,
            'pos': (32.0, 0.0, 9.0, -0.0, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '17.21',
            'space_id': 17,
            'local_id': 21,
            'pos': (36.0, 0.0, 3.0, -0.0, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '17.22',
            'space_id': 17,
            'local_id': 22,
            'pos': (31.0, 0.0, -3.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '17.23',
            'space_id': 17,
            'local_id': 23,
            'pos': (34.0, 0.0, -3.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '17.24',
            'space_id': 17,
            'local_id': 24,
            'pos': (31.0, 0.0, -8.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '17.25',
            'space_id': 17,
            'local_id': 25,
            'pos': (34.0, 0.0, -8.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '17.26',
            'space_id': 17,
            'local_id': 26,
            'pos': (47.0, 0.0, -4.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        27: {
            'id': '17.27',
            'space_id': 17,
            'local_id': 27,
            'pos': (49.0, 0.0, -7.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        28: {
            'id': '17.28',
            'space_id': 17,
            'local_id': 28,
            'pos': (17.0, 0.0, -12.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        29: {
            'id': '17.29',
            'space_id': 17,
            'local_id': 29,
            'pos': (19.0, 0.0, -12.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        30: {
            'id': '17.30',
            'space_id': 17,
            'local_id': 30,
            'pos': (17.0, 0.0, -14.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        31: {
            'id': '17.31',
            'space_id': 17,
            'local_id': 31,
            'pos': (-8.0, 0.0, -19.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        32: {
            'id': '17.32',
            'space_id': 17,
            'local_id': 32,
            'pos': (-11.0, 0.0, -19.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        33: {
            'id': '17.33',
            'space_id': 17,
            'local_id': 33,
            'pos': (-13.0, 0.0, -11.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        34: {
            'id': '17.34',
            'space_id': 17,
            'local_id': 34,
            'pos': (-18.0, 0.0, -11.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        35: {
            'id': '17.35',
            'space_id': 17,
            'local_id': 35,
            'pos': (-32.0, 0.0, -11.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        36: {
            'id': '17.36',
            'space_id': 17,
            'local_id': 36,
            'pos': (-32.0, 0.0, 5.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        37: {
            'id': '17.37',
            'space_id': 17,
            'local_id': 37,
            'pos': (-32.0, 0.0, 17.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        38: {
            'id': '17.38',
            'space_id': 17,
            'local_id': 38,
            'pos': (-48.0, 0.0, 17.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        39: {
            'id': '17.39',
            'space_id': 17,
            'local_id': 39,
            'pos': (-41.0, 0.0, 14.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        40: {
            'id': '17.40',
            'space_id': 17,
            'local_id': 40,
            'pos': (-43.0, 0.0, 13.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        41: {
            'id': '17.41',
            'space_id': 17,
            'local_id': 41,
            'pos': (-44.0, 0.0, -13.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        42: {
            'id': '17.42',
            'space_id': 17,
            'local_id': 42,
            'pos': (-43.0, 0.0, -15.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        43: {
            'id': '17.43',
            'space_id': 17,
            'local_id': 43,
            'pos': (-45.0, 0.0, -19.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        44: {
            'id': '17.44',
            'space_id': 17,
            'local_id': 44,
            'pos': (48.0, 0.0, 2.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        45: {
            'id': '17.45',
            'space_id': 17,
            'local_id': 45,
            'pos': (-30.0, 4.5, 14.0, 1.8, ),
            'team_first_birth': 0,
        }, 
        46: {
            'id': '17.46',
            'space_id': 17,
            'local_id': 46,
            'pos': (-30.0, 4.5, 7.0, 1.8, ),
            'team_first_birth': 0,
        }, 
        47: {
            'id': '17.47',
            'space_id': 17,
            'local_id': 47,
            'pos': (-30.0, 4.5, 4.0, 1.8, ),
            'team_first_birth': 0,
        }, 
        48: {
            'id': '17.48',
            'space_id': 17,
            'local_id': 48,
            'pos': (-30.0, 4.5, -12.0, 1.8, ),
            'team_first_birth': 0,
        }, 
        49: {
            'id': '17.49',
            'space_id': 17,
            'local_id': 49,
            'pos': (22.0, 1.0, -1.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        50: {
            'id': '17.50',
            'space_id': 17,
            'local_id': 50,
            'pos': (22.0, 1.0, 2.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        51: {
            'id': '17.51',
            'space_id': 17,
            'local_id': 51,
            'pos': (22.0, 1.0, 5.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        52: {
            'id': '17.52',
            'space_id': 17,
            'local_id': 52,
            'pos': (16.0, 1.0, 2.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        53: {
            'id': '17.53',
            'space_id': 17,
            'local_id': 53,
            'pos': (22.0, 1.0, 14.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        54: {
            'id': '17.54',
            'space_id': 17,
            'local_id': 54,
            'pos': (-8.0, 0.0, 2.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        55: {
            'id': '17.55',
            'space_id': 17,
            'local_id': 55,
            'pos': (-21.0, 0.0, 11.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        56: {
            'id': '17.56',
            'space_id': 17,
            'local_id': 56,
            'pos': (-29.0, 0.0, 11.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        57: {
            'id': '17.57',
            'space_id': 17,
            'local_id': 57,
            'pos': (-26.0, 0.0, 11.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        58: {
            'id': '17.58',
            'space_id': 17,
            'local_id': 58,
            'pos': (-30.0, 0.0, -5.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        59: {
            'id': '17.59',
            'space_id': 17,
            'local_id': 59,
            'pos': (-30.0, 0.0, -9.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        60: {
            'id': '17.60',
            'space_id': 17,
            'local_id': 60,
            'pos': (-13.0, 0.0, -4.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        61: {
            'id': '17.61',
            'space_id': 17,
            'local_id': 61,
            'pos': (-13.0, 0.0, -9.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        62: {
            'id': '17.62',
            'space_id': 17,
            'local_id': 62,
            'pos': (1.0, 0.0, -4.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        63: {
            'id': '17.63',
            'space_id': 17,
            'local_id': 63,
            'pos': (8.0, 0.0, 2.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        64: {
            'id': '17.64',
            'space_id': 17,
            'local_id': 64,
            'pos': (-25.0, 0.0, -2.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        65: {
            'id': '17.65',
            'space_id': 17,
            'local_id': 65,
            'pos': (-15.0, 0.0, -2.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        66: {
            'id': '17.66',
            'space_id': 17,
            'local_id': 66,
            'pos': (24.0, 1.0, -9.0, -0.8, ),
            'team_first_birth': 0,
        }, 
    }, 
    18: {
        1: {
            'id': '18.1',
            'space_id': 18,
            'local_id': 1,
            'pos': (-7.1, -0.0, 70.6, -3.1, ),
            'team_first_birth': 2,
        }, 
        2: {
            'id': '18.2',
            'space_id': 18,
            'local_id': 2,
            'pos': (-4.9, -0.0, 70.6, -3.1, ),
            'team_first_birth': 2,
        }, 
        3: {
            'id': '18.3',
            'space_id': 18,
            'local_id': 3,
            'pos': (-2.8, -0.0, 70.6, -3.1, ),
            'team_first_birth': 2,
        }, 
        4: {
            'id': '18.4',
            'space_id': 18,
            'local_id': 4,
            'pos': (-6.1, -0.0, 68.9, -3.1, ),
            'team_first_birth': 2,
        }, 
        5: {
            'id': '18.5',
            'space_id': 18,
            'local_id': 5,
            'pos': (-3.9, -0.0, 69.1, -3.1, ),
            'team_first_birth': 2,
        }, 
        6: {
            'id': '18.6',
            'space_id': 18,
            'local_id': 6,
            'pos': (-25.0, -0.0, -105.4, 0.0, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '18.7',
            'space_id': 18,
            'local_id': 7,
            'pos': (-28.2, -0.0, -103.8, 0.0, ),
            'team_first_birth': 1,
        }, 
        8: {
            'id': '18.8',
            'space_id': 18,
            'local_id': 8,
            'pos': (-25.9, -0.0, -103.7, 0.0, ),
            'team_first_birth': 1,
        }, 
        9: {
            'id': '18.9',
            'space_id': 18,
            'local_id': 9,
            'pos': (-27.2, -0.0, -105.3, 0.0, ),
            'team_first_birth': 1,
        }, 
        10: {
            'id': '18.10',
            'space_id': 18,
            'local_id': 10,
            'pos': (-29.3, -0.0, -105.2, 0.0, ),
            'team_first_birth': 1,
        }, 
        11: {
            'id': '18.11',
            'space_id': 18,
            'local_id': 11,
            'pos': (-30.0, 0.0, -108.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        12: {
            'id': '18.12',
            'space_id': 18,
            'local_id': 12,
            'pos': (-28.0, 0.0, -108.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        13: {
            'id': '18.13',
            'space_id': 18,
            'local_id': 13,
            'pos': (-26.0, 0.0, -108.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '18.14',
            'space_id': 18,
            'local_id': 14,
            'pos': (-28.0, 0.0, -106.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '18.15',
            'space_id': 18,
            'local_id': 15,
            'pos': (-26.0, 0.0, -106.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '18.16',
            'space_id': 18,
            'local_id': 16,
            'pos': (-26.0, 0.0, -104.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '18.17',
            'space_id': 18,
            'local_id': 17,
            'pos': (-28.0, 0.0, -104.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '18.18',
            'space_id': 18,
            'local_id': 18,
            'pos': (-30.0, 0.0, -104.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '18.19',
            'space_id': 18,
            'local_id': 19,
            'pos': (-30.0, 0.0, -102.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '18.20',
            'space_id': 18,
            'local_id': 20,
            'pos': (-28.0, 0.0, -102.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '18.21',
            'space_id': 18,
            'local_id': 21,
            'pos': (-26.0, 0.0, -102.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '18.22',
            'space_id': 18,
            'local_id': 22,
            'pos': (-24.0, 0.0, -102.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '18.23',
            'space_id': 18,
            'local_id': 23,
            'pos': (-24.0, 0.0, -104.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '18.24',
            'space_id': 18,
            'local_id': 24,
            'pos': (-26.0, 0.0, -100.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '18.25',
            'space_id': 18,
            'local_id': 25,
            'pos': (-26.0, 0.0, -98.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '18.26',
            'space_id': 18,
            'local_id': 26,
            'pos': (-28.0, 0.0, -98.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        27: {
            'id': '18.27',
            'space_id': 18,
            'local_id': 27,
            'pos': (-30.0, 0.0, -98.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        28: {
            'id': '18.28',
            'space_id': 18,
            'local_id': 28,
            'pos': (-30.0, 0.0, -98.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        29: {
            'id': '18.29',
            'space_id': 18,
            'local_id': 29,
            'pos': (-30.0, 0.0, -100.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        30: {
            'id': '18.30',
            'space_id': 18,
            'local_id': 30,
            'pos': (-28.0, 0.0, -100.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        31: {
            'id': '18.31',
            'space_id': 18,
            'local_id': 31,
            'pos': (-28.0, 0.0, -96.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        32: {
            'id': '18.32',
            'space_id': 18,
            'local_id': 32,
            'pos': (-30.0, 0.0, -96.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        33: {
            'id': '18.33',
            'space_id': 18,
            'local_id': 33,
            'pos': (-30.0, 0.0, -94.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        34: {
            'id': '18.34',
            'space_id': 18,
            'local_id': 34,
            'pos': (-26.0, 0.0, -96.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        35: {
            'id': '18.35',
            'space_id': 18,
            'local_id': 35,
            'pos': (-24.0, 0.0, -98.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        36: {
            'id': '18.36',
            'space_id': 18,
            'local_id': 36,
            'pos': (-30.0, 0.0, -106.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        37: {
            'id': '18.37',
            'space_id': 18,
            'local_id': 37,
            'pos': (-32.0, 0.0, -106.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        38: {
            'id': '18.38',
            'space_id': 18,
            'local_id': 38,
            'pos': (-32.0, 0.0, -104.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        39: {
            'id': '18.39',
            'space_id': 18,
            'local_id': 39,
            'pos': (-32.0, 0.0, -100.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        40: {
            'id': '18.40',
            'space_id': 18,
            'local_id': 40,
            'pos': (-32.0, 0.0, -98.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        41: {
            'id': '18.41',
            'space_id': 18,
            'local_id': 41,
            'pos': (-32.0, 0.0, -96.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        42: {
            'id': '18.42',
            'space_id': 18,
            'local_id': 42,
            'pos': (0.0, 0.2, -96.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        43: {
            'id': '18.43',
            'space_id': 18,
            'local_id': 43,
            'pos': (2.0, 0.2, -96.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        44: {
            'id': '18.44',
            'space_id': 18,
            'local_id': 44,
            'pos': (4.0, 0.2, -96.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        45: {
            'id': '18.45',
            'space_id': 18,
            'local_id': 45,
            'pos': (6.0, 0.2, -96.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        46: {
            'id': '18.46',
            'space_id': 18,
            'local_id': 46,
            'pos': (4.0, 0.2, -94.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        47: {
            'id': '18.47',
            'space_id': 18,
            'local_id': 47,
            'pos': (2.0, 0.2, -94.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        48: {
            'id': '18.48',
            'space_id': 18,
            'local_id': 48,
            'pos': (-24.0, 0.0, 64.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        49: {
            'id': '18.49',
            'space_id': 18,
            'local_id': 49,
            'pos': (-22.0, 0.0, 62.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        50: {
            'id': '18.50',
            'space_id': 18,
            'local_id': 50,
            'pos': (-24.0, 0.0, 62.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        51: {
            'id': '18.51',
            'space_id': 18,
            'local_id': 51,
            'pos': (-26.0, 0.0, 60.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        52: {
            'id': '18.52',
            'space_id': 18,
            'local_id': 52,
            'pos': (-26.0, 0.0, 62.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        53: {
            'id': '18.53',
            'space_id': 18,
            'local_id': 53,
            'pos': (-24.0, 0.0, 60.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        54: {
            'id': '18.54',
            'space_id': 18,
            'local_id': 54,
            'pos': (-12.0, 0.0, 76.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        55: {
            'id': '18.55',
            'space_id': 18,
            'local_id': 55,
            'pos': (-10.0, 0.0, 74.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        56: {
            'id': '18.56',
            'space_id': 18,
            'local_id': 56,
            'pos': (-10.0, 0.0, 76.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        57: {
            'id': '18.57',
            'space_id': 18,
            'local_id': 57,
            'pos': (-8.0, 0.0, 74.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        58: {
            'id': '18.58',
            'space_id': 18,
            'local_id': 58,
            'pos': (-8.0, 0.0, 72.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        59: {
            'id': '18.59',
            'space_id': 18,
            'local_id': 59,
            'pos': (-10.0, 0.0, 72.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        60: {
            'id': '18.60',
            'space_id': 18,
            'local_id': 60,
            'pos': (-12.0, 0.0, 74.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        61: {
            'id': '18.61',
            'space_id': 18,
            'local_id': 61,
            'pos': (-20.0, 0.0, 74.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        62: {
            'id': '18.62',
            'space_id': 18,
            'local_id': 62,
            'pos': (-22.0, 0.0, 72.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        63: {
            'id': '18.63',
            'space_id': 18,
            'local_id': 63,
            'pos': (-20.0, 0.0, 70.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        64: {
            'id': '18.64',
            'space_id': 18,
            'local_id': 64,
            'pos': (-18.0, 0.0, 72.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        65: {
            'id': '18.65',
            'space_id': 18,
            'local_id': 65,
            'pos': (-14.0, 0.0, 62.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        66: {
            'id': '18.66',
            'space_id': 18,
            'local_id': 66,
            'pos': (-16.0, 0.0, 60.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        67: {
            'id': '18.67',
            'space_id': 18,
            'local_id': 67,
            'pos': (0.0, 0.0, 64.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        68: {
            'id': '18.68',
            'space_id': 18,
            'local_id': 68,
            'pos': (2.0, 0.0, 60.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        69: {
            'id': '18.69',
            'space_id': 18,
            'local_id': 69,
            'pos': (2.0, 0.0, 62.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        70: {
            'id': '18.70',
            'space_id': 18,
            'local_id': 70,
            'pos': (0.0, 0.0, 62.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        71: {
            'id': '18.71',
            'space_id': 18,
            'local_id': 71,
            'pos': (2.0, 0.0, 64.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        72: {
            'id': '18.72',
            'space_id': 18,
            'local_id': 72,
            'pos': (2.0, 0.0, 76.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        73: {
            'id': '18.73',
            'space_id': 18,
            'local_id': 73,
            'pos': (4.0, 0.0, 76.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        74: {
            'id': '18.74',
            'space_id': 18,
            'local_id': 74,
            'pos': (8.0, 0.0, 74.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        75: {
            'id': '18.75',
            'space_id': 18,
            'local_id': 75,
            'pos': (-32.0, 0.0, 46.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        76: {
            'id': '18.76',
            'space_id': 18,
            'local_id': 76,
            'pos': (-30.0, 0.0, 48.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        77: {
            'id': '18.77',
            'space_id': 18,
            'local_id': 77,
            'pos': (-28.0, 0.0, 50.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        78: {
            'id': '18.78',
            'space_id': 18,
            'local_id': 78,
            'pos': (14.0, 0.3, 48.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        79: {
            'id': '18.79',
            'space_id': 18,
            'local_id': 79,
            'pos': (16.0, 0.0, 48.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        80: {
            'id': '18.80',
            'space_id': 18,
            'local_id': 80,
            'pos': (16.0, 0.0, 46.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        81: {
            'id': '18.81',
            'space_id': 18,
            'local_id': 81,
            'pos': (14.0, 0.0, 46.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        82: {
            'id': '18.82',
            'space_id': 18,
            'local_id': 82,
            'pos': (16.0, 0.3, 76.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        83: {
            'id': '18.83',
            'space_id': 18,
            'local_id': 83,
            'pos': (18.0, 0.3, 76.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        84: {
            'id': '18.84',
            'space_id': 18,
            'local_id': 84,
            'pos': (16.0, 0.3, 74.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        85: {
            'id': '18.85',
            'space_id': 18,
            'local_id': 85,
            'pos': (18.0, 0.3, 74.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        86: {
            'id': '18.86',
            'space_id': 18,
            'local_id': 86,
            'pos': (18.0, 0.3, 72.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        87: {
            'id': '18.87',
            'space_id': 18,
            'local_id': 87,
            'pos': (20.0, 0.3, 74.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        88: {
            'id': '18.88',
            'space_id': 18,
            'local_id': 88,
            'pos': (14.0, 0.0, 74.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        89: {
            'id': '18.89',
            'space_id': 18,
            'local_id': 89,
            'pos': (24.0, 0.2, -2.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        90: {
            'id': '18.90',
            'space_id': 18,
            'local_id': 90,
            'pos': (24.0, 0.2, 0.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        91: {
            'id': '18.91',
            'space_id': 18,
            'local_id': 91,
            'pos': (22.0, 0.2, 0.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        92: {
            'id': '18.92',
            'space_id': 18,
            'local_id': 92,
            'pos': (24.0, 0.2, -4.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        93: {
            'id': '18.93',
            'space_id': 18,
            'local_id': 93,
            'pos': (22.0, 0.2, -6.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        94: {
            'id': '18.94',
            'space_id': 18,
            'local_id': 94,
            'pos': (24.0, 0.3, 56.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        95: {
            'id': '18.95',
            'space_id': 18,
            'local_id': 95,
            'pos': (22.0, 0.2, -8.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        96: {
            'id': '18.96',
            'space_id': 18,
            'local_id': 96,
            'pos': (22.0, 0.2, -10.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        97: {
            'id': '18.97',
            'space_id': 18,
            'local_id': 97,
            'pos': (24.0, 0.2, -10.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        98: {
            'id': '18.98',
            'space_id': 18,
            'local_id': 98,
            'pos': (24.0, 0.2, -8.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        99: {
            'id': '18.99',
            'space_id': 18,
            'local_id': 99,
            'pos': (28.0, 0.2, -10.0, -3.1, ),
            'team_first_birth': 0,
        }, 
        100: {
            'id': '18.100',
            'space_id': 18,
            'local_id': 100,
            'pos': (20.0, 0.2, -28.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        101: {
            'id': '18.101',
            'space_id': 18,
            'local_id': 101,
            'pos': (18.0, 0.2, -28.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        102: {
            'id': '18.102',
            'space_id': 18,
            'local_id': 102,
            'pos': (16.0, 0.2, -28.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        103: {
            'id': '18.103',
            'space_id': 18,
            'local_id': 103,
            'pos': (16.0, 0.2, -30.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        104: {
            'id': '18.104',
            'space_id': 18,
            'local_id': 104,
            'pos': (18.0, 0.2, -30.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        105: {
            'id': '18.105',
            'space_id': 18,
            'local_id': 105,
            'pos': (20.0, 0.2, -30.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        106: {
            'id': '18.106',
            'space_id': 18,
            'local_id': 106,
            'pos': (20.0, 0.2, -36.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        107: {
            'id': '18.107',
            'space_id': 18,
            'local_id': 107,
            'pos': (20.0, 0.2, -34.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        108: {
            'id': '18.108',
            'space_id': 18,
            'local_id': 108,
            'pos': (24.0, 0.2, -34.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        109: {
            'id': '18.109',
            'space_id': 18,
            'local_id': 109,
            'pos': (24.0, 0.2, -36.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        110: {
            'id': '18.110',
            'space_id': 18,
            'local_id': 110,
            'pos': (26.0, 0.2, -36.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        111: {
            'id': '18.111',
            'space_id': 18,
            'local_id': 111,
            'pos': (26.0, 0.2, -34.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        112: {
            'id': '18.112',
            'space_id': 18,
            'local_id': 112,
            'pos': (26.0, 0.2, -34.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        113: {
            'id': '18.113',
            'space_id': 18,
            'local_id': 113,
            'pos': (26.0, 0.2, -36.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        114: {
            'id': '18.114',
            'space_id': 18,
            'local_id': 114,
            'pos': (28.0, 0.2, -36.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        115: {
            'id': '18.115',
            'space_id': 18,
            'local_id': 115,
            'pos': (28.0, 0.2, -34.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        116: {
            'id': '18.116',
            'space_id': 18,
            'local_id': 116,
            'pos': (29.3, 0.2, -36.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        117: {
            'id': '18.117',
            'space_id': 18,
            'local_id': 117,
            'pos': (29.3, 0.2, -34.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        118: {
            'id': '18.118',
            'space_id': 18,
            'local_id': 118,
            'pos': (-32.0, 0.0, -92.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        119: {
            'id': '18.119',
            'space_id': 18,
            'local_id': 119,
            'pos': (-50.0, 0.2, -78.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        120: {
            'id': '18.120',
            'space_id': 18,
            'local_id': 120,
            'pos': (-48.0, 0.2, -78.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        121: {
            'id': '18.121',
            'space_id': 18,
            'local_id': 121,
            'pos': (-46.0, 0.2, -78.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        122: {
            'id': '18.122',
            'space_id': 18,
            'local_id': 122,
            'pos': (-32.0, 0.0, 34.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        123: {
            'id': '18.123',
            'space_id': 18,
            'local_id': 123,
            'pos': (-32.0, 0.2, 30.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        124: {
            'id': '18.124',
            'space_id': 18,
            'local_id': 124,
            'pos': (-28.0, 0.2, 30.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        125: {
            'id': '18.125',
            'space_id': 18,
            'local_id': 125,
            'pos': (-28.0, 0.0, 34.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        126: {
            'id': '18.126',
            'space_id': 18,
            'local_id': 126,
            'pos': (-30.0, 0.2, 32.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        127: {
            'id': '18.127',
            'space_id': 18,
            'local_id': 127,
            'pos': (-30.0, 0.2, 28.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        128: {
            'id': '18.128',
            'space_id': 18,
            'local_id': 128,
            'pos': (-32.0, 0.0, 26.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        129: {
            'id': '18.129',
            'space_id': 18,
            'local_id': 129,
            'pos': (-28.0, 0.0, 26.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        130: {
            'id': '18.130',
            'space_id': 18,
            'local_id': 130,
            'pos': (-26.0, 0.2, 28.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        131: {
            'id': '18.131',
            'space_id': 18,
            'local_id': 131,
            'pos': (0.0, 0.2, -74.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        132: {
            'id': '18.132',
            'space_id': 18,
            'local_id': 132,
            'pos': (2.0, 0.2, -74.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        133: {
            'id': '18.133',
            'space_id': 18,
            'local_id': 133,
            'pos': (4.0, 0.2, -74.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        134: {
            'id': '18.134',
            'space_id': 18,
            'local_id': 134,
            'pos': (4.0, 0.2, -72.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        135: {
            'id': '18.135',
            'space_id': 18,
            'local_id': 135,
            'pos': (2.0, 0.2, -72.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        136: {
            'id': '18.136',
            'space_id': 18,
            'local_id': 136,
            'pos': (2.0, 0.2, -70.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        137: {
            'id': '18.137',
            'space_id': 18,
            'local_id': 137,
            'pos': (4.0, 0.2, -70.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        138: {
            'id': '18.138',
            'space_id': 18,
            'local_id': 138,
            'pos': (6.0, 0.2, -70.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        139: {
            'id': '18.139',
            'space_id': 18,
            'local_id': 139,
            'pos': (6.0, 0.2, -68.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        140: {
            'id': '18.140',
            'space_id': 18,
            'local_id': 140,
            'pos': (4.0, 0.2, -68.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        141: {
            'id': '18.141',
            'space_id': 18,
            'local_id': 141,
            'pos': (2.0, 0.2, -68.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        142: {
            'id': '18.142',
            'space_id': 18,
            'local_id': 142,
            'pos': (6.0, 0.2, -72.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        143: {
            'id': '18.143',
            'space_id': 18,
            'local_id': 143,
            'pos': (6.0, 0.2, -66.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        144: {
            'id': '18.144',
            'space_id': 18,
            'local_id': 144,
            'pos': (-6.0, 0.0, 14.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        145: {
            'id': '18.145',
            'space_id': 18,
            'local_id': 145,
            'pos': (-8.0, 0.0, 14.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        146: {
            'id': '18.146',
            'space_id': 18,
            'local_id': 146,
            'pos': (-8.0, 0.0, 12.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        147: {
            'id': '18.147',
            'space_id': 18,
            'local_id': 147,
            'pos': (-6.0, 0.0, 12.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        148: {
            'id': '18.148',
            'space_id': 18,
            'local_id': 148,
            'pos': (-6.0, 0.0, 10.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        149: {
            'id': '18.149',
            'space_id': 18,
            'local_id': 149,
            'pos': (-8.0, 0.0, 10.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        150: {
            'id': '18.150',
            'space_id': 18,
            'local_id': 150,
            'pos': (-8.0, 0.0, 8.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        151: {
            'id': '18.151',
            'space_id': 18,
            'local_id': 151,
            'pos': (-6.0, 0.0, 8.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        152: {
            'id': '18.152',
            'space_id': 18,
            'local_id': 152,
            'pos': (-6.0, 0.0, 6.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        153: {
            'id': '18.153',
            'space_id': 18,
            'local_id': 153,
            'pos': (-10.0, 0.0, 8.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        154: {
            'id': '18.154',
            'space_id': 18,
            'local_id': 154,
            'pos': (-10.0, 0.0, 10.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        155: {
            'id': '18.155',
            'space_id': 18,
            'local_id': 155,
            'pos': (-12.0, 0.2, 10.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        156: {
            'id': '18.156',
            'space_id': 18,
            'local_id': 156,
            'pos': (-12.0, 0.2, 8.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        157: {
            'id': '18.157',
            'space_id': 18,
            'local_id': 157,
            'pos': (-10.0, 0.0, 6.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        158: {
            'id': '18.158',
            'space_id': 18,
            'local_id': 158,
            'pos': (-8.0, 0.0, 6.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        159: {
            'id': '18.159',
            'space_id': 18,
            'local_id': 159,
            'pos': (-14.0, 0.0, -8.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        160: {
            'id': '18.160',
            'space_id': 18,
            'local_id': 160,
            'pos': (-12.0, 0.0, -6.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        161: {
            'id': '18.161',
            'space_id': 18,
            'local_id': 161,
            'pos': (-14.0, 0.0, -6.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        162: {
            'id': '18.162',
            'space_id': 18,
            'local_id': 162,
            'pos': (-12.0, 0.0, -8.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        163: {
            'id': '18.163',
            'space_id': 18,
            'local_id': 163,
            'pos': (-14.0, 0.0, -10.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        164: {
            'id': '18.164',
            'space_id': 18,
            'local_id': 164,
            'pos': (-18.0, 0.0, -8.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        165: {
            'id': '18.165',
            'space_id': 18,
            'local_id': 165,
            'pos': (-16.0, 0.0, -4.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        166: {
            'id': '18.166',
            'space_id': 18,
            'local_id': 166,
            'pos': (-16.0, 0.2, -2.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        167: {
            'id': '18.167',
            'space_id': 18,
            'local_id': 167,
            'pos': (-16.0, 0.0, -8.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        168: {
            'id': '18.168',
            'space_id': 18,
            'local_id': 168,
            'pos': (-16.0, 0.0, -10.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        169: {
            'id': '18.169',
            'space_id': 18,
            'local_id': 169,
            'pos': (-16.0, 0.0, -12.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        170: {
            'id': '18.170',
            'space_id': 18,
            'local_id': 170,
            'pos': (-18.0, 0.0, -12.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        171: {
            'id': '18.171',
            'space_id': 18,
            'local_id': 171,
            'pos': (-18.0, 0.0, -10.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        172: {
            'id': '18.172',
            'space_id': 18,
            'local_id': 172,
            'pos': (-2.0, 0.0, -12.0, -0.0, ),
            'team_first_birth': 0,
        }, 
        173: {
            'id': '18.173',
            'space_id': 18,
            'local_id': 173,
            'pos': (0.0, 0.0, -12.0, -0.0, ),
            'team_first_birth': 0,
        }, 
        174: {
            'id': '18.174',
            'space_id': 18,
            'local_id': 174,
            'pos': (-2.0, 0.0, -14.0, -0.0, ),
            'team_first_birth': 0,
        }, 
        175: {
            'id': '18.175',
            'space_id': 18,
            'local_id': 175,
            'pos': (0.0, 0.0, -14.0, -0.0, ),
            'team_first_birth': 0,
        }, 
        176: {
            'id': '18.176',
            'space_id': 18,
            'local_id': 176,
            'pos': (-6.0, 0.0, -14.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        177: {
            'id': '18.177',
            'space_id': 18,
            'local_id': 177,
            'pos': (-12.0, 0.0, -14.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        178: {
            'id': '18.178',
            'space_id': 18,
            'local_id': 178,
            'pos': (-12.0, 0.0, -14.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        179: {
            'id': '18.179',
            'space_id': 18,
            'local_id': 179,
            'pos': (-8.0, 0.0, -16.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        180: {
            'id': '18.180',
            'space_id': 18,
            'local_id': 180,
            'pos': (0.0, 0.0, -4.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        181: {
            'id': '18.181',
            'space_id': 18,
            'local_id': 181,
            'pos': (2.0, 0.0, -4.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        182: {
            'id': '18.182',
            'space_id': 18,
            'local_id': 182,
            'pos': (4.0, 0.0, -6.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        183: {
            'id': '18.183',
            'space_id': 18,
            'local_id': 183,
            'pos': (2.0, 0.0, 0.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        184: {
            'id': '18.184',
            'space_id': 18,
            'local_id': 184,
            'pos': (0.0, 0.0, 0.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        185: {
            'id': '18.185',
            'space_id': 18,
            'local_id': 185,
            'pos': (0.0, 0.0, -2.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        186: {
            'id': '18.186',
            'space_id': 18,
            'local_id': 186,
            'pos': (2.0, 0.0, -2.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        187: {
            'id': '18.187',
            'space_id': 18,
            'local_id': 187,
            'pos': (2.0, 0.0, -6.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        188: {
            'id': '18.188',
            'space_id': 18,
            'local_id': 188,
            'pos': (4.0, 0.0, -8.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        189: {
            'id': '18.189',
            'space_id': 18,
            'local_id': 189,
            'pos': (4.0, 0.0, 6.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        190: {
            'id': '18.190',
            'space_id': 18,
            'local_id': 190,
            'pos': (2.0, 0.0, 4.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        191: {
            'id': '18.191',
            'space_id': 18,
            'local_id': 191,
            'pos': (0.0, 0.0, 2.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        192: {
            'id': '18.192',
            'space_id': 18,
            'local_id': 192,
            'pos': (0.0, 0.0, 4.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        193: {
            'id': '18.193',
            'space_id': 18,
            'local_id': 193,
            'pos': (-8.0, 0.0, 0.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        194: {
            'id': '18.194',
            'space_id': 18,
            'local_id': 194,
            'pos': (-6.0, 0.0, 0.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        195: {
            'id': '18.195',
            'space_id': 18,
            'local_id': 195,
            'pos': (-6.0, 0.0, 2.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        196: {
            'id': '18.196',
            'space_id': 18,
            'local_id': 196,
            'pos': (-6.0, 0.0, 4.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        197: {
            'id': '18.197',
            'space_id': 18,
            'local_id': 197,
            'pos': (-8.0, 0.0, 4.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        198: {
            'id': '18.198',
            'space_id': 18,
            'local_id': 198,
            'pos': (0.0, 0.0, -10.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        199: {
            'id': '18.199',
            'space_id': 18,
            'local_id': 199,
            'pos': (2.0, 0.0, -12.0, 2.4, ),
            'team_first_birth': 0,
        }, 
        200: {
            'id': '18.200',
            'space_id': 18,
            'local_id': 200,
            'pos': (-16.0, 0.0, 50.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        201: {
            'id': '18.201',
            'space_id': 18,
            'local_id': 201,
            'pos': (-14.0, 0.0, 52.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        202: {
            'id': '18.202',
            'space_id': 18,
            'local_id': 202,
            'pos': (-12.0, 0.0, 52.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        203: {
            'id': '18.203',
            'space_id': 18,
            'local_id': 203,
            'pos': (-12.0, 0.0, 54.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        204: {
            'id': '18.204',
            'space_id': 18,
            'local_id': 204,
            'pos': (-14.0, 0.0, 50.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        205: {
            'id': '18.205',
            'space_id': 18,
            'local_id': 205,
            'pos': (-16.0, 0.0, 48.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        206: {
            'id': '18.206',
            'space_id': 18,
            'local_id': 206,
            'pos': (-18.0, 0.0, 50.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        207: {
            'id': '18.207',
            'space_id': 18,
            'local_id': 207,
            'pos': (-18.0, 0.0, 48.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        208: {
            'id': '18.208',
            'space_id': 18,
            'local_id': 208,
            'pos': (-12.0, 0.0, 50.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        209: {
            'id': '18.209',
            'space_id': 18,
            'local_id': 209,
            'pos': (-14.0, 0.0, 48.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        210: {
            'id': '18.210',
            'space_id': 18,
            'local_id': 210,
            'pos': (-14.0, 0.0, 46.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        211: {
            'id': '18.211',
            'space_id': 18,
            'local_id': 211,
            'pos': (-18.0, 0.0, 44.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        212: {
            'id': '18.212',
            'space_id': 18,
            'local_id': 212,
            'pos': (-20.0, 0.0, 44.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        213: {
            'id': '18.213',
            'space_id': 18,
            'local_id': 213,
            'pos': (-16.0, 0.2, 42.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        214: {
            'id': '18.214',
            'space_id': 18,
            'local_id': 214,
            'pos': (-14.0, 0.2, 44.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        215: {
            'id': '18.215',
            'space_id': 18,
            'local_id': 215,
            'pos': (-16.0, 0.2, 40.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        216: {
            'id': '18.216',
            'space_id': 18,
            'local_id': 216,
            'pos': (-18.0, 0.2, 38.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        217: {
            'id': '18.217',
            'space_id': 18,
            'local_id': 217,
            'pos': (-20.0, 0.2, 38.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        218: {
            'id': '18.218',
            'space_id': 18,
            'local_id': 218,
            'pos': (-20.0, 0.2, 36.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        219: {
            'id': '18.219',
            'space_id': 18,
            'local_id': 219,
            'pos': (-14.0, 0.2, 42.0, -2.4, ),
            'team_first_birth': 0,
        }, 
    }, 
    20: {
        1: {
            'id': '20.1',
            'space_id': 20,
            'local_id': 1,
            'pos': (-36.0, 0.0, 38.0, 3.1, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '20.2',
            'space_id': 20,
            'local_id': 2,
            'pos': (-36.0, 0.0, 40.0, 3.1, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '20.3',
            'space_id': 20,
            'local_id': 3,
            'pos': (-34.0, 0.0, 40.0, 3.1, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '20.4',
            'space_id': 20,
            'local_id': 4,
            'pos': (-34.0, 0.0, 38.0, 3.1, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '20.5',
            'space_id': 20,
            'local_id': 5,
            'pos': (-32.0, 0.0, 38.0, 3.1, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '20.6',
            'space_id': 20,
            'local_id': 6,
            'pos': (40.0, 0.0, -22.0, -1.6, ),
            'team_first_birth': 2,
        }, 
        7: {
            'id': '20.7',
            'space_id': 20,
            'local_id': 7,
            'pos': (42.0, 0.0, -20.0, -1.6, ),
            'team_first_birth': 2,
        }, 
        8: {
            'id': '20.8',
            'space_id': 20,
            'local_id': 8,
            'pos': (40.0, 0.0, -18.0, -1.6, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '20.9',
            'space_id': 20,
            'local_id': 9,
            'pos': (40.0, 0.0, -20.0, -1.6, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '20.10',
            'space_id': 20,
            'local_id': 10,
            'pos': (42.0, 0.0, -18.0, -1.6, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '20.11',
            'space_id': 20,
            'local_id': 11,
            'pos': (28.0, 0.0, 6.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        12: {
            'id': '20.12',
            'space_id': 20,
            'local_id': 12,
            'pos': (20.0, 0.5, -12.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        13: {
            'id': '20.13',
            'space_id': 20,
            'local_id': 13,
            'pos': (8.0, 0.5, -18.0, -0.0, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '20.14',
            'space_id': 20,
            'local_id': 14,
            'pos': (18.0, 0.5, -26.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '20.15',
            'space_id': 20,
            'local_id': 15,
            'pos': (10.0, 0.5, -34.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '20.16',
            'space_id': 20,
            'local_id': 16,
            'pos': (-32.0, 0.0, 24.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '20.17',
            'space_id': 20,
            'local_id': 17,
            'pos': (-42.0, 0.0, 24.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '20.18',
            'space_id': 20,
            'local_id': 18,
            'pos': (-42.0, 0.0, 34.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '20.19',
            'space_id': 20,
            'local_id': 19,
            'pos': (-32.0, 0.5, -22.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '20.20',
            'space_id': 20,
            'local_id': 20,
            'pos': (14.0, 0.5, 12.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '20.21',
            'space_id': 20,
            'local_id': 21,
            'pos': (-22.0, 0.0, 40.0, 3.1, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '20.22',
            'space_id': 20,
            'local_id': 22,
            'pos': (22.4, 0.5, -34.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '20.23',
            'space_id': 20,
            'local_id': 23,
            'pos': (6.0, 0.5, -14.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '20.24',
            'space_id': 20,
            'local_id': 24,
            'pos': (-32.0, 0.5, 4.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '20.25',
            'space_id': 20,
            'local_id': 25,
            'pos': (-20.0, 0.5, 4.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '20.26',
            'space_id': 20,
            'local_id': 26,
            'pos': (-24.0, 0.5, -4.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        27: {
            'id': '20.27',
            'space_id': 20,
            'local_id': 27,
            'pos': (-6.0, 0.5, -14.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        28: {
            'id': '20.28',
            'space_id': 20,
            'local_id': 28,
            'pos': (-22.0, 0.5, -14.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        29: {
            'id': '20.29',
            'space_id': 20,
            'local_id': 29,
            'pos': (-22.0, 0.5, -18.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        30: {
            'id': '20.30',
            'space_id': 20,
            'local_id': 30,
            'pos': (0.0, 0.5, -4.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        31: {
            'id': '20.31',
            'space_id': 20,
            'local_id': 31,
            'pos': (0.0, 0.5, 2.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        32: {
            'id': '20.32',
            'space_id': 20,
            'local_id': 32,
            'pos': (20.0, 0.5, 18.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        33: {
            'id': '20.33',
            'space_id': 20,
            'local_id': 33,
            'pos': (18.0, 0.0, 38.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        34: {
            'id': '20.34',
            'space_id': 20,
            'local_id': 34,
            'pos': (32.0, 0.0, -12.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        35: {
            'id': '20.35',
            'space_id': 20,
            'local_id': 35,
            'pos': (18.0, 0.4, -18.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        36: {
            'id': '20.36',
            'space_id': 20,
            'local_id': 36,
            'pos': (6.0, 0.4, -18.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        37: {
            'id': '20.37',
            'space_id': 20,
            'local_id': 37,
            'pos': (-16.0, 0.0, 26.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        38: {
            'id': '20.38',
            'space_id': 20,
            'local_id': 38,
            'pos': (-42.0, 0.5, -8.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        39: {
            'id': '20.39',
            'space_id': 20,
            'local_id': 39,
            'pos': (-42.0, 0.5, 4.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        40: {
            'id': '20.40',
            'space_id': 20,
            'local_id': 40,
            'pos': (-42.0, 0.0, 14.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        41: {
            'id': '20.41',
            'space_id': 20,
            'local_id': 41,
            'pos': (0.0, 0.5, 24.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        42: {
            'id': '20.42',
            'space_id': 20,
            'local_id': 42,
            'pos': (40.0, 0.0, 2.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        43: {
            'id': '20.43',
            'space_id': 20,
            'local_id': 43,
            'pos': (-4.0, 0.0, 40.0, -2.4, ),
            'team_first_birth': 0,
        }, 
        44: {
            'id': '20.44',
            'space_id': 20,
            'local_id': 44,
            'pos': (22.0, 0.5, 12.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        45: {
            'id': '20.45',
            'space_id': 20,
            'local_id': 45,
            'pos': (28.0, 0.0, 14.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        46: {
            'id': '20.46',
            'space_id': 20,
            'local_id': 46,
            'pos': (-22.0, 0.5, -36.0, 0.0, ),
            'team_first_birth': 0,
        }, 
    }, 
    21: {
        1: {
            'id': '21.1',
            'space_id': 21,
            'local_id': 1,
            'pos': (3.0, 0.3, 34.0, 3.1, ),
            'team_first_birth': 2,
        }, 
        2: {
            'id': '21.2',
            'space_id': 21,
            'local_id': 2,
            'pos': (1.0, 0.3, 34.0, 3.1, ),
            'team_first_birth': 2,
        }, 
        3: {
            'id': '21.3',
            'space_id': 21,
            'local_id': 3,
            'pos': (-1.0, 0.3, 34.0, 3.1, ),
            'team_first_birth': 2,
        }, 
        4: {
            'id': '21.4',
            'space_id': 21,
            'local_id': 4,
            'pos': (-3.0, 0.3, 34.0, 3.1, ),
            'team_first_birth': 2,
        }, 
        5: {
            'id': '21.5',
            'space_id': 21,
            'local_id': 5,
            'pos': (-5.0, 0.3, 34.0, 3.1, ),
            'team_first_birth': 2,
        }, 
        6: {
            'id': '21.6',
            'space_id': 21,
            'local_id': 6,
            'pos': (-3.0, 0.3, -33.0, 0.0, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '21.7',
            'space_id': 21,
            'local_id': 7,
            'pos': (3.0, 0.3, -33.0, 0.0, ),
            'team_first_birth': 1,
        }, 
        8: {
            'id': '21.8',
            'space_id': 21,
            'local_id': 8,
            'pos': (-1.0, 0.3, -33.0, 0.0, ),
            'team_first_birth': 1,
        }, 
        9: {
            'id': '21.9',
            'space_id': 21,
            'local_id': 9,
            'pos': (1.0, 0.3, -33.0, 0.0, ),
            'team_first_birth': 1,
        }, 
        10: {
            'id': '21.10',
            'space_id': 21,
            'local_id': 10,
            'pos': (-5.0, 0.3, -33.0, 0.0, ),
            'team_first_birth': 1,
        }, 
        11: {
            'id': '21.11',
            'space_id': 21,
            'local_id': 11,
            'pos': (17.0, 0.3, -33.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        12: {
            'id': '21.12',
            'space_id': 21,
            'local_id': 12,
            'pos': (-18.0, 0.3, -33.0, 0.0, ),
            'team_first_birth': 0,
        }, 
        13: {
            'id': '21.13',
            'space_id': 21,
            'local_id': 13,
            'pos': (-17.0, 0.3, -24.0, 0.8, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '21.14',
            'space_id': 21,
            'local_id': 14,
            'pos': (18.0, 0.3, -20.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '21.15',
            'space_id': 21,
            'local_id': 15,
            'pos': (-4.0, 0.3, -18.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '21.16',
            'space_id': 21,
            'local_id': 16,
            'pos': (18.0, 0.3, -15.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '21.17',
            'space_id': 21,
            'local_id': 17,
            'pos': (21.0, 0.3, 28.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '21.18',
            'space_id': 21,
            'local_id': 18,
            'pos': (21.0, 0.3, 34.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '21.19',
            'space_id': 21,
            'local_id': 19,
            'pos': (-19.0, 0.3, 28.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '21.20',
            'space_id': 21,
            'local_id': 20,
            'pos': (-19.0, 0.3, 34.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '21.21',
            'space_id': 21,
            'local_id': 21,
            'pos': (15.0, 0.3, 23.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '21.22',
            'space_id': 21,
            'local_id': 22,
            'pos': (-17.0, 0.3, 23.0, 2.1, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '21.23',
            'space_id': 21,
            'local_id': 23,
            'pos': (-17.0, 0.3, -15.0, 1.0, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '21.24',
            'space_id': 21,
            'local_id': 24,
            'pos': (17.0, 0.3, 16.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '21.25',
            'space_id': 21,
            'local_id': 25,
            'pos': (-8.0, 0.3, 20.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '21.26',
            'space_id': 21,
            'local_id': 26,
            'pos': (-6.0, 0.3, 16.0, 2.9, ),
            'team_first_birth': 0,
        }, 
        27: {
            'id': '21.27',
            'space_id': 21,
            'local_id': 27,
            'pos': (18.0, 0.3, -5.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        28: {
            'id': '21.28',
            'space_id': 21,
            'local_id': 28,
            'pos': (17.0, 0.3, 2.0, -1.6, ),
            'team_first_birth': 0,
        }, 
        29: {
            'id': '21.29',
            'space_id': 21,
            'local_id': 29,
            'pos': (-17.0, 0.3, 16.0, 2.9, ),
            'team_first_birth': 0,
        }, 
        30: {
            'id': '21.30',
            'space_id': 21,
            'local_id': 30,
            'pos': (-10.0, 0.3, 12.0, 2.9, ),
            'team_first_birth': 0,
        }, 
        31: {
            'id': '21.31',
            'space_id': 21,
            'local_id': 31,
            'pos': (11.0, 0.3, -12.0, -0.8, ),
            'team_first_birth': 0,
        }, 
        32: {
            'id': '21.32',
            'space_id': 21,
            'local_id': 32,
            'pos': (3.0, 0.3, -16.0, 0.3, ),
            'team_first_birth': 0,
        }, 
        33: {
            'id': '21.33',
            'space_id': 21,
            'local_id': 33,
            'pos': (-17.0, 0.3, 17.0, 1.6, ),
            'team_first_birth': 0,
        }, 
        34: {
            'id': '21.34',
            'space_id': 21,
            'local_id': 34,
            'pos': (-7.0, 0.3, 14.0, 1.6, ),
            'team_first_birth': 0,
        }, 
    }, 
    32: {
        1: {
            'id': '32.1',
            'space_id': 32,
            'local_id': 1,
            'pos': (4.82, -0.01, -30.05, 1.0, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '32.2',
            'space_id': 32,
            'local_id': 2,
            'pos': (3.16, 0.24, -31.06, 0.99, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '32.3',
            'space_id': 32,
            'local_id': 3,
            'pos': (7.56, -0.01, -34.55, 0.04, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '32.4',
            'space_id': 32,
            'local_id': 4,
            'pos': (5.1, -0.01, -33.58, 0.47, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '32.5',
            'space_id': 32,
            'local_id': 5,
            'pos': (7.46, -0.02, -31.74, 0.05, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '32.6',
            'space_id': 32,
            'local_id': 6,
            'pos': (5.54, -0.0, -35.15, 0.09, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '32.7',
            'space_id': 32,
            'local_id': 7,
            'pos': (29.28, -0.04, -5.13, -2.04, ),
            'team_first_birth': 2,
        }, 
        8: {
            'id': '32.8',
            'space_id': 32,
            'local_id': 8,
            'pos': (30.68, -0.05, -6.28, -1.79, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '32.9',
            'space_id': 32,
            'local_id': 9,
            'pos': (28.55, -0.0, -3.43, -2.72, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '32.10',
            'space_id': 32,
            'local_id': 10,
            'pos': (30.58, 0.0, -3.36, -2.29, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '32.11',
            'space_id': 32,
            'local_id': 11,
            'pos': (31.61, -0.01, -4.64, -2.22, ),
            'team_first_birth': 2,
        }, 
        12: {
            'id': '32.12',
            'space_id': 32,
            'local_id': 12,
            'pos': (27.3, 0.01, -2.26, -3.06, ),
            'team_first_birth': 2,
        }, 
        13: {
            'id': '32.13',
            'space_id': 32,
            'local_id': 13,
            'pos': (19.58, 0.24, -1.49, 1.83, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '32.14',
            'space_id': 32,
            'local_id': 14,
            'pos': (22.93, 0.01, -1.79, 2.91, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '32.15',
            'space_id': 32,
            'local_id': 15,
            'pos': (28.92, 0.01, -1.33, 3.13, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '32.16',
            'space_id': 32,
            'local_id': 16,
            'pos': (21.68, 0.0, -3.85, 2.47, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '32.17',
            'space_id': 32,
            'local_id': 17,
            'pos': (26.66, -0.02, -6.73, -1.75, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '32.18',
            'space_id': 32,
            'local_id': 18,
            'pos': (30.34, 0.01, -1.25, -2.81, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '32.19',
            'space_id': 32,
            'local_id': 19,
            'pos': (30.75, 0.01, -22.1, -2.63, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '32.20',
            'space_id': 32,
            'local_id': 20,
            'pos': (29.81, 0.0, -30.5, -1.52, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '32.21',
            'space_id': 32,
            'local_id': 21,
            'pos': (30.72, 0.0, -33.08, -1.18, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '32.22',
            'space_id': 32,
            'local_id': 22,
            'pos': (30.57, 0.0, -28.72, -1.61, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '32.23',
            'space_id': 32,
            'local_id': 23,
            'pos': (28.88, 0.0, -27.39, -1.68, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '32.24',
            'space_id': 32,
            'local_id': 24,
            'pos': (28.77, 0.0, -31.83, -1.13, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '32.25',
            'space_id': 32,
            'local_id': 25,
            'pos': (32.49, 0.01, -30.83, -0.9, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '32.26',
            'space_id': 32,
            'local_id': 26,
            'pos': (31.25, 0.01, -25.11, -0.98, ),
            'team_first_birth': 0,
        }, 
        27: {
            'id': '32.27',
            'space_id': 32,
            'local_id': 27,
            'pos': (32.19, 0.01, -26.77, -1.53, ),
            'team_first_birth': 0,
        }, 
        28: {
            'id': '32.28',
            'space_id': 32,
            'local_id': 28,
            'pos': (20.43, 0.04, -18.39, 1.49, ),
            'team_first_birth': 0,
        }, 
        29: {
            'id': '32.29',
            'space_id': 32,
            'local_id': 29,
            'pos': (18.54, -0.01, -18.47, -0.31, ),
            'team_first_birth': 0,
        }, 
        30: {
            'id': '32.30',
            'space_id': 32,
            'local_id': 30,
            'pos': (17.69, -0.03, -14.4, -2.74, ),
            'team_first_birth': 0,
        }, 
        31: {
            'id': '32.31',
            'space_id': 32,
            'local_id': 31,
            'pos': (15.09, -0.02, -18.84, -1.56, ),
            'team_first_birth': 0,
        }, 
        32: {
            'id': '32.32',
            'space_id': 32,
            'local_id': 32,
            'pos': (17.7, -0.07, -21.58, 3.12, ),
            'team_first_birth': 0,
        }, 
        33: {
            'id': '32.33',
            'space_id': 32,
            'local_id': 33,
            'pos': (17.62, -0.07, -23.7, -3.07, ),
            'team_first_birth': 0,
        }, 
        34: {
            'id': '32.34',
            'space_id': 32,
            'local_id': 34,
            'pos': (20.76, 0.24, -10.87, -3.05, ),
            'team_first_birth': 0,
        }, 
        35: {
            'id': '32.35',
            'space_id': 32,
            'local_id': 35,
            'pos': (21.26, 0.24, -14.04, -2.79, ),
            'team_first_birth': 0,
        }, 
        36: {
            'id': '32.36',
            'space_id': 32,
            'local_id': 36,
            'pos': (5.04, 0.1, -14.47, 1.12, ),
            'team_first_birth': 0,
        }, 
        37: {
            'id': '32.37',
            'space_id': 32,
            'local_id': 37,
            'pos': (4.74, 0.0, -3.38, 2.0, ),
            'team_first_birth': 0,
        }, 
        38: {
            'id': '32.38',
            'space_id': 32,
            'local_id': 38,
            'pos': (9.45, 0.03, -2.6, 2.13, ),
            'team_first_birth': 0,
        }, 
        39: {
            'id': '32.39',
            'space_id': 32,
            'local_id': 39,
            'pos': (7.11, 0.02, -3.15, 3.0, ),
            'team_first_birth': 0,
        }, 
        40: {
            'id': '32.40',
            'space_id': 32,
            'local_id': 40,
            'pos': (3.09, -0.02, -7.11, 2.04, ),
            'team_first_birth': 0,
        }, 
        41: {
            'id': '32.41',
            'space_id': 32,
            'local_id': 41,
            'pos': (3.73, -0.03, -4.92, 3.09, ),
            'team_first_birth': 0,
        }, 
        42: {
            'id': '32.42',
            'space_id': 32,
            'local_id': 42,
            'pos': (7.54, -0.01, -6.01, 2.43, ),
            'team_first_birth': 0,
        }, 
        43: {
            'id': '32.43',
            'space_id': 32,
            'local_id': 43,
            'pos': (16.58, 0.41, -32.86, -1.44, ),
            'team_first_birth': 0,
        }, 
        44: {
            'id': '32.44',
            'space_id': 32,
            'local_id': 44,
            'pos': (13.65, -0.0, -35.09, -0.64, ),
            'team_first_birth': 0,
        }, 
        45: {
            'id': '32.45',
            'space_id': 32,
            'local_id': 45,
            'pos': (3.82, -0.0, -34.39, 0.53, ),
            'team_first_birth': 0,
        }, 
        46: {
            'id': '32.46',
            'space_id': 32,
            'local_id': 46,
            'pos': (13.02, -0.01, -32.52, -0.6, ),
            'team_first_birth': 0,
        }, 
    }, 
    33: {
        1: {
            'id': '33.1',
            'space_id': 33,
            'local_id': 1,
            'pos': (19.91, -0.35, -22.57, -0.78, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '33.2',
            'space_id': 33,
            'local_id': 2,
            'pos': (15.32, -0.35, -24.29, -1.58, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '33.3',
            'space_id': 33,
            'local_id': 3,
            'pos': (17.98, -0.35, -27.44, -0.73, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '33.4',
            'space_id': 33,
            'local_id': 4,
            'pos': (17.11, -0.35, -20.93, -0.73, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '33.5',
            'space_id': 33,
            'local_id': 5,
            'pos': (17.88, -0.35, -24.26, -1.15, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '33.6',
            'space_id': 33,
            'local_id': 6,
            'pos': (-20.99, -0.35, 28.16, 0.8, ),
            'team_first_birth': 2,
        }, 
        7: {
            'id': '33.7',
            'space_id': 33,
            'local_id': 7,
            'pos': (-20.29, -0.35, 31.79, 0.38, ),
            'team_first_birth': 2,
        }, 
        8: {
            'id': '33.8',
            'space_id': 33,
            'local_id': 8,
            'pos': (-23.39, -0.35, 26.56, -0.03, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '33.9',
            'space_id': 33,
            'local_id': 9,
            'pos': (-20.98, -0.35, 29.43, -0.03, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '33.10',
            'space_id': 33,
            'local_id': 10,
            'pos': (-17.18, -0.35, 30.46, 1.2, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '33.11',
            'space_id': 33,
            'local_id': 11,
            'pos': (1.75, -0.35, 28.57, 1.39, ),
            'team_first_birth': 0,
        }, 
        12: {
            'id': '33.12',
            'space_id': 33,
            'local_id': 12,
            'pos': (0.7, -0.35, 23.77, -0.9, ),
            'team_first_birth': 0,
        }, 
        13: {
            'id': '33.13',
            'space_id': 33,
            'local_id': 13,
            'pos': (7.46, -0.35, 27.69, -2.74, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '33.14',
            'space_id': 33,
            'local_id': 14,
            'pos': (4.96, -0.35, 29.01, -2.74, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '33.15',
            'space_id': 33,
            'local_id': 15,
            'pos': (-1.05, -0.35, 25.36, -2.18, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '33.16',
            'space_id': 33,
            'local_id': 16,
            'pos': (-2.42, -0.35, -21.16, -0.57, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '33.17',
            'space_id': 33,
            'local_id': 17,
            'pos': (-5.97, -0.35, -21.69, 0.08, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '33.18',
            'space_id': 33,
            'local_id': 18,
            'pos': (-4.71, -0.35, -25.24, 1.76, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '33.19',
            'space_id': 33,
            'local_id': 19,
            'pos': (-8.47, -0.35, -23.35, 1.7, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '33.20',
            'space_id': 33,
            'local_id': 20,
            'pos': (-10.95, -0.35, -22.22, -1.39, ),
            'team_first_birth': 0,
        }, 
    }, 
    37: {
        1: {
            'id': '37.1',
            'space_id': 37,
            'local_id': 1,
            'pos': (-72.96, 1.5, -65.14, -2.98, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '37.2',
            'space_id': 37,
            'local_id': 2,
            'pos': (-59.78, 1.5, -51.17, 0.78, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '37.3',
            'space_id': 37,
            'local_id': 3,
            'pos': (-68.83, 1.5, -44, -2.16, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '37.4',
            'space_id': 37,
            'local_id': 4,
            'pos': (-78.31, 1.5, -48.39, -0.1, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '37.5',
            'space_id': 37,
            'local_id': 5,
            'pos': (-46.19, 1.5, -50.11, -1.37, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '37.6',
            'space_id': 37,
            'local_id': 6,
            'pos': (-23.05, 1.5, -60.45, 2.53, ),
            'team_first_birth': 2,
        }, 
        7: {
            'id': '37.7',
            'space_id': 37,
            'local_id': 7,
            'pos': (-1.19, 1.5, -37.34, 1.93, ),
            'team_first_birth': 2,
        }, 
        8: {
            'id': '37.8',
            'space_id': 37,
            'local_id': 8,
            'pos': (-30.06, 1.5, -31.66, 3.12, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '37.9',
            'space_id': 37,
            'local_id': 9,
            'pos': (-29.75, 5.5, -11.77, 1.53, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '37.10',
            'space_id': 37,
            'local_id': 10,
            'pos': (-47.75, 5.5, -3.03, -0.66, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '37.11',
            'space_id': 37,
            'local_id': 11,
            'pos': (-44.68, 5.5, -25.56, 1.88, ),
            'team_first_birth': 0,
        }, 
        12: {
            'id': '37.12',
            'space_id': 37,
            'local_id': 12,
            'pos': (-64.6, 5.5, -13.21, -1.61, ),
            'team_first_birth': 0,
        }, 
        13: {
            'id': '37.13',
            'space_id': 37,
            'local_id': 13,
            'pos': (-69.32, 5.5, -12.61, 1.13, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '37.14',
            'space_id': 37,
            'local_id': 14,
            'pos': (-67.54, 5.5, -8.7, -2.06, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '37.15',
            'space_id': 37,
            'local_id': 15,
            'pos': (-83.48, 5.5, -21, -2.13, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '37.16',
            'space_id': 37,
            'local_id': 16,
            'pos': (-68.12, 5.5, -57.75, -1.83, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '37.17',
            'space_id': 37,
            'local_id': 17,
            'pos': (-52.55, 5.5, -50.21, -1.18, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '37.18',
            'space_id': 37,
            'local_id': 18,
            'pos': (-45.12, 5.5, -63.86, -2.94, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '37.19',
            'space_id': 37,
            'local_id': 19,
            'pos': (-45.13, 1.5, -63.34, 3.03, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '37.20',
            'space_id': 37,
            'local_id': 20,
            'pos': (-43.72, 5.5, -58.49, 3.01, ),
            'team_first_birth': 0,
        }, 
    }, 
    41: {
        1: {
            'id': '41.1',
            'space_id': 41,
            'local_id': 1,
            'pos': (-84.85, 0.54, -10.47, -2.77, ),
            'team_first_birth': 2,
        }, 
        2: {
            'id': '41.2',
            'space_id': 41,
            'local_id': 2,
            'pos': (-84.68, 0.54, -8.04, -2.8, ),
            'team_first_birth': 2,
        }, 
        3: {
            'id': '41.3',
            'space_id': 41,
            'local_id': 3,
            'pos': (-82.81, 0.54, -7.13, -2.62, ),
            'team_first_birth': 2,
        }, 
        4: {
            'id': '41.4',
            'space_id': 41,
            'local_id': 4,
            'pos': (-80.75, 0.54, -8.65, -2.22, ),
            'team_first_birth': 2,
        }, 
        5: {
            'id': '41.5',
            'space_id': 41,
            'local_id': 5,
            'pos': (-80.62, 0.54, -7.16, -2.4, ),
            'team_first_birth': 2,
        }, 
        6: {
            'id': '41.6',
            'space_id': 41,
            'local_id': 6,
            'pos': (-82.89, 0.57, -3.72, -2.83, ),
            'team_first_birth': 2,
        }, 
        7: {
            'id': '41.7',
            'space_id': 41,
            'local_id': 7,
            'pos': (-46.31, 0.54, -74.1, -0.9, ),
            'team_first_birth': 1,
        }, 
        8: {
            'id': '41.8',
            'space_id': 41,
            'local_id': 8,
            'pos': (-49.05, 0.54, -74.1, -0.81, ),
            'team_first_birth': 1,
        }, 
        9: {
            'id': '41.9',
            'space_id': 41,
            'local_id': 9,
            'pos': (-51.26, 0.54, -74.1, -0.45, ),
            'team_first_birth': 1,
        }, 
        10: {
            'id': '41.10',
            'space_id': 41,
            'local_id': 10,
            'pos': (-46.98, 0.54, -72.49, -1.09, ),
            'team_first_birth': 1,
        }, 
        11: {
            'id': '41.11',
            'space_id': 41,
            'local_id': 11,
            'pos': (-49.36, 0.54, -72.27, -1.13, ),
            'team_first_birth': 1,
        }, 
        12: {
            'id': '41.12',
            'space_id': 41,
            'local_id': 12,
            'pos': (-44.81, 0.54, -73.17, -1.44, ),
            'team_first_birth': 1,
        }, 
        13: {
            'id': '41.13',
            'space_id': 41,
            'local_id': 13,
            'pos': (-67.91, 0.51, -1.14, -2.31, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '41.14',
            'space_id': 41,
            'local_id': 14,
            'pos': (-76.34, 0.51, -0.24, -2.55, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '41.15',
            'space_id': 41,
            'local_id': 15,
            'pos': (-80.36, 0.51, 1.78, -2.75, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '41.16',
            'space_id': 41,
            'local_id': 16,
            'pos': (-69.56, 0.53, -10.48, -2.56, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '41.17',
            'space_id': 41,
            'local_id': 17,
            'pos': (-61.6, 4.49, -8.12, -2.2, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '41.18',
            'space_id': 41,
            'local_id': 18,
            'pos': (-72.6, 4.49, -8.44, -2.25, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '41.19',
            'space_id': 41,
            'local_id': 19,
            'pos': (-78.94, 4.49, -8.11, -2.56, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '41.20',
            'space_id': 41,
            'local_id': 20,
            'pos': (-73.15, 4.49, -12.76, -2.06, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '41.21',
            'space_id': 41,
            'local_id': 21,
            'pos': (-62.77, 4.49, -19.36, -1.52, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '41.22',
            'space_id': 41,
            'local_id': 22,
            'pos': (-52.94, 4.49, -18.04, -1.72, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '41.23',
            'space_id': 41,
            'local_id': 23,
            'pos': (-54.3, 4.49, -10.2, -1.98, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '41.24',
            'space_id': 41,
            'local_id': 24,
            'pos': (-57.72, 4.49, -5.69, -1.91, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '41.25',
            'space_id': 41,
            'local_id': 25,
            'pos': (-57.25, 4.51, -51.54, -1.25, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '41.26',
            'space_id': 41,
            'local_id': 26,
            'pos': (-62.86, 4.51, -49.34, -1.34, ),
            'team_first_birth': 0,
        }, 
        27: {
            'id': '41.27',
            'space_id': 41,
            'local_id': 27,
            'pos': (-67.95, 4.51, -52.74, -0.83, ),
            'team_first_birth': 0,
        }, 
        28: {
            'id': '41.28',
            'space_id': 41,
            'local_id': 28,
            'pos': (-72.06, 4.49, -40.17, -1.49, ),
            'team_first_birth': 0,
        }, 
        29: {
            'id': '41.29',
            'space_id': 41,
            'local_id': 29,
            'pos': (-67.39, 2.26, -58.51, -0.96, ),
            'team_first_birth': 0,
        }, 
        30: {
            'id': '41.30',
            'space_id': 41,
            'local_id': 30,
            'pos': (-58.31, 0.54, -51.33, -1.24, ),
            'team_first_birth': 0,
        }, 
        31: {
            'id': '41.31',
            'space_id': 41,
            'local_id': 31,
            'pos': (-63.67, 0.54, -51.37, -1.24, ),
            'team_first_birth': 0,
        }, 
        32: {
            'id': '41.32',
            'space_id': 41,
            'local_id': 32,
            'pos': (-57.63, 0.63, -44.25, -1.14, ),
            'team_first_birth': 0,
        }, 
        33: {
            'id': '41.33',
            'space_id': 41,
            'local_id': 33,
            'pos': (-72.11, 0.53, -58.07, -0.89, ),
            'team_first_birth': 0,
        }, 
        34: {
            'id': '41.34',
            'space_id': 41,
            'local_id': 34,
            'pos': (-52.86, 4.51, -57.6, -0.76, ),
            'team_first_birth': 0,
        }, 
        35: {
            'id': '41.35',
            'space_id': 41,
            'local_id': 35,
            'pos': (-52.99, 4.51, -54.61, -1.07, ),
            'team_first_birth': 0,
        }, 
        36: {
            'id': '41.36',
            'space_id': 41,
            'local_id': 36,
            'pos': (-54.21, 4.51, -61.76, -0.41, ),
            'team_first_birth': 0,
        }, 
        37: {
            'id': '41.37',
            'space_id': 41,
            'local_id': 37,
            'pos': (-46.88, 4.51, -50.57, -1.4, ),
            'team_first_birth': 0,
        }, 
        38: {
            'id': '41.38',
            'space_id': 41,
            'local_id': 38,
            'pos': (-53.71, 0.54, -48.11, -1.25, ),
            'team_first_birth': 0,
        }, 
        39: {
            'id': '41.39',
            'space_id': 41,
            'local_id': 39,
            'pos': (-81.9, 0.53, -11.0, -2.74, ),
            'team_first_birth': 0,
        }, 
        40: {
            'id': '41.40',
            'space_id': 41,
            'local_id': 40,
            'pos': (-76.49, 0.53, -4.35, -2.62, ),
            'team_first_birth': 0,
        }, 
        41: {
            'id': '41.41',
            'space_id': 41,
            'local_id': 41,
            'pos': (-75.79, 0.53, -9.12, 2.92, ),
            'team_first_birth': 0,
        }, 
        42: {
            'id': '41.42',
            'space_id': 41,
            'local_id': 42,
            'pos': (-63.83, 4.49, -11.18, -2.92, ),
            'team_first_birth': 0,
        }, 
        43: {
            'id': '41.43',
            'space_id': 41,
            'local_id': 43,
            'pos': (-75.83, 4.49, -18.65, 2.91, ),
            'team_first_birth': 0,
        }, 
        44: {
            'id': '41.44',
            'space_id': 41,
            'local_id': 44,
            'pos': (-83.34, 4.49, -24.02, 2.99, ),
            'team_first_birth': 0,
        }, 
        45: {
            'id': '41.45',
            'space_id': 41,
            'local_id': 45,
            'pos': (-84.15, 4.49, -13.6, -3.13, ),
            'team_first_birth': 0,
        }, 
        46: {
            'id': '41.46',
            'space_id': 41,
            'local_id': 46,
            'pos': (-88.61, 4.48, -25.39, -3.14, ),
            'team_first_birth': 0,
        }, 
        47: {
            'id': '41.47',
            'space_id': 41,
            'local_id': 47,
            'pos': (-82.7, 4.49, -29.83, 2.68, ),
            'team_first_birth': 0,
        }, 
        48: {
            'id': '41.48',
            'space_id': 41,
            'local_id': 48,
            'pos': (-56.3, 4.49, -21.04, -2.55, ),
            'team_first_birth': 0,
        }, 
        49: {
            'id': '41.49',
            'space_id': 41,
            'local_id': 49,
            'pos': (-55.04, 4.49, -14.13, -2.8, ),
            'team_first_birth': 0,
        }, 
        50: {
            'id': '41.50',
            'space_id': 41,
            'local_id': 50,
            'pos': (-45.61, 0.55, -24.75, -2.45, ),
            'team_first_birth': 0,
        }, 
        51: {
            'id': '41.51',
            'space_id': 41,
            'local_id': 51,
            'pos': (-66.71, 0.54, -22.77, -3.03, ),
            'team_first_birth': 0,
        }, 
        52: {
            'id': '41.52',
            'space_id': 41,
            'local_id': 52,
            'pos': (-44.58, 4.51, -57.37, -1.13, ),
            'team_first_birth': 0,
        }, 
        53: {
            'id': '41.53',
            'space_id': 41,
            'local_id': 53,
            'pos': (-48.21, 4.51, -48.21, -1.84, ),
            'team_first_birth': 0,
        }, 
        54: {
            'id': '41.54',
            'space_id': 41,
            'local_id': 54,
            'pos': (-48.74, 4.51, -56.62, -1.15, ),
            'team_first_birth': 0,
        }, 
        55: {
            'id': '41.55',
            'space_id': 41,
            'local_id': 55,
            'pos': (-54.06, 4.52, -64.66, -0.45, ),
            'team_first_birth': 0,
        }, 
        56: {
            'id': '41.56',
            'space_id': 41,
            'local_id': 56,
            'pos': (-37.02, 4.51, -57.82, -1.35, ),
            'team_first_birth': 0,
        }, 
        57: {
            'id': '41.57',
            'space_id': 41,
            'local_id': 57,
            'pos': (-41.56, 4.51, -56.2, -1.43, ),
            'team_first_birth': 0,
        }, 
        58: {
            'id': '41.58',
            'space_id': 41,
            'local_id': 58,
            'pos': (-46.77, 0.54, -65.46, -0.95, ),
            'team_first_birth': 0,
        }, 
        59: {
            'id': '41.59',
            'space_id': 41,
            'local_id': 59,
            'pos': (-45.94, 0.54, -62.13, -1.05, ),
            'team_first_birth': 0,
        }, 
        60: {
            'id': '41.60',
            'space_id': 41,
            'local_id': 60,
            'pos': (-42.65, 0.54, -57.38, -1.36, ),
            'team_first_birth': 0,
        }, 
        61: {
            'id': '41.61',
            'space_id': 41,
            'local_id': 61,
            'pos': (-46.6, 0.54, -51.35, -1.44, ),
            'team_first_birth': 0,
        }, 
        62: {
            'id': '41.62',
            'space_id': 41,
            'local_id': 62,
            'pos': (-51.89, 0.54, -56.73, -1.23, ),
            'team_first_birth': 0,
        }, 
        63: {
            'id': '41.63',
            'space_id': 41,
            'local_id': 63,
            'pos': (-53.72, 0.54, -64.5, -0.93, ),
            'team_first_birth': 0,
        }, 
        64: {
            'id': '41.64',
            'space_id': 41,
            'local_id': 64,
            'pos': (-53.77, 0.54, -72.51, -0.8, ),
            'team_first_birth': 0,
        }, 
        65: {
            'id': '41.65',
            'space_id': 41,
            'local_id': 65,
            'pos': (-48.63, 0.54, -70.81, -1.14, ),
            'team_first_birth': 0,
        }, 
        66: {
            'id': '41.66',
            'space_id': 41,
            'local_id': 66,
            'pos': (-45.86, 0.54, -70.53, -1.41, ),
            'team_first_birth': 0,
        }, 
        67: {
            'id': '41.67',
            'space_id': 41,
            'local_id': 67,
            'pos': (-38.17, 0.54, -62.06, -1.39, ),
            'team_first_birth': 0,
        }, 
        68: {
            'id': '41.68',
            'space_id': 41,
            'local_id': 68,
            'pos': (-29.08, 0.54, -65.09, -0.18, ),
            'team_first_birth': 0,
        }, 
        69: {
            'id': '41.69',
            'space_id': 41,
            'local_id': 69,
            'pos': (-29.73, 0.54, -55.43, -0.22, ),
            'team_first_birth': 0,
        }, 
        70: {
            'id': '41.70',
            'space_id': 41,
            'local_id': 70,
            'pos': (-31.23, 0.54, -51.86, -0.32, ),
            'team_first_birth': 0,
        }, 
        71: {
            'id': '41.71',
            'space_id': 41,
            'local_id': 71,
            'pos': (-16.71, 0.56, -49.98, -0.57, ),
            'team_first_birth': 0,
        }, 
        72: {
            'id': '41.72',
            'space_id': 41,
            'local_id': 72,
            'pos': (-17.94, 0.58, -38.94, -0.84, ),
            'team_first_birth': 0,
        }, 
        73: {
            'id': '41.73',
            'space_id': 41,
            'local_id': 73,
            'pos': (-24.95, 0.55, -41.13, -0.13, ),
            'team_first_birth': 0,
        }, 
        74: {
            'id': '41.74',
            'space_id': 41,
            'local_id': 74,
            'pos': (-33.34, 0.56, -39.13, -0.68, ),
            'team_first_birth': 0,
        }, 
        75: {
            'id': '41.75',
            'space_id': 41,
            'local_id': 75,
            'pos': (-54.27, 0.54, -50.9, -0.01, ),
            'team_first_birth': 0,
        }, 
        76: {
            'id': '41.76',
            'space_id': 41,
            'local_id': 76,
            'pos': (-55.57, 4.51, -58.55, 0.5, ),
            'team_first_birth': 0,
        }, 
        77: {
            'id': '41.77',
            'space_id': 41,
            'local_id': 77,
            'pos': (-42.98, 4.51, -58.11, -0.02, ),
            'team_first_birth': 0,
        }, 
        78: {
            'id': '41.78',
            'space_id': 41,
            'local_id': 78,
            'pos': (-32.86, 4.51, -58.87, -0.17, ),
            'team_first_birth': 0,
        }, 
        79: {
            'id': '41.79',
            'space_id': 41,
            'local_id': 79,
            'pos': (-83.85, 4.49, -15.32, 1.17, ),
            'team_first_birth': 0,
        }, 
        80: {
            'id': '41.80',
            'space_id': 41,
            'local_id': 80,
            'pos': (-82.79, 4.49, -10.91, 1.42, ),
            'team_first_birth': 0,
        }, 
        81: {
            'id': '41.81',
            'space_id': 41,
            'local_id': 81,
            'pos': (-83.66, 4.49, -33.28, 0.91, ),
            'team_first_birth': 0,
        }, 
        82: {
            'id': '41.82',
            'space_id': 41,
            'local_id': 82,
            'pos': (-75.91, 4.49, -36.11, 0.87, ),
            'team_first_birth': 0,
        }, 
        83: {
            'id': '41.83',
            'space_id': 41,
            'local_id': 83,
            'pos': (-70.24, 4.51, -41.23, 0.45, ),
            'team_first_birth': 0,
        }, 
        84: {
            'id': '41.84',
            'space_id': 41,
            'local_id': 84,
            'pos': (-76.82, 0.53, -13.09, 1.44, ),
            'team_first_birth': 0,
        }, 
        85: {
            'id': '41.85',
            'space_id': 41,
            'local_id': 85,
            'pos': (-78.8, 0.51, -2.56, 1.76, ),
            'team_first_birth': 0,
        }, 
        86: {
            'id': '41.86',
            'space_id': 41,
            'local_id': 86,
            'pos': (-87.07, 0.58, -9.32, 1.42, ),
            'team_first_birth': 0,
        }, 
        87: {
            'id': '41.87',
            'space_id': 41,
            'local_id': 87,
            'pos': (-69.75, 4.51, -25.57, 0.48, ),
            'team_first_birth': 0,
        }, 
        88: {
            'id': '41.88',
            'space_id': 41,
            'local_id': 88,
            'pos': (-83.99, 0.53, -55.19, 1.32, ),
            'team_first_birth': 0,
        }, 
        89: {
            'id': '41.89',
            'space_id': 41,
            'local_id': 89,
            'pos': (-81.51, 0.53, -59.4, 1.07, ),
            'team_first_birth': 0,
        }, 
        90: {
            'id': '41.90',
            'space_id': 41,
            'local_id': 90,
            'pos': (-78.52, 0.53, -58.0, 1.03, ),
            'team_first_birth': 0,
        }, 
        91: {
            'id': '41.91',
            'space_id': 41,
            'local_id': 91,
            'pos': (-76.99, 0.53, -48.27, 1.72, ),
            'team_first_birth': 0,
        }, 
        92: {
            'id': '41.92',
            'space_id': 41,
            'local_id': 92,
            'pos': (-83.45, 4.49, -43.32, 1.84, ),
            'team_first_birth': 0,
        }, 
        93: {
            'id': '41.93',
            'space_id': 41,
            'local_id': 93,
            'pos': (-77.56, 4.51, -47.46, 1.75, ),
            'team_first_birth': 0,
        }, 
        94: {
            'id': '41.94',
            'space_id': 41,
            'local_id': 94,
            'pos': (-70.32, 0.53, -37.81, 2.0, ),
            'team_first_birth': 0,
        }, 
        95: {
            'id': '41.95',
            'space_id': 41,
            'local_id': 95,
            'pos': (-69.14, 0.53, -42.96, 1.93, ),
            'team_first_birth': 0,
        }, 
        96: {
            'id': '41.96',
            'space_id': 41,
            'local_id': 96,
            'pos': (-69.9, 0.53, -32.44, 2.19, ),
            'team_first_birth': 0,
        }, 
        97: {
            'id': '41.97',
            'space_id': 41,
            'local_id': 97,
            'pos': (-10.34, -0.33, -31.76, -2.39, ),
            'team_first_birth': 0,
        }, 
        98: {
            'id': '41.98',
            'space_id': 41,
            'local_id': 98,
            'pos': (-14.55, 0.58, -30.44, -2.49, ),
            'team_first_birth': 0,
        }, 
        99: {
            'id': '41.99',
            'space_id': 41,
            'local_id': 99,
            'pos': (-26.4, 0.55, -24.99, -2.97, ),
            'team_first_birth': 0,
        }, 
        100: {
            'id': '41.100',
            'space_id': 41,
            'local_id': 100,
            'pos': (-23.33, 4.48, -16.62, -2.96, ),
            'team_first_birth': 0,
        }, 
        101: {
            'id': '41.101',
            'space_id': 41,
            'local_id': 101,
            'pos': (-32.08, 4.49, -19.58, -2.97, ),
            'team_first_birth': 0,
        }, 
        102: {
            'id': '41.102',
            'space_id': 41,
            'local_id': 102,
            'pos': (-42.28, 4.5, -20.26, 2.98, ),
            'team_first_birth': 0,
        }, 
        103: {
            'id': '41.103',
            'space_id': 41,
            'local_id': 103,
            'pos': (-13.25, 0.56, -42.12, -1.9, ),
            'team_first_birth': 0,
        }, 
        104: {
            'id': '41.104',
            'space_id': 41,
            'local_id': 104,
            'pos': (-23.74, 4.48, -21.8, -2.86, ),
            'team_first_birth': 0,
        }, 
        105: {
            'id': '41.105',
            'space_id': 41,
            'local_id': 105,
            'pos': (-37.75, 4.5, -5.52, -2.2, ),
            'team_first_birth': 0,
        }, 
        106: {
            'id': '41.106',
            'space_id': 41,
            'local_id': 106,
            'pos': (-43.25, 4.49, -13.26, -1.77, ),
            'team_first_birth': 0,
        }, 
        107: {
            'id': '41.107',
            'space_id': 41,
            'local_id': 107,
            'pos': (-51.04, 4.49, -14.56, -1.68, ),
            'team_first_birth': 0,
        }, 
        108: {
            'id': '41.108',
            'space_id': 41,
            'local_id': 108,
            'pos': (-51.99, 4.49, -23.16, -1.26, ),
            'team_first_birth': 0,
        }, 
        109: {
            'id': '41.109',
            'space_id': 41,
            'local_id': 109,
            'pos': (-64.51, 0.59, 2.76, -2.58, ),
            'team_first_birth': 0,
        }, 
        110: {
            'id': '41.110',
            'space_id': 41,
            'local_id': 110,
            'pos': (-71.36, 0.51, 2.93, -3.06, ),
            'team_first_birth': 0,
        }, 
        111: {
            'id': '41.111',
            'space_id': 41,
            'local_id': 111,
            'pos': (-53.45, 4.51, -51.47, -1.06, ),
            'team_first_birth': 0,
        }, 
        112: {
            'id': '41.112',
            'space_id': 41,
            'local_id': 112,
            'pos': (-51.42, 4.51, -56.73, -0.84, ),
            'team_first_birth': 0,
        }, 
        113: {
            'id': '41.113',
            'space_id': 41,
            'local_id': 113,
            'pos': (-75.04, 0.56, -60.73, -0.16, ),
            'team_first_birth': 0,
        }, 
        114: {
            'id': '41.114',
            'space_id': 41,
            'local_id': 114,
            'pos': (-79.76, 0.53, -60.62, 0.14, ),
            'team_first_birth': 0,
        }, 
        115: {
            'id': '41.115',
            'space_id': 41,
            'local_id': 115,
            'pos': (-48.79, 0.55, -44.01, -1.3, ),
            'team_first_birth': 0,
        }, 
        116: {
            'id': '41.116',
            'space_id': 41,
            'local_id': 116,
            'pos': (-100.62, 0.54, -34.85, 1.34, ),
            'team_first_birth': 0,
        }, 
        117: {
            'id': '41.117',
            'space_id': 41,
            'local_id': 117,
            'pos': (-96.14, 2.5, -44.82, 1.07, ),
            'team_first_birth': 0,
        }, 
    }, 
    46: {
        1: {
            'id': '46.1',
            'space_id': 46,
            'local_id': 1,
            'pos': (1.44, 0.04, -2.64, 2.45, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '46.2',
            'space_id': 46,
            'local_id': 2,
            'pos': (0.85, 0.0, -11.38, 1.45, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '46.3',
            'space_id': 46,
            'local_id': 3,
            'pos': (5.05, 0.1, -14.47, 0.48, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '46.4',
            'space_id': 46,
            'local_id': 4,
            'pos': (7.02, 0.01, -1.6, 3.04, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '46.5',
            'space_id': 46,
            'local_id': 5,
            'pos': (13.47, 0.01, -2.38, -3.08, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '46.6',
            'space_id': 46,
            'local_id': 6,
            'pos': (12.4, -0.05, -7.98, 1.45, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '46.7',
            'space_id': 46,
            'local_id': 7,
            'pos': (32.7, 0.01, -34.32, -0.79, ),
            'team_first_birth': 2,
        }, 
        8: {
            'id': '46.8',
            'space_id': 46,
            'local_id': 8,
            'pos': (32.46, 0.01, -25.69, -0.06, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '46.9',
            'space_id': 46,
            'local_id': 9,
            'pos': (29.11, 0.0, -28.48, -1.55, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '46.10',
            'space_id': 46,
            'local_id': 10,
            'pos': (23.59, 0.0, -34.9, -0.02, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '46.11',
            'space_id': 46,
            'local_id': 11,
            'pos': (28.99, 0.0, -33.16, -0.86, ),
            'team_first_birth': 2,
        }, 
        12: {
            'id': '46.12',
            'space_id': 46,
            'local_id': 12,
            'pos': (22.87, 0.0, -29.67, -1.42, ),
            'team_first_birth': 2,
        }, 
        13: {
            'id': '46.13',
            'space_id': 46,
            'local_id': 13,
            'pos': (1.24, 0.0, -34.39, 0.61, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '46.14',
            'space_id': 46,
            'local_id': 14,
            'pos': (11.17, -0.01, -33.75, -0.3, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '46.15',
            'space_id': 46,
            'local_id': 15,
            'pos': (17.1, 0.24, -32.6, -1.37, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '46.16',
            'space_id': 46,
            'local_id': 16,
            'pos': (12.57, -0.01, -29.69, 1.25, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '46.17',
            'space_id': 46,
            'local_id': 17,
            'pos': (4.48, 0.18, -23.17, 1.67, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '46.18',
            'space_id': 46,
            'local_id': 18,
            'pos': (4.32, -0.01, -29.43, 0.13, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '46.19',
            'space_id': 46,
            'local_id': 19,
            'pos': (3.2, 0.31, -19.5, -0.16, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '46.20',
            'space_id': 46,
            'local_id': 20,
            'pos': (18.31, 0.24, -1.51, 1.69, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '46.21',
            'space_id': 46,
            'local_id': 21,
            'pos': (23.73, 0.06, -2.85, 2.56, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '46.22',
            'space_id': 46,
            'local_id': 22,
            'pos': (31.42, 0.05, -3.01, -2.78, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '46.23',
            'space_id': 46,
            'local_id': 23,
            'pos': (33.06, 0.09, -9.7, -2.14, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '46.24',
            'space_id': 46,
            'local_id': 24,
            'pos': (27.55, 0.09, -9.76, -3.06, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '46.25',
            'space_id': 46,
            'local_id': 25,
            'pos': (32.56, 0.25, -15.62, 2.97, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '46.26',
            'space_id': 46,
            'local_id': 26,
            'pos': (17.41, -0.01, -11.73, 3.13, ),
            'team_first_birth': 0,
        }, 
        27: {
            'id': '46.27',
            'space_id': 46,
            'local_id': 27,
            'pos': (12.98, -0.03, -19.08, 1.47, ),
            'team_first_birth': 0,
        }, 
        28: {
            'id': '46.28',
            'space_id': 46,
            'local_id': 28,
            'pos': (17.85, -0.06, -24.81, 0.01, ),
            'team_first_birth': 0,
        }, 
        29: {
            'id': '46.29',
            'space_id': 46,
            'local_id': 29,
            'pos': (22.78, 0.01, -17.93, -1.56, ),
            'team_first_birth': 0,
        }, 
        30: {
            'id': '46.30',
            'space_id': 46,
            'local_id': 30,
            'pos': (18.67, -0.02, -19.17, -0.81, ),
            'team_first_birth': 0,
        }, 
    }, 
    53: {
        1: {
            'id': '53.1',
            'space_id': 53,
            'local_id': 1,
            'pos': (28.8, 3.7, -10.81, -3.08, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '53.2',
            'space_id': 53,
            'local_id': 2,
            'pos': (28.79, 3.7, -6.52, -2.75, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '53.3',
            'space_id': 53,
            'local_id': 3,
            'pos': (24.37, 3.7, -10.75, -2.71, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '53.4',
            'space_id': 53,
            'local_id': 4,
            'pos': (24.84, 3.7, -6.22, -2.92, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '53.5',
            'space_id': 53,
            'local_id': 5,
            'pos': (19.7, 3.7, -10.23, 3.06, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '53.6',
            'space_id': 53,
            'local_id': 6,
            'pos': (20.59, 3.7, -6.52, -2.86, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '53.7',
            'space_id': 53,
            'local_id': 7,
            'pos': (12.93, 3.7, -76.85, 0.1, ),
            'team_first_birth': 2,
        }, 
        8: {
            'id': '53.8',
            'space_id': 53,
            'local_id': 8,
            'pos': (10.87, 3.7, -72.06, 1.32, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '53.9',
            'space_id': 53,
            'local_id': 9,
            'pos': (15.87, 3.7, -74.43, 0.03, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '53.10',
            'space_id': 53,
            'local_id': 10,
            'pos': (17.4, 3.7, -70.15, -0.66, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '53.11',
            'space_id': 53,
            'local_id': 11,
            'pos': (18.98, 3.7, -73.0, -0.11, ),
            'team_first_birth': 2,
        }, 
        12: {
            'id': '53.12',
            'space_id': 53,
            'local_id': 12,
            'pos': (21.85, 3.7, -71.89, -0.26, ),
            'team_first_birth': 2,
        }, 
        13: {
            'id': '53.13',
            'space_id': 53,
            'local_id': 13,
            'pos': (9.37, 3.7, -63.9, 1.51, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '53.14',
            'space_id': 53,
            'local_id': 14,
            'pos': (22.07, 3.7, -67.02, -0.61, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '53.15',
            'space_id': 53,
            'local_id': 15,
            'pos': (34.11, 3.7, -61.82, -0.41, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '53.16',
            'space_id': 53,
            'local_id': 16,
            'pos': (34.09, 3.7, -73.47, 0.98, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '53.17',
            'space_id': 53,
            'local_id': 17,
            'pos': (37.17, 3.7, -76.01, -0.1, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '53.18',
            'space_id': 53,
            'local_id': 18,
            'pos': (31.7, 3.7, -68.83, -0.24, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '53.19',
            'space_id': 53,
            'local_id': 19,
            'pos': (26.02, 3.7, -50.78, 2.23, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '53.20',
            'space_id': 53,
            'local_id': 20,
            'pos': (28.24, 3.7, -4.43, -2.41, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '53.21',
            'space_id': 53,
            'local_id': 21,
            'pos': (21.1, 3.7, -1.98, 2.41, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '53.22',
            'space_id': 53,
            'local_id': 22,
            'pos': (23.31, 3.7, -18.76, 1.14, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '53.23',
            'space_id': 53,
            'local_id': 23,
            'pos': (7.26, 3.7, -11.39, 1.52, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '53.24',
            'space_id': 53,
            'local_id': 24,
            'pos': (12.89, 3.7, -12.59, -2.42, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '53.25',
            'space_id': 53,
            'local_id': 25,
            'pos': (12.9, 3.7, -18.54, -1.26, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '53.26',
            'space_id': 53,
            'local_id': 26,
            'pos': (9.02, 3.7, -8.16, -3.02, ),
            'team_first_birth': 0,
        }, 
    }, 
    77: {
        1: {
            'id': '77.1',
            'space_id': 77,
            'local_id': 1,
            'pos': (1679.4, 19.3, 2512, 0.39, ),
            'team_first_birth': 2,
        }, 
        2: {
            'id': '77.2',
            'space_id': 77,
            'local_id': 2,
            'pos': (1740.41, 23.02, 2573.94, -1.88, ),
            'team_first_birth': 2,
        }, 
        3: {
            'id': '77.3',
            'space_id': 77,
            'local_id': 3,
            'pos': (1728.1, 23.02, 2566.16, -2.88, ),
            'team_first_birth': 2,
        }, 
        4: {
            'id': '77.4',
            'space_id': 77,
            'local_id': 4,
            'pos': (1715.54, 21.59, 2557.0, -1.89, ),
            'team_first_birth': 2,
        }, 
        5: {
            'id': '77.5',
            'space_id': 77,
            'local_id': 5,
            'pos': (1693.79, 21.35, 2579.36, 2.5, ),
            'team_first_birth': 2,
        }, 
        6: {
            'id': '77.6',
            'space_id': 77,
            'local_id': 6,
            'pos': (1688.24, 21.22, 2566.19, 2.9, ),
            'team_first_birth': 2,
        }, 
        7: {
            'id': '77.7',
            'space_id': 77,
            'local_id': 7,
            'pos': (1682.52, 21.22, 2571.24, -2.65, ),
            'team_first_birth': 1,
        }, 
        8: {
            'id': '77.8',
            'space_id': 77,
            'local_id': 8,
            'pos': (1673.56, 18.85, 2557.9, 1.55, ),
            'team_first_birth': 1,
        }, 
        9: {
            'id': '77.9',
            'space_id': 77,
            'local_id': 9,
            'pos': (1659.78, 20.67, 2574.15, 3.06, ),
            'team_first_birth': 1,
        }, 
        10: {
            'id': '77.10',
            'space_id': 77,
            'local_id': 10,
            'pos': (1669.03, 32.32, 2570.45, 2.7, ),
            'team_first_birth': 1,
        }, 
        11: {
            'id': '77.11',
            'space_id': 77,
            'local_id': 11,
            'pos': (1645.47, 30.96, 2563.78, -0.9, ),
            'team_first_birth': 1,
        }, 
        12: {
            'id': '77.12',
            'space_id': 77,
            'local_id': 12,
            'pos': (1635.84, 30.96, 2554.68, -2.69, ),
            'team_first_birth': 1,
        }, 
        13: {
            'id': '77.13',
            'space_id': 77,
            'local_id': 13,
            'pos': (1653.84, 19.87, 2552.6, 2.53, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '77.14',
            'space_id': 77,
            'local_id': 14,
            'pos': (1630.61, 20.55, 2560.88, 1.91, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '77.15',
            'space_id': 77,
            'local_id': 15,
            'pos': (1629.16, 20.55, 2571.84, 2.83, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '77.16',
            'space_id': 77,
            'local_id': 16,
            'pos': (1642.58, 19.27, 2536.81, 0.71, ),
            'team_first_birth': 0,
        }, 
        17: {
            'id': '77.17',
            'space_id': 77,
            'local_id': 17,
            'pos': (1619.09, 18.73, 2533.96, 1.85, ),
            'team_first_birth': 0,
        }, 
        18: {
            'id': '77.18',
            'space_id': 77,
            'local_id': 18,
            'pos': (1624.63, 18.86, 2508.11, 0.84, ),
            'team_first_birth': 0,
        }, 
        19: {
            'id': '77.19',
            'space_id': 77,
            'local_id': 19,
            'pos': (1633.37, 18.75, 2497.94, 0.93, ),
            'team_first_birth': 0,
        }, 
        20: {
            'id': '77.20',
            'space_id': 77,
            'local_id': 20,
            'pos': (1642.85, 18.75, 2481.82, -0.34, ),
            'team_first_birth': 0,
        }, 
        21: {
            'id': '77.21',
            'space_id': 77,
            'local_id': 21,
            'pos': (1654.5, 18.91, 2492.69, -1.41, ),
            'team_first_birth': 0,
        }, 
        22: {
            'id': '77.22',
            'space_id': 77,
            'local_id': 22,
            'pos': (1650.48, 18.96, 2508.62, 0.66, ),
            'team_first_birth': 0,
        }, 
        23: {
            'id': '77.23',
            'space_id': 77,
            'local_id': 23,
            'pos': (1663.03, 18.75, 2519.46, 0.7, ),
            'team_first_birth': 0,
        }, 
        24: {
            'id': '77.24',
            'space_id': 77,
            'local_id': 24,
            'pos': (1677.43, 18.79, 2502.39, 0.61, ),
            'team_first_birth': 0,
        }, 
        25: {
            'id': '77.25',
            'space_id': 77,
            'local_id': 25,
            'pos': (1698.77, 20.04, 2509.82, 0.35, ),
            'team_first_birth': 0,
        }, 
        26: {
            'id': '77.26',
            'space_id': 77,
            'local_id': 26,
            'pos': (1704.44, 20.04, 2488.29, 0.71, ),
            'team_first_birth': 0,
        }, 
        27: {
            'id': '77.27',
            'space_id': 77,
            'local_id': 27,
            'pos': (1736.48, 20.04, 2486.28, -0.79, ),
            'team_first_birth': 0,
        }, 
        28: {
            'id': '77.28',
            'space_id': 77,
            'local_id': 28,
            'pos': (1747.89, 20.04, 2500.63, -1.14, ),
            'team_first_birth': 0,
        }, 
        29: {
            'id': '77.29',
            'space_id': 77,
            'local_id': 29,
            'pos': (1748.12, 19.97, 2527.31, -1.9, ),
            'team_first_birth': 0,
        }, 
        30: {
            'id': '77.30',
            'space_id': 77,
            'local_id': 30,
            'pos': (1731.05, 21.6, 2536.86, -1.83, ),
            'team_first_birth': 0,
        }, 
        31: {
            'id': '77.31',
            'space_id': 77,
            'local_id': 31,
            'pos': (1733.11, 19.96, 2524.12, -1.01, ),
            'team_first_birth': 0,
        }, 
        32: {
            'id': '77.32',
            'space_id': 77,
            'local_id': 32,
            'pos': (1737.42, 33.47, 2532.44, -0.97, ),
            'team_first_birth': 0,
        }, 
        33: {
            'id': '77.33',
            'space_id': 77,
            'local_id': 33,
            'pos': (1741.06, 33.47, 2553.21, -1.97, ),
            'team_first_birth': 0,
        }, 
        34: {
            'id': '77.34',
            'space_id': 77,
            'local_id': 34,
            'pos': (1736.85, 39.32, 2569.32, -2.27, ),
            'team_first_birth': 0,
        }, 
    }, 
    78: {
        1: {
            'id': '78.1',
            'space_id': 78,
            'local_id': 1,
            'pos': (2058.7, 22.06, 2994.58, 2.64, ),
            'team_first_birth': 2,
        }, 
        2: {
            'id': '78.2',
            'space_id': 78,
            'local_id': 2,
            'pos': (2057.38, 22.06, 2985.8, 3.0, ),
            'team_first_birth': 2,
        }, 
        3: {
            'id': '78.3',
            'space_id': 78,
            'local_id': 3,
            'pos': (2068.54, 22.06, 2991.09, 3.0, ),
            'team_first_birth': 2,
        }, 
        4: {
            'id': '78.4',
            'space_id': 78,
            'local_id': 4,
            'pos': (2045.07, 22.06, 2992.47, 2.47, ),
            'team_first_birth': 2,
        }, 
        5: {
            'id': '78.5',
            'space_id': 78,
            'local_id': 5,
            'pos': (2049.5, 22.06, 2980.15, 2.47, ),
            'team_first_birth': 2,
        }, 
        6: {
            'id': '78.6',
            'space_id': 78,
            'local_id': 6,
            'pos': (2037.17, 22.06, 2992.16, 2.29, ),
            'team_first_birth': 2,
        }, 
        7: {
            'id': '78.7',
            'space_id': 78,
            'local_id': 7,
            'pos': (2077.27, 22.06, 2892.91, -0.39, ),
            'team_first_birth': 1,
        }, 
        8: {
            'id': '78.8',
            'space_id': 78,
            'local_id': 8,
            'pos': (2076.25, 22.06, 2878.61, 0.08, ),
            'team_first_birth': 1,
        }, 
        9: {
            'id': '78.9',
            'space_id': 78,
            'local_id': 9,
            'pos': (2075.49, 24.51, 2866.6, -0.02, ),
            'team_first_birth': 1,
        }, 
        10: {
            'id': '78.10',
            'space_id': 78,
            'local_id': 10,
            'pos': (2072.74, 22.06, 2882.89, 0.01, ),
            'team_first_birth': 1,
        }, 
        11: {
            'id': '78.11',
            'space_id': 78,
            'local_id': 11,
            'pos': (2066.62, 22.06, 2890.95, -0.07, ),
            'team_first_birth': 1,
        }, 
        12: {
            'id': '78.12',
            'space_id': 78,
            'local_id': 12,
            'pos': (2079.0, 22.06, 2886.14, -0.05, ),
            'team_first_birth': 1,
        }, 
    }, 
    79: {
        1: {
            'id': '79.1',
            'space_id': 79,
            'local_id': 1,
            'pos': (2123.56, 21.78, 2823.94, -2.15, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '79.2',
            'space_id': 79,
            'local_id': 2,
            'pos': (2115.29, 22.41, 2767.93, -1.56, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '79.3',
            'space_id': 79,
            'local_id': 3,
            'pos': (2124.16, 21.78, 2785.25, -1.24, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '79.4',
            'space_id': 79,
            'local_id': 4,
            'pos': (2102.33, 21.78, 2816.56, -3.06, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '79.5',
            'space_id': 79,
            'local_id': 5,
            'pos': (2116.86, 22.18, 2824.22, -2.56, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '79.6',
            'space_id': 79,
            'local_id': 6,
            'pos': (2118.95, 21.78, 2778.59, -0.77, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '79.7',
            'space_id': 79,
            'local_id': 7,
            'pos': (1980.01, 26.63, 2758.88, 0.97, ),
            'team_first_birth': 2,
        }, 
        8: {
            'id': '79.8',
            'space_id': 79,
            'local_id': 8,
            'pos': (1981.02, 28.32, 2783.68, 1.27, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '79.9',
            'space_id': 79,
            'local_id': 9,
            'pos': (1983.12, 26.63, 2825.79, 1.5, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '79.10',
            'space_id': 79,
            'local_id': 10,
            'pos': (1960.34, 28.32, 2811.46, 1.45, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '79.11',
            'space_id': 79,
            'local_id': 11,
            'pos': (1988.39, 28.32, 2798.9, 1.54, ),
            'team_first_birth': 2,
        }, 
        12: {
            'id': '79.12',
            'space_id': 79,
            'local_id': 12,
            'pos': (1992.51, 26.63, 2764.22, 2.04, ),
            'team_first_birth': 2,
        }, 
        13: {
            'id': '79.13',
            'space_id': 79,
            'local_id': 13,
            'pos': (2041.93, 26.63, 2761.1, 1.57, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '79.14',
            'space_id': 79,
            'local_id': 14,
            'pos': (2069.65, 24.47, 2827.15, -1.62, ),
            'team_first_birth': 0,
        }, 
    }, 
    80: {
        1: {
            'id': '80.1',
            'space_id': 80,
            'local_id': 1,
            'pos': (1829.1, 22.13, 3078.37, 1.04, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '80.2',
            'space_id': 80,
            'local_id': 2,
            'pos': (1838.99, 22.15, 3080.01, 1.23, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '80.3',
            'space_id': 80,
            'local_id': 3,
            'pos': (1837.8, 23.11, 3061.81, 0.89, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '80.4',
            'space_id': 80,
            'local_id': 4,
            'pos': (1841.3, 23.11, 3052.13, 0.95, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '80.5',
            'space_id': 80,
            'local_id': 5,
            'pos': (1845.6, 26.81, 3040.75, 0.67, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '80.6',
            'space_id': 80,
            'local_id': 6,
            'pos': (1853.24, 26.77, 3039.47, 0.72, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '80.7',
            'space_id': 80,
            'local_id': 7,
            'pos': (1855.03, 22.13, 3018.23, 0.29, ),
            'team_first_birth': 1,
        }, 
        8: {
            'id': '80.8',
            'space_id': 80,
            'local_id': 8,
            'pos': (1871.35, 22.13, 3026.23, 0.15, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '80.9',
            'space_id': 80,
            'local_id': 9,
            'pos': (1920.22, 22.13, 3081.3, -1.8, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '80.10',
            'space_id': 80,
            'local_id': 10,
            'pos': (1914.47, 22.13, 3090.07, -1.72, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '80.11',
            'space_id': 80,
            'local_id': 11,
            'pos': (1905.57, 22.13, 3107.44, -2.17, ),
            'team_first_birth': 2,
        }, 
        12: {
            'id': '80.12',
            'space_id': 80,
            'local_id': 12,
            'pos': (1883.87, 23.36, 3125.24, -3.11, ),
            'team_first_birth': 2,
        }, 
        13: {
            'id': '80.13',
            'space_id': 80,
            'local_id': 13,
            'pos': (1871.35, 23.62, 3114.01, 2.64, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '80.14',
            'space_id': 80,
            'local_id': 14,
            'pos': (1895.49, 22.13, 3088.24, -2.07, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '80.15',
            'space_id': 80,
            'local_id': 15,
            'pos': (1902.53, 22.13, 3078.84, -1.76, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '80.16',
            'space_id': 80,
            'local_id': 16,
            'pos': (1904.26, 22.13, 3098.81, -2.41, ),
            'team_first_birth': 0,
        }, 
    }, 
    81: {
        1: {
            'id': '81.1',
            'space_id': 81,
            'local_id': 1,
            'pos': (1829.1, 22.13, 3078.37, 1.04, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '81.2',
            'space_id': 81,
            'local_id': 2,
            'pos': (1838.99, 22.15, 3080.01, 1.23, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '81.3',
            'space_id': 81,
            'local_id': 3,
            'pos': (1837.8, 23.11, 3061.81, 0.89, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '81.4',
            'space_id': 81,
            'local_id': 4,
            'pos': (1841.3, 23.11, 3052.13, 0.95, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '81.5',
            'space_id': 81,
            'local_id': 5,
            'pos': (1845.6, 26.81, 3040.75, 0.67, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '81.6',
            'space_id': 81,
            'local_id': 6,
            'pos': (1853.24, 26.77, 3039.47, 0.72, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '81.7',
            'space_id': 81,
            'local_id': 7,
            'pos': (1855.03, 22.13, 3018.23, 0.29, ),
            'team_first_birth': 1,
        }, 
        8: {
            'id': '81.8',
            'space_id': 81,
            'local_id': 8,
            'pos': (1871.35, 22.13, 3026.23, 0.15, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '81.9',
            'space_id': 81,
            'local_id': 9,
            'pos': (1920.22, 22.13, 3081.3, -1.8, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '81.10',
            'space_id': 81,
            'local_id': 10,
            'pos': (1914.47, 22.13, 3090.07, -1.72, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '81.11',
            'space_id': 81,
            'local_id': 11,
            'pos': (1905.57, 22.13, 3107.44, -2.17, ),
            'team_first_birth': 2,
        }, 
        12: {
            'id': '81.12',
            'space_id': 81,
            'local_id': 12,
            'pos': (1883.87, 23.36, 3125.24, -3.11, ),
            'team_first_birth': 2,
        }, 
        13: {
            'id': '81.13',
            'space_id': 81,
            'local_id': 13,
            'pos': (1871.35, 23.62, 3114.01, 2.64, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '81.14',
            'space_id': 81,
            'local_id': 14,
            'pos': (1895.49, 22.13, 3088.24, -2.07, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '81.15',
            'space_id': 81,
            'local_id': 15,
            'pos': (1902.53, 22.13, 3078.84, -1.76, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '81.16',
            'space_id': 81,
            'local_id': 16,
            'pos': (1904.26, 22.13, 3098.81, -2.41, ),
            'team_first_birth': 0,
        }, 
    }, 
    82: {
        1: {
            'id': '82.1',
            'space_id': 82,
            'local_id': 1,
            'pos': (1829.1, 22.13, 3078.37, 1.04, ),
            'team_first_birth': 1,
        }, 
        2: {
            'id': '82.2',
            'space_id': 82,
            'local_id': 2,
            'pos': (1838.99, 22.15, 3080.01, 1.23, ),
            'team_first_birth': 1,
        }, 
        3: {
            'id': '82.3',
            'space_id': 82,
            'local_id': 3,
            'pos': (1837.8, 23.11, 3061.81, 0.89, ),
            'team_first_birth': 1,
        }, 
        4: {
            'id': '82.4',
            'space_id': 82,
            'local_id': 4,
            'pos': (1841.3, 23.11, 3052.13, 0.95, ),
            'team_first_birth': 1,
        }, 
        5: {
            'id': '82.5',
            'space_id': 82,
            'local_id': 5,
            'pos': (1845.6, 26.81, 3040.75, 0.67, ),
            'team_first_birth': 1,
        }, 
        6: {
            'id': '82.6',
            'space_id': 82,
            'local_id': 6,
            'pos': (1853.24, 26.77, 3039.47, 0.72, ),
            'team_first_birth': 1,
        }, 
        7: {
            'id': '82.7',
            'space_id': 82,
            'local_id': 7,
            'pos': (1855.03, 22.13, 3018.23, 0.29, ),
            'team_first_birth': 1,
        }, 
        8: {
            'id': '82.8',
            'space_id': 82,
            'local_id': 8,
            'pos': (1871.35, 22.13, 3026.23, 0.15, ),
            'team_first_birth': 2,
        }, 
        9: {
            'id': '82.9',
            'space_id': 82,
            'local_id': 9,
            'pos': (1920.22, 22.13, 3081.3, -1.8, ),
            'team_first_birth': 2,
        }, 
        10: {
            'id': '82.10',
            'space_id': 82,
            'local_id': 10,
            'pos': (1914.47, 22.13, 3090.07, -1.72, ),
            'team_first_birth': 2,
        }, 
        11: {
            'id': '82.11',
            'space_id': 82,
            'local_id': 11,
            'pos': (1905.57, 22.13, 3107.44, -2.17, ),
            'team_first_birth': 2,
        }, 
        12: {
            'id': '82.12',
            'space_id': 82,
            'local_id': 12,
            'pos': (1883.87, 23.36, 3125.24, -3.11, ),
            'team_first_birth': 2,
        }, 
        13: {
            'id': '82.13',
            'space_id': 82,
            'local_id': 13,
            'pos': (1871.35, 23.62, 3114.01, 2.64, ),
            'team_first_birth': 0,
        }, 
        14: {
            'id': '82.14',
            'space_id': 82,
            'local_id': 14,
            'pos': (1895.49, 22.13, 3088.24, -2.07, ),
            'team_first_birth': 0,
        }, 
        15: {
            'id': '82.15',
            'space_id': 82,
            'local_id': 15,
            'pos': (1902.53, 22.13, 3078.84, -1.76, ),
            'team_first_birth': 0,
        }, 
        16: {
            'id': '82.16',
            'space_id': 82,
            'local_id': 16,
            'pos': (1904.26, 22.13, 3098.81, -2.41, ),
            'team_first_birth': 0,
        }, 
    }, 
}
