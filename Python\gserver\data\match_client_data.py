# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 5-场景地图表.xlsx, sheetname:玩法数据表（客户端模式选择用）
_reload_all = True

data = {
    28: {
        'id': 28,
        'real_match': 28,
        'show_coin': True,
        'space': (62, ),
        'unlock_sys_id': 7,
        'show': True,
        'match_mode': (1, ),
        'leisure_sort_key': 7,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }, 
    29: {
        'id': 29,
        'skip': 'trunk_only',
        'real_match': 29,
        'show_coin': True,
        'space': (2, 36, 62, 63, 64, 67, 68, 69, 42, 47, 55, 73, ),
        'show': True,
        'match_mode': (1, ),
        'leisure_sort_key': 8,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }, 
    31: {
        'id': 31,
        'real_match': 31,
        'show_coin': True,
        'space': (40, 46, 56, 32, 41, 53, 54, 51, 60, 61, 65, 69, 70, 76, 77, 78, 79, 80, 81, 82, ),
        'show': True,
        'match_mode': (1, ),
        'leisure_sort_key': 9,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }, 
    32: {
        'id': 32,
        'skip': 'trunk_only',
        'real_match': 32,
        'show_coin': True,
        'space': (2, 41, 56, 51, 61, 65, 67, 69, ),
        'show': True,
        'match_mode': (1, 2, 4, ),
        'leisure_sort_key': 10,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }, 
    33: {
        'id': 33,
        'skip': 'trunk_only',
        'real_match': 33,
        'show_coin': True,
        'space': (2, 41, 56, 51, 61, 65, 67, ),
        'show': True,
        'match_mode': (1, 2, 4, ),
        'leisure_sort_key': 12,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }, 
    1004: {
        'id': 1004,
        'real_match': 1004,
        'show_coin': True,
        'space': (61, 65, ),
        'unlock_sys_id': 7,
        'show': True,
        'match_mode': (1, 4, ),
        'leisure_sort_key': 1,
        'show_panel_map': True,
        'match_icon_size': 0,
        'match_icon_big': 100001,
        'match_icon_small': 100001,
        'show_rule_info': True,
        'rule_info_ui': 'UIScript/ig_main_guide.csb',
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    1006: {
        'id': 1006,
        'real_match': 1006,
        'show_coin': True,
        'space': (61, 65, ),
        'unlock_sys_id': 8,
        'show': True,
        'match_mode': (1, 4, ),
        'leisure_sort_key': 5,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100004,
        'match_icon_small': 100004,
        'show_rule_info': True,
        'rule_info_ui': 'UIScript/ig_main_guide.csb',
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    1100: {
        'id': 1100,
        'real_match': 1100,
        'show_coin': True,
        'space': (32, 41, 53, 54, 77, 78, 79, 80, 81, 82, ),
        'unlock_sys_id': 11,
        'show': True,
        'match_mode': (6, ),
        'force_team_match': True,
        'leisure_sort_key': 4,
        'show_panel_map': True,
        'match_icon_size': 0,
        'match_icon_big': 100003,
        'match_icon_small': 100003,
    }, 
    1200: {
        'id': 1200,
        'real_match': 2000,
        'show_coin': True,
        'space': (32, 41, 53, 54, ),
        'unlock_sys_id': 10,
        'show': True,
        'match_mode': (4, ),
        'leisure_sort_key': 6,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100005,
        'match_icon_small': 100005,
    }, 
    1202: {
        'id': 1202,
        'real_match': 1004,
        'show_coin': True,
        'space': (32, 41, 53, 54, ),
        'unlock_sys_id': 9,
        'show': True,
        'match_mode': (4, ),
        'leisure_sort_key': 2,
        'show_panel_map': True,
        'match_icon_size': 0,
        'match_icon_big': 100002,
        'match_icon_small': 100002,
        'show_rule_info': True,
        'rule_info_ui': 'UIScript/ig_main_guide.csb',
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    1600: {
        'id': 1600,
        'real_match': 1600,
        'show_coin': True,
        'space': (62, ),
        'show': False,
        'match_mode': (1, ),
        'leisure_sort_key': 11,
        'show_panel_map': True,
        'match_icon_size': 1,
        'match_icon_big': 100006,
        'match_icon_small': 100006,
    }, 
    2000: {
        'id': 2000,
        'real_match': 2000,
        'show_coin': True,
        'space': (32, 41, 53, 54, ),
        'unlock_sys_id': 11,
        'show': True,
        'match_mode': (6, ),
        'force_team_match': True,
        'leisure_sort_key': 3,
        'show_panel_map': True,
        'match_icon_size': 0,
        'match_icon_big': 100003,
        'match_icon_small': 100003,
    }, 
}
