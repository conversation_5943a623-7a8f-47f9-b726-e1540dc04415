# -*- coding: utf-8 -*-
# generated by: excel_to_data.py
# generated from 5-场景地图表.xlsx, sheetname:玩法数据表
_reload_all = True

data = {
    1: {
        'id': 1,
        'skip': 'trunk_only',
        'space': (1, ),
        'game_logic': 'Nowhere',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'mode_type': 2,
        'max_count': 10,
        'max_team_count': 10,
    }, 
    2: {
        'id': 2,
        'skip': 'trunk_only',
        'space': (32, ),
        'game_logic': 'Nowhere',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'mode_type': 2,
        'max_count': 10,
        'max_team_count': 10,
    }, 
    15: {
        'id': 15,
        'space': (51, ),
        'game_logic': 'Placement',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'max_count': 2,
        'max_team_count': 2,
    }, 
    28: {
        'id': 28,
        'space': (62, ),
        'game_logic': 'ShootingRange',
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, ),
        'match_algo': {
            'name': 'MatchQueueShootingRange', 
        },
        'leisure_sort_key': 12,
        'max_count': 1,
        'max_team_count': 1,
        'match_icon_big': 10687,
        'match_icon_small': 10580,
    }, 
    29: {
        'id': 29,
        'skip': 'trunk_only',
        'space': (36, 62, 63, 64, 67, 68, 69, 42, 47, 55, 2, 73, ),
        'game_logic': 'ShootingRange',
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 2, ),
        'match_algo': {
            'name': 'MatchQueueShootingRange', 
        },
        'leisure_sort_key': 6,
        'max_count': 1,
        'max_team_count': 1,
        'match_icon_big': 10687,
        'match_icon_small': 10580,
    }, 
    31: {
        'id': 31,
        'space': (40, 46, 56, 32, 41, 53, 54, 51, 60, 61, 65, 2, 69, 70, 76, 77, 78, 79, 80, 81, 82, ),
        'game_logic': 'ShootingRange',
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 2, ),
        'match_algo': {
            'name': 'MatchQueueShootingRange', 
        },
        'leisure_sort_key': 11,
        'max_count': 1,
        'max_team_count': 1,
        'match_icon_big': 10687,
        'match_icon_small': 10580,
    }, 
    32: {
        'id': 32,
        'skip': 'trunk_only',
        'space': (2, 41, 56, 51, 61, 65, 67, 69, ),
        'game_logic': 'MobaShootingRange',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'mode_type': 2,
        'max_count': 10,
        'max_team_count': 10,
    }, 
    33: {
        'id': 33,
        'skip': 'trunk_only',
        'space': (2, 41, 56, 51, 61, 65, 67, ),
        'game_logic': 'MobaShootingRange',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 3, 4, ),
        'mode_type': 2,
        'max_count': 1,
        'max_team_count': 1,
    }, 
    1002: {
        'id': 1002,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 1,
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': {
            'name': 'MatchQueueMobaNewer', 
        },
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1004,
        'show_panel_map': True,
        'max_count': 1,
        'max_team_count': 1,
        'hall_waiting_time': 1,
        'match_icon_big': 10685,
        'match_icon_small': 10579,
        'show_rule_info': True,
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    1003: {
        'id': 1003,
        'space': (2, 61, 65, ),
        'game_logic': 'MobaFirstNew',
        'type': 1,
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, ),
        'punish': False,
        'match_algo': {
            'name': 'MatchQueueMobaFirstNew', 
        },
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1006,
        'show_panel_map': True,
        'max_count': 1,
        'max_team_count': 1,
        'hall_waiting_time': 1,
        'match_icon_big': 10685,
        'match_icon_small': 10579,
        'show_rule_info': True,
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    1004: {
        'id': 1004,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 1,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': {
            'name': 'MatchQueueBattleRoyale', 
        },
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'show_panel_map': True,
        'chat_share_icon': 40000,
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
        'match_icon_big': 11475,
        'match_icon_small': 11478,
        'show_rule_info': True,
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    1005: {
        'id': 1005,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 1,
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': {
            'name': 'MatchQueueMobaWarm', 
        },
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1004,
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
    }, 
    1006: {
        'id': 1006,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': {
            'name': 'MatchQueueMobaWarm', 
        },
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1006,
        'show_panel_map': True,
        'chat_share_icon': 40000,
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
        'match_icon_big': 11475,
        'match_icon_small': 11478,
        'show_rule_info': True,
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    1007: {
        'id': 1007,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': {
            'name': 'MatchQueueMobaWarm', 
        },
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1006,
        'show_panel_map': True,
        'chat_share_icon': 40000,
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
        'match_icon_big': 11475,
        'match_icon_small': 11478,
        'show_rule_info': True,
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    1008: {
        'id': 1008,
        'space': (61, 65, ),
        'game_logic': 'Moba',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 3, 4, ),
        'punish': True,
        'match_algo': {
            'name': 'MatchQueueMobaWarm', 
        },
        'chat_channel_list': (1, 2, 4, ),
        'mode_type': 1,
        'match_type': 1006,
        'show_panel_map': True,
        'chat_share_icon': 40000,
        'max_count': 40,
        'max_team_count': 40,
        'hall_waiting_time': 30,
        'match_icon_big': 11475,
        'match_icon_small': 11478,
        'show_rule_info': True,
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    1100: {
        'id': 1100,
        'space': (32, 41, 53, 54, 77, 78, 79, 80, 81, 82, ),
        'game_logic': 'HotSpot',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 2, 6, ),
        'match_algo': {
            'name': 'MatchQueueHotSpot', 
        },
        'chat_channel_list': (1, 2, ),
        'mode_type': 1,
        'match_type': 1100,
        'leisure_sort_key': 10,
        'show_panel_map': True,
        'max_count': 12,
        'max_team_count': 2,
        'hall_waiting_time': 10,
        'match_icon_big': 10705,
        'match_icon_small': 10707,
        'rule_info_one_str': '守住热点以获取积分。首个达到积分目标的队伍获胜。',
    }, 
    1101: {
        'id': 1101,
        'space': (32, 41, 53, 54, 77, 78, 79, 80, ),
        'game_logic': 'HotSpot',
        'type': 2,
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, 2, 6, ),
        'match_algo': {
            'name': 'MatchQueueHotSpotNewer', 
        },
        'chat_channel_list': (1, 2, ),
        'mode_type': 1,
        'match_type': 1100,
        'leisure_sort_key': 10,
        'show_panel_map': True,
        'max_count': 10,
        'max_team_count': 2,
        'hall_waiting_time': 10,
        'match_icon_big': 10705,
        'match_icon_small': 10707,
        'rule_info_one_str': '守住热点以获取积分。首个达到积分目标的队伍获胜。',
    }, 
    1600: {
        'id': 1600,
        'skip': 'trunk_only',
        'space': (62, ),
        'game_logic': 'RookieGuideNew',
        'hide_exit_button': False,
        'show': False,
        'match_mode': (1, ),
        'match_type': 1600,
        'max_count': 1,
        'max_team_count': 1,
        'rule_info_icon': {
            'img_1': 10701, 
            'img_2': 10702, 
            'img_3': 10703, 
        },
        'rule_info_str': {
            'txt_t1': '作战装备', 
            'txt_t2': '战利品', 
            'txt_t3': '重新部署', 
            'txt_1': '在战场中收集金币，呼叫空投获取作战装备', 
            'txt_2': '部署进入战场后，收集武器、子弹等各种物资 ', 
            'txt_3': '在倒计时结束前只要有队友存活，即可返回战场', 
        },
    }, 
    2000: {
        'id': 2000,
        'space': (32, 41, 53, 54, ),
        'game_logic': 'CampFight',
        'type': 2,
        'hide_exit_button': False,
        'show': True,
        'match_mode': (1, 2, 6, ),
        'match_algo': {
            'name': 'MatchQueueCampFight', 
        },
        'chat_channel_list': (1, 2, ),
        'mode_type': 1,
        'match_type': 2000,
        'leisure_sort_key': 9,
        'show_panel_map': True,
        'max_count': 12,
        'max_team_count': 2,
        'hall_waiting_time': 10,
        'match_icon_big': 10705,
        'match_icon_small': 10707,
        'rule_info_one_str': '击败敌人获取积分。首个达到积分目标的队伍获胜。',
    }, 
}
