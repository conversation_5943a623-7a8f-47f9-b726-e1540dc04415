# -*- coding: utf-8 -*-
import random

from common.RpcMethodArgs import Int, Str, Delegate, Dict, EntityID, Bool, List, Tuple  # noqa
from common.rpcdecorator import CLIENT_ONLY, SERVER_ONLY, rpc_method  # noqa
from common.classutils import Property, ComponentWithProperty

from gshare.consts import NormalBackpackSlot, MobaBackpackSlot, BackpackSlot, PrototypeId  # noqa
from gshare.consts import AvatarEvent, CombatAvatarEvent
from gserver.data import hero_attributes_data, human_type_prototype_data, hero_data, combat_item_data, squad_shop_item_data, spell_data  # noqa
from gshare.game_logic.igame_logic_base import GameState
from gshare import consts
from gshare import pubsub
from gshare.pyany import PyAny
from gshare.ispell_mgr import SPELL_TICK_INTERVAL


class CombatAvatarMember(ComponentWithProperty):
    Property('select_hero_id', 0, Property.ALL_CLIENTS)     # 等待状态选英雄
    Property("samurai_energy", 0.0, Property.ALL_CLIENTS)  # 武士能量

    def __init_component__(self, bdict):
        # 本局可用的试用英雄以进场景的瞬间决定
        self.weekly_free_hero_ids = GameProc.free_hero_list

    def __after_enter_space_component__(self):
        # 给机器人设置默认衣服
        if self.IsRobotCombatAvatar:
            hero_item_id = hero_data.data[self.hero_id].get('hero_item_id')
            if hero_item_id:
                self.arm_clothes[consts.ItemSubType.Suit] = hero_item_id

        if not self.IsRobotCombatAvatar and self.game_logic.game_state != GameState.WAITING:
            self.add_timer(0.1, lambda: self.RandomSelectHero("robot"))

        self.InitSamuraiEnergyParameter()

    def __after_leave_space_component__(self):
        samurai_energy_timer = getattr(self, 'samurai_energy_timer', None)
        if samurai_energy_timer:
            self.cancel_timer(samurai_energy_timer)

    def InitSamuraiEnergyParameter(self):
        # TODO: 改配表
        if self.select_hero_id and self.select_hero_id == 128:
            spell_id = 310
            self.samurai_energy = 0.0
            samurai_data = spell_data.data.get(spell_id).get(1, {}).get('samurai', {})
            self.samurai_energy_max = samurai_data.get("energy_max", 200.0)
            if 'ShootingRange' in self.space.game_logic_code:
                self.samurai_energy = self.samurai_energy_max
            self.samurai_recover_energy_per_dam = samurai_data.get("recover_energy_per_dam", 1.0)
            self.samurai_recover_energy_per_sec = samurai_data.get("recover_energy_per_sec", 1.0)
            samurai_energy_timer = getattr(self, 'samurai_energy_timer', None)
            if samurai_energy_timer:
                self.cancel_timer(samurai_energy_timer)
            self.samurai_energy_timer = self.add_repeat_timer(0.1, self.UpdateSamuraiEnergy)  # 每0.1秒调用一次
        else:
            samurai_energy_timer = getattr(self, 'samurai_energy_timer', None)
            if samurai_energy_timer:
                self.cancel_timer(samurai_energy_timer)

    @pubsub.Subscribe(CombatAvatarEvent.COMBAT_MAKE_DAMAGE)
    def UpdateOwnerSamuraiEnergyWhenMakeDamage(self, ev):
        if self.select_hero_id and self.select_hero_id == 128:
            # 造成得伤害加能量
            caster = ev.caster
            if caster is not self:
                return
            if not hasattr(self, 'samurai_recover_energy_per_dam'):
                self.InitSamuraiEnergyParameter()
            samurai_energy = self.samurai_energy + int(ev.damage) * self.samurai_recover_energy_per_dam
            self.samurai_energy = max(0.0, min(self.samurai_energy_max, samurai_energy))

    def UpdateSamuraiEnergy(self):
        """更新英雄能量"""
        if not self.is_alive:
            return
        self.samurai_energy = min(self.samurai_energy_max, self.samurai_energy + self.samurai_recover_energy_per_sec * SPELL_TICK_INTERVAL)

    @property
    def can_use_same_hero(self):
        # 是否能用和队友一样的英雄
        # 英雄数量不足的时候 返回True 可以复用英雄
        return len(self.game_logic.player_hero_ids) < self.space.match_mode

    def CanChooseHeroInGame(self, hero_id):
        # 验证玩家能否选英雄。注意游戏已经开始玩家才进游戏的情况
        if "hero_hall_show_anim" not in hero_data.data.get(hero_id, {}):
            return False

        if "is_shootingrange_test" in hero_data.data.get(hero_id, {}):
            return False

        if not self.can_use_same_hero:
            for member_id, member_info in self.combat_team.member_dict.items():
                if member_id == self.id:
                    continue
                if member_info.select_hero_id == hero_id:
                    return False

        master = self.master
        if not master or master.is_destroyed():
            return True

        if master.IsUnlockHero(hero_id):
            return True

        if hero_id in self.weekly_free_hero_ids:
            return True

        return False

    @rpc_method(CLIENT_ONLY, Int())
    def UpdatePreSelectInfo(self, hero_id):
        # 没点确定
        if 'hero_hall_show_anim' not in hero_data.data.get(hero_id, {}):
            return
        self.combat_team.member_dict[self.id].pre_select_hero_id = hero_id

    @rpc_method(CLIENT_ONLY, Int())
    def SelectHero(self, hero_id):
        # 点确定
        if self.space.game_logic.game_state != GameState.WAITING:
            return
        if not self.CanChooseHeroInGame(hero_id):
            return
        self.SelectHeroImpl(hero_id, log_msg="client_rpc")

    def SelectHeroImpl(self, hero_id, log_msg='auto'):
        if self.select_hero_id == hero_id:  # 重复选相同的英雄
            return
        self_info = self.combat_team.member_dict[self.id]
        self_info.select_hero_id = self_info.hero_id = hero_id

        list(map(self.RemoveItemBySlot, (BackpackSlot.SKILL_WEAPON, BackpackSlot.SKILL_WEAPON2, BackpackSlot.CARRIABLE_SLOT)))
        self.hero_id = hero_id
        self.select_hero_id = hero_id
        for skill_id in self_info.select_hero_skill:
            self.AddItemByItemID(skill_id, combat_item_data.data.get(skill_id, {}).get('bag_max_stack_limit', 1))

        # 加一下可投掷物的槽位
        self.AddItemByItemID(consts.CARRIABLE_SKILL_COMBAT_ITEM_ID, 1)

        arm_clothes = self.GetHeroArmClothes(hero_id)
        if arm_clothes:
            self.arm_clothes = arm_clothes
            self.game_logic.SyncTeammateInfo(self.combat_team, {self.id: {'arm_clothes': arm_clothes}})
        self.Publish(CombatAvatarEvent.CHANGE_HERO, PyAny(hero_id=hero_id))

        master = self.master
        master and self.CanChooseHeroInGame(hero_id) and master.Publish(CombatAvatarEvent.CHANGE_HERO, PyAny(hero_id=hero_id))

        self.logger.info("SelectHero hero_id:%s, log_msg:%s", self.hero_id, log_msg)
        self.SetScoreBoardHeroId()

        suit_id = self.arm_clothes.get(consts.ItemSubType.Suit)
        if suit_id and suit_id > 0:
            self.space.game_logic.player_arm_clothes[self.id] = suit_id

        if master:
            execute_item_id = master.GetExecuteItemId(hero_id)
            if execute_item_id:
                self.execute_item_id = execute_item_id
        self.DestroySkillEntity()
        self.InitSamuraiEnergyParameter()

    @rpc_method(CLIENT_ONLY, Int())
    def UnSelectHero(self):
        # 切换角色
        if self.space.game_logic.game_state != GameState.WAITING:
            return

        self.UnSelectHeroImpl()

    def UnSelectHeroImpl(self):
        if not self.select_hero_id:
            return

        self.select_hero_id = 0
        self.hero_id = 0
        self_info = self.combat_team.member_dict[self.id]
        self_info.select_hero_id = self_info.hero_id = 0

    def GetHeroArmClothes(self, hero_id):
        hero_item_id = hero_data.data.get(hero_id, {}).get('hero_item_id')
        master = self.master
        if not master:
            if hero_item_id:
                return {consts.ItemSubType.Suit: hero_item_id}
        else:
            hero_info = master.hero_infos.get(hero_id)
            if not hero_info:
                return

            show_arm_clothes = hero_info.show_arm_clothes
            if not show_arm_clothes:
                return

            return show_arm_clothes

    @pubsub.Subscribe(AvatarEvent.ENTER_BATTLE)
    def EnterBattleSelectHeroTimeOut(self, ev):
        self.RandomSelectHero("timeout (ev enter_battle)")

    def SelectHeroTimeOut(self):
        self.RandomSelectHero("timeout")

    def SelectHeroForRobot(self):
        self.RandomSelectHero('robot')

    def RandomSelectHero(self, log_msg):
        # 限定时间内没有选择英雄，则用当前的pre_select_info，否则随机选一个
        if len(self.combat_team.member_dict) > self.space.match_mode:
            genv.LogAlert("LogicError", msg="team_count(%s) > match_mode(%s), avatar=%s" % (len(self.combat_team.member_dict), self.space.match_mode, self))

        if self.combat_team.member_dict[self.id].select_hero_id:  # 已经选过
            return

        already_select = []
        candidates = []

        if self.master:
            has_hero = self.CanChooseHeroInGame
        else:   # ai
            has_hero = lambda _: True

        if not self.can_use_same_hero:
            for member_id, member_info in self.combat_team.member_dict.items():
                if member_info.select_hero_id:
                    already_select.append(member_info.select_hero_id)

        allow_hero = self.game_logic.robot_hero_ids if self.IsRobotCombatAvatar else self.game_logic.player_hero_ids
        for hero_id, hero_proto in hero_data.data.items():
            if hero_id not in allow_hero:
                continue
            if hero_id not in already_select and hero_proto.get('hero_hall_show_anim') and has_hero(hero_id):
                candidates.append(hero_id)

        pre_hero_id = self.combat_team.member_dict[self.id].pre_select_hero_id
        if pre_hero_id in candidates:
            hero_id = pre_hero_id
        else:
            hero_id = random.choice(candidates)
        self.SelectHeroImpl(hero_id, log_msg)

        # 同步下随机选择的结果
        self.game_logic.SyncTeammateInfo(self.combat_team, {self.id: {'select_hero_id': hero_id}})

    def SetScoreBoardHeroId(self):
        score_board = getattr(self.game_logic, "score_board", None)
        if not score_board:
            return
        unit = score_board.get(self.id)
        if not unit:
            return
        unit.hero_id = self.hero_id
