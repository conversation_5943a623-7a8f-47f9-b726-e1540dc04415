# -*- coding: utf-8 -*-

import math
import random
import functools
from .base import gmcmd, Int, Str, Bool, Float, EntityID
from common.classutils import CustomListType
from common.IdManager import IdManager
from gshare import formula
from gshare.consts import GameLogicComp<PERSON>ind
from gshare import consts
from gshare import irobot
from gshare.async_util import Async

from gserver.data import hero_data
from gserver.data import equip_data


@gmcmd("robot", Str("机器人名字"))
def create_robot(avatar, name):
    robot = create_robot_template(avatar, name)
    genv.robot = robot
    return robot.id


@gmcmd("helen_robot", Str("机器人名字"))
def create_helen_robot(avatar, name):
    robot = create_helen_robot_template(avatar, name)
    robot.RefreshBackpackFromHallBackpack()
    genv.robot = robot
    return robot.id


@gmcmd("helen_robot_active", Str("机器人名字"))
def create_helen_robot_active(avatar, name):
    # 创建一个活得helen机器人
    robot = create_helen_robot_template(avatar, name)
    robot.RefreshBackpackFromHallBackpack()
    genv.robot = robot
    robot.helen_ai.start_ai_system()
    robot.helen_ai.ai_service_start()
    robot.helen_ai.start_ai_threaten_logic()
    robot.helen_ai.report_activate_to_squad()
    return robot.id


@gmcmd("team_robot")
def team_robot(avatar):
    team_guid = IdManager.genid()
    for _ in range(avatar.space.match_mode):
        name = irobot.RandomName()
        robot = create_robot_template(avatar, name, {"ai_team_guid": team_guid})
        genv.robot = robot


@gmcmd("team_helen_robot")
def team_helen_robot(avatar):
    team_guid = IdManager.genid()
    for _ in range(avatar.space.match_mode):
        name = irobot.RandomName()
        robot = create_helen_robot_template(avatar, name, {"ai_team_guid": team_guid})
        robot.RefreshBackpackFromHallBackpack()
        genv.robot = robot


@gmcmd("member_robot")
def member_robot(avatar):
    name = irobot.RandomName()
    create_robot_template(avatar, name, {"ai_team_guid": avatar.combat_avatar.combat_team.id})


@gmcmd("bind_robot", Str("机器人名字"))
def create_bind_robot(avatar, name):
    combat_avatar = avatar.combat_avatar
    comp_robot = combat_avatar.game_logic.GetComp(GameLogicCompKind.ROBOT)
    comp_robot.GenBindRobotTeam(
        comp_robot.GenBornAreaForAvatar(combat_avatar, (100, 200, 500)), 1, binder=combat_avatar)


@gmcmd("debug_robot", Str("机器人名字"))
def create_debug_robot(avatar, name):
    robot = create_robot_template(avatar, name, extra={"brain_class_name": "DebugBrain"})
    genv.robot = robot
    return robot.id


@gmcmd("moving_target_robot", Str("机器人名"), Float("位置x"), Float("位置y"), Float("位置z"), Float("朝向yaw"))
def create_moving_target_robot(avatar, name, x, y, z, direction):
    robot = create_robot_template(avatar, name, extra={"brain_class_name": "MovingTargetBrain",
                                                       'ai_target_position': (x, y, z),
                                                       'direction': direction})
    robot.ai_control_inner_radius = 250
    robot.ai_control_outer_radius = 300


@gmcmd("warm_bind_robot", Str("机器人名字"), Str("brain class name"))
def create_warm_bind_robot(avatar, name, brain_class_name):
    create_robot_template(avatar, name, {'ai_bind_target_guid': avatar.combat_avatar.id,
                                         'brain_class_name': brain_class_name})


@gmcmd("robot_add_item", Str("机器人名字"), Int("物品ID"), Int("物品数量"))
def robot_add_item(avatar, name, item_id, item_count):
    robots = find_robot_by_name(avatar, name)
    for robot in robots.values():
        robot.AddItemByItemID(item_id, item_count)


@gmcmd("robot_change_weapon", Str("机器人名字"), Int("物品ID"))
def robot_change_weapon(avatar, name, item_id):
    robots = find_robot_by_name(avatar, name)
    for robot in robots.values():
        for item in robot.backpack.values():
            if item['item_id'] == item_id:
                robot.ChangeWeapon(item['guid'], True)


@gmcmd("robot_throw_bomb", Str("机器人名字"), Int("物品ID"))
def robot_throw_bomb(avatar, name, item_id):
    robots = find_robot_by_name(avatar, name)
    for robot in robots.values():
        for item in robot.backpack.values():
            if item['item_id'] == item_id:
                robot.CallClient('TryAttackFromServer', robot.id, item.spell_id, 1, item['guid'])
                break


@gmcmd('para_robot', Float('目标位置x'), Float('目标位置y'), Float('目标位置z'))
def create_para_robot(avatar, x, y, z):
    space = avatar.space
    data = {
        "match_type": space.match_type,
        "name": irobot.RandomName(),
        "masterid": "",
        "hand_entity_id": "",
        "main_client_id": avatar.id,
    }

    game_logic = space.game_logic
    position = formula.Add3D(avatar.combat_avatar.position, (100, 400, 0))
    robot = space.create_entity(
        entitytype="Robot" + game_logic.COMBAT_AVATAR_TYPE,
        entityid=None,
        entitycontent=data,
        position=position,
        direction=avatar.combat_avatar.yaw,
    )
    robot.RefreshBackpackFromAvatar(avatar, True)
    space.CombatAvatarEnter(robot)
    robot.ai_target_position = CustomListType((x, y, z))
    robot.para_reason = 1
    robot.SetParaStage(2)
    robot.OnAIControlTick()


def create_robot_template(avatar, name, extra=None):
    space = avatar.space
    data = {
        "match_type": space.match_type,
        "name": name,
        "brain_class_name": random.choice(["RecklessBrain", ]),
        # "brain_class_name": "CarelessBrain",
        "masterid": "",
        "hand_entity_id": "",
        "main_client_id": avatar.id,
    }
    extra and data.update(extra)
    yaw = extra['direction'] if extra and 'direction' in extra else avatar.combat_avatar.yaw

    game_logic = space.game_logic
    offset = formula.YawToVector(avatar.combat_avatar.yaw)
    offset = formula.Mul3D(offset, 2.0)
    position = formula.Add3D(avatar.combat_avatar.position, offset)
    robot = space.create_entity(
        entitytype="Robot" + game_logic.COMBAT_AVATAR_TYPE,
        entityid=None,
        entitycontent=data,
        position=position,
        direction=yaw,
    )
    robot.RefreshBackpackFromAvatar(avatar, True)
    space.CombatAvatarEnter(robot)

    # game_logic.OnAvatarEnter(robot)

    # 重刷一下位置
    def ResetRobotPosition(robot):
        robot.SetTransform(position, avatar.combat_avatar.yaw)
        # robot.Flush()

    robot.add_timer(0.1, functools.partial(ResetRobotPosition, robot))
    return robot


def create_helen_robot_template(avatar, name, extra=None):
    space = avatar.space
    data = {
        "match_type": space.match_type,
        "name": name,
        "brain_class_name": random.choice(["RecklessBrain", ]),
        # "brain_class_name": "CarelessBrain",
        "masterid": "",
        "hand_entity_id": "",
        "main_client_id": avatar.id,
        "start_ai": False,
        "hero_id": avatar.hero_id,
        "auto_f12": True
    }
    extra and data.update(extra)
    yaw = extra['direction'] if extra and 'direction' in extra else avatar.combat_avatar.yaw

    game_logic = space.game_logic
    offset = formula.YawToVector(avatar.combat_avatar.yaw)
    offset = formula.Mul3D(offset, 2.0)
    position = formula.Add3D(avatar.combat_avatar.position, offset)
    robot = space.create_entity(
        entitytype="HelenRobot" + game_logic.COMBAT_AVATAR_TYPE,
        entityid=None,
        entitycontent=data,
        position=position,
        direction=yaw,
    )
    robot.RefreshBackpackFromAvatar(avatar, True)
    space.CombatAvatarEnter(robot)
    robot.AddItemByItemID(43, 1)
    robot.SelectHeroImpl(avatar.hero_id)
    # game_logic.OnAvatarEnter(robot)

    # 重刷一下位置
    def ResetRobotPosition(robot):
        robot.SetTransform(position, avatar.combat_avatar.yaw)
        # robot.Flush()

    robot.add_timer(0.1, functools.partial(ResetRobotPosition, robot))
    return robot


def find_robot_by_name(avatar, name):
    space = avatar.space
    robots = list(space.entity_category["RobotCombatAvatar" + space.game_logic.suffix].values())
    results = {}
    for robot in robots:
        if name == "all" or robot.name == "Robot-" + name:
            results[robot.id] = robot
    return results


@gmcmd("remove_robot", Str("机器人名字"))
def remove_robot(avatar, name):
    for robot in find_robot_by_name(avatar, name).values():
        if name == "all" or robot.name == "Robot-" + name:
            robot.space.CombatAvatarLeave(robot, 0)


@gmcmd("robot_move", Str("机器人名字"), Int("第几个坐标"))
def robot_move(avatar, name, index):
    positions = (
        (64.00342559814453, 4.497017860412598, -53.978511810302734),   # 房子二楼
        (9.787973403930664, -0.05939478054642677, -17.022781372070312),  # 出生点
        (35.859283447265625, -0.10022028535604477, -18.4007511138916),   # 出生点左边箱子的左边
        (30.907644271850586, 2.9585225582122803, -45.357208251953125),   # 不可达的箱子上
        (13.029951095581055, -0.05939458683133125, -18.59941291809082),
        (-19.61366844177246, -0.10022006183862686, -46.799156188964844),
    )
    for robot in find_robot_by_name(avatar, name).values():
        robot.ai_memory.target_pos = CustomListType(positions[index])


@gmcmd("control_robot", Str("机器人名字"))
def control_robot(avatar, name):
    for robot in find_robot_by_name(avatar, name).values():
        robot.ChangeControlState(avatar.id)


@gmcmd("uncontrol_robot", Str("机器人名字"))
def uncontrol_robot(avatar, name):
    for robot in find_robot_by_name(avatar, name).values():
        robot.ChangeControlState("")


@gmcmd("brush_robots", Int("队伍数量"), Int("单个队伍人数"))
def brush_robots(avatar, teams, count):
    # comp_robot = avatar.game_logic.GetComp(GameLogicCompKind.ROBOT)
    # from gserver.util.game_logic_comps.comp_robot import WanderRobotDeliveryManager
    # WanderRobotDeliveryManager(comp_robot, time.time(), 3.0, teams * count).Start()
    pass


@gmcmd("robot_formation")
def robot_formation(avatar):
    avatar.DoGM("remove_robot all")

    points = [
        (-539.5278930664062, 201.4302978515625, 524.9458618164062),
        (-548.92041015625, 201.4302978515625, 520.4540405273438),
        # (-621.1790161132812, 201.4302978515625, 513.512451171875),
        # (-617.908935546875, 201.4302978515625, 518.8209838867188),
        (-531.3348388671875, 210.3234405517578, 559.2470703125),
        (-523.5720825195312, 210.3234405517578, 567.4140625),
        (-550.0337524414062, 207.3795166015625, 652.759033203125),
        (-547.1569213867188, 207.3742218017578, 644.3380126953125),
        (-661.583740234375, 207.38917541503906, 522.9046630859375),
        (-656.47119140625, 207.38890075683594, 528.0191040039062),
        # (-589.6549682617188, 208.06680297851562, 582.9403686523438),
        # (-594.60302734375, 208.06680297851562, 588.4871826171875),
        (-642.3432006835938, 218.60385131835938, 623.2177124023438),
        (-639.3629760742188, 218.60385131835938, 636.1983032226562)
    ]

    for _ in list(range(len(points))):
        avatar.DoGM("robot ''")

    for r in avatar.space.combat_avatars.values():
        r.IsRobotCombatAvatar and r.StopAI()
        avatar.game_logic.count_down = 9999.0

    count = 0
    for a in avatar.space.combat_avatars.values():
        if a.IsRobotCombatAvatar:
            pos = points[int(count)]
            a.position = pos
            a.brain_class_name = "ProjectionBrain"
            count += 1
            a.AddItemByItemID(1, 1)
            a.AddItemByItemID(1, 1)

    for r in list(avatar.space.combat_avatars.values()):
        r.SetMaxHp(9999999)
        r.SetHp(9999999)
        r.GetCurWeapon().ammo = 9999999

    # avatar.game_logic.cancel_timer(avatar.game_logic._robot_balance_timer)
    # avatar.game_logic._robot_balance_timer = None


@gmcmd("clear_robot_dummy")
def clear_robot_dummy(avatar):
    comp_rt = avatar.game_logic.GetComp(GameLogicCompKind.ROBOT)
    comp_rt.dummy_ai_count = 0
    comp_rt.dummy_team_count = 0
    avatar.game_logic.RefreshAliveCount()


@gmcmd("kill_all_robots")
def kill_all_robots(avatar):
    for combat_team in list(avatar.game_logic.team_dict.values()):
        avatar.game_logic.KillRobotTeam(combat_team)


@gmcmd('debug_server_nav_map', Float("x"), Float("y"), Float("z"), Float("range"))
def _do_debug_server_nav_map(avatar, x, y, z, search_range=50):
    if search_range > 0:

        conns = avatar.space.get_offmesh_connections(x, y, z, search_range, 0x30, 0)
        verts, indices, areas, flags, _ = avatar.space.get_navi_poly_data(x, y, z, search_range, 0x30, 0)

        avatar.client.on_debug_server_nav_map(verts, indices, conns, areas, 1)
    else:
        # avatar.call_client('on_debug_server_nav_map', [], [], [], [], False)
        avatar.client.on_debug_server_nav_map([], [], [], [], False)


@gmcmd('debug_draw_tactical', Float("x"), Float("z"), Float("radius"))
def _do_debug_draw_tactical(avatar, x, z, radius):
    print("debug_draw_tactical", x, z, radius)
    if radius > 0:
        points = []
        navi_tactical = avatar.space.game_logic.helen_nav_tactical
        if navi_tactical:
            points = navi_tactical.Query(x, z, radius)
    else:
        points = []
    avatar.client.debug_draw_tactical(points)


@gmcmd('eqs_debug_on', Str("机器人ID"))
def _do_eqs_debug_on(avatar, monster_id):
    from HelenAI import ai_consts
    monster = avatar.space.entities.get(monster_id)
    if not monster:
        return

    monster.enable_draw_eqs = not monster.enable_draw_eqs
    monster.ai_blackboard.set_tuple_value(ai_consts.AI_SQUAD_TASK_POS_BBK, monster.position)
    if monster.enable_draw_eqs:
        monster.draw_eqs_items()
    else:
        monster.forbidden_draw_eqs_items()


@gmcmd('restart_helen_robots_ai')
def _do_restart_helen_robots_ai(avatar):
    combat_avatar = avatar.combat_avatar
    for e in list(combat_avatar.space.entities.values()):
        if e.IsHelenAI or getattr(e, 'helen_ai', False):
            if not e or e.is_destroyed() or not e.is_alive:
                continue
            if e.helen_ai.enable_init_ai:
                continue
            # e.helen_ai.world_state_local = e.helen_ai.initial_world_state
            e.helen_ai.start_ai_system()
            e.helen_ai.ai_service_start()
            e.helen_ai.start_ai_threaten_logic()
            e.helen_ai.report_activate_to_squad()


@gmcmd("stop_entity_ai", Str("需要暂停AI的ID(可不填,不填为停止所有AI)"))
def _do_stop_ai(avatar, e_id=None):
    combat_avatar = avatar.combat_avatar

    def stop_ai(entity):
        helen_ai = getattr(entity, 'helen_ai', None)
        if not helen_ai and hasattr(entity, "ai_bt_runner"):
            helen_ai = entity  # 小怪的AI就是自身

        if not entity or entity.is_destroyed() or not helen_ai:  # 无实体或无AI
            return
        helen_ai.stop_ai_system()
        helen_ai.enable_init_ai = False
        entity.cancel_nav("gm")

    if not e_id:
        for e in list(combat_avatar.space.entities.values()):
            # if getattr(e, "helen_ai"):
            stop_ai(e)
        return "暂停所有AI成功！！"

    if e_id:
        entity = combat_avatar.space.entities.get(e_id)
        # if getattr(entity, "helen_ai"):
        stop_ai(entity)
        if not entity:
            return "无AI可暂停"
        return "{0}AI暂停成功".format(entity)


@gmcmd('ai_change_config', Str("AI的ID"), EntityID("替换AI的entity_id"))
def _do_ai_change_config(avatar, config_name, e_id):
    ai_entity = avatar.space.entities.get(e_id)
    helen_ai = getattr(ai_entity, 'helen_ai', None)
    if helen_ai:
        helen_ai.insert_change_ai_config_action(config_name)
        return {'ai_change_config': config_name}

    ai_entity.insert_change_ai_config_action(config_name)
    return {'ai_change_config': config_name}


@gmcmd('do_debug_server_game_tag', Float("x"), Float("y"), Float("z"), Float("radius"))
def _do_debug_server_game_tag(avatar, x, y, z, radius=50):
    new_game_tag_map = avatar.space.game_logic.helen_game_tag_map
    if radius > 0:
        volumes = new_game_tag_map.get_game_tag_volumes(x, y, z, radius)
        avatar.client.on_debug_game_tag(True, volumes if volumes else [])
    else:
        avatar.client.on_debug_game_tag(True, [])


@gmcmd('debug_indoor_cities_2d')
def _do_debug_indoor_cities_2d(avatar):
    helen_royale_map = avatar.space.game_logic.helen_royale_map
    if helen_royale_map:
        city_ids = helen_royale_map.indoor_map.get_tactical_areas_in_range(avatar.position[0], avatar.position[2], 100000, 1 << 0)
        cities = []
        for city_id in city_ids:
            city_obj = helen_royale_map.indoor_map.debug_mapitem_to_py(city_id, False)
            if city_obj:
                cities.append(city_obj[1])
        avatar.client.on_debug_indoor_city_2d(cities, [])


@gmcmd('debug_indoor_noncities_2d')
def _do_debug_indoor_noncities_2d(avatar):
    helen_royale_map = avatar.space.game_logic.helen_royale_map
    if helen_royale_map:
        city_ids = helen_royale_map.indoor_map.get_tactical_areas_in_range(avatar.position[0], avatar.position[2], 100000, 1 << 1)
        cities = []
        for city_id in city_ids:
            city_obj = helen_royale_map.indoor_map.debug_mapitem_to_py(city_id, False)
            if city_obj:
                cities.append(city_obj[1])
        avatar.client.on_debug_indoor_city_2d(cities, [])


@gmcmd('debug_tactical_area_detail', Float("x"), Float("y"), Float("z"))
def _do_debug_tactical_area_detail(avatar, x, z, r):
    helen_royale_map = avatar.space.game_logic.helen_royale_map
    if helen_royale_map and r >= 0:
        area_ids = helen_royale_map.indoor_map.get_tactical_areas_in_range(x, z, 0, 1 << 0 | 1 << 1)
        if area_ids:
            for area_Id in area_ids:
                is_in_city = helen_royale_map.indoor_map.is_in_city(x, z, area_Id)
                if not is_in_city:
                    continue
                city_obj = helen_royale_map.indoor_map.debug_mapitem_to_py(area_Id, True)
                avatar.client.on_debug_indoor_city([city_obj[1]])
                return
    avatar.client.on_debug_indoor_city([])


@gmcmd('debug_indoor_regions', Float("x"), Float("y"), Float("z"), Float("search_range"))
def _do_debug_indoor_regions(avatar, x, y, z, search_range=50):
    helen_royale_map = avatar.space.game_logic.helen_royale_map
    if helen_royale_map and search_range > 0:
        debug_infos = []
        build_ids = helen_royale_map.indoor_map.get_items_in_range(x, z, search_range, 1 << 10)
        for build_id in build_ids:
            build_obj = helen_royale_map.indoor_map.debug_mapitem_to_py(build_id, True)
            debug_infos.append(build_obj[1])
        avatar.client.on_debug_indoor_regions(debug_infos if debug_infos else [])
    else:
        avatar.client.on_debug_indoor_regions([])


@gmcmd('debug_helen_env_tacticals', Float("x"), Float("z"), Float("radius"))
def _do_debug_helen_env_tacticals(avatar, x, z, radius):
    if radius > 0.1:
        helen_royale_map = avatar.space.game_logic.helen_royale_map
        if helen_royale_map:
            debug_infos = []
            build_ids = helen_royale_map.indoor_map.get_items_in_range(x, z, radius, 1 << 13 | 1 << 14)
            for build_id in build_ids:
                build_obj = helen_royale_map.indoor_map.debug_mapitem_to_py(build_id, False)
                debug_infos.append(build_obj)

            # print "debug_helen_env_tacticals", debug_infos
            avatar.client.on_debug_env_tacticals(debug_infos)
            return
    avatar.client.on_debug_env_tacticals([])


@gmcmd('snare_filter')
def snare_filter(avatar):
    snare_filter_real(avatar)


@Async
def snare_filter_real(avatar):
    f = '%s_snare.py' % avatar.space.spaceno
    f1 = '%s_item.py' % avatar.space.spaceno
    f = open(f, 'w')
    f1 = open(f1, 'w')
    f.write('data = (\n')
    f1.write('data = (\n')
    yield 3
    get_pos = avatar.space.find_nearest_valid_pos
    cubedis = formula.Distance3DSquare
    ecotop = avatar.space.game_logic.GetComp(consts.GameLogicCompKind.ECOTOPE)
    snares = ecotop.snares
    for guid, snare in snares.items():
        snare_pos = snare.position
        if cubedis(get_pos(snare_pos[0], snare_pos[1], snare_pos[-1], 1), snare_pos) > 36:
            f.write(str(snare_pos) + ',\n')
            avatar.ConsoleLog(str(snare_pos))
            yield 0.1
        for guid, data in snare.data["items"].items():
            pos = data["position"]
            if cubedis(get_pos(pos[0], pos[1], pos[-1], 1), pos) > 36:
                f1.write(str(pos) + ',\n')
                yield 0.1

    f.write(')\n')
    f.close()
    f1.write(')\n')
    f1.close()
    avatar.ConsoleLog('DONE!!!!!!!!!!!!!')


@gmcmd('item_filter')
def item_filter(avatar):
    item_filter_impl(avatar)


@Async
def item_filter_impl(avatar):
    f = 'unreach_%s_item.py' % avatar.space.spaceno
    f = open(f, 'w')
    f.write('data = (\n')
    yield 1
    slots = avatar.space.game_logic.common_aoi_mgr.units
    # getentity = avatar.space.entities.get
    get_pos = avatar.space.find_nearest_valid_pos
    cubedis = formula.Distance3DSquare
    for j in list(slots.values()):
        if not j or j.tag != 0x08:
            continue
        x, y, z = j.position
        tpos = get_pos(x, y, z, 1)
        ray_result = avatar.space.RaycastClosest((tpos[0], tpos[1] + 1, tpos[2]), (x, y + 1, z), consts.PHYSICS_COMMON_OBSTACLE)
        if cubedis(tpos, j.position) > 16 or ray_result:
            f.write(str(j.position) + ',\n')
            avatar.ConsoleLog(str(j.position))
            # avatar.client.DrawDebugPos({'position': (x, z), 'color': (255, 0, 0), 'radius': 10})
            yield 0.1
        else:
            paths = avatar.space.find_path_point_detail(tpos[0], tpos[1], tpos[2], x, y, z, 0x70)
            ray_result = avatar.space.RaycastClosest((tpos[0], tpos[1] + 1, tpos[2]), (x, y + 1, z), consts.PHYSICS_COMMON_OBSTACLE)
            if not paths:
                f.write(str(j.position) + ',\n')
                avatar.ConsoleLog(str(j.position))
                # avatar.client.DrawDebugPos({'position': (x, z), 'color': (255, 0, 0), 'radius': 10})
                yield 0.1
                continue
            end_point = paths[-1]
            ray_result = avatar.space.RaycastClosest((end_point[0], tpos[1] + 1, tpos[2]), (x, y + 1, z), consts.PHYSICS_COMMON_OBSTACLE)
            if cubedis(paths[-1], j.position) > 16 or ray_result:
                f.write(str(j.position) + ',\n')
                avatar.ConsoleLog(str(j.position))
                # avatar.client.DrawDebugPos({'position': (x, z), 'color': (255, 0, 0), 'radius': 10})
                yield 0.1

    f.write(')\n')
    f.close()
    avatar.ConsoleLog('DONE!!!!!!!!!!!!!')


@gmcmd('switch_helen_ai', Bool("enable"))
def switch_helen_ai(avatar, enable):
    comp = avatar.game_logic.GetComp(GameLogicCompKind.ROBOT)
    comp.use_helen_ai = enable


@gmcmd('set_can_choose_actor', Bool("是否可选肉鸡"))
def _do_set_can_choose_actor(avatar, can_choose=False):

    for e in list(avatar.space.combat_avatars.values()):

        if e.IsHelenAI or getattr(e, 'helen_ai', False):
            if not e or e.is_destroyed() or not e.is_alive:
                continue
            if not can_choose:
                e.can_set_actor = False
                e.on_change_actor('')
                e.candidate_actor = ''
            else:
                e.can_set_actor = True


@gmcmd('robot_loop_shoot', Float("射击时长"))
def _do_robot_loop_shoot(avatar, shoot_time=2.0):
    for k, e in avatar.space.combat_avatars.items():

        if e.IsHelenAI or getattr(e, 'helen_ai', False):
            if not e or e.is_destroyed() or not e.is_alive:
                continue
            e.debug_ai_shoot(shoot_time)


@gmcmd('robot_loop_shoot_end')
def _do_robot_loop_shoot_end(avatar):  # no qa
    for k, e in avatar.space.combat_avatars.items():

        if e.IsHelenAI or getattr(e, 'helen_ai', False):
            if not e or e.is_destroyed() or not e.is_alive:
                continue
            e.debug_stop_ai_shoot()


@gmcmd('robots_add_item', Int("物品ID"))
def _do_robots_add_item(avatar, item_id):
    for k, e in avatar.space.combat_avatars.items():

        if e.IsHelenAI or getattr(e, 'helen_ai', False):
            if not e or e.is_destroyed() or not e.is_alive:
                continue
            e.AddItemByItemID(item_id, 1)


@gmcmd('robots_entity_add_item', Str("EntityId"), Int("物品ID"))
def _do_robots_add_item(avatar, entity_id, item_id):  # noqa
    entity = avatar.space.entities.get(entity_id, None)
    if not entity:
        return
    if entity.IsHelenAI or getattr(entity, 'helen_ai', False):
        if not entity or entity.is_destroyed() or not entity.is_alive:
            return
        entity.AddItemByItemID(item_id, 1)


@gmcmd('create_robots', Int('数量'), Float('radius'))
def create_robots(avatar, count, radius):
    space = avatar.space

    x, y, z = avatar.combat_avatar.position
    theta = math.pi * 2 / count
    base = 101
    for i in range(count):
        xa = math.cos(i * theta) * radius
        za = math.sin(i * theta) * radius
        data = {
            "match_type": space.match_type,
            "name": irobot.RandomName(),
            "masterid": "",
            "hand_entity_id": "",
            "tester": avatar.combat_avatar.id,
            # "main_client_id": avatar.id,
        }

        game_logic = space.game_logic
        position = space.GetGroundPosition((x + xa, y + 2, z + za))

        robot = space.create_entity(
            entitytype="HelenRobot" + game_logic.COMBAT_AVATAR_TYPE,
            entityid=None,
            entitycontent=data,
            position=position,
            direction=formula.FaceYaw(position, avatar.combat_avatar.position),
        )
        robot.can_set_actor = False
        robot.on_change_actor('')
        robot.candidate_actor = ''
        robot.RefreshBackpackFromAvatar(avatar, True)
        space.CombatAvatarEnter(robot)
        robot.ai_target_position = CustomListType((x, y, z))
        robot.para_reason = 1
        robot.SetParaStage(9)
        helen_ai = getattr(robot, 'helen_ai', None)
        if helen_ai:
            helen_ai.stop_ai_system()
        for j in range(20):
            if base + i not in hero_data.data:
                base += 1
            else:
                break
        if (base + i) in hero_data.data:
            robot.SelectHeroImpl(base + i)
        robot.DoTest()


@gmcmd('create_shoot_test_robot', Int("武器ID"))
def create_shoot_test_robot(avatar, weapon_id=None):
    space = avatar.space

    x, y, z = avatar.combat_avatar.position
    _dir = formula.YawToVector(avatar.combat_avatar.yaw)
    data = {
        "match_type": space.match_type,
        "name": irobot.RandomName(),
        "masterid": "",
        "hand_entity_id": "",
        "tester": avatar.combat_avatar.id,
        "main_client_id": avatar.id,
    }

    game_logic = space.game_logic
    position = space.GetGroundPosition(formula.Add3D((x, y, z), formula.Mul3D(_dir, 3)))

    robot = space.create_entity(
        entitytype="HelenRobot" + game_logic.COMBAT_AVATAR_TYPE,
        entityid=None,
        entitycontent=data,
        position=position,
        direction=formula.FaceYaw(position, avatar.combat_avatar.position),
    )
    # robot.can_set_actor = False
    robot.on_change_actor(avatar.id)
    robot.candidate_actor = avatar.id
    robot.RefreshBackpackFromAvatar(avatar, True)
    space.CombatAvatarEnter(robot)
    robot.ai_target_position = CustomListType((x, y, z))
    robot.para_reason = 1
    robot.SetParaStage(9)
    helen_ai = getattr(robot, 'helen_ai', None)
    if helen_ai:
        helen_ai.stop_ai_system()
    if weapon_id in equip_data.data:
        info = equip_data.data[weapon_id]
        cur_weapon = robot.GetCurWeapon()
        if cur_weapon:
            robot.RemoveItem(cur_weapon.guid)
        robot.AddItemByItemID(info['mod_id'], 1)
    robot.DoWeaponShootTest()


@gmcmd('fill_robots')
def fill_robots(avatar):
    from gshare.iroom import OptionKey
    avatar.c.game_logic.SetOption(OptionKey.DisableBot, False)