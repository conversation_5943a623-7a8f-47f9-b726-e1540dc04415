# -*- coding: utf-8 -*-

import MType
import MCharacter
import MEngine
import MObject
import math
import functools
import random
import time
import switches
from collections import defaultdict
from gclient import cconst
from gshare.utils import Singleton
from gclient.data import material_type_data, effect_data, hit_perform_data, weapon_data, gun_skin_template_data, random_effect_data
from gclient.framework.util import MHelper
from gclient.gameplay.util import replay_util
from gclient.util.decal_util import GenerateHitDecal, GenerateHitMesh
from gshare import formula, weapon_util, consts
from gshare.formula import Tuple
from common.mobilecommon import COMPONENT


IdempotentCaches = {}


# 标记某个函数为幂等函数，做cache
def idempotent(func):
    if COMPONENT == "Client":
        cache = IdempotentCaches[func.__name__] = {}

        @functools.wraps(func)
        def _wrapper(effect_id):
            if effect_id not in cache:
                cache[effect_id] = func(effect_id)
            return cache[effect_id]
        return _wrapper
    else:
        return func


def WrapperHitEffectResult(target, hit_pos, hit_normal, hit_material_type, hit_dir, weapon_id, gun_type=0, hit_perform_id=-1, hit_pos_through=None, hit_part=None):
    if hit_material_type == -1 or target and target.IsHolographicRobot:  # 全息诱饵就不播了
        return
    hit_effect_data = {
        'hit_pos': Tuple(hit_pos),
        'hit_normal': Tuple(hit_normal),
        'hit_dir': Tuple(hit_dir),
        'hit_material_type': hit_material_type,
        'hit_perform_id': hit_perform_id,
        'weapon_id': weapon_id,
        'gun_type': gun_type,
    }
    hit_perform_mode = 0
    if hit_part:
        hit_effect_data['hit_part'] = hit_part
    if hit_pos_through:
        hit_effect_data['hit_pos_through'] = hit_pos_through
    if target:
        hit_effect_data['target_id'] = target.id
        if target.IsStrikeItem or target.IsBreakItem:
            hit_perform_mode |= 1 << 2  # 无decal
    hit_effect_data['hit_perform_mode'] = hit_perform_mode
    return hit_effect_data


def HandleDebugVisualization(hit_pos):
    """处理调试可视化"""
    # [DEBUG]
    if not cconst.DEBUG_SHOW_BULLET_DATA:
        return
    import MDebug
    scale = 0.01
    drawer = MDebug.Box()
    drawer.position = MType.Vector3(*hit_pos)
    drawer.scale = MType.Vector3(scale, scale, scale)
    drawer.color = MType.Vector3(0, 1, 1)
    cconst.DEBUG_SHOW_BULLET_DRAW.append(drawer)
    genv.avatar.add_timer(5, lambda: cconst.DEBUG_SHOW_BULLET_DRAW.pop(0))
    # [DEBUG]
    return


class HitEffectConfig(object):

    __slots__ = (
        'hit_pos', 'hit_dir', 'hit_normal', 'weapon_id', 'target_id', 'hit_material_type', 'hit_perform_id',
        'hit_pos_through', 'hit_part', 'gun_type', 'hit_perform_mode'
    )

    def __init__(self, hit_effect_dict):
        info_getter = hit_effect_dict.get
        self.hit_pos = info_getter('hit_pos')
        self.hit_dir = info_getter('hit_dir')
        self.hit_normal = info_getter('hit_normal')
        self.weapon_id = info_getter('weapon_id')
        self.target_id = info_getter('target_id')
        self.hit_material_type = info_getter('hit_material_type')
        self.hit_perform_mode = info_getter('hit_perform_mode', 0)  # 0表示正常，1表示近战标志，1 << 1 : 表示不播特效，1 << 2 表示无decal
        self.hit_perform_id = info_getter('hit_perform_id', -1)  # 非负则为指定了perform id
        self.hit_pos_through = info_getter('hit_pos_through')
        self.hit_part = info_getter('hit_part')
        self.gun_type = info_getter('gun_type', 0)


class HitPerformance(object):

    __slots__ = ('effect_path', 'decal_path_list', 'decal_width', 'decal_threshold', 'decal_mesh_list', 'guntype_info', 'is_hitfx_kill')

    def __init__(self, hit_perform_id):
        info_getter = hit_perform_data.data.get(hit_perform_id, {}).get
        effect_data_getter = effect_data.data.get
        dlc_checker = genv.dlc_manager.CheckDlcExists
        _effect_info = effect_data_getter(info_getter('hit_effect_id', None), {})
        self.effect_path = _effect_info.get('path', None) if dlc_checker(_effect_info.get('dlc_id', None)) else None

        self.decal_path_list = info_getter('decal_path', None)
        self.decal_width = info_getter('decal_width', 0.14)
        self.decal_threshold = info_getter('decal_threshold', 0.1)
        self.decal_mesh_list = info_getter('decal_mesh_material_path', None)
        self.guntype_info = info_getter('guntype_info', {})
        self.is_hitfx_kill = info_getter('is_hitfx_kill', False)


class HitPerformancePlayer(HitPerformance):

    __slots__ = HitPerformance.__slots__ + ('through_effect_path', 'head_effect_path', 'head_through_effect_path', 'hit_follow_effect_path')

    def __init__(self, hit_perform_id):
        super(HitPerformancePlayer, self).__init__(hit_perform_id)
        info_getter = hit_perform_data.data.get(hit_perform_id, {}).get
        effect_data_getter = effect_data.data.get
        dlc_checker = genv.dlc_manager.CheckDlcExists
        _effect_info = effect_data_getter(info_getter('hit_through_effect_id', None), {})
        self.through_effect_path = _effect_info.get('path', None) if dlc_checker(_effect_info.get('dlc_id', None)) else None
        _effect_info = effect_data_getter(info_getter('hit_head_effect_id', None), {})
        self.head_effect_path = _effect_info.get('path', None) if dlc_checker(_effect_info.get('dlc_id', None)) else None
        _effect_info = effect_data_getter(info_getter('hit_head_through_effect_id', None), {})
        self.head_through_effect_path = _effect_info.get('path', None) if dlc_checker(_effect_info.get('dlc_id', None)) else None
        _effect_info = effect_data_getter(info_getter('follow_effect_id', None), {})
        self.hit_follow_effect_path = _effect_info.get('path', None) if dlc_checker(_effect_info.get('dlc_id', None)) else None


class HitEffectProcessor(Singleton):
    """击中特效处理器"""
    
    def init(self):
        self.hit_perform_pool = {}
        self.last_hit_effect_info = {}
        self.hit_effect_records = defaultdict(float)
        self.hit_effect_min_interval = 0.0

    def GetHitPerformance(self, hit_config, is_combat_avatar):
        """获取命中表现配置"""
        if hit_config.hit_perform_id < 0:
            material_type_id = hit_config.hit_material_type
            if material_type_id is None:
                return None
            _query_key = 'hit_perform_id' if (hit_config.hit_perform_mode & 1 << 0) == 0 else 'melee_hit_perform_id'
            hit_perform_id = material_type_data.data.get(material_type_id, {}).get(_query_key, -1)
        else:
            hit_perform_id = hit_config.hit_perform_id
        if hit_perform_id in hit_perform_data.data:
            return self.hit_perform_pool.setdefault(hit_perform_id, HitPerformancePlayer(hit_perform_id) if is_combat_avatar else HitPerformance(hit_perform_id))
        return None

    @staticmethod
    def ShouldSkipEffect(config, caster, target):
        """检查是否跳过特效播放 - 合并早期退出条件"""
        # 基础参数检查
        if not target:
            return False
        if target.is_fps_avatar or not config.hit_pos or not config.hit_dir or not caster:
            return True
        # 友军攻击跳过
        game_logic = caster.space.game_logic
        if game_logic and target.IsCombatAvatar and game_logic.IsFriend(caster, target):
            return True
        return False
    
    def CheckHitEffectFrequency(self, caster, target):
        """检查特效播放频率限制"""
        # 如果是其他人打其他人，那么飙血特效可以限一下频
        if self.hit_effect_min_interval <= 0.0 or caster.is_fps_avatar or not target:
            return True
        now = time.time()
        target_id = target.id
        if now > self.hit_effect_records.get(target_id, 0):
            self.hit_effect_records[target_id] = now + self.hit_effect_min_interval
            return True
        return False
    
    def PlayStandardAvatarEffects(self, config, target, hit_performance, insure_play):
        """播放标准角色特效"""
        eid = None
        if (config.hit_perform_mode & 1 << 1) == 1:
            return eid
        # 跟随特效
        # if hit_follow_effect_path := hit_performance.hit_follow_effect_path:
        #     MHelper.PlayEffectFollowTarget(target.model.model, hit_follow_effect_path, config.hit_pos, insure_play=insure_play)
        # 主特效
        target_model = target.model.model
        is_head_hit = (config.hit_part and config.hit_part.lower() == consts.AvatarCollisionBone_Head)
        if hit_effect_path := (hit_performance.head_effect_path if is_head_hit else hit_performance.effect_path):
            eid = MHelper.PlayEffectWithTarget(target_model, hit_effect_path, config.hit_pos, insure_play=insure_play, hit_dir=config.hit_dir)
        # 穿透特效
        if config.hit_pos_through:
            if hit_through_effect_path := hit_performance.head_through_effect_path if is_head_hit else hit_performance.through_effect_path:
                MHelper.PlayEffectWithTarget(target_model, hit_through_effect_path, config.hit_pos_through, insure_play=insure_play, hit_dir=config.hit_dir, is_through=True)
        return eid
    
    def PlaySimpleUnitEffect(self, config, target, hit_performance):
        """播放简单单位击中特效"""
        target_hit_pos, _ = GetHitPosFromTarget(target, config.hit_pos, config.hit_normal)
        # 护盾冲击波
        is_shield = target.IsMagicField and target.IsThisMagicFieldRealField()
        if is_shield:
            target.ProcessShockwaveShield(pos=target_hit_pos)
        # 特效播放
        hit_effect_path = hit_performance.effect_path
        if target.model.GetSkeleton() and not is_shield:
            return MHelper.PlayEffectWithTarget(target.model.model, hit_effect_path, config.hit_pos, insure_play=True, hit_dir=config.hit_normal)
        else:
            max_life = target.space.game_logic.GetHitEffectMaxLifeTime(config.weapon_id)
            return MHelper.PlayHitEffectInWorld(hit_effect_path, MType.Vector3(*config.hit_pos), MType.Vector3(*config.hit_normal), max_life=max_life, insure_play=True)
    
    def PlayWorldEffect(self, config, hit_performance, caster):
        """播放世界特效和贴花"""
        hit_pos = MType.Vector3(*config.hit_pos)
        hit_normal = MType.Vector3(*config.hit_normal)
        # 特殊材质法线处理
        if config.hit_material_type == 14:  # 不可破玻璃
            hit_normal = -hit_normal
        eid = None
        # 播放世界特效
        hit_effect_path = hit_performance.effect_path
        if hit_effect_path and (config.hit_perform_mode & 1 << 1) == 0:
            game_logic = caster.space.game_logic
            eid = MHelper.PlayEffectInWorldToLayerByPos(
                hit_effect_path, hit_pos, max_life=game_logic.GetHitEffectMaxLifeTime(config.weapon_id),
                layer=cconst.EFFECT_LAYER_PLAYER if caster == genv.player else cconst.EFFECT_LAYER_NPC,
                priority=cconst.EFFECT_PRIORITY_MEDIUM
            )
        # 生成贴花
        (config.hit_perform_mode & 1 << 2) == 0 and self.GenerateDecalEffects(config, hit_pos, hit_normal, caster, hit_performance)
        return eid
    
    def GenerateDecalEffects(self, config, hit_pos, hit_normal, caster, hit_performance):
        """生成贴花效果"""
        decal_grids = caster.space.hit_decal_manager.GetDecalGrids(config.hit_material_type)
        if not decal_grids:
            return
        # 网格材质贴花
        if decal_mesh_material_path_list := hit_performance.decal_mesh_list:
            decal_threshold, decal_width = None, None
            if decal_gun_info := hit_performance.guntype_info:
                if cur_info := decal_gun_info.get(config.gun_type):
                    decal_threshold, decal_width = cur_info
            decal_width = decal_width or hit_performance.decal_width
            decal_threshold = decal_threshold or hit_performance.decal_threshold
            batcher = caster.space.hit_decal_manager.GetHitMeshBatcher()
            GenerateHitMesh(batcher, decal_grids, random.choice(decal_mesh_material_path_list), -hit_normal, hit_pos, yoffset=0, width=decal_width, height=decal_width, life_time=15, fade_time=0.5, threshold=decal_threshold)
        # 普通贴花
        if decal_path_list := hit_performance.decal_path_list:
            decal_threshold, decal_width = None, None
            if decal_gun_info := hit_performance.guntype_info:
                if cur_info := decal_gun_info.get(config.gun_type):
                    decal_threshold, decal_width = cur_info
            decal_width = decal_width or hit_performance.decal_width
            decal_threshold = decal_threshold or hit_performance.decal_threshold
            GenerateHitDecal(decal_grids, random.choice(decal_path_list), hit_normal, hit_pos, half_depth=0.1, width=decal_width, height=decal_width, life_time=15, fade_time=5, threshold=decal_threshold)


def PlayHitEffect(caster, hit_effect):
    # 配置预处理 - 一次性获取所有配置项
    config = HitEffectConfig(hit_effect)
    processor = HitEffectProcessor.instance()
    # [DEBUG]
    HandleDebugVisualization(config.hit_pos)
    # [DEBUG]
    # 获取目标
    target = None
    if config.target_id:
        target = caster.space.GetEntityByID(config.target_id)
    # 早期退出检查
    if processor.ShouldSkipEffect(config, caster, target):
        return None
    # 频率限制检查
    if not processor.CheckHitEffectFrequency(caster, target):
        return None
    hit_performance = processor.GetHitPerformance(config, target and target.IsCombatAvatar)
    if hit_performance is None:
        return None
    if hit_performance.is_hitfx_kill: # 清理掉之前的命中特效
        last_eid = processor.last_hit_effect_info.get(caster.id, None)
        last_eid and MHelper.ClearEffectInWorld(last_eid, True)
    # 根据目标类型分发处理
    if target and target.IsCombatAvatar:
        e_id = processor.PlayStandardAvatarEffects(config, target, hit_performance, insure_play=caster == genv.player)
    elif target and target.IsSimpleCombatUnit:
        e_id = processor.PlaySimpleUnitEffect(config, target, hit_performance)
    else:
        e_id = processor.PlayWorldEffect(config, hit_performance, caster)
    processor.last_hit_effect_info[caster.id] = e_id
    return e_id

def GetRandomAdsBallisticStartPos(muzzle_trans, radius, angle):
    # 在垂直枪口平面内枪口以下对称扇形中随机取一点
    muzzle_dir = muzzle_trans.z_axis
    muzzle_down_dir = -muzzle_trans.y_axis
    # random_radius = random.uniform(0, radius)
    random_angle = random.uniform(-angle / 2, angle / 2)
    random_theta = math.radians(random_angle)
    random_dir = muzzle_down_dir.rotate(muzzle_dir, random_theta)
    random_pos = radius * random_dir
    return random_pos


def ConfigBallisticEffect(ballistic_effect_id, is_ads, bullet_speed, hit_dis, is_fps_ballistic=True, gun_type=None, custom_scale=None):
    ballistic_effect_entity = MCharacter.GetEffectEntity(ballistic_effect_id)
    if not ballistic_effect_entity:
        return
    if gun_type in (consts.GunType.MR, consts.GunType.SR):
        ballistic_param = cconst.BALLISTIC_EFFECT_PARAM_SR
    else:
        ballistic_param = cconst.BALLISTIC_EFFECT_PARAM
    if is_fps_ballistic:
        remap = formula.LinearMapNumber
        if is_ads:
            ballistic_velocity = remap(hit_dis, ballistic_param['AdsVelocityInRange'], ballistic_param['AdsVelocityOutRange'])
            ballistic_position = remap(hit_dis, ballistic_param['AdsPositionInRange'], ballistic_param['AdsPositionOutRange'])
            ballistic_scale_x = remap(hit_dis, ballistic_param['AdsScaleXInRange'], ballistic_param['AdsScaleXOutRange'])
            ballistic_scale_y = remap(hit_dis, ballistic_param['AdsScaleZInRange'], ballistic_param['AdsScaleZOutRange'])
        else:
            ballistic_velocity = remap(hit_dis, ballistic_param['VelocityInRange'], ballistic_param['VelocityOutRange'])
            ballistic_position = remap(hit_dis, ballistic_param['PositionInRange'], ballistic_param['PositionOutRange'])
            ballistic_scale_x = remap(hit_dis, ballistic_param['ScaleXInRange'], ballistic_param['ScaleXOutRange'])
            ballistic_scale_y = remap(hit_dis, ballistic_param['ScaleZInRange'], ballistic_param['ScaleZOutRange'])

        velocity_param = MObject.CreateObject("ParticleParameterConstantVector3")
        velocity_param.Value = MType.Vector3(0, 0, ballistic_velocity)
        position_param = MObject.CreateObject("ParticleParameterConstantVector3")
        position_param.Value = MType.Vector3(0, 0, ballistic_position)
        scale_param = MObject.CreateObject("ParticleParameterConstantVector2")
        scale_param.Value = MType.Vector2(ballistic_scale_x, ballistic_scale_y)

        for prim in ballistic_effect_entity.Primitives:
            prim.AddParameter("InitVelocity", velocity_param)
            prim.AddParameter("InitPosition", position_param)
            prim.AddParameter("InitScaleFactor", scale_param)
    else:
        if custom_scale:
            scale_param = MObject.CreateObject("ParticleParameterConstantVector2")
            scale_param.Value = MType.Vector2(*custom_scale)
        else:
            scale_param = None
        velocity_param = MObject.CreateObject("ParticleParameterConstantVector3")
        velocity_param.Value = MType.Vector3(0, 0, bullet_speed * ballistic_param['TpsVelocityFactor'])
        for prim in ballistic_effect_entity.Primitives:
            prim.AddParameter("InitVelocity", velocity_param)
            scale_param and prim.AddParameter("InitScaleFactor", scale_param)


def ConfigBallisticInputData(is_ads, result, ballistic_effect):
    caster = result.caster
    extra = result.extra
    hit_dis = formula.Distance3D(ballistic_effect['create_pos'], ballistic_effect['hit_pos'])
    cconst.DEBUG_SHOW_BULLET_DIST = hit_dis
    remap = formula.LinearMapNumber
    ballistic_param = cconst.TRAIL_BALLISTIC_EFFECT_PARAM
    output = {}
    output['end_pos'] = ballistic_effect['hit_pos']
    output['is_dual'] = ballistic_effect.get('is_dual', False)
    if not caster.IsPlayerCombatAvatar:
        # 3P从技能封包里取
        output['ballistic_velocity'] = result.extra.get('bullet_velocity', 100) * ballistic_param['TpsVelocityFactor']
        output['trail_lifetime'] = ballistic_param['TrailLifeTime3P']
    else:
        if is_ads:
            # ballistic_velocity = remap(hit_dis, ballistic_param['AdsVelocityInRange'], ballistic_param['AdsVelocityOutRange'])
            ballistic_position = remap(hit_dis, ballistic_param['AdsPositionInRange'], ballistic_param['AdsPositionOutRange'])
            ballistic_scale_x = remap(hit_dis, ballistic_param['AdsScaleXInRange'], ballistic_param['AdsScaleXOutRange'])
            ballistic_scale_z = remap(hit_dis, ballistic_param['AdsScaleZInRange'], ballistic_param['AdsScaleZOutRange'])
            ballistic_scale = (ballistic_scale_x, 1, ballistic_scale_z)
            position_out_random = ballistic_param['AdsPositionOutRandom']
        else:
            # ballistic_velocity = remap(hit_dis, ballistic_param['VelocityInRange'], ballistic_param['VelocityOutRange'])
            ballistic_position = remap(hit_dis, ballistic_param['PositionInRange'], ballistic_param['PositionOutRange'])
            ballistic_scale_x = remap(hit_dis, ballistic_param['ScaleXInRange'], ballistic_param['ScaleXOutRange'])
            ballistic_scale_z = remap(hit_dis, ballistic_param['ScaleZInRange'], ballistic_param['ScaleZOutRange'])
            ballistic_scale = (ballistic_scale_x, 1, ballistic_scale_z)
            position_out_random = ballistic_param['PositionOutRandom']
        output['ballistic_velocity'] = extra.get('bullet_velocity', 100)
        output['ballistic_position'] = (0, 0, ballistic_position)
        output['ballistic_scale'] = ballistic_scale
        output['trail_lifetime'] = ballistic_param['TrailLifeTime1P']
        output['random_pos_offset'] = position_out_random
        output['is_ads'] = 1 if is_ads else 0
    return output


def PlayBallisticEffect(caster, spell_id, ballistic_effect, simple_version):
    hit_pos = ballistic_effect['hit_pos']
    if not hit_pos or not caster:
        return
    hit_dis = ballistic_effect['hit_dis']
    is_dual = ballistic_effect.get('is_dual', False)

    weapon_case = caster.GetCurWeaponCase(is_fps_weapon=caster.is_fps_avatar and caster.is_fps_mode)
    if not weapon_case or not weapon_util.IsWeaponOwnGun(weapon_case.weapon_id):
        return
    if is_dual:
        # 双持左手
        if not weapon_case.dual_weapon_body:
            return
        muzzle_trans = weapon_case.dual_weapon_body.GetGunMuzzleTrans()
    else:
        muzzle_trans = weapon_case.GetGunMuzzleTrans()
    if not muzzle_trans:
        return
    bullet_speed = weapon_case.GetWeaponAttrValue('bullet_velocity', 800)
    gun_type = weapon_util.GetWeaponGunType(weapon_case.weapon_id)
    caster_id_ads = caster.is_ads
    if caster.is_fps_avatar and (caster.is_fps_mode or caster_id_ads):
        # N米内不显示第一人称弹道
        if hit_dis < weapon_case.weapon_proto['fps_trail_lightbar_hide_dis']:
            return

        effect_id = None
        fps_trace_bullet_offset = weapon_case.weapon_proto.get('fps_trace_bullet_offset', False)  # 弹道是否需要便宜
        fps_trace_bullet_from_tip = False  # 是否从枪口链接到目标点
        # 曳光弹
        if weapon_case.guise_id and weapon_case.show_guise_bullet_trace:
            if simple_version:
                guise_effect_name = "fps_simple_adstracer_bullet_id" if caster_id_ads else "fps_simple_tracer_bullet_id"
            else:
                guise_effect_name = "fps_adstracer_bullet_id" if caster_id_ads else "fps_tracer_bullet_id"
            skin_proto = gun_skin_template_data.data[weapon_case.guise_id]
            skin_effect_id = skin_proto.get(guise_effect_name)
            if genv.dlc_manager.CheckEffectDlcExists(skin_effect_id):
                effect_id = skin_effect_id
                fps_trace_bullet_offset = skin_proto.get('fps_trace_bullet_offset')
                fps_trace_bullet_from_tip = skin_proto.get('fps_trace_bullet_from_tip')
        if not effect_id:
            # 从枪械数据表里取出弹道特效id
            effect_id = weapon_case.GetTrailLightBarEffectId(simple_version, caster_id_ads, True)
        effect_path = effect_data.data.get(effect_id, {}).get('path')
        if effect_path:
            if not simple_version and caster.is_real_ads:
                start_pos = muzzle_trans.translation
                hit_dir = MType.Vector3(*hit_pos) - start_pos
                if not fps_trace_bullet_offset:
                    start_pos = start_pos + GetRandomAdsBallisticStartPos(muzzle_trans, 0.2, 30.0)
                # genv.space.DrawRay(start_pos, start_pos + MType.Vector3(*hit_dir) * 100, reset=False)
            else:
                start_pos = muzzle_trans.translation
                if fps_trace_bullet_from_tip:
                    hit_dir = MType.Vector3(*hit_pos) - start_pos
                    hit_dir.length = 1.0
                else:
                    hit_dir = MType.Vector3(*ballistic_effect['hit_dir'])
            ballistic_effect_id = MHelper.PlayHitEffectInWorldWithId(effect_id, start_pos, hit_dir, True,
                max_life=2 if simple_version else 10, insure_play=True, playSpeed=1.0, scale=(1.0, 1.0, 1.0))
            if not simple_version:
                ConfigBallisticEffect(ballistic_effect_id, caster.is_ads, bullet_speed, hit_dis, is_fps_ballistic=True, gun_type=gun_type)
    else:
        player = replay_util.GetPlayer()
        if not player:
            return
        start_pos = muzzle_trans.translation
        hit_pos = MType.Vector3(*hit_pos)
        hit_dir = hit_pos - start_pos
        # 其他玩家的弹道
        weapon_effect_data_getter = weapon_data.data.get(weapon_case.gun_id, {}).get
        light_effect_id = weapon_case.GetTrailLightBarEffectId(simple_version, caster_id_ads, False)
        # 曳光弹
        if weapon_case.guise_id:
            skin_light_effect_id = gun_skin_template_data.data.get(weapon_case.guise_id, {}).get('tps_simple_tracer_bullet_id' if simple_version else 'tps_tracer_bullet_id', light_effect_id)
            if genv.dlc_manager.CheckEffectDlcExists(skin_light_effect_id):
                light_effect_id = skin_light_effect_id
        light_life_time = weapon_effect_data_getter('tps_trail_lightbar_life_time', 3)

        if simple_version:
            MHelper.PlayHitEffectInWorldWithId(light_effect_id, start_pos, hit_dir, True,
                max_life=light_life_time, insure_play=True, playSpeed=1.0, scale=(1.0, 1.0, 1.0))
        else:
            # 提前剪枝
            camera_trans = MEngine.GetGameplay().Player.Camera.Transform
            camera_pos = camera_trans.translation
            camera_dir = -camera_trans.z_axis
            camera_to_start = start_pos - camera_pos
            # 射线起点在镜头后面，并且射线方向也朝后，那铁定看不见，不播
            if not (camera_dir.dot(camera_to_start) <= 0 and camera_dir.dot(hit_dir) <= 0):
                # 求相机到射线的距离
                start_to_camera = -camera_to_start
                projection_length = (start_to_camera * hit_dir.get_normalized().dot(start_to_camera.get_normalized())).length
                camera_to_ray_distance = math.sqrt(camera_to_start.length ** 2 - projection_length ** 2)
                ballistic_scale_y = formula.LinearMapNumber(camera_to_ray_distance, (5.0, 100.0), (1.0, 4.0))

                ballistic_effect_id = MHelper.PlayHitEffectInWorldWithId(light_effect_id, start_pos, hit_dir, True,
                    max_life=light_life_time, insure_play=True, playSpeed=1.0, scale=(1.0, 1.0, 1.0))
                ConfigBallisticEffect(ballistic_effect_id, caster.is_ads, bullet_speed, hit_dis,
                    is_fps_ballistic=False, gun_type=gun_type, custom_scale=(1.0, ballistic_scale_y))

        # 子弹威胁音效
        space = genv.space
        if not space:
            return
        game_logic = space.game_logic
        if game_logic and not game_logic.IsEnemy(player, caster):
            return

        sound_range = 5.0
        center = MType.Vector3(*player.position)
        caster_distance = (start_pos - center).length
        # 如果hit_distance小于caster_distance - sound_range，那么hit_pos必然不会落在sound_range范围内，可以直接排除
        if hit_dis < caster_distance - sound_range:
            return
        hit_dir.length = caster_distance
        bullet_near_pos = start_pos + hit_dir
        # 使用让hit_dir的长度调整为caster_distance，判断终点是否位于sound_range范围内，否的话就可以直接排除
        if (bullet_near_pos - center).length > sound_range:
            return
        # 如果hit_pos落在sound_range范围内，那就用直接用hit_pos作为发声点
        if (hit_pos - center).length < sound_range:
            bullet_near_pos = hit_pos

        caster.PlayGeneralSoundEventById(120, pos=bullet_near_pos.tuple(), param_dict={'threat_distance': caster_distance}, gameObjectID=random.randint(100000, 999999))
        switches.BULLET_DEBUG and genv.space.DrawCircleOverlapDebugInfo(bullet_near_pos, radius=0.1, life_time=5)


def ProcessThreatenSound(result, ballistic_effect):
    # 子弹威胁音效
    space = genv.space
    if not space:
        return
    player = replay_util.GetPlayer()
    if not player:
        return
    game_logic = space.game_logic
    caster = result.caster
    if not caster:
        return
    if game_logic and not game_logic.IsEnemy(player, caster):
        return
    if not result.extra:
        return
    start_pos, hit_pos = MType.Vector3(*ballistic_effect['create_pos']), MType.Vector3(*ballistic_effect['hit_pos'])

    sound_range = 5.0
    center = MType.Vector3(*player.position)
    caster_distance = (start_pos - center).length
    hit_dis = formula.Distance3D(start_pos, hit_pos)
    # 如果hit_distance小于caster_distance - sound_range，那么hit_pos必然不会落在sound_range范围内，可以直接排除
    if hit_dis < caster_distance - sound_range:
        return
    hit_dir = hit_pos - start_pos
    hit_dir.length = caster_distance
    bullet_near_pos = start_pos + hit_dir
    # 使用让hit_dir的长度调整为caster_distance，判断终点是否位于sound_range范围内，否的话就可以直接排除
    if (bullet_near_pos - center).length > sound_range:
        return
    # 如果hit_pos落在sound_range范围内，那就用直接用hit_pos作为发声点
    if (hit_pos - center).length < sound_range:
        bullet_near_pos = hit_pos

    caster.PlayGeneralSoundEventById(120, pos=bullet_near_pos.tuple(), param_dict={'threat_distance': caster_distance}, gameObjectID=random.randint(100000, 999999))
    switches.BULLET_DEBUG and genv.space.DrawCircleOverlapDebugInfo(bullet_near_pos, radius=0.1, life_time=5)


def GetHitPosFromTarget(target, hit_pos, hit_normal):
    if not target.model.isValid():
        return hit_pos, hit_normal
    pos = formula.Substract3D(hit_pos, target.position)
    pos = MType.Vector3(*pos)
    m = MType.Matrix4x3()
    m.rotation = target.model.model.Transform.rotation
    m = m.inverse
    pos = m.transform_v(pos)
    if hit_normal:
        if isinstance(hit_normal, tuple) or isinstance(hit_normal, list):
            hit_normal = MType.Vector3(*hit_normal)
        hit_normal = m.transform_p(hit_normal)
        return (pos.x, pos.y, pos.z), (hit_normal.x, hit_normal.y, hit_normal.z)
    else:
        return (pos.x, pos.y, pos.z), None


def WrapperPlayEffectInWorld2(effect_id, trans, time=-1, hook_suffix=None, insure_play=False, src=None):
    # src 是来源 entity
    proto = effect_data.data[effect_id]
    if hook_suffix:
        suffix = hook_suffix
    else:
        suffix = 'root:-1:01000000'
    if 'effect_scale' in proto:
        x, y, z = proto['effect_scale']
        suffix = '%s:s%s,%s,%s' % (suffix, x, y, z)
    insure_play and MCharacter.SetInsureWorldEffectPlay(True)
    effect = MCharacter.PlayEffectInWorld2('%s:%s' % (GetFxPathString(proto, src), suffix), trans, time)
    insure_play and MCharacter.SetInsureWorldEffectPlay(False)
    return effect


def WrapperPlayEffectInWorld(effect_id, pos, time=-1, insure_play=False, sound_event_id=None, src=None):
    if isinstance(pos, tuple) or isinstance(pos, list):
        pos = MType.Vector3(*pos)
    proto = effect_data.data[effect_id]
    effect_str = proto.get('effect_string')
    if not effect_str:
        path = GetFxPathString(proto, src)
        if not path:
            return
        suffix = 'root:-1:01000000'
        if 'effect_scale' in proto:
            x, y, z = proto['effect_scale']
            suffix = '%s:s%s,%s,%s' % (suffix, x, y, z)
        effect_str = '%s:%s' % (path, suffix)
    elif src is not None:
        path = GetFxPathString(proto, src)
        if path:
            splits = effect_str.split(':')
            splits[0] = path
            effect_str = ':'.join(splits)

    insure_play and MCharacter.SetInsureWorldEffectPlay(True)
    effect = MCharacter.PlayEffectInWorld(effect_str, pos, time)
    insure_play and MCharacter.SetInsureWorldEffectPlay(False)
    sound_event_id and genv.sound_mgr.PlayEventById(sound_event_id, (pos.x, pos.y, pos.z))
    return effect


def GetFxPathString(proto, src):
    if not src:
        return proto.get('path')
    player = replay_util.GetPlayer()
    relation_type = GetFxRelationType(src, player)
    path_str = cconst.FX_RELATION_TYPE_TO_DATA_KEY.get(relation_type, 'path')
    return proto.get(path_str) or proto.get('path')


def GetFxRelationType(src, player):
    if src is None:
        return cconst.FX_RELATION_TYPE.NONE
    if src.IsPlayerCombatAvatar:
        return cconst.FX_RELATION_TYPE.SELF
    if src.IsMagicField:
        space = genv.space
        owner = space.entities.get(src.owner_id)
        if not owner:
            return cconst.FX_RELATION_TYPE.NONE
        if space.game_logic.IsEnemy(player, owner):
            return cconst.FX_RELATION_TYPE.ENEMY
        return cconst.FX_RELATION_TYPE.SELF
    return cconst.FX_RELATION_TYPE.NONE


def ClearWorldEffect(effect):
    if not effect:
        return
    MCharacter.ClearWorldEffect(effect)


def ClearWorldEffectImmediately(effect_id):
    effect_id and MCharacter.ClearWorldEffectImmediately(effect_id)


def PreprocessRandomEffectCache(random_cache, pattern):
    for group_id, group_data in random_effect_data.data.items():
        cache = {}
        effect_strings = []
        effect_weights = []
        for effect_id, effect_weight in zip(group_data['effect_list'], group_data['effect_weights']):
            if not effect_weight:
                continue
            effect_string = ""
            if effect_id and effect_id in effect_data.data:
                if 'effect_string' in effect_data.data[effect_id]:
                    effect_string = effect_data.data[effect_id]['effect_string']
                else:
                    effect_path = effect_data.data[effect_id]['path']
                    effect_string = pattern % effect_path
            effect_strings.append(effect_string)
            effect_weights.append(effect_weight)
        cache["weights"] = effect_weights
        cache["strings"] = effect_strings
        if effect_strings and effect_weights:
            random_cache[group_id] = cache

@idempotent
def GetEffectPath(effect_id):
    return effect_data.data.get(effect_id, {}).get('path')

@idempotent
def GetEffectString(effect_id):
    return effect_data.data.get(effect_id, {}).get('effect_string')

@idempotent
def GetEffectProto(effect_id):
    return effect_data.data.get(effect_id, {})
