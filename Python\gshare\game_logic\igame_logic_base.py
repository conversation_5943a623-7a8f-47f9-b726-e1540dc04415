# -*- coding: utf-8 -*-
import math
from common.classutils import Property, PropertyMetaClass, CustomMapType
from common.mobilecommon import COMPONENT

from gshare import formula, consts
from gshare.consts import MoveSpeedLevel, CombatAvatarEvent
from gshare.utils import enum
from gshare.ispell_mgr import Spel<PERSON><PERSON><PERSON>ult
from gshare.icombat_attr import AttrType
from gshare.iroom import OptionKey
from gshare.icombat_team import ISimpleCombatTeamInfos
from gshare import time_util
from gshare.pyany import PyAny

if COMPONENT == "Server":
    from gserver.data import weapon_data, match_data, equip_data, melee_weapon_data, spell_data, \
        monster_skill_data, island_data
    from gserver import sconst
else:
    from gclient.data import weapon_data, match_data, equip_data, melee_weapon_data, spell_data, \
        monster_skill_data, island_data


class GameState(enum):
    WAITING = 0      # 正在等待玩家进入
    GAMING = 1       # 游戏中
    FINISHED = 2     # 已经结算


class IGameLogicBase(object, metaclass=PropertyMetaClass):
    if COMPONENT == "Client" or sconst.IS_LOCAL_TEST:
        Property("gm_data", CustomMapType, Property.ALL_CLIENTS)    # gm用

    Property("options", CustomMapType, Property.ALL_CLIENTS)
    Property("match_type", 0, Property.ALL_CLIENTS)
    Property("hall_match_type", 0, Property.ALL_CLIENTS)
    Property("game_state", GameState.WAITING, Property.ALL_CLIENTS)
    Property("game_start_time", 0.0, Property.ALL_CLIENTS)
    Property("game_create_time", 0.0, Property.ALL_CLIENTS)     # 一些玩法需要判断开启时间
    Property("rank_level", 1, Property.ALL_CLIENTS)
    Property("match_mode", 1, Property.ALL_CLIENTS)
    # TODO：抽离成单独的comp
    Property("vehicle_passengers", CustomMapType, Property.ALL_CLIENTS)

    Property("freeview_id", '', Property.ALL_CLIENTS)
    # 简单的队伍信息
    Property("simple_combat_teams", ISimpleCombatTeamInfos, Property.ALL_CLIENTS)

    Property('is_crystal_game', False, Property.ALL_CLIENTS)        # 血晶玩法是否开启
    Property("match_queue_type", 0, Property.ALL_CLIENTS)           # 来自那种类型的匹配队列
    Property("match_space_no", -1, Property.ALL_CLIENTS)            # 来自哪个match_space

    Property('gameplay_modes', 0, Property.ALL_CLIENTS)             # 判断某种玩法是否开启

    Property('player_arm_clothes', CustomMapType, Property.ALL_CLIENTS)  # 全局玩家的皮肤 {guid: suit_id}

    Property('loaded_player_count', 0, Property.ALL_CLIENTS)        # 加载好的玩家总人数
    Property('total_player_count', 0, Property.ALL_CLIENTS)         # 本局玩家总人数

    @property
    def suffix(self):
        return match_data.data.get(self.match_type, {}).get("game_logic", "")

    @property
    def show_match_type(self):
        return consts.Match_Special_To_Normal.get(self.hall_match_type, self.hall_match_type)

    @property
    def match_proto(self):
        return match_data.data.get(self.match_type, {})

    @property
    def is_gaming(self):
        # 是否已在游戏状态
        return self.game_state == GameState.GAMING

    def CanDamage(self, a, b):
        # 伤害计算使用
        if self.GetOption(OptionKey.DamageTeammate, False):
            return True
        else:
            return self.IsEnemy(a, b)

    def IsEnemy(self, a, b):
        # 非伤害的逻辑用
        if a is b:
            return False
        if not a or not b:
            return False
        if a.faction < 0 or b.faction < 0:
            return True
        else:
            return a.faction != b.faction

    def IsFriend(self, a, b):
        return not self.IsEnemy(a, b)

    if COMPONENT == "Server":
        def SetOption(self, name, value):
            self.options[name] = value

    def GetOption(self, name, default=None):
        return self.options.get(name, default)

    def IsOpenGameplayMode(self, mode):
        return self.gameplay_modes & 1 << mode

    # region [特殊模式属性]
    @property
    def is_shutter_clue_open(self):
        return self.IsOpenGameplayMode(GameplayMode.Moba_Clue)

    @property
    def is_shutter_energy_open(self):
        return self.IsOpenGameplayMode(GameplayMode.Moba_Energy)
    # endregion

    def SetOpenGameplayMode(self, mode):
        self.gameplay_modes |= 1 << mode

    def CanDealDamageResult(self, caster, target):
        if not target:
            return False
        if target.IsShield:
            shield_owner = target.owner
            if not shield_owner:
                return False
            return self.CanDamage(caster, shield_owner)
        elif target.IsVehicle:
            if caster.vehicle_id == target.id:
                return False
            return self.CanDamage(caster, target)
        else:
            return self.CanDamage(caster, target)

    def DealWeaponDamageResult(self, spell_id, spell_result, caster, target, weapon_id, calc_damage, **kwargs):
        # [Waring] 该函数需要客户端/服务端都能跑，往函数里面加代码请确认双端都需要有对应的函数
        if not caster or not target:
            return
        if not self.CanDealDamageResult(caster, target):
            return

        hit_part = consts.AvatarCollisionBone_UpperTop if target.IsVehicle else \
            kwargs.get('hit_part', consts.AvatarCollisionBone_UpperBottom)
        hit_dir = kwargs.get('hit_dir', (0,0,0))
        hit_pos = kwargs.get('hit_pos', (0, 0, 0))
        hit_back = kwargs.get('hit_back', False)
        hit_penetrate = kwargs.get('hit_penetrate', False)
        # head_radius = kwargs.get('head_radius', 0.0)
        verify_hit_radius = kwargs.get('verify_hit_radius', 0.0)
        verify_hit_half = kwargs.get('verify_hit_half', 0.0)
        verify_bone_scale = kwargs.get('verify_bone_scale', ())
        verify_hit_offset = kwargs.get('verify_hit_offset', ())
        penetrate_power = kwargs.get('penetrate_power', None)
        penetrate_materials = kwargs.get('penetrate_materials', [])
        is_ads = kwargs.get('is_ads', False)
        is_dual = kwargs.get('is_dual', False)
        weapon_guid = kwargs.get('weapon_guid', '')

        damage_data = {
            'weapon_id': weapon_id,
            'weapon_guid': weapon_guid,
            'damage': 0.0,
            'hit_part': hit_part,
            'hit_dir': formula.Tuple(hit_dir),
            'target_pos': target.position,
            'shoot_idx': kwargs.get('shoot_idx', ''),    # 伤害做的合批，这里用来区分是哪发子弹造成的
        }
        if target.IsVehicle:
            tire_index = target.GetHitTireIndex(hit_pos)
            if tire_index >= 0:
                damage_data.update({'tire_index': tire_index})

        if verify_hit_radius:
            damage_data["verify_hit_radius"] = verify_hit_radius
        if verify_hit_half:
            damage_data["verify_hit_half"] = verify_hit_half
        if verify_bone_scale:
            damage_data["verify_bone_scale"] = verify_bone_scale
        if verify_hit_offset:
            damage_data["verify_hit_offset"] = verify_hit_offset
        if hit_pos:
            damage_data['hit_pos'] = formula.Tuple(hit_pos)
        if hit_back:
            damage_data['hit_back'] = hit_back
        if hit_penetrate:
            damage_data['hit_penetrate'] = hit_penetrate
        if penetrate_power:
            damage_data['penetrate_power'] = penetrate_power
        if penetrate_materials:
            damage_data['penetrate_materials'] = penetrate_materials
        if is_ads:
            damage_data['is_ads'] = is_ads
        if is_dual:
            damage_data['is_dual'] = is_dual

        calc_damage and damage_data.update(
            {'damage': self.CalFinalWeaponDamge(spell_id, spell_result, caster, target, weapon_id, damage_data, False)})
        if damage_data['damage'] == 0 and "immune_reason" not in damage_data:
            return spell_result

        spell_result.damage_result[target.id] = damage_data
        return spell_result

    def DealMeleeWeaponDamageResult(self, spell_id, spell_result, caster, target, weapon_id, **kwargs):
        if not caster or not target:
            return
        if not self.CanDamage(caster, target):
            return
        if not spell_result:
            spell_result = SpellResult()
            spell_result.spell_id = spell_id
            spell_result.caster = caster

        hit_dir = kwargs.get('hit_dir', (0, 0, 0))
        hit_back = kwargs.get('hit_back', False)
        hit_pos = kwargs.get('hit_pos', (0, 0, 0))
        weapon_guid = kwargs.get('weapon_guid', '')

        damage_data = {'weapon_id': weapon_id, 'weapon_guid': weapon_guid, 'hit_dir': formula.Tuple(hit_dir), 'hit_back': hit_back, 'hit_pos':  formula.Tuple(hit_pos)}
        damage_data.update({'damage': self.CalFinalWeaponDamge(spell_id, spell_result, caster, target, weapon_id, damage_data, True)})
        spell_result.damage_result[target.id] = damage_data
        return spell_result

    # region [Waring] 本部分伤害计算函数要求双端都能跑且需要算出相同的数值，加东西记得做双端测试
    def DealHitPartChange(self, cur_weapon, hit_part):
        # 击中部位转化
        if not cur_weapon:
            return hit_part
        hit_part = hit_part.split('_')[0].lower()

        enable_limbs_upperbottom_damage = cur_weapon.GetWeaponAttrValue('EnableLimbsUpperBottomDamage', 0)
        if enable_limbs_upperbottom_damage > 0 and hit_part == consts.AvatarCollisionBone_Limbs:
            # 开启四肢触发下胸伤害
            return consts.AvatarCollisionBone_UpperTop
        enable_uppertop_head_damage = cur_weapon.GetWeaponAttrValue('EnableUpperTopHeadDamage', 0)
        if enable_uppertop_head_damage > 0 and hit_part == consts.AvatarCollisionBone_UpperTop:
            # 开启上胸触发爆头伤害
            return consts.AvatarCollisionBone_Head

        return hit_part

    def DealRangeFalloff(self, caster, weapon_id, cur_weapon, damage, dist):
        # 射程衰减
        if not cur_weapon:
            return 0

        falloff_damage = damage
        func = getattr(caster, 'CheckTalentRemoteShotDamageUp', None)
        GetWeaponAttrValue = cur_weapon.GetWeaponAttrValue
        if not func or not func(weapon_id):
            for idx in (3,2,1):
                damage_range_data = GetWeaponAttrValue('damage_range_%s' % idx)
                damage_dropoff_data = GetWeaponAttrValue('damage_dropoff_%s' % idx)

                if dist > damage_range_data:
                    falloff_damage = damage * damage_dropoff_data
                    break

        return falloff_damage

    def DealPenetrateFalloff(self, caster, cur_weapon, damage, penetrate_power, penetrate_materials):
        # 穿透衰减
        if not cur_weapon:
            return 0

        penetrate_falloff_damage = damage
        weapon_proto_data = weapon_data.data.get(cur_weapon.gun_id)
        if weapon_proto_data:
            penetrate_power_attenuation = weapon_proto_data['penerate_power_attenuation']
            penetrate_power_damage_falloff = cur_weapon.GetRealPenerateDamageDropoffData()
            damage_penetrate_drop_data = {}
            for idx in range(len(penetrate_power_attenuation)):
                damage_penetrate_drop_data[penetrate_power_attenuation[idx]] = penetrate_power_damage_falloff[idx]

            for power, penetrate_falloff in damage_penetrate_drop_data.items():
                if penetrate_power <= power:
                    penetrate_falloff_damage = damage * penetrate_falloff

        return penetrate_falloff_damage

    def DealDamageWithCombatAttr(self, caster, target, weapon_id, base_damage=None):
        # fixme: 所有的CalAttrResult都可以放在这个函数来计算，但是要外部传Item进来，因为当伤害计算时，玩家的武器可能已经丢了
        ret_pct, ret_value = 0.0, 0.0
        if not caster or not target:
            return 0.0, 0.0

        cur_weapon = caster.GetCurWeapon()
        cur_weapon_case = caster.GetCurWeaponCase()
        if not cur_weapon or not cur_weapon_case:
            return 0.0, 0.0

        if target.IsCombatAvatar:
            caster_combat_attr = caster.combat_attr
            # 击中护甲造成额外伤害百分比
            if target and target.armor > 0:
                pct, value, _ = caster_combat_attr.GetAttrValue('ArmorToneUpDamage', gun_type=cur_weapon.gun_type, equip_id=weapon_id)
                ret_pct += pct
                ret_value += value

            # 枪械伤害加成
            if cur_weapon.is_gun:
                pct, value, _ = caster.combat_attr.GetAttrValue('GunDamage', gun_type=cur_weapon.gun_type, equip_id=weapon_id)
                ret_pct += pct
                pct = cur_weapon_case.GetAttrValue('GunDamage', default=1.0)
                ret_pct += pct - 1
                ret_value += value

            # 移动减伤
            if target.speed_level in (MoveSpeedLevel.Walk,
                                      MoveSpeedLevel.Jog,
                                      MoveSpeedLevel.Sprint,
                                      MoveSpeedLevel.SuperSprint):
                pct, value, _ = target.combat_attr.GetAttrValue(AttrType.MoveDamageReduce)
                ret_pct += pct
                ret_value += value

            # 只对护甲额外的额外伤害, 基础伤害破甲时不计算
            if cur_weapon.is_gun and base_damage:
                if base_damage < target.armor:
                    pct = cur_weapon_case.GetAttrValue('OnlyArmorDamageUp', default=1.0)
                    ret_value += min(target.armor - base_damage, base_damage * (pct - 1.0))
                if base_damage > target.armor:
                    hp_damage = base_damage - target.armor
                    pct = cur_weapon_case.GetAttrValue('OnlyHpDamageUp', default=1.0)
                    ret_value += hp_damage * (pct - 1.0)

        # 载具增伤
        if target.IsVehicle:
            ret_pct += cur_weapon_case.GetWeaponAttrValue("VehicleDamage", 1.0) - 1

        return ret_pct, ret_value

    def DealDamageWithTalent(self, caster, target, damage_data):
        # 处理天赋逻辑相关的伤害变化，本函数为基类中兼容用，具体实现见igame_logic_talent_comp
        return 0.0, 0.0

    def PostDealDamage(self, caster, target, damage, base_damage, damage_data):
        # 伤害后处理函数，目前只处理枪伤和近战武器伤害，以后有新增的DealXXXDamageResult，也往新增的函数里面加这个后处理函数
        # 这个函数相当于最后的伤害做一次简单的伤害处理
        cur_weapon_case = caster.GetCurWeaponCase()

        if caster.IsCombatAvatar:
            caster_combat_attr = caster.combat_attr
            damage += caster_combat_attr.CalResult(base_damage, 'IncreaseAllDamage')

        if target.IsCombatAvatar:
            target_combat_attr = target.combat_attr
            damage += target_combat_attr.CalResult(base_damage, 'DecreaseAllDamage')

            if cur_weapon_case:
                # 忽略护甲 直接计算血量的伤害
                direct_hp_damage = damage * (cur_weapon_case.GetWeaponAttrValue('DirectDamage', 1) - 1)
                direct_hp_damage and damage_data.update({"direct_hp_damage": direct_hp_damage})

        return damage

    def CheckBlockSkill(self, caster_or_pos, target, hit_part, damage_data):
        # 武士格挡技能，阻挡正面子弹
        if getattr(target, "is_block_gun_damage", None):
            if hit_part in ("Limbs_R_Calf", "Limbs_L_Calf", "Limbs_R_Thigh", "Limbs_L_Thigh"):    # 腿
                return
            pos = caster_or_pos if isinstance(caster_or_pos, (list, tuple)) else caster_or_pos.position
            d, _ = formula.DirAndLen3D(target.position, pos)
            angle = formula.Angle2D(formula.YawToVector(target.yaw), d)
            if angle < (math.pi / 2) * (180 * 1.0 / 180):   # 150 度
                damage_data["immune_reason"] = 1
                if COMPONENT == "Server":
                    target.Publish(CombatAvatarEvent.BLOCK_GUN_DAMAGE, PyAny())
                return True

    def GetBaseWeaponDamage_gun_spell(self, spell_result, caster, target, weapon_id, cur_weapon, real_hit_part, damage_data):
        hit_part = self.DealHitPartChange(cur_weapon, real_hit_part)
        if not caster or not cur_weapon or not hit_part:
            return 0.0
        if self.CheckBlockSkill(caster, target, real_hit_part, damage_data):
            # FIXME: 先这样简化不播放特效的逻辑
            for he in spell_result.hit_effect:
                he['hit_perform_mode'] |= 1 << 1
            return 0.0

        hit_name = hit_part.split('_')[0].lower()
        penetrate_power = damage_data.get('penetrate_power', 100)
        penetrate_materials = damage_data.get('penetrate_materials', [])
        damage = cur_weapon.GetWeaponAttrValue('%s_damage' % hit_name, 0)

        caster_position = spell_result.caster_pos or caster.position
        target_position = damage_data.get('target_pos', target.position)
        damage = self.DealRangeFalloff(caster, weapon_id, cur_weapon, damage,
                                       formula.Distance3D(caster_position, target_position))
        damage = self.DealPenetrateFalloff(caster, cur_weapon, damage, penetrate_power, penetrate_materials)
        return damage

    GetBaseWeaponDamage_shotgun_spell = GetBaseWeaponDamage_gun_spell
    GetBaseWeaponDamage_bow_spell = GetBaseWeaponDamage_gun_spell
    GetBaseWeaponDamage_dual_gun_spell = GetBaseWeaponDamage_gun_spell

    def GetBaseWeaponDamage_melee_spell(self, spell_result, caster, target, weapon_id, cur_weapon, hit_part, damage_data):
        spell_id = spell_result.spell_id
        level = spell_result.level
        spell_damage = spell_data.data.get(spell_id, {}).get(level, {}).get('spell_value', 0.0)
        damage = melee_weapon_data.data.get(equip_data.data.get(weapon_id, {}).get('melee_weapon_id'), {}).get('melee_damage', spell_damage)
        return damage

    GetBaseWeaponDamage_samurai_spell = GetBaseWeaponDamage_melee_spell

    def GetBaseWeaponDamage_samurai_sprint_spell(self, spell_result, caster, target, weapon_id, cur_weapon, hit_part, damage_data):
        spell_id = spell_result.spell_id
        level = spell_result.level
        spell_damage = spell_data.data.get(spell_id, {}).get(level, {}).get('spell_value', 0.0)
        return spell_damage

    def GetBaseWeaponDamage_kunai_spell(self, spell_result, caster, target, weapon_id, cur_weapon, hit_part, damage_data):
        spell_id = spell_result.spell_id
        level = spell_result.level
        spell_damage = spell_data.data.get(spell_id, {}).get(level, {}).get("kunai", {}).get('damage', 100.0)
        return spell_damage

    def CalFinalWeaponDamge(self, spell_id, spell_result, caster, target, weapon_id, damage_data, check_precondition):
        # 计算经过一系列变化后的最终伤害数值, 可以直接用damage_data来返回值
        # 仅用于武器相关的伤害
        if not caster or not target:
            return 0.0
        if check_precondition and not self.CanDealDamageResult(caster, target):
            return 0.0

        hit_part = damage_data.get('hit_part', consts.AvatarCollisionBone_UpperBottom)

        if caster.IsRobotCombatAvatar:
            if self.CheckBlockSkill(caster, target, hit_part, damage_data):
                # FIXME: 先这样简化不播放特效的逻辑
                for he in spell_result.hit_effect:
                    he['hit_perform_mode'] |= 1 << 1
                return 0.0

            # 对刷伤害的玩家造成原始伤害，bot可以打死玩家
            if target.IsCombatAvatar:
                take_damage = caster.combat_data.take_damage_info.get(target.id, 0)  # target对caster已经造成的伤害
                if target.IsRobotCombatAvatar or take_damage > 4000:
                    damage = self.CalWeaponDamageNormalForRobot(caster, target, hit_part)
                else:
                    damage = self.CalWeaponDamageForRobotAttackPlayer(caster, target, hit_part)
            else:
                damage = self.CalWeaponDamageNormalForRobot(caster, target, hit_part)
        else:
            damage = self.CalWeaponDamageNormal(caster, target, weapon_id, hit_part, spell_result, damage_data)
        return damage

    def CalWeaponDamageNormal(self, caster, target, weapon_id, hit_part, spell_result, damage_data):
        weapon_guid = damage_data.get("weapon_guid")    # 必须要guid
        if not weapon_guid:
            return 0.0
        weapon_case = caster.GetWeaponCase(weapon_guid)     # fixme: 子弹还没击中目标时丢弃武器，会没伤害。
        if not weapon_case:
            return 0.0

        base_damage_func = getattr(self, "GetBaseWeaponDamage_%s" % spell_result.proto.get('code', ''), None)
        if not base_damage_func:
            return 0.0

        base_damage = base_damage_func(spell_result, caster, target, weapon_id, weapon_case, hit_part, damage_data)
        pct_attr, value_attr = self.DealDamageWithCombatAttr(caster, target, weapon_id, base_damage)
        pct_talent, value_talent = self.DealDamageWithTalent(caster, target, damage_data)
        damage = base_damage * (1.0 + pct_attr + pct_talent) + (value_attr + value_talent)
        damage = self.PostDealDamage(caster, target, damage, base_damage, damage_data)
        return damage

    def CalWeaponDamageForRobotAttackPlayer(self, caster, target, hit_part):
        maxval = target.maxhp + target.maxarmor
        curval = target.hp + target.armor
        damage = formula.LerpNumber(0.0, 15.0, curval / maxval)
        damage = min(damage, target.hp - 1.0)
        return damage

    @classmethod
    def _CalDamageForRobot(cls, caster, target, hit_part):
        # 需要双端能跑，简化机器人的伤害
        cur_weapon = caster.GetCurWeapon()
        if not cur_weapon:
            return 0
        hit_name = hit_part.split('_')[0].lower()
        weapon_proto = cur_weapon.weapon_proto
        damage = weapon_proto.get('%s_damage' % hit_name, 10.0)
        dist = formula.Distance3D(caster.position, target.position)
        for idx in (3, 2, 1):
            damage_range_data = weapon_proto.get('damage_range_%s' % idx, 50.0)
            damage_dropoff_data = weapon_proto.get('damage_dropoff_%s' % idx, 0.5)
            if dist > damage_range_data:
                damage = damage * damage_dropoff_data
                break
        return damage

    def CalWeaponDamageNormalForRobot(self, caster, target, hit_part):
        damage = self._CalDamageForRobot(caster, target, hit_part)
        return damage

    def CalDamageForMonster(self, caster, target, skill_idx):
        skill_proto = monster_skill_data.data.get(skill_idx, {})
        return skill_proto.get('damage', 0)

    def IsCrystalMode(self):
        return False

    # endregion


class GameplayMode(enum):
    # 一段时间开启的玩法
    Moba_Energy         = 1     # 禁闭岛能源玩法
    Moba_Clue           = 2     # 禁闭岛线索任务


def IsOpenGameplayMode(mode, **kwargs):
    # 局外&局内判断玩法是否开启
    def Moba_Energy():
        spaceno, avatar_count, show_match_type = kwargs.get("spaceno"), kwargs.get("avatar_count", 0),\
            kwargs.get("show_match_type", consts.EMatchType_Moba_LEISURE)
        proto = island_data.data.get(1)
        if not proto or proto["spaceno"] != spaceno or avatar_count < proto["begin_num"] or show_match_type != consts.EMatchType_Moba:
            return False
        return time_util.IsInLimitTime(time_util.GetTimeNow(), proto.get("begin_time"), proto.get("end_time"))

    def Moba_Clue():
        spaceno = kwargs.get("spaceno")
        proto = island_data.data.get(1)
        return proto and spaceno in proto.get("clue_map", []) and \
            time_util.IsInLimitTime(time_util.GetTimeNow(), proto.get("clue_begin_time"), proto.get("clue_end_time"))

    return locals()[GameplayMode.to_string(mode)]()

