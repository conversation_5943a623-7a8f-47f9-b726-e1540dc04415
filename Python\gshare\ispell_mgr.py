# -*- coding:utf-8 -*-
import six2
import time
import struct
import zlib
import six2.moves.c<PERSON>ickle as cPickle

from common.classutils import CustomListType, CustomMapType, Property
from common.mobilecommon import COMPONENT
from common.IdManager import Id<PERSON>anager

from gshare import time_util
from gshare.consts import SpellChargeType, CombatAvatarEvent
from gshare.icombat_attr import AttrType
from gshare.property_util import ExtraProperty
from gshare.iroom import OptionKey

if COMPONENT == "Client":
    from gclient.data import spell_data
    from gclient.gameplay.util import replay_util
else:
    from gserver.data import spell_data


SPELL_TICK_INTERVAL = 0.1


class ISpell(CustomMapType):

    Property("spell_id", 0, Property.ALL_CLIENTS)
    Property("level", 0, Property.OWN_CLIENT | Property.SPECTATOR)
    Property("count", 0, Property.OWN_CLIENT | Property.SPECTATOR)    # 技能可以使用的次数
    Property("cd", 0.0, Property.OWN_CLIENT | Property.SPECTATOR)   # 连续释放的cd

    Property("last_charge_progress", 0.0, Property.OWN_CLIENT | Property.SPECTATOR)     # 上次同步的充能进度
    Property("charge_speed", 0.0, Property.OWN_CLIENT | Property.SPECTATOR)             # 正数充能, 负数扣能
    Property("charge_start_time", 0.0, Property.OWN_CLIENT | Property.SPECTATOR)        # 充能开始时间（中途打断充能重新赋值）
    Property("mega_rollback_time", 0.0, Property.OWN_CLIENT | Property.SPECTATOR)       # 用技能时倒计时开始的时间

    @property
    def proto(self):
        return spell_data.data.get(self.spell_id, {}).get(self.level, {})

    @property
    def is_passive_skill(self):
        return self.proto.get('is_passive_skill', False)

    @property
    def is_energy_skill(self):
        # 判断是否是能量条技能(幽灵疾步)
        return self.proto.get("cost_speed", 0) > 0

    @property
    def spell_cd(self):
        # 连续使用的cd
        return self.proto.get("skill_cd", 0.0)

    @property
    def charge_time(self):
        # 既是充能时间, 也是能量条上限。如果要减慢充能用cur_charge_speed， 一般不在这里做原子属性，在充能速度，耗能速度那做
        return float(self.proto.get("charging_time", 20.0))

    @property
    def charge_limit(self):
        # 充能次数上限
        return int(self.proto.get("skill_limits", 1))

    @property
    def charge_equip_id(self):
        return self.proto.get("skill_equip_id", 0)

    @property
    def spell_value(self):
        base_value = self.proto.get("spell_value", 0.0)
        return base_value

    @property
    def in_energy_limit(self):
        """使用时所需最小能量"""
        return self.cur_energy_progress < self.proto.get("use_energy_limit", 0)

    @property
    def cur_energy_progress(self):
        # 充能进度
        if self.charge_start_time == 0.0 or self.charge_speed == 0.0:
            return self.last_charge_progress
        now = time.time() if COMPONENT == "Server" else replay_util.ReplayServerNow()
        return max(0.0, min(self.charge_time,
                            (now - self.charge_start_time) * self.charge_speed + self.last_charge_progress))

    @property
    def cur_pct_progress(self):
        return self.cur_energy_progress / self.charge_time

    @property
    def cur_charge_speed(self):
        owner = self.get_owner()
        if not owner.SkillAutoCharge() or owner.game_logic.GetOption(OptionKey.DisableSkill, False):
            return 0.0
        return self.get_owner().CalAttrResult(AttrType.SkillChargeSpeed, owner.GetSkillChargeSpeed(self.spell_id) * 1.0, self.charge_equip_id)

    @property
    def is_charge_finished(self):
        return self.last_charge_progress == self.charge_time

    @property
    def is_charging(self):
        return self.charge_speed != 0.0 and self.charge_start_time != 0.0

    if COMPONENT == "Client":
        @property
        def is_in_cd(self):
            return self.cd > replay_util.ReplayServerNow()

    @property
    def hide_cd_when_charging(self):
        return self.proto.get('hide_skill_cd', False)

    def ClockProgress(self):
        # 记录当前进度, (要在设置charge_speed和charge_start_time前使用)
        self.last_charge_progress = self.cur_energy_progress

    def ClearProgress(self):
        self.last_charge_progress = 0.0

    def FillProgress(self):
        self.last_charge_progress = self.charge_time

    def ChangePctProgress(self, delta):
        # 百分比增减进度
        charge_time = self.charge_time
        delta_progress = charge_time * delta
        self.last_charge_progress = max(0.0, min(self.last_charge_progress + delta_progress, charge_time))

    def StopCharge(self):
        # 停止充能/耗能
        self.charge_speed = 0.0
        self.charge_start_time = 0.0

    def StartCharge(self, charge_speed=None):
        # 开始充能
        self.charge_speed = charge_speed or self.cur_charge_speed
        self.charge_start_time = time.time()


class ISpellMgr(CustomMapType):
    VALUE_TYPE = ISpell

    def AddSpell(self, spell_id, level=1, count=0):
        self[spell_id] = self.VALUE_TYPE({"spell_id": spell_id, "level": level, "count": count})

    def RemoveSpell(self, spell_id):
        return self.pop(spell_id, None)

    def Clear(self):
        self.clear()

    def OnTick(self):
        return

    def CheckCanSpell(self, spell_id):
        # HARDCODE
        if spell_id in (1, 20):
            return True

        if spell_id not in self:
            return False

        spell = self[spell_id]

        if time_util.GetTimeNow() < spell.cd or spell.count <=0:
            return False
        if spell.in_energy_limit:
            return False

        return True

    def OnSpellStrike(self, spell_id, extra):
        spell = self.get(spell_id, None)
        if not spell:
            return

        if not extra.get('refresh_cd', True):
            return

        cd = self.get_owner().GetSpellCd(spell)
        if cd > 0:
            spell.cd = time_util.GetTimeNow() + cd

    def OnSpellStop(self, spell_id, extra):
        spell = self.get(spell_id, None)
        if not spell:
            return

        if not extra.get('refresh_cd', False):
            return

        cd = spell.proto.get("skill_cd", 0.0)
        if cd > 0:
            spell.cd = time_util.GetTimeNow() + cd


class SpellResult(object):
    # 技能封包

    def __init__(self):
        ##### 公共数据 #####
        self.weapon_guid = ""
        # 技能ID
        self.spell_id = 0
        # 技能等级
        self.level = 1
        # 技能释放者
        self.caster = None
        # 直接攻击者
        self.direct_caster = None
        # 对于两段式攻击结算，需要标识第二阶段不扣子弹
        self.cost_ammo = True
        self.cost_ammo_hand = 1   # 双持枪，  0: 左手， 1：右手（默认）
        # extra信息
        self.extra = None
        # 子弹开始飞的起点,  因为子弹有飞行, 这个是飞行的起点
        self.shoot_start_pos = None
        self.shoot_end_pos = None
        # 唯一id
        self.id = IdManager.genid()

        ##### 伤害数据 #####
        # {
        #     "Y5wk7fcFyHKcJdXl": {
        #         "weapon_id": 15,
        #         "damage": 36.0,
        #         "hp_damage": 0,
        #         "armor_damage": 0,
        #         "hit_part": "Lower",
        #         "hit_dir": (0.9959, 0.0699, 0.0573),
        #         "target_pos": (521.1114501953125, 193.29742431640625, -319.1925048828125),
        #         "hit_pos": (520.8326, 194.2567, -319.4942),
        #         "hit_back": true,
        #         "penetrate_power": 100.0,
        #         "penetrate_materials": [1001, 1],
        #         "is_ads": true
        #     }
        # },

        self.damage_result = {}
        # 是否系统伤害：坠落伤害/毒圈伤害
        self.is_system_damage = False

        self.caster_pos = None

        ###### 子弹是否击中目标######
        self.is_hit = True

        ##### 子弹打中数据 ####
        self.hit_effect = []

        ##### 弹道特效数据 ####
        self.ballistic_effect = []

        #### Bomb数据 #########
        self.bomb_result = {}

        #### 技能表现数据 #####
        self.spell_result = {}
        self.sound_results = []

        # 击中水体后，特效封包的hit_effect_id会设为-1
        self.hit_water = False

        #### 发往服务端校验的数据 ####
        self.verify_camera_pos = None
        self.verify_camera_yaw = 0.0
        self.verify_start_pos = None
        self.verify_shoot_dir = None
        self.verify_timestamp = 0.0
        self.verify_recoil_pitch = 0.0
        self.verify_assist_aim = False
        return

    @property
    def proto(self):
        return spell_data.data.get(self.spell_id, {}).get(self.level, {})

    def Pack(self):
        # 序列化
        result = {}

        if self.weapon_guid:
            result['weapon_guid'] = self.weapon_guid

        if not self.cost_ammo:
            result['cost_ammo'] = self.cost_ammo
        else:
            result['verify_timestamp'] = time.time()
        if self.cost_ammo_hand == 0:
            result['cost_ammo_hand'] = self.cost_ammo_hand

        if self.spell_id:
            result['spell_id'] = self.spell_id

        if self.level:
            result['level'] = self.level

        if self.caster:
            result['caster'] = self.caster.id
            # 客户端需要服务器的cast_pos(因为caster可能在视野外)
            # 对于服务器也需要客户端位置, 需要校验位置是否正确
            result['caster_pos'] = self.caster.position

        if self.damage_result:
            result['damage_result'] = self.damage_result

        if self.is_system_damage:
            result['is_system_damage'] = self.is_system_damage

        if not self.is_hit:
            result["is_hit"] = False
            if self.shoot_end_pos:
                result["shoot_end_pos"] = self.shoot_end_pos

        if self.hit_effect:
            result['hit_effect'] = self.hit_effect

        if self.ballistic_effect:
            result['ballistic_effect'] = self.ballistic_effect

        if self.bomb_result:
            result['bomb_result'] = self.bomb_result

        if self.spell_result:
            result['spell_result'] = self.spell_result

        if self.sound_results:
            result['sound_results'] = self.sound_results

        if COMPONENT == 'Client':
            if self.shoot_start_pos:
                result['shoot_start_pos'] = self.shoot_start_pos.tuple()
            if self.verify_start_pos:
                result['verify_start_pos'] = self.verify_start_pos.tuple()
            if self.verify_shoot_dir:
                result['verify_shoot_dir'] = self.verify_shoot_dir.tuple()
        else:
            if self.shoot_start_pos:
                result['shoot_start_pos'] = self.shoot_start_pos
            if self.verify_start_pos:
                result['verify_start_pos'] = self.verify_start_pos
            if self.verify_shoot_dir:
                result['verify_shoot_dir'] = self.verify_shoot_dir

        if self.verify_camera_pos:
            result['verify_camera_pos'] = self.verify_camera_pos
        if self.verify_camera_yaw:
            result['verify_camera_yaw'] = self.verify_camera_yaw
        if self.verify_recoil_pitch:
            result['verify_recoil_pitch'] = self.verify_recoil_pitch
        if self.verify_assist_aim:
            result['verify_assist_aim'] = self.verify_assist_aim

        if self.extra:
            result['extra'] = self.extra

        if self.id:
            result['id'] = self.id

        return result

    if COMPONENT == "Client":
        def Unpack(self, result):
            self.__dict__.update(result)

            if self.caster and genv.space:
                self.caster = genv.space.GetEntityByID(self.caster)
            return

    else:
        def Unpack(self, result):
            self.__dict__.update(result)

    @classmethod
    def CreateSimple(cls, caster, spell_id, spell_level=1, direct_caster=None):
        # direct_caster是直接攻击者，可以是法术场、玩家等
        spell = cls()
        spell.spell_id = spell_id
        spell.level = spell_level
        spell.caster = caster
        spell.direct_caster = direct_caster or caster
        spell.cost_ammo = False
        spell.is_system_damage = spell.proto.get('is_system_damage', False)
        return spell

    def FillSimpleDamage(self, targetid, damage, weapon_id=None, **kwargs):
        if not weapon_id:
            self.damage_result = {targetid: {'damage': damage, 'spell_id': self.spell_id}}
        else:
            self.damage_result = {targetid: {'damage': damage, 'weapon_id': weapon_id, 'spell_id': self.spell_id}}
        self.damage_result[targetid].update(kwargs)
